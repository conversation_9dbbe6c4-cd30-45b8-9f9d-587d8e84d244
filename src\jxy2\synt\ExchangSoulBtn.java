package jxy2.synt;

import com.tool.btn.MoBanBtn;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class ExchangSoulBtn extends MoBanBtn {
    public int typeBtn;
    public ExchangSoulJPanel exchangSoulJPanel;
    public ExchangSoulBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, ExchangSoulJPanel exchangSoulJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.exchangSoulJPanel = exchangSoulJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        FormsManagement.HideForm(117);
    }
}
