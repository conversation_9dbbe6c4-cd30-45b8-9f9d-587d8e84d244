package come.tool.JDialog;

import org.come.Frame.LingbaoJframe;
import org.come.model.Lingbao;
import org.come.until.UserData;

import com.tool.role.RoleLingFa;

public class SkillDiscatdJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		String skill=(String)object;
		if (tishi) {
			//先判断人物是否有足够的银两
	    	if (UserData.uptael(800000)) {
	    		Lingbao lingbao = RoleLingFa.getRoleLingFa().getChoseBao();		
	    		String sikils=lingbao.getSkills();
	    		lingbao.setSkills(UserData.Splice(sikils,skill,0));
	    		 //开启格子后发服务器
	    		lingbao.UpdateLing();
		    	 //刷新展示面板
				LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel().shu<PERSON><PERSON><PERSON><PERSON>(lingbao); 	   		
			}
		}else {
		
		}
	}

}
