package com.tool.btn;

import java.awt.Color;
import java.awt.Desktop;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.net.URI;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.GoodDetailedJpanel;
import org.come.Jpanel.MonthlyCardJpanel;
import org.come.bean.BuyShopBean;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.model.Eshop;
import org.come.socket.Agreement;
import org.come.socket.GameClient;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;

/**
 * 普通按钮
 * 
 * <AUTHOR>
 * @time 2019-5-8
 * 
 */
public class CommonBtn extends MoBanBtn {

	// ★★★普通按钮★★★
	// 汉仪小隶书简 16px 加粗
	// 常态 #101818，R16,G24,B2
	// 经过 #305838，R48,G88,B56
	// 点击 #305838，R48,G88,B56 字体向左、向下各移动1px
	// ☆☆☆☆☆☆☆☆☆☆☆

	private int caozuo;
	private GoodDetailedJpanel goodDetailedJpanel;
	private MonthlyCardJpanel monthlyCardJpanel;
	private int wupinCount;

	public CommonBtn(String iconpath, int type) {
		// TODO Auto-generated constructor stub
		super(iconpath, type);
	}

	public CommonBtn(String iconpath, int type, String text, int caozuo) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub
		this.caozuo = caozuo;
		this.setText(text);
		if (caozuo == 8 || caozuo == 9) {
			setFont(UIUtils.TEXT_FONT);
			setForeground(Color.white);
		} else {
			setFont(UIUtils.TEXT_HY16);
			setForeground(UIUtils.COLOR_BTNPUTONG[0]);
		}
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	public CommonBtn(String iconpath, int type, String text, int caozuo, GoodDetailedJpanel goodDetailedJpanel) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub
		this.caozuo = caozuo;
		this.goodDetailedJpanel = goodDetailedJpanel;
		this.setText(text);
		setFont(UIUtils.TEXT_HY16);
		setForeground(UIUtils.COLOR_BTNPUTONG[0]);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	public CommonBtn(String iconpath, int type, String text, int caozuo, MonthlyCardJpanel monthlyCardJpanel) {
		super(iconpath, type);
		setText(text);

		setFont(UIUtils.TEXT_HY16);
		setForeground(UIUtils.COLOR_BTNPUTONG[0]);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.caozuo = caozuo;
		this.monthlyCardJpanel = monthlyCardJpanel;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		int goodsPrice = 0;
		// 区分按钮类型 1 <= caozuo <= 3 属于 物品购买面板操作
		if (caozuo >= 1 && caozuo <= 3) {

			if ("".equals(goodDetailedJpanel.getGoumaiCount().getText().trim())) {
				ZhuFrame.getZhuJpanel().addPrompt2("数量不能为空");
				return;
			}
			// 获取物品数量
			wupinCount = Integer.valueOf(goodDetailedJpanel.getGoumaiCount().getText());
			Eshop eshop = goodDetailedJpanel.getEshop();
			if (eshop == null) {
				return;
			}
			if (caozuo == 1) {
				int sum = Integer.parseInt(goodDetailedJpanel.getGoumaiCount().getText());
				if (sum <= 0) {
					ZhuFrame.getZhuJpanel().addPrompt2("购买数量必须大于零");
					return;
				}
				Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(eshop.getEshopiid()));
				int max = GoodsListFromServerUntil.Surplussum(goodstable == null ? "-1" : goodstable.getType() + "", goodstable == null ? "-1" : goodstable.getGoodsid() + "", sum);
				if (max < sum) {
					ZhuFrame.getZhuJpanel().addPrompt2("你背包已不足");
					return;
				}
				LoginResult loginResult = RoleData.getRoleData().getLoginResult();
				int jg = Integer.parseInt(goodDetailedJpanel.getXiaohaoXianyu().getText());
				if (goodDetailedJpanel.getEshop().getEshoptype().equals("5")) {
					if (loginResult.getMoney() < jg) {
						ZhuFrame.getZhuJpanel().addPrompt2("没有足够的积分!");
						return;
					}
				} else {
					if (loginResult.getCodecard().longValue() < jg) {
						ZhuFrame.getZhuJpanel().addPrompt2("没有足够的仙玉!");
						return;
					}
				}
				try {
					BuyShopBean bean = new BuyShopBean();
					bean.setAte(0);
					bean.setCd(goodDetailedJpanel.getEshop().getEshopid());
					bean.setSum(sum);
					String senmes = Agreement.getAgreement().nbuyAgreement(GsonUtil.getGsonUtil().getgson().toJson(bean));
					SendMessageUntil.toServer(senmes);
				} catch (Exception e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
			} else if (caozuo == 2) {
				if (wupinCount >= 999) {
					ZhuFrame.getZhuJpanel().addPrompt2("最大数量999");
					return;
				} else {
					// 获取物品单价
					goodsPrice = Integer.valueOf(goodDetailedJpanel.getWupinPrice().getText());
					// 数量增加
					wupinCount++;
					// 设置数量显示
					goodDetailedJpanel.getGoumaiCount().setText(wupinCount + "");
					// 设置消耗仙玉显示
					goodDetailedJpanel.getXiaohaoXianyu().setText((goodsPrice * wupinCount) + "");
					goodDetailedJpanel.getPriceVal().setText((goodsPrice * wupinCount) + goodDetailedJpanel.getNowMoneyType());
				}
			} else if (caozuo == 3) {
				if (wupinCount <= 0) {
					// 提示无法减少
					ZhuFrame.getZhuJpanel().addPrompt2("数量为零了");
					return;
				} else {
					// 获取物品单价
					goodsPrice = Integer.valueOf(goodDetailedJpanel.getWupinPrice().getText());
					// 数量减少
					wupinCount--;
					// 设置数量显示
					goodDetailedJpanel.getGoumaiCount().setText(wupinCount + "");
					// 设置消耗仙玉显示
					goodDetailedJpanel.getXiaohaoXianyu().setText((goodsPrice * wupinCount) + "");
					goodDetailedJpanel.getPriceVal().setText((goodsPrice * wupinCount) + goodDetailedJpanel.getNowMoneyType());
				}
			}
		} else if (caozuo >= 20 && caozuo <= 22) {// 月周卡奖励操作
			if (caozuo == 20) {//领取奖励
				String mes = Agreement.getAgreement().TaskNAgreement("R1");
				SendMessageUntil.toServer(mes);
			} else if (caozuo == 21||caozuo == 22) {//21购买月卡//22购买周卡
				try {
					LoginResult login=RoleData.getRoleData().getLoginResult();
//					if(login.getGrade() <=102){
//						ZhuFrame.getZhuJpanel().addPrompt2("等级需到达102级才可以购买");
//						return;
//					}
					StringBuffer buffer = new StringBuffer();
					// http://ip:8080/TestMaven/requestPay30pay?username=123&rolename=兰少&money=1&quid=25001&type=2
					buffer.append("http://www.dongmengzhongchou.com/pay_config/requestPay30?username=");
					buffer.append(login.getUserName());
					buffer.append("&rolename=");
					buffer.append(login.getRolename());
					buffer.append("&money=");
					buffer.append(caozuo == 21 ? 30 : 10);
					buffer.append("&quid=");
					buffer.append(GameClient.atid);
					buffer.append("&type=2");
					buffer.append("&serverMeString=");
					buffer.append(login.getServerMeString());
					Desktop desktop = Desktop.getDesktop();
					URI uri = new URI(buffer.toString()); // 创建URI统一资源标识符
					desktop.browse(uri);
				} catch (Exception e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
			}
		}
		// 区分按钮类型 4 <= caozuo <= 5 属于 基金面板操作
		// switch (caozuo) {
		// // 确定购买
		// case 1:
		//
		// break;
		// // 添加数量
		// case 2:
		//
		// break;
		// // 减少数量
		// case 3:
		//
		// break;
		// // 上一页(基金面板)
		// case 4:
		//
		// break;
		// // 下一页(基金面板)
		// case 5:
		//
		// break;
		// // 上一页(月卡面板)
		// case 6:
		//
		// break;
		// // 下一页(月卡面板)
		// case 7:
		//
		// break;
		// case 8:
		// break;
		// case 9:
		// break;
		// // 屏幕分辨率
		// case 10:
		// /** 7-16修改start */
		// // if
		// //
		// (TestSetupJframe.getTestSetupJframe().getTestSetupJpanel().getFlag()
		// // == 0) {
		// //
		// TestSetupJframe.getTestSetupJframe().getTestSetupJpanel().getScreen().setVisible(true);
		// //
		// TestSetupJframe.getTestSetupJframe().getTestSetupJpanel().setFlag(1);
		// // } else {
		// //
		// TestSetupJframe.getTestSetupJframe().getTestSetupJpanel().getScreen().setVisible(false);
		// //
		// TestSetupJframe.getTestSetupJframe().getTestSetupJpanel().setFlag(0);
		// // }
		// /** 7-16修改end */
		// break;
		// // 确认设置
		// case 20:
		// break;
		// case 21:// 月卡领取奖励、周卡领取奖励
		//
		// break;
		// default:
		// break;
		// }
	}
}
