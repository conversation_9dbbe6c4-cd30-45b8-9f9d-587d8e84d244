package jxy2.petView;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import jxy2.soul.ResetSoulSkillFrame;
import org.come.Frame.ZhuFrame;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.summonequip.JpanelSummonEquipMain;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class EquiBtn extends MoBanBtn {
    private int typeBtn;
    private JpanelSummonEquipMain jpanelSummonEquipMain;
    public EquiBtn(String iconpath, int type,Color[] colors,Font font,String text,Integer typeBtn, JpanelSummonEquipMain jpanelSummonEquipMain,int index,String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.jpanelSummonEquipMain = jpanelSummonEquipMain;
        this.index = index;
    }


    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

        switch (typeBtn) {
            case 0:
//                jpanelSummonEquipMain.lingFan(false);
                break;
            case 1:
//                jpanelSummonEquipMain.lingFan(true);
                break;
            case 2:
                jpanelSummonEquipMain.faFan(false);
                break;
            case 3:
                jpanelSummonEquipMain.faFan(true);
                break;
            case 21:
                Peiyang();
                break;
            case 22:
                jpanelSummonEquipMain.getNumText().setText(
                        RoleData.getRoleData().getLoginResult().getScoretype("比斗奖章").toString());
                break;
            case 23:
                //TODO 重置魂技技能
                FormsManagement.showForm(115);
                FormsManagement.upgradForm(115);
                ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl().InitializeSkillList();
                break;
        }
    }
    private void Peiyang() {
        if (jpanelSummonEquipMain.getChooseEquip() == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择召唤兽装备");
            return;
        }
        Goodstable chooseEquip = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseEquip());
        if (chooseEquip == null) {
            // 没有选中召唤兽装备
            ZhuFrame.getZhuJpanel().addPrompt2("没有选中召唤兽装备");
            return;
        }
        // 召唤兽装备属性
        String[] values = chooseEquip.getValue().split("\\|");
        SuitOperBean suitOperBean = new SuitOperBean();
        if (jpanelSummonEquipMain.getSummonEquipMenuType() == 1 && jpanelSummonEquipMain.getRadioBtnType() == 1) {
            /** 培养 */
            if (jpanelSummonEquipMain.getChooseGoods() == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选择千年魂石");
                return;
            }
            Goodstable chooseGoods = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseGoods());
            if (chooseGoods == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选中玄铁晶石");
                return;
            }
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                // 金钱不足
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足100万");
                return;
            }
            int valuesMessage = Integer.parseInt(jpanelSummonEquipMain.getValuesMessage(values, "通灵"));
            if (valuesMessage >= 6000) {
                ZhuFrame.getZhuJpanel().addPrompt2("该装备的通灵值已经抵达上限，无法在培养");
                return;
            }

            suitOperBean.setType(41);
            java.util.List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            goods.add(chooseGoods.getRgid());
//            suitOperBean.setQuantity(Integer.parseInt(jpanelSummonEquipMain.getpyText().getText()));
            suitOperBean.setGoods(goods);
//            deductGoods(chooseGoods, Integer.parseInt(jpanelSummonEquipMain.getpyText().getText()));
            // jpanelSummonEquipMain.recoverEquipImgTwo();
            if (chooseGoods.getUsetime() <= 0)
                jpanelSummonEquipMain.recoverEquipImgTwo();
        } else if (jpanelSummonEquipMain.getSummonEquipMenuType() == 1 && jpanelSummonEquipMain.getRadioBtnType() == 2) {//分解界面
            /** 分解 */
            suitOperBean.setType(42);
            java.util.List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            deductGoods(chooseEquip, 1);
            suitOperBean.setGoods(goods);
            jpanelSummonEquipMain.recoverEquipImgOne();
        } else if (jpanelSummonEquipMain.getSummonEquipMenuType() == 2 && jpanelSummonEquipMain.getRadioBtnType() == 1) {
            /** 重洗技能 */
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                // 金钱不足
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足100万");
                return;
            }
            suitOperBean.setType(43);
            java.util.List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            Goodstable[] chooseWashGoods = jpanelSummonEquipMain.getChooseWashGoods();
            for (int i = 0; i < chooseWashGoods.length; i++) {
                if (chooseWashGoods[i] == null) {
                    if (i == 0) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请放入玄铁晶石");
                    } else if (i == 1) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请放入内丹精华");
                    } else {
                        ZhuFrame.getZhuJpanel().addPrompt2("材料不齐全");
                    }
                    return;
                }
                goods.add(chooseWashGoods[i].getRgid());
            }
            suitOperBean.setGoods(goods);
            for (int i = 0; i < chooseWashGoods.length; i++) {
                deductGoods(chooseWashGoods[i], 1);
            }
            if (!Extractedframe.getExtractedframe().isVisible()) {
                FormsManagement.showForm(112);
            }
//            equiJPanel.recoverWashGoodsLabs(-1);
        } else if (jpanelSummonEquipMain.getSummonEquipMenuType() == 2
                && jpanelSummonEquipMain.getRadioBtnType() == 2) {
            /** 重悟技能 */
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                // 金钱不足
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足500万");
                return;
            }
            if (jpanelSummonEquipMain.getChooseGoods() == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选择千年魂石");
                return;
            }
            Goodstable chooseGoods = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseGoods());
            if (chooseGoods == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选中隐月神石");
                return;
            }
            if (jpanelSummonEquipMain.getValuesMessage(values, "觉醒技") == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("该装备没有觉醒技");
                return;
            }
            suitOperBean.setType(44);
            List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            goods.add(chooseGoods.getRgid());
            suitOperBean.setGoods(goods);
            deductGoods(chooseGoods, 1);
            if (chooseGoods.getUsetime() <= 0) {
                jpanelSummonEquipMain.recoverEquipImgTwo();
                FormsManagement.showForm(94);
            }
        } else if (jpanelSummonEquipMain.getSummonEquipMenuType() == 3
                && jpanelSummonEquipMain.getRadioBtnType() == 1) {
            /** 提升等级 */
            long num = Long.parseLong(jpanelSummonEquipMain.getNumText().getText());
            if (num <= 0) {
                return;
            }
            String valuesMessage = jpanelSummonEquipMain.getValuesMessage(values, "觉醒技");
            if (valuesMessage == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("该装备没有觉醒技");
                return;
            }
            if (new BigDecimal(num).compareTo(RoleData.getRoleData().getLoginResult().getScoretype("比斗奖章")) > 0) {
                // 比斗奖章数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("比斗奖章数量不足");
                return;
            }
            String[] split = valuesMessage.split("&");
            long exp = Long.parseLong(split[3]);
            if (JpanelSummonEquipMain.expChangeLevel(exp) >= 20) {
                ZhuFrame.getZhuJpanel().addPrompt2("等级已经达到最大值");
                return;
            }
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                // 金钱不足
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足");
                return;
            }

            suitOperBean.setType(46);
            suitOperBean.setJade(new PartJade((int) num, 0));
            java.util.List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            suitOperBean.setGoods(goods);
            jpanelSummonEquipMain.recoverEquipImgTwo();

        } else if (jpanelSummonEquipMain.getSummonEquipMenuType() == 3
                && jpanelSummonEquipMain.getRadioBtnType() == 2) {
            /** 开启觉醒 */
            if (jpanelSummonEquipMain.getValuesMessage(values, "觉醒技") != null) {
                ZhuFrame.getZhuJpanel().addPrompt2("该装备已有觉醒技");
                return;
            }
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                // 金钱不足
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足");
                return;
            }
            if (jpanelSummonEquipMain.getChooseGoods() == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选择千年魂石");
                return;
            }
            Goodstable chooseGoods = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseGoods());
            if (chooseGoods == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选择千年魂石");
                return;
            }
            suitOperBean.setType(45);
            List<BigDecimal> goods = new ArrayList<>();
            // 添加物品rgid
            goods.add(chooseEquip.getRgid());
            goods.add(chooseGoods.getRgid());
            suitOperBean.setGoods(goods);
            deductGoods(chooseGoods, 1);
            if (chooseGoods.getUsetime() <= 0)
                jpanelSummonEquipMain.recoverEquipImgTwo();
        }
        String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
        SendMessageUntil.toServer(sendmes);
    }


    /** 物品消耗扣除 */
    public void deductGoods(Goodstable goodstable, int num) {
        goodstable.goodxh(num);
        if (goodstable.getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goodstable.getRgid());
        }
    }
}
