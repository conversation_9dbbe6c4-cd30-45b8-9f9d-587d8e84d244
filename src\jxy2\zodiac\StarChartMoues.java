package jxy2.zodiac;

import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.entity.Goodstable;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.MessagrFlagUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.ArrayList;
import java.util.List;

public class StarChartMoues implements MouseListener {
    public StarChartJPanel starChartJPanel;
    public int caozuo ;
    public static List<String> list = new ArrayList<>();
    public StarChartMoues(int caozuo, StarChartJPanel starChartJPanel) {
        this.caozuo = caozuo;
        this.starChartJPanel = starChartJPanel;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        Goodstable goodstable = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[caozuo]);
        if (goodstable!=null){
            starChartJPanel.chooseXpImg(goodstable);
//            System.out.println(goodstable.getUsetime());
        }

        starChartJPanel.getGoodLabels()[caozuo].setBorder(BorderFactory.createLineBorder(Color.red,2));
        for (int i = 0; i < starChartJPanel.getGoodLabels().length; i++) {
            if (i!=caozuo){
                starChartJPanel.getGoodLabels()[i].setBorder(BorderFactory.createEmptyBorder());
            }
        }
        list.add(caozuo + "");
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        list.remove(caozuo + "");
    }

    @Override
    public void mouseEntered(MouseEvent e) {
    if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        MsgJframe.getJframe().getJapnel().StarChartGoods(caozuo,1);
    }

    @Override
    public void mouseExited(MouseEvent e) {
    if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }
}
