package jxy2.duofriand;

import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class FriendChatMessagesJpanel extends JPanel {

    private JTextArea chatArea;
    private JTextField messageField;
    private JButton sendButton;
    private String friendName;

    public FriendChatMessagesJpanel(String friendName) {
        this.friendName = friendName;
        setLayout(new BorderLayout());

        chatArea = new JTextArea();
        chatArea.setEditable(false);
        add(new JScrollPane(chatArea), BorderLayout.CENTER);

        JPanel bottomPanel = new JPanel(new BorderLayout());
        messageField = new JTextField();
        sendButton = new JButton("Send");

        bottomPanel.add(messageField, BorderLayout.CENTER);
        bottomPanel.add(sendButton, BorderLayout.EAST);

        add(bottomPanel, BorderLayout.SOUTH);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz67, 0, 0, getWidth(), getHeight(), 1);
    }

    public void appendMessage(String message) {
        chatArea.append(friendName + ": " + message + "\n");
    }
}
