package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import com.tool.role.RoleData;
import org.come.summonequip.JframeSummonEquipMain;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import java.awt.event.MouseEvent;
/**
 * ClassName:召唤兽物品界面按钮操作事件
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/4/17 19:39
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */

public class PetEquipmentBtn extends MoBanBtn {
	private int caozuo;
	public PetEquipmentBtn(String iconpath, int type, int caozuo, String labelName, String string) {
		super(iconpath, type,0,string,labelName);
		this.caozuo = caozuo;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		if (caozuo == 1) {
			FormsManagement.HideForm(67);
			JframeSummonEquipMain.getJframeSummonEquipMain().getJpanelSummonEquipMain().changeMenuView(-1);
			Util.StopFrame(91);
		}else if (caozuo == 2) {
			ImageMixDeal.adoppetMap.put(RoleData.getRoleData().getLoginResult().getRolename(), new ManimgAttribute(UserMessUntil.getChosePetMes(), 1));
		}
	}

}
