package jxy2.petAi;

import jxy2.setup.AudioSteupMouslisten;
import org.come.Frame.ZhuFrame;
import org.come.bean.PetOperationDTO;
import org.come.until.SendRoleAndRolesummingUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class Pet<PERSON>attle<PERSON><PERSON>Mouse implements MouseListener {
    private PetBattleAiJPanel petBattleAiJPanel;
    private int petId;
    public boolean isFirstCase = true;
    public Map<Integer,String> stringList = new ConcurrentHashMap<>();
    public PetBattleAiMouse(PetBattleAiJPanel petBattleAiJPanel, int petId) {
        this.petBattleAiJPanel = petBattleAiJPanel;
        this.petId = petId;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
//        petBattleAiJPanel.getPet().setAi("");
        // 假设petId的有效范围是1到5，‌且petBattleAiJPanel.getXz()的长度至少为5
        PetOperationDTO dto = new PetOperationDTO();
        if (petId >= 1 && petId <= 5 && petBattleAiJPanel.getXz()[petId - 1].getIcon() == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("请按照条件顺序选择");
            return;
        }
        if (petId==0&&petBattleAiJPanel.getXz()[6].getIcon()!=null){
                petBattleAiJPanel.getXz()[6].setIcon(null);
                petBattleAiJPanel.getPet().setAi("");
        }

        if (petBattleAiJPanel.getXz()[petId].getIcon() == null){
            int num = petId == 0 ? 1 : petId == 2 ? 5 : 10;
            String[] goodIds = null;
            if (petBattleAiJPanel.getPet().getAi() != null && !petBattleAiJPanel.getPet().getAi().isEmpty()) {
                goodIds = petBattleAiJPanel.getPet().getAi().split("\\|");
            }
            StringBuilder buffer = new StringBuilder();

            if (petId==6){
                // 清除之前的内容
                for (int j = 0; j < petBattleAiJPanel.getXz().length; j++) {
                    if (j != 6) {
                        petBattleAiJPanel.getXz()[j].setIcon(null);
                    }
                }
                buffer.setLength(0);
                buffer.append("条件=回合-1=防御-9999-25000");
            }else {
                if (goodIds != null) {
                    for (int i = 0; i < goodIds.length; i++) {
                        if (buffer.length() != 0) {buffer.append("|");}
                        buffer.append(goodIds[i]);
                    }
                }
                if (buffer.length() != 0) {buffer.append("|");}


                if (petId==0){
                    buffer.append("条件=回合-").append(num).append("=法术-").append(petBattleAiJPanel.getSkill().getSkillid()).append("=25000");
                }else if (petId==1){
                    buffer.append("条件=回合-").append(2).append("=普通攻击");
                }else if (petId==2){
                    buffer.append("条件=回合-").append(num).append("=法术-").append(petBattleAiJPanel.getSkill().getSkillid()).append("=25000");
                }else if (petId==3){
                    buffer.append("条件=回合-").append(7).append("=普通攻击");
                }else if (petId==4){
                    buffer.append("条件=回合-").append(num).append("=法术-").append(petBattleAiJPanel.getSkill().getSkillid()).append("=25000");
                }else if (petId==5){
                    buffer.append("条件=回合-").append(11).append("=普通攻击");
                }
            }
            dto.setEventType(buffer.toString());
            petBattleAiJPanel.getXz()[petId].setIcon(AudioSteupMouslisten.icon);
        }else {

            if (petId==6){
                petBattleAiJPanel.getXz()[6].setIcon(null);
                petBattleAiJPanel.getPet().setAi("");
            }else {
                if (petId >= 0 && petId <= 4 && petBattleAiJPanel.getXz()[petId+1].getIcon() == null) {
                    petBattleAiJPanel.getXz()[petId].setIcon(null);
                }
                if (petId==5){
                    petBattleAiJPanel.getXz()[5].setIcon(null);
                }

                if (petBattleAiJPanel.getXz()[petId].getIcon()==null) {
                    String originalAi = petBattleAiJPanel.getPet().getAi();
                    String[] strings = originalAi.split("\\|");
                    StringBuilder newAi = new StringBuilder();  // 初始化一个 StringBuilder 用于构建新的字符串
                    // 遍历字符串数组
                    for (int i = 0; i < strings.length; i++) {
                        if (i == petId) {
                                continue;  // 当 i 等于 petId 时，跳过当前字符串
                        } else {
                            newAi.append(strings[i]);  // 否则将当前字符串添加到 newAi 中
                            if (i != strings.length - 1) {
                                newAi.append("|");  // 如果不是最后一个字符串，添加 "|" 分隔符
                            }
                        }
                    }
                    // 更新原始字符串
                    dto.setEventType(newAi.toString());
                }
            }
        }
        dto.setPetId(petBattleAiJPanel.getPet().getSid());
        dto.setOperationType("SUMMONING_BEAST_AI_CONDITION_CHANGE");
        SendRoleAndRolesummingUntil.sendRoleSumming(dto);

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }


}
