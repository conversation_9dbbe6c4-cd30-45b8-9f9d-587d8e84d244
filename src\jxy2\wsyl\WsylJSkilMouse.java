package jxy2.wsyl;

import org.come.bean.Skill;
import org.come.until.FormsManagement;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WsylJ<PERSON>kilMouse implements MouseListener {
    public WsylJPanel wsylJPanel;
    public int index;
    public WsylJSkilMouse(WsylJPanel wsylJPanel,int index) {
        this.wsylJPanel = wsylJPanel;
        this.index = index;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
//        Music.addyinxiao("关闭窗口.mp3");
//        if (e.getButton() == MouseEvent.BUTTON1) {//左键
//            if(wsylJPanel.getLabLock()[index].getIcon()==null){
//               wsylJPanel.changePoint(this, true);
//            }
//        } else if (e.getButton() == MouseEvent.BUTTON3) {//右键
//            wsylJPanel.changePoint(this, false);
//        }
//
//
//        int x =  wsylJPanel.getLabSkillImg()[index].getX();
//        int y =  wsylJPanel.getLabSkillImg()[index].getY();
//        wsylJPanel.getLabSkillImg()[index].setBounds(x+1,y+1,45,45);
    }

    @Override
    public void mouseReleased(MouseEvent e) {
//        int x =  wsylJPanel.getLabSkillImg()[index].getX()-1;
//        int y =  wsylJPanel.getLabSkillImg()[index].getY()-1;
//        wsylJPanel.getLabSkillImg()[index].setBounds(x,y,45,45);
    }

    @Override
    public void mouseEntered(MouseEvent e) {

//        Skill skill;
//        switch (wsylJPanel.type){
//            case 0:
//                skill= UserMessUntil.getSkillId(wsylJPanel.getSkillId(index)+"");
//                MsgJframe.getJframe().getJapnel().LX(skill != null ? skill.getSkillname() : "", getSkillMsg(skill));
//                break;
//            case 1:
//                skill = UserMessUntil.getSkillId(wsylJPanel.getSpellID(index)+"");
//                MsgJframe.getJframe().getJapnel().LX(skill != null ? skill.getSkillname() : "", getSkillMsg(skill));
//                break;
//            case 2:
//                 skill = UserMessUntil.getSkillId(wsylJPanel.getAuxiliaryID(index)+"");
//                MsgJframe.getJframe().getJapnel().LX(skill != null ? skill.getSkillname() : "", getSkillMsg(skill));
//                break;
//        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        FormsManagement.HideForm(46);
    }
    /** 生成技能描述 */
    public String getSkillMsg(Skill skill) {
//        int lv = wsylJPanel.getNowPoint()[index];
//        int maxlv = wsylJPanel.getMaxPoint(Integer.parseInt(skill.getSkillid()),index);
//        StringBuffer buffer = new StringBuffer();
//        buffer.append("#cffffff【等级】	" + lv + "/" +maxlv);
//        buffer.append("#r#cffffff【技能类别】	被动");
//        buffer.append("#r#G" + calLxNumber(skill.getRemark(),lv));
//        buffer.append("#r #r#cffffff学习条件:");
//        //TODO 这里等级打标或点数打标则标记绿色
//        buffer.append("#r#Y召唤兽要达到#R点化"+skill.getSkilllevel()+"#Y级");
//        if (StringUtils.isNotEmpty(skill.getDielectric()) && !"0.0".equals(skill.getDielectric())) {
//            buffer.append("#r#Y需已分配点数#R"+skill.getDielectric().split("\\.")[0]+"#Y点");
//        }
//        if (StringUtils.isNotEmpty(skill.getSkillralation())) {
//            buffer.append("#r #r#Y与#R"+UserMessUntil.getSkillId(skill.getSkillralation()).getSkillname()+"#Y互斥");
//        }
//        if (Integer.parseInt(skill.getSkillid()) >= 11020 && Integer.parseInt(skill.getSkillid()) <= 11022) {
//            buffer.append("#r #r#Y可同时修炼#R一往无前、有备无患、将功补过#Y中的两种技能");
//        } else if (Integer.parseInt(skill.getSkillid()) >= 11023 && Integer.parseInt(skill.getSkillid()) <= 11025) {
//            buffer.append("#r #r#Y可同时修炼#R奋不顾身、卷土重来、惊涛骇浪#Y中的两种技能");
//        } else  if (Integer.parseInt(skill.getSkillid()) >= 11039 && Integer.parseInt(skill.getSkillid()) <= 11041) {
//            buffer.append("#r #r#Y可同时修炼#R一飞冲天、展翅欲飞、青云直上#Y中的两种技能");
//        } else if (Integer.parseInt(skill.getSkillid()) >= 11042 && Integer.parseInt(skill.getSkillid()) <= 11044) {
//            buffer.append("#r #r#Y可同时修炼#R大开杀戒、锥心刺骨、哀兵必败#Y中的两种技能");
//        } else  if (Integer.parseInt(skill.getSkillid()) >= 11058 && Integer.parseInt(skill.getSkillid()) <= 11060) {
//            buffer.append("#r #r#Y可同时修炼#R有仇必报、春色满园、步步相逼#Y中的两种技能");
//        } else if (Integer.parseInt(skill.getSkillid()) >= 11061 && Integer.parseInt(skill.getSkillid()) <= 11063) {
//            buffer.append("#r #r#Y可同时修炼#R飘然出尘、碧荷凝露、焕然新生#Y中的两种技能");
//        }
//
//        if (lv !=0 && lv < maxlv) {
//            buffer.append("#r #r#cffffff下一等级:");
//            buffer.append("#r#G" + calLxNumber(skill.getRemark(),lv+1));
//        }
//        buffer.append("");
//
//        return buffer.toString();
        return "";
    }

    /**
     * 计算灵犀描述数值
     * @param msg
     * @param lvl
     * @return
     */
    public static String calLxNumber(String msg,int lvl) {
        Matcher mat = Pattern.compile("<([^>]*)>").matcher(msg);
        lvl = lvl > 0 ? lvl - 1 : lvl;
        StringBuilder sb = new StringBuilder();
        int idx = 0;
        while(mat.find()){
            String str = mat.group();
            str = str.replaceAll("<", "").replaceAll(">", "");
            if (str.indexOf("+") > -1) {
                String[] num = str.split("\\+");
                if (num.length == 2) {
                    double s = Double.parseDouble(num[0]);
                    double e = Double.parseDouble(num[1]);
                    String txt = "#R" + ((s + e*lvl)+"").split("\\.")[0] + "#G";

                    sb.append(msg.substring(idx,mat.start()));
                    sb.append(txt);
                    idx = mat.end();
                }
            } else if (str.indexOf("-") > -1) {
                String[] num = str.split("-");
                if (num.length == 2) {
                    double s = Double.parseDouble(num[0]);
                    double e = Double.parseDouble(num[1]);
                    String txt = "#R" + ((s - e*lvl)+"").split("\\.")[0] + "#G";

                    sb.append(msg.substring(idx,mat.start()));
                    sb.append(txt);
                    idx = mat.end();
                }
            }
        }
        sb.append(msg.substring(idx));
        return sb.toString();
    }
}
