package come.tool.JDialog;

import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.AccessSuitMsgUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Goodtype;
import org.come.until.Util;

import com.tool.btn.BaptizeBtn;

/**
 * 物品丢弃
 * <AUTHOR>
 *
 */
public class GoodDiscatdJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
	
		if (tishi) {
		    /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
            if (GoodsListFromServerUntil.is == GoodsListFromServerUntil.Pagenumber + 1) {
                ZhuFrame.getZhuJpanel().addPrompt2("当前包裹无法操作");
                return;
            }
			//先判断这个物品是否已加锁
			Goodstable good=GoodsListFromServerUntil.getGoodslist()[GoodsMouslisten.replace];
			if(good.getGoodlock() == 1){
				ZhuFrame.getZhuJpanel().addPrompt("该物品已加锁，不可丢弃。。");
				return;
			}
			if (Goodtype.EquipmentType(good.getType())!=-1) {
				if (AccessSuitMsgUntil.getExtra(good.getValue(),BaptizeBtn.Extras[4])!=null) {
					ZhuFrame.getZhuJpanel().addPrompt2("已镶嵌宝石无法丢弃");
					return;
				}
			}

			good.setUsetime(0);
			GoodsMouslisten.gooduse(good,1);
			GoodsListFromServerUntil.Delete(GoodsMouslisten.replace);
			GoodsMouslisten.goodreplace(-1, -1);
//            clearAllBack();
		}else {
		  GoodsMouslisten.goodreplace(-1, -1);	
		}
	}
	
	/**背包物品全部丢弃*/
	public void clearAllBack() {
	    int len = GoodsListFromServerUntil.getGoodslist().length;
        for (int i = 0; i < len; i++) {
            // 先判断这个物品是否已加锁
            Goodstable good = GoodsListFromServerUntil.getGoodslist()[i];
            if (good == null)
                continue;
            // Goodstable
            // good=GoodsListFromServerUntil.getGoodslist()[GoodsMouslisten.replace];
            if (good.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已加锁，不可丢弃。。");
                continue;
            }
            if (Goodtype.EquipmentType(good.getType()) != -1) {
                if (AccessSuitMsgUntil.getExtra(good.getValue(), BaptizeBtn.Extras[4]) != null) {
                    ZhuFrame.getZhuJpanel().addPrompt2("已镶嵌宝石无法丢弃");
                    continue;
                }
            }
            if(good.getType()!=2010){
                continue;
            }
            good.setUsetime(0);
            GoodsMouslisten.gooduse(good, 1);
            // GoodsListFromServerUntil.Delete(GoodsMouslisten.replace);
            GoodsListFromServerUntil.Delete(i);
            GoodsMouslisten.goodreplace(-1, -1);
        }
    }
}
