package jxy2.wsyl;

import org.come.until.MessagrFlagUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class Wsyl<PERSON>Mouse implements MouseListener {
    public WsylJPanel wsylJPanel;
    public int OFFSET = 185;
    public int index;
    public WsylJMouse(WsylJPanel wsylJPanel,int index) {
        this.wsylJPanel = wsylJPanel;
        this.index = index;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        wsylJPanel.changePanel(index+1);
        wsylJPanel.stopjpanel = false;
        updateCoordinates(index,51,16);
        wsylJPanel.getRichLabels()[index].setBounds(54+index*185,306,165,346-30);
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        updateCoordinates(index,50,15);
        wsylJPanel.getRichLabels()[index].setBounds(53+index*185,305,165,346-30);
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
    }
    private void updateCoordinates(int index, int baseX, int baseY) {
        switch (index) {
            case 0:
                wsylJPanel.setXs(baseX);
                wsylJPanel.setYs(baseY);
                break;
            case 1:
                wsylJPanel.setXs1(baseX + OFFSET);
                wsylJPanel.setYs1(baseY);
                break;
            case 2:
                wsylJPanel.setXs2(baseX + OFFSET * 2);
                wsylJPanel.setYs2(baseY);
                break;
        }
    }
}
