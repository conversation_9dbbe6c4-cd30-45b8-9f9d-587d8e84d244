package jxy2.LawGate;

import org.come.Frame.ZhuFrame;
import org.come.bean.Skill;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class LawGateMouse implements MouseListener {
    private int numType;
    private LawGateJPanel lawGateJPanel;

    public LawGateMouse(int numType,LawGateJPanel lawGateJPanel) {
        this.numType = numType;
        this.lawGateJPanel = lawGateJPanel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        Skill skill = UserMessUntil.getskill1(lawGateJPanel.skillname[numType].getText());
        if (skill == null) {return;}
        ZhuFrame.getZhuJpanel().LawGateStrategy(skill,lawGateJPanel.types[numType].getText(),lawGateJPanel.skillValue[numType].getText().split("/")[0]);
    }

    @Override
    public void mouseExited(MouseEvent e) {
        FormsManagement.HideForm(628);
    }
}
