package jxy2.zodiac;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.Sprite;

import java.awt.*;
import java.util.Arrays;
/**
* 封装连线逻辑
* <AUTHOR>
* @date 2024/11/26 下午4:41
*/
public class Connection {
        private int target;
        private String required;
        private Sprite sprite;
        private int x;
        private int y;

        public Connection(int target, String required, Sprite sprite, int x, int y) {
            this.target = target;
            this.required = required;
            this.sprite = sprite;
            this.x = x;
            this.y = y;
        }

        public boolean shouldDraw(int i, String[] activated) {
            return i == target && Arrays.asList(activated).contains(required);
        }

        public void draw(Graphics g) {
            sprite.updateToTime(ImageMixDeal.userimg.getTime(), 0);
            sprite.draw(g, x, y);
        }

    public Sprite getSprite() {
        return sprite;
    }

    public void setSprite(Sprite sprite) {
        this.sprite = sprite;
    }

    public int getTarget() {
        return target;
    }

    public void setTarget(int target) {
        this.target = target;
    }

    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }
}
