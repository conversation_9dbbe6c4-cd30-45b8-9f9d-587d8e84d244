package jxy2.LawGate;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.LocalTextPathUtil;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.PrivateData;
import org.skill.panel.SkillSMGatePanel;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
*法门组面板
* <AUTHOR>
* @date 2024/12/6 下午6:42
*/

public class LawGateMainJPanel  extends JPanel {
    public static LawGateJPanel[] lawGateJPanels;
    public LawGateXLBtn study;
    public LawGateMainJPanel() {
        this.setBounds(36, 62, 523, 340);
        this.setOpaque(false);
        this.setLayout(null);
        lawGateJPanels = new LawGateJPanel[3];
        for (int i = 0; i < lawGateJPanels.length; i++) {
            lawGateJPanels[i] = new LawGateJPanel(i);
            this.add(lawGateJPanels[i]);
        }
        study = new LawGateXLBtn(ImgConstants.tz112, 1, "重置",2,this);
        study.setBounds(480, 315, 34, 18);
        add(study);
    }
    /** 判断种族和性别 */
    public static void getRaceSkillPanel(BigDecimal raceId, String sex) {
        // 10001 人 10002 魔 10003 仙 10004 鬼 10005 龙
        if (raceId.compareTo(new BigDecimal("10001")) == 0) {
            if (sexisMan(sex)) {
                String skillName = "守中|血刃|乱神";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("4"),raceId,sex);
            } else {
                String skillName = "守中|乱神|神力";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("5"),raceId,sex);
            }
        } else if (raceId.compareTo(new BigDecimal("10002")) == 0) {
            if (sexisMan(sex)) {
                String skillName = "机变|神力|御兽";
                        changeSkillBar(skillName,LocalTextPathUtil.Srext("6"),raceId,sex);
            } else {
                String skillName = "戒定|神力|御兽";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("7"),raceId,sex);
            }
        } else if (raceId.compareTo(new BigDecimal("10003")) == 0) {
                String skillName = "灵光|攻坚|破壁";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("8"),raceId,sex);
        } else if (raceId.compareTo(new BigDecimal("10004")) == 0) {
            if (sexisMan(sex)) {
                String skillName = "攻坚|护持|正心";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("9"),raceId,sex);
            } else {
                String skillName = "戒定|攻坚|禁咒";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("10"),raceId,sex);
            }
        } else if (raceId.compareTo(new BigDecimal("10005")) == 0) {
                String skillName = "潜龙|亢龙|惊龙";
                changeSkillBar(skillName,LocalTextPathUtil.Srext("11"),raceId,sex);

        }
    }
    /** 判断男女 */
    public static boolean sexisMan(String sex) {
        return SkillSMGatePanel.sexisMan(sex);
    }

    public static void changeSkillBar(String skillName,String richLabel, BigDecimal raceId,String sex) {
        String[] skillNames = skillName.split("\\|");
        String[] richLabelS = richLabel.split("\\|");
        PrivateData data = RoleData.getRoleData().getPrivateData();
        String[] skills = data.getSkill("F");

        for (int i = 0; i < skillNames.length; i++) {
            lawGateJPanels[i].title = skillNames[i];
            lawGateJPanels[i].hhtext = richLabelS[i];
            String str = raceId + "|" + sex +"|"+skillNames[i];
            lawGateJPanels[i].addSkillsName(str, skills);
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.TextBackground(g, "温馨提示：每系法门均会增加一个主动技能和一个被动技能", 15, 5, 315, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);
    }
}



