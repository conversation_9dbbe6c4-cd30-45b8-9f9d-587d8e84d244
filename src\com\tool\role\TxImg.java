package com.tool.role;

import javax.swing.ImageIcon;

public class TxImg {

	private int id;
	private ImageIcon icon;
	private String wingSkin;

	public TxImg() {
		// TODO Auto-generated constructor stub
	}

	public TxImg(int id, ImageIcon icon) {
		super();
		this.id = id;
		this.icon = icon;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public ImageIcon getIcon() {
		return icon;
	}

	public void setIcon(ImageIcon icon) {
		this.icon = icon;
	}

	public String getWingSkin() {
		return wingSkin;
	}

	public void setWingSkin(String wingSkin) {
		this.wingSkin = wingSkin;
	}

}
