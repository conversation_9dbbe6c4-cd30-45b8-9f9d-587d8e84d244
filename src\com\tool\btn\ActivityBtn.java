package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.imagemonitor.ScriptOpen;
import com.tool.imagemonitor.ScriptTask;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import come.tool.map.XLPath;
import jxy2.Xy2oView.TestRankJPanel;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ActivityJpanel;
import org.come.Jpanel.ActivityModelJpanel;
import org.come.bean.Coordinates;
import org.come.bean.RoleShow;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.thread.TimeControlRunnable;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class ActivityBtn extends MoBanBtn {
    private int caozuo;
    private ActivityJpanel activityJpanel;
    private ActivityModelJpanel activityModelJpanel;
    private TestRankJPanel rankJPanel;
    private int subscript;
    private String text;


    public ActivityBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            ActivityModelJpanel activityModelJpanel) {
        super(iconpath, type, colors);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setFont(font);
        this.caozuo = caozuo;
        this.activityModelJpanel = activityModelJpanel;
    }



    public ActivityBtn(String string, int type, int caozuo, ActivityJpanel activityJpanel, String text, Color[] colors) {
        super(string,type,colors);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        this.text = text;
        this.activityJpanel = activityJpanel;
    }
    public ActivityBtn(String string, int type, int caozuo,String text, Color[] colors,TestRankJPanel rankJPanel) {
        super(string,type,colors);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        this.text = text;
        this.rankJPanel = rankJPanel;
    }



    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Graphics2D g2d = (Graphics2D) g.create();
        if (text != null) {
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            if (type==-1){type=0;}
            g2d.setColor(this.colors[type]);
            if ("激活仙令".equals(text)||"突破境界".equals(text)){
                g2d.setFont(UIUtils.TEXT_HYJ16B);
                if (type == 2) {
                    g2d.drawString(text, 16 , 18);
                } else {
                    g2d.drawString(text, 15, 17);
                }
            }else if ("领取".equals(text)){
                g2d.setFont(UIUtils.TEXT_HYJ16B);
                if (type == 2) {
                    g2d.drawString(text, 13 , 18);
                } else {
                    g2d.drawString(text, 12, 17);
                }
            }else {
                g2d.setFont(UIUtils.TEXT_HYJ19);
                Juitil.TextUi(g,text,type,14,28,13,27,3);
            }

        }
    }


    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch (caozuo) {
            case 1: {
                String sendmes = Agreement.getAgreement().TaskNAgreement("R2=" + subscript);
                SendMessageUntil.toServer(sendmes);
                break;
            }
            case 2:
                if (FightingMixDeal.State != HandleState.USUAL) {
                    return;
                }
                if (ImageMixDeal.userimg.getTeams() == null) {
                    ZhuFrame.getZhuJpanel().addPrompt2("你是队员无法操作");
                    return;
                }
                String guide = activityModelJpanel.getActiveBase().getGuide();
                String[] v = guide.split("-");
                if (v.length == 5) {
                    TimeControlRunnable.addTask(new ScriptTask(v, activityModelJpanel.getActiveBase().getSid()));
                    return;
                }
                RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
                Coordinates coordinates = new Coordinates(Integer.parseInt(v[0]), Integer.parseInt(v[1]), Integer.parseInt(v[2]));
                List<Object> list = XLPath.ZDXL(roleShow.getX(), roleShow.getY(), roleShow.getMapid().intValue(), coordinates.getX(), coordinates.getY(), coordinates.getMapID());
                if (list == null) {
                    ZhuFrame.getZhuJpanel().addPrompt2("你所在位置无法达到目的地");
                    return;
                }
                ScriptOpen open = new ScriptOpen(1);
                open.setNpc(Integer.parseInt(v[3]));
                list.add(0, open);
                TimeControlRunnable.addScript(list);
                break;
            case 3:
                activityJpanel.getXyl().btnchange(0);
                activityJpanel.getMri().btnchange(2);
                activityJpanel.setIndex(0);
                break;
            case 4:
                activityJpanel.getXyl().btnchange(2);
                activityJpanel.getMri().btnchange(0);
                activityJpanel.setIndex(1);
                break;
            case 5: {
                String sendmes = Agreement.getAgreement().Account_KtxlAgreement("K");
                SendMessageUntil.toServer(sendmes);
                break;
            }
            case 6: {
                int currentValue = activityJpanel.getPeneItme0Lits().getHorizontalScrollBar().getValue();
                // 向右移动，可以根据需要设置移动的步长
                activityJpanel.getPeneItme0Lits().getHorizontalScrollBar().setValue(currentValue + 60); // 例如，每次移动50像素

                break;
            }
            case 7: {
                int currentValue = activityJpanel.getPeneItme0Lits().getHorizontalScrollBar().getValue();
                // 向左移动，可以根据需要设置移动的步长
                activityJpanel.getPeneItme0Lits().getHorizontalScrollBar().setValue(currentValue - 60); // 例如，每次移动50像素

                break;
            }
            case 8: {//突破境界
                String mes = Agreement.getAgreement().JliRewardAgreement("T");
                SendMessageUntil.toServer(mes);
                break;
            }
            case 9: {//领取聚灵奖励
                String mes = Agreement.getAgreement().JliRewardAgreement("J");
                SendMessageUntil.toServer(mes);
                break;
            }
        }
        if (activityJpanel!=null){
            activityJpanel.initBtnState(caozuo);
        }


    }

    @Override
    public void mouseEntered(MouseEvent e) {
        super.mouseEntered(e);
        if (caozuo == 1) {
            String name = this.getName();
//            String vitality = activityJpanel.getLabVitality()[subscript].getText();
//            MsgJframe.getJframe().getJapnel().TYC(vitality + "活跃奖励", name);
        } else if (caozuo == 2) {

        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        super.mouseExited(e);
        if (caozuo == 1) {
            FormsManagement.HideForm(46);// 隐藏窗体
        }
    }

}
