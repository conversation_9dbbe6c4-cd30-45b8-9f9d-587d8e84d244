package com.tool.imagemonitor;

import come.tool.Fighting.FightingEvents;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.FightingState;
import come.tool.Fighting.Fightingimage;
import come.tool.handle.HandleState;
import org.come.Frame.ZhuFrame;
import org.come.bean.FightOperation;
import org.come.entity.Goodstable;
import org.come.mouslisten.GoodsMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.MessagrFlagUntil;

import java.util.ArrayList;
import java.util.List;
/**
 * 战斗图像监听
 * <AUTHOR>
 *
 */
public class FightingMonitor {
	//人的操作记录
	public static FightOperation RoleOperation=new FightOperation();
	//召唤兽的操作记录
	public static FightOperation PetOperation=new FightOperation();;	
	public static int mousestate=0;//鼠标状态  0普通攻击(默认) 1法术攻击 2物品使用 3保护 4捕捉
	//战斗点击的类型
	public static String mousesname="普通攻击";
	public static void Fighting(Fightingimage fightingimage) {
	// TODO Auto-generated constructor stub
		int man=FightingMixDeal.myman();
		if (FightingMixDeal.State==HandleState.HANDLE_PET) man+=5;
		if (mousestate==0) {
			if (DecisionAttack(man, fightingimage)) {
				FightOperation operation=FightingMonitor.getOperation();
				operation.Record(fightingimage.getFightingManData().getCamp(),fightingimage.getFightingManData().getMan(),mousestate, null);
				FightingMonitor.execution(operation);	
	        }
		}else if (mousestate==1) {
			if (DecisionSpell(mousesname, man, fightingimage)) {
				FightOperation operation=FightingMonitor.getOperation();
				operation.Record(fightingimage.getFightingManData().getCamp(),fightingimage.getFightingManData().getMan(),mousestate, mousesname);
				FightingMonitor.execution(operation);	
			}
		}else if (mousestate==2) {
			FightOperation operation=FightingMonitor.getOperation();
			operation.Record(fightingimage.getFightingManData().getCamp(),fightingimage.getFightingManData().getMan(),mousestate, mousesname);
			FightingMonitor.execution(operation);	
		}else if (mousestate==3) {
			if (youfang(fightingimage)) {
				if (!ziji(man, fightingimage)) {
					FightOperation operation=FightingMonitor.getOperation();
					operation.Record(fightingimage.getFightingManData().getCamp(),fightingimage.getFightingManData().getMan(),mousestate,null);
					FightingMonitor.execution(operation);	
				}
			}
		}else if (mousestate==4) {
			//判断是否是敌方且战斗类型是0
			if ((FightingMixDeal.BattleType==0||FightingMixDeal.BattleType==1||FightingMixDeal.BattleType==2)&&!youfang(fightingimage)) {
				FightOperation operation=FightingMonitor.getOperation();
				operation.Record(fightingimage.getFightingManData().getCamp(),fightingimage.getFightingManData().getMan(),mousestate,null);
				FightingMonitor.execution(operation);	
			}
		}
    }
	/**判断是否是友方*/
	public static boolean youfang(Fightingimage fightingimage){
		if (fightingimage.getFightingManData().getCamp()==FightingMixDeal.camp) {
				return true;
		}
		return false;
	}
	/**判断点击的人是否有隐身状态*/
	public static boolean yinshen(Fightingimage fightingimage){
		return fightingimage.getFightingManData().getAlpha()==1.0f?true:false;
	}
	/**判断点击的人是否是自己*/
	public static boolean ziji(int man,Fightingimage fightingimage){
		if (fightingimage.getFightingManData().getCamp()==FightingMixDeal.camp
				&&fightingimage.getFightingManData().getMan()==man) {
			return true;
		}
		return false;
	}
	/**普通攻击对象判断*/
	public static boolean DecisionAttack(int man,Fightingimage fightingimage){
		if (yinshen(fightingimage)&&!ziji(man, fightingimage)) {
			return true;
		}
		return false;
	}
	/**法术攻击对象判断*/
	public static boolean DecisionSpell(String skillname,int man,Fightingimage fightingimage){
		if (skillname.equals("移花接木")||skillname.equals("大手印"))return true;
		if (Effect(skillname)) {
			if (youfang(fightingimage)) return true;	
			else return false;
		}else {
			if (!youfang(fightingimage)&&yinshen(fightingimage)) return true;	
			else return false;
		}
	}
	/**判断该法术是否可以作用于友方*/
	public static boolean Effect(String skillname) {
		if (skillname.equals("绝境逢生") || skillname.equals("子虚乌有")
				|| skillname.equals("春回大地") || skillname.equals("妙手回春")
				|| skillname.equals("春意盎然") || skillname.equals("忠诚")
				|| skillname.equals("自医") || skillname.equals("遗产")
				|| skillname.equals("隐身") || skillname.equals("仁之木叶神")
				|| skillname.equals("智之水叶神")
				|| skillname.equals("礼之火叶神") || skillname.equals("大隐于朝")
				|| skillname.equals("鬼神莫测") || skillname.equals("神出鬼没")
				|| skillname.equals("作鸟兽散") || skillname.equals("将死")
				|| skillname.equals("妖之魔力") || skillname.equals("力神复苏")
				|| skillname.equals("红袖添香") || skillname.equals("莲步轻舞")
				|| skillname.equals("魔之飞步") || skillname.equals("急速之魔")
				|| skillname.equals("幽怜魅影") || skillname.equals("醉生梦死")
				|| skillname.equals("狮王之怒") || skillname.equals("兽王神力")
				|| skillname.equals("魔神附身") || skillname.equals("楚楚可怜")
				|| skillname.equals("魔神护体") || skillname.equals("含情脉脉")
				|| skillname.equals("魔神飞舞") || skillname.equals("天外飞魔")
				|| skillname.equals("乾坤借速") || skillname.equals("一曲销魂")
				|| skillname.equals("秦丝冰雾") || skillname.equals("倩女幽魂")
				|| skillname.equals("泽披天下") || skillname.equals("将军令")
				|| skillname.equals("大势锤") || skillname.equals("七宝玲珑塔")
				|| skillname.equals("黑龙珠") || skillname.equals("幽冥鬼手")
				|| skillname.equals("绝情鞭") || skillname.equals("宝莲灯")
				|| skillname.equals("番天印") || skillname.equals("锦襕袈裟")
				|| skillname.equals("银索金铃") || skillname.equals("飞花溅玉")
				|| skillname.equals("百害不侵") || skillname.equals("明心见性")
				|| skillname.equals("五蕴炽盛") || skillname.equals("清江映雪")
				|| skillname.equals("回光返照") || skillname.equals("吹箫引凤")
				|| skillname.equals("六根清净") || skillname.equals("因缘际会")
				|| skillname.equals("以直报怨") || skillname.equals("如有神助")
				|| skillname.equals("破釜沉舟") || skillname.equals("弱水三千")
				|| skillname.equals("流风回雪") || skillname.equals("岳镇渊渟")
				|| skillname.equals("穿针引线") || skillname.equals("鸿雁长飞")
				|| skillname.equals("销声匿迹") || skillname.equals("一苇渡江")
				|| skillname.equals("苦海慈航") || skillname.equals("拔山")
				|| skillname.equals("七星贯日") || skillname.equals("御龙")
				|| skillname.equals("覆雨")|| skillname.equals("震风陵雨")
				|| skillname.equals("引火烧身") || skillname.equals("久旱初雨")
				|| skillname.equals("兴云致雨") || skillname.equals("润物无声")
				|| skillname.equals("沛然莫御") || skillname.equals("泽被万物")
				|| skillname.equals("春暖花开")|| skillname.equals("瞒天过海")
				|| skillname.equals("双生两仪盾")|| skillname.equals("行眠立盹")|| skillname.equals("佳期如梦")) {
			return true;
		}
		return false;
	}	
	/**向服务器发送战斗操作*/
	public static void FightingOperation(FightingEvents fightingEvents) {
		if (FightingMixDeal.camp == -1) return;
		fightingEvents.setFightingsum(FightingMixDeal.FightingNumber);
		fightingEvents.setCurrentRound(FightingMixDeal.CurrentRound);
		String sendMes = Agreement.getAgreement().battleroundAgreement(GsonUtil.getGsonUtil().getgson().toJson(fightingEvents));
		SendMessageUntil.toServer(sendMes);
		FormsManagement.HideForm(34);
		FormsManagement.HideForm(6);
	}
	public static void execution(FightOperation Operation){
		execution(Operation, false);
	}
	//执行操作 true 是自动
	public static void execution(FightOperation Operation,boolean is){
	    if (FightingMixDeal.State==HandleState.HANDLE_PLAYER) {
		    if (!is&&Operation.getType()!=6&&Operation.getType()!=5&&FightingMixDeal.isLL()) { return; }
	    }else if (FightingMixDeal.State!=HandleState.HANDLE_PET) {
		    return;
	    }
	    FightingEvents events=AttackGenerate(Operation);
	    FightingOperation(events);
	    operateEnd();
	}
	/**点击生效处理*/
	public static void operateEnd(){
		MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		 mousestate=0;
		 mousesname="普通攻击";
		 if (FightingMixDeal.State==HandleState.HANDLE_PLAYER&&FightingMixDeal.MyBeastLifeAndDeath()) {
			FightingMixDeal.changeState(HandleState.HANDLE_PET);
			ZhuFrame.getZhuJpanel().HideBeastBtn();
			ZhuFrame.getZhuJpanel().ShowBeastBtn();	
		 }else{	
			FightingMixDeal.changeState(HandleState.HANDLE_WAIT);
			FightingMixDeal.RoundFighting();		 
		 }
	}	
	//获取当前是人是兽
	public static FightOperation getOperation(){
		if (FightingMixDeal.State==HandleState.HANDLE_PLAYER) {
			return RoleOperation;
		}else {
		    return PetOperation;
		}
	}
	   //生成战斗指令
	   public static FightingEvents AttackGenerate(FightOperation operation){
			FightingEvents fEvents=new FightingEvents();
			List<FightingState> fList=new ArrayList<>();
		    int type=0;
		    if (FightingMixDeal.State==HandleState.HANDLE_PET) {
		    	type=1;
			}
		    fEvents.setOriginator(FightingMixDeal.FightingState("普通攻击", type));//设置事件
		    //当前被打人物	
		    switch (operation.getType()) {
			case 1:
				fEvents.getOriginator().setStartState("技能");
				fEvents.getOriginator().setEndState(operation.getSpell());
				break;
			case 2:
				Goodstable goodstable=GoodsListFromServerUntil.getRgid(operation.getGood());
				if (goodstable!=null) {
					fEvents.getOriginator().setStartState("药");
					fEvents.getOriginator().setEndState(goodstable.getValue());	
					goodstable.goodxh(1);
					GoodsMouslisten.gooduse(goodstable,1);
					if (goodstable.getUsetime()<=0){
						GoodsListFromServerUntil.Deletebiaoid(goodstable.getRgid());
						operation.setCamp(-1);
						operation.setMan(-1);
						operation.setType(0);
					}
				}else {
					operation.setCamp(-1);
					operation.setMan(-1);
				}
				break;
			case 3:
				fEvents.getOriginator().setStartState("保护");
				break;
			case 4:
				fEvents.getOriginator().setStartState("捕捉");
				break;
			case 5:
				fEvents.getOriginator().setStartState("防御");
				break;
			case 6:
				fEvents.getOriginator().setStartState("逃跑");
				break;
			}

			FightingState dqstate=new FightingState(operation.getCamp(),operation.getMan(),null);
			fList.add(dqstate);
			fEvents.setAccepterlist(fList);
			return fEvents;		
	   }
}
