package jxy2.refine;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.RefiningUtil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 装备打造面板
* <AUTHOR>
* @date 2024/7/28 下午8:11
*/
public class EqmMakeJpanel  extends JPanel {
    public String[] ordinary;
    public EqMakeBtn[] eqMake =new EqMakeBtn[5];//装备打造小菜单按钮
    public JLabel[] sixEquipment = new JLabel[2];
    private int minType;
    public Goodstable[] goods = new Goodstable[2];
    private EqMakeBtn workshopBtn;// 炼化装备按钮
    public BigDecimal money;
    public EqmMakeJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        money = new BigDecimal(100000);
        ordinary = new String[]{"普通装备","高级装备","仙器洗练","仙器升级","仙器合成"};
        for (int i = 0; i < eqMake.length; i++) {
            eqMake[i] = new EqMakeBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, ordinary[i], i, this,"");
            eqMake[i].btnchange(i==0?2:0);
            eqMake[i].setBounds(138+i*66, 48, 66, 24);
            add( eqMake[i]);
        }
        for (int i = 0; i < sixEquipment.length; i++) {
            sixEquipment[i] = new JLabel();
            sixEquipment[i].addMouseListener(new RefineMouse(this, 24 + i));
            sixEquipment[i].setBounds(215 + i * 134, 202, 50, 50);
            this.add(sixEquipment[i]);
        }
        // 炼化装备按钮
        workshopBtn = new EqMakeBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 99, this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);
    }
    /**清除*/
    public void clear() {
        for (int i = 0; i < 2; i++) {
            goods[i] = null;
            sixEquipment[i].setIcon(null);
        }
        workshopBtn.setText("?");
    }

    public void ClickGood(Goodstable good, int path) {
        if (path < 24) {
            boolean a = true;
            if (good!=null&&good.getType()==500) {
                int i =1;
                   if (good.getType()==500){
                       i= 1;
                   }
                if (goods[i] == null) {
                    change(good, i);
                    a = false;
                } else if (!goods[i].getType().equals(good.getType())) {
                    change(good, i);
                    a = false;
                }
            }

            if (a){
                for (int i = 0; i < 2; i++) {
                    if (goods[i] == null) {
                        change(good, i);
                        break;
                    }
                }
            }
        } else {
            change(null, path - 24);
        }
        String v = RefiningUtil.detection(goods,3);
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }

    /** 切换指定位置·炼器 */
    public void change(Goodstable good, int path) {
        goods[path] = good;
        if (good != null) {
            sixEquipment[path].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        } else {
            sixEquipment[path].setIcon(null);
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        for (int i = 0; i < 2; i++) {
            Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
        }
        Textdrawing(g,minType+16);
        switch (minType) {
            case 0:
                break;
            case 1:
                Juitil.ImngBack(g, Juitil.good_2, 282, 268 - 66, 50, 50, 1);
                Juitil.TextBackground(g, "巫铸", 13, 293, 285 - 66, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                break;
            case 2:
                break;
            case 3:
                break;
            case 4:
                break;
        }
    }
    /**文本绘制*/
    public void Textdrawing(Graphics g, int index){
        for (int i = 0; i < 2; i++) {
            if (sixEquipment[i].getIcon() == null) {
                //文本剧中显示
                int x = Juitil.CenterTextdrawing(g, getPrompt(index)[i], 238, 134, i, UIUtils.FZCY_HY13);
                //绘制文本
                Juitil.TextBackground(g, getPrompt(index)[i], 13, x, 220, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }
        }
    }


    public String[] getPrompt(int type) {
        String[] result = EqartificeJapanel.PROMPT_MAP.get(type);
        return result != null ? result.clone() : null;
    }

    public String[] getOrdinary() {
        return ordinary;
    }

    public void setOrdinary(String[] ordinary) {
        this.ordinary = ordinary;
    }

    public EqMakeBtn[] getEqMake() {
        return eqMake;
    }

    public void setEqMake(EqMakeBtn[] eqMake) {
        this.eqMake = eqMake;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public EqMakeBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(EqMakeBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }


}
