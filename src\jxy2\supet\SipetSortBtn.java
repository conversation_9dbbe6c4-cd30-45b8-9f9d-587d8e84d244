package jxy2.supet;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.AlchemyJframe;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class SipetSortBtn extends MoBanBtn {
    private SipetSortJPanel sipetSortJPanel;
    public SipetSortBtn(String iconpath, int type, String text,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.index=index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public SipetSortBtn(String iconpath, int type,int index, SipetSortJPanel sipetSortJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,"","");
        setFont(UIUtils.TEXT_FONT);
        this.index=index;
        this.sipetSortJPanel=sipetSortJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        SipetSortJPanel sipetSortJPanel = SipetSortFrame.getSipetSortFrame().getSipetSortJPanel();
        SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
        JupoDanJPanel jupoDanJPanel = JupoDanFrame.getJupoDanFrame().getJupoDanJPanel();
        int w= sipetSortJPanel.getListpet().getSelectedIndex();
        if (w==-1) {return;}
        RoleData data=RoleData.getRoleData();
        List<String> list;
        if (index==1) {
            list=data.PetBb(w, w-1);
        }else if (index==2) {
            list=data.PetBb(w, w+1);
        }else if (index==3) {
            list=data.PetBb(w, 0);
        }else {
            list=data.PetBb(w, 99);
        }
        if (list!=null) {

            sipetSortJPanel.showStar();
            sipetJPanel.RefreshPetViewport();
            jupoDanJPanel.RewriteState(UserMessUntil.getChosePetMes());
            SipetJPanel.getInstance().RefreshPetViewport();
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().ServerObtainsData();
        }
    }
}
