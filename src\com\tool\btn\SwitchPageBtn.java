package com.tool.btn;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SynthesisJpanel;
import org.come.Jpanel.TransferJpanel;
import org.come.Jpanel.UpgradeJpanel;
import org.come.Jpanel.WashJpanel;
import org.come.until.AccessSuitMsgUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserData;

import com.tool.role.RoleData;

public class SwitchPageBtn extends MoBanBtn {
	
	private int caozuo;
	public SwitchPageBtn(String iconpath, int type, int caozuo) {
		super(iconpath, type);
		this.caozuo = caozuo;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void nochoose(MouseEvent e) {
		
		if(caozuo == 1){ //上一页（套装合成）
			if(AccessSuitMsgUntil.accessIdlEqu(1) == null)return;
			if(SynthesisJpanel.page - 1 >= 0){
				SynthesisJpanel.page -= 1;
			}
		}else if(caozuo == 2){ //下一页（套装合成）
			if(AccessSuitMsgUntil.accessIdlEqu(1) == null)return;
			if(AccessSuitMsgUntil.accessIdlEqu(1).size() > (SynthesisJpanel.page+1)*18){
				SynthesisJpanel.page += 1;
			}
		}else if(caozuo == 3){ //上一页（套装洗炼）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(WashJpanel.page - 1 >= 0){
				WashJpanel.page -= 1;
			}
		}else if(caozuo == 4){ //下一页（套装洗炼）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(AccessSuitMsgUntil.accessIdlEqu(2).size() > (WashJpanel.page+1)*21){
				WashJpanel.page += 1;
			}
		}else if(caozuo == 5){ //上一页（套装升级）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(UpgradeJpanel.page - 1 >= 0){
				UpgradeJpanel.page -= 1;
			}
		}else if(caozuo == 6){ //下一页（套装升级）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(AccessSuitMsgUntil.accessIdlEqu(2).size() > (UpgradeJpanel.page+1)*21){
				UpgradeJpanel.page += 1;
			}
		}else if(caozuo == 7){ //上一页（拆解/转移-套装）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(TransferJpanel.page - 1 >= 0){
				TransferJpanel.page -= 1;
			}
		}else if(caozuo == 8){ //下一页（拆解/转移-套装）
			if(AccessSuitMsgUntil.accessIdlEqu(2) == null)return;
			if(AccessSuitMsgUntil.accessIdlEqu(2).size() > (TransferJpanel.page+1)*9){
				TransferJpanel.page += 1;
			}
		}else if(caozuo == 9){ //上一页（拆解/转移）
			if(AccessSuitMsgUntil.accessIdlEqu(1) == null)return;
			if(TransferJpanel.page2 - 1 >= 0){
				TransferJpanel.page2 -= 1;
			}
		}else if(caozuo == 10){ //下一页（拆解/转移）
			if(AccessSuitMsgUntil.accessIdlEqu(1) == null)return;
			if(AccessSuitMsgUntil.accessIdlEqu(1).size() > (TransferJpanel.page2+1)*9){
				TransferJpanel.page2 += 1;
			}
		}else if(caozuo == 11){ //加收录套装上限
			//先判断金币是否足够
			if(RoleData.getRoleData().getLoginResult().getGold().compareTo(new BigDecimal(100000000)) < 0){
				ZhuFrame.getZhuJpanel().addPrompt("需要1E金币才能增加上限..");
				return;
			}
			int num = RoleData.getRoleData().getPackRecord().getSuitNum()+1;
			RoleData.getRoleData().getPackRecord().setSuitNum(num);
			//发送消息给服务器
			GoodsListFromServerUntil.sendPackRecord(3, num+"");
			//扣除金币
			UserData.uptael(new BigDecimal(100000000).longValue());
		}
	}

}
