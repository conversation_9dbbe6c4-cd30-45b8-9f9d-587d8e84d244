package com.tool.btn;

import com.tool.imagemonitor.NpcMonitor;
import org.come.Frame.MsgJframe;
import org.come.bean.PathPoint;
import org.come.test.Main;
import org.come.until.FormsManagement;

import java.awt.event.MouseEvent;

public class GangGroupBtn extends MoBanBtn {
	private int npcType;
	private int lvl;
    public GangGroupBtn(String iconpath, int type, int npcType) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.npcType=npcType;
    }
	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		if (npcType!=0) {
			NpcMonitor.npc(npcType);	
		}
	}
    @Override
	public void mouseEntered(MouseEvent e) {
    	super.mouseEntered(e);
    	if (npcType==2021||npcType==2022||npcType==2023) {
    		PathPoint point= Main.frame.getLoginJpanel().mousepath();
    		MsgJframe.getJframe().getJapnel().ewts("等级  "+lvl, point.getX(), point.getY());
		}
    }
    @Override
	public void mouseExited(MouseEvent e) {
    	super.mouseExited(e);
    	if (npcType==2021||npcType==2022||npcType==2023) {
        	FormsManagement.HideForm(46);	
    	}
    }
	public void setLvl(int lvl) {
		this.lvl = lvl;
	}
    
}
