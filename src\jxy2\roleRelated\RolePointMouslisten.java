package jxy2.roleRelated;

import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.until.AnalysisString;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class RolePointMouslisten  implements MouseListener {

    private int type,index;
    public RolePointMouslisten(int type,int index) {
        super();
        this.type = type;
        this.index = index;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        point(type,(e.isShiftDown()?10:1)*(type%2==0?-1:1));
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    /**
     * 加点
     */
    public void point(int type,int point){
        if (type<10) {
            rolepoint(RoleData.getRoleData().getLoginResult(), type, point);
        }
    }
    /**
     * 人物加点
     */
    public void rolepoint(LoginResult loginResult, int type, int point){
        type=type/2;
        RoleSwitchJPanel jpanel= RoleSwitchFrame.getRoleSwitchFrame().getRoleSwitchJPanel();
        point=addpoint(getdz(type, loginResult), jpanel.getdian(type,index),jpanel.getdian(5,index), point);
        if (point==0){
            ZhuFrame.getZhuJpanel().addPrompt2("已无法改变点数");
            return;
        }
        jpanel.adddian(type, point,index);
    }
    public int addpoint(int d,int z,int f,int point){
        if (d>z+point) point=d-z;
        if (f-point<0) point=f;
        return point;
    }

    public int getdz(int type, LoginResult result){
        if (result.getSpoint()==null){
            int gg = result.getBone() - (result.getBone()- AnalysisString.lvlint(result.getGrade()));
            int lx = result.getSpir() - (result.getSpir()-AnalysisString.lvlint(result.getGrade()));
            int ll = result.getPower() - (result.getPower()-AnalysisString.lvlint(result.getGrade()));
            int mj = result.getSpeed() - (result.getSpeed()-AnalysisString.lvlint(result.getGrade()));
            int dl = result.getCalm() - (result.getCalm()- AnalysisString.lvlint(result.getGrade()));
            if (type==0) {
                type= gg;
            }else if (type==1) {
                type=lx;
            }else if (type==2) {
                type=ll;
            }else if (type==3) {
                type=mj;
            }else if (type==4) {
                type=dl;
            }
        }else {
            String[] str = result.getSpoint().split("\\|");
            for (String string : str) {
                String[] vs = string.split("&")[0].split("=");
                int  gg = Integer.parseInt(vs[0].split("_")[1].substring(1));//根骨
                int  lx = Integer.parseInt(vs[1]);//灵力
                int  ll = Integer.parseInt(vs[2]);//力量
                int  mj = Integer.parseInt(vs[3]);//敏捷
                int  dl = Integer.parseInt(vs[4]);//定力
                if (index == 0 && string.startsWith("D_Y") || index == 1 && string.startsWith("D_R") || index==2&&string.startsWith("D_S")){
                    if (type==0) {
                        type= gg;
                    }else if (type==1) {
                        type=lx;
                    }else if (type==2) {
                        type=ll;
                    }else if (type==3) {
                        type=mj;
                    }else if (type==4) {
                        type=dl;
                    }
                }

            }
        }



        return type;
    }
}
