package come.tool.JDialog;

import org.come.Frame.LingbaoJframe;
import org.come.model.Lingbao;
import org.come.until.UserData;

import com.tool.role.RoleProperty;

/**
 * 灵宝抗性转换
 * <AUTHOR>
 */
public class LingKangJDialog implements TiShiChuLi{
	
	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		
		
		if (tishi) {
           Lingbao lingbao=(Lingbao)object;
			if (UserData.uptael(200000)) {
		   //切换抗性
			lingbao.setKangxing(UserData.kangxing(lingbao.getLingbaolvl().intValue()));
			//向服务器发修改后的灵宝
			UserData.upling(lingbao);
		    RoleProperty.ResetEw();
			//刷新面板
			LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel().shu<PERSON><PERSON><PERSON><PERSON>(lingbao);
	    	}
		}
	}
}
