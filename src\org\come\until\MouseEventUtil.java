package org.come.until;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class MouseEventUtil {
    /**
     * 检测是否为右键点击（兼容Java 8-21）
     */
    public static boolean isRightClick(MouseEvent e) {
        return javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                (e.isMetaDown() && System.getProperty("os.name").toLowerCase().contains("windows"));
    }

    /**
     * 检测是否为左键点击
     */
    public static boolean isLeftClick(MouseEvent e) {
        return SwingUtilities.isLeftMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON1;
    }
}