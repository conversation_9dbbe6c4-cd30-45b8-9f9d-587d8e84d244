package jxy2.wdfDome.view;

import jxy2.wdfDome.bean.UpdateInterface;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;

public class AnimationPane extends JPanel implements UpdateInterface {
    private final String name;
    private final BufferedImage[] bufImage;
    private int index;
    private final Color c1 = new Color(4210752);
    private final Color c2 = new Color(8421504);
    private boolean zoom = false;

    public AnimationPane(String name, BufferedImage[] bufImage) {
        super(new BorderLayout());
        this.name = name;
        this.bufImage = bufImage;
        this.index = 0;
        this.setPreferredSize(new Dimension(bufImage[0].getWidth(), bufImage[0].getHeight()));
    }
    public String getItemName() {
        return this.name;
    }

    public BufferedImage[] getItemBufImages() {
        return this.bufImage;
    }

    public AnimationPane copyOne() {
        return new AnimationPane(this.name, this.bufImage);
    }

    public void setZoom(boolean zoom) {
        this.zoom = zoom;
    }

    public void paint(Graphics g) {
        g.setColor(this.c1);
        g.clearRect(0, 0, this.getWidth(), this.getHeight());
        g.fillRect(0, 0, this.getWidth(), this.getHeight());
        if (this.bufImage[this.index] != null) {
            BufferedImage image = this.bufImage[this.index];
            g.setColor(this.c2);
            int width;
            int height;
            if (!this.zoom || image.getWidth() <= this.getWidth() && image.getHeight() <= this.getHeight()) {
                width = (this.getWidth() - image.getWidth()) / 2;
                height = (this.getHeight() - image.getHeight()) / 2;
                g.drawRect(width, height, image.getWidth(), image.getHeight());
                g.drawImage(image, width, height, this);
            } else {
                width = this.getWidth();
                height = this.getHeight();
                if (image.getWidth() - width > image.getHeight() - height) {
                    width = this.getWidth();
                    height = (int)((double)width / (double)image.getWidth() * (double)image.getHeight());
                } else {
                    height = this.getHeight();
                    width = (int)((double)height / (double)image.getHeight() * (double)image.getWidth());
                }

                int ox = (this.getWidth() - width) / 2;
                int oy = (this.getHeight() - height) / 2;
                g.drawImage(image, ox, oy, width, height, null, this);
            }
        }

        g.setColor(Color.BLACK);
        g.drawRect(0, 0, this.getWidth(), this.getHeight());
        g.setColor(Color.WHITE);
        g.drawString(this.name, 5, 15);
        ++this.index;
        if (this.index >= this.bufImage.length) {
            this.index = 0;
        }

    }

    @Override
    public void updateNow() {
        repaint();
    }
}
