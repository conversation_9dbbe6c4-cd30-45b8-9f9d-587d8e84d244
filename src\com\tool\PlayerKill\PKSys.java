package com.tool.PlayerKill;

import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.bean.Middle;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;
import org.come.until.Util;

import com.tool.role.RoleData;


/**
 * pk系统
 * <AUTHOR>
 */
public class PKSys {
//	PK点数=身份标志=做天牢次数=每周坐牢次数
	private static PKSys pkSys;
	//记录 pk标识      默认 0
	private int pk1=0;
	//身份 0良民 1出狱需贿赂 2出狱不需贿赂
	private int pk2=0;
	//记录 做天牢次数   
	private int pk3=0;
	//记录 每周坐牢次数 无默认
	private int pk4=0;
	//度过的时间  野外或者是监狱
	private long JailTime;
	public static PKSys getPkSys(){
		if (pkSys == null)initial();
		return pkSys;
	}
	public void upPK(String taskDaily){
		try {
			RoleData.getRoleData().getLoginResult().setTaskDaily(taskDaily);
			String[] v=RoleData.getRoleData().getLoginResult().getTaskDaily().split("\\|");
			pk1=Integer.parseInt(v[0]);
			pk2=Integer.parseInt(v[1]);
			pk3=Integer.parseInt(v[2]);
			pk4=Integer.parseInt(v[3]);		
		} catch (Exception e) {
			
		}
	}
	public static void initial(){
		pkSys=new PKSys();
		try {
			String[] v=RoleData.getRoleData().getLoginResult().getTaskDaily().split("\\|");
			pkSys.pk1=Integer.parseInt(v[0]);
			pkSys.pk2=Integer.parseInt(v[1]);
			pkSys.pk3=Integer.parseInt(v[2]);
			pkSys.pk4=Integer.parseInt(v[3]);		
		} catch (Exception e) {
			
		}
	}
	/**
	 * 判断时间是否减轻罪行
	 * @return
	 */
	public void isexpiation(){
      if (pk1<=0)return;
  	    JailTime+=75000;
		if (pk2!=0) {
            if (PKMixdeal.getJailTime(pk4)<JailTime)expiation();
		}else if (Util.CREEPSMAP) {
            if (PKMixdeal.YW<JailTime)expiation();
		}
	}
	/**
	 * 减轻罪行
	 * @return
	 */
	public void expiation(){
		JailTime=0;
		pk1--;
		ZhuFrame.getZhuJpanel().addPrompt2("你减少了一点pk标识你还有#G "+pk1+" #Y点PK标志");
		LoginResult loginResult=RoleData.getRoleData().getLoginResult();
		loginResult.setTaskDaily(splicing());
		Middle middle=new Middle();
		middle.setRolename(loginResult.getRolename());
		middle.setTaskDaily(loginResult.getTaskDaily());
		String mes = Agreement.getAgreement().MiddleAgreement(GsonUtil.getGsonUtil().getgson().toJson(middle));
		SendMessageUntil.toServer(mes);
	}
	public String splicing(){
		return pkSys.pk1+"|"+pkSys.pk2+"|"+pkSys.pk3+"|"+pkSys.pk4;
	}
	public int getPk1() {
		return pk1;
	}
	public void setPk1(int pk1) {
		this.pk1 = pk1;
	}
	public int getPk2() {
		return pk2;
	}
	public void setPk2(int pk2) {
		this.pk2 = pk2;
	}
	public int getPk3() {
		return pk3;
	}
	public void setPk3(int pk3) {
		this.pk3 = pk3;
	}
	public int getPk4() {
		return pk4;
	}
	public void setPk4(int pk4) {
		this.pk4 = pk4;
	}
}
