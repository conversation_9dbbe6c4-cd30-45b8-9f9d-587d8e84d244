package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.NewRefiningJframe;
import org.come.Frame.PartnerArenaExchangeJframe;
import org.come.Jpanel.NewRefiningJpanel;
import org.come.Jpanel.PartnerArenaExchangeModelPanel;
import org.come.Jpanel.PartnerArenaMainPanel;
import org.come.Jpanel.PartnerArenaModelPanel;
import org.come.bean.OneArenaRole;
import org.come.model.Achieve;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;

/**
 * <AUTHOR>
 * @time 2020年3月19日 下午3:28:24<br>
 * @class 类名:PartnerArenaBtn 伙伴竞技按钮 <br>
 */
public class PartnerArenaBtn extends MoBanBtn {

    private int caozuo;
    private PartnerArenaMainPanel partnerArenaMainPanel;
    private PartnerArenaModelPanel partnerArenaModelPanel;
    private PartnerArenaExchangeModelPanel partnerArenaExchangeModelPanel;

    /** 1战报 2兑换 5领取每日奖励 6称号炼化 */
    public PartnerArenaBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            PartnerArenaMainPanel partnerArenaMainPanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.partnerArenaMainPanel = partnerArenaMainPanel;
        this.caozuo = caozuo;
    }

    /** 3挑战 */
    public PartnerArenaBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            PartnerArenaModelPanel partnerArenaModelPanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.partnerArenaModelPanel = partnerArenaModelPanel;
        this.caozuo = caozuo;
    }
    /** 4领取称号 */
    public PartnerArenaBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            PartnerArenaExchangeModelPanel partnerArenaExchangeModelPanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.partnerArenaExchangeModelPanel = partnerArenaExchangeModelPanel;
        this.caozuo = caozuo;
    }

    /** 0增加次数 */
    public PartnerArenaBtn(String iconpath, int type, int caozuo, PartnerArenaMainPanel partnerArenaMainPanel) {
        super(iconpath, type);
        this.partnerArenaMainPanel = partnerArenaMainPanel;
        this.caozuo = caozuo;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if(caozuo == 0){//添加挑战次数
            String sendmes = Agreement.getAgreement().oneArenaAgreement("3");
            SendMessageUntil.toServer(sendmes);
        }else if (caozuo == 1) {//打开战报
            String sendmes = Agreement.getAgreement().oneArenaAgreement("2");
            SendMessageUntil.toServer(sendmes);
//            FormsManagement.showForm(80);
        }else if(caozuo == 2){//打开兑换奖励
            PartnerArenaExchangeJframe.getPartnerArenaExchangeJframe().getPartnerArenaExchangePanel().showView();
            FormsManagement.showForm(107);
        }else if(caozuo == 3){//挑战
            OneArenaRole oneArenaRole = partnerArenaModelPanel.getOneArenaRole();
            if(oneArenaRole == null){
                return;
            }
            String sendmes = Agreement.getAgreement().oneArenaAgreement("P"+oneArenaRole.getRoleId());
            SendMessageUntil.toServer(sendmes);
        }else if(caozuo == 4){//领取称谓
            Achieve achieve = partnerArenaExchangeModelPanel.getAchieve();
            if(achieve!=null){
                String sendmes = Agreement.getAgreement().TaskNAgreement("R4="+achieve.getId());
                SendMessageUntil.toServer(sendmes);
            }
        }else if(caozuo == 5){
            String sendmes = Agreement.getAgreement().oneArenaAgreement("4");
            SendMessageUntil.toServer(sendmes);
        }else if(caozuo == 6){
            NewRefiningJpanel refiningJpanel = NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel();
            if (!NewRefiningJframe.getNewRefiningJframe().isVisible()) {
                refiningJpanel.show(null, 4, false);
            }
        }
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        super.mouseEntered(e);
        if (caozuo == 3) {
            partnerArenaModelPanel.setOpaque(true);
        }
    }
    @Override
    public void mouseExited(MouseEvent e) {
        super.mouseExited(e);
        if (caozuo == 3) {
            partnerArenaModelPanel.setOpaque(false);
            
        }
    }
}
