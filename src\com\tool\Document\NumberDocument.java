package com.tool.Document;

import java.awt.event.KeyEvent;
import java.math.BigDecimal;

import javax.swing.JTextField;
import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.PlainDocument;

import org.come.until.Util;

import com.tool.role.RoleData;

/**只能单个输入数字 且区分显示*/
public class NumberDocument extends PlainDocument {
	private static final long serialVersionUID = 1L;
//	private TraslationWantSentjishoujinqianJpanel jpanel;
//	private TransJpanel jpanel2;
//	private JPanel jPanel;
	private InputNum inputNum;
    private JTextField field;
    //倒着数 第一位 0需要填充,  第二位 0需要变色 第三位 0需要小于人物金钱
	private int p;
    public NumberDocument(JTextField field) {
		super();
		this.field = field;
		this.p=4;
	}
	public NumberDocument(JTextField field, int p) {
		super();
		this.field = field;
		this.p = p;
	}
	public NumberDocument(JTextField field, int p,InputNum inputNum) {
		super();
		this.field = field;
		this.p = p;
		this.inputNum = inputNum;
	}
	@Override
	public void insertString(int offset, String str, AttributeSet attrSet) throws BadLocationException {
		if (inputNum!=null&&inputNum.isChange()) {return;}
		if (str == null||str.length()!=1) {
            return;
        }
        int charstr=str.charAt(0); 
        if (charstr<KeyEvent.VK_0||charstr>KeyEvent.VK_9) {
			return;
		}
        super.insertString(offset,str,attrSet);
        String value=null;
        BigDecimal bigDecimal=null;
        if ((p & 0x01)==0) {
        	value=getText(0, getLength()).replaceAll(",", "");
        	super.remove(0, getLength());
            super.insertString(0,value,attrSet);
            int p=value.length();
            while (p>3) {
    			p-=3;
    		    super.insertString(p,",",attrSet);
    		}	
		}
        if ((p>>>2 & 0x01)==0) {
        	if (value==null) {
            	value=getText(0, getLength());
            	if ((p & 0x01)==0) {value.replaceAll(",", "");}
			}
            bigDecimal=new BigDecimal(value);
            if (bigDecimal.compareTo(RoleData.getRoleData().getLoginResult().getGold())>0) {
            	bigDecimal=RoleData.getRoleData().getLoginResult().getGold();
            	value=bigDecimal.toString();
            	super.remove(0, getLength());
                super.insertString(0,value,attrSet);
                if ((p & 0x01)==0) {
                	int p=value.length();
                    while (p>3) {
            			p-=3;
            		    super.insertString(p,",",attrSet);
            		}
				}  
			}
		}
        if ((p>>>1 & 0x01)==0) {
        	if (value==null) {
            	value=getText(0, getLength());
            	if ((p & 0x01)==0) {
            		value.replaceAll(",", "");
				}
			}
            bigDecimal=new BigDecimal(value);
        	Util.changeTextColor(field,bigDecimal);	
		}
        if (inputNum!=null) {inputNum.upNum();}
    }
	@Override
	public void remove(int offs, int len) throws BadLocationException {
		if (inputNum!=null&&inputNum.isChange()) {return;}
		super.remove(offs,len);
		if (getLength()!=0) {
			String value=getText(0, getLength()).replaceAll(",", "");
			if (!value.equals("")) {
				BigDecimal bigDecimal=new BigDecimal(value);
				if ((p>>>1 & 0x01)==0) {
					Util.changeTextColor(field,bigDecimal);	
				}
				if ((p & 0x01)==0) {
					super.remove(0, getLength());
			        super.insertString(0,value,null);
					int p=value.length();
			        while (p>3) {
						p-=3;
					    super.insertString(p,",",null);
					}
				}  
			}
		}
		if (inputNum!=null) {inputNum.upNum();}
    }
	/**替换*/
	public void replace(String value){
        try {
        	 BigDecimal bigDecimal=new BigDecimal(value);
        	 if ((p>>>1 & 0x01)==0) {
				 Util.changeTextColor(field,bigDecimal);	
			 }	
             super.remove(0, getLength());
             super.insertString(0,value,null);
             if ((p & 0x01)==0) {
                int p=value.length();
                while (p>3) {
     			   p-=3;
     		       super.insertString(p,",",null);
     		    }
             }
		} catch (BadLocationException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	/***/
	public BigDecimal getNum(){
		try {
			String value = getText(0, getLength()).replaceAll(",", "");
			if (!value.equals("")) {
				return new BigDecimal(value);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return new BigDecimal(0);
	}
}
