package jxy2.wormap;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class NpcQueryFrame extends JInternalFrame implements MouseListener {
    private NpcQueryJPanel npcQueryJPanel;
    private int first_x,first_y;//x、y坐标菜单

    public static NpcQueryFrame getNpcQueryFrame() {
        return (NpcQueryFrame) FormsManagement.getInternalForm(131).getFrame();
    }

    public NpcQueryFrame() {
        npcQueryJPanel = new NpcQueryJPanel();
        this.getContentPane().add(npcQueryJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        ((BasicInternalFrameUI)this.getUI()).setNorthPane(null);//去除顶部的边框
        this.setBounds(0, 158, 246, 466);//设置窗口出现的位置
        this.setBackground(UIUtils.Color_BACK);
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {//判断窗口移动的位置
            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(),getWidth(),getHeight());
                }
            }
        });
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(131);
        }else {
            FormsManagement.Switchinglevel(131);
        }
        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public NpcQueryJPanel getNpcQueryJPanel() {
        return npcQueryJPanel;
    }

    public void setNpcQueryJPanel(NpcQueryJPanel npcQueryJPanel) {
        this.npcQueryJPanel = npcQueryJPanel;
    }
}
