package come.tool.Fighting;

import com.tool.tcp.*;
import org.come.until.Article;

import java.awt.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 战斗中人物数据
 * <AUTHOR>
 */
public class FightingManData {
	private static final Map<String, Sprite> stateSpriteCache = new HashMap<>();
	static String[] fbs;
	static List<String> drawDown;//绘制在脚底下
	static{//法宝类型
		fbs=new String[]{"fbYsjl","fbJjl","fbDsc","fbQbllt","fbHlz","fbYmgs",
		"fbDsy","fbJqb","fbQw","fbBld","fbJge","fbFty","fbJljs","fbBgz","fbHd",
		"bbss","6018","6019","6022","6024","6025","6027","6028","7002","7008",
		"7015","7026","7033","7034","7035","9389","沧波","扶摇","甘霖","甘霖附加","1231","1232","1866","1869","1873"};
		drawDown=new ArrayList<>();//绘制在脚底的素材编号
		drawDown.add("金");drawDown.add("木");drawDown.add("土");drawDown.add("水");drawDown.add("火");
		drawDown.add("金A");drawDown.add("木A");drawDown.add("土A");drawDown.add("水A");drawDown.add("火A");
		drawDown.add("1869");drawDown.add("1876");
	}
	
	private int id;
    //阵营                  0  1
	private int Camp;
    //位置
	private int man;	
	//类型  0属于玩家  1属于召唤兽  2属于野怪3属于灵宝 4属于宝宝 
	private int type;
	//技能和召唤兽和灵宝状态
	private String State_1;
	//名称
	private String manname;
	//总血量
	private int hp_Total;
	//当前血量 
	private int hp_Current;
	//总蓝量
	private int mp_Total;
	//当前蓝量
	private int mp_Current;
	//怨气值
	private int yqz;
	//怒气值
	private int nqz;
	//是否隐身状态 不为1.0f为隐身
	private float alpha = 1.0f;
	//转生字段
	private int zs;
	//喊话
	private String msg;
	//人物造型
	private String model="img/角色/鬼族/祭剑魂";
	private Integer W;//勋章ID
	
	private List<String> States=new ArrayList<>();//常规状态
	
	private List<String> aaa;//内丹光环
	/**内丹归类*/
	public void initState(){
		if (States==null) {return;}
		for (int i = States.size()-1; i>=0; i--) {
			String type=States.get(i);
			if (type.startsWith("tj")||type.startsWith("mj")||type.startsWith("xl")||type.startsWith("rj")){
				if (aaa==null) {aaa=new ArrayList<>();}
				aaa.add(type);
				States.remove(i);
			}
		}
	}
	/**判断是否属于状态列表中*/
	public boolean isstate(String type){		
		if (type.equals("金")||type.equals("木")||type.equals("水")||type.equals("土")||type.equals("火")||type.equals("1876")) {
			deletestate("清除五行");
			return true;
		}else if (type.equals("金A")||type.equals("木A")||type.equals("水A")||type.equals("土A")||type.equals("火A")||type.equals("1876A")) {
			deletestate("清除五行");
			return true;
		}else if (type.equals("遗忘")||type.equals("封印")||type.equals("昏睡")||type.equals("混乱")) {
			if (type.equals("封印")) {
				deletestate("中毒");
		    }
			deletestate("封印");
			deletestate("昏睡");
			deletestate("混乱");
			deletestate("遗忘");
			return true;
		}else if (type.equals("骨盾")||type.equals("减人仙")||type.equals("减魔鬼")||type.equals("庇护")||
				type.equals("中毒")||type.equals("力量")||type.equals("抗性")||
				type.equals("加速")||type.equals("归墟")||type.equals("毒针轻刺")||type.equals("回魂咒")||
				type.equals("化羽")||type.equals("阴阳逆转")||type.equals("smmh")||type.equals("减速")) {
			 return true;
		}else if (type.equals("风水")||type.equals("雷火")||type.equals("鬼力")||type.equals("玉净散")) {
			deletestate("风水");
			deletestate("雷火");
			deletestate("鬼力");
			deletestate("玉净散");
			return true;
		}else if (type.equals("6029")) {
			String[] values={"6029","遗忘","封印","中毒","昏睡","混乱","fbJge","fbQw"};
			RemoveAbnormal(values);
			return true;
		}else if (type.equals("7009")) {
			deletestate("昏睡");
			deletestate("混乱");
			deletestate("遗忘");
			deletestate("7009");
			return true;
		}else if (type.equals("1237")||type.equals("1237A")||type.equals("1237B")) {
			String[] values={"1237","1237A","1237B"};
			RemoveAbnormal(values);
			return true;
		}else {//判断是否为法宝状态
			for (int i = 0; i < fbs.length; i++) {
				if (fbs[i].equals(type)) {
					deletestate(type);
					return true;
				}
			}
		}
	   return false;
	}
	/**状态添加 */
	public void addstate(String typess){
		if (typess==null)return;

		String[] types=typess.split("\\|");

		for (int i = 0; i < types.length; i++) {
			String type=types[i];
			if(type.equals("隐身")){
				alpha=0.3f;
			}else if (!States.contains(type)&&isstate(type)) {
				States.add(type);	
			}
		}
	}
	/**状态删除*/
	public void deletestate(String typess){
		if (typess==null)return;
		String[] types=typess.split("\\|");
        for (int i = 0; i < types.length; i++) {
			String type=types[i];
			if (type.equals("清除状态")) {
				String[] values={"减人仙","减魔鬼","庇护","遗忘","封印","中毒","昏睡","混乱","金","木","水","火","土","1876","力量","抗性","加速","smmh","减速"};
				RemoveAbnormal(values);
		    }else if (type.equals("清除异常状态")) {
				String[] values={"遗忘","封印","中毒","昏睡","混乱","fbJge","fbQw","1232","1869"};
				RemoveAbnormal(values);
			}else if (type.equals("清除五行")) {
				String[] values={"金","木","水","火","土","1876"};
				RemoveAbnormal(values);
			}else if (type.equals("非控制减益")) {
				String[] values={"减速","减人仙","减魔鬼","中毒"};
				RemoveAbnormal(values);
			}else if (type.equals("隐身")) {
				alpha=1.0f;				
			}else {
				States.remove(type);
			}
		}
	}
	/**
	 * 清除指定状态
	 */
	public void RemoveAbnormal(String[] values){
		for (int j = 0; j < values.length; j++) {
			States.remove(values[j]);
	    }
	}
	/**绘制状态*/
	public void draw1(Graphics g, long time, int x, int y) {
		for (int i = 0; i < States.size(); i++) {
			String type = States.get(i);
			if (drawDown.contains(type))
				continue;
			Sprite State = SpriteFactory.Prepare(GetTcpPath.getBuffTcp(type));
			if (State != null) {
				State.updateToTime(time,0);
				State.draw(g, x, y);
			}
		}
	}
	/**绘制状态*/
	public void draw2(Graphics g,long time,int x,int y){
		for (int i = 0; i < States.size(); i++) {
			String type = States.get(i);
			if (drawDown.contains(type)) {
				Sprite stateSprite = stateSpriteCache.get(type);
				if (stateSprite == null) {
					stateSprite = PartOne.sprite(-1, type);
					if (stateSprite != null) {
						stateSpriteCache.put(type, stateSprite);
					}
				}
				if (stateSprite != null) {
					stateSprite.updateToTime(time, 0);
					stateSprite.draw(g, x, y);
				}
			}
		}
		if (aaa!=null&&aaa.size()!=0) {
			try {
				Sprite State=SpriteFactory.Prepare(GetTcpPath.getBuffTcp(aaa.get(FightingMixDeal.CurrentRound%aaa.size())));
				if (State!=null) {
					State.updateToTime(time,0);
					State.draw(g, x, y);
				}
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
		}
	}
	/**
	 * 血蓝变化
	 * @return
	 */
	public void chang(int hp,int mp,int yq,int nq){
		int myman=FightingMixDeal.myman();
		if (hp!=0) {
			hp_Current=Article.xiugai(hp_Total, hp_Current, hp);
			if (FightingMixDeal.camp==Camp&&(myman==man||myman+5==man)) {			
				Article.fightingarticlehp(this,hp_Current);
			}
		}
		if (mp!=0) {
			mp_Current=Article.xiugai(mp_Total, mp_Current, mp);
			if (FightingMixDeal.camp==Camp&&(myman==man||myman+5==man)) {			
				Article.fightingarticlemp(this,mp_Current);
			}
		}
		if (yq!=0) {
			this.yqz+=yq;
			if (FightingMixDeal.camp==Camp&&myman==man) {		
				FightingMixDeal.yqz=this.yqz+"";
			}
		}
		if (nq!=0) {
			this.nqz+=nq;		
            if (FightingMixDeal.camp==Camp&&myman==man) {		
            	FightingMixDeal.nqz=this.nqz+"";
			}
		}
	
	}
	/**
	 * 状态查找
	 * @return
	 */
	public boolean ztcz(String type){
		for (int i = 0; i < States.size(); i++) {
			if (States.get(i).equals(type))return true;
		}
		return false;
	}
	/**
	 * 查找制定的信息
	 * @return
	 */
	public List<TypeState> cxxx(String type){
		List<TypeState> xx=new ArrayList<>();
		String[] v=State_1.split("\\|");
		for (int i = 0; i < v.length; i++) {
			String[] v2=v[i].split("=");
			if (!v2[0].equals(type))continue;
			String[] v3=v2[1].split("\\_");
			for (int j = 0; j < v3.length; j++) {
				String[] v4=v3[j].split("\\-");
				TypeState state=new TypeState();
				state.setType(v4[0]);
				state.setState(Integer.parseInt(v4[1]));
				xx.add(state);
			}
		}
		return xx;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

	public String getState_1() {
		return State_1;
	}
	public void setState_1(String state_1) {
		State_1 = state_1;
	}
	public String getManname() {
		return manname;
	}
	public void setManname(String manname) {
		this.manname = manname;
	}
	public int getCamp() {
		return Camp;
	}
	public void setCamp(int camp) {
		Camp = camp;
	}
	public int getMan() {
		return man;
	}
	public void setMan(int man) {
		this.man = man;
	}
	
	public int getHp_Total() {
		return hp_Total;
	}
	public void setHp_Total(int hp_Total) {
		this.hp_Total = hp_Total;
	}
	public int getHp_Current() {
		return hp_Current;
	}
	public void setHp_Current(int hp_Current) {
		this.hp_Current = hp_Current;
	}
	public int getMp_Total() {
		return mp_Total;
	}
	public void setMp_Total(int mp_Total) {
		this.mp_Total = mp_Total;
	}
	public int getMp_Current() {
		return mp_Current;
	}
	public void setMp_Current(int mp_Current) {
		this.mp_Current = mp_Current;
	}
	public List<String> getStates() {
		return States;
	}
	public void setStates(List<String> states) {
		States = states;
	}
	public float getAlpha() {
		return alpha;
	}
	public void setAlpha(float alpha) {
		this.alpha = alpha;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getZs() {
		return zs;
	}
	public void setZs(int zs) {
		this.zs = zs;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public int getYqz() {
		return yqz;
	}
	public void setYqz(int yqz) {
		this.yqz = yqz;
	}
	public int getNqz() {
		return nqz;
	}
	public void setNqz(int nqz) {
		this.nqz = nqz;
	}
	public Integer getW() {
		return W;
	}
	public void setW(Integer w) {
		W = w;
	}
	public String getModel() {
		return model;
	}
	public void setModel(String model) {
		this.model = model;
	}
	//路径$额外条件_等级_动作_颜色&
	public NewPart getPart(){//路径_等级_动作_颜色
		String[] v=model.split("\\&");
		NewPart parts=null;
		for (int i = 0; i < v.length; i++) {
			if (v[i].startsWith("W")) {
			    W=Integer.parseInt(v[i].substring(1));
				continue;
			}
			String[] vs=v[i].split("\\_");
			String skin=vs[0];
			int lvl=0,act=-2;
			String color=null;
			if (vs.length==1) {
				lvl=1;
				act=7;
			}else {
				if (vs.length>1) {lvl=Integer.parseInt(vs[1]);}
				if (vs.length>2) {act=Integer.parseInt(vs[2]);}
				if (vs.length>3) {color=vs[3];}
			}
			NewPart part=SpriteFactory.createPart(skin, act, lvl, color);
			if (parts==null) {parts=part;}
			else {parts=parts.addPart(part);}
		}
		return parts;	
	}
}
