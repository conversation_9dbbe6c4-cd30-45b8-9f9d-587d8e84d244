package com.tool.role;

import org.come.Frame.LingbaoJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.LingbaoEquipmentJpanel;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserData;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/** 玩家的灵宝和法宝 */
public class RoleLingFa {
    public static ImageIcon LF_G = new ImageIcon("img/lingbao/msg/lf_g.png");
    private static RoleLingFa roleLingFa;

    public static RoleLingFa getRoleLingFa() {
        if (roleLingFa == null) {
            roleLingFa = new RoleLingFa();
        }
        return roleLingFa;
    }

    /**
     * 灵宝图片路径
     */
    public static ImageIcon lingbaoimg(String lingbao, int w, int h) {
        return CutButtonImage.getImage("img/lingbao/" + lingbao + ".png", w, h);
    }

    // 选中的
    public Lingbao choseBao = null;
    // 装备的灵宝位置 和2 个装备的法宝 0灵宝 1 2法宝
    public Lingbao[] equipBao = new Lingbao[3];
    // 灵宝页数
    private int lingNum = 0;// 页数 0表示第一页
    // 法宝页数
    private int faNum = 0;// 页数 0表示第一页
    // 灵宝
    private Lingbao[] lingBaos = new Lingbao[48];
    // 法宝
    private Lingbao[] faBaos = new Lingbao[16];
    // 存放当前页面的img
    private ImageIcon[] LFimg = new ImageIcon[16];

    public void drawTrans(Graphics g, int x, int y, int ys, List<Lingbao> lingbaos) {
        ys = -ys * 9 - 1;
        s: for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null || lingbao.getEquipment() != 0) {
                continue;
            }
            if (lingbaos != null) {
                for (int j = lingbaos.size() - 1; j >= 0; j--) {
                    if (lingbao.getBaoid().compareTo(lingbaos.get(j).getBaoid()) == 0) {
                        continue s;
                    }
                }
            }
            ys++;
            if (ys >= 0) {
                g.drawImage(lingbaoimg(lingbao.getSkin(), -1, -1).getImage(), x + ys % 3 * 51, y + ys / 3 * 51 + 2, 45,
                        45, null);
                if (ys >= 8) {
                    return;
                }
            }
        }
    }

    public Lingbao getTrans(int ys, List<Lingbao> lingbaos, int p) {
        ys = -ys * 9 - 1;
        s: for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null || lingbao.getEquipment() != 0) {
                continue;
            }
            if (lingbaos != null) {
                for (int j = lingbaos.size() - 1; j >= 0; j--) {
                    if (lingbao.getBaoid().compareTo(lingbaos.get(j).getBaoid()) == 0) {
                        continue s;
                    }
                }
            }
            ys++;
            if (ys == p) {
                return lingbao;
            }
        }
        return null;
    }

    /***/
    public void drawCBG(Graphics g, int x, int y, int ys) {
        ys = -ys * 30 - 1;
        for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null || lingbao.getEquipment() != 0) {
                continue;
            }
            ys++;
            if (ys >= 0) {
                g.drawImage(lingbaoimg(lingbao.getSkin(), -1, -1).getImage(), x + ys % 6 * 51, y + ys / 6 * 51 + 2, 45,
                        45, null);
                if (ys >= 29) {
                    return;
                }
            }
        }
    }

    public Lingbao getCBG(int ys, int p) {
        p += ys * 30;
        ys = -ys * 30 - 1;
        for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null || lingbao.getEquipment() != 0) {
                continue;
            }
            ys++;
            if (ys == p) {
                return lingbao;
            }
        }
        return null;
    }

    public Lingbao czGBG(BigDecimal rgid) {
        for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null || lingbao.getEquipment() != 0) {
                continue;
            }
            if (lingbao.getBaoid().compareTo(rgid) == 0) {
                return lingbao;
            }
        }
        return null;
    }
    /**换装返回灵宝rgid*/
    public Lingbao DressUp(BigDecimal rgid) {
        for (int i = 0; i < lingBaos.length; i++) {
            Lingbao lingbao = lingBaos[i];
            if (lingbao == null) {continue;}
            if (lingbao.getBaoid().compareTo(rgid) == 0) {
                return lingbao;
            }
        }

        for (int i = 0; i < faBaos.length; i++) {
            Lingbao lingbao = faBaos[i];
            if (lingbao == null) {continue;}
            if (lingbao.getBaoid().compareTo(rgid) == 0) {
                return lingbao;
            }
        }

        return null;
    }

    /** 灵宝或者法宝装备 true 表示装备 false 脱下 */
    public void choseuse(Lingbao lingbao, boolean type) {
        LingbaoEquipmentJpanel equipmentJpanel = LingbaoJframe.getLingbaoJframe().getLingbaoJpanel()
                .getLingbaoCardJpanel().getEquipmentJpanel();
        if (type) {// 装备
            if (lingbao.getEquipment() != 0) {
                return;
            }
            int path = -1;
            if (lingbao.getBaotype().equals("法宝")) {
                if (equipBao[1] == null) {
                    path = 1;
                } else if (equipBao[2] == null) {
                    path = 2;
                } else {
                    choseuse(equipBao[2], false);
                    path = 2;
                }
            } else {
                if (equipBao[0] != null) {
                    choseuse(equipBao[0], false);
                }
                path = 0;
            }
            if (path != -1) {
                equipBao[path] = lingbao;
                lingbao.setEquipment(1);
                UserData.upling(lingbao);
                ImageIcon imageIcon = lingbaoimg(lingbao.getSkin(), 40, 40);
                if (path == 0) {
                    equipmentJpanel.getLabLingbao().setIcon(imageIcon);
                } else if (path == 1) {
                    equipmentJpanel.getLabMagicOne().setIcon(imageIcon);
                } else {
                    equipmentJpanel.getLabMagicTwo().setIcon(imageIcon);
                }
            }
        } else {// 脱下
            for (int i = 0; i < equipBao.length; i++) {
                if (equipBao[i] == lingbao) {
                    equipBao[i] = null;
                    lingbao.setEquipment(0);
                    UserData.upling(lingbao);
                    if (i == 0) {
                        equipmentJpanel.getLabLingbao().setIcon(null);
                    } else if (i == 1) {
                        equipmentJpanel.getLabMagicOne().setIcon(null);
                    } else {
                        equipmentJpanel.getLabMagicTwo().setIcon(null);
                    }
                    break;
                }
            }
        }
        RoleProperty.ResetEw();
    }

    /**
     * 符石替换 true穿上
     */
    public void fushichange(Lingbao lingbao, Goodstable goodstable, boolean type) {
        if (type) {
            // 穿上
            goodstable.setStatus(1);
            GoodsListFromServerUntil.fushis.put(goodstable.getRgid(), goodstable);
            lingbao.fashijihe(goodstable.getRgid() + "");
            LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel()
                    .shuxingzhanshi(lingbao);
            UserData.upling(lingbao);
            GoodsMouslisten.gooduse(goodstable, 0);
        } else {
            // 脱下先判断是否有空闲格子
            if (GoodsListFromServerUntil.newgood(goodstable)) {
                lingbao.fashijihe(goodstable.getRgid().toString());
                LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel()
                        .shuxingzhanshi(lingbao);
                UserData.upling(lingbao);
                GoodsMouslisten.gooduse(goodstable, 0);
            } else {
                // 格子满了
                ZhuFrame.getZhuJpanel().addPrompt("背包已经满了");
            }
        }
    }

    /** 判断是否有同类型的符石存在 true表示没有 */
    public boolean fushity(String[] v, String fstype) {
        if (v == null) {
            return true;
        }
        for (int i = 0; i < v.length; i++) {
            Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(v[i]));
            if (fstype.equals(good.getGoodsname())) {
                return false;
            }
        }
        return true;
    }

    /** 绘制 */
    public void drawL(Graphics g, int x, int y) {
        for (int i = 0; i < 8; i++) {
            int row = i % 4 * 51;
            int col = i / 4 * 51;
            Lingbao lingbao = getlingbao(i);
            if (lingbao != null) {
                if (LFimg[i] != null) {
                    g.drawImage(LFimg[i].getImage(), x + row, y + col, 48, 48, null);
                }
                if (lingbao.getEquipment() == 1) {
                    g.drawImage(LF_G.getImage(), x + row, y + col, null);
                }
            }
        }
    }

    public void drawF(Graphics g, int x, int y) {
        for (int i = 0; i < 8; i++) {
            int row = i % 4 * 51;
            int col = i / 4 * 51;
            Lingbao lingbao = getfabao(i);
            if (lingbao != null) {
                if (LFimg[i + 8] != null) {
                    g.drawImage(LFimg[i + 8].getImage(), x + row, y + col, 48, 48, null);
                }
                if (lingbao.getEquipment() == 1) {
                    g.drawImage(LF_G.getImage(), x + row, y + col, null);
                }
            }
        }
    }

    /** 获取灵宝 */
    public Lingbao getlingbao(int path) {
        return lingBaos[path + lingNum * 8];
    }

    /** 获取法宝 */
    public Lingbao getfabao(int path) {
        return faBaos[path + faNum * 8];
    }

    /**
     * 刷新灵宝界面
     */
    public void lingImg(int Position, ImageIcon imageIcon) {
        LFimg[Position] = imageIcon;
    }

    /**
     * 刷新法宝界面
     */
    public void FaImg(int Position, ImageIcon imageIcon) {
        LFimg[Position + 8] = imageIcon;
    }

    /** 根据页数 切换灵宝界面 */
    public void lingNumChange(int number) {
        lingNum = number;
        for (int i = lingNum * 8; i < (lingNum + 1) * 8; i++) {
            if (lingBaos[i] != null) {
                lingImg(i - lingNum * 8, lingbaoimg(lingBaos[i].getSkin(), -1, -1));
            } else {
                lingImg(i - lingNum * 8, null);
            }
        }
    }

    /** 根据页数 切换法宝界面 */
    public void faNumChange(int number) {
        faNum = number;
        for (int i = faNum * 8; i < (faNum + 1) * 8; i++) {
            if (faBaos[i] != null) {
                FaImg(i - faNum * 8, lingbaoimg(faBaos[i].getSkin(), -1, -1));
            } else {
                FaImg(i - faNum * 8, null);
            }
        }
    }

    /**
     * 灵宝翻页 flase上
     */
    public void lingFan(boolean type) {
        if (type) {
            if ((lingNum + 1) * 8 < lingBaos.length && lingBaos[(lingNum + 1) * 8] != null) {
                lingNumChange(lingNum + 1);
            }
        } else {
            if (lingNum > 0) {
                lingNumChange(lingNum - 1);
            }
        }
    }

    /**
     * 法宝翻页 flase上
     */
    public void faFan(boolean type) {
        if (type) {
            if ((faNum + 1) * 8 < faBaos.length && faBaos[(faNum + 1) * 8] != null) {
                faNumChange(faNum + 1);
            }
        } else {
            if (faNum > 0) {
                faNumChange(faNum - 1);
            }
        }
    }

    /**
     * 添加灵宝法宝
     */
    public void addlingfa(Lingbao lingbao) {
        if (choseBao != null) {
            if (lingbao.getBaoid().compareTo(choseBao.getBaoid()) == 0) {
                choseBao = lingbao;
                LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel()
                        .shuxingzhanshi(lingbao);
            }
        }
        for (int i = 0; i < equipBao.length; i++) {
            Lingbao equip = equipBao[i];
            if (equip != null) {
                if (lingbao.getBaoid().compareTo(equip.getBaoid()) == 0) {
                    equipBao[i] = lingbao;
                    break;
                }
            }
        }
        if (!lingbao.getBaotype().equals("法宝")) {
            int p = -1;
            for (int i = 0; i < lingBaos.length; i++) {
                if (lingBaos[i] == null) {
                    if (p == -1) {
                        p = i;
                    }
                } else if (lingBaos[i].getBaoid().compareTo(lingbao.getBaoid()) == 0) {
                    lingBaos[i] = lingbao;
                    return;
                }
            }
            if (p == -1) {
                return;
            }
            lingBaos[p] = lingbao;
            lingNumChange(lingNum);
        } else {
            int p = -1;
            for (int i = 0; i < faBaos.length; i++) {
                if (faBaos[i] == null) {
                    if (p == -1) {
                        p = i;
                    }
                } else if (faBaos[i].getBaoid().compareTo(lingbao.getBaoid()) == 0) {
                    faBaos[i] = lingbao;
                    return;
                }
            }
            if (p == -1) {
                return;
            }
            if (isFB(lingbao.getBaoname())) {
                lingbao.setOperation("删除");
                UserData.upling(lingbao);
                return;
            }
            faBaos[p] = lingbao;
            faNumChange(lingNum);
        }
    }

    /** 灵宝法宝初始化 */
    public void lingChange(List<Lingbao> list) {
        if (list == null) {
            return;
        }
        for (int i = 0; i < lingBaos.length; i++) {
            lingBaos[i] = null;
        }
        for (int i = 0; i < faBaos.length; i++) {
            faBaos[i] = null;
        }
        for (int i = 0; i < equipBao.length; i++) {
            equipBao[i] = null;
        }
        LingbaoEquipmentJpanel equipmentJpanel = LingbaoJframe.getLingbaoJframe().getLingbaoJpanel()
                .getLingbaoCardJpanel().getEquipmentJpanel();
        for (int i = 0; i < list.size(); i++) {
            Lingbao lingbao = list.get(i);
            if (lingbao.getEquipment() == 1) {
                if (lingbao.getBaotype().equals("法宝")) {
                    if (equipBao[1] == null) {
                        equipBao[1] = lingbao;
                        ImageIcon imageIcon = lingbaoimg(lingbao.getSkin(), 40, 40);
                        equipmentJpanel.getLabMagicOne().setIcon(imageIcon);
                    } else {
                        equipBao[2] = lingbao;
                        ImageIcon imageIcon = lingbaoimg(lingbao.getSkin(), 40, 40);
                        equipmentJpanel.getLabMagicTwo().setIcon(imageIcon);
                    }
                } else {
                    choseBao = lingbao;
                    equipBao[0] = lingbao;
                    ImageIcon imageIcon = lingbaoimg(lingbao.getSkin(), 40, 40);
                    equipmentJpanel.getLabLingbao().setIcon(imageIcon);
                    LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel()
                            .shuxingzhanshi(lingbao);
                }
            }
            if (!lingbao.getBaotype().equals("法宝")) {
                // if
                // (lingbao.getFushis()!=null&&!lingbao.getFushis().equals(""))
                // {
                // lingbao.setOperation("");
                // lingbao.setFushis("");
                // UserData.upling(lingbao);
                // }
                // if (lingbao.getEquipment()==1) {
                // System.out.println("修改灵包");
                // System.out.println(lingbao.getFushis());
                // lingbao.setFushis(null);
                // lingbao.setOperation(null);
                // UserData.upling(lingbao);
                // }
                for (int l = 0; l < lingBaos.length; l++) {
                    if (lingBaos[l] == null) {
                        lingBaos[l] = lingbao;
                        break;
                    }
                }
            } else {
                if (!isFB(lingbao.getBaoname())) {
                    for (int j = 0; j < faBaos.length; j++) {
                        if (faBaos[j] == null) {
                            faBaos[j] = lingbao;
                            break;
                        }
                    }
                } else {
                    lingbao.setOperation("删除");
                    UserData.upling(lingbao);
                }
            }
        }
        lingNumChange(lingNum);
        faNumChange(faNum);
    }

    /**
     * 删除灵宝
     */
    public void deleteling(Lingbao lingbao) {
        for (int i = 0; i < lingBaos.length; i++) {
            if (lingBaos[i] != null) {
                if (lingbao.getBaoid().compareTo(lingBaos[i].getBaoid()) == 0) {
                    lingBaos[i] = null;
                    break;
                }
            }
        }
        lingChange(lingchangelist2());
    }

    public void deleteling(List<Lingbao> lingbaos) {
        if (lingbaos == null) {
            return;
        }
        for (int i = 0; i < lingbaos.size(); i++) {
            Lingbao lingbao = lingbaos.get(i);
            for (int j = 0; j < lingBaos.length; j++) {
                if (lingBaos[j] != null) {
                    if (lingbao.getBaoid().compareTo(lingBaos[j].getBaoid()) == 0) {
                        lingBaos[j] = null;
                        break;
                    }
                }
            }
        }
        lingChange(lingchangelist2());
    }

    /**
     * 将灵宝数组传为list
     */
    public List<Lingbao> lingchangelist() {
        List<Lingbao> lingbaolist = new ArrayList<>();
        for (int i = 0; i < lingBaos.length; i++) {
            if (lingBaos[i] != null) {
                lingbaolist.add(lingBaos[i]);
            }
        }
        return lingbaolist;
    }

    /**
     * 将灵宝数组传为list
     */
    public List<Lingbao> lingchangelist2() {
        List<Lingbao> lingbaolist = new ArrayList<>();
        for (int i = 0; i < lingBaos.length; i++) {
            if (lingBaos[i] != null) {
                lingbaolist.add(lingBaos[i]);
            }
        }
        for (int i = 0; i < faBaos.length; i++) {
            if (faBaos[i] != null) {
                lingbaolist.add(faBaos[i]);
            }
        }
        return lingbaolist;
    }

    // 0.55 0.31 0.14
    // 获取总法宝品质 最大值 15*250=3750 单法宝250*7 单人300*3 3750 1750 900
    public int getFaPJ() {
        int pf = 0;
        for (int i = 0; i < faBaos.length; i++) {
            if (faBaos[i] != null) {
                pf += getFv(faBaos[i]);
            }
        }
        return pf;
    }

    // 获取单法宝评分
    public static int getFv(Lingbao lingbao) {
        int pf = 0;
        pf += lingbao.getLingbaolvl().intValue();
        pf += getQv(lingbao.getBaoquality());
        return pf;
    }

    // 根据品质获取系数
    public static int getQv(String quality) {
        switch (quality) {
        case "把玩":
            return 10;
        case "贴身":
            return 20;
        case "珍藏":
            return 30;
        case "无价":
            return 40;
        case "传世":
            return 50;
        case "神迹":
            return 60;
        }
        return 10;
    }

    // 判断是否已经有该法宝了 ture表示有了
    public boolean isFB(String bn) {
        for (int i = 0; i < faBaos.length; i++) {
            if (faBaos[i] != null) {
                if (faBaos[i].getBaoname().equals(bn)) {
                    return true;
                }
            }
        }
        return false;
    }

    static String[] fbs = new String[] { "银索金铃", "将军令", "大势锤", "七宝玲珑塔", "黑龙珠", "幽冥鬼手", "大手印", "绝情鞭", "情网", "宝莲灯",
            "金箍儿", "番天印", "锦襕袈裟", "白骨爪", "化蝶" };

    // 判断是否是法宝
    public static boolean isFB2(String bn) {
        for (int i = 0; i < fbs.length; i++) {
            if (fbs[i].equals(bn)) {
                return true;
            }
        }
        return false;
    }

    List<String> fabao = new ArrayList<>();

    // 添加购买法宝记录
    public boolean addfb(String bn) {
        if (fabao.contains(bn)) {
            return false;
        }
        if (isFB(bn)) {
            return false;
        }
        fabao.add(bn);
        return true;
    }

    public Lingbao getChoseBao() {
        return choseBao;
    }

    public void setChoseBao(Lingbao choseBao) {
        this.choseBao = choseBao;
    }

    public int getLingNum() {
        return lingNum;
    }

    public void setLingNum(int lingNum) {
        this.lingNum = lingNum;
    }

    public int getFaNum() {
        return faNum;
    }

    public void setFaNum(int faNum) {
        this.faNum = faNum;
    }

    public Lingbao[] getLingBaos() {
        return lingBaos;
    }

    public void setLingBaos(Lingbao[] lingBaos) {
        this.lingBaos = lingBaos;
    }

    public Lingbao[] getFaBaos() {
        return faBaos;
    }

    public void setFaBaos(Lingbao[] faBaos) {
        this.faBaos = faBaos;
    }

    public ImageIcon[] getLFimg() {
        return LFimg;
    }

    public void setLFimg(ImageIcon[] lFimg) {
        LFimg = lFimg;
    }

}
