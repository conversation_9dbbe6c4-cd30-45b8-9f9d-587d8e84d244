package com.tool.tcp;

import java.util.Arrays;

public class HHOne {
//	2 4 8 16
	private byte act;
	private long[] ls;
	public byte getAct() {
		return act;
	}
	public void setAct(byte act) {
		this.act = act;
	}
	public long[] getLs() {
		return ls;
	}
	public void setLs(long[] ls) {
		this.ls = ls;
	}

	@Override
	public String toString() {
		return "HHOne{" +
				"act=" + act +
				", ls=" + Arrays.toString(ls) +
				'}';
	}
}
