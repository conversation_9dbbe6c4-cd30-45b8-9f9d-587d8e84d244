package jxy2.zodiac;

import jxy2.jutnil.Juitil;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Music;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class ChineseZodiacDjMouse implements MouseListener {
    public int i;
    public ChineseZodiacPanel zodiacPanel;
    public String[][] name;
    public ChineseZodiacDjMouse(ChineseZodiacPanel zodiacPanel, int i,String[][] name) {
        this.zodiacPanel = zodiacPanel;
        this.i = i;
        this.name = name;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {

        if (!zodiacPanel.xpdlMap.isEmpty()) {
            boolean zero = zodiacPanel.containsValue(zodiacPanel.xpdlMap.get(zodiacPanel.getIu()), i + "");
            if (zero) {
                //进入修复耐久
                return;
            }
        }

        int x = ChineseZodiacFrame.getChineseZodiacFrame().getX();
        int y = ChineseZodiacFrame.getChineseZodiacFrame().getY();
        if (!FormsManagement.getframe(134).isVisible()) {
                // 设置存款
                FormsManagement.showForm(134);
                InlaidStarFrame.getInlaidStarFrame().getInnerPanel().init(name,i,zodiacPanel.getIu(),1);
                InlaidStarFrame.getInlaidStarFrame().setBounds(x+200, y+112, 250, 220);
                Juitil.addClosingButtonToPanel(InlaidStarFrame.getInlaidStarFrame().getInnerPanel(),134,250);
                Music.addyinxiao("开关窗口.mp3");
            } else {
                FormsManagement.HideForm(134);
                Music.addyinxiao("关闭窗口.mp3");
            }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
         if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
    }
}
