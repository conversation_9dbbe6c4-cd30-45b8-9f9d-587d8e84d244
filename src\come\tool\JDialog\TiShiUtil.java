package come.tool.JDialog;

import java.util.HashMap;
import java.util.Map;

public class TiShiUtil {
    // 灵宝抗性转换
    public static String lingkang = "lingkang";
    // 开启灵宝技能格子
    public static String lingskill = "lingskill";
    // 关闭灵宝符石格子
    public static String lingfushi = "lingfushi";
    // 灵宝突破
    public static String lingtupo = "lingtupo";
    // 删除灵宝技能
    public static String SkillDiscatd = "SkillDiscatd";
    // 灵宝丢弃
    public static String lingDiscatd = "lingDiscatd";
    // 物品丢弃
    public static String GoodDiscatd = "GoodDiscatd";
    // 踢出帮派
    public static String KickOut = "KickOut";
    // 退位让贤
    public static String Abdication = "Abdication";
    // 脱离帮派
    public static String BreakAway = "BreakAway";
    // 删除召唤兽技能
    public static String DeleteSkill = "DeleteSkill";
    // 拆卸宝石
    public static String GemOff = "GemOff";
    // 天演策洗点
    public static String washPoint = "washPoint";
    // 交易
    public static String trans = "trans";
    // 星卡
    public static String starCard = "starCard";
    // 星卡取出与存入
    public static String starCardDeposit = "starCardDeposit";
    // 召唤兽放生
    public static String Release = "Release";
    // 孩子抚养
    public static String childRearing = "ChildRearing";
    // 伙伴激活
    public static String PalKey = "PalKey";
    // 确认框
    public static String confirm = "confirm";
    // 召唤兽携带数量
    public static String petxdsum = "petxdsum";
    //开启灵犀锁
    public static String openLxLock = "openLxLock";
    //一键修炼灵犀
    public static String Oneclick = "Oneclick";
    //重炼星魂
    public static String reLingHun = "reLingHun";
    //重置法门
    public static String ResetLawGate = "ResetLawGate";
    //解锁守护槽
    public static String unlockGuardianSlot = "unlockGuardianSlot";
    //镶嵌坐骑
    public static String Inlaidmounts = "Inlaidmounts";
    //销毁飞行器
    public static String DestroyFly = "DestroyFly";
    //开启玄印格子
    public static String openXuanYin = "openXuanYin";

    public static Map<String, TiShiChuLi> tishiMap = new HashMap<>();
    public static String XiLingXi = "XiLingXi";
    public static String batchitme = "batchitme";
    public static String petqiling = "petqiling";

    static {
        tishiMap.put(lingtupo, new LingTupoJDialog());
        tishiMap.put(lingskill, new LingSkillJDialog());
        tishiMap.put(lingfushi, new LingFuShiJDialog());
        tishiMap.put(GoodDiscatd, new GoodDiscatdJDialog());
        tishiMap.put(KickOut, new KickOutJDialog());
        tishiMap.put(Abdication, new AbdicationJDialog());
        tishiMap.put(BreakAway, new BreakAwayJDialog());
        tishiMap.put(lingkang, new LingKangJDialog());
        tishiMap.put(lingDiscatd, new LingDiscatdJDialog());
        tishiMap.put(SkillDiscatd, new SkillDiscatdJDialog());
        tishiMap.put(DeleteSkill, new DeleteSkillJDialog());
        tishiMap.put(GemOff, new GemOffJDialog());
        tishiMap.put(washPoint, new washPointJDialog());
        tishiMap.put(trans, new TransJDialog());
        tishiMap.put(starCard, new StarCardJDialog());
        tishiMap.put(starCardDeposit, new StarCardDepositJDialog());
        tishiMap.put(Release, new ReleaseJDialog());
        tishiMap.put(childRearing, new ChildRearingJDialog());
        tishiMap.put(PalKey, new PalKeyJDialog());
        tishiMap.put(confirm, new ConfirmDialog());
        tishiMap.put(petxdsum, new PetxdsumDialog());
        tishiMap.put(openLxLock, new OpenLxLockDialog());
        tishiMap.put(Oneclick, new OneclickDialog());
        tishiMap.put(XiLingXi, new XiLxJDialog());
        tishiMap.put(batchitme, new BatchitmeJDialog());
        tishiMap.put(reLingHun, new ReLingHunJDialog());
        tishiMap.put(ResetLawGate, new ResetLawGateJDialog());
        tishiMap.put(unlockGuardianSlot, new UnlockGuardianSlotJDialog());
        tishiMap.put(Inlaidmounts, new InlaidmountsJDialog());
        tishiMap.put(petqiling, new PetQilingJDialog());
        tishiMap.put(DestroyFly, new DestroyFlyJDialog());
        tishiMap.put(openXuanYin, new OpenXuanYinJDialog());


    }

}
