package com.tool.role;

/**特技*/
public class Stunt {

	private int skillId;//技能id
	private int suitid;//套装id
	private double value;//套装生效时的数值
	private int lvl;//技能等级	
	private int sum;//技能数
	private int maxSum;//技能需求
	
	public Stunt(int skillId, int suitid, int lvl, int maxSum) {
		super();
		this.skillId = skillId;
		this.suitid = suitid;
		this.lvl = lvl;
		this.maxSum = maxSum;
	}
	/**特技是否生效*/
	public boolean isValid(){
		if (sum>=maxSum) {
			return true;
		}
		return false;	
	}
//	6001	加强封印     6002	加强昏睡
//	6003	加强遗忘    6004	加强混乱
//	6005	加强风法    6006	加强伤害
//	6007	加强法术    6008	加强火法
//	6009	加强鬼火    6010	忽视抗火
//	6011	忽视抗混    6012	忽视抗风
//	6013	忽视抗封    6014	忽视抗睡
//	6015	忽视抗遗忘6016	加强震慑
//	6030	加强加速    6031	水魔附身
//	6032	加强三尸虫6035	加强加防
//	6036	加强魅惑    6039	加强加攻
//	加强震慑：加强震慑法术（按照套装品质有所不同，把玩2%贴身2.5%珍藏3% 无价3.5%传世4%）
//	加强遗忘：加强遗忘（数值受套装品质影响，把玩4%贴身5%珍藏6%无价7%传世8%）
//	忽视抗封：忽视抗封（数值受套装品质影响，把玩1%贴身1.5%珍藏2%无价2.5%传世3%）
//	提抗上限：减少封印/混乱/昏睡/遗忘抗性，同时增加这四项抗性上限，把玩6，贴身7珍藏8无价9传世10
//	8011	正骨(混)	每50点根骨提供0.1%忽视抗混
//	8012	寒骨(封)	每50点根骨提供0.1%忽视抗封印
//	8013	黯骨(睡)	每50点根骨提供0.1%忽视抗睡
//	8014	魂骨(忘)	每50点根骨提供0.1%忽视抗遗忘
//	8016	炼骨(毒)	每10点根骨提供0.1%强毒伤害
//	8017	圣灵(混)	每30点灵性提供0.1%忽视抗混
//	8018	晶灵(封)	每30点灵性提供0.1%忽视抗封印
//	8019	通灵(睡)	每30点灵性提供0.1%忽视抗睡
//	8020	释灵(忘)	每30点灵性提供0.1%忽视抗遗忘
//	8021	韵灵(毒)	每10点灵性提供0.1%强毒伤害
//	8022	血骤(雷)	每10点根骨提供0.1%忽视抗雷
//	8023	血炽(火)	每10点根骨提供0.1%忽视抗火
//	8024	血行(风)	每10点根骨提供0.1%忽视抗风
//	8025	血盈(水)	每10点根骨提供0.1%忽视抗风
//	8026	血冥(鬼火)	每20点根骨提供0.1%忽视抗鬼火
//	8027	无属性	使用该装备，无属性需求
//	8028	无级别	使用该装备，无等级、无转生需求
//	8029	低级精准	使用该装备，命中率增加15%
//	8030	高级精准	使用该装备，命中率增加30%
//	8031	低级精气	师门法术5%几率恢复所消耗魔法
//	8032	高级精气	师门法术10%几率恢复所消耗魔法


	/**判断是否对属性加成*/
	public boolean isAffect(){
		if ((skillId>=6001&&skillId<=6017)||skillId==6030||skillId==6035||
				skillId==6036||skillId==6039||skillId==6031||skillId==6032||
				(skillId>=8001&&skillId<=8023)||(skillId>=8030&&skillId<=8036)||
				(skillId>=8038&&skillId<=8039)) {
			return true;
		}
		return false;	
	}
	public int getSuitid() {
		return suitid;
	}
	public void setSuitid(int suitid) {
		this.suitid = suitid;
	}
	public int getSkillId() {
		return skillId;
	}
	public void setSkillId(int skillId) {
		this.skillId = skillId;
	}
	public int getLvl() {
		return lvl;
	}
	public void setLvl(int lvl) {
		this.lvl = lvl;
	}
	public int getSum() {
		return sum;
	}
	public void setSum(int sum) {
		this.sum = sum;
	}
	public void addSum() {
		this.sum+=1;
	}
	public int getMaxSum() {
		return maxSum;
	}
	public void setMaxSum(int maxSum) {
		this.maxSum = maxSum;
	}
	public double getValue() {
		return value;
	}
	public void setValue(double value) {
		this.value = value;
	}
	
	
}
