package come.tool.JDialog;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import jxy2.supet.SipetFrame;
import jxy2.supet.SipetJPanel;
import org.come.Frame.AlchemyJframe;
import org.come.Frame.MountJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.Mount;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChangeMouseSymbolMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.ExpIncreaseUntil;
import org.come.until.FormsManagement;

/**召唤兽放生*/
public class ReleaseJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		if (tishi) {
			if (object instanceof RoleSummoning) {
				RoleSummoning pet = (RoleSummoning) object;
		        // 判断召唤兽是否有放生
		        if (pet.getPetlock() == 1) {
		            ZhuFrame.getZhuJpanel().addPrompt("该召唤兽已加锁，不能放生。。");
		            return;
		        }
		        if (pet.getGoods() != null) {
		            ZhuFrame.getZhuJpanel().addPrompt("该召唤兽携带着装备");
		            return;
		        }
		        if (ZhuJpanel.getPetMount(pet.getSid()) != null) {
		            ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽被管制中，不可放生！！！");
		            return;
		        }
		        if (RoleData.getRoleData().getLoginResult().getSummoning_id() != null) {
		            if (RoleData.getRoleData().getLoginResult().getSummoning_id().compareTo(pet.getSid()) == 0) {
		                ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽已在参战中！！！");
		                return;
		            }
		        }
		        ChangeMouseSymbolMouslisten.releasePet(pet);
				SipetJPanel.getInstance().RefreshPetViewport();
				AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().ServerObtainsData();
				SipetFrame.getSipetFrame().getSipetJPanel().RefreshPetViewport();
			}else if (object instanceof Mount) {
				Mount mount = (Mount) object;
		        // 发送放生坐骑的指示给后台
		        String sendmes = Agreement.getAgreement().mountreleaseAgreement(mount.getMid() + "");
		        SendMessageUntil.toServer(sendmes);
		        // 删除这个坐骑
		        int index=-1;
		        for (int i = 0; i < ZhuJpanel.getListMount().size(); i++) {
					if (mount==ZhuJpanel.getListMount().get(i)) {
					    index=i;
						ZhuJpanel.getListMount().remove(i);
						break;
					}
				}
//		        MountJframe.getMountjframe().getMountjpanel().getModelmount().remove(index);
		        // 判断是否还有坐骑
		        if (ZhuJpanel.getListMount().size() <= 0) {// 没有
		            // 等级
		            MountJframe.getMountjframe().getMountjpanel().getLabmountlevel().setText("");
		            // 体力
		            MountJframe.getMountjframe().getMountjpanel().getLabmounttili().setText("");
		            // 灵性
		            MountJframe.getMountjframe().getMountjpanel().getLabmountintelligence().setText("");
		            // 力量
		            MountJframe.getMountjframe().getMountjpanel().getLabmountpower().setText("");
		            // 根骨
		            MountJframe.getMountjframe().getMountjpanel().getLabmountRootbone().setText("");
		            // 经验
		            MountJframe.getMountjframe().getMountjpanel().getLabmountexp().setText("");
//		            MountJframe.getMountjframe().getMountjpanel().getListmount().setSelectedIndex(-1);
		        } else {
		            // 判断是否有骑乘
		            if (ImageMixDeal.userimg.getRoleShow().getMount_id() != 0) {
		                int exi = 0;
		                for (int i = 0; i < ZhuJpanel.getListMount().size(); i++) {
		                    if (ZhuJpanel.getListMount().get(i).getMountid() == ImageMixDeal.userimg.getRoleShow().getMount_id()) {
		                        exi = i;
		                    }
		                }
		                // 展示骑乘的坐骑信息
		                ExpIncreaseUntil.showMountValue(ZhuJpanel.getListMount().get(exi));
//		                MountJframe.getMountjframe().getMountjpanel().getListmount().setSelectedIndex(exi);
		            } else {
		                // 展示第一只坐骑的信息
		                ExpIncreaseUntil.showMountValue(ZhuJpanel.getListMount().get(0));
//		                MountJframe.getMountjframe().getMountjpanel().getListmount().setSelectedIndex(0);
		            }
		        }
		        // 关闭坐骑技能面板
		        FormsManagement.HideForm(20);
			}
		}
	}

}
