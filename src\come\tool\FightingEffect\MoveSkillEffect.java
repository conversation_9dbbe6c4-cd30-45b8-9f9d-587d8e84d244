package come.tool.FightingEffect;


import java.util.ArrayList;
import java.util.List;

import org.come.bean.PathPoint;

import com.tool.tcp.NewPart;
import com.tool.tcp.SpriteFactory;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.FightingMove2;
import come.tool.Fighting.FightingState;
import come.tool.Fighting.Fightingimage;
import come.tool.Fighting.ShadowMode;
import come.tool.Fighting.SkillSpell;
import come.tool.Fighting.StateProgress;

public class MoveSkillEffect implements Effect{

	@Override
	public StateProgress analysisAction(FightingState State, int path) {
		// TODO Auto-generated method stub
		Fightingimage fightingimage=FightingMixDeal.CurrentData.get(path);
		StateProgress progress=EffectType.getProgress(State, path);
		SkillSpell skillSpell=new SkillSpell();		 
		if (State.getSkillskin().equals("4000")||State.getSkillskin().equals("10")) {
			PathPoint pathPoint=MoveEffect.move(State.getEndState());				   	
			List<PathPoint> lists=new ArrayList<>();
			lists.add(new PathPoint(fightingimage.getX(), fightingimage.getY()));
			lists.add(pathPoint);	
			int dir=FightingMove2.getdir(pathPoint.getX()-fightingimage.getX(),pathPoint.getY()-fightingimage.getY(), 0);
			NewPart part=null;
			if (State.getSkillskin().equals("4000")) {
				part=SpriteFactory.createPart(State.getSkillskin(), -1, 0, null);
			}else {
				part=fightingimage.getPart().clonePart();
				part.setAct(Integer.parseInt(State.getSkillskin()));
			}
			ShadowMode shadowMode=new ShadowMode(part,fightingimage.getX(),fightingimage.getY(), lists,dir);
			skillSpell.setShadowMode(shadowMode);			
		}else {
			String[] vs=State.getEndState().split("\\|");
			int path2=FightingMixDeal.CurrentData(Integer.parseInt(vs[0]),Integer.parseInt(vs[1]));
			Fightingimage image=FightingMixDeal.CurrentData.get(path2);
			int dir=EffectType.getdir(3,image.getFightingManData().getCamp());	
			NewPart part=fightingimage.getPart().clonePart();
			part.setAct(Integer.parseInt(State.getSkillskin()));
			if (vs.length==2) {
				ShadowMode shadowMode=new ShadowMode(part, image.getX(), image.getY(), null, dir==3?1:0);
				skillSpell.setShadowMode(shadowMode);		
			}else {
				ShadowMode shadowMode=new ShadowMode(part, dir==3?image.getX()+70:image.getX()-70,  dir==3?image.getY()+30:image.getY()-30, null, dir==3?1:0);
				skillSpell.setShadowMode(shadowMode);		
			}
		}
		progress.setSpell(skillSpell);
		progress.setMan(path);
		progress.setType(0);
		return progress;
	}

}
