package com.tool.btn;

import org.come.Jpanel.AlchemyReturnJpanel;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class SpiritualPetSkillsBtn extends MoBanBtn implements MouseListener {
    public int index;
    public AlchemyReturnJpanel alchemyReturnJpanel;
    public SpiritualPetSkillsBtn(String iconpath, int type, int index, AlchemyReturnJpanel alchemyReturnJpanel) {
        super(iconpath, 0,type);
        this.index = index;
        this.alchemyReturnJpanel = alchemyReturnJpanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // TODO Auto-generated method stub
        if (getZhen()==1||alchemyReturnJpanel==null){return;}

        if (index==9){

        }else {
            int sum = Util.SwitchUI==1?30:0;
            int suy = Util.SwitchUI==1?-15:0;
            int x = sum+alchemyReturnJpanel.positions[index][0]; // 获取X坐标
            int y = suy+alchemyReturnJpanel.positions[index][1]; // 获取Y坐标
            int zhen =alchemyReturnJpanel.getSpiritualPetSkills()[index].getZhen()==0?0:2;
            Image image =  alchemyReturnJpanel.getSpiritualPetSkills()[index].getIcons()[zhen].getImage().getScaledInstance(22, 23, Image.SCALE_SMOOTH);
            alchemyReturnJpanel.getSpiritualPetSkills()[index].setIcon(new ImageIcon(image));
            alchemyReturnJpanel.getSpiritualPetSkills()[index].setBounds(x-12, y+20, 22, 23);
        }
    }
    @Override
    public void mouseReleased(MouseEvent e) {
        if (getZhen()==1||alchemyReturnJpanel==null)return;
        if (index==9){

        }else {
            // TODO Auto-generated method stub
            int sum = Util.SwitchUI==1?30:0;
            int suy = Util.SwitchUI==1?-15:0;
            int x = sum+alchemyReturnJpanel.positions[index][0]; // 获取X坐标
            int y = suy+alchemyReturnJpanel.positions[index][1]; // 获取Y坐标
            int zhen =alchemyReturnJpanel.getSpiritualPetSkills()[index].getZhen()==0?0:2;
            Image image = alchemyReturnJpanel.getSpiritualPetSkills()[index].getIcons()[zhen].getImage().getScaledInstance(30, 31, Image.SCALE_SMOOTH);
            alchemyReturnJpanel.getSpiritualPetSkills()[index].setIcon(new ImageIcon(image));
            alchemyReturnJpanel.getSpiritualPetSkills()[index].setBounds(x - 17, y + 15, 30, 31);
        }
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (btn!=1) {btnchange(0);}
    }
    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }
}
