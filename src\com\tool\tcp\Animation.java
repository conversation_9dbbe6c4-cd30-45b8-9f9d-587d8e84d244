package com.tool.tcp;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.util.Vector;

public class Animation {

	private static final long serialVersionUID = 1L;
	private Object UPDATE_LOCK = new Object();
	private Vector<Frame> frames;
	private int index;//当前动画序号
	private Frame currFrame;// 当前帧
	private long animTime;// 动画已播放时间(1周期内)
	private long totalDuration;// 总共持续时间
	private int frameCount;
	public Animation() {
		frames = new Vector<Frame>();
	}
	
	public Animation(Vector<Frame> frames) {
		super();
		this.frames = frames;
		this.currFrame = frames.get(0);
		this.frameCount=frames.size();
		this.totalDuration=SpriteFactory.ANIMATION_INTERVAL*frameCount;
	}

	public Animation(Animation anim) {
		totalDuration = anim.totalDuration;
		frames = anim.frames;
		currFrame = anim.currFrame;
		frameCount=anim.getFrames().size();
	}

	public void addFrame(Frame frame) {
		synchronized (UPDATE_LOCK) {
			totalDuration += SpriteFactory.ANIMATION_INTERVAL;
			frames.add(frame);
			frameCount++;
			currFrame = frame;
		}
	}

	public void addFrame(BufferedImage image, int centerX, int centerY) {
		synchronized (UPDATE_LOCK) {
			totalDuration += SpriteFactory.ANIMATION_INTERVAL;
			Frame frame = new Frame(image, centerX, centerY);
			frames.add(frame);
			frameCount++;
			currFrame = frame;	
		}
	}

	/**
	 * 根据消逝的时间更新目前应该显示哪一帧动画
	 * 
	 * @param elapsedTime
	 * @return 返回此次跳过的帧数
	 */
	public void update(long elapsedTime) {
		animTime += elapsedTime;
		this.updateToTime(animTime);
	}
	/**
	 * 从第0帧开始计算，更新到elapsedTime时间后的帧
	 */
	public void updateToTime(long playTime) {
		synchronized (UPDATE_LOCK) {
			if (frameCount > 1) {
				animTime = playTime;
				index=(int) (animTime/SpriteFactory.ANIMATION_INTERVAL);
				index%=frameCount;
				currFrame = frames.get(index);
			} else if (frameCount > 0) {
				currFrame = frames.get(0);
			}
			UPDATE_LOCK.notifyAll();
		}
	}

	public Image getImage() {
		synchronized (UPDATE_LOCK) {
			return (currFrame == null) ? null : currFrame.getImage();
		}
	}

	/**
	 * 从头开始播放这个动画
	 */
	public void reset() {
		synchronized (UPDATE_LOCK) {
			animTime = 0;
			index = 0;
			currFrame = frames.size() > 0 ? frames.get(0) : null;
		}
	}
	public int getWidth() {
		synchronized (UPDATE_LOCK) {
			return (currFrame == null) ? 0 : currFrame.getWidth();
		}
	}
	public int getHeight() {
		synchronized (UPDATE_LOCK) {
			return (currFrame == null) ? 0 : currFrame.getHeight();
		}
	}
	public int getRefPixelX() {
		synchronized (UPDATE_LOCK) {
			return (currFrame == null) ? 0 : currFrame.getRefPixelX();
		}
	}
	public int getRefPixelY() {
		synchronized (UPDATE_LOCK) {
			return (currFrame == null) ? 0 : currFrame.getRefPixelY();
		}
	}

	public Vector<Frame> getFrames() {
		return frames;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
		this.currFrame = frames.get(index);
	}

	public long getTotalDuration() {
		return totalDuration;
	}

	@Override
	public Animation clone() {
		return new Animation(this);
	}

	public Frame getCurrFrame() {
		return currFrame;
	}

	public void setCurrFrame(Frame currFrame) {
		this.currFrame = currFrame;
	}

	public long getAnimTime() {
		return animTime;
	}

	public void setAnimTime(long animTime) {
		this.animTime = animTime;
	}

	public void dispose() {
		for (Frame f : this.frames) {
			f.dispose();
		}
		this.frames.clear();
	}

	public boolean contains(int x, int y) {
		return this.currFrame.contains(x, y);
	}

	public Object getUPDATE_LOCK() {
		return UPDATE_LOCK;
	}

	public void setUPDATE_LOCK(Object uPDATE_LOCK) {
		UPDATE_LOCK = uPDATE_LOCK;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public void setFrames(Vector<Frame> frames) {
		this.frames = frames;
	}

	public void setTotalDuration(long totalDuration) {
		this.totalDuration = totalDuration;
	}
}
