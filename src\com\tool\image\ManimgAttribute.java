package com.tool.image;

import com.tool.ModerateTask.TaskProgress;
import com.tool.tcp.GetTcpPath;
import com.tool.tcp.NewPart;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
import com.tool.tcpimg.Effects;
import com.tool.tcpimg.FloatPanel;
import com.tool.tcpimg.TxZJ;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.FightingMove2;
import come.tool.Fighting.Fightingimage;
import come.tool.Fighting.sidian;
import come.tool.handle.HandleState;
import jxy2.setup.SetupMainJPanel;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.bean.*;
import org.come.entity.RoleSummoning;
import org.come.model.Door;
import org.come.model.Robots;
import org.come.npc.TP;
import org.come.test.Main;
import org.come.thread.TimeControlRunnable;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 人物属性
 * 
 * <AUTHOR>
 */
public class ManimgAttribute {
	public static boolean ISTCP = true;
	public static boolean ISNAME = true;
	private RoleShow roleShow;
	private NpcInfoBean npc;// npc数据
	private MapMonsterBean mapMonsterBean;// 野怪数据
	private TaskProgress taskdata;// 任务怪数据
	private int leixing; // 0 玩家自己 1其他玩家 2npc 3召唤兽 4 宝宝 5 野怪 6任务怪
	private long time;
	private int dir;
	private NewPart part;
	private NewPart flyShadow; // 飞行时的影子部分
	private String currentFlyPath; // 当前飞行动画路径
	private boolean isLanding = false; // 是否正在降落
	// 用了判断是否在屏幕内
	private Image image;
	private int x,y;
	/** 历史冒泡对话 */
	private List<FloatPanel> chatPanels = new ArrayList<>();
	// 名称
	private String name;
	// 名
	private String[] names;
	// 称谓
	private String[] appellation;
	/** 显示点击效果 */
	private static List<Effects> Effectslist = new ArrayList<>();
	private int currentFlyId = -1; // 添加成员变量保存当前的飞行器ID
	//关闭雾化
	public static float alpha=0.4f;
	/** 修改人物皮肤 */
	public void changeskin(String skin) {
		boolean isZJ=false;
		NewPart oldPart = part; // 保存旧的part引用
		part=null;
		if (roleShow!=null) {
			if (roleShow.getSkin() != null && !roleShow.getSkin().equals("")) {
				String[] vs=roleShow.getSkin().split("\\|");
				for (int i = 0; i < vs.length; i++) {
					if (vs[i].startsWith("X") || vs[i].startsWith("P")) {
						String[] ts = vs[i].substring(1).split("_");
						NewPart newPart=SpriteFactory.createPart("tx/tx" + ts[0], -2, Integer.parseInt(ts[1]), null);
						if (part==null) {part=newPart;}
						else {part=part.addPart(newPart);}
					}else if (vs[i].startsWith("J")) {// 足迹
						isZJ = true;
						if (txZJ == null) {txZJ = new TxZJ("tx/tx" + vs[i].substring(1));}
						else {txZJ.setSkin("tx/tx" + vs[i].substring(1));}
					} else if (vs[i].startsWith("S")) {// 皮肤
						skin=vs[i].substring(1);
					} else if (vs[i].startsWith("C")) {// 称谓特效
						NewPart newPart=SpriteFactory.createPart("tx/" + vs[i].substring(1), -2, 6, null);
						if (part==null) {part=newPart;}
						else {part=part.addPart(newPart);}
					} else if (vs[i].startsWith("B")) {// 翅膀特效
						String cb = vs[i].substring(1);
						NewPart newPart=SpriteFactory.createPart("tx/" + cb + "0", -2, -5, null);
						if (part==null) {part=newPart;}
						else {part=part.addPart(newPart);}
						newPart=SpriteFactory.createPart("tx/" + cb + "1", -2, 5, null);
						part=part.addPart(newPart);
					}
				}
			}
		}
		if (skin!=null&&(roleShow==null||roleShow.getMount_id()==0||skin.length()<10)) {
			NewPart newPart=SpriteFactory.createPart(skin, 2, 1, null);
			if (part==null) {
				part=newPart;
			} else {
				part=part.addPart(newPart);
			}
		}else if (roleShow!=null) {
			if (roleShow.getMount_id()!=0) {
				NewPart newPart=SpriteFactory.createPart(((long)roleShow.getMount_id()<<40)|roleShow.getSpecies_id().longValue(), 2, 1,null);
				if (part==null) {part=newPart;}
				else {part=part.addPart(newPart);}
			}else {
				NewPart newPart=SpriteFactory.createPart(roleShow.getSpecies_id().longValue(), 2, 1, null);
				if (part==null) {part=newPart;}
				else {part=part.addPart(newPart);}
            }
        }


		if (roleShow != null && roleShow.getFlyID() != 0) {
			currentFlyId = roleShow.getFlyID(); // 保存当前的飞行器ID
			String flyPath = "res/fly/fly" + currentFlyId + "/stand.tcp";

			// 如果是新的飞行状态或从非飞行状态切换到飞行状态
			if (!flyPath.equals(currentFlyPath) || isLanding) {
				currentFlyPath = flyPath;
				isLanding = false;
				// 创建飞行主体
				NewPart flyPart = SpriteFactory.createPart(flyPath, -3, 1, null);
			if (part == null) {
					part = flyPart;
			} else {
					// 设置飞行状态
					part.setFly(flyPath);
				}
				// 创建独立的影子
				String shadowPath = "res/fly/fly" + currentFlyId + "-shadow/run.tcp";
				flyShadow = SpriteFactory.createPart(shadowPath, -3, 1, null);
				// 重置影子位置
				currentShadowOffset = 0;
			}
		} else if (currentFlyPath != null && !isLanding) {
			// 如果之前在飞行，现在要降落
			isLanding = true;
			currentShadowOffset = 100;
			// 保持当前的part和flyShadow不变，让动画系统处理它们
			if (oldPart != null) {
				part = oldPart;
			}
		}

        if (!isZJ) {txZJ=null;}
	}
	/** 玩家的初始化方法 */
	public ManimgAttribute(RoleShow roleShow, int leixing) {
		super();
		try {
			this.name = roleShow.getRolename();
			this.names = AccessTeamInfoUntil.getss(name);
			this.appellation = AccessTeamInfoUntil.getss(roleShow.getTitle());
			this.leixing = leixing;
			this.roleShow = roleShow;
			initTeam();
			changeskin(null);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	//宠物跟随
	public ManimgAttribute(RoleSummoning roleSummoning, int leixing) {
		super();
		try {
			this.name = roleSummoning.getSummoningname();
			this.names = AccessTeamInfoUntil.getss(name);
			this.leixing = leixing;
			part=roleSummoning.getPart();
			LoginResult loginResult = new LoginResult();
			loginResult.setX(0L);
			loginResult.setY(0L);
			loginResult.setGrade(0);
			loginResult.setTurnAround(roleSummoning.getTurnRount());
			this.roleShow=new RoleShow(loginResult);//
			this.getMoves();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void moveadopPet(ManimgAttribute manimgAttribute){
		if (manimgAttribute != null) {
			manimgAttribute.bhs(this.getmove());
		}
	}
	public void bhs(sidian sidian) {
		addmove();
		if (sidian.getZ()==4){
			roleShow.setY(sidian.getY()-20);
			roleShow.setX(sidian.getX()+50);
		}else if (sidian.getZ()==7){
			roleShow.setY(sidian.getY());
			roleShow.setX(sidian.getX()-40);
		}else {
			roleShow.setY(sidian.getY());
			roleShow.setX(sidian.getX() + 40);
		}
		setDir(sidian.getZ());
		setType(sidian.getW());
	}

	/** npc的初始化方法 */
	public ManimgAttribute(NpcInfoBean npcInfoBean) {
		super();
		try {
			changeskin(npcInfoBean.getNpctable().getSkin());
			this.name = npcInfoBean.getNpctable().getNpcname();
			this.names = AccessTeamInfoUntil.getss(name);
			this.leixing = 2;
			this.npc = npcInfoBean;
			x = Integer.parseInt(npc.getNpctable().getTx());
			y = Integer.parseInt(npc.getNpctable().getTy());
			this.appellation = AccessTeamInfoUntil.getss(npc.getNpctable().getTitle());
			this.dir = dirtiao(Integer.parseInt(npc.getNpctable().getDir()));
		} catch (Exception e) {
		    e.printStackTrace();
		}
	}
	/** npc喊话 */
	public void npcmsg() {
		String text = npc.getNpctable().getTicktxt();
		if (text == null || text.equals(""))
			return;
		String jg = npc.getNpctable().getTick();
		if (jg == null || jg.equals("") || jg.equals("0"))
			return;
		int jgtime = Integer.parseInt(jg) * 1000;
		if (time > jgtime) {
			time = 0;
			if (chatPanels.size() == 0)
				chatPanels.add(new FloatPanel(text));
			FrameMessageChangeJpanel.addtext(0, text, null,name);
		}
	}
	/** npc方向调整 */
	public int dirtiao(int dir) {
		if (dir == 1)
			return 0;
		else if (dir == 3)
			return 5;
		else if (dir == 5)
			return 1;
		else if (dir == 7)
			return 7;
		return 1;
	}
	/** 野怪初始化5 */
	public ManimgAttribute(MapMonsterBean mapMonsterBean,String[] names,String[] appellation) {
//		
		try {
			this.name=mapMonsterBean.getRobotname();
			this.names=names;
			this.appellation=appellation;
			changeskin(mapMonsterBean.getRobotskin());
			this.leixing = 5;
			this.mapMonsterBean = mapMonsterBean;
			x = mapMonsterBean.getX();
			y = mapMonsterBean.getY();
		} catch (Exception e) {

		}
	}
	/** 任务怪初始化6 */
	public ManimgAttribute(TaskProgress progress) {
		try {
			this.name = progress.getDName();
			this.names = AccessTeamInfoUntil.getss(name);
			Robots robots=UserMessUntil.getRobot(progress.getDId()+"");
			changeskin(robots!=null?robots.getRobotskin():"100");
			this.leixing = 6;
			this.taskdata = progress;
			x = progress.getX();
			y = progress.getY();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/** 判断是否在画布内 */
	public PathPoint isdraw() {
		PathPoint pathPoint = null;

		if (roleShow != null) {
			pathPoint = Util.mapmodel.path(roleShow.getX(), roleShow.getY());
		} else {
			pathPoint = Util.mapmodel.path(x, y);
		}
		return pathPoint;
	}
	/** 传入画笔 所要显示的位置 */
	public boolean Drawing(Graphics g2, long DieTime) {
		PathPoint pathPoint = isdraw();
		if (pathPoint == null) {
			if (part != null) part.recycle();
			return false;
		}
		time += DieTime;
		if (!ISTCP && leixing == 1) {
			if (part != null) part.recycle();
			if (ISNAME)
				drawfonts(g2,pathPoint.getX(),pathPoint.getY());
			if (roleShow != null)
				drawTou(g2,pathPoint);
			return true;
		}
		if (ISNAME || leixing != 1)
			drawfonts(g2, pathPoint.getX(), pathPoint.getY());

		if (part != null) {

			// 先绘制影子（如果存在）
			if (flyShadow != null) {
				int[] specialShadowIds = {42, 43, 44, 45, 46};
				boolean isSpecialShadow = Arrays.stream(specialShadowIds).anyMatch(x -> x == currentFlyId);
				
				int baseHeight = isSpecialShadow ? 100 : 0;  // 特殊模型预设低100，其他与主体同高
				int maxDownOffset = isSpecialShadow ? -40 : 0;  // 特殊模型下降到-100，其他下降到0
				
				int shadowY;
				// 更新影子位置
				if (isLanding) {
					// 降落时，影子逐渐上移
					currentShadowOffset = Math.max(maxDownOffset, currentShadowOffset - SHADOW_STEP-2);
					// 特殊ID直接使用-100作为基准位置
					shadowY = isSpecialShadow ? 
						(pathPoint.getY()-100) + (int)currentShadowOffset :
						(pathPoint.getY()-baseHeight) + (int)currentShadowOffset;
					// 先确保影子完全移动到位
					if (currentShadowOffset <= maxDownOffset) {
						// 影子已经到位，开始处理主体消失
						double alpha = part.getBodyAlpha();
						alpha = Math.max(0, alpha - 0.05);
						part.setBodyAlpha(alpha);

						// 只有当主体完全消失后，才清理状态
						if (alpha <= 0) {
							currentFlyPath = null;
							flyShadow = null;
							part.setFly(null);
							isLanding = false;
							currentFlyId = -1; // 重置飞行器ID
							changeskin(null);
						}
					}
				} else {
					// 起飞时，影子逐渐下移
					currentShadowOffset = Math.min(100, currentShadowOffset + SHADOW_STEP+2);
					// 特殊ID直接使用-100作为基准位置
					shadowY = isSpecialShadow ? 
						(pathPoint.getY()-100) + (int)currentShadowOffset :
						(pathPoint.getY()-baseHeight) + (int)currentShadowOffset;
				}

				// 绘制影子
				if (flyShadow != null) {
					flyShadow.draw(g2, pathPoint.getX(), shadowY, dir, time);
				}

				synchronized (Util.mapmodel.clouds) {
					if (Util.mapmodel.clouds.size() <= 0) Util.mapmodel.initClouds();
					for (Map.Entry<Mapmodel.Cloud, Sprite> stringSpriteEntry : Util.mapmodel.clouds.entrySet()) {
						Mapmodel.Cloud cloud = stringSpriteEntry.getKey();
						Sprite value = stringSpriteEntry.getValue();
						if (value == null) continue;
						if (cloud.x > (Util.mapmodel.getShot_x() - ScrenceUntil.Screen_x * 0.5) && cloud.x < (Util.mapmodel.getShot_x() + ScrenceUntil.Screen_x) && cloud.y > (Util.mapmodel.getShot_y() - ScrenceUntil.Screen_y * 0.5) && cloud.y < (Util.mapmodel.getShot_y() + ScrenceUntil.Screen_y)) {
							value.updateToTime(ImageMixDeal.userimg.getTime(), 0);
							value.draw(g2, cloud.x - Util.mapmodel.getShot_x(), cloud.y - Util.mapmodel.getShot_y());
						}
						cloud.x += 1;
						if (cloud.x > Util.mapmodel.getjMap().getMapWidth()) cloud.x = -100;
					}
				}

				//雾化
				Graphics graphics = g2.create();
				((Graphics2D) graphics).setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.2F));
				ImageIcon icon = CutButtonImage.getImage("res/fly/white.png",-1,-1);
				graphics.drawImage(icon.getImage(), -100, -100, Main.frame.getLoginJpanel().getGameView().getWidth() * 2, Main.frame.getLoginJpanel().getGameView().getHeight() * 2, null);
				graphics.dispose();



			}

			// 然后绘制主体
			if (Util.mapmodel.zhezhao(pathPoint.getX(), pathPoint.getY())) {
				part.draw(g2, pathPoint.getX(), pathPoint.getY(), dir, time, 0.6f);
			} else {
				if (leixing == 1||leixing == 0 && flyShadow == null) {
					SpriteFactory.shadow.draw(g2, pathPoint.getX(), pathPoint.getY());
				}
				part.draw(g2, pathPoint.getX(), pathPoint.getY(), dir, time);

			}
			
			draw(g2, pathPoint.getX(), pathPoint.getY());

			if (roleShow != null){
				drawTou(g2, pathPoint);
			}else if(mapMonsterBean!=null&&mapMonsterBean.getHP()!=null&&mapMonsterBean.getHP().getX()>0){
				int hp_bai = (int) (75D*mapMonsterBean.getHP().getX()/mapMonsterBean.getHP().getY());
				if (hp_bai > 75) {hp_bai = 75;}
				else if (hp_bai < 0) {hp_bai = 0;}
				g2.drawImage(Fightingimage.xuelans[0].getImage(), pathPoint.getX() - 35, pathPoint.getY() - 125, 75, 5, null);
				g2.drawImage(Fightingimage.xuelans[1].getImage(), pathPoint.getX() - 35, pathPoint.getY() - 125, hp_bai, 5, null);
			}
			if (txZJ!=null) {txZJ.draw(g2, DieTime);}
		}
		return true;
	}
	/** 玩家头上动画 */
	private void drawTou(Graphics g2, PathPoint pathPoint) {
		// 判断是否在战斗
		if (roleShow.getFighting() != 0) {
			Sprite mouse = SpriteFactory.Prepare(GetTcpPath.PK);
			if (mouse != null) {
				mouse.updateToTime(time,0);
				mouse.draw(g2, pathPoint.getX() - 15, pathPoint.getY() - 110);
			}
		} else {
			if (roleShow.getCaptian() == 1) {
				Sprite mouse = SpriteFactory.Prepare(GetTcpPath.LIN);
				if (mouse != null) {
					mouse.updateToTime(time,0);
					mouse.draw(g2, pathPoint.getX() -3, pathPoint.getY() - 120);
				}
			}
		}
	}
	private long movejiange = 0;
	private List<sidian> moves;
	public List<sidian> getMoves() {
		if (moves == null)
			moves = new ArrayList<>();
		return moves;
	}
	public void setMoves(List<sidian> moves) {
		this.moves = moves;
	}
	public sidian getmove() {
		if (getMoves().size() == 0)
			moves.add(new sidian(roleShow.getX(), roleShow.getY(), dir, part.getAct()));
		return moves.get(0);
	}
	/** 重置 */
	public void CZmove() {
		getMoves();
		for (int i = 0; i < moves.size(); i++) {
			moves.get(i).setsidian(roleShow.getX(), roleShow.getY(), dir, part.getAct());
		}
	}
	public void addmove() {
		if (getMoves().size() >= 13) {
			if (part.getAct() == 1) {
				sidian sidian = moves.remove(0);
				sidian.setsidian(roleShow.getX(), roleShow.getY(), dir, part.getAct());
				if (txZJ!=null) {txZJ.addZJ(roleShow.getX(), roleShow.getY(), dir);}
				moves.add(sidian);
			} else {
				getmove().setW(2);
			}
		}else {
			if (part.getAct()==1&&txZJ!=null) {txZJ.addZJ(roleShow.getX(), roleShow.getY(), dir);}
			moves.add(new sidian(roleShow.getX(), roleShow.getY(), dir, part.getAct()));
		}
	}

	private String[] teams;

	public void initTeam() {
		teams = null;
		if (roleShow.getTroop_id()==null||roleShow.getTeamInfo()==null|| roleShow.getTeamInfo().isEmpty()) {
			teams =new String[]{roleShow.getRolename()};	
			roleShow.setCaptian(0);
		}else {
			teams=roleShow.getTeamInfo().split("\\|");
			if (!teams[0].equals(roleShow.getRolename())) {
				teams=null;
			}
			roleShow.setCaptian(teams!=null?1:0);
		}
		if (leixing==0&&teams==null) {
			TimeControlRunnable.removeScript();
		}
	}


    private TxZJ txZJ;
	private float currentShadowOffset = 0; // 当前影子偏移量
	private static final float SHADOW_STEP = 2.0f; // 影子移动步长
	/** 移动 */
	public void move(long time) {
		if (teams != null) {
			addmove();
			if (roleShow.getPlayer_Paths().size() <= 1) {
				//禁止状体
				setType(2);
			} else {
				//移动状体
				setType(1);
				movejiange = FightingMove2.getmovetime(this,roleShow,roleShow.getMount_id() == 7 ? 0.28 : 0.24, getMovejiange(time), dir);
				if (leixing == 0){DoorRect();}
			}
			ManimgAttribute my = this;
			for (int i = 1; i < teams.length; i++) {
				ManimgAttribute manimgAttribute = ImageMixDeal.huoquLogin(teams[i]);
				if (manimgAttribute != null) {
					manimgAttribute.bh(my.getmove());
					my = manimgAttribute;
				}
			}
		}
	}



	/**野怪移动*/
	public void move2(long time) {
		if (mapMonsterBean.getFollow()!=null) {
			ManimgAttribute attribute=ImageMixDeal.huoquLogin(mapMonsterBean.getFollow());
			if (attribute!=null) {
				GSM(attribute.getmove());
				return;
			}
		}
		if (mapMonsterBean.getMovePath()==null||mapMonsterBean.getType()!=0) {
		
		}else if (mapMonsterBean.getMovePath().isMove(this,time,dir)) {
			setType(2);
			mapMonsterBean.setMovePath(null);
		}
	}
	/** 队员坐标变化 */
	public void bh(sidian sidian) {
		addmove();
		roleShow.setX(sidian.getX());
		roleShow.setY(sidian.getY());
		setDir(sidian.getZ());
		setType(sidian.getW());
	}
    /**跟随处理*/
	public void GS(sidian sidian){
		x=sidian.getX();
		y=sidian.getY();
		setDir(sidian.getZ());
		setType(sidian.getW());
	}
	/**野怪跟随处理*/
	public void GSM(sidian sidian) {
		x = sidian.getX()+5;
		y = sidian.getY()+5;
		setDir(sidian.getZ());
		setType(sidian.getW());
	}
	/** 判断是否进入door矩阵内 */
	public void DoorRect() {
		Door door = Util.mapmodel.tp(roleShow.getX(), roleShow.getY());
		if (door == null)
			return;
		roleShow.getPlayer_Paths().clear();
		TP.tp(door,0);
	}
	public void draw(Graphics g, int x, int y) {
		if (!chatPanels.isEmpty()) {
			int re = part.getPy()<200?48:88;
			int py =roleShow!=null&&roleShow.getMount_id()>0?part.getPy()-68 :
					roleShow!=null&&roleShow.getSkin()!=null&&!roleShow.getSkin().isEmpty()?part.getPy()-re:
					part.getPy();
			if (py!=-1) {// 人物冒泡对话内容
				int chatY = y - py ;				
				for (int i = chatPanels.size() - 1; i >= 0; i--) {
					FloatPanel chatPanel = chatPanels.get(i);
					if (shouldDisplay(chatPanel)) {
						int chatX=x-chatPanel.getWidth()/2;
						chatY-=chatPanel.getHeight()+2;
						g.translate(chatX,   chatY);
						chatPanel.paint(g);
						g.translate(-chatX, -chatY);
					} else {
						chatPanel.remove();
						chatPanels.remove(i);
					}
				}
			}	
		}
	}
	public void draweffects(Graphics g) {
		for (int i = Effectslist.size() - 1; i >= 0; i--) {
			Effects effects = Effectslist.get(i);
			effects.addTime(15);
			if (EffectsDisplay(effects)) {
				PathPoint pathPoint = Util.mapmodel.path(Effectslist.get(i).getX(), Effectslist.get(i).getY());
				if (pathPoint==null) {
					continue;
				}
				Sprite sprite1 = SpriteFactory.Prepare(GetTcpPath.getMouseTcp(effects.getEffectsName()));
				if (sprite1 == null){
					continue;
				}
				sprite1.updateToTime(effects.getTime(),0);
				sprite1.draw(g, pathPoint.getX(), pathPoint.getY());
			} else {
				Effectslist.remove(i);
			}
		}
	}

	public static boolean shouldDisplay(FloatPanel chatPanel) {
		return Util.getTime() - chatPanel.getCreateTime() < Util.TIME_CHAT;
	}

	public static boolean EffectsDisplay(Effects effects) {
		if (effects.getEffectsName().equals("升级")) {
			return effects.getTime() < Util.TIME_CHAT / 2;
		}
		return effects.getTime() < Util.TIME_CHAT / 10;
	}

	// 徽章图片
	public static Image[] hzImgs = new Image[6];
	static {
		hzImgs[0] = new ImageIcon("img/head/hz_0.png").getImage();
		hzImgs[1] = new ImageIcon("img/head/hz_1.png").getImage();
		hzImgs[2] = new ImageIcon("img/head/hz_2.png").getImage();
		hzImgs[3] = new ImageIcon("img/head/hz_3.png").getImage();
		hzImgs[4] = new ImageIcon("img/head/hz_4.png").getImage();
		hzImgs[5] = new ImageIcon("img/head/hz_5.png").getImage();
	}

	public static Image getHzImg(int i) {
		if (i >= hzImgs.length) {i=hzImgs.length-1;}
		return hzImgs[i];
	}

	/**
	 * 画字体
	 */
	int size1;
	public void drawfonts(Graphics g, int x, int y) {
		if (names == null){
			return;
		}
		g.setFont(SetupMainJPanel.getAllFont());
		int textY = y + 50;
		if (size1 == 0) {
			size1 = org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(name) / 2;
		}
		int textX = x - size1;
		Graphics2D g2d = (Graphics2D) g.create();
		if (SetupMainJPanel.getAllFont()!=null&&SetupMainJPanel.getAllFont().getFontName().equals("宋体")){
			g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
		}else {
			g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
		}
		if (leixing==0||leixing==1) {
			if (roleShow.getSkill_id() != null) {
				Image hz = getHzImg(roleShow.getSkill_id());
				g2d.drawImage(hz, textX - 28, textY - 9, null);
			}
			textY += 9;
			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			textX = x - org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(name) / 3;
			for (int i = 0; i < names.length; i++) {
				// 黑
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString(names[i], textX + 1, textY + 1);
				// 浅
				g2d.setColor(UIUtils.getcolor(roleShow.getTurnAround()));
				g2d.drawString(names[i], textX, textY);
				textX += fm.stringWidth(names[i]) - 1;
			}



			if (Util.hideTitle) {
				if (appellation != null&&roleShow.getTitle()!=null) {
					textY -= 19;
					textX = 9+ x - org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(roleShow.getTitle()) / 2;
					for (int i = 0; i < appellation.length; i++) {
						g2d.setColor(UIUtils.COLOR_NAME8);
						g2d.drawString(appellation[i], textX + 1, textY+1);
						g2d.setColor(UIUtils.COLOR_title2);
						g2d.drawString(appellation[i], textX, textY);
						textX += fm.stringWidth(appellation[i]) - 1;
					}
				}		
			}
			//名字颜色
			if (roleShow.getStatlv()!=null){
				textY += !Util.hideTitle?19:38;
				textX = 9 + x - g.getFontMetrics().stringWidth("["+roleShow.getStatlv()+"]") / 2;
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString("["+roleShow.getStatlv()+"]", textX+1, textY +1);
				g2d.setColor(UIUtils.getcolor(roleShow.getTurnAround()));
				g2d.drawString("["+roleShow.getStatlv()+"]", textX, textY);
			}
		}else {
			if(appellation!=null){
				textY -= 5;
			}else{
				textY -= 20;
			}
			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			for (int i = 0; i < names.length; i++) {
				// 黑
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString(names[i], textX + 1, textY + 1);
				// 浅
				g2d.setColor(UIUtils.COLOR_NPC1);
				g2d.drawString(names[i], textX, textY);
				textX += fm.stringWidth(names[i]) - 1;		
			}
			if (appellation!=null) {
				if (npc!=null) {
					textX = x - g2d.getFontMetrics().stringWidth(npc.getNpctable().getTitle()) / 2;
				}else if (mapMonsterBean!=null) {
					textX = x - g2d.getFontMetrics().stringWidth(mapMonsterBean.getRobottitle()) / 2;	
				}
				for (int i = 0; i < appellation.length; i++) {
					g2d.setColor(UIUtils.COLOR_NAME8);
					g2d.drawString(appellation[i], textX + 1, textY-19);
					g2d.setColor(UIUtils.COLOR_title2);
					g2d.drawString(appellation[i], textX, textY - 20);
					textX += fm.stringWidth(appellation[i]) - 1;
				}	
			}		
		}
		g2d.dispose();
	}

	/**
	 * 添加对话内容
	 * 
	 * @return
	 */
	public void Dialogue(String text) {
		if (FightingMixDeal.State == HandleState.USUAL) {
			chatPanels.add(new FloatPanel(text));
		} else {
			FightingMixDeal.Dialogue(roleShow.getRolename(), text);
		}
	}

	/**
	 * 添加特效内容
	 * 
	 * @return
	 */
	public static void addEffects(String text, int x, int y) {
		for (int i = 0; i < Effectslist.size(); i++) {
			if (!Effectslist.get(i).getEffectsName().equals(text))
				continue;
			Effectslist.get(i).setX(x);
			Effectslist.get(i).setY(y);
			Effectslist.get(i).setTime(0);
			return;
		}
		Effectslist.add(new Effects(text, x, y));
	}

	public RoleShow getRoleShow() {
		return roleShow;
	}
	public void setRoleShow(RoleShow roleShow) {
        this.roleShow = roleShow;
		initTeam();
	}
	
	public MapMonsterBean getMapMonsterBean() {
		return mapMonsterBean;
	}

	public void setMapMonsterBean(MapMonsterBean mapMonsterBean) {
		this.mapMonsterBean = mapMonsterBean;
	}

	public int getDir() {
		return dir;
	}

	public void setDir(int dir) {
		this.dir = dir;
	}

	public int getType() {
		return part.getAct();
	}

	public void setType(int type) {
		if (part==null)return;
		if (this.part.getAct() == type) {
			return;
		}

		this.part.setAct(type);
	}

	public long getMovejiange(long movejiange) {
		return this.movejiange += movejiange;
	}

	public void setMovejiange(long movejiange) {
		this.movejiange = movejiange;
	}

	public NpcInfoBean getNpc() {
		return npc;
	}

	public void setNpc(NpcInfoBean npc) {
		this.npc = npc;
	}

	public long getTime() {
		return time;
	}

	public void setTime(long time) {
		this.time += time;
	}

	public Image getImage() {
		return image;
	}

	public void setImage(Image image) {
		this.image = image;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.size1 = 0;
		this.name = name;
		this.names = AccessTeamInfoUntil.getss(this.name);
	}

	public TaskProgress getTaskdata() {
		return taskdata;
	}

	public void setTaskdata(TaskProgress taskdata) {
		this.taskdata = taskdata;
	}

	public String[] getNames() {
		return names;
	}

	public void setNames(String[] names) {
		this.names = names;
	}

	public String[] getAppellation() {
		return appellation;
	}

	public void setAppellation(String chang) {
		this.appellation = AccessTeamInfoUntil.getss(chang);
	}

	public String[] getTeams() {
		return teams;
	}

	public void setTeams(String[] teams) {
		this.teams = teams;
	}
	public int getLeixing() {
		return leixing;
	}
	
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	/**
	 * 判断是否点击内
	 * 
	 * @return
	 */
	public boolean isContains(int x, int y) {
		if (roleShow != null) {
			return part.contains(x - roleShow.getX(), y - roleShow.getY());
		} else {
			return part.contains(x - this.x, y - this.y);
		}
	}

	/**
	 * 重置对象状态 - 用于对象池复用
	 */
	public void reset(NpcInfoBean npcInfo) {
		// 清理旧数据
		cleanup();

		// 重新初始化NPC数据
		this.npc = npcInfo;
		this.leixing = 2; // NPC类型
		this.name = npcInfo.getNpctable().getNpcname();
		this.names = AccessTeamInfoUntil.getss(name);
		this.appellation = AccessTeamInfoUntil.getss(npcInfo.getNpctable().getTitle());

		// 重置位置和状态
		this.x = Integer.parseInt(npcInfo.getNpctable().getTx());
		this.y = Integer.parseInt(npcInfo.getNpctable().getTy());
		this.dir = dirtiao(Integer.parseInt(npcInfo.getNpctable().getDir()));
		this.time = 0;
		// this.type = 2; // 静止状态 - 注释掉，因为type字段不存在

		// 重新加载皮肤
		try {
			changeskin(npcInfo.getNpctable().getSkin());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 清理对象资源 - 用于对象池回收
	 */
	public void cleanup() {
		// 清理图像资源
		if (image != null) {
			image.flush();
			image = null;
		}

		// 清理聊天面板
		if (chatPanels != null) {
			chatPanels.clear();
		}

		// 清理飞行相关资源
		if (flyShadow != null) {
			flyShadow = null;
		}
		currentFlyPath = null;
		isLanding = false;

		// 重置基本属性
		roleShow = null;
		npc = null;
		mapMonsterBean = null;
		taskdata = null;
		part = null;
		name = null;
		names = null;
		appellation = null;
		teams = null;

		// 重置数值
		leixing = 0;
		time = 0;
		dir = 0;
		x = 0;
		y = 0;
		// type字段不存在，注释掉
		// type = 0;
		movejiange = 0;
		currentShadowOffset = 0;
	}
}
