package com.tool.btn;

import com.tool.role.GetExp;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.guaed.fl.RonglianJPanel;
import jxy2.jutnil.Juitil;
import org.come.Frame.GuarDianLvFrame;
import org.come.Jpanel.MountJPanel;
import org.come.bean.LoginResult;
import org.come.until.AnalysisString;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Music;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;

public class GuardMountBtn extends MoBanBtn implements MouseListener {
    public int index;
    public  int[] xy ;
    // 定义图标数组以减少重复代码
    public Icon[] selectedIcons = {Juitil.tz285, Juitil.tz287, Juitil.tz298, Juitil.tz284};
    public static Icon[] unselectedIcons = {Juitil.tz286, Juitil.tz288, Juitil.tz289, Juitil.tz283};
    public MountJPanel mountJPanel;
    public GuardMountBtn(String iconpath, int type, int index, MountJPanel mountJPanel,int[] xy) {
        super(iconpath, 0, type);
        this.index=index;
        this.mountJPanel=mountJPanel;
        this.xy = xy;
    }
    public GuardMountBtn(String iconpath, int type, String text, MountJPanel mountJPanel, String prowpt, int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        setColor(UIUtils.COLOR_CL_NAME);
        setFont(UIUtils.NEWTX_HY15);
        this.index=index;
        this.mountJPanel=mountJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        btnchange(2);
        if (index==6){
                String type = mountJPanel.getName(mountJPanel.getGuardType());
                if (!FormsManagement.getframe(139).isVisible()) {
                    // 获取登录用户信息
                    LoginResult loginResult = RoleData.getRoleData().getLoginResult();
                    // 获取已兑换属性点
                    int parseInt = loginResult.getExtraPointInt(TypeDianNumData(type));
                    // 获取人物经验
                    BigDecimal experience = loginResult.getExperience();
                    // 获取当前的天枢点
                    int tsp = GetExp.getTSP(parseInt);
                    // 获取当前的修为值
                    int tsx = GetExp.getTSX(parseInt);
                    // 获取当前等级兑换所需的经验值
                    int tsExp = GetExp.getTSExp(tsp + 1);
                    // 获取人物等级
                    String lvl = AnalysisString.lvl(loginResult.getGrade());
                    // 获取当前人物可以兑换的最大天枢点
                    int realmMaxTSP = 24;
                    // 获取当前人物境界名称
                    GuarDianLvFrame.getGuarDianLvFrame().getGuarDianLvJPanel()
                            .ObtainVariousParameters(experience, tsx, tsExp, lvl, type, tsp, realmMaxTSP);
                    FormsManagement.showForm(139);
                } else {
                    FormsManagement.HideForm(139);
                    Music.addyinxiao("关闭窗口.mp3");
                }

        }else if (index==5){
            RonglianJPanel.isvstop();
            FormsManagement.HideForm(142);
            if (!FormsManagement.getframe(141).isVisible()) {
            FormsManagement.showForm(141);
        } else {
            FormsManagement.HideForm(141);
            Music.addyinxiao("关闭窗口.mp3");
        }
        }else {
            // 设置选中的图标
            if (index >= 0 && index < selectedIcons.length) {
                mountJPanel.getRosefinch().setIcon(index == 0 ? selectedIcons[0] : unselectedIcons[0]);
                mountJPanel.getWhiteTiger().setIcon(index == 1 ? selectedIcons[1] : unselectedIcons[1]);
                mountJPanel.getQinglong().setIcon(index == 2 ? selectedIcons[2] : unselectedIcons[2]);
                mountJPanel.getXuanwu().setIcon(index == 3 ? selectedIcons[3] : unselectedIcons[3]);
                MountJPanel.getDrawEffect().setBounds(xy[0]-10,xy[1]+4,50,126);
                MountJPanel.getDrawEffect().setVisible(true);
                mountJPanel.isLoaded= false;
                mountJPanel.getZhongtian().setIcon(Juitil.tz309);
            }
            // 设置其他按钮的状态
            StopShow(mountJPanel,index);
            mountJPanel.ComponentDisplay(index);
        }

    }

    public static String TypeDianNumData(String type) {
        switch (type){
            case "青龙":
                return "S";
            case "白虎":
                return "C";
            case "朱雀":
                return "Z";
            case "玄武":
                return "W";
            default://中天
                return "E";
        }
    }

    /**
     * 停止展示除特定索引外的其他角色
     * 此方法用于确保只有指定索引的角色保持当前状态，其他所有角色都重置为未选中状态
     *
     * @param mountJPanel 包含角色按钮和图标的应用窗口面板
     * @param index       特定角色的索引，此角色将保持当前状态，其他角色将被重置
     */
    public static void StopShow(MountJPanel mountJPanel,int index){
        for (int i = 0; i < mountJPanel.getGirlQlBtns().length; i++) {
            if (index != i) {
                mountJPanel.getGirlQlBtns()[i].btnchange(0);
                if (i < unselectedIcons.length) {
                    switch (i) {
                        case 0:
                            mountJPanel.getRosefinch().setIcon(unselectedIcons[0]);
                            break;
                        case 1:
                            mountJPanel.getWhiteTiger().setIcon(unselectedIcons[1]);
                            break;
                        case 2:
                            mountJPanel.getQinglong().setIcon(unselectedIcons[2]);
                            break;
                        case 3:
                            mountJPanel.getXuanwu().setIcon(unselectedIcons[3]);
                            break;
                    }
                }
            }
        }

    }


    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (btn!=1) {
            btnchange(0);
        }
    }
    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
    }
}
