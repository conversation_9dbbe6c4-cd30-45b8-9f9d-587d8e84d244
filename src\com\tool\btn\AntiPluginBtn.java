package com.tool.btn;

import java.awt.event.MouseEvent;

import org.come.Jpanel.AntiPluginJpanel;

public class AntiPluginBtn extends MoBanBtn {
	
	private static final long serialVersionUID = 1L;
	private String selt;
	private AntiPluginJpanel jpanel;
	
	public AntiPluginBtn(String iconpath, int type,String selt,AntiPluginJpanel jpanel) {
		super(iconpath, type);
		this.jpanel = jpanel;
		this.selt = selt;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		String value=jpanel.getValue();
		if(value != null && !value.equals("")){
			//判断是否选中正确的字
			if(selt.equals(value)){ //选中
				jpanel.correct();
			}else{//未选中
				jpanel.error();
			}
		}
	}
}
