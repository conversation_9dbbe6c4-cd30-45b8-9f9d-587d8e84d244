package com.tool.imagemonitor;

public class ScriptOpen {

	private int type;//0寻路  1点击NPC 2点击自定义野怪  3选择传送选项  4传送指定door对象  5领取对应的任务选项
	private int x,y;
	private int npc;
	private int door;
	
	public ScriptOpen() {
		// TODO Auto-generated constructor stub
	}
	
	public ScriptOpen(int type) {
		super();
		this.type = type;
	}

	public ScriptOpen(int type, int npc, int door) {
		super();
		this.type = type;
		this.npc = npc;
		this.door = door;
	}
	public ScriptOpen(int x, int y) {
		super();
		this.x = x;
		this.y = y;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public int getNpc() {
		return npc;
	}
	public void setNpc(int npc) {
		this.npc = npc;
	}
	public int getDoor() {
		return door;
	}
	public void setDoor(int door) {
		this.door = door;
	}

	@Override
	public String toString() {
		return "ScriptOpen{" +
				"type=" + type +
				", x=" + x +
				", y=" + y +
				", npc=" + npc +
				", door=" + door +
				'}';
	}
}
