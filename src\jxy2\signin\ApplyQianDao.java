package jxy2.signin;

import org.come.entity.Goodstable;

import java.util.List;
import java.util.Map;

/**
 * 签到申请类
 * 用于处理和存储用户签到相关信息
 */
public class ApplyQianDao {
    // 签到类型
    private int type;
    // 签到日记
    private int dayri;
    // 抽奖列表，映射关系为抽奖ID到奖品列表
    private Map<Integer , List<Goodstable>> choujianBean;
    // 连续签到领取奖励列表，映射关系为抽奖ID到奖品列表
    private Map<Integer , List<Goodstable>> lxCjBean;
    // 签到日期
    private String Qiandaoday;
    // 抽奖结果集合的字符串表示
    private String chpujiangjihe;
    // 连续签到天数
    private int obtainCumulativeDays;
    public String DAYS;
    /**
     * 获取抽奖列表
     * @return 抽奖列表的映射
     */
    public Map<Integer, List<Goodstable>> getChoujianBean() {
        return choujianBean;
    }

    /**
     * 设置抽奖列表
     * @param choujianBean 新的抽奖列表映射
     */
    public void setChoujianBean(Map<Integer, List<Goodstable>> choujianBean) {
        this.choujianBean = choujianBean;
    }
    public Map<Integer, List<Goodstable>> getLxCjBean() {
        return lxCjBean;
    }

    public void setLxCjBean(Map<Integer, List<Goodstable>> lxCjBean) {
        this.lxCjBean = lxCjBean;
    }
    /**
     * 获取签到类型
     * @return 签到类型
     */
    public int getType() {
        return type;
    }

    /**
     * 设置签到类型
     * @param type 新的签到类型
     */
    public void setType(int type) {
        this.type = type;
    }

    /**
     * 获取签到日记
     * @return 签到日记
     */
    public int getDayri() {
        return dayri;
    }

    /**
     * 设置签到日记
     * @param dayri 新的签到日记
     */
    public void setDayri(int dayri) {
        this.dayri = dayri;
    }

    /**
     * 获取签到日期
     * @return 签到日期
     */
    public String getQiandaoday() {
        return Qiandaoday;
    }

    /**
     * 设置签到日期
     * @param qiandaoday 新的签到日期
     */
    public void setQiandaoday(String qiandaoday) {
        Qiandaoday = qiandaoday;
    }

    /**
     * 获取抽奖结果集合
     * @return 抽奖结果的字符串表示
     */
    public String getChpujiangjihe() {
        return chpujiangjihe;
    }

    /**
     * 设置抽奖结果集合
     * @param chpujiangjihe 新的抽奖结果字符串表示
     */
    public void setChpujiangjihe(String chpujiangjihe) {
        this.chpujiangjihe = chpujiangjihe;
    }

    public int getObtainCumulativeDays() {
        return obtainCumulativeDays;
    }

    public void setObtainCumulativeDays(int obtainCumulativeDays) {
        this.obtainCumulativeDays = obtainCumulativeDays;
    }

    public String getDAYS() {
        return DAYS;
    }

    public void setDAYS(String DAYS) {
        this.DAYS = DAYS;
    }
}

