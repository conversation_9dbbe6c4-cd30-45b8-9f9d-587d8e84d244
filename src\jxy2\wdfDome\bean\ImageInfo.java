package jxy2.wdfDome.bean;

import java.io.File;

public class ImageInfo {
    public File file;
    public long offset;
    public String cacheKey;
   public  ImageInfo(File file, long offset) {
       this.file = file;
       this.offset = offset;
       this.cacheKey = file.getPath() + "_" + offset;
   }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public long getOffset() {
        return offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public String getCacheKey() {
        return cacheKey;
    }

    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }
}
