package com.tool.btn;

import org.come.Frame.RaceChangeMainJframe;
import org.come.Jpanel.RaceChangeMainJpanel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class RoleMetemBtn extends MoBanBtn {

    private RaceChangeMainJpanel roleMetempsychosisJpanel;
    private int caozuo;

    public RoleMetemBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer caozuo, RaceChangeMainJpanel roleMetempsychosisJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.caozuo = caozuo;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.roleMetempsychosisJpanel = roleMetempsychosisJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        RaceChangeMainJpanel raceChangeMainJpanel = RaceChangeMainJframe.getRaceChangeMainJframe()
                .getRaceChangeMainJpanel();

        switch (caozuo) {
        case 1:
            if (raceChangeMainJpanel.getRoleType() != 1 && raceChangeMainJpanel.getRoleType() != 2) {
                raceChangeMainJpanel.setRoleType(1);
                raceChangeMainJpanel.changSexNames();
            }

            break;
        case 2:
            if (raceChangeMainJpanel.getRoleType() != 3 && raceChangeMainJpanel.getRoleType() != 4) {
                raceChangeMainJpanel.setRoleType(3);
                raceChangeMainJpanel.changSexNames();
            }
            break;
        case 3:
            if (raceChangeMainJpanel.getRoleType() != 5 && raceChangeMainJpanel.getRoleType() != 6) {
                raceChangeMainJpanel.setRoleType(5);
                raceChangeMainJpanel.changSexNames();
            }

            break;
        case 4:
            if (raceChangeMainJpanel.getRoleType() != 7 && raceChangeMainJpanel.getRoleType() != 8) {
                raceChangeMainJpanel.setRoleType(7);
                raceChangeMainJpanel.changSexNames();
            }
            break;
        case 5:
            if (raceChangeMainJpanel.getRoleType() != 9 && raceChangeMainJpanel.getRoleType() != 10) {
                raceChangeMainJpanel.setRoleType(9);
                raceChangeMainJpanel.changSexNames();
            }

            break;
        default:
            break;
        }
        changMenu(caozuo);
        raceChangeMainJpanel.reloadRace(raceChangeMainJpanel.getRoleType(), raceChangeMainJpanel.getNumber());
    }

    public void changMenu(int type) {
       roleMetempsychosisJpanel.getMenuBtnHuman()[type-1].btnchange(2);
        for (int i = 0; i < roleMetempsychosisJpanel.getMenuBtnHuman().length; i++) {
            if (i != (type-1)){
                roleMetempsychosisJpanel.getMenuBtnHuman()[i].btnchange(0);
            }
        }
    }
}
