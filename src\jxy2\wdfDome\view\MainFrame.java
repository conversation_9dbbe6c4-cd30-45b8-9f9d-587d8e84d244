package jxy2.wdfDome.view;

import jxy2.wdfDome.bean.MainItem;
import jxy2.wdfDome.bean.UpdateAdapter;
import jxy2.wdfDome.bean.WasData;
import jxy2.wdfDome.bean.WdfHead;
import jxy2.wdfDome.event.MainEvent;
import jxy2.wdfDome.main.WdfMianUtil;
import jxy2.wdfDome.util.WdfTool;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.image.BufferedImage;
import java.io.File;

public class MainFrame extends JFrame implements ActionListener {
    private JPanel westPane,centerPane,southPane;
    private JButton[] buttons;
    private UpdateAdapter uadapter;
    private MainEvent mevent;
    private JComboBox rowBox,cellBox;
    private JLabel page,tip;
    private JButton layoutButton,upButton,downButton;
    private int row,cell;
    private WdfHead wdfHead;
    private File wdfFile;
    private int indexItem = 0;
    private static String title = "SeeWDF 1.2";

    public MainFrame() {
        super(title);
        this.setSize(800, 600);
        this.setExtendedState(6);
        this.setLocationRelativeTo(null);
        this.setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
        this.init();
        this.setVisible(true);
    }
    private void init() {
        Container c = this.getContentPane();
        this.westPane = new JPanel();
        this.centerPane = new JPanel();
        this.centerPane.setBackground(new Color(4210752));
        this.southPane = new JPanel(new GridLayout(1, 3));
        c.add(new JScrollPane(this.westPane), "West");
        c.add(this.centerPane, "Center");
        c.add(this.southPane, "South");
        this.westPane.setLayout(new GridLayout(WdfMianUtil.wdfFiles.size(), 1));
        this.buttons = new JButton[WdfMianUtil.wdfFiles.size()];

        for(int i = 0; i < WdfMianUtil.wdfFiles.size(); ++i) {
            JButton button = new JButton((WdfMianUtil.wdfFiles.get(i)).getName());
            this.westPane.add(button);
            button.addActionListener(this);
            this.buttons[i] = button;
        }

        JPanel left = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JPanel center = new JPanel();
        JPanel right = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        String[] s = new String[]{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"};
        this.rowBox = new JComboBox(s);
        this.cellBox = new JComboBox(s);
        this.layoutButton = new JButton("调整");
        this.page = new JLabel("");
        this.tip = new JLabel("");
        this.upButton = new JButton("<");
        this.downButton = new JButton(">");
        left.add(this.rowBox);
        left.add(this.cellBox);
        left.add(this.layoutButton);
        left.add(new JLabel("左键图像弹出预览并复制索引 右键图像复制索引"));
        center.add(this.page);
        right.add(this.tip);
        right.add(this.upButton);
        right.add(this.downButton);
        this.southPane.add(left);
        this.southPane.add(center);
        this.southPane.add(right);
        this.layoutButton.addActionListener(this);
        this.upButton.addActionListener(this);
        this.downButton.addActionListener(this);
        this.uadapter = new UpdateAdapter(10);
        this.uadapter.start();
        this.mevent = new MainEvent();
        this.setItemLayout(1, 1);
    }
    private void setItemLayout(int row, int cell) {
        this.row = row;
        this.cell = cell;
        this.centerPane.removeAll();
        this.uadapter.removeAll();
        this.centerPane.setLayout(new GridLayout(row, cell));
        this.centerPane.updateUI();
        this.updateItem(0);
    }
    private void setItemPage(int index, int count) {
        this.page.setText(String.format("%d/%d", index, count));
    }
    private void updateItem(int index) {
        if (this.wdfHead != null) {
            int sum = this.wdfHead.getWasDataList().length / (this.row * this.cell);
            if (this.wdfHead.getWasDataList().length % (this.row * this.cell) > 0) {
                ++sum;
            }
            if (index >= 0 && index < sum) {
                this.mevent.clear();
                this.centerPane.removeAll();
                this.uadapter.removeAll();
                this.centerPane.setLayout(new GridLayout(this.row, this.cell));
                this.indexItem = index + 1;
                this.setItemPage(this.indexItem, sum);

                for(int i = index * this.row * this.cell; i < (index + 1) * this.row * this.cell && i < this.wdfHead.getWasDataList().length; ++i) {
                    WasData wasData = this.wdfHead.getWasDataList()[i];
                    this.mevent.add(new MainItem(this, this.wdfFile, wasData));
                }

                this.createTip();
            }
        }

    }


    @Override
    public void actionPerformed(ActionEvent e) {
        Object o = e.getSource();
        int i;
        if (o == this.layoutButton) {
            i = this.rowBox.getSelectedIndex() + 1;
            int cell = this.cellBox.getSelectedIndex() + 1;
            this.setItemLayout(i, cell);
        } else if (o == this.upButton) {
            this.updateItem(this.indexItem - 2);
        } else if (o == this.downButton) {
            this.updateItem(this.indexItem);
        } else {
            for(i = 0; i < this.buttons.length; ++i) {
                if (o == this.buttons[i]) {
                    WdfHead wdfHead = WdfTool.getWdfHead(WdfMianUtil.wdfFiles.get(i));
                    if (wdfHead != null) {
                        this.wdfHead = wdfHead;
                        this.wdfFile = WdfMianUtil.wdfFiles.get(i);
                        this.setTitle(title + " - " + this.wdfFile.getName());
                        this.updateItem(0);
                    }
                }
            }
        }
    }

    public void addItemPane(String name, BufferedImage[] bufimages) {
        this.updateTip();
        AnimationPane ap = new AnimationPane(name, bufimages);
        this.uadapter.add(ap);
        this.centerPane.add(new ItemPane(this, name, ap));
        this.centerPane.updateUI();
    }
    private void createTip() {
        this.tip.setText(String.format("正在处理%d个事件", this.mevent.size()));
    }
    private void updateTip() {
        if (this.mevent.size() > 1) {
            this.tip.setText(String.format("正在处理%d个事件", this.mevent.size() - 1));
        } else {
            this.tip.setText("");
        }
    }
}
