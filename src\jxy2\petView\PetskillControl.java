package jxy2.petView;

import jxy2.Config;
import jxy2.guaed.fl.Updatenumericalvalues;
import org.come.action.FromServerAction;
import org.come.bean.enterGameBean;
import org.come.until.GsonUtil;

public class PetskillControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        enterGameBean petsk = GsonUtil.getGsonUtil().getgson().fromJson(mes,enterGameBean.class);
        if (petsk.getResult()!=null) {
            Config.update(petsk.getResult());
            Updatenumericalvalues.Updaten();
        }
    }
}
