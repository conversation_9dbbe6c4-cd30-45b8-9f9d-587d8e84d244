package jxy2.wdfDome.event;

import jxy2.wdfDome.bean.MainInterface;

import java.util.LinkedList;

public class MainEvent implements Runnable{
    private final LinkedList<MainInterface> list = new LinkedList<>();

    public MainEvent() {
        (new Thread(this)).start();
    }

    public void add(MainInterface m) {
        this.list.add(m);
        synchronized(this) {
            this.notify();
        }
    }

    public void clear() {
        this.list.clear();
    }

    public int size() {
        return this.list.size();
    }
    @Override
    public void run() {
        while(true) {
            if (this.list.size() > 0) {
                MainInterface m = this.list.getFirst();
                m.execute();
                this.list.removeFirst();
            } else {
                synchronized(this) {
                    try {
                        this.wait();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
