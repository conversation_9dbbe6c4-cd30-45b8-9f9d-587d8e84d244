package jxy2.refine;

import com.tool.btn.MoBanBtn;
import come.tool.JDialog.TiShiUtil;
import jxy2.jutnil.ImgConstants;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.GsonUtil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;

public class RefNewBtn extends MoBanBtn {
    public RefiningNewEquiJpanel refineJPanel;
    public int typeBtn;
    public RefNewBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, RefiningNewEquiJpanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        Goodstable goodstable = refineJPanel.goods[0];
    switch (typeBtn){
        case 0:
            break;
        case 1:
            //判断是否解锁
            if (Util.isCanBuyOrno()) {return;}
                //TODO 需要重构
                if (refineJPanel.getEquipment()[0].getIcon()!=null&&goodstable.getRefinelv()!=null&& refineJPanel.getEquipment()[2].getIcon()==null&&goodstable.getRefinelv()>=7){
                    OptionsJframe.getOptionsJframe().getOptionsJpanel().showBox(TiShiUtil.confirm,null, "#R您的装备精炼等级相对比较高，确定不加入祝福符？失败装备会消失！");
                    return;
                }

                if (timer != null && timer.isRunning()) {
                    return; // 如果计时器已经在运行，则直接返回，不创建新的计时器
                }
                if (refineJPanel.getEquipment()[0].getIcon() == null|| refineJPanel.getEquipment()[1].getIcon()==null) {
                    currentProgress = 0;
                    if (timer != null) {
                        timer.stop();
                    }
                    ZhuFrame.getZhuJpanel().addPrompt("#G请放入需要精炼的装备或精炼石");
                    return;
                }
                ChantingExample(refineJPanel);
            break;
        case 2://左
            if (goodstable != null) {

                    if (refineJPanel.getPer()>(goodstable.getRefinelv()==null?0:goodstable.getRefinelv())) {
                        refineJPanel.setPer(refineJPanel.getPer() - 1);
                    }
                    if (refineJPanel.getPer()==(goodstable.getRefinelv()==null?0:goodstable.getRefinelv())){
                        refineJPanel.left.setBtn(-1);
                        refineJPanel.left.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz91,17,21,"defaut.wdf"));
                    }
                }
            break;
        case 3://右
            if (goodstable!=null&&refineJPanel.getPer()<15) {
                    refineJPanel.setPer(refineJPanel.getPer() + 1);
                }
                refineJPanel.left.setBtn(1);
                refineJPanel.left.setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz89, "defaut.wdf"));
                break;
         }
    }


    public static int currentProgress = 0;
    public static Timer timer;
    public static void ChantingExample(RefiningNewEquiJpanel refineJPanel) {
        // 初始化进度条
        // 创建一个定时器，每500毫秒触发一次
        timer = new Timer(10, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 每次定时器触发时增加进度
                currentProgress += 1; // 假设每次增加5%
                // 更新进度条
                ZhuFrame.getZhuJpanel().creatSkillChanting(true,currentProgress);
                // 当进度达到100%时停止定时器并执行逻辑
                if (currentProgress >= 100) {
                    ((javax.swing.Timer)e.getSource()).stop();
                    executeLogic(refineJPanel);
                }
            }
        });
        // 启动定时器
        timer.start();
    }
    public static void executeLogic(RefiningNewEquiJpanel refineJPanel) {
        //我现在有3个物品，如果一个物品是石头，一个物品是祝福符，一个物品是装备，现在要把物品信息发送给服务端，如果只有一个物品，再或者者三个物品，如果两个物品，那么就发送两个物品的信息
        currentProgress = 0;
        RefineFrame.getRefineFrame().getRefineJPanel().getCardJpanel().getNewEquiJpanel().setPer(1);
        ZhuFrame.getZhuJpanel().creatSkillChanting(false, 0);
        String serverMes = Agreement.getAgreement().RefineAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(refineJPanel.goods[0])+
                        "&"+GsonUtil.getGsonUtil().getgson().toJson(refineJPanel.goods[1])+
                        "&"+GsonUtil.getGsonUtil().getgson().toJson(refineJPanel.goods[2]));
        SendMessageUntil.toServer(serverMes);
        refineJPanel.ClickGood(null, 24);
    }
}
