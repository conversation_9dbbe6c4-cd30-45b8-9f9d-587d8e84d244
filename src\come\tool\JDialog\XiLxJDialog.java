package come.tool.JDialog;

import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class XiLxJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi) {
            if (20000L > RoleData.getRoleData().getLoginResult().getCodecard().longValue()) {
                ZhuFrame.getZhuJpanel().addPrompt2("你的仙玉不足以支付洗点费用");
                return;
            }
            String sendmes = Agreement.getAgreement().OpenLxLockAgreement("D&" + object);
            SendMessageUntil.toServer(sendmes);
        }
    }
}
