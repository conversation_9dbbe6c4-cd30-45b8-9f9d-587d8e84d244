package jxy2;

import jxy2.setup.AudioSteupMouslisten;
import org.come.mouslisten.SystemMouslisten;
import org.come.test.Main;
import org.come.until.DeviceEshopUntil;
import org.come.until.UserMessUntil;

/**
*
* <AUTHOR>
* @date 2024/4/5 18:32
*/
public class TestGameMain {
    public TestGameMain() {
            DeviceEshopUntil.initEshopUntil(UserMessUntil.getEshops());
            Main.frame.setCursor(null);
            Main.frame.isF12Pressed = true;
            AudioSteupMouslisten.readSysteminit();
            Main.frame.getLoginJpanel().framechange(5,null);
            SystemMouslisten.readSysteminit();
//            Main.frame.dispose(); // 关闭Swing窗口
//            new Thread(() -> new jxy2.lwjgl.GameMainWindow().run()).start(); // 启动LWJGL窗口
    }

}
