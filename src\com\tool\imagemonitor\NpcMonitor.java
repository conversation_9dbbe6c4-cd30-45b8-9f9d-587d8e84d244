package com.tool.imagemonitor;

import java.util.List;

import org.come.Frame.NPCJfram;
import org.come.bean.NpcInfoBean;
import org.come.bean.NpcMenuBean;
import org.come.bean.NpcSureMenuBean;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.ControlNpcXmlUntil;
import org.come.until.MessagrFlagUntil;
import org.come.until.SplitStringTool;

import com.tool.ModerateTask.Hero;
import com.tool.ModerateTask.Task;
import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;

/**
 * NPC监听
 * 
 * <AUTHOR>
 * 
 */
public class NpcMonitor {
	public final static List<String> NPC = SplitStringTool.splitString("5-9|12-18|31|38-40|44-48|50|54|57|58|60|61|66|69|71|77|78");

	/**npc监听*/
	public static void npc(ManimgAttribute attribute) {
		if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)) {
			npc(attribute.getNpc());
		} else if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE3)) {
			// 给与信息
			PlayerMonitor.give(attribute);
		}
	}
	/***/
	public static void npc(NpcInfoBean npc) {
		String[] v = ImageMixDeal.userimg.getTeams();
		int npctype = Integer.parseInt(npc.getNpctable().getNpctype());
		if (npctype == 1002 || npctype == 1107 || npctype==521 ||
				(npctype >= 1101 && npctype <= 1105) || (npctype >= 510 && npctype <= 514) || 
				npctype==2020 || npctype==2022 ||npctype==2023) {
			String sendmes = Agreement.getAgreement().gangmonitor(npc.getNpctable().getNpctype());
			SendMessageUntil.toServer(sendmes);
			return;
		}
		if (v!=null&&v.length>1&&NPC.contains(npc.getNpctable().getNpctype())) {
			// 共享
			String serverMes = Agreement.getAgreement().NPCDialogAgreement(npc.getNpctable().getNpcid());
			SendMessageUntil.toServer(serverMes);
		} else {
			// 直接打开
			ControlNpcXmlUntil.setNpcmenubean(new NpcMenuBean());
			ControlNpcXmlUntil.setSureBean(new NpcSureMenuBean());
			ControlNpcXmlUntil.setType(npc.getNpctable().getNpctype());
			try {
				ControlNpcXmlUntil.GetXmlPath("npcMenu.xml");
			} catch (Exception e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			NPCJfram.getNpcJfram().getNpcjpanel().npc(ControlNpcXmlUntil.getNpcmenubean(), npc);
		}
		Task task=Hero.getHero().PartFinish("问候",npc.getNpctable().getNpcname());
		if (task!=null) {
           	String mes = Agreement.getAgreement().TaskNAgreement("W"+task.getTaskId()+"|"+npc.getNpctable().getNpcname());
			SendMessageUntil.toServer(mes);
		}
	}
	public static void npc(int npctype) {
		if (npctype == 1002 || npctype == 1107 || npctype==521 || (npctype >= 1101 && npctype <= 1105) || (npctype >= 510 && npctype <= 514) || npctype==2020 || npctype==2022 ||npctype==2023) {
			String sendmes = Agreement.getAgreement().gangmonitor(npctype+"");
			SendMessageUntil.toServer(sendmes);
			return;
		}
		ControlNpcXmlUntil.setNpcmenubean(new NpcMenuBean());
		ControlNpcXmlUntil.setSureBean(new NpcSureMenuBean());
		ControlNpcXmlUntil.setType(npctype+"");
		try {
			ControlNpcXmlUntil.GetXmlPath("npcMenu.xml");
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		NPCJfram.getNpcJfram().getNpcjpanel().npc(ControlNpcXmlUntil.getNpcmenubean(),npctype);
	}
}
