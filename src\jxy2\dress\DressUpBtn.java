package jxy2.dress;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import com.tool.role.RoleLingFa;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.starcard.JframeStarCardMain;
import org.come.starcard.JpanelStarCardMain;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
/**
* 右键换装逻辑
* <AUTHOR>
* @date 2024/12/8 下午8:45
*/
public class DressUpBtn extends MoBanBtn {
    public DressUpJPanel dressUpJPanel;
    public DressUpBtn(String iconpath, int type, Color[] colors, Font font, String text, DressUpJPanel dressUpJPanel, int index, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.index = index;
        this.dressUpJPanel = dressUpJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (index==-1)return;
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        StringBuffer buffer = new StringBuffer();
        if ("保存套装".equals(getText())){
            //获取身上的装备写入数据中方便读取……
            for (int i = 0; i < GoodsListFromServerUntil.getChoseGoodsList().length; i++) {
                if (i==13)continue;
                Goodstable good = GoodsListFromServerUntil.getChoseGoodsList()[i];
                if (good!=null) {
                    if (buffer.length() != 0) {buffer.append("|");}
                    buffer.append(i + "_" + good.getRgid());
                }
            }
            if (buffer.length()==0){
                ReadCharacte(loginResult);
                return;
            }
            if (loginResult.getOneclick() == null || loginResult.getOneclick().isEmpty()) {
                loginResult.setOneclick(dressUpJPanel.getType() + "=" + buffer);
                dressUpJPanel.getChoseGoodsJLabelMap().put(dressUpJPanel.getType(),dressUpJPanel.getChoseGoodsJLabel());
            } else {
                // 解析现有的 oneclick 字符串
                String[] pairs = loginResult.getOneclick().split("&");
                StringBuilder updatedOneClick = new StringBuilder();
                boolean found = false;
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=", 2);
                    if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                        // 更新现有键的值
                        keyValue[1] = buffer.toString();
                        found = true;
                    }
                    if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                    updatedOneClick.append(keyValue[0]).append("=").append(keyValue[1]);
                }
                if (!found) {
                    // 如果没有找到键，则添加新的键值对
                    if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                    updatedOneClick.append(dressUpJPanel.getType()).append("=").append(buffer);
                }
                loginResult.setOneclick(updatedOneClick.toString());
                dressUpJPanel.getChoseGoodsJLabelMap().put(dressUpJPanel.getType(),dressUpJPanel.getChoseGoodsJLabel());
            }


            //读取角色修正数据……
            //格式   0=0_738000|1_713000|2_745000|3_875000|5_874000|7_843000|8_846000|9_793000|10_842000
            //读取角色帮派小成数据……
            XcData(loginResult);
            //读取角色帮派大成数据……
            DcData(loginResult);
            //读取角色帮派大成数据……
            //读取角色属性数据……
            Readdata(loginResult);
            //读取角色天演策数据……
            ReadTYCdata(loginResult);
            //读取角色法门数据……
            LawGateData(loginResult);
            //读取角色天罡星盘数据……
            TiangangStarChart(loginResult);
            //读取角色参战孩子数据……

            //读取角色参战星符数据……
            SetStarCard(loginResult);
            //读取角色参战灵宝数据……
            ReadCharacte(loginResult);
            //读取角色参战法宝数据……
            dressUpJPanel.getBlacBuy().setText("取消套装");
            dressUpJPanel.initData(false,dressUpJPanel.getType());
            String mes = Agreement.getAgreement().rolechangeAgreement("8" + GsonUtil.getGsonUtil().getgson().toJson(loginResult));
            SendMessageUntil.toServer(mes);
        } else if ("取消套装".equals(getText())) {
            dressUpJPanel.clear();
        } else {
            dressUpJPanel.setType(index);
            handleButtonClick(index);
            dressUpJPanel.initData(true,dressUpJPanel.getType());
        }

    }
    /**读取法门*/
    private void LawGateData(LoginResult loginResult) {
        StringBuffer buffer_fm = new StringBuffer();
        String[] vs=RoleData.getRoleData().getPrivateData().getSkill("F");
        if (vs==null)return;
        buffer_fm.append(vs[0].substring(0,2));
        if (loginResult.getLawlick()==null || loginResult.getLawlick().isEmpty()){
            loginResult.setLawlick(dressUpJPanel.getType() + "·" + buffer_fm);
        }else {
            // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getLawlick().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("·", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_fm.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("·").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("·").append(buffer_fm);
            }
            loginResult.setLawlick(updatedOneClick.toString());
        }
    }

    /**读取天演策*/
    private void ReadTYCdata(LoginResult loginResult) {
        StringBuffer buffer_tyc = new StringBuffer();
        String[] vs=RoleData.getRoleData().getPrivateData().getSkill("T");
        if (vs==null)return;
        buffer_tyc.append(vs[0]);
        if (loginResult.getTycclick()==null || loginResult.getTycclick().isEmpty()){
            loginResult.setTycclick(dressUpJPanel.getType() + "·" + buffer_tyc);
        }else {
            // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getTycclick().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("·", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_tyc.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("·").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("·").append(buffer_tyc);
            }
            loginResult.setTycclick(updatedOneClick.toString());
        }
    }

    /**读取星盘*/
    private void TiangangStarChart(LoginResult loginResult) {
        StringBuffer buffer_xp = new StringBuffer();
        String[] vs=RoleData.getRoleData().getPrivateData().getSkill("X");
        if (vs==null)return;
        buffer_xp.append(vs[0]);
        if (loginResult.getXpskill()==null || loginResult.getXpskill().isEmpty()){
            loginResult.setXpskill(dressUpJPanel.getType() + "·" + buffer_xp);
        }else {
            // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getXpskill().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("·", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_xp.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("·").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("·").append(buffer_xp);
            }
            loginResult.setXpskill(updatedOneClick.toString());
        }
    }

    /***
     * 帮派大成切换
     * @param loginResult
     */

    private void DcData(LoginResult loginResult) {
        if (loginResult.getResistance().isEmpty())return;
        StringBuffer buffer_dc = new StringBuffer();
        String[] v= loginResult.getResistance().split("\\|");
                for (String s : v){
                    String[] kx = s.split("&");
                    if (kx.length>1) {
                        int ix = Integer.parseInt(kx[0].split("_")[1]);
                        if (kx[1].startsWith("D") && ix == 1) {
                            buffer_dc.append(kx[0]).append(kx[1]);
                            break;
                        }
                    }
                }
    if (loginResult.getDcpractice()==null || loginResult.getDcpractice().isEmpty()){
            loginResult.setDcpractice(dressUpJPanel.getType() + "@" + buffer_dc);
        }else {
        // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getDcpractice().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("@", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_dc.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("@").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("@").append(buffer_dc);
            }
            loginResult.setDcpractice(updatedOneClick.toString());
        }
    }

    /***
     * 帮派小成切换
     * @param loginResult
     */

    private void XcData(LoginResult loginResult) {
        if (loginResult.getResistance().isEmpty())return;
        StringBuffer buffer_xc = new StringBuffer();
       String[] v= loginResult.getResistance().split("\\|");
                for (String s : v){
                    String[] kx = s.split("&");
                    if (kx.length>1) {
                        int ix = Integer.parseInt(kx[0].split("_")[1]);
                        if (kx[1].startsWith("X") && ix == 1) {
                            buffer_xc.append(kx[0]).append(kx[1]);
                            break;
                        }
                    }
                }
        if (loginResult.getXcpractice()==null || loginResult.getXcpractice().isEmpty()){
            loginResult.setXcpractice(dressUpJPanel.getType() + "@" + buffer_xc);
        }else {
        // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getXcpractice().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("@", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_xc.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("@").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("@").append(buffer_xc);
            }
            loginResult.setXcpractice(updatedOneClick.toString());
        }

    }

    /**
     * 读取角色参战星符数据
     * @param loginResult
     */
    private void SetStarCard(LoginResult loginResult) {
        JpanelStarCardMain jpanelStarCardMain = JframeStarCardMain.getJframeSummonEquipMain().getJpanelStarCardMain();
        BigDecimal chooseStarCardId = jpanelStarCardMain.getChooseStarCardId();
        if (chooseStarCardId == null) {return;}
        Goodstable goodstable = GoodsListFromServerUntil.getRgid(chooseStarCardId);
        if (goodstable == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择要培养的星卡");
            return;
        }
        StringBuffer buffer_xk = new StringBuffer();
        if (buffer_xk.length() != 0) {buffer_xk.append("|");}
        buffer_xk.append(goodstable.getRgid());
        if (loginResult.getStatcard()==null|| loginResult.getStatcard().isEmpty()) {
            loginResult.setStatcard(dressUpJPanel.getType() + "=" + buffer_xk);
        }else {
            // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getStatcard().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_xk.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("=").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("=").append(buffer_xk);
            }
            loginResult.setStatcard(updatedOneClick.toString());
        }
    }


    public void handleButtonClick(int clickedIndex) {

        dressUpJPanel.getBtnBasis()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态） 生肖
        for (int i = 0; i <dressUpJPanel.getBtnBasis().length; i++) {
            if (i != clickedIndex) {
                dressUpJPanel.getBtnBasis()[i].btnchange(0);
            }
        }
    }

    public void ReadCharacte(LoginResult loginResult){
        StringBuffer buffer_LB = new StringBuffer();
        for (int i = 0; i < RoleLingFa.getRoleLingFa().equipBao.length; i++) {
            Lingbao lingbao = RoleLingFa.getRoleLingFa().equipBao[i];
            if (lingbao!=null){
                if (buffer_LB.length() != 0) {buffer_LB.append("|");}
                buffer_LB.append(i + "_" + lingbao.getBaoid());
            }
        }
        if (buffer_LB.length()==0)return;
        if (loginResult.getClicklingbao() == null || loginResult.getClicklingbao().isEmpty()) {
            loginResult.setClicklingbao(dressUpJPanel.getType() + "=" + buffer_LB);
            dressUpJPanel.getChoseLingbaosJLabelMap().put(dressUpJPanel.getType(),dressUpJPanel.getChoselingbaosJLabel());
        } else {
            // 解析现有的 oneclick 字符串
            String[] pairs = loginResult.getClicklingbao().split("&");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")) {
                    // 更新现有键的值
                    keyValue[1] = buffer_LB.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(keyValue[0]).append("=").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("&");}
                updatedOneClick.append(dressUpJPanel.getType()).append("=").append(buffer_LB);
            }
            loginResult.setClicklingbao(updatedOneClick.toString());
            dressUpJPanel.getChoseLingbaosJLabelMap().put(dressUpJPanel.getType(),dressUpJPanel.getChoselingbaosJLabel());
        }
    }
    /**保存属性*/
    public void Readdata(LoginResult loginResult){
        StringBuffer buffer_sx = new StringBuffer();
        buffer_sx.append(loginResult.getSpoint());
        if (loginResult.getStoresa()==null || loginResult.getStoresa().isEmpty()){
            loginResult.setStoresa(dressUpJPanel.getType()+"·"+buffer_sx);
        }else {
            String[] pairs = loginResult.getStoresa().split("@");
            StringBuilder updatedOneClick = new StringBuilder();
            boolean found = false;
            for (String pair : pairs) {
                String[] keyValue = pair.split("·",2);
                if (keyValue[0].equals(dressUpJPanel.getType()+"")){
                    // 更新现有键的值
                    keyValue[1] = buffer_sx.toString();
                    found = true;
                }
                if (updatedOneClick.length() != 0) {updatedOneClick.append("@");}
                updatedOneClick.append(keyValue[0]).append("·").append(keyValue[1]);
            }
            if (!found) {
                // 如果没有找到键，则添加新的键值对
                if (updatedOneClick.length() != 0) {updatedOneClick.append("@");}
                updatedOneClick.append(dressUpJPanel.getType()).append("·").append(buffer_sx);
            }
            loginResult.setStoresa(updatedOneClick.toString());
        }
    }
}
