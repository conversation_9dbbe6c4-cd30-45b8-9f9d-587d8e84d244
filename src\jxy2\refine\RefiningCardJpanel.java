package jxy2.refine;

import javax.swing.*;
import java.awt.*;

public class RefiningCardJpanel extends JPanel {
    private CardLayout car;
    //装备精炼面板
    public RefiningNewEquiJpanel newEquiJpanel;
    //装备炼化面板
    private EqartificeJapanel eqartifice;
    //装备打造面板
    private EqmMakeJpanel eqmMakeJpanel;
    //装备炼器面板
    private ReimpJpanel reimpJpanel;
    //装备套装面板
    private EqsuitJpanel eqsuitJpanel;
    //套装收录面板
    private CollectionJpanel collectionJanel;
    //符石合成
    private SymbolJpanel symbolJpanel;
    //神兵操作
    private GodJpanel godJpanel;
    //宝石
    private GemsTJpanel gemsTJpanel;

    public RefiningCardJpanel() {
        car = new CardLayout();
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(car);
        this.setOpaque(false);
        this.add(newEquiJpanel = new RefiningNewEquiJpanel(), "l1");
        this.add(eqartifice = new EqartificeJapanel(), "l2");
        this.add(eqmMakeJpanel = new EqmMakeJpanel(), "l3");
        this.add(reimpJpanel = new ReimpJpanel(), "l4");
        this.add(eqsuitJpanel = new EqsuitJpanel(), "l5");
        this.add(collectionJanel = new CollectionJpanel(), "l6");
        this.add(symbolJpanel = new SymbolJpanel(), "l7");
        this.add(godJpanel = new GodJpanel(), "l8");
        this.add(gemsTJpanel = new GemsTJpanel(), "l9");

    }

    public EqmMakeJpanel getEqmMakeJpanel() {
        return eqmMakeJpanel;
    }

    public void setEqmMakeJpanel(EqmMakeJpanel eqmMakeJpanel) {
        this.eqmMakeJpanel = eqmMakeJpanel;
    }

    public CardLayout getCar() {
        return car;
    }

    public void setCar(CardLayout car) {
        this.car = car;
    }

    public RefiningNewEquiJpanel getNewEquiJpanel() {
        return newEquiJpanel;
    }

    public void setNewEquiJpanel(RefiningNewEquiJpanel newEquiJpanel) {
        this.newEquiJpanel = newEquiJpanel;
    }

    public EqartificeJapanel getEqartifice() {
        return eqartifice;
    }

    public void setEqartifice(EqartificeJapanel eqartifice) {
        this.eqartifice = eqartifice;
    }

    public ReimpJpanel getReimpJpanel() {
        return reimpJpanel;
    }

    public void setReimpJpanel(ReimpJpanel reimpJpanel) {
        this.reimpJpanel = reimpJpanel;
    }

    public EqsuitJpanel getEqsuitJpanel() {
        return eqsuitJpanel;
    }

    public void setEqsuitJpanel(EqsuitJpanel eqsuitJpanel) {
        this.eqsuitJpanel = eqsuitJpanel;
    }

    public CollectionJpanel getCollectionJanel() {
        return collectionJanel;
    }

    public void setCollectionJanel(CollectionJpanel collectionJanel) {
        this.collectionJanel = collectionJanel;
    }

    public SymbolJpanel getSymbolJpanel() {
        return symbolJpanel;
    }

    public void setSymbolJpanel(SymbolJpanel symbolJpanel) {
        this.symbolJpanel = symbolJpanel;
    }

    public GodJpanel getGodJpanel() {
        return godJpanel;
    }

    public void setGodJpanel(GodJpanel godJpanel) {
        this.godJpanel = godJpanel;
    }

    public GemsTJpanel getGemsTJpanel() {
        return gemsTJpanel;
    }

    public void setGemsTJpanel(GemsTJpanel gemsTJpanel) {
        this.gemsTJpanel = gemsTJpanel;
    }
}
