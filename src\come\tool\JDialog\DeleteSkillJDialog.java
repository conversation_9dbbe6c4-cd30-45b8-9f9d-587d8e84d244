package come.tool.JDialog;

import jxy2.jutnil.Juitil;
import jxy2.supet.JupoDanMouse;
import jxy2.supet.SipetUtil;
import org.come.Frame.PetSkillsJframe;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.bean.PetOperationDTO;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.util.Arrays;

public class DeleteSkillJDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {

        if (tishi) {
            PetSkillsJpanel petSkillsJpanel = PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel();
            RoleSummoning pet = UserMessUntil.getChosePetMes();
            // 发送协议代码保持不变...
            String sendmes = Agreement.getAgreement().userpetAgreement(
                    "PS|" + pet.getSid() + "|" + petSkillsJpanel.getPetskillID());
            SendMessageUntil.toServer(sendmes);
            //检测是否存在悟灵技能，则默认关闭
            boolean containsId = Arrays.stream(SipetUtil.lingfan).anyMatch(id -> id == Integer.parseInt(petSkillsJpanel.getPetskillID()));
                if (containsId){
                    PetOperationDTO dto = new PetOperationDTO();
                    dto.setPetId(pet.getSid());
                    dto.setOperationType("PET_OPEN_WL");
                    dto.setEventType(petSkillsJpanel.getPetskillID());
                    dto.setOpenClose("0");
                    SendRoleAndRolesummingUntil.sendRoleSumming(dto);
                }
            // 获取当前操作的位置索引
            int deletedIndex = petSkillsJpanel.getPetskillNO();
            // 1. 清除数据源
            petSkillsJpanel.getShowPetSkills()[deletedIndex].setSkill(null);
            petSkillsJpanel.getLabPetskills()[deletedIndex].setIcon(null);

            // 2. 重新排列剩余技能
            for (int i = deletedIndex; i < petSkillsJpanel.getShowPetSkills().length - 1; i++) {
                // 将后续技能前移
                Skill nextSkill = petSkillsJpanel.getShowPetSkills()[i + 1].getSkill();
                if (nextSkill != null) {
                    // 更新数据源
                    petSkillsJpanel.getShowPetSkills()[i].setSkill(nextSkill);
                    // 更新图标显示
                    ImageIcon icon = CutButtonImage.getWdfPng("0x6B6B" + nextSkill.getSkillid(), 47, 46, "skill.wdf");
                    petSkillsJpanel.getLabPetskills()[i].setIcon(new ImageIcon(
                            Juitil.toRoundedCornerImage(icon.getImage(), 10)
                    ));
                } else {
                    // 清空后续空位
                    petSkillsJpanel.getLabPetskills()[i].setIcon(null);
                    petSkillsJpanel.getLabPetskills()[i].setVisible(false);
                }
            }

            // 3. 处理最后一个位置
            int lastIndex = petSkillsJpanel.getShowPetSkills().length - 1;
            petSkillsJpanel.getLabPetskills()[lastIndex].setIcon(null);
            petSkillsJpanel.getLabPetskills()[lastIndex].setVisible(false);
            petSkillsJpanel.getShowPetSkills()[lastIndex].setSkill(null);

            // 4. 重置临时数据
            petSkillsJpanel.setPetskillID("");
            PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getMapPetModelPanel().get(0).getBox().setText(null);
            if (FormsManagement.getframe(145).isVisible()){
                JupoDanMouse.refreshPetSkills(UserMessUntil.getChosePetMes());
            }
        }
    }

}
