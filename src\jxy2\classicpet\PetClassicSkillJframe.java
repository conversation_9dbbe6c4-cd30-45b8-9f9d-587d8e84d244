package jxy2.classicpet;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

/**
* 宠物经典技能
* <AUTHOR>
* @date 2024/9/12 下午9:02
*/
public class PetClassicSkillJframe  extends JInternalFrame implements MouseListener {
    private PetClassicSkillJPanel petClassicSkillJPanel;
    private int first_x, first_y;// x、y坐标
    public static PetClassicSkillJframe getPetClassicSkillJframe() {
        return (PetClassicSkillJframe) FormsManagement.getInternalForm(126).getFrame();
    }

    public PetClassicSkillJframe() {
        super();
        petClassicSkillJPanel = new PetClassicSkillJPanel();
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        ((BasicInternalFrameUI) this.getUI()).setNorthPane(null);// 去除顶部的边框
        this.setBounds(344, 118, 370,354);
        this.setBackground(UIUtils.Color_BACK);
        this.add(petClassicSkillJPanel);
        this.pack();

        this.addMouseMotionListener(new MouseMotionListener() {// 判断窗口移动的位置

            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(), getWidth(), getHeight());
                }
            }
        });
        this.addMouseListener(this);

    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // 开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
         boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击
            FormsManagement.HideForm(126);

        } else {
            FormsManagement.Switchinglevel(126);
        }
        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public PetClassicSkillJPanel getPetClassicSkillJPanel() {
        return petClassicSkillJPanel;
    }

    public void setPetClassicSkillJPanel(PetClassicSkillJPanel petClassicSkillJPanel) {
        this.petClassicSkillJPanel = petClassicSkillJPanel;
    }
}
