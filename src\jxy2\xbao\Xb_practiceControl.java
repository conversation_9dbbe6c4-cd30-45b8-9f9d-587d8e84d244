package jxy2.xbao;

import org.come.action.FromServerAction;
import org.come.until.GsonUtil;

public class Xb_practiceControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        Xuanbao xuanbao = GsonUtil.getGsonUtil().getgson().fromJson(mes, Xuanbao.class);
        if (xuanbao==null)return;
        RoleXuanbao.getRoleXuanbao().choseBao = xuanbao;
        XbPracticeFrame.getXbPracticeFrame().getXbPracticeJPanel().initialization(xuanbao);
        XbaoFrame.getXbaoFrame().getXbaoJpanel().setAttributeVisible(true);
        XbaoFrame.getXbaoFrame().getXbaoJpanel().LoadingOfSelected(xuanbao);
    }
}
