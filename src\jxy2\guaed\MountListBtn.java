package jxy2.guaed;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class MountListBtn extends MoBanBtn {
    public int index;
    private MountListJPanel mountListJPanel;
    public MountListBtn(String iconpath, int type,String text,MountListJPanel mountListJPanel,int index ) {
        super(iconpath, type , UIUtils.COLOR_BTNPUTONG);
        // TODO Auto-generated constructor stub
        setText(text);
        setFont(UIUtils.TEXT_HY16);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.mountListJPanel=mountListJPanel;
        this.index=index;
    }
    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }
    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }
    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        if ((300000) > RoleData.getRoleData().getLoginResult().getGold().longValue()) {
            ZhuFrame.getZhuJpanel().addPrompt2("你金钱不够手续费");
            return;
        }
        OptionsJframe.getOptionsJframe().getOptionsJpanel().
                showBox(TiShiUtil.Inlaidmounts,mountListJPanel.getId(), "#W坐骑移入守护槽后，可#G获得守护石加持#W。坐骑移入守护槽需要话费#M300000#W银两（优先使用绑玉），确认选择此坐骑吗？");
        FormsManagement.HideForm(140);
    }


}
