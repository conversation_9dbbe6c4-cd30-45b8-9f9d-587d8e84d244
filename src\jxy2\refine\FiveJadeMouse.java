package jxy2.refine;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SynthesisJpanel;
import org.come.mouslisten.StorageJadeMouslisten;
import org.come.until.AccessSuitMsgUntil;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;

public class Five<PERSON>adeMouse implements MouseListener {
    public EqsuitJpanel refineJPanel;
    public int index;
    private int[] jadeNum = new int[5]; // 存放玉符的数量
    public FiveJadeMouse(EqsuitJpanel refineJPanel,int index) {
        this.refineJPanel = refineJPanel;
        this.index = index;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        ImageIcon icon=  CutButtonImage.getWdfPng("0x6FEB8"+ (666+index),49,49,"defaut.wdf");
        switch (refineJPanel.getMinType()){
            case 0:
                if (refineJPanel.partJade != null) {
                    jadeNum[0] = refineJPanel.partJade.getJade1();
                    jadeNum[1] = refineJPanel.partJade.getJade2();
                    jadeNum[2] = refineJPanel.partJade.getJade3();
                    jadeNum[3] = refineJPanel.partJade.getJade4();
                    jadeNum[4] = refineJPanel.partJade.getJade5();

                    if (jadeNum[index] > 0) {// 表示有玉符
                        // 获取要合成的玉符
                        EqsuitJpanel.getGoodstableBean().setPartJade(refineJPanel.partJade);
                        SynthesisJpanel.getGoodstableBean().setType((index + 1));
                        // 展示玉符的照片
                        refineJPanel.getTwoEquipment()[1].setIcon(icon);
                        refineJPanel.ClickSuit(null,1);
                        BigDecimal big = AccessSuitMsgUntil.returnMoney(EqsuitJpanel.getGoodstableBean(), 1);
                        if (big != null) {// 消耗的金钱
                            EqsuitJpanel.setMoney(big);
                        }
                    }
                }
                break;
            case 3:
                if (refineJPanel.partJade != null) {
                    jadeNum[0] = refineJPanel.partJade.getJade1();
                    jadeNum[1] = refineJPanel.partJade.getJade2();
                    jadeNum[2] = refineJPanel.partJade.getJade3();
                    jadeNum[3] = refineJPanel.partJade.getJade4();
                    jadeNum[4] = refineJPanel.partJade.getJade5();

                    if (jadeNum[index] > 0) {// 表示有玉符
                        // 展示玉符的照片
                        refineJPanel.getLabyf().setIcon(icon);
                        refineJPanel.getLabpz().setText(AccessSuitMsgUntil.returnJadeName((index + 1)));
                        refineJPanel.getLabgs().setText(jadeNum[index] + "");
                        if (AccessSuitMsgUntil.returnJadeMoney((index + 1)) != null) {
                            EqsuitJpanel.setMoney(AccessSuitMsgUntil.returnJadeMoney((index + 1)));
                        }
                        int num = AccessSuitMsgUntil.returnJadeNum((index + 1));
                        if (num != 0) {
                            EqsuitJpanel.setNumber((jadeNum[index] - num) >= 0 ? 0 : (num - jadeNum[index]));// 还需要几个玉符
                            EqsuitJpanel.getGoodstableBean().setPartJade(refineJPanel.partJade);
                            EqsuitJpanel.getGoodstableBean().setType((index + 1));
                            refineJPanel.getWorkshopBtn().setText("升级");
                        } else {
                            EqsuitJpanel.setNumber(-1);
                        }
                    }
                }
                break;
            case 5:
                if (refineJPanel.partJade != null) {
                    jadeNum[0] = refineJPanel.partJade.getJade1();
                    jadeNum[1] = refineJPanel.partJade.getJade2();
                    jadeNum[2] = refineJPanel.partJade.getJade3();
                    jadeNum[3] = refineJPanel.partJade.getJade4();
                    jadeNum[4] = refineJPanel.partJade.getJade5();

                    if (jadeNum[index] > 0) {// 表示有玉符
                        // 展示面板信息
                        refineJPanel.getLabTzyf().setIcon(icon);
                        // 默认兑换数量是一
                        refineJPanel.getTextNum().setText("1");
                        // 可以兑换的灵修值
                        EqsuitJpanel.khdlxz = new BigDecimal(AccessSuitMsgUntil.returnExcNum((index + 1)));
                        // 存放要兑换的玉符
                        EqsuitJpanel.getGoodstableBean().setType((index + 1));
                        EqsuitJpanel.getGoodstableBean().setGoodstable(null);
                        EqsuitJpanel.getGoodstableBean().setPartJade(refineJPanel.partJade);
                        refineJPanel.getWorkshopBtn().setText("兑换");
                    } else {
                        // 展示面板信息
                        refineJPanel.getLabTzyf().setIcon(null);
                        // 默认兑换数量是一
                        refineJPanel.getTextNum().setText("");
                        // 可以兑换的灵修值
                        EqsuitJpanel.khdlxz = null;
                        // 存放要兑换的玉符
                        EqsuitJpanel.getGoodstableBean().setType(0);
                        EqsuitJpanel.getGoodstableBean().setGoodstable(null);
                        EqsuitJpanel.getGoodstableBean().setPartJade(null);
                    }
                }
                break;
                case 6:
                    if (refineJPanel.partJade != null) {
                        jadeNum[0] = refineJPanel.partJade.getJade1();
                        jadeNum[1] = refineJPanel.partJade.getJade2();
                        jadeNum[2] = refineJPanel.partJade.getJade3();
                        jadeNum[3] = refineJPanel.partJade.getJade4();
                        jadeNum[4] = refineJPanel.partJade.getJade5();

                        if (jadeNum[index] > 0) {// 表示有玉符
                            // 展示面板信息
                            refineJPanel.getLabTzcoll().setIcon(icon);
                            // 这个套装所收录的部件数量
                            int num = AccessSuitMsgUntil.getCollNum(refineJPanel.partJade.getSuitid());
                            // 所需灵修值
                            EqsuitJpanel.setSxlxz(new BigDecimal(50));
                            // 所需金钱
                            EqsuitJpanel.setMoney(new BigDecimal((num + 1) * 10000000));
                            // 存放要兑换的玉符
                            EqsuitJpanel.getGoodstableBean().setType((index + 1));
                            EqsuitJpanel.getGoodstableBean().setPartJade(refineJPanel.partJade);
                            refineJPanel.getWorkshopBtn().setText("收录");

                        } else {
                            refineJPanel.getLabTzcoll().setIcon(null);
                            EqsuitJpanel.setMoney(null);
                            EqsuitJpanel.setSxlxz(null);
                            EqsuitJpanel.getGoodstableBean().setType(0);
                            EqsuitJpanel.getGoodstableBean().setPartJade(null);
                        }
                    }
                    break;


        }

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        StorageJadeMouslisten.showMsg(index);
    }

    @Override
    public void mouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
    }

}
