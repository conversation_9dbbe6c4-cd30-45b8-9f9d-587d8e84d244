package jxy2.flight;
import java.math.BigDecimal;
/**
 * ClassName:飞行器表 2025年重构
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/7/1 22:07
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class Fly {
    //rdies表ID
    private BigDecimal flyid;
    //角色ID
    private BigDecimal roleId;
    //飞行器名字
    private String flyname;
    //飞行器阶级
    private int flysteps;
    //飞行器等级
    private int flylv;
    //飞行器经验
    private int flyexe;
    //飞行器速度
    private double flysp;
    //飞行器消耗
    private int flyconsume;
    //飞行器燃灵值
    private int flyBsv;
    //飞行器已装备
    private int flyEquip;

    public BigDecimal getFlyid() {
        return flyid;
    }

    public void setFlyid(BigDecimal flyid) {
        this.flyid = flyid;
    }

    public BigDecimal getRoleId() {
        return roleId;
    }

    public void setRoleId(BigDecimal roleId) {
        this.roleId = roleId;
    }

    public String getFlyname() {
        return flyname;
    }

    public void setFlyname(String flyname) {
        this.flyname = flyname;
    }

    public int getFlysteps() {
        return flysteps;
    }

    public void setFlysteps(int flysteps) {
        this.flysteps = flysteps;
    }

    public int getFlylv() {
        return flylv;
    }

    public void setFlylv(int flylv) {
        this.flylv = flylv;
    }

    public int getFlyexe() {
        return flyexe;
    }

    public void setFlyexe(int flyexe) {
        this.flyexe = flyexe;
    }

    public double getFlysp() {
        return flysp;
    }

    public void setFlysp(double flysp) {
        this.flysp = flysp;
    }

    public int getFlyconsume() {
        return flyconsume;
    }

    public void setFlyconsume(int flyconsume) {
        this.flyconsume = flyconsume;
    }

    public int getFlyBsv() {
        return flyBsv;
    }

    public void setFlyBsv(int flyBsv) {
        this.flyBsv = flyBsv;
    }

    public int getFlyEquip() {
        return flyEquip;
    }

    public void setFlyEquip(int flyEquip) {
        this.flyEquip = flyEquip;
    }
}
