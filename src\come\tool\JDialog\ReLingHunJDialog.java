package come.tool.JDialog;

import jxy2.zodiac.ConversionBean;
import jxy2.zodiac.RStarSoulsJPanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

public class ReLingHunJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            RStarSoulsJPanel rStarSoulsJPanel = (RStarSoulsJPanel) object;
            rStarSoulsJPanel.setXhGoods(null);
            if (!rStarSoulsJPanel.getGoodsrgid().isEmpty()) {
                ConversionBean conversionBean = new ConversionBean();
                conversionBean.setGoodsrgid(rStarSoulsJPanel.getGoodsrgid());
                // 发送给服务器
                String msg = Agreement.getAgreement().ActivateSSAgreement("C" + GsonUtil.getGsonUtil().getgson().toJson(conversionBean));
                // 向服务器发送信息
                SendMessageUntil.toServer(msg);
                //转换结束直接清空MAP
                rStarSoulsJPanel.getGoodsrgid().clear();
                rStarSoulsJPanel.firstLv =null;
            }
        }

    }
}
