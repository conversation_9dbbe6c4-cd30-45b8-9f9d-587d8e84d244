package jxy2.jutnil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

public class SecureImgGenerator {

    private static final int FILE_HEADER_MAGIC_NUMBER = 0x47494D53; // 'GIMS'
    private static final int FILE_HEADER_VERSION = 1;
    private static final int FILE_HEADER_RESERVED_SPACE = 16;

    private static final int INDEX_ENTRY_NAME_LENGTH = 64;
    private static final int INDEX_ENTRY_RESERVED_SPACE = 16;

    public static void main(String[] args) throws Exception {
        String inputDir = "path/img"; // Replace with your local directory containing PNG files
        String outputFile = "imgbak.secureimg";

        SecureImgGenerator generator = new SecureImgGenerator();
        generator.createSecureImgFile(inputDir, outputFile);
    }

    public void createSecureImgFile(String inputDir, String outputFile) throws Exception {
        Map<String, UnencryptedImageInfo> unencryptedImages = new HashMap<>();

        try (FileOutputStream fos = new FileOutputStream(outputFile);
             FileChannel outChannel = fos.getChannel()) {

            // 1. 收集本地文件夹中的图像资源信息
            collectImageInfoFromDirectory(inputDir, unencryptedImages);

            // 2. 计算文件头和索引表大小
            int indexTableSize = indexTableSize(unencryptedImages);
            int fileSizeWithoutChecksum = FILE_HEADER_RESERVED_SPACE + indexTableSize + totalImageDataSize(unencryptedImages);

            // 3. 写入文件头
            ByteBuffer headerBuffer = ByteBuffer.allocate(FILE_HEADER_RESERVED_SPACE);
            populateFileHeader(headerBuffer, unencryptedImages.size(), fileSizeWithoutChecksum);
            writeBufferToChannel(outChannel, headerBuffer);

            // 4. 写入索引表
            ByteBuffer indexTableBuffer = ByteBuffer.allocate(indexTableSize);
            populateIndexTable(indexTableBuffer, unencryptedImages);
            writeBufferToChannel(outChannel, indexTableBuffer);

            // 5. 写入未加密数据块
            for (UnencryptedImageInfo imageInfo : unencryptedImages.values()) {
                writeBufferToChannel(outChannel, ByteBuffer.wrap(imageInfo.getImageData()));
            }
        }
    }
    private void collectImageInfoFromDirectory(String inputDir, Map<String, UnencryptedImageInfo> unencryptedImages) throws IOException {
    File directory = new File(inputDir);
    File[] pngFiles = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".png"));

    if (pngFiles != null) {
        for (File pngFile : pngFiles) {
            try (FileInputStream fis = new FileInputStream(pngFile)) {
                byte[] imageData = new byte[(int) pngFile.length()];
                fis.read(imageData);
                unencryptedImages.put(pngFile.getName(), new UnencryptedImageInfo(unencryptedImages.size(), imageData));
            }
        }
    }
}


    private int indexTableSize(Map<String, UnencryptedImageInfo> unencryptedImages) {
        return unencryptedImages.size() * (INDEX_ENTRY_NAME_LENGTH + Integer.BYTES * 2 + INDEX_ENTRY_RESERVED_SPACE);
    }

    private int totalImageDataSize(Map<String, UnencryptedImageInfo> unencryptedImages) {
        return unencryptedImages.values().stream().mapToInt(UnencryptedImageInfo::getImageDataLength).sum();
    }

    private void populateFileHeader(ByteBuffer buffer, int indexEntryCount, int fileSizeWithoutChecksum) {
        buffer.putInt(FILE_HEADER_MAGIC_NUMBER);
        buffer.putInt(FILE_HEADER_VERSION);
        buffer.putInt(fileSizeWithoutChecksum);
        buffer.position(buffer.position() + FILE_HEADER_RESERVED_SPACE - Integer.BYTES * 3); // Fill reserved space with zeros
    }

    private void populateIndexTable(ByteBuffer buffer, Map<String, UnencryptedImageInfo> unencryptedImages) {
        for (Map.Entry<String, UnencryptedImageInfo> entry : unencryptedImages.entrySet()) {
            String imageName = entry.getKey();
            UnencryptedImageInfo imageInfo = entry.getValue();
            // Write image name (truncated to 64 bytes)
            byte[] imageNameBytes = imageName.getBytes(StandardCharsets.UTF_8);
            int nameLength = Math.min(INDEX_ENTRY_NAME_LENGTH, imageNameBytes.length);
            buffer.put(imageNameBytes, 0, nameLength);
            buffer.position(buffer.position() + INDEX_ENTRY_NAME_LENGTH - nameLength); // Fill remaining name space with zeros
            // Write offset and size
            buffer.putInt(imageInfo.getOffset());
            buffer.putInt(imageInfo.getImageDataLength());
            // Fill reserved space with zeros
            buffer.position(buffer.position() + INDEX_ENTRY_RESERVED_SPACE);

        }
    }

    private void writeBufferToChannel(FileChannel channel, ByteBuffer buffer) throws IOException {
        buffer.flip();
        while (buffer.hasRemaining()) {
            channel.write(buffer);
        }
        buffer.clear();
    }

    private void writeChecksum(FileChannel channel, String filePath, int fileSizeWithoutChecksum) throws NoSuchAlgorithmException, IOException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        FileChannel inChannel = new FileInputStream(filePath).getChannel();
        ByteBuffer checksumBuffer = ByteBuffer.allocate(md.getDigestLength());

        long position = inChannel.position();
        inChannel.position(0);
        do {
            int bytesRead = inChannel.read(checksumBuffer);
            if (bytesRead == -1) {
                break;
            }
            md.update(checksumBuffer.array(), 0, bytesRead);
            checksumBuffer.clear();
        } while (inChannel.position() < fileSizeWithoutChecksum);

        inChannel.position(position);
        byte[] checksum = md.digest();
        writeBufferToChannel(channel, ByteBuffer.wrap(checksum));
    }
}

class UnencryptedImageInfo {
    private int offset;
    private byte[] imageData;
    private int imageDataLength;

    public UnencryptedImageInfo(int offset, byte[] imageData) {
        this.offset = offset;
        this.imageData = imageData;
        this.imageDataLength = imageData.length;
    }

    public int getOffset() {
        return offset;
    }

    public byte[] getImageData() {
        return imageData;
    }

    public int getImageDataLength() {
        return imageDataLength;
    }
}
