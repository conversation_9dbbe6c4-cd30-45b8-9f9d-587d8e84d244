package jxy2.soul;

import jxy2.jutnil.Juitil;
import org.come.action.FromServerAction;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
/**
* 重置魔晶技能服务器返回物品协议(作废)
* <AUTHOR>
* @date 2024/7/11 上午7:07
*/

public class ResetSkillControl  implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        Goodstable goodstable = GsonUtil.getGsonUtil().getgson().fromJson(mes,Goodstable.class);
//        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
//        synthesisJPanl.A_isVisible();
    }

    public static void ReserGoodsv(Goodstable goodstable) {

        ResetSoulSkillJPanl  resetSoulSkillJPanl = ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl();
        String[] skillID = goodstable.getValue().split("\\|");
        for (int i = 0; i < skillID.length; i++) {
            Skill skill = UserMessUntil.getSkillId(skillID[i]);
            ImageIcon icon = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(), 44, 44, "skill.wdf");
            Image img = Juitil.toRoundedCornerImage(icon.getImage(), 15);
            if (i == 0) {
                resetSoulSkillJPanl.goods_1.setIcon(new ImageIcon(img));
                resetSoulSkillJPanl.goods_1.setVisible(true); // 确保 goods_1 可见
                resetSoulSkillJPanl.type = i;
                resetSoulSkillJPanl.goods_1.setBounds(305,61,44,44);
            }
            if (i == 1) {
                resetSoulSkillJPanl.goods_2.setIcon(new ImageIcon(img));
                resetSoulSkillJPanl.goods_2.setVisible(true); // 确保 goods_2 可见
                resetSoulSkillJPanl.type = i;
                resetSoulSkillJPanl.goods_2.setBounds(375,61,44,44);
            }
            // 如果 golang 只有一个值，隐藏 goods_2
            if (skillID.length == 1) {
                resetSoulSkillJPanl.goods_2.setVisible(false);
                resetSoulSkillJPanl.type = -1;
                resetSoulSkillJPanl.goods_1.setBounds(345,61,44,44);
            }
        }

        ResetModeSkills resetModeSkills1 = resetSoulSkillJPanl.getMapResistanceModelPanel().get(resetSoulSkillJPanl.xz);//这个值取决去于当前选中的索引
       if (resetModeSkills1!=null)
        resetModeSkills1.initSkillInfo(goodstable);
    }
}
