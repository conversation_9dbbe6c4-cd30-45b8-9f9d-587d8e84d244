package jxy2.guaed.fl;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.Config;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.Goodstable;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 守护石融炼界面更新数值
* <AUTHOR>
* @date 2024/12/24 上午9:46
*/
public class RonglianJPanel extends JPanel implements IValueUpdateListener {
    public RonglianBtn oneclickupbtn;
    public RonglianBtn[] plus = new RonglianBtn[2];
    private JLabel[] addGoodsImg = new JLabel[2];
    private JLabel[] exerciseLevel = new JLabel[2];
    private Goodstable[] goodstables = new Goodstable[2];
    private JLabel[] imgback = new JLabel[7];
    private JLabel[] newtext = new JLabel[7];
    private int type;
    public RonglianJPanel() {
        this.setPreferredSize(new Dimension(633, 507));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        oneclickupbtn =new RonglianBtn(ImgConstants.tz85, 1,  1, "开始融炼", this,"");
        oneclickupbtn.setBounds(520, 446, 82,29);
        add(oneclickupbtn);

        for (int i = 0; i < plus.length; i++) {
            plus[i] = new RonglianBtn(ImgConstants.tz310, 1, i+2, "", this, "");
            plus[i].setBounds(85, 184, 20, 20);
            this.add(plus[i]);
        }

        for (int i = 0; i < addGoodsImg.length ; i++) {
            addGoodsImg[i] = new JLabel();
            addGoodsImg[i].setBounds(70+i*318, 81, 52, 52);
            addGoodsImg[i].addMouseListener(new RonglianMouse(this,i,0));
            this.add(addGoodsImg[i]);
        }

        for (int i = 0; i < exerciseLevel.length ; i++) {
            exerciseLevel[i] = new JLabel();
            exerciseLevel[i].setBounds((156 - 18) + i * 318, 114, 52, 22);
            this.add(exerciseLevel[i]);
        }
        for (int i = 0; i < imgback.length; i++) {
            imgback[i] = new JLabel();
            if (i>=5){
                imgback[i].setBounds(352, 200+i*24, 32, 32);
            }else {
                imgback[i].setBounds(352, 164+i*24, 32, 32);
            }
            imgback[i].setVisible(false);
            imgback[i].setIcon(Juitil.tz325);
            this.add(imgback[i]);

        }
        for (int i = 0; i < newtext.length; i++) {
            newtext[i] = TeststateJpanel.GJpanelText(UIUtils.COLOR_zhi, UIUtils.TEXT_FONT1);
            if (i>=5){
                newtext[i].setBounds(284, 200+i*24, 60, 32);
            }else {
                newtext[i].setBounds(284, 164+i*24, 60, 32);
            }
            newtext[i].setVisible(false);
            this.add(newtext[i]);

        }
        // 注册为监听器
        Updatenumericalvalues.getInstance().addListener(this);
    }

    public void setComponentVisible(boolean b, ImageIcon icon) {
        plus[0].setVisible(b);
        addGoodsImg[0].setIcon(icon);
    }

    public void setComponentVisibleTow(boolean b, ImageIcon icon) {
        plus[1].setVisible(b);
        addGoodsImg[1].setIcon(icon);
    }

    public String[] monuy = {"消耗金钱","拥有金钱","消耗守护之尘","拥有守护之尘"};
    private BigDecimal SpendGuardianMoney = new BigDecimal(Config.getInt("SpendGuardianMoney_Value"));
    private BigDecimal GuardianDust = new BigDecimal(Config.getInt("SpendGuardianMoney_Value2"));
    
    @Override
    public void onValueUpdate() {
        // 实时更新数值
        this.GuardianDust = new BigDecimal(Config.getInt("SpendGuardianMoney_Value2"));
        this.SpendGuardianMoney = new BigDecimal(Config.getInt("SpendGuardianMoney_Value"));
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        for (int i = 0; i < monuy.length; i++) {
            int x = i % 2;
            int y = i / 2;
            Juitil.TextBackground(g, monuy[i], 16, 23+x*240, 425 + y * 36, UIUtils.COLOR_goods_quantity, UIUtils.NEWTX_HY17B, UIUtils.Color_BACK);
            Juitil.ImngBack(g, Juitil.tz128, 140+x*245, 426 + y * 36, 121, 22, 1);
        }
        BigDecimal money = RoleData.getRoleData().getLoginResult().getGold();
        Util.drawPrice(g, money, 388, 442, UIUtils.LiSu_LS16);
        Util.drawPrice(g, this.SpendGuardianMoney, 147, 442, UIUtils.LiSu_LS16);

        Juitil.TextBackground(g, GuardianDust.toString(), 16, 144 , 442+20, UIUtils.COLOR_Draw1, UIUtils.LiSu_LS16);
        Juitil.TextBackground(g, RoleData.getRoleData().getLoginResult().getScoretype("守护之尘")+"",16, 388, 442+20,UIUtils.COLOR_Draw1, UIUtils.LiSu_LS16);
        for (int i = 0; i < 2; i++) {
            g.drawImage(Juitil.tz315.getImage(), 25+i*315, 138, 260, 248, null);
        }
        g.drawImage(Juitil.tz313.getImage(), 294, 300, 38, 16, null);
        g.drawImage(Juitil.tz320.getImage(), 35, 390, 510, 22, null);

        for (int i = 0; i < 2; i++) {
            plus[i].setBounds(86+i*318, 96, 20, 20);
            g.drawImage(Juitil.tz316.getImage(), 70 + i * (318), 81, 52, 52, null);
            Juitil.TextBackground(g, "锻炼等级  ", 13, (156-18)+i*318, 114, UIUtils.COLOR_Pack, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
            Juitil.ImngBack(g, Juitil.tz128, (220-18)+i*318, 114, 44, 17, 1);
            Juitil.TextBackground(g, "主守护石", 17, 156-18, 80, UIUtils.COLOR_Pack, UIUtils.NEWTX_HY17B, UIUtils.Color_BACK);
            Juitil.TextBackground(g, "副守护石", 17, 455, 80, UIUtils.COLOR_Pack, UIUtils.NEWTX_HY17B, UIUtils.Color_BACK);
            if (AsoulJPanel.getForgeGrades()[i]!=0&&addGoodsImg[i].getIcon()!=null){
                Juitil.TextBackground(g, AsoulJPanel.getForgeGrades()[i]+"", 13, 205+i*318, 115, UIUtils.COLOR_White, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
            }
        }
        if (addGoodsImg[0].getIcon() == null) {
            Juitil.TextBackground(g, "请选择需要融炼的主守护石", 13, 65, 245, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
        }
        if (addGoodsImg[1].getIcon() == null) {
            Juitil.TextBackground(g, "请选择用作融炼材料的副守护石", 13, 372, 245, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
        }
        if (addGoodsImg[0].getIcon() != null) {
            FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getAsoulJPanel().
                    RefiningAttributeDisplay(g, 203, 172, 24, 75, 50, 165, getGoodstables()[0]);

        }
        if (addGoodsImg[1].getIcon() != null) {
            FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getAsoulJPanel().
                    RefiningAttributeDisplay(g, 498+10,172,24,372+10,345+10,165, getGoodstables()[1]);
            int num = AsoulJPanel.getForgeGrades()[1]-AsoulJPanel.getForgeGrades()[0] + 2 ;
            Juitil.TextBackground(g, "可吸收灵窍个数" , 19, 210, 388, UIUtils.COLOR_SHS, UIUtils.MSYH_HY19, UIUtils.Color_BACK);
            Juitil.ImngBack(g, Juitil.tz128, 360, 392, 44, 20, 1);
            Juitil.TextBackground(g, num+"", 19, 360, 388, UIUtils.COLOR_PLAY, UIUtils.MSYH_HY19, UIUtils.Color_BACK);
        }


    }

    /**
     * 隐藏融炼动态按钮
     */
    public static  void  isvstop(){
        RonglianJPanel ronglianJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel();
        for (int i = 0; i < ronglianJPanel.getImgback().length; i++) {
            ronglianJPanel.getImgback()[i].setVisible(false);
            ronglianJPanel.getNewtext()[i].setVisible(false);
        }
    }
    public JLabel[] getAddGoodsImg() {
        return addGoodsImg;
    }

    public void setAddGoodsImg(JLabel[] addGoodsImg) {
        this.addGoodsImg = addGoodsImg;
    }

    public JLabel[] getExerciseLevel() {
        return exerciseLevel;
    }

    public void setExerciseLevel(JLabel[] exerciseLevel) {
        this.exerciseLevel = exerciseLevel;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Goodstable[] getGoodstables() {
        return goodstables;
    }

    public void setGoodstables(Goodstable[] goodstables) {
        this.goodstables = goodstables;
    }

    public RonglianBtn[] getPlus() {
        return plus;
    }

    public void setPlus(RonglianBtn[] plus) {
        this.plus = plus;
    }

    public JLabel[] getImgback() {
        return imgback;
    }

    public void setImgback(JLabel[] imgback) {
        this.imgback = imgback;
    }

    public JLabel[] getNewtext() {
        return newtext;
    }

    public void setNewtext(JLabel[] newtext) {
        this.newtext = newtext;
    }
}
