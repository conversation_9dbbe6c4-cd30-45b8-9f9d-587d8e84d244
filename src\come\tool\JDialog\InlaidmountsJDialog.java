package come.tool.JDialog;

import jxy2.jutnil.Juitil;
import org.come.Frame.MountJframe;
import org.come.Jpanel.MountJPanel;
import org.come.Jpanel.MoutnModelJPanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.Mount;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;

public class InlaidmountsJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
        int index = (int) object;
            MountJPanel mountjpanel = MountJframe.getMountjframe().getMountjpanel();
            JList<MoutnModelJPanel> modelJPanelJList = mountjpanel.getModelJPanelJList();
            Mount mount = ZhuJpanel.getListMount().get(modelJPanelJList.getSelectedIndex());
            ImageIcon img = CutButtonImage.getWdfPng("0x7BA"+MoutnModelJPanel.getMountId()+mount.getMountid(),51,52,"head.wdf");
            Image imgs = Juitil.toRoundedCornerImage(img.getImage(), 10);
            if ( mountjpanel.getGuardType()==4){
                mountjpanel.getAddMountImg()[index].setIcon(new ImageIcon(imgs));
                mountjpanel.getAddMountImg()[index].setVisible(true);
                mountjpanel.getGuardianStone()[index].setVisible(true);
                mountjpanel.getOkis()[index] =  Juitil.number2Chinese(mount.getMountid())+"·"+mount.getMountname();
                mountjpanel.getMountidlits().put(index, mount.getMid()+"");
                if (MountJPanel.ShstoneID(index)!=null) {
                    mount.setMountstone(MountJPanel.ShstoneID(index));
                    String sendmes = Agreement.getAgreement().changeMountValue(GsonUtil.getGsonUtil().getgson().toJson(mount));
                    SendMessageUntil.toServer(sendmes);
                }
            }else {
                //四神兽坐骑守护
                mountjpanel.getAddFourGodImg()[index].setIcon(new ImageIcon(imgs));
                mountjpanel.getAddFourGodImg()[index].setVisible(true);
                mountjpanel.getFourname()[index] = Juitil.number2Chinese(mount.getMountid())+"·"+mount.getMountname();
                mountjpanel.getFourmountidlits().put(index, mount.getMid()+"");
            }
            FormsManagement.HideForm(140);
            //刷新前端召唤兽抗性
            //需要发送守护坐骑的信息给服务端
            String mes = Agreement.getAgreement().rolechangeAgreement("U"+mountjpanel.getGuardType()+"|"+mount.getMid()+"|"+index);
            SendMessageUntil.toServer(mes);

        }
    }
}
