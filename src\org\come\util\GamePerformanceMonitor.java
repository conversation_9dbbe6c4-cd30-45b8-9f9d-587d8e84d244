package org.come.util;

import org.come.login.GameView;
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 游戏性能监控管理器
 * 统一管理FPS监控和性能问题检测
 */
public class GamePerformanceMonitor {
    
    private static GamePerformanceMonitor instance;
    private GameView currentGameView;
    private GameFocusListener focusListener;
    private Timer performanceCheckTimer;
    
    // 性能统计
    private int consecutiveZeroFPS = 0;
    private long lastPerformanceCheck = System.currentTimeMillis();
    
    private GamePerformanceMonitor() {
        initPerformanceMonitoring();
    }
    
    public static GamePerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new GamePerformanceMonitor();
        }
        return instance;
    }
    
    /**
     * 设置当前活跃的GameView
     */
    public void setCurrentGameView(GameView gameView) {
        this.currentGameView = gameView;
        if (focusListener != null) {
            focusListener.setGameView(gameView);
        }
        System.out.println("[性能监控] 设置当前GameView: " + (gameView != null ? "已设置" : "null"));
    }
    
    /**
     * 获取焦点监听器
     */
    public GameFocusListener getFocusListener() {
        if (focusListener == null) {
            focusListener = new GameFocusListener(currentGameView);
        }
        return focusListener;
    }
    
    /**
     * 初始化性能监控
     */
    private void initPerformanceMonitoring() {
        // 每5秒检查一次性能状态
        performanceCheckTimer = new Timer(5000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                checkPerformanceStatus();
            }
        });
        performanceCheckTimer.start();
        
        System.out.println("[性能监控] 性能监控系统已启动");
    }
    
    /**
     * 检查性能状态
     */
    private void checkPerformanceStatus() {
        if (currentGameView == null) {
            return;
        }

        int currentFPS = currentGameView.getCurrentFPS();
        long currentTime = System.currentTimeMillis();

        // 获取游戏活跃状态（通过反射或其他方式）
        boolean isGameActive = isGameViewActive();

        // 只在游戏活跃时检测零帧数问题
        if (currentFPS == 0 && isGameActive) {
            consecutiveZeroFPS++;
//            System.err.println("[性能警告] 游戏活跃时检测到零帧数，连续次数: " + consecutiveZeroFPS);

            if (consecutiveZeroFPS >= 5) { // 增加阈值
//                System.err.println("[性能严重] 游戏活跃时连续" + consecutiveZeroFPS + "次检测到零帧数！");
                triggerPerformanceRecovery();
            }
        } else if (currentFPS > 0) {
            if (consecutiveZeroFPS > 0) {
//                System.out.println("[性能恢复] 帧数恢复正常: " + currentFPS + " FPS");
                consecutiveZeroFPS = 0;
            }
        } else if (currentFPS == 0 && !isGameActive) {
            // 游戏非活跃时零帧数是正常的，重置计数器
            consecutiveZeroFPS = 0;
        }

        // 只在游戏活跃时检测低帧数
        if (isGameActive && currentFPS > 0 && currentFPS < 15) {
//            System.out.println("[性能提示] 检测到低帧数: " + currentFPS + " FPS");
        }

        lastPerformanceCheck = currentTime;
    }

    /**
     * 检查游戏视图是否活跃
     */
    private boolean isGameViewActive() {
        // 简单的窗口焦点检测
        if (currentGameView != null) {
            try {
                // 检查窗口是否有焦点
                return currentGameView.getTopLevelAncestor() != null &&
                       ((javax.swing.JFrame)currentGameView.getTopLevelAncestor()).isFocused();
            } catch (Exception e) {
                return true; // 默认认为活跃
            }
        }
        return false;
    }
    
    /**
     * 触发性能恢复机制
     */
    private void triggerPerformanceRecovery() {
//        System.out.println("[性能恢复] 尝试恢复游戏性能...");
        
        if (currentGameView != null) {
            // 强制刷新GameView
            currentGameView.forceRefresh();
            
            // 重置活跃状态
            currentGameView.setGameViewActive(false);
            
            // 延迟恢复活跃状态
            Timer recoveryTimer = new Timer(1000, new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    if (currentGameView != null) {
                        currentGameView.setGameViewActive(true);
//                        System.out.println("[性能恢复] 游戏状态已重置");
                    }
                    ((Timer) e.getSource()).stop();
                }
            });
            recoveryTimer.setRepeats(false);
            recoveryTimer.start();
        }
        
        // 强制垃圾回收
        System.gc();
        
        // 重置计数器
        consecutiveZeroFPS = 0;
    }
    
    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        if (currentGameView == null) {
            return "GameView未设置";
        }
        
        int fps = currentGameView.getCurrentFPS();
        return String.format("FPS: %d, 连续零帧: %d次", fps, consecutiveZeroFPS);
    }
    
    /**
     * 停止性能监控
     */
    public void stopMonitoring() {
        if (performanceCheckTimer != null) {
            performanceCheckTimer.stop();
        }
        System.out.println("[性能监控] 性能监控系统已停止");
    }
}
