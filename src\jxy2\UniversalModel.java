package jxy2;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.supet.SipetBtn;
import jxy2.supet.SipetJPanel;
import org.come.entity.RoleSummoning;
import org.come.until.CutButtonImage;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

/**
 * ClassName: 通用模型(多参数，按钮，图像)
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/4/13 9:24
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class UniversalModel extends JPanel {
    private SipetBtn btnRich;
    private JLabel labPetName,labPetImg,labAnswer,labBorder;
    private Color textColor = new Color(188, 188, 188); // 默认颜色
    private Color isClick = new Color(187, 165, 75);//参展颜色
    private int types;
    private int index;
    private JLabel labkrdimg;

    /**
     * 传入通用参数着，设置模型列表数据
     * @param width 模型宽度
     * @param height 模型高度
     */
    public UniversalModel(int width,int height) {
        setPreferredSize(new Dimension(width, height));
        setOpaque(false);
        setLayout(null);
        getBtnRich();
        getPetname();
        getLabPetImg();
        getLabAnswer();
        getLabkrdimg();
        getLabBorder();
    }

    /**
     * 加载召唤兽数据信息
     * @param index 排序标识
     * @param pet 召唤兽实体类
     * @param btnx 按钮x坐标
     * @param btny 按钮y坐标
     * @param listHeight 列表高度
     * @param isVisible 图标头像按钮可见度
     * @param buttonVisibility 按钮的可见度
     * @param types 0主列表，1排序，2炼妖
     */
    public void PetData(int index, RoleSummoning pet,int btnx,int btny,int listHeight,boolean isVisible,boolean buttonVisibility,int namex ,int types) {
        this.types= types;
        btnRich.setNtext("L|" + (index+1));
        btnRich.setBounds(btnx, btny, 18, 18);
        btnRich.setVisible(buttonVisibility);
        labPetName.setText(pet.getSummoningname());
        labPetName.setBounds(namex, isVisible?6:0, 118, listHeight);
        labPetImg.setBounds(7, 5, 29, 29);
        labPetImg.setVisible(isVisible);
        labkrdimg.setBounds(125, 1, 18, 18);
        labBorder.setVisible(isVisible);
        labBorder.setBounds(4, 2, 35, 35);
        //设置名字背景颜色
        if (SipetJPanel.getBtnRich(pet)) {
            setTextColor(isClick);
        }else {
            setTextColor(UIUtils.COLOR_goods_quantity);
        }
        labAnswer.setVisible(SipetJPanel.getBtnRich(pet));
        ImageIcon icon1 = CutButtonImage.getWdfPng("0x6B" + pet.getSummoningskin(), 29,29,"head.wdf");
        labPetImg.setIcon(icon1);
        String leng18_18 = Util.SwitchUI ==0||Util.SwitchUI==1  ? "0x6FEB8580": "0x6FAB1017";
        btnRich.setIcons(Juitil.getImgs(leng18_18));
        labkrdimg.setVisible(SipetJPanel.QueryTimeBuff("枯荣丹").compareTo(pet.getSid()+"")==0);
        String borderIcon = Util.SwitchUI ==0||Util.SwitchUI==1  ? "0x6FAC1075": "0x6FAB1088";
        labBorder.setIcon(Juitil.getImg(borderIcon));
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
    }

    public void getBtnRich() {
        if (btnRich == null){
            btnRich = new SipetBtn(ImgConstants.tz80, 1, UIUtils.COLOR_BTNTEXT, UIUtils.TEXT_FONT,this, -1,"");
            btnRich.setBounds(0, 2, 18, 18);
            this.add(btnRich);
        }
    }

    public void getPetname() {
        if (labPetName==null){
            labPetName = new JLabel(){
                protected void paintComponent(Graphics g) {
                    Graphics2D g2d = (Graphics2D) g;
                    g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                    g.setFont(new Font("宋体", Font.PLAIN, 16));
                    g.setColor(textColor);
                    g.drawString(getText(), 0, 17);
                    g.drawString(getText(), 0, 17);
                }
            };
            labPetName.setFont(UIUtils.MSYH_HY14);
            labPetName.setBounds(20, 0, 118, 20);
            add(labPetName);
        }
    }

    public void setBtnRich(SipetBtn btnRich) {
        this.btnRich = btnRich;
    }

    public void setPetname(JLabel labPetName) {
        this.labPetName = labPetName;
    }

    public Color getTextColor() {
        return textColor;
    }

    public void setTextColor(Color color) {
        this.textColor = color;
        repaint(); // 触发重绘
    }

    public Color getIsClick() {
        return isClick;
    }

    public void setIsClick(Color isClick) {
        this.isClick = isClick;
    }

    public JLabel getLabPetName() {
        return labPetName;
    }

    public void setLabPetName(JLabel labPetName) {
        this.labPetName = labPetName;
    }

    public JLabel getLabPetImg() {
        if (labPetImg==null){
            labPetImg = new JLabel();
            labPetImg.setIcon(Juitil.tz25);
            add(labPetImg);
        }
        return labPetImg;
    }

    public void setLabPetImg(JLabel labPetImg) {
        this.labPetImg = labPetImg;
    }

    public int getTypes() {
        return types;
    }

    public void setTypes(int types) {
        this.types = types;
    }

    public JLabel getLabAnswer() {
        if (labAnswer==null){
            labAnswer = new JLabel("√");
            labAnswer.setBounds(7, 2, 18, 18);
            labAnswer.setBackground(UIUtils.COLOR_NAME);
            labAnswer.setForeground(UIUtils.COLOR_NAME);
            labAnswer.setVisible(false);
            add(labAnswer);
        }
        return labAnswer;
    }

    public void setLabAnswer(JLabel labAnswer) {
        this.labAnswer = labAnswer;
    }

    public JLabel getLabkrdimg() {
         if (labkrdimg==null){
            labkrdimg = new JLabel();
             labkrdimg.setBounds(125, 1, 18, 18);
             labkrdimg.setText("获");
             labkrdimg.setForeground( UIUtils.COLOR_zhi);
             labkrdimg.setFont(UIUtils.MSYH_HY13);
             labkrdimg. setVerticalTextPosition(SwingConstants.CENTER);
             labkrdimg.setHorizontalTextPosition(SwingConstants.CENTER);
            labkrdimg.setIcon(Juitil.tz340);
            add(labkrdimg);
        }
        return labkrdimg;
    }

    public void setLabkrdimg(JLabel labkrdimg) {
        this.labkrdimg = labkrdimg;
    }

    public JLabel getLabBorder() {
        if (labBorder==null){
            labBorder = new JLabel();
            labBorder.setBounds(0, 0, 35, 35);
            add(labBorder);
        }
        return labBorder;
    }

    public void setLabBorder(JLabel labBorder) {
        this.labBorder = labBorder;
    }
}
