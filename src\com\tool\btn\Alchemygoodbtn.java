package com.tool.btn;

import com.tool.tcpimg.UIUtils;
import jxy2.backutil.AlchemyGoodUntil;
import org.come.Frame.AlchemyJframe;
import org.come.Jpanel.AlchemyMainJpanel;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class Alchemygoodbtn  extends MoBanBtn{
    private JPanel jpanel;
    // 按钮位置额外
    private int path;
    public Alchemygoodbtn(String iconpath, int type, JPanel jpanel, int path,String name) {
        super(iconpath, type,0, UIUtils.COLOR_BTNTEXT,"");
        this.jpanel = jpanel;
        this.path = path;
        this.setText(name);
        setFont(UIUtils.TEXT_FONT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public void dianjisz() {
        type = 2;
        btnchange(2);
        if (jpanel instanceof AlchemyMainJpanel) {
            AlchemyMainJpanel alchemyMainJpanel = (AlchemyMainJpanel) jpanel;
            BtnUtil.btnBinding(alchemyMainJpanel.getBtnrights(), path);
        }
    }

    @Override
    public void chooseyes() {
        AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getBtnrights()[path].dianjisz();
        AlchemyGoodUntil.getgood(path);
    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

    }
}
