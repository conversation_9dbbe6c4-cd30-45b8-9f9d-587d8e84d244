package com.tool.role;


/**
 * 
 * <AUTHOR>
 *
 *角色抗性字段
 */

public class Ql {
	    //抗性
		private double kwl=0;//抗物理
		private double kzs=0;//抗震慑
		private double kff=0;//抗风
		private double klf=0;//抗雷
		private double ksf=0;//坑水
		private double khf=0;//坑火
		private double khl=0;//抗混乱
		private double khs=0;//抗昏睡
		private double kfy=0;//抗封印
		private double kzd=0;//抗中毒
		private double kzds=0;//抗中毒伤害
		private double kyw=0;//抗遗忘
		private double kgh=0;//抗鬼火
		private double ksc=0;//抗三尸
		private double klb;//抗灵宝伤害
		private double kqk;//抵御强克效果
		private double kwsx;//抗无属性伤害
		private double kzshp;//抗震慑气血
		private double kzsmp;//抗震慑魔法
		private double kjge;//抗金箍
		private double kqw;//抗情网
		private double khr;//抗浩然正气
		private double kqm;//抗青面獠牙
		private double ktm;//抗天魔解体
		private double kxl;//抗小楼夜哭
		private double kfg;//抗分光化影
		private double kzml;//抗致命率
		private double kbf;//抗风法狂暴
		private double kbh;//抗火法狂暴
		private double kbs;//抗水法狂暴
		private double kbl;//抗雷法狂暴
		private double kbg;//抗鬼火狂暴

		//忽视
		private double hfyv=0;//忽视防御程度
		private double hfyl=0;//忽视防御几率
		private double hff=0;//忽视风法
		private double hlf=0;//忽视雷法
		private double hsf=0;//忽视水法
		private double hhf=0;//忽视火法
		private double hhl=0;//忽视混乱
		private double hhs=0;//忽视昏睡
		private double hfy=0;//忽视封印
		private double hzd=0;//忽视中毒
		private double hzs=0;//忽视抗震慑
	    private double hds=0;//忽视躲闪
	    private double hfj=0;//忽视反击
	    private double hxfkl=0;//忽视仙法抗性率
	    private double hxfcd=0;//忽视仙法抗性程度
		private double hgh=0;//忽视鬼火
		private double hyw=0;//忽视遗忘
		
		//强
		private double qff=0;//强风法	
		private double qlf=0;//强雷法
		private double qsf=0;//强水法
		private double qhf=0;//强火法
		private double qhl=0;//强混乱
		private double qhs=0;//强昏睡
		private double qzs=0;//强震慑
		private double qfy=0;//强封印
		private double qzd=0;//强中毒
		private double qzds=0;//强中毒伤害
		private double qgh=0;//强鬼火
		private double qyw=0;//强遗忘
		private double qsc=0;//强三尸血
		private double qschx=0;//强三尸血回血程度
		private double qqk;//增加强克效果
		private double qzhs;//对召唤兽伤害
		private double qgjf=0;//加强攻击法术效果
		private double qfyf=0;//加强防御法术效果
		private double qsdf=0;//加强速度法术效果
		private double qmh=0;//加强魅惑
		private double qjg=0;//强金箍
		private double qqw=0;//强情网
		
		private double qlpl=0;//加强霹雳效果
		private double qlfy=0;//加强扶摇效果
		private double qlcb=0;//加强沧波效果
		private double qlglv=0;//加强甘霖回血值
		private double qlglc=0;//加强甘霖回血程度
		
		//其他
		private double eds=0;//躲闪率
		private double efjl=0;//反击率
		private double efjv=0;//反击次数
		private double eljl=0;//连击率
		private double eljv=0;//连击次数	
		private double emzl=0;//命中率
		private double ezml=0;//致命率
		private double ekbl=0;//狂暴率
		private double efzl=0;//反震率
		private double efzcd=0;//反震程度
	    private double exfljl=0;//仙法连击率
	    private double exfljs=0;//仙法连击次数
	    
	    private double ejs=0;//百分比减伤
	    private double efsds=0;//法术躲闪
	    
		//五行
		private double wxj=0;//金
		private double wxm=0;//木
		private double wxt=0;//土
		private double wxs=0;//水	
		private double wxh=0;//火
		private double wxqj=0;//强力克金
		private double wxqm=0;//强力克木
		private double wxqt=0;//强力克土
		private double wxqs=0;//强力克水
		private double wxqh=0;//强力克火
		
		//伤害
		private double swsx=0;//无属性伤害
		private double sff=0;//风法伤害
		private double slf=0;//雷法伤害	
		private double ssf=0;//水法伤害
		private double shf=0;//火法伤害
		private double szd=0;//毒伤害
		private double sgh=0;//鬼火伤害
		private double ssc=0;//三尸伤害
	    
		//狂暴
		private double blf=0;//雷法狂暴
		private double bff=0;//风法狂暴
		private double bsf=0;//水法狂暴
		private double bhf=0;//火法狂暴
	    private double bgh=0;//鬼火狂暴
	    private double bsc=0;//三尸虫狂暴
	    private double bfy=0;//封印狂暴
	    private double bhl=0;//混乱狂暴
	    private double bhs=0;//昏睡狂暴
	    private double bzd=0;//毒法狂暴
	    private double bjf=0;//加防狂暴
	    private double bjg=0;//加攻狂暴
	    private double bzs=0;//震慑狂暴
	    private double byw=0;//遗忘狂暴
	    private double bmh=0;//魅惑狂暴

	    private double blfcd=0;//雷法狂暴程度
		private double bffcd=0;//风法狂暴程度
		private double bsfcd=0;//水法狂暴程度
		private double bhfcd=0;//火法狂暴程度
	    private double bghcd=0;//鬼火狂暴程度
	    private double bsccd=0;//三尸虫狂暴程度
	    
		private double f_f;//附加封印攻击
		private double f_h;//附加混乱攻击
		private double f_d;//附加中毒攻击
		private double f_xf;//附加风法攻击
		private double f_xh;//附加火法攻击
		private double f_xs;//附加水法攻击
		private double f_xl;//附加雷法攻击
		private double f_zs;//附加震慑攻击
		private double f_sc;//附加三尸攻击		
		
		//躲闪
		private double dzs;//震慑躲闪
		private double dhf;//火法躲闪
		private double dlf;//雷法躲闪
		private double dff;//风法躲闪
		private double dsf;//水法躲闪
		private double ddf;//毒法躲闪
		private double dfy;//封印躲闪
		private double dhl;//混乱躲闪
		private double dhs;//昏睡躲闪
		private double dyw;//遗忘躲闪
		private double dgh;//鬼火躲闪
		private double dsc;//三尸虫躲闪
		
		//伤害减免
		private double jsf;//水法伤害减免
		private double jff;//风法伤害减免
		private double jlf;//雷法伤害减免
		private double jhf;//火法伤害减免
		private double jgh;//鬼火伤害减免

		//抗强力五行
		private double kqgh;
		private double kqgl;
		private double kqgf;
		private double kqgs;


		public void Reset(){
			kwl=0;kzs=0;kff=0;klf=0;ksf=0;khf=0;khl=0;khs=0;kfy=0;
			kzd=0;kzds=0;kyw=0;kgh=0;ksc=0;klb=0;kqk=0;kwsx=0;kzshp=0;
			kzsmp=0;kjge=0;kqw=0;khr=0;kqm=0;ktm=0;kxl=0;kfg=0;kzml=0;
		    kbf=0;kbh=0;kbs=0;kbl=0;kbg=0;
		    
			hfyv=0;hfyl=0;hff=0;hlf=0;hsf=0;hhf=0;
			hhl=0;hhs=0;hfy=0;hzd=0;hzs=0;hds=0;
			hfj=0;hxfkl=0;hxfcd=0;hgh=0;hyw=0;
		    
			qff=0;qlf=0;qsf=0;qhf=0;qhl=0;qhs=0;qzs=0;qfy=0;qzd=0;qzds=0;
			qgh=0;qyw=0;qsc=0;qschx=0;qqk=0;qzhs=0;qgjf=0;qfyf=0;qsdf=0;
		    qmh=0;qjg=0;qqw=0;//强情网
		    qlpl=0;qlfy=0;qlcb=0;qlglv=0;qlglc=0;
			
			eds=0;efjl=0;efjv=0;eljl=0;eljv=0;emzl=0;ezml=0;
			ekbl=0;efzl=0;efzcd=0;exfljl=0;exfljs=0;ejs=0;efsds=0;
			
			wxj=0;wxm=0;wxt=0;wxs=0;wxh=0;wxqj=0;wxqm=0;wxqt=0;wxqs=0;wxqh=0;
			
			swsx=0;sff=0;slf=0;ssf=0;shf=0;szd=0;sgh=0;ssc=0;
			
			blf=0;bff=0;bsf=0;bhf=0;bgh=0;bsc=0;
		    bfy=0;bhl=0;bhs=0;bzd=0;bjf=0;bjg=0;bzs=0;byw=0;bmh=0;//魅惑狂暴
			blfcd=0;bffcd=0;bsfcd=0;bhfcd=0;bghcd=0;bsccd=0;
		    
			f_f=0;f_h=0;f_d=0;f_xf=0;f_xh=0;f_xs=0;f_xl=0;
			f_zs=0;f_sc=0;
			
			dzs=0;dhf=0;dlf=0;dff=0;dsf=0;ddf=0;dfy=0;dhl=0;dhs=0;dgh=0;dyw=0;dsc=0;
			jsf=0;jff=0;jlf=0;jlf=0;jgh=0;
			kqgh=0;kqgl=0;kqgf=0;kqgs=0;
		}
		/**加强全系法术*/
		public void addQ(double v){
			qff+=v;
			qlf+=v;
			qsf+=v;
			qhf+=v;
			
			qhl+=v;
			qhs+=v;
			qfy+=v;
			qzds+=v;
			
			qzs+=v;
			qgjf+=v;
			qfyf+=v;
			qsdf+=v;
			
			qgh+=v;
			qyw+=v;
			qsc+=v*100;
			qmh+=v;
			
			qlpl+=v;
			qlfy+=v;
			qlcb+=v;
			qlglv+=v*100;
		}
		/**加抗人法*/
		public void addkr(double v){
			 khl+=v;
			 khs+=v; 
			 kfy+=v;
			 kzd+=v;
		} 
		/**控制法4抗上限过滤*/
		public void addKKUp(double up){
			if (khl>up) {khl=up;}
			if (khs>up) {khs=up;}
			if (kfy>up) {kfy=up;}
			if (kyw>up) {kyw=up;}
		}
		/**抗上限过滤*/
		public void addKUp(double up){
			if (kwl>up) {kwl=up;}
			if (kzs>up) {kzs=up;}
			if (kff>up) {kff=up;}
			if (klf>up) {klf=up;}
			if (ksf>up) {ksf=up;}
			if (khf>up) {khf=up;}
			if (kzd>up) {kzd=up;}
			if (kgh>up) {kgh=up;}
			if (klb>up) {klb=up;}
			if (kzml>up) {kzml=up;}
		}
		public double getRolekwl() {
			return kwl;
		}

		public void setRolekwl(double rolekwl) {
			this.kwl = rolekwl;
		}

		public double getRolekzs() {
			return kzs;
		}

		public void setRolekzs(double rolekzs) {
			this.kzs = rolekzs;
		}

		public double getRolekff() {
			return kff;
		}

		public void setRolekff(double rolekff) {
			this.kff = rolekff;
		}

		public double getRoleklf() {
			return klf;
		}

		public void setRoleklf(double roleklf) {
			this.klf = roleklf;
		}

		public double getRoleksf() {
			return ksf;
		}

		public void setRoleksf(double roleksf) {
			this.ksf = roleksf;
		}

		public double getRolekhf() {
			return khf;
		}

		public void setRolekhf(double rolekhf) {
			this.khf = rolekhf;
		}

		public double getRolekhl() {
			return khl;
		}

		public void setRolekhl(double rolekhl) {
			this.khl = rolekhl;
		}

		public double getRolekhs() {
			return khs;
		}

		public void setRolekhs(double rolekhs) {
			this.khs = rolekhs;
		}

		public double getRolekfy() {
			return kfy;
		}

		public void setRolekfy(double rolekfy) {
			this.kfy = rolekfy;
		}

		public double getRolekzd() {
			return kzd;
		}

		public void setRolekzd(double rolekzd) {
			this.kzd = rolekzd;
		}

		public double getRolekyw() {
			return kyw;
		}

		public void setRolekyw(double rolekyw) {
			this.kyw = rolekyw;
		}

		public double getRolekgh() {
			return kgh;
		}

		public void setRolekgh(double rolekgh) {
			this.kgh = rolekgh;
		}

		public double getRoleksc() {
			return ksc;
		}

		public void setRoleksc(double roleksc) {
			this.ksc = roleksc;
		}

		public double getRolehsfyv() {
			return hfyv;
		}

		public void setRolehsfyv(double rolehsfyv) {
			this.hfyv = rolehsfyv;
		}

		public double getRolehsfyl() {
			return hfyl;
		}

		public void setRolehsfyl(double rolehsfyl) {
			this.hfyl = rolehsfyl;
		}

		public double getRolehsff() {
			return hff;
		}

		public void setRolehsff(double rolehsff) {
			this.hff = rolehsff;
		}

		public double getRolehslf() {
			return hlf;
		}

		public void setRolehslf(double rolehslf) {
			this.hlf = rolehslf;
		}

		public double getRolehssf() {
			return hsf;
		}

		public void setRolehssf(double rolehssf) {
			this.hsf = rolehssf;
		}

		public double getRolehshf() {
			return hhf;
		}

		public void setRolehshf(double rolehshf) {
			this.hhf = rolehshf;
		}

		public double getRolehshl() {
			return hhl;
		}

		public void setRolehshl(double rolehshl) {
			this.hhl = rolehshl;
		}

		public double getRolehshs() {
			return hhs;
		}

		public void setRolehshs(double rolehshs) {
			this.hhs = rolehshs;
		}

		public double getRolehsfy() {
			return hfy;
		}

		public void setRolehsfy(double rolehsfy) {
			this.hfy = rolehsfy;
		}

		public double getRolehszd() {
			return hzd;
		}

		public void setRolehszd(double rolehszd) {
			this.hzd = rolehszd;
		}

		public double getRoleqff() {
			return qff;
		}

		public void setRoleqff(double roleqff) {
			this.qff = roleqff;
		}

		public double getRoleqlf() {
			return qlf;
		}

		public void setRoleqlf(double roleqlf) {
			this.qlf = roleqlf;
		}

		public double getRoleqsf() {
			return qsf;
		}

		public void setRoleqsf(double roleqsf) {
			this.qsf = roleqsf;
		}
		public double getRoleqhf() {
			return qhf;
		}
		public void setRoleqhf(double roleqhf) {
			this.qhf = roleqhf;
		}
		public double getRoleqhl() {
			return qhl;
		}
		public void setRoleqhl(double roleqhl) {
			this.qhl = roleqhl;
		}
		public double getRoleqhs() {
			return qhs;
		}
		public void setRoleqhs(double roleqhs) {
			this.qhs = roleqhs;
		}
		public double getRoleqzs() {
			return qzs;
		}

		public void setRoleqzs(double roleqzs) {
			this.qzs = roleqzs;
		}

		public double getRoleqfy() {
			return qfy;
		}

		public void setRoleqfy(double roleqfy) {
			this.qfy = roleqfy;
		}

		public double getRoleqzd() {
			return qzd;
		}

		public void setRoleqzd(double roleqzd) {
			this.qzd = roleqzd;
		}

		public double getRolefdsl() {
			return eds;
		}

		public void setRolefdsl(double rolefdsl) {
			this.eds = rolefdsl;
		}

		public double getRoleffjl() {
			return efjl;
		}

		public void setRoleffjl(double roleffjl) {
			this.efjl = roleffjl;
		}

		public double getRoleffjv() {
			return efjv;
		}

		public void setRoleffjv(double roleffjv) {
			this.efjv = roleffjv;
		}

		public double getRolefljl() {
			return eljl;
		}

		public void setRolefljl(double rolefljl) {
			this.eljl = rolefljl;
		}

		public double getRolefljv() {
			return eljv;
		}

		public void setRolefljv(double rolefljv) {
			this.eljv = rolefljv;
		}

		public double getRolefmzl() {
			return emzl;
		}

		public void setRolefmzl(double rolefmzl) {
			this.emzl = rolefmzl;
		}

		public double getRolefkbl() {
			return ekbl;
		}
		public void setRolefkbl(double rolefkbl) {
			this.ekbl = rolefkbl;
		}
		public double getRoleffzl() {
			return efzl;
		}
		public void setRoleffzl(double roleffzl) {
			this.efzl = roleffzl;
		}
		public double getRoleffzcd() {
			return efzcd;
		}
		public void setRoleffzcd(double roleffzcd) {
			this.efzcd = roleffzcd;
		}
		public double getRolewxj() {
			return wxj;
		}

		public void setRolewxj(double rolewxj) {
			this.wxj = rolewxj;
		}

		public double getRolewxm() {
			return wxm;
		}

		public void setRolewxm(double rolewxm) {
			this.wxm = rolewxm;
		}

		public double getRolewxt() {
			return wxt;
		}

		public void setRolewxt(double rolewxt) {
			this.wxt = rolewxt;
		}

		public double getRolewxs() {
			return wxs;
		}

		public void setRolewxs(double rolewxs) {
			this.wxs = rolewxs;
		}

		public double getRolewxh() {
			return wxh;
		}

		public void setRolewxh(double rolewxh) {
			this.wxh = rolewxh;
		}

		public double getRolewxqkj() {
			return wxqj;
		}

		public void setRolewxqkj(double rolewxqkj) {
			this.wxqj = rolewxqkj;
		}

		public double getRolewxqkm() {
			return wxqm;
		}

		public void setRolewxqkm(double rolewxqkm) {
			this.wxqm = rolewxqkm;
		}

		public double getRolewxqkt() {
			return wxqt;
		}

		public void setRolewxqkt(double rolewxqkt) {
			this.wxqt = rolewxqkt;
		}

		public double getRolewxqks() {
			return wxqs;
		}

		public void setRolewxqks(double rolewxqks) {
			this.wxqs = rolewxqks;
		}

		public double getRolewxqkh() {
			return wxqh;
		}

		public void setRolewxqkh(double rolewxqkh) {
			this.wxqh = rolewxqkh;
		}

		public double getRolewsxsh() {
			return swsx;
		}

		public void setRolewsxsh(double rolewsxsh) {
			this.swsx = rolewsxsh;
		}

		public double getRoleffsh() {
			return sff;
		}

		public void setRoleffsh(double roleffsh) {
			this.sff = roleffsh;
		}

		public double getRolelfsh() {
			return slf;
		}

		public void setRolelfsh(double rolelfsh) {
			this.slf = rolelfsh;
		}

		public double getRolesfsh() {
			return ssf;
		}

		public void setRolesfsh(double rolesfsh) {
			this.ssf = rolesfsh;
		}

		public double getRolehfsh() {
			return shf;
		}

		public void setRolehfsh(double rolehfsh) {
			this.shf = rolehfsh;
		}

		public double getRolelfkb() {
			return blf;
		}

		public void setRolelfkb(double rolelfkb) {
			this.blf = rolelfkb;
		}

		public double getRoleffkb() {
			return bff;
		}

		public void setRoleffkb(double roleffkb) {
			this.bff = roleffkb;
		}

		public double getRolesfkb() {
			return bsf;
		}

		public void setRolesfkb(double rolesfkb) {
			this.bsf = rolesfkb;
		}

		public double getRolehfkb() {
			return bhf;
		}

		public void setRolehfkb(double rolehfkb) {
			this.bhf = rolehfkb;
		}

		public double getRolezdsh() {
			return szd;
		}

		public void setRolezdsh(double rolezdsh) {
			this.szd = rolezdsh;
		}

		public double getRoleghsh() {
			return sgh;
		}

		public void setRoleghsh(double roleghsh) {
			this.sgh = roleghsh;
		}

		public double getRolesssh() {
			return ssc;
		}

		public void setRolesssh(double rolesssh) {
			this.ssc = rolesssh;
		}

		public double getRolegstronghostfire() {
			return qgh;
		}

		public void setRolegstronghostfire(double rolegstronghostfire) {
			this.qgh = rolegstronghostfire;
		}

		public double getRolestrongforget() {
			return qyw;
		}

		public void setRolestrongforget(double rolestrongforget) {
			this.qyw = rolestrongforget;
		}

		public double getRolestrongbodyblood() {
			return qsc;
		}

		public void setRolestrongbodyblood(double rolestrongbodyblood) {
			this.qsc = rolestrongbodyblood;
		}

		public double getRolestrongbodyblooddeep() {
			return qschx;
		}

		public void setRolestrongbodyblooddeep(double rolestrongbodyblooddeep) {
			this.qschx = rolestrongbodyblooddeep;
		}

		public double getRoleghkb() {
			return bgh;
		}

		public void setRoleghkb(double roleghkb) {
			this.bgh = roleghkb;
		}

		public double getRolesskb() {
			return bsc;
		}

		public void setRolesskb(double rolesskb) {
			this.bsc = rolesskb;
		}

		public double getRolehsds() {
			return hds;
		}

		public void setRolehsds(double rolehsds) {
			this.hds = rolehsds;
		}

		public double getRolehsfj() {
			return hfj;
		}

		public void setRolehsfj(double rolehsfj) {
			this.hfj = rolehsfj;
		}

		public double getRolexfljl() {
			return exfljl;
		}

		public void setRolexfljl(double rolexfljl) {
			this.exfljl = rolexfljl;
		}

		public double getRolexfljs() {
			return exfljs;
		}

		public void setRolexfljs(double rolexfljs) {
			this.exfljs = rolexfljs;
		}

		public double getRolehsxfkl() {
			return hxfkl;
		}

		public void setRolehsxfkl(double rolehsxfkl) {
			this.hxfkl = rolehsxfkl;
		}

		public double getRolehsxfcd() {
			return hxfcd;
		}

		public void setRolehsxfcd(double rolehsxfcd) {
			this.hxfcd = rolehsxfcd;
		}

		public double getRolehsgh() {
			return hgh;
		}

		public void setRolehsgh(double rolehsgh) {
			this.hgh = rolehsgh;
		}

		public double getRolehsyw() {
			return hyw;
		}

		public void setRolehsyw(double rolehsyw) {
			this.hyw = rolehsyw;
		}

		public double getRolefzml() {
			return ezml;
		}

		public void setRolefzml(double rolefzml) {
			this.ezml = rolefzml;
		}

		public double getJqgjfs() {
			return qgjf;
		}

		public void setJqgjfs(double jqgjfs) {
			this.qgjf = jqgjfs;
		}

		public double getJqfyfs() {
			return qfyf;
		}

		public void setJqfyfs(double jqfyfs) {
			this.qfyf = jqfyfs;
		}

		public double getJqsdfs() {
			return qsdf;
		}

		public void setJqsdfs(double jqsdfs) {
			this.qsdf = jqsdfs;
		}
		public double getRoleklb() {
			return klb;
		}
		public void setRoleklb(double roleklb) {
			this.klb = roleklb;
		}
		public double getQ_qk() {
			return qqk;
		}
		public void setQ_qk(double q_qk) {
			this.qqk = q_qk;
		}
		public double getK_qk() {
			return kqk;
		}
		public void setK_qk(double k_qk) {
			this.kqk = k_qk;
		}
		public double getK_wsxsh() {
			return kwsx;
		}
		public void setK_wsxsh(double k_wsxsh) {
			this.kwsx = k_wsxsh;
		}
		public double getK_zshp() {
			return kzshp;
		}
		public void setK_zshp(double k_zshp) {
			this.kzshp = k_zshp;
		}
		public double getK_zsmp() {
			return kzsmp;
		}
		public void setK_zsmp(double k_zsmp) {
			this.kzsmp = k_zsmp;
		}
		public double getQ_zhssh() {
			return qzhs;
		}
		public void setQ_zhssh(double q_zhssh) {
			this.qzhs = q_zhssh;
		}
		public double getK_jge() {
			return kjge;
		}
		public void setK_jge(double k_jge) {
			this.kjge = k_jge;
		}
		public double getK_qw() {
			return kqw;
		}
		public void setK_qw(double k_qw) {
			this.kqw = k_qw;
		}
		public double getRolehszs() {
			return hzs;
		}
		public void setRolehszs(double rolehszs) {
			this.hzs = rolehszs;
		}
		public double getK_ndhr() {
			return khr;
		}
		public void setK_ndhr(double k_ndhr) {
			this.khr = k_ndhr;
		}
		public double getK_ndqm() {
			return kqm;
		}
		public void setK_ndqm(double k_ndqm) {
			this.kqm = k_ndqm;
		}
		public double getK_ndtm() {
			return ktm;
		}
		public void setK_ndtm(double k_ndtm) {
			this.ktm = k_ndtm;
		}
		public double getK_ndxl() {
			return kxl;
		}
		public void setK_ndxl(double k_ndxl) {
			this.kxl = k_ndxl;
		}
		public double getK_ndfg() {
			return kfg;
		}
		public void setK_ndfg(double k_ndfg) {
			this.kfg = k_ndfg;
		}
		public double getF_f() {
			return f_f;
		}
		public void setF_f(double f_f) {
			this.f_f = f_f;
		}
		public double getF_h() {
			return f_h;
		}
		public void setF_h(double f_h) {
			this.f_h = f_h;
		}
		public double getF_d() {
			return f_d;
		}
		public void setF_d(double f_d) {
			this.f_d = f_d;
		}
		public double getF_xf() {
			return f_xf;
		}
		public void setF_xf(double f_xf) {
			this.f_xf = f_xf;
		}
		public double getF_xh() {
			return f_xh;
		}
		public void setF_xh(double f_xh) {
			this.f_xh = f_xh;
		}
		public double getF_xs() {
			return f_xs;
		}
		public void setF_xs(double f_xs) {
			this.f_xs = f_xs;
		}
		public double getF_xl() {
			return f_xl;
		}
		public void setF_xl(double f_xl) {
			this.f_xl = f_xl;
		}
		public double getKzds() {
			return kzds;
		}
		public void setKzds(double kzds) {
			this.kzds = kzds;
		}
		public double getQzds() {
			return qzds;
		}
		public void setQzds(double qzds) {
			this.qzds = qzds;
		}
		public double getF_zs() {
			return f_zs;
		}
		public void setF_zs(double f_zs) {
			this.f_zs = f_zs;
		}
		public double getF_sc() {
			return f_sc;
		}
		public void setF_sc(double f_sc) {
			this.f_sc = f_sc;
		}
		public double getKzml() {
			return kzml;
		}
		public void setKzml(double kzml) {
			this.kzml = kzml;
		}

		public double getKbf() {
			return kbf;
		}
		public void setKbf(double kbf) {
			this.kbf = kbf;
		}
		public double getKbh() {
			return kbh;
		}
		public void setKbh(double kbh) {
			this.kbh = kbh;
		}
		public double getKbs() {
			return kbs;
		}
		public void setKbs(double kbs) {
			this.kbs = kbs;
		}
		public double getKbl() {
			return kbl;
		}
		public void setKbl(double kbl) {
			this.kbl = kbl;
		}
		public double getKbg() {
			return kbg;
		}
		public void setKbg(double kbg) {
			this.kbg = kbg;
		}
		public double getQmh() {
			return qmh;
		}
		public void setQmh(double qmh) {
			this.qmh = qmh;
		}
		public double getQjg() {
			return qjg;
		}
		public void setQjg(double qjg) {
			this.qjg = qjg;
		}
		public double getQqw() {
			return qqw;
		}
		public void setQqw(double qqw) {
			this.qqw = qqw;
		}
		public double getBfy() {
			return bfy;
		}
		public void setBfy(double bfy) {
			this.bfy = bfy;
		}
		public double getBhl() {
			return bhl;
		}
		public void setBhl(double bhl) {
			this.bhl = bhl;
		}
		public double getBhs() {
			return bhs;
		}
		public void setBhs(double bhs) {
			this.bhs = bhs;
		}
		public double getBzd() {
			return bzd;
		}
		public void setBzd(double bzd) {
			this.bzd = bzd;
		}
		public double getBjf() {
			return bjf;
		}
		public void setBjf(double bjf) {
			this.bjf = bjf;
		}
		public double getBjg() {
			return bjg;
		}
		public void setBjg(double bjg) {
			this.bjg = bjg;
		}
		public double getBzs() {
			return bzs;
		}
		public void setBzs(double bzs) {
			this.bzs = bzs;
		}
		public double getByw() {
			return byw;
		}
		public void setByw(double byw) {
			this.byw = byw;
		}
		public double getBmh() {
			return bmh;
		}
		public void setBmh(double bmh) {
			this.bmh = bmh;
		}
		
		public double getEjs() {
			return ejs;
		}
		public void setEjs(double ejs) {
			this.ejs = ejs;
		}
		public double getEfsds() {
			return efsds;
		}
		public void setEfsds(double efsds) {
			this.efsds = efsds;
		}
		public double getQlpl() {
			return qlpl;
		}
		public void setQlpl(double qlpl) {
			this.qlpl = qlpl;
		}
		public double getQlfy() {
			return qlfy;
		}
		public void setQlfy(double qlfy) {
			this.qlfy = qlfy;
		}
		public double getQlcb() {
			return qlcb;
		}
		public void setQlcb(double qlcb) {
			this.qlcb = qlcb;
		}
		public double getQlglv() {
			return qlglv;
		}
		public void setQlglv(double qlglv) {
			this.qlglv = qlglv;
		}
		public double getQlglc() {
			return qlglc;
		}
		public void setQlglc(double qlglc) {
			this.qlglc = qlglc;
		}

		public double getBlfcd() {
			return blfcd;
		}
		public void setBlfcd(double blfcd) {
			this.blfcd = blfcd;
		}
		public double getBffcd() {
			return bffcd;
		}
		public void setBffcd(double bffcd) {
			this.bffcd = bffcd;
		}
		public double getBsfcd() {
			return bsfcd;
		}
		public void setBsfcd(double bsfcd) {
			this.bsfcd = bsfcd;
		}
		public double getBhfcd() {
			return bhfcd;
		}
		public void setBhfcd(double bhfcd) {
			this.bhfcd = bhfcd;
		}
		public double getBghcd() {
			return bghcd;
		}
		public void setBghcd(double bghcd) {
			this.bghcd = bghcd;
		}
		public double getBsccd() {
			return bsccd;
		}
		public void setBsccd(double bsccd) {
			this.bsccd = bsccd;
		}
		public double getDzs() {
			return dzs;
		}
		public void setDzs(double dzs) {
			this.dzs = dzs;
		}
		public double getDhf() {
			return dhf;
		}
		public void setDhf(double dhf) {
			this.dhf = dhf;
		}
		public double getDlf() {
			return dlf;
		}
		public void setDlf(double dlf) {
			this.dlf = dlf;
		}
		public double getDff() {
			return dff;
		}
		public void setDff(double dff) {
			this.dff = dff;
		}
		public double getDsf() {
			return dsf;
		}
		public void setDsf(double dsf) {
			this.dsf = dsf;
		}
		public double getDdf() {
			return ddf;
		}
		public void setDdf(double ddf) {
			this.ddf = ddf;
		}
		public double getDfy() {
			return dfy;
		}
		public void setDfy(double dfy) {
			this.dfy = dfy;
		}
		public double getDhl() {
			return dhl;
		}
		public void setDhl(double dhl) {
			this.dhl = dhl;
		}
		public double getDhs() {
			return dhs;
		}
		public void setDhs(double dhs) {
			this.dhs = dhs;
		}
		public double getDyw() {
			return dyw;
		}
		public void setDyw(double dyw) {
			this.dyw = dyw;
		}
		public double getDgh() {
			return dgh;
		}
		public void setDgh(double dgh) {
			this.dgh = dgh;
		}
		public double getDsc() {
			return dsc;
		}
		public void setDsc(double dsc) {
			this.dsc = dsc;
		}
		public double getJsf() {
			return jsf;
		}
		public void setJsf(double jsf) {
			this.jsf = jsf;
		}
		public double getJff() {
			return jff;
		}
		public void setJff(double jff) {
			this.jff = jff;
		}
		public double getJlf() {
			return jlf;
		}
		public void setJlf(double jlf) {
			this.jlf = jlf;
		}
		public double getJhf() {
			return jhf;
		}
		public void setJhf(double jhf) {
			this.jhf = jhf;
		}
		public double getJgh() {
			return jgh;
		}
		public void setJgh(double jgh) {
			this.jgh = jgh;
		}

	public double getKqgl() {
		return kqgl;
	}

	public void setKqgl(double kqgl) {
		this.kqgl = kqgl;
	}

	public double getKwl() {
		return kwl;
	}

	public void setKwl(double kwl) {
		this.kwl = kwl;
	}

	public double getKzs() {
		return kzs;
	}

	public void setKzs(double kzs) {
		this.kzs = kzs;
	}

	public double getKff() {
		return kff;
	}

	public void setKff(double kff) {
		this.kff = kff;
	}

	public double getKlf() {
		return klf;
	}

	public void setKlf(double klf) {
		this.klf = klf;
	}

	public double getKsf() {
		return ksf;
	}

	public void setKsf(double ksf) {
		this.ksf = ksf;
	}

	public double getKhf() {
		return khf;
	}

	public void setKhf(double khf) {
		this.khf = khf;
	}

	public double getKhl() {
		return khl;
	}

	public void setKhl(double khl) {
		this.khl = khl;
	}

	public double getKhs() {
		return khs;
	}

	public void setKhs(double khs) {
		this.khs = khs;
	}

	public double getKfy() {
		return kfy;
	}

	public void setKfy(double kfy) {
		this.kfy = kfy;
	}

	public double getKzd() {
		return kzd;
	}

	public void setKzd(double kzd) {
		this.kzd = kzd;
	}

	public double getKyw() {
		return kyw;
	}

	public void setKyw(double kyw) {
		this.kyw = kyw;
	}

	public double getKgh() {
		return kgh;
	}

	public void setKgh(double kgh) {
		this.kgh = kgh;
	}

	public double getKsc() {
		return ksc;
	}

	public void setKsc(double ksc) {
		this.ksc = ksc;
	}

	public double getKlb() {
		return klb;
	}

	public void setKlb(double klb) {
		this.klb = klb;
	}

	public double getKqk() {
		return kqk;
	}

	public void setKqk(double kqk) {
		this.kqk = kqk;
	}

	public double getKwsx() {
		return kwsx;
	}

	public void setKwsx(double kwsx) {
		this.kwsx = kwsx;
	}

	public double getKzshp() {
		return kzshp;
	}

	public void setKzshp(double kzshp) {
		this.kzshp = kzshp;
	}

	public double getKzsmp() {
		return kzsmp;
	}

	public void setKzsmp(double kzsmp) {
		this.kzsmp = kzsmp;
	}

	public double getKjge() {
		return kjge;
	}

	public void setKjge(double kjge) {
		this.kjge = kjge;
	}

	public double getKqw() {
		return kqw;
	}

	public void setKqw(double kqw) {
		this.kqw = kqw;
	}

	public double getKhr() {
		return khr;
	}

	public void setKhr(double khr) {
		this.khr = khr;
	}

	public double getKqm() {
		return kqm;
	}

	public void setKqm(double kqm) {
		this.kqm = kqm;
	}

	public double getKtm() {
		return ktm;
	}

	public void setKtm(double ktm) {
		this.ktm = ktm;
	}

	public double getKxl() {
		return kxl;
	}

	public void setKxl(double kxl) {
		this.kxl = kxl;
	}

	public double getKfg() {
		return kfg;
	}

	public void setKfg(double kfg) {
		this.kfg = kfg;
	}

	public double getHfyv() {
		return hfyv;
	}

	public void setHfyv(double hfyv) {
		this.hfyv = hfyv;
	}

	public double getHfyl() {
		return hfyl;
	}

	public void setHfyl(double hfyl) {
		this.hfyl = hfyl;
	}

	public double getHff() {
		return hff;
	}

	public void setHff(double hff) {
		this.hff = hff;
	}

	public double getHlf() {
		return hlf;
	}

	public void setHlf(double hlf) {
		this.hlf = hlf;
	}

	public double getHsf() {
		return hsf;
	}

	public void setHsf(double hsf) {
		this.hsf = hsf;
	}

	public double getHhf() {
		return hhf;
	}

	public void setHhf(double hhf) {
		this.hhf = hhf;
	}

	public double getHhl() {
		return hhl;
	}

	public void setHhl(double hhl) {
		this.hhl = hhl;
	}

	public double getHhs() {
		return hhs;
	}

	public void setHhs(double hhs) {
		this.hhs = hhs;
	}

	public double getHfy() {
		return hfy;
	}

	public void setHfy(double hfy) {
		this.hfy = hfy;
	}

	public double getHzd() {
		return hzd;
	}

	public void setHzd(double hzd) {
		this.hzd = hzd;
	}

	public double getHzs() {
		return hzs;
	}

	public void setHzs(double hzs) {
		this.hzs = hzs;
	}

	public double getHds() {
		return hds;
	}

	public void setHds(double hds) {
		this.hds = hds;
	}

	public double getHfj() {
		return hfj;
	}

	public void setHfj(double hfj) {
		this.hfj = hfj;
	}

	public double getHxfkl() {
		return hxfkl;
	}

	public void setHxfkl(double hxfkl) {
		this.hxfkl = hxfkl;
	}

	public double getHxfcd() {
		return hxfcd;
	}

	public void setHxfcd(double hxfcd) {
		this.hxfcd = hxfcd;
	}

	public double getHgh() {
		return hgh;
	}

	public void setHgh(double hgh) {
		this.hgh = hgh;
	}

	public double getHyw() {
		return hyw;
	}

	public void setHyw(double hyw) {
		this.hyw = hyw;
	}

	public double getQff() {
		return qff;
	}

	public void setQff(double qff) {
		this.qff = qff;
	}

	public double getQlf() {
		return qlf;
	}

	public void setQlf(double qlf) {
		this.qlf = qlf;
	}

	public double getQsf() {
		return qsf;
	}

	public void setQsf(double qsf) {
		this.qsf = qsf;
	}

	public double getQhf() {
		return qhf;
	}

	public void setQhf(double qhf) {
		this.qhf = qhf;
	}

	public double getQhl() {
		return qhl;
	}

	public void setQhl(double qhl) {
		this.qhl = qhl;
	}

	public double getQhs() {
		return qhs;
	}

	public void setQhs(double qhs) {
		this.qhs = qhs;
	}

	public double getQzs() {
		return qzs;
	}

	public void setQzs(double qzs) {
		this.qzs = qzs;
	}

	public double getQfy() {
		return qfy;
	}

	public void setQfy(double qfy) {
		this.qfy = qfy;
	}

	public double getQzd() {
		return qzd;
	}

	public void setQzd(double qzd) {
		this.qzd = qzd;
	}

	public double getQgh() {
		return qgh;
	}

	public void setQgh(double qgh) {
		this.qgh = qgh;
	}

	public double getQyw() {
		return qyw;
	}

	public void setQyw(double qyw) {
		this.qyw = qyw;
	}

	public double getQsc() {
		return qsc;
	}

	public void setQsc(double qsc) {
		this.qsc = qsc;
	}

	public double getQschx() {
		return qschx;
	}

	public void setQschx(double qschx) {
		this.qschx = qschx;
	}

	public double getQqk() {
		return qqk;
	}

	public void setQqk(double qqk) {
		this.qqk = qqk;
	}

	public double getQzhs() {
		return qzhs;
	}

	public void setQzhs(double qzhs) {
		this.qzhs = qzhs;
	}

	public double getQgjf() {
		return qgjf;
	}

	public void setQgjf(double qgjf) {
		this.qgjf = qgjf;
	}

	public double getQfyf() {
		return qfyf;
	}

	public void setQfyf(double qfyf) {
		this.qfyf = qfyf;
	}

	public double getQsdf() {
		return qsdf;
	}

	public void setQsdf(double qsdf) {
		this.qsdf = qsdf;
	}

	public double getEds() {
		return eds;
	}

	public void setEds(double eds) {
		this.eds = eds;
	}

	public double getEfjl() {
		return efjl;
	}

	public void setEfjl(double efjl) {
		this.efjl = efjl;
	}

	public double getEfjv() {
		return efjv;
	}

	public void setEfjv(double efjv) {
		this.efjv = efjv;
	}

	public double getEljl() {
		return eljl;
	}

	public void setEljl(double eljl) {
		this.eljl = eljl;
	}

	public double getEljv() {
		return eljv;
	}

	public void setEljv(double eljv) {
		this.eljv = eljv;
	}

	public double getEmzl() {
		return emzl;
	}

	public void setEmzl(double emzl) {
		this.emzl = emzl;
	}

	public double getEzml() {
		return ezml;
	}

	public void setEzml(double ezml) {
		this.ezml = ezml;
	}

	public double getEkbl() {
		return ekbl;
	}

	public void setEkbl(double ekbl) {
		this.ekbl = ekbl;
	}

	public double getEfzl() {
		return efzl;
	}

	public void setEfzl(double efzl) {
		this.efzl = efzl;
	}

	public double getEfzcd() {
		return efzcd;
	}

	public void setEfzcd(double efzcd) {
		this.efzcd = efzcd;
	}

	public double getExfljl() {
		return exfljl;
	}

	public void setExfljl(double exfljl) {
		this.exfljl = exfljl;
	}

	public double getExfljs() {
		return exfljs;
	}

	public void setExfljs(double exfljs) {
		this.exfljs = exfljs;
	}

	public double getWxj() {
		return wxj;
	}

	public void setWxj(double wxj) {
		this.wxj = wxj;
	}

	public double getWxm() {
		return wxm;
	}

	public void setWxm(double wxm) {
		this.wxm = wxm;
	}

	public double getWxt() {
		return wxt;
	}

	public void setWxt(double wxt) {
		this.wxt = wxt;
	}

	public double getWxs() {
		return wxs;
	}

	public void setWxs(double wxs) {
		this.wxs = wxs;
	}

	public double getWxh() {
		return wxh;
	}

	public void setWxh(double wxh) {
		this.wxh = wxh;
	}

	public double getWxqj() {
		return wxqj;
	}

	public void setWxqj(double wxqj) {
		this.wxqj = wxqj;
	}

	public double getWxqm() {
		return wxqm;
	}

	public void setWxqm(double wxqm) {
		this.wxqm = wxqm;
	}

	public double getWxqt() {
		return wxqt;
	}

	public void setWxqt(double wxqt) {
		this.wxqt = wxqt;
	}

	public double getWxqs() {
		return wxqs;
	}

	public void setWxqs(double wxqs) {
		this.wxqs = wxqs;
	}

	public double getWxqh() {
		return wxqh;
	}

	public void setWxqh(double wxqh) {
		this.wxqh = wxqh;
	}

	public double getSwsx() {
		return swsx;
	}

	public void setSwsx(double swsx) {
		this.swsx = swsx;
	}

	public double getSff() {
		return sff;
	}

	public void setSff(double sff) {
		this.sff = sff;
	}

	public double getSlf() {
		return slf;
	}

	public void setSlf(double slf) {
		this.slf = slf;
	}

	public double getSsf() {
		return ssf;
	}

	public void setSsf(double ssf) {
		this.ssf = ssf;
	}

	public double getShf() {
		return shf;
	}

	public void setShf(double shf) {
		this.shf = shf;
	}

	public double getSzd() {
		return szd;
	}

	public void setSzd(double szd) {
		this.szd = szd;
	}

	public double getSgh() {
		return sgh;
	}

	public void setSgh(double sgh) {
		this.sgh = sgh;
	}

	public double getSsc() {
		return ssc;
	}

	public void setSsc(double ssc) {
		this.ssc = ssc;
	}

	public double getBlf() {
		return blf;
	}

	public void setBlf(double blf) {
		this.blf = blf;
	}

	public double getBff() {
		return bff;
	}

	public void setBff(double bff) {
		this.bff = bff;
	}

	public double getBsf() {
		return bsf;
	}

	public void setBsf(double bsf) {
		this.bsf = bsf;
	}

	public double getBhf() {
		return bhf;
	}

	public void setBhf(double bhf) {
		this.bhf = bhf;
	}

	public double getBgh() {
		return bgh;
	}

	public void setBgh(double bgh) {
		this.bgh = bgh;
	}

	public double getBsc() {
		return bsc;
	}

	public void setBsc(double bsc) {
		this.bsc = bsc;
	}

	public double getKqgh() {
		return kqgh;
	}

	public void setKqgh(double kqgh) {
		this.kqgh = kqgh;
	}

	public double getKqgf() {
		return kqgf;
	}

	public void setKqgf(double kqgf) {
		this.kqgf = kqgf;
	}

	public double getKqgs() {
		return kqgs;
	}

	public void setKqgs(double kqgs) {
		this.kqgs = kqgs;
	}
}
