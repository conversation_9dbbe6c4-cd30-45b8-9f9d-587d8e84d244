package com.tool.PlayerKill;

import java.util.List;

/**
 * PK杂项
 * 
 * <AUTHOR>
 * 
 */
public class PKMixdeal {
	public static long YW = 36000000;

	/**
	 * 获取参与PK者
	 */
	public static int getPKNmae(String role, List<String> team, int ns) {
		int w = 1;
		for (int i = 0; i < team.size(); i++) {
			if (team.get(i).equals(role)) {
				w = i + 1;
				break;
			}
		}
		return getPKSign(w, team.size(), ns);
	}

	/** 位列 人数 分配 */
	public static int getPKSign(int w, int s, int ns) {
		return ns / s + (ns % s >= w ? 1 : 0);
	}

	/**
	 * 获取坐牢的时间
	 */
	public static long getJailTime(int c) {
		long time = 7200000;
		if (c <= 1)
			return time;
		if (c > 3)
			c = 3;
		time = time * c;
		return time;
	}

}
