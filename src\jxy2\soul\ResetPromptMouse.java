package jxy2.soul;

import org.come.Frame.ZhuFrame;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;

public class ResetPromptMouse implements MouseListener {
    public int type;//0-物品、1-技能
    public ResetSoulSkillJPanl resetSoulSkillJPanl;
    /**0-物品、1-技能 */
    public ResetPromptMouse(int type,ResetSoulSkillJPanl resetSoulSkillJPanl) {
        this.type = type;
        this.resetSoulSkillJPanl = resetSoulSkillJPanl;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (type==0) {
            ZhuFrame.getZhuJpanel().creatgoodtext(GoodsName(resetSoulSkillJPanl.goodstable.getGoodsname()));
        }else {
            String[] skillID = resetSoulSkillJPanl.builder.toString().split("\\|");
            for (int i = 0; i < skillID.length; i++) {
                if (type == 1) {
                    Skill skill = UserMessUntil.getSkillId(skillID[0]);
                    ZhuFrame.getZhuJpanel().creatlingtext3(skill);
                } else {
                    Skill skill = UserMessUntil.getSkillId(skillID[1]);
                    ZhuFrame.getZhuJpanel().creatlingtext3(skill);
                }
            }
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
        FormsManagement.HideForm(628);
    }

    public static Goodstable GoodsName(String Name){
        switch (Name){
            case "普通魔晶":
                return UserMessUntil.getgoodstable(new BigDecimal(81268));
            case "百年魔晶":
                return UserMessUntil.getgoodstable(new BigDecimal(81269));
            case "千年魔晶":
                return UserMessUntil.getgoodstable(new BigDecimal(81270));
            case "仙灵魔晶":
                return UserMessUntil.getgoodstable(new BigDecimal(81271));
            default:
                return null;
        }
    }
}
