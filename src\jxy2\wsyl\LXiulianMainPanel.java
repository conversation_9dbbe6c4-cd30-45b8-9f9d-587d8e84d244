package jxy2.wsyl;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.RoleSummoning;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

public class LXiulianMainPanel extends JPanel {
    private WsylBtn promoteBtn // 提升修炼点按钮
            , exchangeBtn; // 一键兑换按钮
    private JLabel currentPoin // 当前灵犀等级
            , zhaohuanshou // 召唤兽名称
            , trainingProgress // 当前修炼进度
            , needExperience // 所需经验
            , currentExperience // 当前经验
            , needMoney // 所需金钱
            ;
    private int numExp;
    private long needMoneyNum = 50000;//扣出游戏币
    private long needExperienceNum = 6000000;//扣除经验

    public LXiulianMainPanel() {
        this.setPreferredSize(new Dimension(370,354));
        this.setOpaque(false);
        this.setLayout(null);
        Juitil.addClosingButtonToPanel(this,124,370);
        promoteBtn = new WsylBtn(ImgConstants.tz85,1,4,"","提升修炼",null);
        promoteBtn.setBounds(235, 307, 82, 27);
        this.add(promoteBtn);
        exchangeBtn = new WsylBtn(ImgConstants.tz85,1,5,"","一键修炼",null);
        exchangeBtn.setBounds(75, 307, 82, 27);
        this.add(exchangeBtn);

        // 组件初始化
        currentPoin = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        zhaohuanshou = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        trainingProgress = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        needExperience = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        currentExperience = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        needMoney = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY14);
        needExperience.setText(needExperienceNum+"");
        needMoney.setText(needMoneyNum + "");


        // 组件设置大小和方位
        zhaohuanshou.setBounds(85, 36, 220, 18);
        currentPoin.setBounds(270, 36, 45, 16);
        trainingProgress.setBounds(240, 67, 150, 17);

        needExperience.setBounds(140, 158, 150, 16);
        currentExperience.setBounds(140, 158+36, 150, 16);
        needMoney.setBounds(140, 158+36*2, 150, 16);

        // 组件内容居中
        currentPoin.setHorizontalAlignment(SwingConstants.CENTER);
        trainingProgress.setHorizontalAlignment(SwingConstants.CENTER);
        needExperience.setHorizontalAlignment(SwingConstants.CENTER);
        currentExperience.setHorizontalAlignment(SwingConstants.CENTER);
        needMoney.setHorizontalAlignment(SwingConstants.CENTER);
        zhaohuanshou.setHorizontalAlignment(SwingConstants.LEFT);

        // 组件设置字体颜色
        currentPoin.setForeground(Color.white);
        trainingProgress.setForeground(Color.white);
        needExperience.setForeground(Color.white);
        currentExperience.setForeground(Color.white);
        needMoney.setForeground(Color.white);

        // 组建设置字体
        currentPoin.setFont(UIUtils.FZCY_HY14);
        trainingProgress.setFont(UIUtils.FZCY_HY14);
        needExperience.setFont(UIUtils.FZCY_HY14);
        currentExperience.setFont(UIUtils.FZCY_HY14);
        needMoney.setFont(UIUtils.FZCY_HY14);
        zhaohuanshou.setFont(UIUtils.FZCY_HY14);

        // 组件透明化
        currentPoin.setOpaque(false);
        trainingProgress.setOpaque(false);
        needExperience.setOpaque(false);
        currentExperience.setOpaque(false);
        needMoney.setOpaque(false);
        zhaohuanshou.setOpaque(false);

        // 添加组件
        this.add(currentPoin);
        this.add(trainingProgress);
        this.add(needExperience);
        this.add(currentExperience);
        this.add(needMoney);
        this.add(zhaohuanshou);
    }
    /**
     * 面板获取数据
     * @param pet
     */
    public int xl,ds;
    public void panelGetData(RoleSummoning pet,WsylJPanel wsylJPanel) {
        String lingxi = pet.getLingxi();
        if (lingxi.isEmpty()) {
            lingxi = "Lx=0&Lv=0&Point=0&Open=11001_0|11002_0|11003_0|11004_0|11005_0|11006_0|11007_0|11008_0|11009_0|11010_0|11026_0|11027_0|11028_0|11029_0|11045_0|11046_0|11047_0|11048_0|11049_0";
        }
        String[] param = lingxi.split("&");
        // 修炼等级
        int xl = Integer.parseInt(param[1].split("=")[1]);
        // 灵犀点数
        int ds = Integer.parseInt(param[2].split("=")[1]);
        this.xl =xl;
        this.ds =ds;
        if (ds == 175) {
            xl = 175;
            this.getTrainingProgress().setFont(new Font("黑体",Font.PLAIN,14));
            this.getTrainingProgress().setForeground(Color.RED);
        }

        // 当前经验值
        this.getCurrentExperience().setText(pet.getExp().toString());
        // 当前修炼进度
        this.getTrainingProgress().setText(ds == 175 ? "登峰造极" :  (xl + "/" + (ds+9)));
        this.numExp = (int)(xl / (ds+1.0) * 166);
        // 当前灵犀点数
        this.getCurrentPoin().setText(ds + "");
        // 召唤兽名称
        this.getZhaohuanshou().setText(pet.getSummoningname());
    }


    public String[] str = {"兑换灵犀所需","所需经验","当前经验","所需金钱","当前金钱"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz67, 0, 0, 370, 354, 1);
        Juitil.ImngBack(g, Juitil.tz237, 6, 8, 370-13, 354-15, 1);
        g.drawImage(Juitil.icon.getImage(), 14, 176-30, 338, 3, null);

        Juitil.TextBackground(g, "灵犀值：1000/1000", 16, 25, 120-26, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);
        for (int i = 0; i < str.length; i++) {
            Juitil.TextBackground(g, str[i], 16, 25, 120+i*36, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);
        }
        for (int i = 0; i < 4; i++) {
            Juitil.ImngBack(g, Juitil.tz26, 135, 155+(i)*35, 166, 25, 1);
        }
        Juitil.TextBackground(g, "召唤兽：", 16, 25, 35, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);
        Juitil.TextBackground(g, "灵犀等级", 16, 188, 35, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);
        Juitil.ImngBack(g, Juitil.tz26, 264, 33, 60, 25, 1);
        Juitil.TextBackground(g, "当前灵修修炼进度", 16, 25, 86-20, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);
        Juitil.ImngBack(g, Juitil.tz26, 165, 84-20, 166, 25, 1);
        Util.drawPrice(g, RoleData.getRoleData().getLoginResult().getGold(), 140, 169+36*3);


    }

    public JLabel getZhaohuanshou() {
        return zhaohuanshou;
    }

    public void setZhaohuanshou(JLabel zhaohuanshou) {
        this.zhaohuanshou = zhaohuanshou;
    }

    public WsylBtn getPromoteBtn() {
        return promoteBtn;
    }

    public void setPromoteBtn(WsylBtn promoteBtn) {
        this.promoteBtn = promoteBtn;
    }

    public WsylBtn getExchangeBtn() {
        return exchangeBtn;
    }

    public void setExchangeBtn(WsylBtn exchangeBtn) {
        this.exchangeBtn = exchangeBtn;
    }

    public JLabel getCurrentPoin() {
        return currentPoin;
    }

    public void setCurrentPoin(JLabel currentPoin) {
        this.currentPoin = currentPoin;
    }

    public JLabel getTrainingProgress() {
        return trainingProgress;
    }

    public void setTrainingProgress(JLabel trainingProgress) {
        this.trainingProgress = trainingProgress;
    }

    public JLabel getNeedExperience() {
        return needExperience;
    }

    public void setNeedExperience(JLabel needExperience) {
        this.needExperience = needExperience;
    }

    public JLabel getCurrentExperience() {
        return currentExperience;
    }

    public void setCurrentExperience(JLabel currentExperience) {
        this.currentExperience = currentExperience;
    }

    public JLabel getNeedMoney() {
        return needMoney;
    }

    public void setNeedMoney(JLabel needMoney) {
        this.needMoney = needMoney;
    }


    public int getNumExp() {
        return numExp;
    }

    public void setNumExp(int numExp) {
        this.numExp = numExp;
    }

    public long getNeedMoneyNum() {
        return needMoneyNum;
    }

    public void setNeedMoneyNum(long needMoneyNum) {
        this.needMoneyNum = needMoneyNum;
    }

    public long getNeedExperienceNum() {
        return needExperienceNum;
    }

    public void setNeedExperienceNum(long needExperienceNum) {
        this.needExperienceNum = needExperienceNum;
    }

    public int getXl() {
        return xl;
    }

    public void setXl(int xl) {
        this.xl = xl;
    }

    public int getDs() {
        return ds;
    }

    public void setDs(int ds) {
        this.ds = ds;
    }
}
