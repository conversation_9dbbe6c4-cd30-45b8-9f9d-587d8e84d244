package jxy2.xbao;

import com.tool.tcpimg.ChatBox;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.Objects;
@SuppressWarnings("all")
public class XbaoJpanel extends JPanel {
     private  XbaoBrn xbaoEquiBtn,
                      xbaoAttrBtn,
                      previousPage,//上一页 玄宝图鉴
                      xbaoLibrary,
                    transformationBtn,
                      nextPage;//下一页
     private int type = 0;
    private JLabel[] xuanbaoListLabel = new JLabel[20];// 用来存放20个玄宝表格
    private JLabel[] xuanbaoframe = new JLabel[20];// 边框
    private JLabel[] choseGoodsJLabel = new JLabel[20];// 边框
    private JLabel[] wearableImg = new JLabel[3];//3个穿戴装备
    private JLabel[] wearableback = new JLabel[3];//3个穿戴装备
    private JLabel[] acGoodsLabel  = new JLabel[8];
    private boolean[] isClick;//被点击
    public int[] maxlv = {40,60,80,100,150};
    public XbaoJpanel() {
        this.setPreferredSize(new Dimension(617,480));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        // 初始化透明度为0.5F（未激活状态）
        for (int i = 0; i < 6; i++) {
            skillAlpha[i] = 0.5f;
        }
        ImageIcon[] path = Util.SwitchUI==1?Juitil.bt1:Juitil.bt2;
        xbaoEquiBtn = new XbaoBrn(path, 1, "玄宝装备", this,0);
        xbaoEquiBtn.btnchange(2);
        xbaoEquiBtn.setBounds(42, 49, 30, 80);
        this.add(xbaoEquiBtn);

        xbaoAttrBtn = new XbaoBrn(path, 1, "玄宝属性", this,1);
        xbaoAttrBtn.setBounds(42, 130, 30, 80);
        this.add(xbaoAttrBtn);

        //上一页
         path = Util.SwitchUI==1?Juitil.bt3:Juitil.bt5;
        previousPage =new XbaoBrn(path, -1, "", this,2);
        previousPage.setIcon(Juitil.tz370);
        previousPage.setBounds(539, 442, 18, 18);
        this.add(previousPage);
        //下一页
        path = Util.SwitchUI==1?Juitil.bt4:Juitil.bt6;
        nextPage =new XbaoBrn(path, -1, "", this,3);
        nextPage.setIcon(Juitil.tz371);
        nextPage.setBounds(560, 442, 18, 18);
        this.add(nextPage);




        for (int i = 0; i < 20; i++) {
            int row = i % 10 * 51;
            int col = i / 10 * 51;
            xuanbaoListLabel[i] = new JLabel();
            xuanbaoListLabel[i].setBounds(74+row, 332+col, 50, 50);
            xuanbaoListLabel[i].addMouseListener(new XuanbaoMouse(i,this));
            this.add(xuanbaoListLabel[i]);
            xuanbaoframe[i] = new JLabel();
            xuanbaoframe[i].setBounds(74+row, 332+col, 50, 50);
            this.add(xuanbaoframe[i]);
            choseGoodsJLabel[i] = new JLabel();
            choseGoodsJLabel[i].setBounds(74+row, 332+col, 50, 50);
            this.add(choseGoodsJLabel[i]);
        }
        for (int i = 0; i < 3; i++) {
            wearableImg[i] = new JLabel();
            wearableImg[i].setBounds(84, 81+i*60, 51, 51);
            wearableImg[i].addMouseListener(new XuanbaoEquipMouse(i,this));
            this.add(wearableImg[i]);
            wearableback[i] = new JLabel();
            wearableback[i].setBounds(84, 81+i*60, 51, 51);
            this.add(wearableback[i]);
        }

        for (int i = 0; i < acGoodsLabel.length; i++) {
            int row = i % 4 * 51;
            int col = i / 4 * 51;
            acGoodsLabel[i] = new JLabel();
            acGoodsLabel[i].setBounds(74+row, 330+col, 51, 51);
            acGoodsLabel[i].addMouseListener(new XuanbaoMouse(i,this));
            this.add(acGoodsLabel[i]);

        }

        xzimg = new JLabel();
        xzimg.setBounds(245, 96, 168, 168);
        this.add(xzimg);

        xzname = TeststateJpanel.GJpanelText(new Color(225,240,238),UIUtils.NEWTX_HY17);
        xzname.setBounds(529, 48, 168, 20);
        this.add(xzname);

        baolvl = TeststateJpanel.GJpanelText(new Color(255,231,185),UIUtils.TEXT_FONT15);
        baolvl.setVisible(false);
        baolvl.setBounds(126, 267, 130, 20);
        this.add(baolvl);

        baoexe = TeststateJpanel.GJpanelText(new Color(225,240,238),UIUtils.TEXT_FONT15);
        baoexe.setVisible(false);
        baoexe.setBounds(145, 294, 130, 20);
        this.add(baoexe);

        liteicon = new JLabel();
        liteicon.setIcon(Juitil.tz367);
        liteicon.setBounds(438, 45, 148, 28);
        this.add(liteicon);

        for (int i = 0; i < xzfouricon.length; i++) {
            xzfouricon[i] = new JLabel();
            xzfouricon[i].setBounds(245, 96 + i * 20, 64, 64);
            add(xzfouricon[i]);
            Storecolors[i] = new JLabel();
            add(Storecolors[i]);
            colorsicon[i] = new JLabel();
            colorsicon[i].setBounds(245, 96 + i * 20, 64, 64);
            add(colorsicon[i]);

            xzfouricon[i].addMouseListener(new XzFourIconMouse(i,this));
            // 调整组件层级，确保colorsicon在xzfouricon上面
            setComponentZOrder(colorsicon[i], 0);
            setComponentZOrder(xzfouricon[i], 1);
        }
        richLabel = new RichLabel("",UIUtils.TEXT_FONT,130);
        richLabel.setBounds(245, 96, 130, 168);
        this.add(richLabel);

        richLabel2 = new RichLabel("",UIUtils.TEXT_FONT,100);
        richLabel2.setBounds(245, 96, 100, 40);
        this.add(richLabel2);

        BasicSkills = new RichLabel("",UIUtils.TEXT_FONT15,269);
        BasicSkills.setBounds(301, 87, 269, 100);
        Dimension d = BasicSkills.computeSize(269);
        BasicSkills.setSize(d);
        BasicSkills.setPreferredSize(d);
        this.add(BasicSkills);

        for (int i = 0; i < min_xzfouricon.length; i++) {
            min_xzfouricon[i] = new JLabel();
            min_xzfouricon[i].setIcon(Juitil.tz372);
            min_xzfouricon[i].setVisible(false);
            min_xzfouricon[i].setBounds(294+i*46, 234, 36, 36);
            add(min_xzfouricon[i]);
        }
        for (int i = 0; i < min_Storecolors.length; i++) {
            min_Storecolors[i] = new JLabel();
            min_Storecolors[i].setVisible(false);
            min_Storecolors[i].setBounds(294+i*46, 234, 36, 36);
            add(min_Storecolors[i]);
        }

        for (int i = 0; i < chatBoxBackImg.length; i++) {
            chatBoxBackImg[i] = new JLabel();
            chatBoxBackImg[i].setIcon(Juitil.tz373);
            chatBoxBackImg[i].setVisible(false);
            chatBoxBackImg[i].setBounds(294, 234+i*56, 56, 16);
            this.add(chatBoxBackImg[i]);
        }

        // 初始化技能图标
        for (int i = 0; i < 6; i++) {
            final int index = i;  // 用于内部类访问
            skillimg[i] = new JLabel() {
                @Override
                protected void paintComponent(Graphics g) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    // 设置透明度
                    g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, skillAlpha[index]));
                    super.paintComponent(g2d);
                    g2d.dispose();
                }
            };

            skillimg[i].setBounds(294 + (i % 2) * 12, 314 + (i / 2) * 12, 12, 12);
            this.add(skillimg[i]);
        }

        this.jScrollPane = new JScrollPane(BasicSkills);
        this.jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        this.jScrollPane.getVerticalScrollBar().setUI(null);
        this.jScrollPane.getVerticalScrollBar().setUnitIncrement(20);
        this.jScrollPane.getViewport().setOpaque(false);
        this.jScrollPane.setOpaque(false);
        jScrollPane.setVisible( false);
        this.jScrollPane.setBounds(301, 87, 269, 100);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        this.jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(this.jScrollPane);
        String paths = Util.SwitchUI==1?"0x6FAC1096":"0x6FAB1103";
        xbaoLibrary = new XbaoBrn(paths, 1, "玄宝图鉴", 5,this);
        xbaoLibrary.setBounds(428, 441, 61, 18);
        this.add(xbaoLibrary);
        // 转换
        paths = Util.SwitchUI==1?"0x6FAC1097":"0x6FAB1014";
        transformationBtn = new XbaoBrn(paths, 1,  "修炼", 6,this);
        transformationBtn.setBounds(228, 295, 34, 18);
        transformationBtn.setVisible(false);
        this.add(transformationBtn);
    }
    public ImageIcon EqIcon  = Juitil.tz157;
    /**
     * 设置装备选中状态
     * @param pos 装备位置索引
     * @param size 装备图标大小
     */
    public void EqPain(int pos, int size) {
        choseGoodsJLabel[pos].setIcon(EqIcon);
        if (pos < 3 && size == 50) {
            choseGoodsJLabel[pos].setBounds(84, 81 + pos * 60, 51, 51);
        } else {
            if (XuanbaoMouse.shijian){
                choseGoodsJLabel[pos].setBounds(74 + (pos % 10 * 51), 332 + (pos / 10 * 51), 50, 50);
            }else {
                choseGoodsJLabel[pos].setBounds(74 + (pos % 4 * 51), 330 + (pos / 4 * 51), 50, 50);
            }

        }
    }

    /**
     * 清除装备选中状态
     * @param pos 装备位置索引
     */
    public void EqClearText(int pos) {
        choseGoodsJLabel[pos].setIcon(null);
        if (pos < 3) {
            choseGoodsJLabel[pos].setBounds(84, 81 + pos * 60, 51, 51);
        } else {
            choseGoodsJLabel[pos].setBounds(74 + (pos % 10 * 51), 332 + (pos / 10 * 51), 50, 50);
        }
    }

    /**
     * 穿戴装备触发组件绘制显示坐标描述属性展示
     * @param goodPosition 装备位置索引
     * @param isShow 是否显示
     */
    public void drawEquip(int goodPosition, boolean isShow) {
        for (int i = 0; i < wearableImg.length; i++) {
            if (i == goodPosition){
                wearableImg[i].setBounds(isShow ? 85 : 84, (isShow ? 82 : 81) + i * 60, 51, 51);
                wearableback[i].setBorder(BorderFactory.createLineBorder(Color.red, 2));
            } else {
                wearableback[i].setBorder(null);
            }
        }
    }
    /**设置组件可见度*/
    public void setComponentVisible(boolean visible) {

        xzname.setVisible(visible);
        xbaoLibrary.setVisible(visible);
        transformationBtn.setVisible(!visible);
        for (JLabel jLabel : xzfouricon) {
            jLabel.setVisible(visible);
        }
        for (JLabel jLabel : wearableback) {
            jLabel.setVisible(visible);
        }
        for (JLabel jLabel : wearableImg) {
            jLabel.setVisible(visible);
        }

        for (JLabel jLabel : acGoodsLabel) {
            jLabel.setVisible(!visible);
            jLabel.setBorder(BorderFactory.createEmptyBorder());
        }
        for (JLabel jLabel : colorsicon) {
            jLabel.setVisible(visible);
        }
        for (JLabel jLabel : xuanbaoListLabel) {
            jLabel.setVisible(visible);
        }
        for (JLabel jLabel : min_xzfouricon) {
            jLabel.setVisible(!visible);
        }
        for (JLabel jLabel : min_Storecolors) {
            jLabel.setVisible(!visible);
        }
        for (JLabel jLabel : chatBoxBackImg) {
            jLabel.setVisible(!visible);
        }

        for (JLabel jLabel : skillimg) {
            jLabel.setVisible(!visible);
        }

        richLabel.setVisible( visible);
        richLabel2.setVisible(visible);
        liteicon.setVisible(visible);
        baolvl.setVisible(!visible);
        baoexe.setVisible(!visible);
        BasicSkills.setVisible(!visible);
        jScrollPane.setVisible(!visible);


    }
    private JLabel baolvl,baoexe;
    private RichLabel BasicSkills;
    private JLabel[] min_xzfouricon = new JLabel[5];//4个选中图标
    private JLabel[] min_Storecolors = new JLabel[5];//存放4种颜色
    private ChatBox chatBox = new ChatBox();
    private JLabel[] chatBoxBackImg = new JLabel[3];
    private JLabel[] skillimg = new JLabel[6];//放置三个激活技能图标
    private float[] skillAlpha = new float[6]; // 每个技能图标的透明度
    private JScrollPane jScrollPane;
    /**设置玄宝属性界面可见组件*/
    public void setAttributeVisible(boolean visible) {
        if (RoleXuanbao.getRoleXuanbao().choseBao==null)return;
        Xuanbao xuanbao = UserMessUntil.getXuanabao(RoleXuanbao.getRoleXuanbao().choseBao.getBaoname());
        if (xuanbao!=null){
            for (int i = 0; i < 5; i++) {
                min_xzfouricon[i].setIcon(null);
                min_Storecolors[i].setIcon(null);

            }
            for (int i = 0; i < 3; i++) {
                chatBoxBackImg[i].setIcon(null);
            }
            for (int i = 0; i < 6; i++) {
                skillimg[i].setIcon(null);
            }
            Xuanbao choseBao = RoleXuanbao.getRoleXuanbao().choseBao;
            BasicSkills.setText("#G"+xuanbao.getBaoskillthree());
            baolvl.setText(choseBao.getBaolvl()+"级");
            baoexe.setText(choseBao.getBaoexe()+"/"+expTable[choseBao.getBaolvl().intValue()]);
            XuanbaoAcCuorosIcon(xuanbao);
            Dimension d = BasicSkills.computeSize(269);
            BasicSkills.setSize(d);
            BasicSkills.setPreferredSize(d);
            XuanMsgFrame.getXuanMsgFrame().getXuanMsgJPanel().ObtainImagesBasedOnColor(
                    xuanbao.getBaoskill().split("&")[0].split(","),
                    xuanbao.getBaoskillone().split("&")[0].split(","),
                    xuanbao.getBaoskilltwo().split("&")[0].split(","),skillimg
            );

            // 计算实际的技能组数和文本内容
            int skillGroupCount = 0;
            String[] skillTexts = new String[3];
            if (xuanbao.getBaoskill() != null && !xuanbao.getBaoskill().isEmpty()) {
                skillTexts[skillGroupCount] = xuanbao.getBaoskill().split("&")[1];
                skillGroupCount++;
            }
            if (xuanbao.getBaoskillone() != null && !xuanbao.getBaoskillone().isEmpty()) {
                skillTexts[skillGroupCount] = xuanbao.getBaoskillone().split("&")[1];
                skillGroupCount++;
            }
            if (xuanbao.getBaoskilltwo() != null && !xuanbao.getBaoskilltwo().isEmpty()){
                skillTexts[skillGroupCount] = xuanbao.getBaoskilltwo().split("&")[1];
                skillGroupCount++;
            }

            // 为每个技能文本计算位置
            int currentY = 314; // 起始Y坐标
            int textHeight;
            for (int i = 0; i < skillGroupCount; i++) {
                if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                    // 设置chatBoxBackImg的位置在当前文本开始处
                    chatBoxBackImg[i].setIcon(Juitil.tz373);
                    chatBoxBackImg[i].setBounds(294, currentY, 56, 16);
                    // 计算当前文本的渲染高度
                    textHeight = calculateTextHeight(skillTexts[i], 208, UIUtils.TEXT_FONT15);
                    // 更新下一个文本的Y坐标
                    currentY += textHeight-4; // 4像素间距
                }
            }

            // 最后设置主ChatBox的内容（合并所有文本）
            StringBuilder combinedText = new StringBuilder();
            for (int i = 0; i < skillGroupCount; i++) {
                if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                    if (combinedText.length() > 0) {
                        combinedText.append("#r"); // 换行标记
                    }
                    combinedText.append("#c9f9f9f").append(skillTexts[i]);
                }
            }
            if (combinedText.length() > 0) {
                chatBox.removeAddText(combinedText.toString(), 208, UIUtils.TEXT_FONT15);
            }
            for (int i = 0; i < 6; i++) {
                int row = i % 2 * 15;
                int baseY = chatBoxBackImg[i/2].getY(); // 获取对应chatBoxBackImg的Y坐标
                skillimg[i].setBounds(304 + row, baseY + 2, 12, 12); // +2是微调，使图标垂直居中对齐
            }
        }
        if (!visible){
            // 装备界面 10x2布局
            for (int i = 0; i < 20; i++) {
                int row = i % 10 * 51;
                int col = i / 10 * 51;
                xuanbaoListLabel[i].setBounds(74+row, 332+col, 50, 50);
                xuanbaoframe[i].setBounds(74+row, 332+col, 50, 50);
                choseGoodsJLabel[i].setBounds(74+row, 332+col, 50, 50);
            }
            previousPage.setBounds(539, 442, 18, 18);
            nextPage.setBounds(560, 442, 18, 18);
        }else {
            // 属性界面 4x2布局
            for (int i = 0; i < 20; i++) {
                // 计算4x2布局的坐标
                int row = i % 4 * 51;
                int col = i / 4 * 51;
                if(i < 8) {
                    xuanbaoListLabel[i].setBounds(74+row, 330+col, 50, 50);
                    xuanbaoframe[i].setBounds(74+row, 330+col, 50, 50);
                    choseGoodsJLabel[i].setBounds(74+row, 330+col, 50, 50);
                } else {
                    // 对于超出4x2范围的项目，仍然使用4x2布局计算位置
                    xuanbaoListLabel[i].setBounds(74+row, 330+col, 50, 50);
                    xuanbaoframe[i].setBounds(74+row, 330+col, 50, 50);
                    choseGoodsJLabel[i].setBounds(74+row, 330+col, 50, 50);
                }
            }
            previousPage.setBounds(46, 365, 18, 18);
            nextPage.setBounds(46, 388, 18, 18);
        }

    }



    /**展示组件可见度以及选中后组件的加载*/
    private JLabel xzimg,xzname;//选中主图标
    private JLabel[] xzfouricon = new JLabel[5];//4个选中图标
    private JLabel[] Storecolors = new JLabel[5];//存放4种颜色
    private JLabel liteicon;//右上角图形
    private JLabel[] colorsicon = new JLabel[5];//颜色图形
    private RichLabel richLabel,richLabel2;//右上角文本绘制
    /**
     * 展示组件可见度以及选中后组件的加载
     * 装备界面
     * @param choseBao 选中的玄宝对象
     */
    public void LoadingOfSelected(Xuanbao choseBao) {
        // 中心点坐标
        int centerX = 330;  // 中心X坐标
        int centerY = 183;  // 中心Y坐标
        int offset = 105;   // 图片距离中心的偏移量
        int imageSize = 64; // 图片大小

        Xuanbao nexuanbao = UserMessUntil.getXuanabao(choseBao.getBaoname());
        if (nexuanbao != null) {
            xzname.setText(nexuanbao.getBaoname());
            xzimg.setIcon(GoodsListFromServerUntil.xbaoWdfile(UserMessUntil.getXuanabaoSikn(choseBao.getBaoname()), 168, 168));
            if (type == 0) {
                for (int i = 0; i < 5; i++) {
                    xzfouricon[i].setIcon(null);
                    xzfouricon[i].setVisible(false);
                    colorsicon[i].setVisible(false);
                    colorsicon[i].setIcon(null);
                    xzfouricon[i].setBorder(BorderFactory.createEmptyBorder());
                    // 重新设置层级顺序
                    setComponentZOrder(colorsicon[i], 0);
                    setComponentZOrder(xzfouricon[i], 1);
                }
                String[] text = nexuanbao.getBaodescribe().split("&");
                if (text.length > 1) {
                    richLabel.setBounds(495, 75, 100, 40);
                    richLabel.setText(text[0]);
                    richLabel2.setBounds(460, 117, 130, 168);
                    richLabel2.setText("#c4f798b" + text[2]);
                }
                XuanbaoCuorosIcon(nexuanbao, centerX, imageSize, centerY, offset,choseBao);
            }
        }

    }

    /**
     * 玄宝属性界面
     * 根据玄宝的属性绘制其技能图标和颜色
     * @param nexuanbao 玄宝对象，包含玄宝的属性信息
     */
    public void XuanbaoAcCuorosIcon(Xuanbao nexuanbao) {
        String[] xyConfig = nexuanbao.getXyConfig().split("\\|");
        String[] split = RoleXuanbao.getRoleXuanbao().choseBao.getBaomarkactive().split("\\|");
        for (int i = 0; i < xyConfig.length; i++) {
            boolean flag = RoleXuanbao.getRoleXuanbao().choseBao.getBaolvl().intValue()>=maxlv[i];
                if (split[i].equals("1")){
                    min_xzfouricon[i].setIcon(Juitil.tz388);
                }else {
                    min_xzfouricon[i].setIcon(flag?Juitil.tz387:Juitil.tz372);
                }
                min_xzfouricon[i].setBounds(294+i*46, 234, 36, 36);
                drawSkillIcon(xyConfig[i], 294+i*46, 234,i);
        }

    }

    /**
     * 根据传入的宝物信息和位置参数，绘制炫宝图标
     * 此方法负责根据宝物的技能和名称，在特定位置绘制相应的图标和技能效果
     *
     * @param nexuanbao 宝物对象，包含宝物的技能和名称信息
     * @param centerX 图标中心的X坐标
     * @param imageSize 图标的大小
     * @param centerY 图标中心的Y坐标
     * @param offset 图标之间的偏移量
     */
    private void XuanbaoCuorosIcon(Xuanbao nexuanbao, int centerX, int imageSize, int centerY, int offset,Xuanbao choseBao) {
        //当前玄宝等级，依据开启玄印
        System.out.println(choseBao.getBaolvl()+"??");
        String[] xyConfig = nexuanbao.getXyConfig().split("\\|");
        int[][] positions;
        // 根据配置数量动态生成坐标
        switch (xyConfig.length) {
            case 3: // 三孔布局（三角形）
                positions = new int[][]{
                    {centerX - imageSize / 2, centerY - offset - imageSize / 2},  // 上
                    {centerX - offset - imageSize / 2, centerY + offset/2 - imageSize / 2},  // 左下
                    {centerX + offset - imageSize / 2, centerY + offset/2 - imageSize / 2}   // 右下
                };
                break;
            case 4: // 四孔布局（十字形）
                positions = new int[][]{
                    {centerX - imageSize / 2, centerY - offset - imageSize / 2},  // 上
                    {centerX - offset - imageSize / 2, centerY - imageSize / 2},  // 左
                    {centerX - imageSize / 2, centerY + offset - imageSize / 2},  // 下
                    {centerX + offset - imageSize / 2, centerY - imageSize / 2}   // 右
                };
                break;
            case 5: // 五孔布局（五边形）
                positions = new int[5][2];
                // 计算五边形的顶点坐标
                for (int i = 0; i < 5; i++) {
                    double angle = Math.PI / 2 + i * 2 * Math.PI / 5;  // 从顶部开始，逆时针排列
                    positions[i][0] = (int)(centerX + offset * Math.cos(angle) - (double) imageSize / 2);
                    positions[i][1] = (int)(centerY - offset * Math.sin(angle) - (double) imageSize / 2);
                }
                break;
            default: // 默认使用十字形布局
                positions = new int[xyConfig.length][2];
                for (int i = 0; i < xyConfig.length; i++) {
                    double angle = 2 * Math.PI * i / xyConfig.length;
                    positions[i][0] = (int)(centerX + offset * Math.cos(angle) - (double) imageSize / 2);
                    positions[i][1] = (int)(centerY + offset * Math.sin(angle) - (double) imageSize / 2);
                }
        }
        String[] split = choseBao.getBaomarkactive().split("\\|");
        String[] id = choseBao.getBaomarkid().split("\\|");
        for (int i = 0; i < xyConfig.length; i++) {
            Goodstable goodstable = GoodsListFromServerUntil.fushis.get(new BigDecimal(id[i]));
            if (goodstable!=null){
                xzfouricon[i].setIcon(GoodsListFromServerUntil.xbaoWdfile(goodstable.getSkin(),imageSize,imageSize));
            }else {
                boolean flag = choseBao.getBaolvl().intValue()>=maxlv[i];
                if (split[i].equals("1")){
                    xzfouricon[i].setIcon(Juitil.tz386);
                }else {
                    xzfouricon[i].setIcon(flag?Juitil.tz384:Juitil.tz368);
                }
            }

            xzfouricon[i].setVisible(true);
            xzfouricon[i].setBounds(positions[i][0], positions[i][1], imageSize, imageSize);
            drawSkillIcon(xyConfig[i], positions[i][0], positions[i][1],i);
        }


    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "玄宝");
        } else {
            Juitil.SheNewShow(g, 617, getHeight(), "玄宝", 239, 55);
        }

        if (type == 0) {
            UiBack.UIResource.IMG_ZOOM.draw(g, this);
            g.drawImage(Juitil.tz352.getImage(), 73, 45, 513, 278, null);
            for (int i = 0; i < 3; i++) {
                g.drawImage(Util.SwitchUI == 1 ? Juitil.tz355.getImage() : Juitil.tz359.getImage(), 82, 79 + i * 60, 55, 55, null);
            }
            g.drawImage(Util.SwitchUI == 1 ? Juitil.tz356.getImage() : Juitil.tz360.getImage(), 71, 330, 513, 105, null);
            RoleXuanbao.getRoleXuanbao().drawXb(g, 74, 332);
        } else {
            g.drawImage(Juitil.tz357.getImage(), 69, 40, 520, 418, null);
            if (RoleXuanbao.getRoleXuanbao().choseBao!=null){
                g.drawImage(Juitil.tz369.getImage(), 87, 55, 28, 108, null);
                String baoname = RoleXuanbao.getRoleXuanbao().choseBao.getBaoname();
                String qu = Objects.requireNonNull(UserMessUntil.getXuanabao(baoname)).getBaoquality();
                Juitil.Txtpet(g, 90, 78, baoname,UIUtils.COLOR_SHS_4, UIUtils.NEWTX_HY19B);
                Juitil.TextBackground(g, qu, 13, 179, 236, UIUtils.COLOR_FM_Title, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
                Juitil.TextBackground(g, "品质", 13, 143, 236, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
                Graphics g3 = g.create(354,  309, 208, 124);
                chatBox.paint(g3);
                g3.dispose();

            }
            g.drawImage(Util.SwitchUI==1?Juitil.tz358.getImage():Juitil.tz361.getImage(), 72, 328, 207, 105, null);
            RoleXuanbao.getRoleXuanbao().drawXbac(g, 74, 330);

            int progressWidth;
            Xuanbao choseBao = RoleXuanbao.getRoleXuanbao().choseBao;
            if (choseBao!=null) {
                int zexe = expTable[choseBao.getBaolvl().intValue()];
                double progressRatio = (zexe > 0) ?
                        (double) choseBao.getBaoexe().longValue() / zexe : 0;
                progressWidth = (int) (106 * Math.min(progressRatio, 1.0)); // 确保不超过最大长度
                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.she_0024 : Juitil.red_0045,
                        119, 296, progressWidth,
                        Util.SwitchUI == 1 ? 16 : 12, 1);
            }
        }


    }


    /**
     * 绘制技能图标
     * @param x 横坐标
     * @param y 纵坐标
     */
    private void drawSkillIcon(String color, int x, int y,int i) {
        if (color == null) return;
        Storecolors[i].setText(color);
        if (!color.isEmpty()) {
            if (type==0){
                ImageIcon icon = setSkillIcon(color);
                if (icon != null) {
                    colorsicon[i].setIcon(icon);
                    colorsicon[i].setVisible(true);
                    colorsicon[i].setBounds(x, y, 64, 64);
                    // 确保图标显示在最上层
                    setComponentZOrder(colorsicon[i], 0);
                }
            }else {
                ImageIcon icon = SetAcJPanelIcon(color);
                if (icon != null) {
                    min_Storecolors[i].setIcon(icon);
                    min_Storecolors[i].setBounds(x, y, 36, 36);
                    // 确保图标显示在最上层
                    setComponentZOrder(min_Storecolors[i], 0);
                }
            }

        }
    }


    /**
     * 设置技能图标
     * @param color 颜色名称
     * @return 对应的图标对象
     */
    private ImageIcon setSkillIcon(String color) {
        if (color == null || color.trim().isEmpty()) {
            return null;
        }
        color = color.trim();
        switch (color) {
            case "蓝":
                return CutButtonImage.getWdfPng("0x6FEB9104", "defaut.wdf");
            case "黄":
                return CutButtonImage.getWdfPng("0x6FEB9105", "defaut.wdf");
            case "绿":
                return CutButtonImage.getWdfPng("0x6FEB9103", "defaut.wdf");
            case "红":
                return CutButtonImage.getWdfPng("0x6FEB9108", "defaut.wdf");
            case "蓝绿":
                return CutButtonImage.getWdfPng("0x6FEB9109", "defaut.wdf");
            case "蓝黄":
                return CutButtonImage.getWdfPng("0x6FEB9142", "defaut.wdf");
            case "红黄":
                return CutButtonImage.getWdfPng("0x6FEB9143", "defaut.wdf");
            case "黄绿":
                return CutButtonImage.getWdfPng("0x6FEB9144", "defaut.wdf");
            case "红蓝":
                return CutButtonImage.getWdfPng("0x6FEB9141", "defaut.wdf");
            case "红绿":
            case "红绿红":
                return CutButtonImage.getWdfPng("0x6FEB9107", "defaut.wdf");
            case "绿蓝红":
            case "蓝绿红":
            case "蓝红绿":
            case "红蓝绿":
                return CutButtonImage.getWdfPng("0x6FEB9106", "defaut.wdf");
            default:
                System.out.println("未知的颜色类型: " + color);
                return null;
        }
    }



    private ImageIcon SetAcJPanelIcon(String color) {
        if (color == null || color.trim().isEmpty()) {
            return null;
        }
        System.out.println("属性界面:"+color);
        color = color.trim();
        switch (color) {
            case "蓝":
                return CutButtonImage.getWdfPng("0x6FEB9113", "defaut.wdf");
            case "黄":
                return CutButtonImage.getWdfPng("0x6FEB9116", "defaut.wdf");
            case "绿":
                return CutButtonImage.getWdfPng("0x6FEB9122", "defaut.wdf");
            case "红":
                return CutButtonImage.getWdfPng("0x6FEB9119", "defaut.wdf");
            case "蓝绿":
                return CutButtonImage.getWdfPng("0x6FEB9123", "defaut.wdf");
            case "蓝黄":
                return CutButtonImage.getWdfPng("0x6FEB9145", "defaut.wdf");
            case "红黄":
                return CutButtonImage.getWdfPng("0x6FEB9146", "defaut.wdf");
            case "黄绿":
                return CutButtonImage.getWdfPng("0x6FEB9126", "defaut.wdf");
            case "红蓝":
                return CutButtonImage.getWdfPng("0x6FEB9124", "defaut.wdf");
            case "红绿":
            case "红绿红":
                return CutButtonImage.getWdfPng("0x6FEB9125", "defaut.wdf");
            case "绿蓝红":
            case "蓝绿红":
            case "蓝红绿":
            case "红蓝绿":
                return CutButtonImage.getWdfPng("0x6FEB9120", "defaut.wdf");
            default:
                System.out.println("未知的颜色类型: " + color);
                return null;
        }
    }


    public XbaoBrn getXbaoEquiBtn() {
        return xbaoEquiBtn;
    }

    public void setXbaoEquiBtn(XbaoBrn xbaoEquiBtn) {
        this.xbaoEquiBtn = xbaoEquiBtn;
    }

    public XbaoBrn getXbaoAttrBtn() {
        return xbaoAttrBtn;
    }

    public void setXbaoAttrBtn(XbaoBrn xbaoAttrBtn) {
        this.xbaoAttrBtn = xbaoAttrBtn;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public JLabel[] getXuanbaoListLabel() {
        return xuanbaoListLabel;
    }

    public void setXuanbaoListLabel(JLabel[] xuanbaoListLabel) {
        this.xuanbaoListLabel = xuanbaoListLabel;
    }

    public JLabel[] getXuanbaoframe() {
        return xuanbaoframe;
    }

    public void setXuanbaoframe(JLabel[] xuanbaoframe) {
        this.xuanbaoframe = xuanbaoframe;
    }

    public JLabel[] getChoseGoodsJLabel() {
        return choseGoodsJLabel;
    }

    public void setChoseGoodsJLabel(JLabel[] choseGoodsJLabel) {
        this.choseGoodsJLabel = choseGoodsJLabel;
    }

    public JLabel[] getWearableImg() {
        return wearableImg;
    }

    public void setWearableImg(JLabel[] wearableImg) {
        this.wearableImg = wearableImg;
    }

    public boolean[] getIsClick() {
        return isClick;
    }

    public void setIsClick(boolean[] isClick) {
        this.isClick = isClick;
    }

    public JLabel[] getStorecolors() {
        return Storecolors;
    }

    public void setStorecolors(JLabel[] storecolors) {
        Storecolors = storecolors;
    }

    public JLabel[] getXzfouricon() {
        return xzfouricon;
    }

    public void setXzfouricon(JLabel[] xzfouricon) {
        this.xzfouricon = xzfouricon;
    }

    public JLabel[] getWearableback() {
        return wearableback;
    }

    public void setWearableback(JLabel[] wearableback) {
        this.wearableback = wearableback;
    }

    public ImageIcon getEqIcon() {
        return EqIcon;
    }

    public void setEqIcon(ImageIcon eqIcon) {
        EqIcon = eqIcon;
    }

    public JLabel getXzimg() {
        return xzimg;
    }

    public void setXzimg(JLabel xzimg) {
        this.xzimg = xzimg;
    }

    public JLabel getXzname() {
        return xzname;
    }

    public void setXzname(JLabel xzname) {
        this.xzname = xzname;
    }

    public JLabel getLiteicon() {
        return liteicon;
    }

    public void setLiteicon(JLabel liteicon) {
        this.liteicon = liteicon;
    }

    public JLabel[] getColorsicon() {
        return colorsicon;
    }

    public void setColorsicon(JLabel[] colorsicon) {
        this.colorsicon = colorsicon;
    }

    public RichLabel getRichLabel() {
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }

    public RichLabel getRichLabel2() {
        return richLabel2;
    }

    public void setRichLabel2(RichLabel richLabel2) {
        this.richLabel2 = richLabel2;
    }

    public XbaoBrn getPreviousPage() {
        return previousPage;
    }

    public XbaoBrn getNextPage() {
        return nextPage;
    }

    /**
     * 计算文本在指定宽度下的渲染高度
     * @param text 要计算的文本
     * @param maxWidth 最大宽度
     * @param font 字体
     * @return 文本的渲染高度
     */
    public int calculateTextHeight(String text, int maxWidth, Font font) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 创建临时的RichLabel来计算尺寸
        RichLabel tempLabel = new RichLabel("#c9f9f9f" + text, font);
        Dimension size = tempLabel.computeSize(maxWidth);
        return size.height;
    }

    // 设置技能图标的透明度
    public void setSkillAlpha(int index, float alpha) {
        if (index >= 0 && index < skillAlpha.length) {
            skillAlpha[index] = alpha;
            skillimg[index].repaint();
        }
    }

    // 设置技能图标的激活状态
    public void setSkillActive(int index, boolean active) {
        setSkillAlpha(index, active ? 1.0f : 0.5f);
    }
    // 经验表：expTable[i] 表示i级升到i+1级所需经验（expTable[0]无用，expTable[1]是1升2级经验）
    int[] expTable = new int[] {
            0,      // 占位，方便下标与等级对应（expTable[1]表示1级升2级）
            500,    // 1-2
            500,    // 2-3
            500,    // 3-4
            500,    // 4-5
            500,    // 5-6
            600,    // 6-7
            600,    // 7-8
            600,    // 8-9
            600,    // 9-10
            700,    // 10-11
            700,    // 11-12
            700,    // 12-13
            800,    // 13-14
            800,    // 14-15
            800,    // 15-16
            800,    // 16-17
            800,    // 17-18
            800,    // 18-19
            900,    // 19-20
            1000,   // 20-21
            1100,   // 21-22
            1100,   // 22-23
            1100,   // 23-24
            1200,   // 24-25
            1200,   // 25-26
            1300,   // 26-27
            1300,   // 27-28
            1400,   // 28-29
            1400,   // 29-30
            1500,   // 30-31
            1500,   // 31-32
            1600,   // 32-33
            1600,   // 33-34
            1700,   // 34-35
            1700,   // 35-36
            1800,   // 36-37
            1800,   // 37-38
            1900,   // 38-39
            1900,   // 39-40
            2000,   // 40-41
            2100,   // 41-42
            2200,   // 42-43
            2300,   // 43-44
            2400,   // 44-45
            2500,   // 45-46
            2600,   // 46-47
            2700,   // 47-48
            2800,   // 48-49
            2900,   // 49-50
            3400,   // 50-51
            3500,   // 51-52
            3600,   // 52-53
            3700,   // 53-54
            3800,   // 54-55
            3900,   // 55-56
            4000,   // 56-57
            4100,   // 57-58
            4200,   // 58-59
            4300,   // 59-60
            4400,   // 60-61
            4500,   // 61-62
            4600,   // 62-63
            4700,   // 63-64
            4800,   // 64-65
            4900,   // 65-66
            5000,   // 66-67
            5100,   // 67-68
            5200,   // 68-69
            5300,   // 69-70
            5400,   // 70-71
            5500,   // 71-72
            5600,   // 72-73
            5700,   // 73-74
            5800,   // 74-75
            5900,   // 75-76
            6000,   // 76-77
            6100,   // 77-78
            6200,   // 78-79
            6300,   // 79-80
            6400,   // 80-81
            6500,   // 81-82
            6600,   // 82-83
            6700,   // 83-84
            6800,   // 84-85
            6900,   // 85-86
            7000,   // 86-87
            8000,   // 87-88
            9700,   // 88-89
            9800,   // 89-90
            9900,   // 90-91
            10000,  // 91-92
            10100,  // 92-93
            10200,  // 93-94
            10300,  // 94-95
            10400,  // 95-96
            10500,  // 96-97
            10600,  // 97-98
            10700,  // 98-99
            10800,  // 99-100
            10900,  // 100-101
            11000,  // 101-102
            11100,  // 102-103
            11200,  // 103-104
            11300,  // 104-105
            11400,  // 105-106
            11500,  // 106-107
            11600,  // 107-108
            11700,  // 108-109
            11800,  // 109-110
            11900,  // 110-111
            12000,  // 111-112
            12100,  // 112-113
            12200,  // 113-114
            12300,  // 114-115
            12400,  // 115-116
            12500,  // 116-117
            12600,  // 117-118
            12700,  // 118-119
            12800,  // 119-120
            12900,  // 120-121
            13000,  // 121-122
            13100,  // 122-123
            13200,  // 123-124
            13300,  // 124-125
            13400,  // 125-126
            13500,  // 126-127
            13600,  // 127-128
            13700,  // 128-129
            13800,  // 129-130
            13900,  // 130-131
            14000,  // 131-132
            14100,  // 132-133
            14200,  // 133-134
            14300,  // 134-135
            14400,  // 135-136
            14500,  // 136-137
            14600,  // 137-138
            14700,  // 138-139
            14800,  // 139-140
            14900,  // 140-141
            15000,  // 141-142
            15100,  // 142-143
            15200,  // 143-144
            15300,  // 144-145
            15400,  // 145-146
            15500,  // 146-147
            15600,  // 147-148
            15700,  // 148-149
            15800,  // 149-150
            15900,  // 150-151
            16000,  // 151-152
            16100,  // 152-153
            16200,  // 153-154
            16300,  // 154-155
            16400,  // 155-156
            16500,  // 156-157
            16600,  // 157-158
            16700,  // 158-159
            16800,  // 159-160
            16900,  // 160-161
            17000,  // 161-162
            17100,  // 162-163
            17200,  // 163-164
            17300,  // 164-165
            17400,  // 165-166
            17500,  // 166-167
            17600,  // 167-168
            17700,  // 168-169
            17800,  // 169-170
            17900,  // 170-171
            18000,  // 171-172
            18100,  // 172-173
            18200,  // 173-174
            18300,  // 174-175
            18400,  // 175-176
            18500,  // 176-177
            18600,  // 177-178
            18700,  // 178-179
            18800,  // 179-180
            88800,  // 180-181
            90000,  // 181-182
            92000,  // 182-183
            94000,  // 183-184
            96000,  // 184-185
            98000,  // 185-186
            104900, // 186-187
            106900, // 187-188
            108900, // 188-189
            110900, // 189-190
            112900, // 190-191
            114900, // 191-192
            116900, // 192-193
            118900, // 193-194
            120900, // 194-195
            122900, // 195-196
            124900, // 196-197
            126900, // 197-198
            128900, // 198-199
            130900  // 199-200
    };

}

