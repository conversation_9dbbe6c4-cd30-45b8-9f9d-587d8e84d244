package jxy2.wormap;

import com.tool.tcpimg.InputBean;
import come.tool.map.XLPath;
import org.come.bean.Coordinates;
import org.come.model.Gamemap;
import org.come.mouslisten.Mouselistener;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;
import java.math.BigDecimal;

public class WordlMapMenuMouse implements MouseListener, MouseMotionListener {
    public WordlMapMenuJPanel wordlMapMenuJPanel;
    public Image image;
    public int  index,MapID;
    public double smalx, smaly,bili_x,bili_y;
    public WordlMapMenuMouse(WordlMapMenuJPanel wordlMapMenuJPanel, int index, int MapID, Image image) {
        this.wordlMapMenuJPanel=wordlMapMenuJPanel;
        this.index=index;
        this.MapID=MapID;
        this.image=image;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {
        Gamemap gamemap = UserMessUntil.getAllmapbean().getAllMap().get(MapID + "");
        int mapw = Integer.parseInt(gamemap.getWidth());
        int mapwh = Integer.parseInt(gamemap.getHeight());
        int  min_x = image.getWidth(null);
        int  min_y = image.getHeight(null);
        int  w = mapw / Util.CELL_WIDTH;
        int  h = mapwh / Util.CELL_WIDTH;
        double bili_x = (double) w / min_x;
        double bili_y = (double) h / min_y;
        double smalx = ((e.getX()-4) * bili_x);
        double smaly = ((e.getY() -30) * bili_y);
        XLPath.getXLMap(MapID);
        Coordinates coordinates=new Coordinates(MapID, (int) smalx*20, (int) smaly*20);
        InputBean inputBean=new InputBean(new BigDecimal(0),25,coordinates);
        Mouselistener.DJInputBean(inputBean);
    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    @Override
    public void mouseDragged(MouseEvent e) {

    }

    @Override
    public void mouseMoved(MouseEvent e) {

    }
}
