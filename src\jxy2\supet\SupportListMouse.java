package jxy2.supet;

import jxy2.UniversalModel;
import org.come.Jpanel.SupportListJpanel;
import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class SupportListMouse extends TemplateMouseListener {
    private JList<UniversalModel> listpet;
    private SupportListJpanel supportListJpanel;
    public SupportListMouse(JList<UniversalModel> listpet, SupportListJpanel supportListJpanel) {
        this.listpet = listpet;
        this.supportListJpanel = supportListJpanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        SupportListJpanel.idx = -1;

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        if (listpet!=null){
            if (e.getY() / 35 < supportListJpanel.getListModel().getSize()){
                SupportListJpanel.idx = supportListJpanel.getListpet().locationToIndex(e.getPoint());
            }
        }

    }
}
