package com.tool.btn;

import com.tool.pet.PetProperty;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.supet.SipetFrame;
import jxy2.supet.SipetJPanel;
import org.come.Frame.AlchemyJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.AlchemyJpanel;
import org.come.Jpanel.AlchemyMainJpanel;
import org.come.Jpanel.AlchemySpellJpanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.PetOperationDTO;
import org.come.bean.SummonPetBean;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.llandudno.AtlasListCell;
import org.come.mouslisten.ChoseAlchemyGoodsMouslisten;
import org.come.mouslisten.GoodsMouslisten;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.summonequip.JframeHelpMain;
import org.come.summonequip.JpanelHelpMain;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PetOperationPanelBtn extends MoBanBtn {
    public int caozuo;
    public AlchemyMainJpanel alchemyMainJpanel;
    /**
     * 构造宠物操作面板按钮
     * @param iconpath 按钮图标路径
     * @param type 按钮类型
     * @param colors 按钮颜色
     * @param font 按钮字体
     * @param text 按钮文本
     * @param caozuo 操作类型
     * @param prompt 提示信息
     */
    public PetOperationPanelBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer caozuo, String prompt) {
        super(iconpath, type, 0, colors, prompt);
        initializeButton(text, font, caozuo);
    }

    public PetOperationPanelBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer caozuo) {
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        this.caozuo = caozuo;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    /**
     * 构造宠物操作面板按钮（带炼妖主面板）
     * @param iconpath 按钮图标路径
     * @param type 按钮类型
     * @param colors 按钮颜色
     * @param font 按钮字体
     * @param text 按钮文本
     * @param caozuo 操作类型
     * @param alchemyMainJpanel 炼妖主面板
     * @param prompt 提示信息
     */
    public PetOperationPanelBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer caozuo, AlchemyMainJpanel alchemyMainJpanel, String prompt) {
        super(iconpath, type, 0, colors, prompt);
        initializeButton(text, font, caozuo);
        this.alchemyMainJpanel = alchemyMainJpanel;
    }

    /**
     * 初始化按钮公共属性
     * @param text 按钮文本
     * @param font 按钮字体
     * @param caozuo 操作类型
     */
    private void initializeButton(String text, Font font, Integer caozuo) {
        this.setNtext(text);
        setFont(font);
        this.caozuo = caozuo;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    @Override
    public void chooseyes() {
        // 预留方法，根据具体需求实现
        // 可用于处理确认操作的逻辑
    }

    @Override
    public void chooseno() {
        // 预留方法，根据具体需求实现
        // 可用于处理取消操作的逻辑
    }

    @Override
    public void nochoose(MouseEvent e) {
        // 获取炼妖主面板
        AlchemyMainJpanel mainJpanel = AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel();

        // 菜单导航操作
        if (caozuo >= 10 && caozuo <= 14) {
            handleMenuNavigation(mainJpanel);
        } else if (caozuo == 15) {
            // 处理设备选择
            mainJpanel.getCardJPanel().getAlchemySpellJpanel().handleEquipmentSelection(true);
        } else if (caozuo == 16) {
            // 处理设备取消选择
            mainJpanel.getCardJPanel().getAlchemySpellJpanel().handleEquipmentSelection(false);
        } else {
            // 其他功能实现事件
            handleOtherFunctionEvents(mainJpanel);
        }

        AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getPetList().setSelectedIndex(-1);
    }

    /**删除低级符咒跟高级符咒炼妖后的图像*/
    public static void Delete(int index) {
        AlchemySpellJpanel alchemySpellJpanel = AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemySpellJpanel();
        // 获取精炼标签数组
        JLabel[] labs = alchemySpellJpanel.getLabRefined();
        // 如果index为0，清空全部，否则只清空第一个
        if (index == 0) {
            for (JLabel lab : labs) lab.setIcon(null); // 全部清空
        } else if (labs.length > 0) {
            labs[0].setIcon(null); // 只清空第一个
        }
        // 清空炼丹物品列表
        AlchemySpellJpanel.newPart =null;
        AlchemySpellJpanel.newPartTow =null;
        ChoseAlchemyGoodsMouslisten.getGoodstableList().clear();
        alchemySpellJpanel.getSummoningMap().clear();
    }


    /**
     * 炼妖的方法
     */
    //TODO 需要重构方法
    public void artificePet() {
        //直接将物品信息发送至服务端请求
        PetOperationDTO dto = new PetOperationDTO();
        AlchemyMainJpanel alchemyMainJpanel = AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel();
        if (alchemyMainJpanel.getPetList().getSelectedIndex() == -1) {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择你要炼化的召唤兽！");
            return;
        }
        Goodstable goodstable = ChoseAlchemyGoodsMouslisten.getGoodstableList().get(24);
        if (goodstable == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("你还没选中物品");
            return;
        } else {
            if (GoodsListFromServerUntil.isExist(goodstable)) {
                return;
            }
            if (goodstable.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
        }


        int index = alchemyMainJpanel.getPetList().getSelectedIndex();
        RoleSummoning pet = UserMessUntil.getPetListTable().get(index);
        if (goodstable.getType() == 701) {//金柳露
        	jll(pet, goodstable);
        	ZhuJpanel.setGoodstableAl(null);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyJpanel().getLabRefined().setIcon(null);
            return;
        } else if (goodstable.getType() == 713) {
            // 卧龙丹
            dto.setOperationType("WOLONG_DAN");
            ZhuFrame.getZhuJpanel().addPrompt("恭喜您，您的召唤兽炼化成功！！！");
        } else if (goodstable.getType() == 714) {
            dto.setOperationType("DRAGON_PHOENIX_STONE");
            ZhuFrame.getZhuJpanel().addPrompt("恭喜您，您的召唤兽炼化成功！！！");
        } else if (pet.getAlchemynum() >= 12) {
            ZhuFrame.getZhuJpanel().addPrompt2("召唤兽" + pet.getSummoningname() + "的炼妖次数已经超过12次！");
            return;
        } else {
            //REFINING_DEMONS
            String[] Arraystoneat = goodstable.getValue().split("\\|");
            if (pet.getLyk() != null && !pet.getLyk().isEmpty()) {
                for (int i = 1; i < Arraystoneat.length; i++) {
                    StringBuilder values = new StringBuilder();
                    int have = 0;
                    String[] Arraystone = Arraystoneat[i].split("=");
                    String[] Arraypetat = pet.getLyk().split("\\|");
                    for (String s : Arraypetat) {
                        String[] Arraypet = s.split("=");
                        if (Arraypet[0].equals(Arraystone[0])) {
                            Arraypet[1] = String.valueOf(Double.parseDouble(Arraypet[1])
                                    + Double.parseDouble(Arraystone[1]));
                            String value = Arraypet[0] + "=" +
                                    Arraypet[1] + "|";
                            values.append(value);
                            have = 1;
                        } else {
                            String value = Arraypet[0] + "=" +
                                    Arraypet[1] + "|";
                            values.append(value);
                        }
                    }

                    if (have == 1)
                        pet.setLyk(values.toString());
                    else pet.setLyk(values + Arraystone[0] + "=" +
                            Arraystone[1] + "|");
                }

            } else {
                StringBuilder values = new StringBuilder();
                for (int i = 1; i < Arraystoneat.length; i++) {
                    String[] Arraystone = Arraystoneat[i].split("=");
                    String value = Arraystone[0] + "=" + Arraystone[1] +
                            "|";
                    values.append(value);
                }

                pet.setLyk(values.toString());
            }
            pet.setAlchemynum(pet.getAlchemynum() + 1);
            dto.setOperationType("REFINING_DEMONS");
            ZhuFrame.getZhuJpanel().addPrompt("恭喜您，您的召唤兽炼化成功！！！");
        }
        goodstable.setUsetime(goodstable.getUsetime() - 1);
        GoodsMouslisten.gooduse(goodstable, 1);
        if (goodstable.getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goodstable.getRgid());
        }
        dto.setPetId(pet.getSid());
        dto.setItmeId(goodstable.getRgid());
   	    SendRoleAndRolesummingUntil.sendRoleSumming(dto);
        ZhuJpanel.setGoodstableAl(null);
        AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyJpanel().getLabRefined().setIcon(null);
        // 更新面板
        PetAddPointMouslisten.ShowPet(pet);
        // 刷新召唤兽抗性面板
        if (FormsManagement.getframe(58).isVisible()) {
            PetProperty.ShowQl(UserMessUntil.getChosePetMes());
        }
    }
    /**金柳露*/
    public void jll(RoleSummoning pet,Goodstable good){
    	if (pet.getSsn()!=null&&!pet.getSsn().equals("0")) {
            ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽无法使用金柳露");
            return;
        }else if (pet.getTurnRount()!=0) {
        	ZhuFrame.getZhuJpanel().addPrompt2("未转的宝宝才可以使用金柳露");
            return;
		}
    	SummonPetBean summonPetBean = new SummonPetBean();
        summonPetBean.setOpertype(1);// 使用金柳露
        summonPetBean.setPetid(pet.getSid());
        summonPetBean.setGoodid(good.getRgid());

        String mes = Agreement.getAgreement().summonpetAgreement(GsonUtil.getGsonUtil().getgson().toJson(summonPetBean));
        SendMessageUntil.toServer(mes);// 向服务器发送信息
    }

    public void handleButtonClick(int clickedIndex) {
        alchemyMainJpanel.getBtnBasis()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <alchemyMainJpanel.getBtnBasis().length; i++) {
            if (i != clickedIndex) {
                alchemyMainJpanel.getBtnBasis()[i].btnchange(0);
            }
        }
    }

    /**
     * 处理菜单导航操作
     * @param mainJpanel 炼妖主面板
     */
    private void handleMenuNavigation(AlchemyMainJpanel mainJpanel) {
        // 根据操作类型切换面板
        switch (caozuo) {
            case 10:
                switchPanel(mainJpanel, "refd", "炼妖", 1);
                break;
            case 11:
                switchPanel(mainJpanel, "Spell", "符咒炼妖", 2);
                break;
            case 12:
                switchPanel(mainJpanel, "Combine", "合成", 3);
                break;
            case 13:
                switchPanel(mainJpanel, "Extract", "提炼", 4);
                break;
            case 14:
                switchPanel(mainJpanel, "Return", "灵返", 5);
                break;
        }

        // 设置宠物列表渲染器
        mainJpanel.getPetList().setCellRenderer(
            mainJpanel.getInterface() == 2 || mainJpanel.getInterface() == 4  || mainJpanel.getInterface() == 5 ? null :
            new AtlasListCell(AlchemyMainJpanel.idx, Color.gray, 143, 20, 11)
        );

        handleButtonClick(caozuo - 10);
        resetAlchemyState(mainJpanel);
    }

    /**
     * 处理其他功能事件
     * @param mainJpanel 炼妖主面板
     */
    private void handleOtherFunctionEvents(AlchemyMainJpanel mainJpanel) {
        if (caozuo != 1) {
            // 帮助面板处理
            JpanelHelpMain jpanelHelpMain = JframeHelpMain.getJframeHelpMain().getJpanelHelpMain();
            jpanelHelpMain.initData(String.valueOf(caozuo));
            Util.StopFrame(93);
        } else {
            // 仅在普通状态下处理
            if (FightingMixDeal.State == HandleState.USUAL) {
                switch (mainJpanel.getInterface()) {
                    case 1:
                        artificePet();
                        break;
                    case 2:
                        handleSpellAlchemy(mainJpanel);
                        break;
                    case 4:
                        handleExtractAlchemy(mainJpanel);
                        break;
                    case 5:
                        handleRteurnAlchemy(mainJpanel);
                        break;
                }
            }
        }
    }



    /**
     * 切换面板并设置界面状态
     * @param mainJpanel 炼妖主面板
     * @param panelName 面板名称
     * @param title 面板标题
     * @param interfaceType 界面类型
     */
    private void switchPanel(AlchemyMainJpanel mainJpanel, String panelName, String title, int interfaceType) {
        mainJpanel.getCardJPanel().getCardLayout().show(mainJpanel.getCardJPanel(), panelName);
        mainJpanel.titie = title;
        mainJpanel.setInterface(interfaceType);
    }

    /**
     * 重置炼妖状态
     * @param mainJpanel 炼妖主面板
     */
    private void resetAlchemyState(AlchemyMainJpanel mainJpanel) {
        // 清空物品列表
        ChoseAlchemyGoodsMouslisten.getGoodstableList().clear();

        // 清空精炼标签图标
        for (JLabel label : mainJpanel.getCardJPanel().getAlchemySpellJpanel().getLabRefined()) {
            label.setIcon(null);
        }

        // 重置炼妖状态
        AlchemyJpanel.newPart = null;
        AlchemySpellJpanel.newPart = null;
        AlchemySpellJpanel.newPartTow = null;

        // 处理设备选择
        mainJpanel.getCardJPanel().getAlchemySpellJpanel().handleEquipmentSelection(true);

        // 根据界面类型获取数据
        if (mainJpanel.getInterface() != 2 && mainJpanel.getInterface() != 4 && mainJpanel.getInterface() != 5) {
            mainJpanel.ServerObtainsData();
        }

        // 初始化提炼列表
        if (mainJpanel.getInterface() == 4) {
            mainJpanel.getCardJPanel().getAlchemyExtractJpanel().initList(mainJpanel);
        }else if (mainJpanel.getInterface() == 5) {
            mainJpanel.getCardJPanel().getAlchemyReturnJpanel().initList(mainJpanel);
        }
    }

    /**
     * 处理符咒炼妖逻辑
     * @param mainJpanel 炼妖主面板
     */
    private void handleSpellAlchemy(AlchemyMainJpanel mainJpanel) {
        PetOperationDTO dto = new PetOperationDTO();
        AlchemySpellJpanel spellJpanel = mainJpanel.getCardJPanel().getAlchemySpellJpanel();

        if (spellJpanel.getType() == 0) {
            // 低级符咒炼妖逻辑
            handleLowLevelSpellAlchemy(dto);
        } else {
            // 高级符咒炼妖逻辑
            handleAdvancedSpellAlchemy(mainJpanel, dto);
        }

        SendRoleAndRolesummingUntil.sendRoleSumming(dto);
        Delete(spellJpanel.getType());
    }

    /**
     * 处理低级符咒炼妖
     * @param dto 宠物操作DTO
     */
    private void handleLowLevelSpellAlchemy(PetOperationDTO dto) {
        // 检查炼妖石等级
        String level = GoodsListFromServerUntil.luanyaoshi(ChoseAlchemyGoodsMouslisten.getGoodstableList().get(25));
        if (level == null || Integer.parseInt(level) < 9) {
            ZhuFrame.getZhuJpanel().addPrompt("#R炼妖石属性小于+9无法炼妖");
            return;
        }

        // 构建炼妖石ID列表
        StringBuilder builder = new StringBuilder();
        // 从Map中提取Goodstable值到List
        List<Goodstable> goodsList = new ArrayList<>(ChoseAlchemyGoodsMouslisten.getGoodstableList().values());
        for (int i = 0; i < goodsList.size(); i++) {
            Goodstable goodstable = goodsList.get(i + 24);
            builder.append(builder.length() != 0 ? "|" : "").append(goodstable.getRgid());
        }

        dto.setPetId(UserMessUntil.getChosePetMes().getSid());
        dto.setOperationType("SPELL_ALCHEMY");
        dto.setEventType(builder.toString());
    }

    /**
     * 处理高级符咒炼妖
     * @param mainJpanel 炼妖主面板
     * @param dto 宠物操作DTO
     */
    private void handleAdvancedSpellAlchemy(AlchemyMainJpanel mainJpanel, PetOperationDTO dto) {
        Goodstable spellItem = ChoseAlchemyGoodsMouslisten.getGoodstableList().get(24);
        if (spellItem == null) return;

        AlchemySpellJpanel spellJpanel = mainJpanel.getCardJPanel().getAlchemySpellJpanel();
        RoleSummoning petOne = spellJpanel.getSummoningMap().get(0);
        RoleSummoning petTwo = spellJpanel.getSummoningMap().get(1);

        // 验证宠物
        if (!validatePetsForAlchemy(petOne, petTwo)) return;

        // 准备高级炼妖数据
        dto.setPetId(petOne.getSid());
        dto.setItmeId(new BigDecimal(String.valueOf(spellItem.getRgid())));
        dto.setOperationType("ADVANCED_LEVEL");
        dto.setEventType(petOne.getSid() + "|" + petTwo.getSid());

        // 更新宠物列表
        updatePetList(petOne, petTwo);
    }

    /**
     * 验证炼妖宠物
     * @param petOne 第一只宠物
     * @param petTwo 第二只宠物
     * @return 是否验证通过
     */
    private boolean validatePetsForAlchemy(RoleSummoning petOne, RoleSummoning petTwo) {
        if (petOne == null || petTwo == null || petOne.getTurnRount() < 1 || petTwo.getTurnRount() < 1) {
            ZhuFrame.getZhuJpanel().addPrompt("#R缺少召唤兽，或者召唤兽转生低于二转");
            return false;
        }

        if (petOne.getSid().compareTo(petTwo.getSid()) == 0) {
            ZhuFrame.getZhuJpanel().addPrompt("#R不能加入两只相同的召唤兽");
            return false;
        }

        return true;
    }

    /**
     * 更新宠物列表
     * @param petOne 第一只宠物
     * @param petTwo 第二只宠物
     */
    private void updatePetList(RoleSummoning petOne, RoleSummoning petTwo) {
        List<RoleSummoning> petList = UserMessUntil.getPetListTable();
        petList.remove(petOne);
        petList.remove(petTwo);

        SipetJPanel.getInstance().RefreshPetViewport();
        AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().ServerObtainsData();
        SipetFrame.getSipetFrame().getSipetJPanel().RefreshPetViewport();
    }

    /**
     * 处理提炼逻辑
     * @param mainJpanel 炼妖主面板
     */
    private void handleExtractAlchemy(AlchemyMainJpanel mainJpanel) {
        Goodstable goodstable = ChoseAlchemyGoodsMouslisten.getGoodstableList().get(24);
        if (goodstable == null) return;

        RoleSummoning pet = mainJpanel.getCardJPanel().getAlchemyExtractJpanel().getPet();
        if (pet == null) return;

        mainJpanel.getCardJPanel().getAlchemyExtractJpanel().initList(mainJpanel);

        // 发送提炼协议
        String sendMessage = Agreement.getAgreement().userpetAgreement(goodstable.getRgid() + "|" + pet.getSid());
        SendMessageUntil.toServer(sendMessage);

        // 更新宠物列表
        List<RoleSummoning> petList = UserMessUntil.getPetListTable();
        petList.remove(pet);
        mainJpanel.getCardJPanel().getAlchemyExtractJpanel().setPet(null);
        SipetFrame.getSipetFrame().getSipetJPanel().RefreshPetViewport();
        FormsManagement.HideForm(17);
    }

    /**
     * 处理灵返逻辑
     * @param mainJpanel 灵返主面板
     */
    private void handleRteurnAlchemy(AlchemyMainJpanel mainJpanel) {
        Goodstable goodstable = ChoseAlchemyGoodsMouslisten.getGoodstableList().get(24);
        if (goodstable == null) return;

        RoleSummoning pet = mainJpanel.getCardJPanel().getAlchemyReturnJpanel().getPet();
        if (pet == null) return;
        String[] vs=pet.getPetSkills().split("\\|");
        // 将字符串数组转换为 ArrayList
        List<String> skillsList = new ArrayList<>(Arrays.asList(vs));
        // 删除指定ID
        skillsList.remove(mainJpanel.getCardJPanel().getAlchemyReturnJpanel().getPetskillID());
        // 将 ArrayList 转回字符串
        String updatedSkills = String.join("|", skillsList);
        pet.setPetSkills(updatedSkills);

        mainJpanel.getCardJPanel().getAlchemyReturnJpanel().initList(mainJpanel);
        PetOperationDTO dto = new PetOperationDTO();
        dto.setPetId(pet.getSid());
        dto.setOperationType("LINGHUI");
        dto.setItmeId(goodstable.getRgid());
        dto.setEventType(mainJpanel.getCardJPanel().getAlchemyReturnJpanel().getPetskillID());
        SendRoleAndRolesummingUntil.sendRoleSumming(dto);
    }

}
