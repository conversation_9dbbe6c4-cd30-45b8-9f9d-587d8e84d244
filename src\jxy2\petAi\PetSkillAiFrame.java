package jxy2.petAi;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class PetSkillAiFrame extends JInternalFrame implements MouseListener {
    private PetSkillAiJPanel petSkillAiJPanel;
    private int first_x,first_y;//x、y坐标

    public static PetSkillAiFrame getPetSkillAiFrame() {
        return (PetSkillAiFrame) FormsManagement.getInternalForm(113).getFrame();
    }

    public PetSkillAiFrame() {
        petSkillAiJPanel = new PetSkillAiJPanel();
        this.getContentPane().add(petSkillAiJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        ((BasicInternalFrameUI)this.getUI()).setNorthPane(null);//去除顶部的边框
        this.setBounds(200, 80, 284, 480);//设置窗口出现的位置
        this.setBackground(UIUtils.Color_BACK);
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {//判断窗口移动的位置

            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(),getWidth(),getHeight());
                }
            }
        });
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
//关闭按钮
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(113);
        }else {
            FormsManagement.Switchinglevel(113);
        }
        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public PetSkillAiJPanel getPetSkillAiJPanel() {
        return petSkillAiJPanel;
    }

    public void setPetSkillAiJPanel(PetSkillAiJPanel petSkillAiJPanel) {
        this.petSkillAiJPanel = petSkillAiJPanel;
    }
}
