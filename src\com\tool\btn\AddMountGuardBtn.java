package com.tool.btn;

import jxy2.guaed.MountListFrame;
import org.come.Jpanel.MountJPanel;
import org.come.until.FormsManagement;
import org.come.until.Music;

import java.awt.event.MouseEvent;

public class AddMountGuardBtn extends MoBanBtn {
    public int id;
    public MountJPanel mountJPanel;
    public AddMountGuardBtn(String iconpath, int type, int id, MountJPanel mountJPanel) {
        super(iconpath,0, type);
        // TODO Auto-generated constructor stub
        this.id = id;
        this.mountJPanel = mountJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

        if (!FormsManagement.getframe(140).isVisible()) {
            FormsManagement.showForm(140);
            MountListFrame.getMountListFrame().getMountListJPanel().initData(id);
            mountJPanel.refreshMount(mountJPanel.mounts);
            Music.addyinxiao("开关窗口.mp3");
        } else {
            FormsManagement.HideForm(140);
            Music.addyinxiao("关闭窗口.mp3");
        }
    }
}
