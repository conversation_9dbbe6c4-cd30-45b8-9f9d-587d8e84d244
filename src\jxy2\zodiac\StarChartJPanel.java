package jxy2.zodiac;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 星盘
* <AUTHOR>
* @date 2024/11/23 下午10:20
*/
public class StarChartJPanel extends JPanel {
    private JLabel[] goodLabels = new JLabel[36];
    private BigDecimal XpGoods; // 当前选中的翅膀
    private StarChartBtn conversion,refining;
    public StarChartJPanel() {
        this.setPreferredSize(new Dimension(444, 481));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,135,444);
        for (int i = 0; i < goodLabels.length; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            goodLabels[i] = new JLabel();
            goodLabels[i].setBounds(90+shop_x*51,90+shop_y*51, 51, 51);
            goodLabels[i].addMouseListener(new StarChartMoues(i, this));
            this.add(goodLabels[i]);
        }
        conversion = new StarChartBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "转化", 1, this,"");
        conversion.setBounds(196, 411, 59, 24);
        this.add(conversion);

        refining = new StarChartBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "重炼", 2, this,"");
        refining.setBounds(294, 411, 59, 24);
        this.add(refining);
    }

    public void changeChooseXpGoods(Goodstable goodstable) {
        XpGoods = goodstable.getRgid();
        System.out.println(goodstable.getGoodsname());
    }
     /** 选中星盘展示 */
    public void chooseXpImg(Goodstable goodstable) {
        XpGoods = goodstable.getRgid();
    }
    public int xi,yi;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"星 盘");
        Juitil.ImngBack(g, Juitil.tz22, 15, 80, 400, 340, 1);
        Juitil.TextBackground(g, "魂值", 13, 15, 411, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);
        Juitil.ImngBack(g, Juitil.tz26, 55, 407, 98, 23, 1);


        for (int i = 0; i < 36; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,90+shop_x*51,90+shop_y*51,51,51,1);
            int x = StarChartMoues.list.contains(i+"")?1:0;
            Goodstable goodstable = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[i]);
            g.drawImage(goodstable!=null?Juitil.tz264.getImage():Juitil.tz265.getImage(),x+90+shop_x*51,x+90+shop_y*51,50,50,null);
            Juitil.Txtpet(g, x+107+shop_x*51, x+111+shop_y*51, Juitil.XpName()[i], goodstable!=null?UIUtils.COLOR_FightingRound_Black:UIUtils.COLOR_White, UIUtils.TEXT_HY16);
            if (goodstable!=null) {
                Juitil.Sum(g,goodstable.getUsetime(),90+shop_x*51,103+shop_y*51);
            }
        }
        Juitil.ImngBack(g,Juitil.tz267,88,88,310,310,1);
        for (int i = 1; i < 7; i++) {
            Juitil.TextBackground(g, "等级 "+i, 13, 30, 61+i*50, UIUtils.COLOR_239, UIUtils.FZCY_HY17,UIUtils.COLOR_CL_NAME);
        }
        // 画拥有的魂值
        if (RoleData.getRoleData().getLoginResult().getScoretype("魂值") != null) {
            int totalValue = RoleData.getRoleData().getLoginResult().getScoretype("魂值").intValue();
            Juitil.TextBackground(g, totalValue + "", 15, 58, 410, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);
        }

    }

    public JLabel[] getGoodLabels() {
        return goodLabels;
    }

    public void setGoodLabels(JLabel[] goodLabels) {
        this.goodLabels = goodLabels;
    }

    public int getXi() {
        return xi;
    }

    public void setXi(int xi) {
        this.xi = xi;
    }

    public int getYi() {
        return yi;
    }

    public void setYi(int yi) {
        this.yi = yi;
    }

    public BigDecimal getXpGoods() {
        return XpGoods;
    }

    public void setXpGoods(BigDecimal xpGoods) {
        XpGoods = xpGoods;
    }
}
