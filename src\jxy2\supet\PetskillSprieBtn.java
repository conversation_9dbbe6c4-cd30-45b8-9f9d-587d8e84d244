package jxy2.supet;

import org.come.Frame.MsgJframe;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.login.SpriteBtn;
import org.come.mouslisten.TemplateMouseListener;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;

public class PetskillSprieBtn  extends TemplateMouseListener {
    public int i;
    public SpriteBtn btn;
    public PetSkillsJpanel petSkillsJpanel;
    public PetskillSprieBtn(int i, SpriteBtn btn, PetSkillsJpanel petSkillsJpanel) {
        super();
        this.i = i;
        this.btn = btn;
        this.petSkillsJpanel=petSkillsJpanel;
    }


    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (btn.isChoose()) {
            if (btn.getZhen() != 2) {// 选择类 选中
                btn.btn(2);
                btn.setXz(2);
            } else {
                // 选择类 取消选中
                btn.btn(0);
            }
        } else {
            btn.btn(2);
        }

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        if (btn.isChoose()) {
            btn.btn(0);}
        String msg = Agreement.getAgreement().DiceSkillAgreement("S"+ UserMessUntil.getChosePetMes().getSid());
        SendMessageUntil.toServer(msg);
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        if (btn.getZhen() != 2) {btn.btn(1);}
        petSkillsJpanel.IsStopSp = MsgJframe.getJframe().getJapnel().getTimes()==0;
        MsgJframe.getJframe().getJapnel().Pointskills();

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        if (btn.getZhen() != 2) {btn.btn(0);}
        if (btn.getXz()!=2) {btn.btn(0);}
        FormsManagement.HideForm(46);
        petSkillsJpanel.IsStopSp = MsgJframe.getJframe().getJapnel().getTimes()==0;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }


}
