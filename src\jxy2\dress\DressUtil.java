package jxy2.dress;

import com.tool.role.RoleData;
import org.come.bean.LoginResult;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* 换装设置工具类
* <AUTHOR>
* @date 2024/10/24 上午8:54
*/
public class DressUtil {
    /**获取装备id*/
    public static BigDecimal EuRid(int index,int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getOneclick()!=null&& !result.getOneclick().isEmpty()){
            String[] vs = result.getOneclick().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                String[] goodid = vss[1].split("\\|");
                if (Integer.parseInt(vss[0]) == type) {
                    for (String s : goodid) {
                        int path = Integer.parseInt(s.split("_")[0]);
                        if (index==path) {
                            return new BigDecimal(s.split("_")[1]);
                        }
                    }
                }
            }
        }
        return null;
    }
    /**获取装备位置*/
    public static List<Integer> EuRid(int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
        List<Integer> ids = new ArrayList<>();
        if (result.getOneclick()!=null&& !result.getOneclick().isEmpty()){
            String[] vs = result.getOneclick().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                String[] goodid = vss[1].split("\\|");
                if (Integer.parseInt(vss[0]) == type) {
                        for (String s : goodid) {
                            ids.add(Integer.parseInt(s.split("_")[0]));
                        }
                }
            }
        }
        return ids;
    }

    /**获取灵宝id*/
    public static BigDecimal LingBaoRid(int index,int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getClicklingbao()!=null && !result.getClicklingbao().isEmpty()){
            String[] vs = result.getClicklingbao().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                String[] lingbaoid = vss[1].split("\\|");
                if (Integer.parseInt(vss[0]) == type) {
                    for (String s : lingbaoid) {
                        int path = Integer.parseInt(s.split("_")[0]);
                        if (index==path) {
                            return new BigDecimal(s.split("_")[1]);
                        }
                    }
                }
            }
        }
        return null;
    }
    /**获取星盘技能id*/
    public static String[] XpSkillRid(int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getXpskill()!=null && !result.getXpskill().isEmpty()){
            String[] vs = result.getXpskill().split("&");
            for (String v : vs) {
                String[] vss = v.split("·");
//                String[] xpskill = vss[1].split("_");
                if (Integer.parseInt(vss[0]) == type) {
                    return  vss;
                }
            }
        }
        return null;
    }
    /**获取星卡*/
    public static BigDecimal XKSRid(int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getStatcard()!=null && !result.getStatcard().isEmpty()){
            String[] vs = result.getStatcard().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                if (Integer.parseInt(vss[0]) ==type){
                    return new BigDecimal(vss[1]);
                }
            }
        }
        return null;
    }




    /**清除灵宝对应的值_从新拼接字符*/
    public static void removeClicklingbaoByType(LoginResult result, int type) {
        String clicklingbao = result.getClicklingbao();
        if (clicklingbao == null || clicklingbao.isEmpty()) {
            return;
        }
        String[] lb = clicklingbao.split("&");
        StringBuilder newClicklingbao = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("=", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newClicklingbao.length() > 0) {
                    newClicklingbao.append("&");
                }
                newClicklingbao.append(item);
            }
        }
        result.setClicklingbao(newClicklingbao.toString());
    }

    /**
     * 清除装备对应的值_从新拼接字符
     * @param result
     * @param type
     */
    public static void removeEquipmentByType(LoginResult result, int type) {
        String oneclick = result.getOneclick();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("=", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setOneclick(newoneclick.toString());
    }

    /**
     *清除属性对应的值_从新拼接字符
     * @param result
     * @param type
     */
    public static void removeSxtByType(LoginResult result, int type) {
        String storesa = result.getStoresa();
        if (storesa == null || storesa.isEmpty()) {
            return;
        }
        String[] sx = storesa.split("@");
        StringBuilder newstoresa = new StringBuilder();
        for (String item : sx) {
            String[] parts = item.split("·", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newstoresa.length() > 0) {
                    newstoresa.append("@");
                }
                newstoresa.append(item);
            }
        }
        result.setStoresa(newstoresa.toString());
    }

    /**
     *
     * 清除对应星卡
     * @param result
     * @param type
     */
    public static void removeStarCard(LoginResult result, int type) {
        String oneclick = result.getStatcard();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("=", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setStatcard(newoneclick.toString());
    }
    /**
     *
     * 清除对应小成
     * @param result
     * @param type
     */
    public static void removeXc(LoginResult result, int type) {
        String oneclick = result.getXcpractice();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("@", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setXcpractice(newoneclick.toString());
    }
    /**
     *
     * 清除对应大成
     * @param result
     * @param type
     */
    public static void removeDc(LoginResult result, int type) {
        String oneclick = result.getDcpractice();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("@", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setDcpractice(newoneclick.toString());
    }

    /**
     *
     * 清除对应星盘技能
     * @param result
     * @param type
     */
    public static void removeXp(LoginResult result, int type) {
        String oneclick = result.getXpskill();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("·", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setXpskill(newoneclick.toString());
    }
    /**清除对应的天演策*/
    public static void removeTYC(LoginResult result, int type) {
        String oneclick = result.getTycclick();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("·", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setTycclick(newoneclick.toString());
    }

    public static void removeFM(LoginResult result, int type) {
        String oneclick = result.getLawlick();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("·", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setLawlick(newoneclick.toString());
    }


    /**清除对应的守护石*/
    public static void removeShtStonType(LoginResult result, int type) {
        String oneclick = result.getStid();
        if (oneclick == null || oneclick.isEmpty()) {
            return;
        }
        String[] lb = oneclick.split("&");
        StringBuilder newoneclick = new StringBuilder();
        for (String item : lb) {
            String[] parts = item.split("_", 2);
            if (parts.length == 2 && Integer.parseInt(parts[0]) != type) {
                if (newoneclick.length() > 0) {
                    newoneclick.append("&");
                }
                newoneclick.append(item);
            }
        }
        result.setStid(newoneclick.toString());
        String sendmes = Agreement.getAgreement().rolechangeAgreement("8"+ GsonUtil.getGsonUtil().getgson().toJson(result));
        SendMessageUntil.toServer(sendmes);
    }
}
