package jxy2.wdfDome.bean;

import java.util.ArrayList;
import java.util.Date;

public class UpdateAdapter implements Runnable{
    private boolean alive;
    private final ArrayList<UpdateInterface> list;
    private long sleepTime = 0L;
    public UpdateAdapter(int frame) {
        this.setFrame(frame);
        this.list = new ArrayList();
    }
    public void setFrame(int frame) {
        this.sleepTime = 1000 / frame;
    }

    public void add(UpdateInterface u) {
        this.list.add(u);
    }

    public void remove(UpdateInterface u) {
        this.list.remove(u);
    }

    public void removeAll() {
        this.list.clear();
    }

    public void start() {
        (new Thread(this)).start();
        this.alive = true;
    }

    public void stop() {
        this.alive = false;
    }


    @Override
    public void run() {
        while(true) {
            try {
                if (this.alive) {
                    long time = (new Date()).getTime();
                    for (UpdateInterface updateInterface : this.list) {
                        updateInterface.updateNow();
                    }

                    time = (new Date()).getTime() - time;
                    if (time < this.sleepTime) {
                        Thread.sleep(this.sleepTime - time);
                    }
                    continue;
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            return;
        }
    }
}
