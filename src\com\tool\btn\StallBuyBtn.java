package com.tool.btn;

import com.tool.Stall.Commodity;
import com.tool.Stall.StallBuy;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.StallBuyJpanel;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class StallBuyBtn extends MoBanBtn {
    // 记录当前购买的商品
    private Commodity commodity;
    private StallBuyJpanel buyJpanel;

    public StallBuyBtn(String path, int type, Color[] colors, Font font, String text, StallBuyJpanel buyJpanel) {
        super(path, type, colors);
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.buyJpanel = buyJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        if (e.getButton() != MouseEvent.BUTTON1) {
            return;
        }
        if (commodity == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("你还没有选中商品");
            return;
        }
        int sum = Integer.parseInt(buyJpanel.getTextNumber().getText());
        if (sum <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("你购买数量为0");
            return;
        }
        Goodstable good = commodity.getGood();
        RoleSummoning pet = commodity.getPet();
        if (good != null) {
            // 判断数量是否足够
            if (good.getUsetime() < sum) {
                ZhuFrame.getZhuJpanel().addPrompt2("物品数量不足");
                return;
            }
            sum = GoodsListFromServerUntil.Surplussum(good.getType() + "", good.getGoodsid() + "", sum);
            if (sum <= 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你背包已满");
                return;
            }
            LoginResult loginResult = RoleData.getRoleData().getLoginResult();
            if (loginResult.getGold().longValue() < (commodity.getMoney() * sum)) {
                ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                return;
            }
            StallBuy stallBuy = new StallBuy();
            stallBuy.setId(buyJpanel.getId());
            stallBuy.setRoleid(loginResult.getRole_id());
            stallBuy.setType(0);
            stallBuy.setBuyid(good.getRgid());
            stallBuy.setSum(sum);
            String sendMes = Agreement.getAgreement().stallbuyAgreement(
                    GsonUtil.getGsonUtil().getgson().toJson(stallBuy));
            SendMessageUntil.toServer(sendMes);
        } else if (pet != null) {
//            int x =  TestPetJframe.getTestPetJframe().getPetMainJPanel().getPetsum();
//            if (UserMessUntil.getPetListTable() != null && UserMessUntil.getPetListTable().size() >= x) {
//                ZhuFrame.getZhuJpanel().addPrompt2("您的召唤兽可携带的数量已满！！！");
//                return;
//            }
            LoginResult loginResult = RoleData.getRoleData().getLoginResult();
            if (loginResult.getGold().longValue() < commodity.getMoney()) {
                ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                return;
            }
            StallBuy stallBuy = new StallBuy();
            stallBuy.setId(buyJpanel.getId());
            stallBuy.setRoleid(loginResult.getRole_id());
            stallBuy.setType(1);
            stallBuy.setBuyid(pet.getSid());
            stallBuy.setSum(1);
            String sendMes = Agreement.getAgreement().stallbuyAgreement(
                    GsonUtil.getGsonUtil().getgson().toJson(stallBuy));
            SendMessageUntil.toServer(sendMes);
        }
    }

    public Commodity getCommodity() {
        return commodity;
    }

    public void setCommodity(Commodity commodity) {
        this.commodity = commodity;
    }

}
