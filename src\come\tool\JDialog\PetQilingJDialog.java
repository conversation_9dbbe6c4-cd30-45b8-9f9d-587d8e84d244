package come.tool.JDialog;

import org.come.bean.PetOperationDTO;
import org.come.entity.RoleSummoning;
import org.come.until.SendRoleAndRolesummingUntil;

public class PetQilingJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            RoleSummoning pet = (RoleSummoning)object;
            PetOperationDTO dto = new PetOperationDTO();
            dto.setPetId(pet.getSid());
            dto.setOperationType("PET_QILING");
            pet.setQiling(pet.getQiling()+1);
            SendRoleAndRolesummingUntil.sendRoleSumming(dto);
        }
    }
}
