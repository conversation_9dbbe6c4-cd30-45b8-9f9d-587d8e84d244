package com.tool.btn;

import com.tool.tcpimg.UIUtils;
import jxy2.backutil.AssGoodUntil;
import org.come.Frame.PetEquipmentJframe;
import org.come.Jpanel.PetEquipmentJpanel;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class additionalgoodbtn extends MoBanBtn{
    private JPanel jpanel;
    // 按钮位置额外
    private int path;

    public additionalgoodbtn(String iconpath, int type, JPanel jpanel, int path,String name) {
        super(iconpath, type,0, UIUtils.COLOR_BTNTEXT,"");
        this.jpanel = jpanel;
        this.path = path;
        this.setText(name);
        setFont(UIUtils.TEXT_FONT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public void dianjisz() {
        type = 2;
        btnchange(2);
        if (jpanel instanceof PetEquipmentJpanel) {
            PetEquipmentJpanel equipmentJpanel = (PetEquipmentJpanel) jpanel;
            BtnUtil.btnBinding(equipmentJpanel.getBtnrights(), path);
        }
    }
    @Override
    public void chooseyes() {
        PetEquipmentJframe.getPetEquipmentJframe().getEquipmentJpanel().getBtnrights()[path].dianjisz();
        AssGoodUntil.getgood(path);
    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

    }
}
