package jxy2.refine;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import jxy2.jutnil.Juitil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class RefineBtn extends MoBanBtn {
    public int typeBtn;//提示
    public RefineJPanel refineJPanel;
    public RefineBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, RefineJPanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        int minType;
        BtnMoni(typeBtn);
        refineJPanel.getRichLabel().setText("");
        refineJPanel.getCardJpanel().getNewEquiJpanel().clear();
        refineJPanel.setType(typeBtn);
        refineJPanel.getCardJpanel().getCar().show(refineJPanel.getCardJpanel(),"l"+(typeBtn+1));
        switch (typeBtn){
        case 0:
            String text  = Juitil.TextInedx(9);
            refineJPanel.getRichLabel().setTextSize(text,160);
            break;
        case 1:
             minType = refineJPanel.getCardJpanel().getEqartifice().getMinType();
            refineJPanel.getRichLabel().setTextSize(Juitil.getPrompt(1,minType),160);
            break;
        case 2:
            minType = refineJPanel.getCardJpanel().getEqmMakeJpanel().getMinType();
            refineJPanel.getRichLabel().setTextSize(Juitil.getPrompt(2,minType),160);
            break;
        case 3:
            refineJPanel.getRichLabel().setTextSize(Juitil.TextInedx(7),160);
            break;
        case 4:
            break;
        case 5:
            CollectionJpanel collectionJanel = RefineFrame.getRefineFrame().getRefineJPanel().getCardJpanel().getCollectionJanel();
            collectionJanel.clear();
            List<Integer> listSuit = RoleData.getRoleData().getPackRecord().accessCollect();
            collectionJanel.getListModel().removeAllElements();
            CollectionJpanel.suitNum = 0;
            if (listSuit != null && !listSuit.isEmpty()) {
                CollectionJpanel.suitNum = listSuit.size();
                for (int i = 0; i < listSuit.size(); i++) {
                    collectionJanel.getListModel()
                            .add(i, UserMessUntil.getAllSuit().getRolesuit().get(listSuit.get(i)).getSname());
                }
            }
            collectionJanel.clear();

            break;
            case 6:
                 minType = refineJPanel.getCardJpanel().getSymbolJpanel().getMinType();
                refineJPanel.getRichLabel().setTextSize(Juitil.getPrompt(6, minType), 160);
                break;
            case 7:
            minType = refineJPanel.getCardJpanel().getGodJpanel().getMinType();
            refineJPanel.getRichLabel().setTextSize(Juitil.getPrompt(7,minType),160);
            break;
            case 8:
                minType = refineJPanel.getCardJpanel().getGemsTJpanel().getMinType();
                refineJPanel.getRichLabel().setTextSize(Juitil.getPrompt(8,minType),160);
                break;
        }
    }

    private void BtnMoni(int typeBtn){
        refineJPanel.getForging()[typeBtn].btnchange(2);
        for (int i = 0; i < refineJPanel.getForging().length; i++) {
            if (i!=typeBtn){
                refineJPanel.getForging()[i].btnchange(0);
            }
        }
    }
}
