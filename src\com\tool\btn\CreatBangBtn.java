package com.tool.btn;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.CreatBangJpanel;
import org.come.Jpanel.JoinBangJpanel;
import org.come.entity.Gang;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import com.tool.image.ImageMixDeal;
import com.tool.tcpimg.UIUtils;

public class CreatBangBtn extends MoBanBtn{

	private CreatBangJpanel bangJpanel;
	private JoinBangJpanel joinBangJpanel;
	public CreatBangBtn(String iconpath, int type,String text,CreatBangJpanel bangJpanel) {
		super(iconpath, type,UIUtils.COLOR_BTNPUTONG);
		// TODO Auto-generated constructor stub
		this.setText(text);
		setFont(UIUtils.TEXT_HY16);	
		setVerticalTextPosition(SwingConstants.CENTER); 
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.bangJpanel=bangJpanel;
	}
	public CreatBangBtn(String iconpath, int type,String text,JoinBangJpanel joinBangJpanel) {
		super(iconpath, type,UIUtils.COLOR_BTNPUTONG);
		// TODO Auto-generated constructor stub
		this.setText(text);
		setFont(UIUtils.TEXT_HY16);	
		setVerticalTextPosition(SwingConstants.CENTER); 
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.joinBangJpanel=joinBangJpanel;
	}
	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		BigDecimal gangid=ImageMixDeal.userimg.getRoleShow().getGang_id();
		if (gangid!=null&&gangid.intValue()!=0) {
			ZhuFrame.getZhuJpanel().addPrompt2("你已有帮派");
			return;
		}
		if (joinBangJpanel!=null) {
			int index = joinBangJpanel.getTableMsg().getSelectedRow();
			if (index==-1) {
				ZhuFrame.getZhuJpanel().addPrompt2("你未选中要加入的帮派");
				return;
			}
			Gang gang = joinBangJpanel.getGangs().get(index);
			String sendMes = Agreement.getAgreement().GangApplyAgreement(gang.getGangid().toString());
			SendMessageUntil.toServer(sendMes);
		}else if (bangJpanel!=null) {
			if (bangJpanel.getSetword1().getText().equals("")) {
				ZhuFrame.getZhuJpanel().addPrompt2("帮派名为空");
			}else if (bangJpanel.getSetword2().getText().equals("")) {
				ZhuFrame.getZhuJpanel().addPrompt2("帮派宗旨为空");
			}else {
				int cx=GoodsListFromServerUntil.chaxuns(501);
				if (cx!=-1){
					//发送消息
					Gang gangCreate=new Gang();
					gangCreate.setGangname(bangJpanel.getSetword2().getText());
					gangCreate.setIntroduction(bangJpanel.getSetword1().getText());			
					String senmes=Agreement.getAgreement().GangCreateAgreement(GsonUtil.getGsonUtil().getgson().toJson(gangCreate));
					SendMessageUntil.toServer(senmes);//向服务器发送信息
				}else {
					 ZhuFrame.getZhuJpanel().addPrompt2("你背包没有三界召集令");	
				}
		        FormsManagement.HideForm(25);
			}
		}
    }

}
