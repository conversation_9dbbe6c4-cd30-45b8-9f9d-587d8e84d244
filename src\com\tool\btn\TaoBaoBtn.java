package com.tool.btn;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.event.MouseEvent;
import java.util.Vector;

import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;

import org.come.Jpanel.DailyLoginJPanel;
import org.come.Jpanel.QuotaJpanel;
import org.come.Jpanel.SeventyTwoChangesJpanel;
import org.come.Jpanel.ShopOnlineJpanel;
import org.come.Jpanel.TaobaoCourtCardJpanel;
import org.come.Jpanel.TaobaoCourtMainJpanel;
import org.come.until.CutButtonImage;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;

public class TaoBaoBtn extends MoBanBtn {

    /**
     * 充值1 商城2 锦绣3 限购4 周卡5 月卡6 基金7 <br>
     * 珍品10, 技能11, 神兽12, 仙器13, 配饰14, 积分15 <br>
     * (多宝阁商城 -- 首页20 上一页21 下一页22 末页23)<br>
     * 欢送好礼30 冲击必备31 限时折扣32 <br>
     * (多宝阁充值 -- 首页40 上一页41 下一页42 末页43) <br>
     * (多宝阁限购 -- 首页50 上一页51 下一页52 末页53) <br>
     * 每日签到60 周/月卡领奖61 <br>
     * 自定义70
     */
    private int caozuo;
    private QuotaJpanel quotaJpanel;
    private DailyLoginJPanel dailyLoginJPanel;
    private SeventyTwoChangesJpanel seventyTwoChangesJpanel;

    private TaobaoCourtMainJpanel taobaoCourtMainJpanel;
    private ShopOnlineJpanel shopOnlineJpanel;

    public TaoBaoBtn(String iconpath, int type, String text, int caozuo) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.setText(text);
        if (caozuo > 9 && caozuo < 20) {
            setFont(UIUtils.TEXT_HY16);
            setForeground(Color.white);
        } else if (caozuo > 19 && caozuo < 24) {
            setFont(UIUtils.TEXT_FONT);
            setForeground(Color.white);
        } else if (caozuo > 23 && caozuo < 30) {
            setFont(UIUtils.TEXT_HY12);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY19);
            setForeground(UIUtils.COLOR_BTNXUANXIANGKA[0]);
        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public TaoBaoBtn(String iconpath, int type, String text, int caozuo, ShopOnlineJpanel shopOnlineJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.shopOnlineJpanel = shopOnlineJpanel;
        this.setText(text);
        if (caozuo > 19 && caozuo < 24) {
            setFont(UIUtils.TEXT_FONT);
            setForeground(Color.white);
        } else if (caozuo > 23 && caozuo < 30) {
            setFont(UIUtils.TEXT_HY12);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY19);
            setForeground(UIUtils.COLOR_BTNXUANXIANGKA[0]);
        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public TaoBaoBtn(String iconpath, int type, String text, int caozuo, TaobaoCourtMainJpanel taobaoCourtMainJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.taobaoCourtMainJpanel = taobaoCourtMainJpanel;
        this.setText(text);
        if (caozuo > 9 && caozuo < 20) {
            setFont(UIUtils.TEXT_HY16);
            setForeground(Color.white);
        } else if (caozuo > 19 && caozuo < 30) {
            setFont(UIUtils.TEXT_HY12);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY19);
            setForeground(UIUtils.COLOR_BTNXUANXIANGKA[0]);
        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public TaoBaoBtn(String iconpath, int type, String text, int caozuo, QuotaJpanel quotaJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.setText(text);
        this.quotaJpanel = quotaJpanel;
        if (caozuo > 9) {
            setFont(UIUtils.TEXT_HY16);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY19);
            setForeground(UIUtils.COLOR_BTNXUANXIANGKA[0]);
        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    /** 60-79 */
    public TaoBaoBtn(String iconpath, int type, String text, int caozuo, DailyLoginJPanel dailyLoginJPanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.setText(text);
        this.dailyLoginJPanel = dailyLoginJPanel;
        if (caozuo > 59 && caozuo < 70) {
            setFont(UIUtils.TEXT_FONT1);
            setForeground(Color.black);
        } else if (caozuo > 69 && caozuo < 80) {
            setFont(UIUtils.TEXT_FONT2);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY19);
            setForeground(UIUtils.COLOR_BTNXUANXIANGKA[0]);
        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    /** 80-99 */
    public TaoBaoBtn(String iconpath, int type, String text, int caozuo, SeventyTwoChangesJpanel seventyTwoChangesJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.setText(text);
        this.seventyTwoChangesJpanel = seventyTwoChangesJpanel;
        if (caozuo > 79 && caozuo < 90) {
            setFont(UIUtils.TEXT_FONT);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_HY14);
            // new
            // font=font.deriveFont(Font.BOLD);

        }

        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        try {
            switch (caozuo) {
            /** 7-17-HGC-start */
            case 2:
            case 3:
            case 6:
            case 7:
                int menuType = taobaoCourtMainJpanel.getTaobaoCourtCardJpanel().getMenuType();
                TaobaoCourtCardJpanel taobaoCourtCardJpanel = taobaoCourtMainJpanel.getTaobaoCourtCardJpanel();
                if (menuType != caozuo) {
                    if (menuType == 2) {
                        taobaoCourtMainJpanel.getShopMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B262.png"));
                    } else if (menuType == 3) {
                        taobaoCourtMainJpanel.getTaobaoMenuBtn()
                                .setIcons(CutButtonImage.cuts("inkImg/button/B260.png"));
                    } else if (menuType == 6) {
                        taobaoCourtMainJpanel.getMonthCardBtn().setIcons(CutButtonImage.cuts("inkImg/button/B264.png"));
                    } else if (menuType == 7) {
                        taobaoCourtMainJpanel.getVipMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B258.png"));
                    }
                    taobaoCourtMainJpanel.getTaobaoCourtCardJpanel().setMenuType(caozuo);
                    menuType = taobaoCourtMainJpanel.getTaobaoCourtCardJpanel().getMenuType();
                }
                if (menuType == 2) {
                    taobaoCourtMainJpanel.getShopMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B263.png"));
                    taobaoCourtCardJpanel.getCardLayout().show(taobaoCourtCardJpanel, "shopOnline");
                } else if (menuType == 3) {
                    taobaoCourtMainJpanel.getTaobaoMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B261.png"));
                    taobaoCourtCardJpanel.getCardLayout().show(taobaoCourtCardJpanel, "taobaoCourt");
                    taobaoCourtCardJpanel.getTaobaoCourtSplendidJpanel().showTaobao();
                } else if (menuType == 6) {
                    taobaoCourtMainJpanel.getMonthCardBtn().setIcons(CutButtonImage.cuts("inkImg/button/B265.png"));
                    taobaoCourtCardJpanel.getMonthlyCardJpanel().changeTime();
                    taobaoCourtCardJpanel.getCardLayout().show(taobaoCourtCardJpanel, "monthCard");
                } else if (menuType == 7) {
                    taobaoCourtMainJpanel.getVipMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B259.png"));
                    taobaoCourtCardJpanel.getVipShopJpanel().getShop();
                }
                break;
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
                /** "珍品"-6, "技能"-7, "神兽"-8, "仙器"-9, "配饰"-10, "积分"-11 */
                int shopType = shopOnlineJpanel.getShopType();
                if (shopType != (caozuo - 4)) {
                    if (shopType == 6) {
                        shopOnlineJpanel.getTreasureMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B280.png"));
                    } else if (shopType == 7) {
                        shopOnlineJpanel.getSkillMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B282.png"));
                    } else if (shopType == 8) {
                        shopOnlineJpanel.getMythicalMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B252.png"));
                    } else if (shopType == 9) {
                        shopOnlineJpanel.getDeviceMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B288.png"));
                    } else if (shopType == 10) {
                        shopOnlineJpanel.getBaldricMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B250.png"));
                    } else if (shopType == 11) {
                        shopOnlineJpanel.getIntegralMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B246.png"));
                    }
                    shopType = caozuo - 4;
                    if (shopType == 6) {
                        shopOnlineJpanel.getTreasureMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B281.png"));
                    } else if (shopType == 7) {
                        shopOnlineJpanel.getSkillMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B283.png"));
                    } else if (shopType == 8) {
                        shopOnlineJpanel.getMythicalMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B253.png"));
                    } else if (shopType == 9) {
                        shopOnlineJpanel.getDeviceMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B289.png"));
                    } else if (shopType == 10) {
                        shopOnlineJpanel.getBaldricMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B251.png"));
                    } else if (shopType == 11) {
                        shopOnlineJpanel.getIntegralMenuBtn().setIcons(CutButtonImage.cuts("inkImg/button/B247.png"));
                    }
                }
                shopOnlineJpanel.setNowPage(1);
                shopOnlineJpanel.isShopGoods(caozuo - 4);
                break;
            /** 7-17-HGC-end */
            case 20:
                if (shopOnlineJpanel.getNowPage() <= 1) {
                    return;
                }
                shopOnlineJpanel.setNowPage(1);
                shopOnlineJpanel.isShopGoods(shopOnlineJpanel.getShopType());
                break;
            case 21:
                if (shopOnlineJpanel.getNowPage() <= 1) {
                    return;
                }
                shopOnlineJpanel.setNowPage(shopOnlineJpanel.getNowPage() - 1);
                shopOnlineJpanel.isShopGoods(shopOnlineJpanel.getShopType());
                break;
            case 22:
                if (shopOnlineJpanel.getNowPage() >= shopOnlineJpanel.getMaxPage()) {

                    return;
                }
                shopOnlineJpanel.setNowPage(shopOnlineJpanel.getNowPage() + 1);
                shopOnlineJpanel.isShopGoods(shopOnlineJpanel.getShopType());
                break;
            case 23:
                if (shopOnlineJpanel.getNowPage() >= shopOnlineJpanel.getMaxPage()) {
                    return;
                }
                shopOnlineJpanel.setNowPage(shopOnlineJpanel.getMaxPage());
                shopOnlineJpanel.isShopGoods(shopOnlineJpanel.getShopType());
                break;

            /** 7-18-HGC-start */
            // case 30:
            // changeMenuBtnQuota();
            //
            // break;
            // case 31:
            // changeMenuBtnQuota();
            //
            // break;
            // case 32:
            // changeMenuBtnQuota();
            //
            // break;

            // 兑换好礼exchangeMenuBtn<br>
            // * 冲级必备levelMenuBtn<br>
            // * 显示折扣discountMenuBtn<br>
            case 30:
                quotaJpanel.getExchangeMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_赠送好礼_选中_w100,h78.png"));
                quotaJpanel.getLevelMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_冲级必备_未选中_w100,h78.png"));
                quotaJpanel.getDiscountMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_显示折扣_未选中_w100,h78.png"));

                break;
            case 31:
                quotaJpanel.getExchangeMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_赠送好礼_未选中_w100,h78.png"));
                quotaJpanel.getLevelMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_冲级必备_选中_w100,h78.png"));
                quotaJpanel.getDiscountMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_显示折扣_未选中_w100,h78.png"));
                break;
            case 32:
                quotaJpanel.getExchangeMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_赠送好礼_未选中_w100,h78.png"));
                quotaJpanel.getLevelMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_冲级必备_未选中_w100,h78.png"));
                quotaJpanel.getDiscountMenuBtn().setIcons(
                        CutButtonImage.cuts("img/xy2uiimg/二级选项卡_商城_限购_显示折扣_选中_w100,h78.png"));
                break;
            /** 7-18-HGC-end */
            case 60:
                changeMenuBtnDailyLogin();
                break;
            case 61:
                changeMenuBtnDailyLogin();
                break;
            case 80:
                changeMenuBtnSeventyTwoChanges();
                break;
            case 81:
                changeMenuBtnSeventyTwoChanges();
                break;
            case 90:
                RichLabel view = (RichLabel) seventyTwoChangesJpanel.getScrollPaneAttribute().getViewport().getView();
                StringBuffer buffer = new StringBuffer();
                buffer.append("#cffffff11111111111111111#r");
                buffer.append("#cff000022222222222222222");
                buffer.append("#cffff0033333333333333333");
                buffer.append("#c00000013215121651#r");
                buffer.append("#cffffff651111235123511561321");
                buffer.append("#cffffff3216511111132151321332");
                buffer.append("#cffffff11111111111111111#r");
                buffer.append("#cffffff11111111111111111");
                buffer.append("#cffffff11111111111111111");
                buffer.append("#cffffff11111111111111111#r");
                buffer.append("#cff000022222222222222222");
                buffer.append("#cffff0033333333333333333");
                buffer.append("#c00000013215121651#r");
                buffer.append("#cffffff651111235123511561321");
                buffer.append("#cffffff3216511111132151321332");
                buffer.append("#cffffff11111111111111111#r");
                buffer.append("#cffffff11111111111111111");
                buffer.append("#cffffff11111111111111111");
                view = new RichLabel(buffer.toString(), UIUtils.TEXT_FONT2);
                Dimension d = view.computeSize(130);
                view.setSize(d);
                view.setPreferredSize(d);
                seventyTwoChangesJpanel.getScrollPaneAttribute().setViewportView(view);
                // seventyTwoChangesJpanel.getScrollPaneAttribute().updateUI();
                // seventyTwoChangesJpanel.getScrollPaneAttribute().invalidate();
                // seventyTwoChangesJpanel.getScrollPaneAttribute().validate();
                // seventyTwoChangesJpanel.getScrollPaneAttribute().repaint();
                break;
            case 91:
                DefaultTableModel tableModel = seventyTwoChangesJpanel.getTableModel();
                tableModel.getDataVector().clear();
                for (int i = 0; i < 20; i++) {
                    Vector<String> vectorRank = new Vector<>();
                    vectorRank.add("弟" + i);
                    vectorRank.add("弟" + i);
                    vectorRank.add("弟" + i);
                    vectorRank.add("弟" + i);
                    tableModel.addRow(vectorRank);
                }

                break;
            default:
                break;
            }
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }

    /** 7-17-HGC-start */
    /** 珍品10, 技能11, 神兽12, 仙器13, 配饰14, 积分15 */
    // public void changeMenuView() throws Exception {
    // for (int i = 0; i < shopOnlineJpanel.getMenuLable().length; i++) {
    // if ((i + 10) == caozuo) {
    // shopOnlineJpanel.getMenuLable()[i].setIcons(CutButtonImage.cuts("inkImg/button/4.png"));
    // } else {
    // shopOnlineJpanel.getMenuLable()[i].setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
    // }
    // }
    // }

    // /**
    // * 多宝阁之间的面板切换菜单项修改
    // *
    // * @throws Exception
    // */
    // public void changeMenuTaoBao() throws Exception {
    // for (int i = 0; i < taobaoCourtMainJpanel.getMenuBtn().length; i++) {
    // if ((i + 1) == caozuo) {
    // taobaoCourtMainJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/5.png"));
    // } else {
    // taobaoCourtMainJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/6.png"));
    // }
    // }
    // }
    // /**
    // * 限购子面板切换
    // *
    // * @throws Exception
    // */
    // public void changeMenuBtnQuota() throws Exception {
    // for (int i = 0; i < quotaJpanel.getMenuBtn().length; i++) {
    // if ((i + 30) == caozuo) {
    // quotaJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/4.png"));
    // } else {
    // quotaJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
    // }
    // }
    // }
    //
    /** 7-17-HGC-end */

    /**
     * 每日登录面板切换
     * 
     * @throws Exception
     */
    public void changeMenuBtnDailyLogin() throws Exception {
        for (int i = 0; i < dailyLoginJPanel.getMenuBtn().length; i++) {
            if ((i + 60) == caozuo) {
                dailyLoginJPanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/5.png"));
                dailyLoginJPanel.getMenuStr().setText(dailyLoginJPanel.getMenuName()[i]);
            } else {
                dailyLoginJPanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/6.png"));
            }
        }
    }

    /**
     * 人物七十二变面板切换
     * 
     * @throws Exception
     */
    public void changeMenuBtnSeventyTwoChanges() throws Exception {
        for (int i = 0; i < seventyTwoChangesJpanel.getMenuBtn().length; i++) {
            if ((i + 80) == caozuo) {
                seventyTwoChangesJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/4.png"));
            } else {
                seventyTwoChangesJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
            }
        }
    }
}
