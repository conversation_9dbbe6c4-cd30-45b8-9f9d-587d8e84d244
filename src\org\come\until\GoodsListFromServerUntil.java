package org.come.until;

import com.tool.Stall.Commodity;
import com.tool.Stall.Stall;
import com.tool.Stall.StallBean;
import com.tool.btn.*;
import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.tcpimg.UIUtils;
import com.tool.time.TimeLimit;
import jxy2.backutil.AlchemyGoodUntil;
import jxy2.backutil.AssGoodUntil;
import jxy2.guaed.fl.AsoulJPanel;
import jxy2.guaed.fl.FuLingFrame;
import jxy2.guaed.fl.RonglianJPanel;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.npk.NpkImageReader;
import jxy2.refine.RefineFrame;
import jxy2.soul.ResetSkillControl;
import jxy2.soul.ResetSoulSkillFrame;
import jxy2.soul.ResetSoulSkillJPanl;
import jxy2.supet.SipetJPanel;
import jxy2.synt.SynthesisFrame;
import jxy2.synt.SynthesisJPanl;
import jxy2.zodiac.RStarSoulsFrame;
import jxy2.zodiac.RStarSoulsJPanel;
import org.come.Frame.*;
import org.come.Jpanel.GemRefineryMainJpanel;
import org.come.Jpanel.TestpackJapnel;
import org.come.Jpanel.WorkshopRefiningCardJpanel;
import org.come.bean.GoodTrans;
import org.come.bean.IncludedPart;
import org.come.bean.PathPoint;
import org.come.entity.Goodstable;
import org.come.entity.PackRecord;
import org.come.entity.RoleSummoning;
import org.come.model.InternalForm;
import org.come.mouslisten.GoodsMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.starcard.*;
import org.wing.panel.WingMainFrame;
import org.wing.panel.WingMainPanel;

import javax.swing.*;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.math.BigDecimal;
import java.util.List;
import java.util.*;

/**
* 此类用来保存数据库发送的物品装备信息，方便使用和对数据操作后返还给服务器警醒修改
* <AUTHOR>
* @date 2024/12/21 上午8:26 重构
*/
@SuppressWarnings("all")
public class GoodsListFromServerUntil {
    private static Goodstable[] Goodslist = new Goodstable[24];// 背包物品的信息list
    private static Goodstable[] choseGoodsList = new Goodstable[14];// 穿上的物品集合下标13星卡
    private static List<Goodstable> wingGoodsList = new ArrayList<>(); // 翅膀物品集合
    private static List<Goodstable> starCardList = new ArrayList<>(); // 星卡物品集合
    private static List<Goodstable> XpGoodsList = new ArrayList<>(); // 星盘

    private static ImageIcon[] goodimg = new ImageIcon[24];// 存放当前背包页面的img
    private static ImageIcon lockimg = CutButtonImage.getWdfPng(ImgConstants.tz42,"defaut.wdf");
    public static boolean Nottrue = true;//本地控制显示于关闭
    public static boolean NottrueKs = true;//单独取消显示矿石等级
    public static int goodPlace ;


    public static void draw(Graphics g, int x, int y) {
        g.setFont(UIUtils.TEXT_FONT);
        g.setColor(Color.WHITE);
        for (int i = 0; i < 24; i++) {
            int row = i % 6 * 51;
            int col = i / 6 * 51;
            if (Goodslist[i + Pagenumber * 24] != null) {
                if (goodimg[i] != null) {
                   boolean is = GoodsMouslisten.list.contains(i + "");
                    int gx =is?x + row+1: x + row;
                    int gy = is?y + col + 3:y + col + 2;

                    if (Goodslist[i + Pagenumber * 24].getIsSelected() == 1&&RefineFrame.getRefineFrame().isVisible()) {
                        g.drawImage(Juitil.convertToGray(goodimg[i].getImage()), gx , gy, 50, 50, null);
                    }else {
                        g.drawImage(goodimg[i].getImage(), gx , gy, 50, 50, null);
                        Goodslist[i + Pagenumber * 24].setIsSelected(0);
                    }
                }
                packTimeUi(g, x, y, i, row, col);
                // 判断如果是已加锁，则加上加锁标志
                if (Goodslist[i + Pagenumber * 24].getGoodlock() == 1) {
                    g.drawImage(lockimg.getImage(), x + row + 26, y + col + 28, 15, 15, null);
                }

            }
        }
    }
    /**绘制叠加物品数量*/
    private static void packTimeUi(Graphics g, int x, int y, int i, int row, int col) {
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        int rx = x-1+row;
        int ry = y+13 + col;
        if (!EquipTool.isEquip(Goodslist[i + Pagenumber * 24].getType())) {//可叠加物品
            SipetJPanel.PJpanelText(g,"" + Goodslist[i + Pagenumber * 24].getUsetime(), rx+2, ry);
            if (NottrueKs&&Goodtype.Ore(Goodslist[i + Pagenumber * 24].getType())||Goodslist[i + Pagenumber * 24].getType()==8889){
                Goodstable good = Goodslist[i+Pagenumber *24];
                if (!good.getValue().isEmpty()) {
                    String lv = good.getValue().split("\\|")[0].split("=")[1];
                    Juitil.TextBackground(g, lv + "级", 13, rx + 18, ry + 14, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                }
            }
        }else if (Nottrue&&Goodslist[i + Pagenumber * 24].getType()==188||Goodtype.baoshi(Goodslist[i + Pagenumber * 24].getType())){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            String lv = good.getValue().split("\\|")[0].split("=")[1];
            Juitil.TextBackground(g, lv+"级", 13, rx+18, ry+15, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
        }else if (Nottrue&&Goodtype.ExerciseMonsterOre(Goodslist[i + Pagenumber * 24].getType())){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            String lv = good.getValue().split("\\|")[1].split("=")[1];
            Juitil.TextBackground(g, "+"+lv, 13, rx+20, ry-13, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
        }else if (Goodslist[i + Pagenumber * 24].getType()==7099&&Nottrue){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            if (!good.getValue().isEmpty()) {
            String lv = good.getValue().split("\\|")[0].split("=")[1];
                Juitil.TextBackground(g, lv + "阶", 13, rx + 18, ry + 14, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
            }
        }
        if (Goodslist[i + Pagenumber * 24].getType()==690&&Nottrue){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            if (!good.getValue().isEmpty()) {
            String lv = good.getValue().split("\\|")[0].split("=")[1];
                Juitil.TextBackground(g, lv + "级", 13, rx + 1, ry + 20, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
            }
        }
    }

    public static void drawBuffImage(Graphics g) {
//        bufferedImage.updateToTime(ImageMixDeal.userimg.getTime(),0);
//        bufferedImage.draw(g,8,22);
    }
    // 绘制装备和加锁状态
    public static void drawLock(Graphics g, TestpackJapnel testpackJapnel) {
        int  nxs = Util.SwitchUI==1?28:0;
        int  nys = Util.SwitchUI==1?-14:0;
        Graphics2D g2d = (Graphics2D) g;
        g2d.setColor(UIUtils.COLOR_Pack);
//        g2d.setColor(UIUtils.COLOR_CHENS);
        for (int i = 0; i < 12; i++) {
            PathPoint path = testpackJapnel.path(i);//值
            int row = path.getX()+nxs;
            int col = path.getY()+nys;
            if (choseGoodsList[i] != null) {
                g2d.fill(new RoundRectangle2D.Float(row , col,51, 51, 10, 10)); // 绘制圆角矩形
//                g2d.drawImage(imgpathWdfile(choseGoodsList[i].getSkin(),60,60).getImage(), row + 2, col + 2, 50, 50, null);
                // 判断如果是已加锁，则加上加锁标志时长
                if (choseGoodsList[i].getGoodlock() == 1) {
                    g2d.drawImage(lockimg.getImage(), row + 38, col + 2, 10, 12, null);
                }
            }
        }

    }

    // 绘制人物身上未装备、且不是套装的装备
    public static void drawIdlEqu(Graphics g, int x, int y, int page, int number, int para) {
        if (AccessSuitMsgUntil.accessIdlEqu(1) != null && AccessSuitMsgUntil.accessIdlEqu(1).size() > 0) {
            int size = (AccessSuitMsgUntil.accessIdlEqu(1).size() / number + (AccessSuitMsgUntil.accessIdlEqu(1).size()
                    % number == 0 ? 0 : 1))
                    * number;
            Goodstable[] Goodslist = new Goodstable[size];
            for (int i = 0; i < AccessSuitMsgUntil.accessIdlEqu(1).size(); i++) {
                Goodslist[i] = AccessSuitMsgUntil.accessIdlEqu(1).get(i);
            }
            for (int i = 0; i < number; i++) {
                int row = i % para * 51;
                int col = i / para * 51;
                if (Goodslist[i + page * number] != null) {
                    g.drawImage(imgpathWdfile(Goodslist[i + page * number].getSkin(),60,60).getImage(), x + row + 2, y + col + 2,
                            45, 45, null);
                    if (Goodslist[i + page * number].getGoodlock() == 1) {
                        g.drawImage(lockimg.getImage(), x + row + 38, y + col + 2, 10, 12, null);
                    }
                }
            }
        }
    }

    // 绘制人物身上的套装
    public static void drawRoleSuit(Graphics g, int x, int y, int page, int number) {
        if (AccessSuitMsgUntil.accessIdlEqu(2) != null && AccessSuitMsgUntil.accessIdlEqu(2).size() > 0) {
            int size = (AccessSuitMsgUntil.accessIdlEqu(2).size() / number + (AccessSuitMsgUntil.accessIdlEqu(2).size()
                    % number == 0 ? 0 : 1))
                    * number;
            Goodstable[] Goodslist = new Goodstable[size];
            for (int i = 0; i < AccessSuitMsgUntil.accessIdlEqu(2).size(); i++) {
                Goodslist[i] = AccessSuitMsgUntil.accessIdlEqu(2).get(i);
            }
            for (int i = 0; i < number; i++) {
                int row = i % 3 * 51;
                int col = i / 3 * 51;
                if (Goodslist[i + page * number] != null) {
                    g.drawImage(imgpathWdfile(Goodslist[i + page * number].getSkin(),60,60).getImage(), x + row + 2, y + col + 2,45, 45, null);
                    if (Goodslist[i + page * number].getGoodlock() == 1) {
                        g.drawImage(lockimg.getImage(), x + row + 38, y + col + 2, 10, 12, null);
                    }
                }
            }
        }
    }

    // 绘制已收录的套装部件
    public static void drawIncludedSuit(Graphics g,int suitid) {
        if (suitid <= 0) {return;}
        // 获取已收录套装的信息
        List<IncludedPart> parts = AccessSuitMsgUntil.getIncludedMsg(suitid);
        int centerX = 282; // 圆心的X坐标
        int centerY = 203; // 圆心的Y坐标
        int radius = 95;  // 圆的半径
        int  numElements = 8; // 元素数量
        double  angleIncrement = Math.PI / numElements * 2 ; // 角度增量，注意这里是π/3，因为我们只需要两次增量
        double  startAngle = Math.PI / 2; // 起始角度，指向正上方
//        ImageIcon placeholderImage = CutButtonImage.getWdfPng(ImgConstants.Goods_2,50,50,"defaut.wdf");
        Image placeholderImage = imgpathWdfile("tz55_6", 50, 50).getImage();
        for (int i = 0; i < numElements; i++) {
            double angle = startAngle + i * angleIncrement; // 当前元素的角度
           int  x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
            int  y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
            Image img = (i < parts.size()) ? imgpathWdfile("tz" + suitid + "_" + parts.get(i).getPartid(), 50, 50).getImage() : placeholderImage;
            g.drawImage(img, x, y, 50, 50, null);
           String vs =  (i < parts.size())?parts.get(i).getNumber() + "":"";
            Juitil.TextBackground(g, vs, 13, x, y, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        }
    }

    // 绘制背包身上的宝石
    public static void drawGemstoneOperationSuit(Graphics g, int number) {
        g.setFont(UIUtils.TEXT_FONT);
        g.setColor(Color.red);
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (!Goodtype.BS(Goodslist[i].getType())) {
                    continue;
                }
                g.drawImage(CutButtonImage.getGemstoneIcon(Goodslist[i].getSkin(), 51, 51).getImage(),
                        50 + number % 6 * 51, 308 + number / 6 * 51, 48, 48, null);
                if (!EquipTool.isEquip(Goodslist[i].getType())) {
                    g.drawString(Goodslist[i].getUsetime() + "", 54 + number % 6 * 51, 318 + number / 6 * 51);
                }
                // 判断如果是已加锁，则加上加锁标志
                if (Goodslist[i].getGoodlock() == 1) {
                    g.drawImage(lockimg.getImage(), 88 + number % 6 * 51, number / 6 * 51 + 310, 10, 12, null);
                }
                number++;
                if (number >= 12) {
                    return;
                }
            }
        }
    }


    //选择守护石判断
    public static Goodstable getGuardianOperationSuit(int p, int number) {
        number = -number * 12-1;
        for (Goodstable goodstable : Goodslist) {
            if (goodstable != null) {
                //判断是否是有相同属性的 守护石
                if (!Goodtype.SHS(goodstable.getType())) {continue;}
                number++;

                if (number == p) {
                    return goodstable;
                }

            }
        }
        return null;
    }


    // 绘制背包身上的守护石
    public static void drawGuardianOperationSuit(Graphics g, int number,int x,int y) {
        RonglianJPanel ronglianJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel();
        Goodstable good;
        if (FuLingFrame.getFuLingFrame().getFuLingJPanel().getType()==1&&ronglianJPanel.getType()==3){
            good = ronglianJPanel.getGoodstables()[0];  // 获取主守护石
            if (good!=null){
                // 获取主守护石的属性列表
                Map<String, String> mainAttributes = parseAttributes(good.getValue());
                number = - number * 12;
                for (Goodstable goodstable : Goodslist) {
                    int row = number % 6 * 51;
                    int col = number / 6 * 51;
                    // 只检查非空和类型是否为守护石
                    if (goodstable != null) {
                        // 过滤非守护石物品
                        if (!Goodtype.SHS(goodstable.getType())) {continue;}
                        // 解析当前物品的属性
                        Map<String, String> currentAttributes = parseAttributes(goodstable.getValue());
                        // 检查是否有相同的属性词条
                        boolean hasMatchingAttribute = false;
                        int matchCount = 0;
                        for (Map.Entry<String, String> entry : mainAttributes.entrySet()) {
                            if (currentAttributes.containsKey(entry.getKey())) {
                                matchCount++;
                                if (matchCount >= 1) {  // 至少有1个相同属性
                                    hasMatchingAttribute = true;
                                    break;
                                }
                            }
                        }
                        number++;
                        // 绘制物品图标
                        if (number >= 1) {
                            int xs = TemplateMouseListener.list.contains(number + "") ? 1 : 0;
                            Image itemImage = GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 51, 51).getImage();
                            if (hasMatchingAttribute && goodstable != good) {
                                // 正常显示有相同属性的物品
                                g.drawImage(itemImage, xs + x + row, xs + y + col, 48, 48, null);
                            } else {
                                // 将不符合条件的物品图标设置为灰色
                                Image grayImage = Juitil.convertToGray(itemImage);
                                g.drawImage(grayImage, xs + x + row, xs + y + col, 48, 48, null);
                            }
                            // 判断如果是已加锁，则加上加锁标志
                            if (goodstable.getGoodlock() == 1) {
                                g.drawImage(lockimg.getImage(), 88 + row, col + 310, 10, 12, null);
                            }

                            if (number >= 12) {
                                return;
                            }
                        }
                    }
                }
            }else {
                Originalattribute(g, number,16,21);
            }
        }else {
            Originalattribute(g, number,16,21);
        }
    }

    public static void Originalattribute(Graphics g, int number,int x,int y)
    {
        number = - number * 12;
        g.setFont(UIUtils.TEXT_FONT);
        g.setColor(Color.red);
        for (Goodstable goodstable : Goodslist) {

            int row = number % 6 * 51;
            int col = number / 6 * 51;
            // 只检查非空和类型是否为守护石
            if (goodstable != null) {

                // 过滤非守护石物品
                if (!Goodtype.SHS(goodstable.getType())) {
                    continue;
                }
                number++;
                if (number >= 1) {
                    int xs = TemplateMouseListener.list.contains(number-1 + "") ? 1 : 0;
                    g.drawImage(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 51, 51).getImage(),
                            xs + x + row, xs + y + col, 48, 48, null);
                    // 判断如果是已加锁，则加上加锁标志
                    if (goodstable.getGoodlock() == 1) {
                        g.drawImage(lockimg.getImage(), x + row+35, y+col +35, 15, 15, null);
                    }
                    if (number >= 12) {
                        return;
                    }
                }
            }
        }
    }
    // 解析物品属性为Map
    public static Map<String, String> parseAttributes(String value) {
        Map<String, String> attributes = new HashMap<>();
        if (value == null || value.isEmpty()) {
            return attributes;
        }
        String[] properties = value.split("\\|");
        for (String property : properties) {
            String[] keyValue = property.split("=");
            if (keyValue.length == 2) {
                // 排除特定属性
                if (!keyValue[0].startsWith("耐久度") && !keyValue[0].startsWith("锻造等级")) {
                    attributes.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return attributes;
    }
    public static Goodstable getGemstoneOperationSuit(int p, int number) {
        for (Goodstable goodstable : Goodslist) {
            if (goodstable != null) {
                if (!Goodtype.BS(goodstable.getType())) {
                    continue;
                }
                if (number == p) {
                    return goodstable;
                }
                number++;
            }
        }
        return null;
    }

    // 绘制背包身上的宝石
    public static void drawGemstoneSuit(Graphics g, int number, int type) {
        g.setFont(UIUtils.TEXT_FONT);
        g.setColor(Color.red);
        int num = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (!Goodtype.baoshi(type, Goodslist[i].getType())) {
                    continue;
                }
                g.drawImage(CutButtonImage.getGemstoneIcon(Goodslist[i].getSkin(), 50, 50).getImage(),
                        218 + num * 51, 284, 50, 50, null);
                if (!EquipTool.isEquip(Goodslist[i].getType())) {
                    g.drawString(Goodslist[i].getUsetime() + "", 228 + 23 + num * 51, 284);
                }
                //绘制符石等级
                Goodstable good = Goodslist[i];
                String lv = good.getValue().split("\\|")[0].split("=")[1];
                Juitil.TextBackground(g, lv+"级", 13, 220 + 23 + num * 51, 316, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                // 判断如果是已加锁，则加上加锁标志
                if (Goodslist[i].getGoodlock() == 1) {
                    g.drawImage(lockimg.getImage(), 272 + 23 + num * 51, 284, 10, 12, null);
                }
                num++;
                if (num >= number) {
                    return;
                }
            }
        }
    }


    // 绘制背包宠物物品
    public static void drawPetitme(Graphics g, int yss,int x ,int y) {
        yss = - yss * 24;
        for (int i = 0; i < Goodslist.length; i++) {
            int row = yss % 6 * 49;
            int col = yss / 6 * 49;
            if (Goodslist[i] != null) {
                if (!Goodtype.isSummonGoodsEquip(Goodslist[i].getType())) {
                    continue;
                }
                yss++;
                if (yss>=1) {
                    g.drawImage(imgpathWdfile(Goodslist[i].getSkin(), 60, 60).getImage(),
                            x + row, y + col, 45, 45, null);
                    if (!EquipTool.isEquip(Goodslist[i].getType())) {
                        Juitil.TextBackground(g, Goodslist[i].getUsetime() + "", 13, x + row, y + col, Color.WHITE, UIUtils.MSYH_HY13);
                    }
                    // 判断如果是已加锁，则加上加锁标志
                    if (Goodslist[i].getGoodlock() == 1) {
                        g.drawImage(lockimg.getImage(), 272 + 23 + yss * 51, 284, 10, 12, null);
                    }
                    if (Goodtype.ExerciseMonsterOre(Goodslist[i].getType())) {
                        g.setFont(UIUtils.MSYH_HY13);
                        int levelTwo = Integer.parseInt(Objects.requireNonNull(luanyaoshi(Goodslist[i])));
                        g.setColor(new Color(255, 255, 255));
                        if (levelTwo >= 10) {
                            Juitil.TextBackground(g, "+" + levelTwo, 13, x + 18 + row, y + 32 + col, Color.WHITE, UIUtils.MSYH_HY13B);
                        } else {
                            Juitil.TextBackground(g, "+" + levelTwo, 13, x + 18 + row, y + 32 + col, Color.WHITE, UIUtils.MSYH_HY13);
                        }
                    }
                    if (yss >= 24) {
                        return;
                    }
                }
            }
        }
    }
    /**解析炼妖石等级*/
    public static String luanyaoshi(Goodstable goods) {
        String Bottletext=goods.getValue();
        String[] split = Bottletext.split("\\|");
        String[] kx = split[1].split("=");
        return kx[1];
    }

    public static Goodstable getPetItem(int ys, int p) {
        ys = -ys * 24 - 1;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (!Goodtype.isSummonGoodsEquip(Goodslist[i].getType())) {
                    continue;
                }
                ys++;
                if (p == ys) {
                    return Goodslist[i];
                }

            }
        }
        return null;
    }


    public static Goodstable getGemstoneSuit(int p, int number, int type) {
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (!Goodtype.baoshi(type, Goodslist[i].getType())) {
                    continue;
                }
                if (number == p) {
                    return Goodslist[i];
                }
                number++;
            }
        }
        return null;
    }

    // 绘制参宝阁可出售物品
    public static void drawCBG(Graphics g, int x, int y, int ys) {
        ys = -ys * 30 - 1;
        for (int i = 0; i < Goodslist.length; i++) {
            Goodstable good = Goodslist[i];
            if (good == null || good.getGoodlock() == 1 || AnalysisString.jiaoyi(good.getQuality())
                    || !EquipTool.isEquip(good.getType())) {
                continue;
            }
            if (Goodtype.EquipmentType(good.getType()) != -1
                    && AccessSuitMsgUntil.getExtra(good.getValue(), "套装属性") != null) {
                continue;
            }
            ys++;
            if (ys >= 0) {
//                g.drawImage(imgpathWdfile(good.getSkin(),60,60).getImage(), x + ys % 6 * 51, y + ys / 6 * 51 + 2, 45, 45, null);
                if (ys >= 29) {
                    return;
                }
            }
        }
    }

    public static Goodstable getCBG(int ys, int p) {
        p += ys * 30;
        ys = -ys * 30 - 1;
        for (int i = 0; i < Goodslist.length; i++) {
            Goodstable good = Goodslist[i];
            if (good == null || !EquipTool.isEquip(good.getType())) {
                continue;
            }
            if (isJY(good)) {
                continue;
            }
            ys++;
            if (p == ys) {
                return good;
            }
        }
        return null;
    }

    public static Goodstable czGBG(BigDecimal rgid) {
        for (int i = 0; i < Goodslist.length; i++) {
            Goodstable good = Goodslist[i];
            if (good == null || !EquipTool.isEquip(good.getType())) {
                continue;
            }
            if (isJY(good)) {
                continue;
            }
            if (good.getRgid().compareTo(rgid) == 0) {
                return good;
            }
        }
        return null;
    }

    // 绘制召唤兽加锁状态
    public static void drawPetLock(Graphics g, RoleSummoning roleSummoning) {
        g.drawImage(lockimg.getImage(), 168, 243, 27, 33, null);
    }

    public static int Pagenumber = 0;// 页数 0表示第一页
//    public static int Fixednumber = 0;// 页数 0表示第一页
    // 已装备的符石
    public static Map<BigDecimal, Goodstable> fushis = new HashMap<BigDecimal, Goodstable>();

    /**
     * 根据表id找到goodstable
     *
     * @return
     */
    public static Goodstable czgood(BigDecimal rgid) {
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null && rgid.compareTo(Goodslist[i].getRgid()) == 0) {
                return Goodslist[i];
            }
        }
        return null;
    }

    /**
     * 符石卸下 格子不够返回false
     */
    public static boolean Unloadfushi(BigDecimal id) {
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                Goodslist[i] = fushis.get(id);
                fushis.remove(id);
                PageNumberChange(Pagenumber);
                return true;
            }
        }
        return false;
    }
    public static int vacancy(int pagenumber) {
        for (int i = pagenumber * 24; i < (pagenumber + 1) * 24; i++) {
            if (Goodslist[i] == null) {
                return i;
            }
        }
        return 0;
    }

    /**通过皮肤获取物品图片*/
    public static ImageIcon imgpath(String skin) {
//        return NpkImageReader.getNpkPng(skin, 60, 60, "item.npk");
        return null;
    }
    public static ImageIcon imgpath2(String skin) {
//        return NpkImageReader.getNpkPng(skin, 43, 43, "item.npk");
        return null;
    }
    public static ImageIcon imgpathAdaptive(String skin, int width, int height) {
//        return NpkImageReader.getNpkPng(skin, width, height, "item.npk");
        return null;
    }

    public static ImageIcon imgpathWdfile(String skin,int w,int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "item.npk");
//        return null;
    }

    public static ImageIcon xbaoWdfile(String skin,int w,int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "item.npk");
//        return null;
    }

    /**
     * 切换页数 传入切换到的页数 0为第一页
     */
    public static void PageNumberChange(int number) {
        Pagenumber = number;
        for (int i = Pagenumber * 24; i < (Pagenumber + 1) * 24; i++) {
            if (Goodslist[i] != null) {
                goodimg[i - Pagenumber * 24] = imgpathWdfile(Goodslist[i].getSkin(), 60, 60);
                AssGoodUntil.addddgood(Goodslist);
                AlchemyGoodUntil.addddgood(Goodslist);
            } else {
                goodimg[i - Pagenumber * 24] = null;
            }
        }
    }


    /**
     * 穿戴装备与背包物品切换 传入 装备和传入物品的位置
     * 
     * @return
     * @throws Exception
     */
    public static void MutualChange(int Equipmentid, int good) throws Exception {
        if (Equipmentid != -1 && good != -1) {
            Goodstable goodstable = null;
            if (choseGoodsList[Equipmentid] != null) {
                choseGoodsList[Equipmentid].setStatus(0);
                goodstable = choseGoodsList[Equipmentid];
            }
            Goodslist[good].setStatus(1);
            //穿装备
            TestpackJapnel testpackJapnel= TestpackJframe.getTestpackJframe().getTestpackJapnel();
            testpackJapnel.getChoseGoodsimg()[Equipmentid].setIcon(null);
            GoodsMouslisten.gooduse(goodstable, Goodslist[good], 0);
            choseGoodsList[Equipmentid] = Goodslist[good];
            Goodslist[good] = goodstable;
            // 图片显示切换
            if (Goodslist[good] != null) {
                goodimg[good - Pagenumber * 24] = imgpathWdfile(Goodslist[good].getSkin(),45,45);
            } else {
                //TODO ?
//                System.out.println(good - Pagenumber * 24);
//                goodimg[good + Pagenumber * 24] = null;
            }
        } else if (good == -1) {// 脱装备

            // 判断是身上的装备脱下来
            boolean Full = true;
            for (int i = 0; i < Goodslist.length; i++) {
                if (Goodslist[i] == null) {
                    choseGoodsList[Equipmentid].setStatus(0);
                    GoodsMouslisten.gooduse(choseGoodsList[Equipmentid], 0);
                    Goodslist[i] = choseGoodsList[Equipmentid];
                    if (i >= Pagenumber * 24 && i < (Pagenumber + 1) * 24) {// 判断背包物品位置是否需要显示
                        goodimg[i - Pagenumber * 24] = imgpathWdfile(Goodslist[i].getSkin(),60,60);
                    }

                    ZhuFrame.getZhuJpanel().drawEquip(choseGoodsList[Equipmentid],0);
                    choseGoodsList[Equipmentid] = null;// 装备图片清空
                    Full = false;
                    break;
                }
            }
            if (Full) {// 判断背包是否满了
                ZhuFrame.getZhuJpanel().addPrompt2("背包已满");
                return;
            }
        }
        // 1|3|6|8|11
        RoleProperty.getRoleProperty().equipWearOff();
        Thread.sleep(5);
        TimeLimit.getLimits().changeSkin();
    }
    public static Goodstable Getgood(int Position,int index) {
        switch (index) {
            case 0:
                return Getgood(Position);
            case 1:
                return AssGoodUntil.getGoodslist()[AssGoodUntil.Pagenumber * 24 + Position];
            case 2:
                return AlchemyGoodUntil.getGoodslist()[AlchemyGoodUntil.Pagenumber * 24 + Position];
            case 3:

            case 4:

            default:
                return null;
        }

    }
    /** 获取指定背包物品 */
    public static Goodstable Getgood(int Position) {
        return Goodslist[Pagenumber * 24 + Position];//页码*24格子+选择物品的位置编号
    }

    /** 根据传入的物品集合分类物品信息 */
    public static void Classification(List<Goodstable> goodstables, String packrecord) {

        if (!goodstables.isEmpty())
            if (goodstables.get(0).getRole_id().longValue() != ImageMixDeal.userimg.getRoleShow().getRole_id()
                    .longValue())
                return;
        GoodExpansion(ImageMixDeal.userimg.getRoleShow().getTurnAround(),ImageMixDeal.userimg.getRoleShow().getAttachPack(),ImageMixDeal.userimg.getRoleShow().getFixedPack());
        DDGoodUntil.ddgood.clear();
        fushis.clear();
        wingGoodsList.clear();
        starCardList.clear();
        XpGoodsList.clear();
        Arrays.fill(choseGoodsList, null);

        // 先归类
        for (int i = goodstables.size() - 1; i >= 0; i--) {
            Goodstable goodstable = goodstables.get(i);
            String[] vs = goodstable.getValue().split("\\|");
            for (int k = 0; k < vs.length; k++) {
                if (vs[k].startsWith("传送")) {
                    vs[k] = vs[k].split("=")[1];
                    String[] path = vs[k].split(",");
                    TestsmallmapJframe.getTestsmallmapJframe(0).getTestsmallmapJpanel().getNpcfunction().add(goodstable.getGoodsid()+","+
                            path[0] +","+path[1] + "," + (path[2] )+ "," + path[3]
                    );
                }
            }
            if (goodstable.getType() == 8888) {
                wingGoodsList.add(goodstable);
                if (goodstable.getStatus() == 1) {
                    choseGoodsList[12] = goodstable;
                }
                goodstables.remove(i);
            } else if (goodstable.getType() == 8903) {
                XpGoodsList.add(goodstable);
                goodstables.remove(i);
            } else if (goodstable.getType() == 520 && (goodstable.getStatus() == 4 || goodstable.getStatus() == 1)) {
                if (goodstable.getStatus() == 1) {
                    choseGoodsList[13] = goodstable;
                }
                starCardList.add(goodstable);
                goodstables.remove(i);
            } else if (goodstable.getStatus() == 1) {
                if (goodstable.getType() == 188 ||goodstable.getType() == 690 || goodstable.getType() == 729 || goodstable.getType() == 750
                        || Goodtype.baoshi(goodstable.getType())
                        || (goodstable.getType() >= 54 && goodstable.getType() <= 61)
                        || Goodtype.isSummonEquip(goodstable.getType()) || Goodtype.isPalEquip(goodstable.getType()) || Goodtype.SHS(goodstable.getType())) {
                    System.out.println(goodstable.getGoodsname()+"=="+goodstable.getRgid());
                    // 属于符石的
                    fushis.put(goodstables.get(i).getRgid(), goodstables.get(i));
                } else {
                    // 属于装备的
                    int Equipment = Goodtype.EquipmentType(goodstables.get(i).getType());
                    if (Equipment != -1) {
                        if (Equipment != 10) {
                            choseGoodsList[Equipment] = goodstables.get(i);
                        } else if (choseGoodsList[10] == null) {
                            choseGoodsList[Equipment] = goodstables.get(i);
                        } else {
                            choseGoodsList[11] = goodstables.get(i);
                        }
                        TestpackJapnel testpackJapnel= TestpackJframe.getTestpackJframe().getTestpackJapnel();
                        testpackJapnel.getChoseGoodsimg()[Equipment].setIcon(null);
                    } else {
                        goodstable.setUsetime(0);
                        GoodsMouslisten.gooduse(goodstable, 1);
                    }
                }
                goodstables.remove(i);
            } else if (goodstable.getStatus() == 2) {
                // 存放进典当的背包
                DDGoodUntil.addddgood(goodstables.get(i));
                goodstables.remove(i);
            } else if (goodstable.getUsetime() <= 0) {
                goodstables.remove(i);
            }

        }
        tiankeng(goodstables);
        if (packrecord != null && !packrecord.isEmpty()) {
            try {
                String[] vs = packrecord.split("\\|");
                // 指定类型的目标起始格
                int specifiedIndex = Goodslist.length - 24; // 当前要放指定类型物品的格子
                for (String v : vs) {
                    String[] vvs = v.split("-");
                    int p = Integer.parseInt(vvs[0]);
                    if (p >= Goodslist.length) {
                        continue;
                    }
                    BigDecimal rgid = new BigDecimal(vvs[1]);
                    for (Goodstable good : goodstables) {
                        if (good.getRgid().compareTo(rgid) == 0) {
                            // 判断是否指定类型
                            if (Goodtype.FixedItems(good.getType())) {
                                // 指定类型物品，优先放到目标区间
                                // 找下一个空的目标格子
                                while (specifiedIndex < Goodslist.length && Goodslist[specifiedIndex] != null) {
                                    specifiedIndex++;
                                }
                                if (specifiedIndex < Goodslist.length) {
                                    Goodslist[specifiedIndex] = good; // 放到指定区间
                                }
                            } else {
                                // 其它类型，按原位置p放
                                if (Goodslist[p] == null) {
                                    Goodslist[p] = good;
                                }
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                // TODO: handle exception
                e.printStackTrace();
            }
        }
        weidaima(goodstables);
        // 显示背包和装备图片
        PageNumberChange(Pagenumber);
    }
    /** 填交易摆摊炼妖面板的坑 */
    public static boolean tiankeng(Goodstable good) {
        InternalForm form = FormsManagement.getInternalForm2(16);
        if (form != null) {
            Stall stall = ((BoothBoxJframe) form.getFrame()).getBoothboxjpanel().getStall2();
            if (stall != null && (stall.getState() == StallBean.PREPARE || stall.getState() == StallBean.NO)) {
                Commodity[] commodities = stall.getGoodstables();
                for (int i = 0; i < commodities.length; i++) {
                    Goodstable goodstable = commodities[i] != null ? commodities[i].getGood() : null;
                    if (goodstable != null) {
                        if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                            good.goodxh(goodstable.getUsetime());
                            if (good.getUsetime() <= 0) {
                                return true;
                            }
                            break;
                        }
                    }
                }
            }
        }
        form = FormsManagement.getInternalForm2(14);
        if (form != null) {
            GoodTrans goodTrans = ((TransJframe) form.getFrame()).getTransJpanel().getGoodTrans();
            if (goodTrans != null && goodTrans.getGoods() != null) {
                for (int i = 0; i < goodTrans.getGoods().size(); i++) {
                    Goodstable goodstable = goodTrans.getGoods().get(i);
                    if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                        good.goodxh(goodstable.getUsetime());
                        if (good.getUsetime() <= 0) {
                            return true;
                        }
                        break;
                    }
                }
            }
        }
        return false;
    }

    public static void tiankeng(List<Goodstable> goods) {
        InternalForm form = FormsManagement.getInternalForm2(16);
        if (form != null) {
            Stall stall = ((BoothBoxJframe) form.getFrame()).getBoothboxjpanel().getStall2();
            if (stall != null && (stall.getState() == StallBean.PREPARE || stall.getState() == StallBean.NO)) {
                Commodity[] commodities = stall.getGoodstables();
                for (int i = 0; i < commodities.length; i++) {
                    Goodstable goodstable = commodities[i] != null ? commodities[i].getGood() : null;
                    if (goodstable != null) {
                        for (int j = goods.size() - 1; j >= 0; j--) {
                            Goodstable good = goods.get(j);
                            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                                good.goodxh(goodstable.getUsetime());
                                if (good.getUsetime() <= 0) {
                                    goods.remove(j);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
        form = FormsManagement.getInternalForm2(14);
        if (form != null) {
            GoodTrans goodTrans = ((TransJframe) form.getFrame()).getTransJpanel().getGoodTrans();
            if (goodTrans != null && goodTrans.getGoods() != null) {
                for (int i = 0; i < goodTrans.getGoods().size(); i++) {
                    Goodstable goodstable = goodTrans.getGoods().get(i);
                    for (int j = goods.size() - 1; j >= 0; j--) {
                        Goodstable good = goods.get(j);
                        if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                            good.goodxh(goodstable.getUsetime());
                            if (good.getUsetime() <= 0) {
                                goods.remove(j);
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    /** 伪代码 背包不自动整理 */
    public static void weidaima(List<Goodstable> goods) {
        List<Integer> you = new ArrayList<>();
        List<Integer> no = new ArrayList<>();
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null)
                no.add(i);
            else
                you.add(i);
        }
        for (int i = goods.size() - 1; i >= 0; i--) {
            Goodstable good = goods.get(i);
            for (int j = you.size() - 1; j >= 0; j--) {
                Goodstable good2 = Goodslist[you.get(j)];
                if (good2.getRgid().longValue() == good.getRgid().longValue()) {
                    Goodslist[you.get(j)] = good;
                    goods.remove(i);
                    you.remove(j);
                    break;
                }
            }
        }
        for (int j = you.size() - 1; j >= 0; j--) {
            no.add(you.get(j));
            Goodslist[you.get(j)] = null;
        }
        int size1 = no.size();
        int size2 = goods.size();
        for (int i = 0; i < size1 && i < size2; i++) {
            Goodslist[no.get(i)] = goods.get(i);
        }
    }

    /**
     * 判断是否发送背包数据
     */
    public static void isSendPackRecord() {
        String sendmes = getPackRecord();
        PackRecord packRecord = RoleData.getRoleData().getPackRecord();
        if (packRecord.getRecord() == null || !packRecord.getRecord().equals(sendmes)) {
            packRecord.setRecord(sendmes);
            sendPackRecord(0, sendmes);
        }
    }

    /** 发送背包记忆数据 0背包数据 1召唤兽支援数据 2灵宝支援数据 3修改收录最大数量 7召唤兽携带数量*/
    public static void sendPackRecord(int type, String msg) {
        String sendmes = Agreement.getAgreement().packRecordAgreement(type + msg);
        SendMessageUntil.toServer(sendmes);
    }

    /** 生成背包记忆格式 */
    public static String getPackRecord() {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (buffer.length() != 0) {
                    buffer.append("|");
                }
                buffer.append(i);
                buffer.append("-");
                buffer.append(Goodslist[i].getRgid());
            }
        }
        return buffer.toString();
    }
    public static int is = 0;
    /** 转生背包扩充 */
    public static void GoodExpansion(int TurnAround, int attachPack,int Fixed) {
        if (TurnAround >= 4) {
            TurnAround = 3;
        }
        int Total = (1+attachPack+TurnAround+1) * 24;
        int Totals = (1 + TurnAround + attachPack) * 24;
        if (Goodslist != null) {
            if (Total != Goodslist.length) {
                Goodstable[] Goodslists = new Goodstable[Total];
                for (int i = 0; i < Goodslist.length; i++) {
                    Goodslists[i] = Goodslist[i];
                }
                Goodslist = Goodslists;
            }
        } else {
            Goodslist = new Goodstable[Total];
        }
        int keyong = Total / 24;
        int keyongs = Totals / 24;
        is = keyong;
        Btnsm();//根据包裹数量来设定显示图像
        // 按钮设置
        btntype(TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights(), keyong);
        btntypes(PetEquipmentJframe.getPetEquipmentJframe().getEquipmentJpanel().getBtnrights(),keyongs);
        btntypes(AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getBtnrights(),keyongs);
        //初始化默认点击0
        SwitchInterfaceClass();
    }

    public static void SwitchInterfaceClass(){
        TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights()[Pagenumber].dianji();
        PetEquipmentJframe.getPetEquipmentJframe().getEquipmentJpanel().getBtnrights()[Pagenumber].dianji();
        AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getBtnrights()[Pagenumber].dianji();
    }


    public static void btntypes(MoBanBtn[] btnrights, int path) {
        for (int i = 0; i < 5; i++) {
            if (btnrights[i] instanceof additionalgoodbtn){
                btnrights[i].btntypechange(i >= path?0:2);
            }else if (btnrights[i] instanceof Alchemygoodbtn){
                btnrights[i].btntypechange(i >= path?0:2);
            }

        }
    }



    public static void Btnsm() {

        for (int i = 0; i < is - 1; i++) {
            ImageIcon[] BackItem = Util.SwitchUI==1 ? Juitil.bt10:Juitil.bt11;
            TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights()[i].
                    setIcons(BackItem);
        }
        // 单独设置最后一个按钮的图标
        if (is > 0) {
            ImageIcon[] BackItem = Util.SwitchUI==1 ? Juitil.bt12: Juitil.bt13;
            TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights()[is - 1].
                    setIcons(BackItem);
        }
    }

    // /**重置可用按钮类型*/
    public static void btntype(goodbtn[] btnrights, int path) {
        for (int i = 0; i < is; i++) {
            btnrights[i].btntypechange(i >= path?0:2);
        }
    }

    /** 新添加的物品 */
    public static boolean newgood(Goodstable goodstables) {
        for (int j = 0; j < Goodslist.length; j++) {
            if (Goodslist[j] == null) {
                goodstables.setStatus(0);
                Goodslist[j] = goodstables;
                PageNumberChange(Pagenumber);
                return true;
            }
        }
        return false;
    }

    /** 批量查找满足条件的物品 放回lsit */
    public static List<Integer> chaxuns(int[] goodids) {
        List<Integer> weizhi = new ArrayList<>();
        ss: for (int i = 0; i < goodids.length; i++) {
            int id = goodids[i];
            int sum = 0;
            for (int j = 0; j < goodids.length; j++) {
                if (id == goodids[j]) {
                    sum++;
                }
            }
            for (int j = 0; j < Goodslist.length; j++) {
                if (Goodslist[j] != null) {
                    if (Goodslist[j].getGoodsid().intValue() == id) {
                        if (Goodslist[j].getUsetime() >= sum) {
                            weizhi.add(j);
                            continue ss;
                        }
                    }
                }
            }
        }
        // 判断是否所有物品都查询到 没查询到返回null
        return weizhi.size() == goodids.length ? weizhi : null;
    }

    public static int chaxuns(int goodid) {
        for (int j = 0; j < Goodslist.length; j++) {
            if (Goodslist[j] != null) {
                if (Goodslist[j].getGoodsid().intValue() == goodid) {
                    return j;
                }
            }
        }
        return -1;
    }

    /** 查找满足类型的全部物品返回物品 */
    public static List<Goodstable> chaxunss(long type) {
        List<Goodstable> weizhi = new ArrayList<>();
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null && Goodslist[i].getType() == type) {
                weizhi.add(Goodslist[i]);
            }
        }
        return weizhi;
    }
    /** 删除指定位置的物品 放回表id集合 */
    public static void Deleted(int goodid) {
        Goodslist[goodid] = null;
        PageNumberChange(Pagenumber);
    }

    /** 批量道具使用一次 放回表id集合 */
    public static void Delete(List<Integer> goodids) {
        GoodsMouslisten.goodarr.setI(1);
        List<Goodstable> list = GoodsMouslisten.goodarr.getList();
        list.clear();
        for (int i = 0; i < goodids.size(); i++) {
            Goodslist[goodids.get(i)].goodxh(1);
            if (!list.contains(Goodslist[goodids.get(i)])) {
                list.add(Goodslist[goodids.get(i)]);
            }
        }
        String sendmes = Agreement.getAgreement().packchangeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(GoodsMouslisten.goodarr));
        // 将装备物品返还给客户端// 向服务器发送信息
        SendMessageUntil.toServer(sendmes);
        for (int i = 0; i < goodids.size(); i++) {
            if (Goodslist[goodids.get(i)] != null) {
                if (Goodslist[goodids.get(i)].getUsetime() <= 0) {
                    Goodslist[goodids.get(i)] = null;
                }
            }
        }
        PageNumberChange(Pagenumber);
    }

    /** 批量道具使用一次 放回表id集合 */
    public static List<BigDecimal> Delete2(List<Integer> goodids) {
        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
        List<BigDecimal> list = new ArrayList<>();
        for (int i = 0; i < goodids.size(); i++) {
            Goodstable good = Goodslist[goodids.get(i)];
            if (good != null) {
                good.goodxh(1);
                if (synthesisJPanl!=null){
                    synthesisJPanl.getLaItmeNum()[i].setText(good.getUsetime()+"");
                }
                list.add(good.getRgid());
                if (good.getUsetime() <= 0) {
                    Goodslist[goodids.get(i)] = null;
                }
            }

        }
        PageNumberChange(Pagenumber);
        return list;
    }

    public static List<BigDecimal> Delete(int goodids) {
        List<BigDecimal> biaoids = new ArrayList<>();
        if (Goodslist[goodids] == null) {
            return null;
        }
        biaoids.add(Goodslist[goodids].getRgid());
        Goodslist[goodids] = null;
        PageNumberChange(Pagenumber);
        return biaoids;
    }

    /** 根据表id来删除物品 */
    public static void Deletebiaoid(BigDecimal biaoid) {
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (Goodslist[i].getRgid().intValue() == biaoid.intValue()) {
                    if (Goodslist[i].getUsetime() <= 0 || Goodslist[i].getStatus() != 0) {
                        Goodslist[i] = null;
                        PageNumberChange(Pagenumber);
                        return;
                    }
                }
            }
        }
    }

    /** 表id使用一次 */
    public static Goodstable Uerbiaoid(BigDecimal biaoid) {

        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                Goodstable goodstable = Goodslist[i];
                if (goodstable.getRgid().intValue() == biaoid.intValue()) {
                    goodstable.goodxh(1);
                    if (goodstable.getUsetime() <= 0) {
                        Goodslist[i] = null;
                        PageNumberChange(Pagenumber);
                    }
                    return goodstable;
                }
            }
        }
        return null;
    }

    // 获取表id
    public static Goodstable getRgid(BigDecimal rgid) {
        Goodstable good = fushis.get(rgid);
        if (good != null) {
            return good;
        }
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null) {
                if (Goodslist[i].getRgid().compareTo(rgid) == 0) {
                    return Goodslist[i];
                }
            }
        }
        for (int i = 0; i < choseGoodsList.length; i++) {
            if (choseGoodsList[i] != null) {
                if (choseGoodsList[i].getRgid().compareTo(rgid) == 0) {
                    return choseGoodsList[i];
                }
            }
        }
        for (int i = wingGoodsList.size() - 1; i >= 0; i--) {
            good = wingGoodsList.get(i);
            if (good.getRgid().compareTo(rgid) == 0) {
                return good;
            }
        }
        for (int i = XpGoodsList.size() - 1; i >= 0; i--) {
            good = XpGoodsList.get(i);
            if (good.getRgid().compareTo(rgid) == 0) {
                return good;
            }
        }
        for (int i = starCardList.size() - 1; i >= 0; i--) {
            good = starCardList.get(i);
            if (good.getRgid().compareTo(rgid) == 0) {
                return good;
            }
        }
        return null;
    }

    /** 查询id 和数量判断是否足够 */
    public static boolean isgood(int goodid, int sum) {
        int sysum = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null)
                continue;
            if (Goodslist[i].getGoodsid().intValue() != goodid)
                continue;
            sysum += Goodslist[i].getUsetime();
            if (sysum >= sum)
                return true;
        }
        return false;
    }

    /** 使用id 和数量 */
    public static boolean consume(int goodid, int sum) {
        int sysum = sum;
        for (int i = 0; i < Goodslist.length; i++) {
            if (sum <= 0)
                break;
            if (Goodslist[i] == null)
                continue;
            if (Goodslist[i].getGoodsid().intValue() != goodid)
                continue;
            Goodstable goodstable = Goodslist[i];
            if (goodstable.getUsetime() >= sysum) {
                sum = 0;
                goodstable.setUsetime(goodstable.getUsetime() - sysum);
            } else {
                sum -= goodstable.getUsetime();
                goodstable.setUsetime(0);
            }
            if (goodstable.getUsetime() <= 0) {
                Goodslist[i] = null;
                GoodsMouslisten.gooduse(goodstable, 1);
            }
            if (sum <= 0)
                break;
        }
        PageNumberChange(Pagenumber);
        return false;
    }

    /** 2个物品格子的替换 */
    public static void tihuan(int one, int two) {
        Goodstable goodstable = Goodslist[one];
        Goodslist[one] = Goodslist[two];
        Goodslist[two] = goodstable;
        PageNumberChange(Pagenumber);
//        ZhuFrame.getZhuJpanel().drawEquip(goodstable);
    }

    /** 根据类型和 数量判断格子是否足够 */
    public static int Surplussum(String type, String id, int sum) {
        int size = 0;
        // 先判断物品是否可以重叠
        if (!EquipTool.isEquip(Long.parseLong(type))) {
            // 可重叠
            for (int i = 0; i < Goodslist.length-24; i++) {
                if (Goodslist[i] == null) {
                    return sum;
                } else if (id.equals(Goodslist[i].getGoodsid() + "")) {
                    return sum;
                }
            }
        } else {
            // 不可重叠
            for (int i = 0; i < Goodslist.length - 24; i++) {
                if (Goodslist[i] == null) {
                    size++;
                    if (size >= sum) {
                        return sum;
                    }
                }
            }
        }
        return size;
    }

    /** 判断格子数是否够（在购买物品的时候使用） */
    public static boolean sureCanBuyOrNo(String id) {
        boolean sureCanbuOrNo = false;
        // 判断背包里面有没有这个物品或者是否有多余的空格来装东西
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null)
                sureCanbuOrNo = true;
            else if (id.equals(Goodslist[i].getGoodsid() + "")) {
                if (Surplussum(Goodslist[i].getType() + "", id, 1) > 0)
                    sureCanbuOrNo = true;
            }
        }
        // 如果没有，判断格子数目是否为空
        return sureCanbuOrNo;
    }

    /** 修改物品数量 */
    public static void addGood(BigDecimal id, int sum) {
        Goodstable good = fushis.get(id);
        if (good != null) {
            good.setUsetime(sum);
            if (sum == 0) {
                fushis.remove(good);
            }
        }
        for (int i = Goodslist.length - 1; i >= 0; i--) {
            if (Goodslist[i] == null) {
                continue;
            }
            good = Goodslist[i];
            if (good.getRgid().compareTo(id) == 0) {
                if (good.getType()==8899){
                    //刷新消耗的重置丹
                    ResetSoulSkillJPanl resetSoulSkillJPanl = ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl();
                    resetSoulSkillJPanl.goodsSum.setText(good.getUsetime()-1+"/1");
                }
                good.setUsetime(sum);
                if (tiankeng(good)) {
                    Goodslist[i] = null;
                    PageNumberChange(Pagenumber);
                } else if (good.getUsetime() <= 0) {
                    Goodslist[i] = null;
                    PageNumberChange(Pagenumber);
                }
                ZFShow(good);
                return;
            }
        }
        for (int i = wingGoodsList.size() - 1; i >= 0; i--) {
            good = wingGoodsList.get(i);
            if (good.getRgid().compareTo(id) == 0) {
                good.setUsetime(sum);
                if (good.getUsetime() <= 0) {
                    wingGoodsList.remove(i);
                }
                return;
            }
        }
        for (int i = XpGoodsList.size() - 1; i >= 0; i--) {
            good = XpGoodsList.get(i);
            if (good.getRgid().compareTo(id) == 0) {
                good.setUsetime(sum);
                if (good.getUsetime() <= 0) {
                    XpGoodsList.remove(i);
                }
                return;
            }
        }
        for (int i = starCardList.size() - 1; i >= 0; i--) {
            good = starCardList.get(i);
            if (good.getRgid().compareTo(id) == 0) {
                good.setUsetime(sum);
                if (good.getUsetime() <= 0) {
                    starCardList.remove(i);
                }
                return;
            }
        }
    }

    /** 新添加的物品 不为空添加成功 空添加失败 */
    public static void addGood(Goodstable goodstable) {
        if (goodstable.getType() == -1) {
            RoleData.getRoleData().addTx(-goodstable.getGoodsid().longValue() + "");
            return;
        }
        if (goodstable.getType() == 8888) {
            addWingGood(goodstable);
            return;
        }
        if (goodstable.getType() == 8903) {
            addXpGood(goodstable);
            return;
        }
        if (goodstable.getType() == 520 && (goodstable.getStatus() == 4 || goodstable.getStatus() == 1)) {
            addStarCard(goodstable);
            return;
        }
        if (tiankeng(goodstable)) {
            return;
        }

        if (goodstable.getType()==8898){
            ResetSkillControl.ReserGoodsv(goodstable);
        }
        //守护石
        if (Goodtype.SHS(goodstable.getType())){
            AsoulJPanel asoulJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getAsoulJPanel();
            asoulJPanel.setNewProperty(false);
            asoulJPanel.setNewitemattributes(goodstable);
        }

        fushis.remove(goodstable.getRgid());
        if (goodstable.getStatus() == 1) {
            if (goodstable.getUsetime() > 0) {
                if (goodstable.getType() == 188 || goodstable.getType() == 729 || goodstable.getType() == 750
                        || Goodtype.baoshi(goodstable.getType())
                        || (goodstable.getType() >= 54 && goodstable.getType() <= 61)
                        || Goodtype.isSummonEquip(goodstable.getType()) || Goodtype.isPalEquip(goodstable.getType())) {
                    fushis.put(goodstable.getRgid(), goodstable);
                }
            }
            for (int i = Goodslist.length - 1; i >= 0; i--) {
                if (Goodslist[i] != null && Goodslist[i].getRgid().compareTo(goodstable.getRgid()) == 0) {
                    Goodslist[i] = null;
                    PageNumberChange(Pagenumber);
                    break;
                }
            }
            for (int i = choseGoodsList.length - 1; i >= 0; i--) {
            	if (choseGoodsList[i] != null && choseGoodsList[i].getRgid().compareTo(goodstable.getRgid()) == 0) {
            		choseGoodsList[i] = goodstable;
                    break;
                }
            }
            return;
        }
        int ky = -1;
        for (int i = Goodslist.length - 1; i >= 0; i--) {
            if (Goodslist[i] == null) {
                if (Goodtype.FixedItems(goodstable.getType())){
                    int targetPage = is - 1;
                    int targetIndex = targetPage*24;
                    while (targetIndex < Goodslist.length && Goodslist[targetIndex] != null) {
                        targetIndex++;
                    }
                    ky = targetIndex;
                }else {
                    ky = i;
                }
                continue;
            }
            Goodstable good = Goodslist[i];
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                ky = i;
                break;
            }
        }
        if (ky != -1) {
            String skin = Goodslist[ky] != null ? Goodslist[ky].getSkin() : null;
            if (goodstable.getUsetime() > 0) {
                Goodslist[ky] = goodstable;
            } else {
                Goodslist[ky] = null;
            }
            if (Goodslist[ky] == null || skin == null || !skin.equals(goodstable.getSkin())) {
                PageNumberChange(Pagenumber);
            }
            ZFShow(goodstable);
        }
    }

    /** 判断是否刷新作坊界面 */
    public static void ZFShow(Goodstable good) {
        InternalForm form = FormsManagement.getInternalForm2(61);
        if (form == null || !form.getFrame().isVisible()) {
            return;
        }
        WorkshopRefiningCardJpanel cardJpanel = ((WorkshopRefiningJframe) form.getFrame()).getWorkshopRefiningJpanel()
                .getCardJpanel();
        GemRefineryMainJpanel gemRefineryMainJpanel = cardJpanel.getGemRefineryMainJpanel();
        if(gemRefineryMainJpanel.isVisible()){
            if(gemRefineryMainJpanel.getChooseGoodstable() != null &&gemRefineryMainJpanel.getChooseGoodstable().getRgid().compareTo(good.getRgid()) == 0){
                gemRefineryMainJpanel.refreshChooseGoodstable(good,1);
            }else {
                gemRefineryMainJpanel.refreshChooseGoodstable(good,2);
            }
        }else{
            cardJpanel.getEquiJpanel().ResetGood(good);
            cardJpanel.getRefinersJpanel().ResetGood(good);
        }
    }

    /** 翅膀装备修改 */
    public static void addWingGood(Goodstable goodstable) {
        for (int i = wingGoodsList.size() - 1; i >= 0; i--) {
            Goodstable good = wingGoodsList.get(i);
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                if (goodstable.getUsetime() <= 0) {
                    wingGoodsList.remove(i);
                } else {
                    wingGoodsList.set(i, goodstable);
                    if (goodstable.getStatus() == 1) {
                        choseGoodsList[12] = goodstable;
                        RoleProperty.getRoleProperty().equipWearOff();
                    }
                    if (FormsManagement.getframe(86).isVisible()) {
                        // 刷新翅膀界面
                        WingMainPanel wingMainPanel = WingMainFrame.getWingMainFrame().getWingMainPanel();
                        if (wingMainPanel.getWingGoods() != null
                                && wingMainPanel.getWingGoods().compareTo(goodstable.getRgid()) == 0)
                            wingMainPanel.changeChooseWingGoods(goodstable, wingMainPanel.getWingGoodsType());
                    }
                }
                return;
            }
        }
        wingGoodsList.add(goodstable);
    }
    /** 星盘装备修改 */
    public static void addXpGood(Goodstable goodstable) {
        for (int i = XpGoodsList.size() - 1; i >= 0; i--) {
            Goodstable good = XpGoodsList.get(i);
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                if (goodstable.getUsetime() <= 0) {
                    XpGoodsList.remove(i);
                } else {
                    XpGoodsList.set(i, goodstable);
                    if (FormsManagement.getframe(137).isVisible()) {
                        RStarSoulsJPanel rStarSoulsJPanel = RStarSoulsFrame.getRStarSoulsFrame().getrStarSoulsJPanel();
                        rStarSoulsJPanel.setXhGoods(goodstable.getRgid());
                    }
                    ZhuFrame.getZhuJpanel().drawEquip(goodstable,0);
                }
                return;
            }
        }
        XpGoodsList.add(goodstable);
        if (FormsManagement.getframe(137).isVisible()) {
            RStarSoulsJPanel rStarSoulsJPanel = RStarSoulsFrame.getRStarSoulsFrame().getrStarSoulsJPanel();
            rStarSoulsJPanel.setXhGoods(goodstable.getRgid());
        }
        ZhuFrame.getZhuJpanel().drawEquip(goodstable,0);
    }

    /** 星卡修改 */
    public static void addStarCard(Goodstable goodstable) {
        if (goodstable.getStatus() == 1) {
            choseGoodsList[13] = goodstable;
        }
        for (int i = starCardList.size() - 1; i >= 0; i--) {
            Goodstable good = starCardList.get(i);
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                if (goodstable.getUsetime() <= 0) {
                    starCardList.remove(i);
                } else {
                    starCardList.set(i, goodstable);
                }
                if (good.getStatus() == 1 && goodstable.getStatus() == 4) {
                    choseGoodsList[13] = null;
                    RoleProperty.getRoleProperty().equipWearOff();
                }
                JframeStarCardMain jframeStarCardMain = JframeStarCardMain.getJframeSummonEquipMain();
                if (jframeStarCardMain != null) {
                    JpanelStarCardMain jpanelStarCardMain = jframeStarCardMain.getJpanelStarCardMain();
                    if (jpanelStarCardMain.getChooseStarCardId() != null
                            && goodstable.getRgid().compareTo(jpanelStarCardMain.getChooseStarCardId()) == 0) {
                        if (jpanelStarCardMain.getBigType() == 2) {
                            if (jpanelStarCardMain.getSmallType() == 1 || jpanelStarCardMain.getSmallType() == 4
                                    || jpanelStarCardMain.getSmallType() == 2) {
                                jpanelStarCardMain.viewChange(goodstable.getUsetime() <= 0 ? null : goodstable);
                            }
                        } else if (jpanelStarCardMain.getBigType() == 1) {
                            jpanelStarCardMain.attributeImgShow(goodstable.getUsetime() <= 0 ? null : goodstable);
                        }
                    }
                }
                JframeStarTransferMain jframeStarTransferMain = JframeStarTransferMain.getShowJframeStarTransferMain();
                if (jframeStarTransferMain != null) {
                    JpanelStarTransferMain jpanelStarTransferMain = jframeStarTransferMain.getJpanelStarTransferMain();
                    if (jpanelStarTransferMain.getChooseOneId() != null
                            && jpanelStarTransferMain.getChooseOneId().compareTo(goodstable.getRgid()) == 0) {
                        String[] values = goodstable.getValue().split("\\|");
                        String[] split2 = values[3].split("&");
                        for (int j = 0; j < split2.length; j++) {
                            if (split2[j].startsWith("星阵属性")) {
                                String[] split = split2[j].split("=");
                                if (BtnStarCard.isfiveElements(split[1])) {
                                    jpanelStarTransferMain.showStarCardAttribute(0, split,
                                            goodstable.getUsetime() <= 0 ? null : goodstable);
                                    jpanelStarTransferMain.showStarCardAttribute(1, null, null);
                                }
                            }
                        }
                    }
                }
                return;
            }
        }
        starCardList.add(goodstable);
    }

    /**
     * 装备替换 1表示在装备 2表示预装备
     * 
     * @return
     */
    public static boolean ChangeParts(Goodstable good1, Goodstable good2) {
        // 获取good2的位置
        int p = -1;
        if (good2 != null) {
            for (int i = 0; i < Goodslist.length; i++) {
                if (Goodslist[i] == null)
                    continue;
                if (Goodslist[i].getRgid().compareTo(good2.getRgid()) != 0)
                    continue;
                Goodslist[i] = null;
                p = i;
                break;
            }
            good2.setStatus(1);
            GoodsMouslisten.gooduse(good2, 0);
        } else {
            for (int i = 0; i < Goodslist.length; i++) {
                if (Goodslist[i] != null)
                    continue;
                p = i;
                break;
            }
        }
        if (good1 != null) {
            if (p != -1) {
                Goodslist[p] = good1;
                good1.setStatus(0);
                GoodsMouslisten.gooduse(good1, 0);
            } else {
                return false;
            }
        }
        p -= Pagenumber * 24;
        if (p >= 0 && p < 24)
            PageNumberChange(Pagenumber);
        return true;

    }

    /** 封装的物品检测 true表示不在背包 */
    public static boolean isExist(Goodstable good) {
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (good == Goodslist[i]) {
                if (Goodslist[i].getUsetime() <= 0) {
                    Goodslist[i] = null;
                    PageNumberChange(Pagenumber);
                    return true;
                }
                return false;
            }
        }
        ZhuFrame.getZhuJpanel().addPrompt2("物品已经不在背包了");
        return true;
    }

    /** 封装的物品检测 true表示不在装备栏 */
    public static boolean ischoseExist(Goodstable good) {
        for (int i = 0; i < choseGoodsList.length; i++) {
            if (choseGoodsList[i] == null) {
                continue;
            }
            if (good == choseGoodsList[i]) {
                return false;
            }
        }
        Goodstable goodstable = fushis.get(good.getRgid());
        if (goodstable != null && goodstable == good) {
            return false;
        }
        ZhuFrame.getZhuJpanel().addPrompt2("物品已经不在背包了");
        return true;
    }

    // 将摆摊的物品扣除
    public static void stall1(Goodstable goodstable) {
        for (int i = Goodslist.length - 1; i >= 0; i--) {
            if (Goodslist[i] == null) {
                continue;
            }
            Goodstable good = Goodslist[i];
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                Goodslist[i].setUsetime(Goodslist[i].getUsetime() - goodstable.getUsetime());
                if (Goodslist[i].getUsetime() <= 0) {
                    Goodslist[i] = null;
                    PageNumberChange(Pagenumber);
                }
                return;
            }
        }
    }

    // 将摆摊的物品还回去
    public static void stall2(Goodstable goodstable) {
        int ky = -1;
        for (int i = Goodslist.length - 1; i >= 0; i--) {
            if (Goodslist[i] == null) {
                ky = i;
                continue;
            }
            Goodstable good = Goodslist[i];
            if (good.getRgid().compareTo(goodstable.getRgid()) == 0) {
                Goodslist[i].setUsetime(Goodslist[i].getUsetime() + goodstable.getUsetime());
                return;
            }
        }
        if (ky != -1) {
            Goodslist[ky] = goodstable;
            PageNumberChange(Pagenumber);

        }
    }

    /** 禁止交易的初步筛选 true禁交易 */
    public static boolean isJY(Goodstable good) {
        if (good.getGoodlock() == 1 || AnalysisString.jiaoyi(good.getQuality())) {return true;}
        if (Goodtype.EquipmentType(good.getType()) != -1) {
            if (AccessSuitMsgUntil.getExtra(good.getValue(), BaptizeBtn.Extras[3]) != null) {return true;}
            if (AccessSuitMsgUntil.getExtra(good.getValue(), BaptizeBtn.Extras[4]) != null) {return true;}
        }
        return false;
    }

    /** 获取背包物品id数量 */
    public static int getGoodNum(BigDecimal sid) {
        int size = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null && Goodslist[i].getGoodsid().compareTo(sid) == 0) {
                size += Goodslist[i].getUsetime();
            }
        }
        return size;
    }
    /** 根据提供的物品名称来查找对应的物品*/
    public static Goodstable getGoodsName(String goodsName){
        Goodstable goodstable = null;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null ) {
                switch (goodsName){
                    case "普通魔晶":
                        if (Goodslist[i].getGoodsname().equals("普通重置丹")){
                            goodstable = Goodslist[i];
                        }
                    case "百年魔晶":
                        if (Goodslist[i].getGoodsname().equals("百年重置丹")){
                            goodstable = Goodslist[i];
                        }
                    case "千年魔晶":
                        if (Goodslist[i].getGoodsname().equals("千年重置丹")){
                            goodstable = Goodslist[i];
                        }
                    case "仙灵魔晶":
                        if (Goodslist[i].getGoodsname().equals("仙灵重置丹")){
                            goodstable = Goodslist[i];
                        }
                }

            }
        }
        return goodstable;
    }
    /** 获取背包物品 */
    public static Goodstable getGoods(BigDecimal sid) {
        Goodstable goodstable = null;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null && Goodslist[i].getGoodsid().compareTo(sid) == 0) {
                goodstable = Goodslist[i];
            }
        }
        return goodstable;
    }

    /** 获取背包魂技物品 */
    public static Goodstable SoulGoods(int type) {
        Goodstable goodstable = null;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] != null && Goodslist[i].getType() == type) {
                goodstable = Goodslist[i];
            }
        }
        return goodstable;
    }
    /**
     * 根据等级筛选商品列表中特定类型的商品
     *
     * @param lv 商品的等级，用于筛选Goodslist中的商品
     *           该方法遍历Goodslist数组，检查每个元素是否为指定类型（类型ID：188），
     *           并且其值的格式符合预期（即值可以按照 "|" 和 "=" 分割）。
     *           如果找到匹配项，这些项将被收集并返回。
     *
     * @return Goodstable[] 包含所有匹配等级的商品对象数组。
     *         如果没有找到匹配项或Goodslist为空，则返回null。
     *
     * 注意: 当前实现存在缺陷，未收集和返回符合条件的商品，直接返回null。
     */
    public static Goodstable[] Tolvel(int lv) {
        int count = 0;
        List<Goodstable> result = new ArrayList<>(); // 创建一个List来收集符合条件的商品
        for (int i = 0; i < Goodslist.length && count < 5; i++) {
            if (Goodslist[i] != null && Goodslist[i].getType() == 188) { // 检查商品类型是否为188
                String v = Goodslist[i].getValue().split("\\|")[0].split("=")[1];
                // 这里可以添加更多逻辑来判断v是否满足条件，例如与lv比较
                // 假设我们想收集所有等级等于lv的商品
                if (Integer.parseInt(v) == lv) {
                    result.add(Goodslist[i]); // 添加符合条件的商品到结果列表
                    count++;
                }
            }
        }
        return result.toArray(new Goodstable[0]); // 将结果列表转换为数组并返回
    }

    public static Goodstable[] Gemslvel(int lv) {
        int count = 0;
        List<Goodstable> result = new ArrayList<>(); // 创建一个List来收集符合条件的商品
        for (int i = 0; i < Goodslist.length && count < 2; i++) {
            if (Goodslist[i] != null && Goodtype.baoshi(Goodslist[i].getType())) { // 检查商品类型是否为188
                String v = Goodslist[i].getValue().split("\\|")[0].split("=")[1];
                // 这里可以添加更多逻辑来判断v是否满足条件，例如与lv比较
                // 假设我们想收集所有等级等于lv的商品
                if (Integer.parseInt(v) == lv) {
                    result.add(Goodslist[i]); // 添加符合条件的商品到结果列表
                    count++;
                }
            }
        }
        return result.toArray(new Goodstable[0]); // 将结果列表转换为数组并返回
    }


    // 升级界面物品获取
    public static Goodstable getUpgradeGoods(int type, WingMainPanel wingMainPanel) {
        int j = -(wingMainPanel.getPageNumber() - 1) * 15 - 1;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (++j < 0) {
                continue;
            }
            if (type == j) {
                return Goodslist[i];
            }
        }
        return null;
    }

    /** 获取翅膀升星材料 */
    public static Goodstable getWingUpStarGoods(int type) {
        int number = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (!Goodtype.upStarWing(Goodslist[i].getType())) {
                continue;
            }
            if (number == type) {
                return Goodslist[i];
            }
            number++;
        }
        return null;
    }

    // 翅膀升级物品图片展示
    public static void getUpgrade(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        int j = -(wingMainPanel.getPageNumber() - 1) * 15 - 1;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (++j < 0) {
                continue;
            }
            ImageIcon imgpathAdaptive = imgpathAdaptive(Goodslist[i].getSkin(), 49, 49);
            g.drawImage(imgpathAdaptive.getImage(), x + j % 5 * 51, y + j / 5 * 51, 49, 49, null);
            g.setColor(Color.red);
            g.drawString(Goodslist[i].getUsetime().toString(), x + j % 5 * 51 + 4, y + j / 5 * 51 + 10);
            if (j >= 14) {
                return;
            }
        }
    }

    // 获取升星材料
    public static void getGoodsUpStar(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        int j = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (Goodtype.upStarWing(Goodslist[i].getType())) {
                g.drawImage(imgpathAdaptive(Goodslist[i].getSkin(), 49, 49).getImage(), x + j * 51, y, 49, 49, null);
                g.setColor(Color.red);
                g.drawString(Goodslist[i].getUsetime().toString(), x + j * 51 + 4, y + 10);
                j++;
                if (j > 4) {
                    return;
                }
            }
        }
    }

    // 翅膀品质面板的培养物品获取
    public static void getQualityGoods(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(8890));
        if (goodstable == null) {
            // return;
        } else {
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 58, 56).getImage(), x, y, 58, 56, null);
        }
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (Goodslist[i].getType() == 8890) {
                wingMainPanel.setChosegoods(Goodslist[i]);
                // g.drawImage(imgpathAdaptive(wingMainPanel.getChosegoods().getSkin(),
                // 58, 56).getImage(), x, y, 58, 56, null);
                g.setColor(Color.red);
                g.drawString(Goodslist[i].getUsetime().toString(), x + 2, y + 12);
                return;
            }
        }
        g.setColor(Color.red);
        g.drawString("0", x + 2, y + 12);
    }

    // 获取翅膀重铸界面的重铸物品
    public static void getRecastGoods(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(8892));
        if (goodstable == null) {
            // return;
        } else {
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 49, 49).getImage(), x, y, 49, 49, null);
        }
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (Goodslist[i].getType() == 8892) {
                wingMainPanel.setChosegoods(Goodslist[i]);
                // g.drawImage(imgpathAdaptive(wingMainPanel.getChosegoods().getSkin(),
                // 58, 56).getImage(), x, y, 58, 56, null);
                g.setColor(Color.red);
                g.drawString(Goodslist[i].getUsetime().toString(), x + 2, y + 12);
                return;
            }
        }
        g.setColor(Color.red);
        g.drawString("0", x + 2, y + 12);
    }

    // 获取翅膀炼化界面的炼化物品
    public static void getRefiningGoods(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(8893));
        if (goodstable == null) {
            // return;
        } else {
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 58, 56).getImage(), x, y, 58, 56, null);
        }
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (Goodslist[i].getType() == 8893) {
                wingMainPanel.setChosegoods(Goodslist[i]);
                // g.drawImage(imgpathAdaptive(wingMainPanel.getChosegoods().getSkin(),
                // 58, 56).getImage(), x, y, 58, 56, null);
                g.setColor(Color.red);
                g.drawString(Goodslist[i].getUsetime().toString(), x + 2, y + 12);
                return;
            }
        }
        g.setColor(Color.red);
        g.drawString("0", x + 2, y + 12);
    }

    // 翅膀图片展示
    public static void drawWingImg(Graphics g, WingMainPanel wingMainPanel, int x, int y) {
        for (int i = 25 * (wingMainPanel.getPageNumber() - 1); i < wingGoodsList.size(); i++) {
            ImageIcon imgpathAdaptive = imgpathAdaptive(wingGoodsList.get(i).getSkin(), 49, 49);
            g.drawImage(imgpathAdaptive.getImage(), x + i % 25 % 5 * 51, y + i % 25 / 5 * 51, 49, 49, null);
            if (i >= 25 * wingMainPanel.getPageNumber() - 1) {
                return;
            }
        }
    }

    /**
     * 画出召唤兽装备的材料物品
     */
    public static void drawSummonGoods(Graphics g, int x, int y,int ys) {
        ys = - ys * 12;
        for (int i = 0; i < Goodslist.length; i++) {
            int row = ys % 6 * 51;
            int col = ys / 6 * 51;
            if (Goodslist[i] == null) {
                continue;
            }
            if (!Goodtype.isSummonEquip(Goodslist[i].getType()) && !Goodtype.isSummonGoods(Goodslist[i].getType())) {
                continue;
            }
            ys++;
            if (ys>=1) {
                g.drawImage(imgpathWdfile(Goodslist[i].getSkin(), 50, 50).getImage(), x + row , y +col,
                        50, 50, null);

                if (Goodtype.isSummonGoods(Goodslist[i].getType())) {
                    Juitil.TextBackground(g, Goodslist[i].getUsetime() + "", 13, x + 2 +row , y+  col, Color.WHITE, UIUtils.MSYH_HY13);
                }

                if (ys >= 12) {
                    return;
                }
            }
        }
    }

    /**
     * 获取当前召唤兽材料
     * 
     * @param type
     * @return goodstable
     */
    public static Goodstable getSummonGoods(int ys ,int type) {
        ys = -ys * 12 ;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            if (!Goodtype.isSummonEquip(Goodslist[i].getType()) && !Goodtype.isSummonGoods(Goodslist[i].getType())) {
                continue;
            }
            if (type == ys) {
                return Goodslist[i];
            }
            ys++;
        }
        return null;
    }

    /** 获取并展示星卡图片 */
    public static void drawStarCardImg(Graphics g, int x, int y) {
        for (int i = 0; i < starCardList.size(); i++) {
            Goodstable goodstable = starCardList.get(i);
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 50, 45).getImage(), x + i % 4 * 51, y + i / 4 * 51, 50,
                    45, null);
            if (goodstable.getStatus() == 1) {
                g.drawImage(CutButtonImage.getImage("img/lingbao/msg/lf_g.png", 16, 16).getImage(),
                        x + 31 + i % 4 * 51, y + i / 4 * 51, 16, 16, null);
            }
            if (goodstable.getGoodlock() == 1) {
                g.drawImage(lockimg.getImage(), x + i % 4 * 51, y + i / 4 * 51, 10, 12, null);
            }
            if (i >= 23) {
                return;
            }
        }
    }

    /** 获取并展示星卡图片 */
    public static void drawStarCardImg(Graphics g, int x, int y, ArrayList<BigDecimal> num) {
        for (int i = 0; i < starCardList.size(); i++) {
            Goodstable goodstable = starCardList.get(i);
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 50, 50).getImage(), x + i % 6 * 51, y + i / 6 * 51, 50,
                    50, null);
            if (num.contains(goodstable.getRgid())) {
                g.drawImage(CutButtonImage.getImage("img/lingbao/msg/lf_g.png", 16, 16).getImage(),
                        x + 24 + i % 6 * 51, y + i / 6 * 51, 16, 16, null);
            }
            if (goodstable.getGoodlock() == 1) {
                g.drawImage(lockimg.getImage(), x + i % 6 * 51, y + i / 6 * 51, 10, 12, null);
            }
            if (i >= 23) {
                return;
            }
        }
    }

    /** 获取指定星卡物品 */
    public static Goodstable getStarCardGoods(int type) {
        for (int i = 0; i < starCardList.size(); i++) {
            if (type == i) {
                return starCardList.get(i);
            }
        }
        return null;
    }

    /** 获取指定星盘物品 */
    public static Goodstable getXpGoods(String name) {
        for (int i = 0; i < XpGoodsList.size(); i++) {
            Goodstable goodstable = XpGoodsList.get(i);
            if (goodstable.getGoodsname().contains(name)) {
                return XpGoodsList.get(i);
            }

        }
        return null;
    }

    /** 获取战斗后中的星卡物品 */
    public static Goodstable InBattleCard(){
        for (Goodstable goodstable : starCardList) {
            if (goodstable.getStatus() == 1) {
                return goodstable;
            }
        }
        return null;

    }

    /** 获取星卡培养材料图片 */
    public static void drawStarCardCultivateMaterialImg(Graphics g, long type, int x, int y) {
        int num = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            Goodstable goodstable = Goodslist[i];
            if (goodstable == null) {
                continue;
            }
            if (goodstable.getType() != type) {
                continue;
            }
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 50, 50).getImage(), x + num % 8 * 47, y + num / 8 * 51,
                    50, 50, null);
            g.setColor(Color.red);
            g.drawString(Goodslist[i].getUsetime() + "", x + 2 + num % 8 * 47, y + 11 + num / 8 * 51);
            num++;
            if (num >= 16) {
                return;
            }
        }
    }

    /** 获取星卡培养材料 */
    public static Goodstable getStarCardCultivateMaterialGoods(long type, int nowNum) {
        int num = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            Goodstable goodstable = Goodslist[i];
            if (goodstable == null) {
                continue;
            }
            if (goodstable.getType() != type) {
                continue;
            }
            if (nowNum == num) {
                return goodstable;
            }
            num++;
        }
        return null;
    }

    /** 斗转星移面板 */
    public static void drawStarArray(Graphics g, int x, int y) {
        for (int i = 0; i < starCardList.size(); i++) {
            Goodstable goodstable = starCardList.get(i);
            g.drawImage(imgpathAdaptive(goodstable.getSkin(), 50, 50).getImage(), x + i * 51, y, 50, 50, null);
            if (i >= 8) {
                return;
            }
        }
    }

    public static Goodstable getStarArray(int type) {
        for (int i = 0; i < starCardList.size(); i++) {
            if (type == i) {
                return starCardList.get(i);
            }
            if (i >= 8) {
                return null;
            }
        }
        return null;
    }

    /** 画伙伴物品 */
    public static void drawPalGoods(int x, int y, Graphics g) {
        int num = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            // 7500-7550
            if (Goodtype.isPalGoods(Goodslist[i].getType())) {
                g.drawImage(imgpathWdfile(Goodslist[i].getSkin(),60,60).getImage(), x + num % 3 * 39, y + num / 3 * 40, 38, 39,
                        null);
                if (!Goodtype.isPalEquip(Goodslist[i].getType())) {
                    g.setColor(Color.red);
                    g.drawString(Goodslist[i].getUsetime() + "", x + num % 3 * 39, y + 11 + num / 3 * 40);
                }

                num++;
                if (num >= 24) {
                    return;
                }
            }
        }
    }

    /**
     * 排序
     * @param objects
     */
    public static void sort(Goodstable[] objects){
        for(int i = 0; i < objects.length - 1; i++){
            for (int j = 0; j < objects.length - i - 1; j++){
                if (objects[j] != null && objects[j+1] != null) {
                    if (objects[j].getGoodsid().longValue() > objects[j+1].getGoodsid().longValue()){
                        Goodstable temp = objects[j];
                        objects[j] = objects[j + 1];
                        objects[j + 1] = temp;
                    }
                }
            }
        }
    }

    /**
     * 当前页整理
     */
    public static void arrange(){
        if (GoodsListFromServerUntil.is == GoodsListFromServerUntil.Pagenumber + 1) {
            ZhuFrame.getZhuJpanel().addPrompt2("当前包裹无法操作");
            return;
        }
        Goodstable[] gs = new Goodstable[24];
        int i = 0;
        for (int j = Pagenumber * 24; j < (Pagenumber + 1) * 24; j++){
            Goodstable goodstable = Goodslist[j];
            if (goodstable != null) {
                gs[i++] = goodstable;
            }
        }
        sort(gs);
        i = 0;
        for (int j = Pagenumber * 24; j < (Pagenumber + 1) * 24; j++){
            Goodslist[j] = gs[i++];//....
        }

//        Goodslist = gs;
        PageNumberChange(Pagenumber);
        isSendPackRecord();
    }
    /** 全局整理 */
    public static void allArrange() {
        if (GoodsListFromServerUntil.is >= 2 && GoodsListFromServerUntil.is <= 6) {
            int specifiedPosition = Goodslist.length - 24; // 指定的位置，这里假设是数组的倒数第24个位置
            Goodstable[] gs = new Goodstable[Goodslist.length];
            int i = 0;
            // 遍历 Goodslist 数组，将特定类型的物品放到指定位置
            for (int j = 0; j < Goodslist.length; j++) {
                Goodstable goodstable = Goodslist[j];
                if (goodstable != null) {
                    // 判断物品类型是否是指定类型
                    if (Goodtype.FixedItems(goodstable.getType())) {
                        // 放到指定位置
                        gs[specifiedPosition++] = goodstable;
                    } else {
                        // 放到普通位置
                        gs[i++] = goodstable;
                    }
                }
            }
            sort(gs);
            Goodslist = gs;
            PageNumberChange(Pagenumber);
            isSendPackRecord();
        }
    }





    /**
     * 获取对应的伙伴物品
     * @param type
     * @return
     */
    public static Goodstable getPalGoods(int type) {
        int num = 0;
        for (int i = 0; i < Goodslist.length; i++) {
            if (Goodslist[i] == null) {
                continue;
            }
            // 7500-7550
            if (Goodtype.isPalGoods(Goodslist[i].getType())) {
                if (num == type) {
                    return Goodslist[i];
                }
                num++;
                if (num >= 24) {
                    return null;
                }
            }
        }
        return null;
    }

    public static Goodstable[] getGoodslist() {
        return Goodslist;
    }

    public static Goodstable[] getChoseGoodsList() {
        return choseGoodsList;
    }

    public static List<Goodstable> getWingGoodsList() {
        return wingGoodsList;
    }

    public static void setWingGoodsList(List<Goodstable> wingGoodsList) {
        GoodsListFromServerUntil.wingGoodsList = wingGoodsList;
    }


    public static List<Goodstable> getXpGoodsList() {
        return XpGoodsList;
    }

    public static void setXpGoodsList(List<Goodstable> xpGoodsList) {
        XpGoodsList = xpGoodsList;
    }

    public static List<Goodstable> getStarCardList() {
        return starCardList;
    }

    public static void setStarCardList(List<Goodstable> starCardList) {
        GoodsListFromServerUntil.starCardList = starCardList;
    }

    public static boolean isNottrue() {
        return Nottrue;
    }

    public static void setNottrue(boolean nottrue) {
        Nottrue = nottrue;
    }

    public static boolean isNottrueKs() {
        return NottrueKs;
    }

    public static void setNottrueKs(boolean nottrueKs) {
        NottrueKs = nottrueKs;
    }



}
