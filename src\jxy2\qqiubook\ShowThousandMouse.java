package jxy2.qqiubook;

import org.come.Frame.ZhuFrame;
import org.come.until.MessagrFlagUntil;
import org.come.until.ScrenceUntil;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class ShowThousandMouse implements MouseListener, MouseMotionListener {
    private ShowThousandJPanl showThousandJPanl;
    private int lastX;    // 上一次鼠标的X坐标
    private int lastY;    // 上一次鼠标的Y坐标
    private boolean isDragging = false;  // 是否正在拖动的标志
    private Point dragStart = null;  // 拖动开始的点

    // 定义面板的尺寸常量
    private static final int PANEL_WIDTH = 320;
    private static final int PANEL_HEIGHT = 70;

    public ShowThousandMouse(ShowThousandJPanl showThousandJPanl) {
        this.showThousandJPanl = showThousandJPanl;
    }

    @Override
    public void mouseClicked(MouseEvent e) {
        // 点击事件处理
    }

    @Override
    public void mousePressed(MouseEvent e) {
        // 记录开始拖动时的坐标
        dragStart = e.getPoint();
        lastX = e.getXOnScreen();
        lastY = e.getYOnScreen();
        isDragging = true;
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        if (e.getButton()==MouseEvent.BUTTON3){
            showThousandJPanl.setVisible(false);
            ZhuFrame.getZhuJpanel().remove(showThousandJPanl);
        }else {
            // 结束拖动
            isDragging = false;
            dragStart = null;
        }


    }

    @Override
    public void mouseEntered(MouseEvent e) {
        // 鼠标进入事件处理
    }

    @Override
    public void mouseExited(MouseEvent e) {
        // 鼠标离开事件处理
    }

    @Override
    public void mouseDragged(MouseEvent e) {
        if (!isDragging || dragStart == null) return;

        // 计算鼠标移动的距离
        int dx = e.getXOnScreen() - lastX;
        int dy = e.getYOnScreen() - lastY;

        // 获取当前面板的位置
        Point location = showThousandJPanl.getLocation();

        // 计算新位置
        int newX = location.x + dx;
        int newY = location.y + dy;

        // 限制面板不超出屏幕范围
        newX = Math.max(0, Math.min(newX, ScrenceUntil.Screen_x - PANEL_WIDTH));
        newY = Math.max(0, Math.min(newY, ScrenceUntil.Screen_y - PANEL_HEIGHT));

        // 更新面板位置
        showThousandJPanl.setLocation(newX, newY);

        // 更新上一次的鼠标位置
        lastX = e.getXOnScreen();
        lastY = e.getYOnScreen();
    }

    @Override
    public void mouseMoved(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
    }
}
