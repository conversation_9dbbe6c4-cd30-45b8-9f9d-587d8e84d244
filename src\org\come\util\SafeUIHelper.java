package org.come.util;

import javax.swing.*;
import javax.swing.plaf.ComponentUI;
import javax.swing.plaf.basic.*;
import java.lang.reflect.Method;

/**
 * 安全的 UI 设置助手类
 * 解决 Native Image 环境中 setUI 方法的访问权限问题
 */
public class SafeUIHelper {
    
    private static Method setUIMethod = null;
    private static boolean methodInitialized = false;
    
    /**
     * 初始化反射方法
     */
    private static void initializeSetUIMethod() {
        if (methodInitialized) {
            return;
        }
        
        try {
            setUIMethod = JComponent.class.getDeclaredMethod("setUI", ComponentUI.class);
            setUIMethod.setAccessible(true);
            System.out.println("[SafeUIHelper] setUI 方法反射初始化成功");
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] setUI 方法反射初始化失败: " + e.getMessage());
            setUIMethod = null;
        }
        
        methodInitialized = true;
    }
    
    /**
     * 安全地设置组件 UI
     */
    public static boolean safeSetUI(JComponent component, ComponentUI ui) {
        if (component == null || ui == null) {
            return false;
        }
        
        // 确保反射方法已初始化
        initializeSetUIMethod();
        
        if (setUIMethod == null) {
            System.err.println("[SafeUIHelper] setUI 方法不可用");
            return false;
        }
        
        try {
            setUIMethod.invoke(component, ui);
            return true;
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 设置 UI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 为 JTextArea 设置安全的 UI
     */
    public static boolean setTextAreaUI(JTextArea textArea) {
        try {
            BasicTextAreaUI ui = new BasicTextAreaUI();
            return safeSetUI(textArea, ui);
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 创建 BasicTextAreaUI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 为 JTextField 设置安全的 UI
     */
    public static boolean setTextFieldUI(JTextField textField) {
        try {
            BasicTextFieldUI ui = new BasicTextFieldUI();
            return safeSetUI(textField, ui);
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 创建 BasicTextFieldUI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 为 JLabel 设置安全的 UI
     */
    public static boolean setLabelUI(JLabel label) {
        try {
            BasicLabelUI ui = new BasicLabelUI();
            return safeSetUI(label, ui);
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 创建 BasicLabelUI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 为 JPanel 设置安全的 UI
     */
    public static boolean setPanelUI(JPanel panel) {
        try {
            BasicPanelUI ui = new BasicPanelUI();
            return safeSetUI(panel, ui);
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 创建 BasicPanelUI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 为 JButton 设置安全的 UI
     */
    public static boolean setButtonUI(JButton button) {
        try {
            BasicButtonUI ui = new BasicButtonUI();
            return safeSetUI(button, ui);
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 创建 BasicButtonUI 失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全地更新组件 UI
     */
    public static void safeUpdateUI(JComponent component) {
        if (component == null) {
            return;
        }

        try {
            component.updateUI();
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] updateUI 失败: " + e.getMessage());

            // 尝试设置基本 UI
            boolean success = false;
            if (component instanceof JTextArea) {
                success = setTextAreaUI((JTextArea) component);
            } else if (component instanceof JTextField) {
                success = setTextFieldUI((JTextField) component);
            } else if (component instanceof JLabel) {
                success = setLabelUI((JLabel) component);
            } else if (component instanceof JPanel) {
                success = setPanelUI((JPanel) component);
            } else if (component instanceof JButton) {
                success = setButtonUI((JButton) component);
            } else {
                // 对于其他组件类型，尝试通用处理
                success = setGenericUI(component);
            }

            if (success) {
                System.out.println("[SafeUIHelper] 成功设置基本 UI: " + component.getClass().getSimpleName());
            } else {
                System.err.println("[SafeUIHelper] 设置基本 UI 失败: " + component.getClass().getSimpleName());
                // 最后的回退：尝试重新验证组件
                try {
                    component.revalidate();
                    component.repaint();
                } catch (Exception ex) {
                    System.err.println("[SafeUIHelper] 组件重新验证失败: " + ex.getMessage());
                }
            }
        }
    }

    /**
     * 为通用组件设置 UI
     */
    private static boolean setGenericUI(JComponent component) {
        try {
            // 尝试获取组件的默认 UI
            String uiClassName = component.getUIClassID();
            if (uiClassName != null) {
                // 尝试从 UIManager 获取 UI
                javax.swing.plaf.ComponentUI ui = javax.swing.UIManager.getUI(component);
                if (ui != null) {
                    return safeSetUI(component, ui);
                }
            }
            return false;
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] 通用 UI 设置失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试 UI 设置功能
     */
    public static void testUISetup() {
        System.out.println("[SafeUIHelper] 开始测试 UI 设置功能...");
        
        try {
            // 测试 JTextArea
            JTextArea textArea = new JTextArea();
            safeUpdateUI(textArea);
            
            // 测试 JTextField
            JTextField textField = new JTextField();
            safeUpdateUI(textField);
            
            // 测试 JLabel
            JLabel label = new JLabel("测试");
            safeUpdateUI(label);
            
            System.out.println("[SafeUIHelper] UI 设置功能测试完成");
            
        } catch (Exception e) {
            System.err.println("[SafeUIHelper] UI 设置功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
