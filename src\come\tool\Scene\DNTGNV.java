package come.tool.Scene;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import org.come.until.ScrenceUntil;

import com.tool.tcpimg.UIUtils;

public class DNTGNV {

	
	private int num;//自己击杀次数
	private int ci;//自己名次
	private String msg1;
	private String msg2;
	private DNTGNVRole[] TTNV;
	private DNTGNVRole[] HGSNV;
	private Image image;
	public DNTGNV() {
		// TODO Auto-generated constructor stub
		this.num=0;
		this.ci=0;
		this.msg1="";
		this.msg2="";
		TTNV =new DNTGNVRole[5];
		HGSNV=new DNTGNVRole[5];
		this.image=new ImageIcon("inkImg/background/S19.png").getImage();
	}
	public void upRole(int camp,int num,int ci){
		this.num=num;
		this.ci=ci;
		if (ci==0) {
			msg1=camp==0?"天庭":"花果山";
		}else {
			msg1=camp==0?"天庭   第"+ci+"名":"花果山   第"+ci+"名";
		}
		msg2=num+"";
	}
	/**刷新排行榜*/
	public void upRanking(int camp,String msg){
//		N0/1(阵营排行榜)名称$击杀次数&名称$击杀次数
		DNTGNVRole[] roles=camp==0?TTNV:HGSNV;
		String[] vs=msg.split("&");
		for (int i = 0; i < vs.length&&i<5; i++) {
			String[] v=vs[i].split("\\$");
			if (roles[i]==null) {
				roles[i]=new DNTGNVRole(v[0],v[1]);
			}else {
				roles[i].upRole(v[0],v[1]);
			}
		}
		for (int i = vs.length; i < 5; i++) {
			roles[i]=null;
		}
	}
	public void draw(Graphics g) {
		int x=ScrenceUntil.Screen_x-image.getWidth(null);
		g.drawImage(image,x,130,null);
		g.setColor(Color.white);
		g.setFont(UIUtils.TEXT_FONT2);
		x+=40;
		g.drawString(msg1, x, 230);
		g.drawString(msg2, x+108, 230);
		for (int i = 0; i < TTNV.length; i++) {
			DNTGNVRole role=TTNV[i];
			if (role==null) {
				break;
			}
			g.drawString(role.getName(), x,  258+21*i);
			g.drawString(role.getSize(), x+108, 258+21*i);
		}
		for (int i = 0; i < HGSNV.length; i++) {
			DNTGNVRole role=HGSNV[i];
			if (role==null) {
				break;
			}
			g.drawString(role.getName(), x,  381+21*i);
			g.drawString(role.getSize(), x+108, 381+21*i);
		}
	}
	public int getNum() {
		return num;
	}
	public void setNum(int num) {
		this.num = num;
	}
	public int getCi() {
		return ci;
	}
	public void setCi(int ci) {
		this.ci = ci;
	}
	public DNTGNVRole[] getTTNV() {
		return TTNV;
	}
	public void setTTNV(DNTGNVRole[] tTNV) {
		TTNV = tTNV;
	}
	public DNTGNVRole[] getHGSNV() {
		return HGSNV;
	}
	public void setHGSNV(DNTGNVRole[] hGSNV) {
		HGSNV = hGSNV;
	}
	public Image getImage() {
		return image;
	}
	public void setImage(Image image) {
		this.image = image;
	}

	
}
