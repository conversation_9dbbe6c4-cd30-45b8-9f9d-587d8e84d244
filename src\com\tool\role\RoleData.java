package com.tool.role;

import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import jxy2.supet.SipetSortFrame;
import org.come.bean.LoginResult;
import org.come.bean.PrivateData;
import org.come.bean.RoleShow;
import org.come.bean.TeamBean;
import org.come.entity.PackRecord;
import org.come.entity.Pal;
import org.come.entity.RoleSummoning;
import org.come.until.AnalysisString;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserMessUntil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RoleData {
	private static RoleData roleData;
	//玩家数据
	private LoginResult loginResult;
	// 背包记录数据
	private PackRecord packRecord;
	// 系统设置
	private RoleSystem roleSystem;
	// 私密数据
	private PrivateData privateData;
	// 召唤兽支援列表
	private List<BigDecimal> helpBb;
	// 伙伴数据
	private List<Pal> pals;
	private TeamBean teamBean;
	public static RoleData getRoleData() {
		if (roleData == null) {
			roleData = new RoleData();
		}
		return roleData;
	}
	public RoleData() {
		// TODO Auto-generated constructor stub
		this.packRecord = new PackRecord();
		this.roleSystem = new RoleSystem();
		this.privateData = new PrivateData();
		helpBb = new ArrayList<>();
	}
	public void init(PackRecord packRecord, RoleSystem roleSystem, PrivateData privateData, LoginResult role) {
		if (role!=null) {
			this.loginResult=role;
		}
		if (packRecord != null) {
			this.packRecord = packRecord;
			String bbs = packRecord.getHelpBb();
			if (bbs != null) {
				String[] v = bbs.split("\\|");
				helpBb.clear();
				for (int i = 0; i < v.length; i++) {
					BigDecimal a = new BigDecimal(v[i]);
					helpBb.add(a);
				}
			}
			RoleTX roleTX = RoleTX.getRoleTX();
			roleTX.Toggle2(0);
			roleTX.EToggle(-1);
			roleTX.EToggle(-2);
			roleTX.EToggle(-3);
			roleTX.EToggle(-4);
			List<String> list = this.packRecord.getPutTX();
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					roleTX.EToggle(Integer.parseInt(list.get(i)));
				}
			}
			roleTX.chushihuaWing();
		}
		if (roleSystem != null) {
			this.roleSystem = roleSystem;
		}
		if (privateData != null) {
			this.privateData = privateData;
			String[] vs = privateData.getSkill("S");
			if (vs != null) {
				boolean is = false;
				List<Integer> list = new ArrayList<>();
				for (int i = 0; i < vs.length; i++) {
					String[] vss = vs[i].split("_");
					int id = Integer.parseInt(vss[0]);
					if (!list.contains(id)) {
						list.add(Integer.parseInt(vss[0]));
					} else {
						is = true;
					}
				}
				String[] ses = SkillUtil.getSepciesS(SkillUtil.getSepciesN(ImageMixDeal.userimg.getRoleShow().getSpecies_id()));
				for (int i = list.size() - 1; i >= 0; i--) {
					int id = SkillUtil.changeSkillId(list.get(i), ses);
					if (id != list.get(i)) {
						list.remove(i);
						is = true;
					}
				}
				if (is) {
					int sld = AnalysisString.shuliandu(ImageMixDeal.userimg.getRoleShow().getGrade());
					StringBuffer buffer = new StringBuffer();
					for (int i = list.size() - 1; i >= 0; i--) {
						if (buffer.length() != 0) {
							buffer.append("#");
						}
						buffer.append(list.get(i));
						buffer.append("_");
						buffer.append(sld);
					}
					this.privateData.setSkills("S", buffer.length() == 0 ? null : buffer.toString());
					SendRoleAndRolesummingUntil.sendRole(this.privateData);
				}
			}

		}
	}
	/**初始化列表*/
	public void addHelpBb(List<BigDecimal> Bbs) {
		for (int i = helpBb.size() - 1; i >= 0; i--) {
			if (!Bbs.contains(helpBb.get(i))) {
				helpBb.remove(i);
			}
		}
		for (int i = 0; i < Bbs.size(); i++) {
			if (!helpBb.contains(Bbs.get(i))) {
				helpBb.add(Bbs.get(i));
			}
		}
	}

	/** 获取拥护闪现召唤兽列表 */
	public List<BigDecimal> getHelpBbId() {
		int[] vs = new int[] { 1806, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827 };
		List<BigDecimal> list = new ArrayList<>();
		List<RoleSummoning> pets = UserMessUntil.getPetListTable();
		ss: for (int i = pets.size() - 1; i >= 0; i--) {
			String skill = pets.get(i).getPetSkills();
			if (skill == null || skill.equals("")) {
				continue ss;
			}
			String[] v = skill.split("\\|");
			for (int j = 0; j < v.length; j++) {
				int id = Integer.parseInt(v[j]);
				for (int k = 0; k < vs.length; k++) {
					if (vs[k] == id) {
						list.add(pets.get(i).getSid());
						continue ss;
					}
				}
			}
		}
		return list;
	}

	/** 根据列表获取召唤兽名称 */
	public List<String> getHelpBbName(List<BigDecimal> Bbs) {
		List<String> list = new ArrayList<>();
		List<RoleSummoning> pets = UserMessUntil.getPetListTable();
        for (BigDecimal id : helpBb) {
            for (int j = pets.size() - 1; j >= 0; j--) {
                if (pets.get(j).getSid().compareTo(id) == 0) {
                    list.add(pets.get(j).getSummoningname());
                    break;
                }
            }
        }
		return list;
	}

	// 切换位置返回变化后的名称 当前位置 变化后位置
	public List<String> CHelpBb(int v, int v2) {
		if (v2 > helpBb.size() - 1) {
			v2 = helpBb.size() - 1;
		}
		if (v > helpBb.size() - 1) {
			v = helpBb.size() - 1;
		}
		if (v == v2) {
			return null;
		}
		if (v < 0 || v2 < 0) {
			return null;
		}
		BigDecimal id = helpBb.remove(v);
		helpBb.add(v2, id);
		sendHelpBb();
		return getHelpBbName(helpBb);
	}
	
	// 存修改的召唤兽列表
	public void sendPetBb(List<RoleSummoning> list) {
		UserMessUntil.setPetListTable(list);
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < list.size(); i++) {
			if (i != 0) {
				buffer.append("|");
			}
			buffer.append(list.get(i).getSid().toString());
		}
		GoodsListFromServerUntil.sendPackRecord(6, buffer.toString());
	}


	// 存修改的召唤兽支援列表
	public void sendHelpBb() {
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < helpBb.size(); i++) {
			if (i != 0) {
				buffer.append("|");
			}
			buffer.append(helpBb.get(i));
		}
		packRecord.setHelpBb(buffer.toString());
		GoodsListFromServerUntil.sendPackRecord(1, packRecord.getHelpBb());
	}

	/** 添加特效 */
	public void addTx(String id) {
		packRecord.addTX(id);
		RoleTX.getRoleTX().Toggle2(RoleTX.getRoleTX().getTxYs());
	}

	public PackRecord getPackRecord() {
		return packRecord;
	}

	public void setPackRecord(PackRecord packRecord) {
		this.packRecord = packRecord;
	}

	public RoleSystem getRoleSystem() {
		return roleSystem;
	}

	public void setRoleSystem(RoleSystem roleSystem) {
		this.roleSystem = roleSystem;
	}

	public List<BigDecimal> getHelpBb() {
		return helpBb;
	}

	public void setHelpBb(List<BigDecimal> helpBb) {
		this.helpBb = helpBb;
	}

	public PrivateData getPrivateData() {
		return privateData;
	}

	public void setPrivateData(PrivateData privateData) {
		this.privateData = privateData;
	}

	public LoginResult getLoginResult() {
		return loginResult;
	}

	public void setLoginResult(LoginResult loginResult) {
		this.loginResult = loginResult;
	}
	public List<Pal> getPals() {
		return pals;
	}
	public void setPals(List<Pal> pals) {
		this.pals = pals;
	}
	public Pal getPal(int pid){
		for (int i = pals.size()-1; i>=0; i--) {
			if (pals.get(i).getpId()==pid) {
				return pals.get(i);
			}
		}
		return null;
	}
	public void addPal(Pal pal){
		for (int i = pals.size()-1; i>=0; i--) {
			if (pals.get(i).getId().compareTo(pal.getId())==0) {
				pals.set(i, pal);
				return;
			}
		}
		pals.add(pal);
	}
	public TeamBean getTeamBean() {
		return teamBean;
	}
	public void setTeamBean(TeamBean teamBean) {
		this.teamBean = teamBean;
	}

	/**
	 * 获取所有在线玩家的RoleShow列表
	 */
	public List<RoleShow> getRoleShows() {
		List<RoleShow> roleShows = new ArrayList<>();
		// 添加当前玩家
		if (ImageMixDeal.userimg != null && ImageMixDeal.userimg.getRoleShow() != null) {
			roleShows.add(ImageMixDeal.userimg.getRoleShow());
		}
		// 添加其他在线玩家
		for (ManimgAttribute attribute : ImageMixDeal.Playerimgmap.values()) {
			if (attribute != null && attribute.getRoleShow() != null) {
				roleShows.add(attribute.getRoleShow());
			}
		}
		return roleShows;
	}

	public List<String> PetBb(int v, int v2) {
		List<RoleSummoning> list = new ArrayList<RoleSummoning>(){
			{
				this.addAll(UserMessUntil.getPetListTable());
			}
		};
		if (v2 > list.size() - 1) {
			v2 = list.size() - 1;
		}
		if (v > list.size() - 1) {
			v = list.size() - 1;
		}
		if (v == v2) {
			return null;
		}
		if (v < 0 || v2 < 0) {
			return null;
		}
		RoleSummoning id = list.remove(v);
		list.add(v2, id);

		SipetSortFrame.getSipetSortFrame().getSipetSortJPanel().getListpet().setSelectedIndex(v2);
		sendPetBb(list);
		return new ArrayList<String>(){
			{
				list.forEach(pet -> add(pet.getSummoningname()));
			}
		};
	}
}
