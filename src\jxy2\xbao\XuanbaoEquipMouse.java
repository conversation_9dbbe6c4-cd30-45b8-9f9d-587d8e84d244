package jxy2.xbao;

import org.come.Frame.ZhuFrame;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;

import java.awt.event.MouseEvent;

public class XuanbaoEquipMouse extends TemplateMouseListener {
    private int path;
    public XbaoJpanel xbaoJpanel;
    public XuanbaoEquipMouse(int path,XbaoJpanel xbaoJpanel) {
        this.path = path;
        this.xbaoJpanel = xbaoJpanel;
    }
    @Override
    protected void specificMousePressed(MouseEvent e) {
        for (int i = 0; i <RoleXuanbao.getRoleXuanbao().equipBao.length ; i++) {
            if (path==i){
                if (RoleXuanbao.getRoleXuanbao().equipBao[i]==null){
                    return;
                }
            }
        }
        Xuanbao xuanbao = getXuanbao();

        if (e.getButton() == MouseEvent.BUTTON3) {// 右键点击脱下玄宝
            RoleXuanbao.getRoleXuanbao().choseuse(xuanbao, false);
        }
        xbaoJpanel.LoadingOfSelected(xuanbao);
        RoleXuanbao.getRoleXuanbao().choseBao = xuanbao;
        xbaoJpanel.drawEquip( path,true);
        FormsManagement.Switchinglevel(149);
    }

    private Xuanbao getXuanbao() {
        Xuanbao xuanbao;
        if (path==2){
            xuanbao = RoleXuanbao.getRoleXuanbao().equipBao[2];
        }else if (path==1){
            xuanbao = RoleXuanbao.getRoleXuanbao().equipBao[1];
        }else {
            xuanbao = RoleXuanbao.getRoleXuanbao().equipBao[0];
        }
        return xuanbao;
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        for (int i = 0; i <RoleXuanbao.getRoleXuanbao().equipBao.length ; i++) {
            if (path==i){
                if (RoleXuanbao.getRoleXuanbao().equipBao[i]==null){
                    return;
                }
            }
        }
        xbaoJpanel.drawEquip( path,false);
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        Xuanbao xuanbao = getXuanbao();
        if (xuanbao != null) {
            ZhuFrame.getZhuJpanel().creatXuantext(xuanbao);
            if (path>=0)
                xbaoJpanel.EqPain(path,50);
        }
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().clearxuantext();
        if (path>=0)
            xbaoJpanel.EqClearText(path);

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
