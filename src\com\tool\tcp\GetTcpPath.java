package com.tool.tcp;
/**
 * 补充不同类型tcp路径
 * <AUTHOR>
 */
public class GetTcpPath {

	public static String WEI=".tcp";
	public static String ROLE="skin/";
	public static String BUFF="resource/FightingSkill/持续状态/";
	public static String SKILL="resource/FightingSkill/";
	public static String MOUSE="resource/mouse/";
	//令牌
	public static String LIN=MOUSE+"令牌"+WEI;
	//PK
	public static String PK=MOUSE+"PK"+WEI;
	//获取人物素材
	public static String getRoleTcp(String skin,int type,String ColorScheme){
		StringBuffer buffer=new StringBuffer();
		buffer.append(ROLE);
		buffer.append(skin);
		buffer.append("/");
		buffer.append(SpriteFactory.getActionType(type));	
		buffer.append(WEI);
		if (ColorScheme!=null&&!ColorScheme.equals("")) {
			buffer.append("_");
			buffer.append(ColorScheme);
		}
		return buffer.toString();
	}
	//获取持续状态
	public static String getBuffTcp(String tcp){
		return BUFF+tcp+WEI;
	}
	//获取法术
	public static String getSkillTcp(String tcp){
		return SKILL+tcp+WEI;
	}
	//获取鼠标
	public static String getMouseTcp(String tcp){
		return MOUSE+tcp+WEI;
	}
	
}
