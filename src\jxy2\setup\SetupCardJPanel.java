package jxy2.setup;

import org.come.Jpanel.TestSetupJpanel;

import javax.swing.*;
import java.awt.*;

public class SetupCardJPanel extends JPanel {
    private CardLayout cardLayout;
    private AudioSteupJpanel audioSteupJpanel;//影音设置
    private TestSetupJpanel testSetupJpanel;//游戏设置
    public SetupCardJPanel() {
        setBounds(0, 0, 300, 354);
        this.setLayout(null);
        this.setOpaque(false);
        cardLayout = new CardLayout();
        setLayout(cardLayout);
        this.add(audioSteupJpanel = new AudioSteupJpanel(), "l1");
        this.add(testSetupJpanel = new TestSetupJpanel(), "l2");

    }

    public CardLayout getCardLayout() {
        return cardLayout;
    }

    public void setCardLayout(CardLayout cardLayout) {
        this.cardLayout = cardLayout;
    }

    public AudioSteupJpanel getAudioSteupJpanel() {
        return audioSteupJpanel;
    }

    public void setAudioSteupJpanel(AudioSteupJpanel audioSteupJpanel) {
        this.audioSteupJpanel = audioSteupJpanel;
    }

    public TestSetupJpanel getTestSetupJpanel() {
        return testSetupJpanel;
    }

    public void setTestSetupJpanel(TestSetupJpanel testSetupJpanel) {
        this.testSetupJpanel = testSetupJpanel;
    }
}
