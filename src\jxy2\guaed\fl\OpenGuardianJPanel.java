package jxy2.guaed.fl;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class OpenGuardianJPanel extends JPanel {
    private LoadGuardianJPanel loadPanel;  // 作为成员而不是父类
    private LoadGuardianBtn btndecompose;
    private Goodstable goodstable;
    private JLabel goodslabel;
    private int quantityObtainable=0;
    public OpenGuardianJPanel() {
        // 初始化基本面板设置
        this.setPreferredSize(new Dimension(375, 386));
        this.setLayout(null);  // 使用绝对布局
        this.setBackground(UIUtils.Color_BACK);
        
        // 创建LoadGuardianJPanel实例并设置位置
        loadPanel = new LoadGuardianJPanel();
        loadPanel.setBounds(15, 220, 370, 170);  // 设置在合适的位置
        loadPanel.setIs(false);
        // 将loadPanel添加到最上层
        this.add(loadPanel);
        // 添加关闭按钮
        Juitil.addClosingButtonToPanel(this, 143, 375);
        // 确保面板是不透明的
        this.setOpaque(true);
        btndecompose = new LoadGuardianBtn(ImgConstants.tz34, 1,  9, "分 解", this,"");
        btndecompose.setBounds(152, 208, 59, 24);
        this.add(btndecompose);
        goodslabel = new JLabel();
        goodslabel.setBounds(162, 73, 56, 57);
        goodslabel.addMouseListener(new TemplateMouseListener() {
            @Override
            protected void specificMousePressed(MouseEvent e) {
                goodslabel.setIcon(null);
                quantityObtainable = 0;
            }

            @Override
            protected void specificMouseReleased(MouseEvent e) {

            }

            @Override
            protected void specificMouseEntered(MouseEvent e) {

            }

            @Override
            protected void specificMouseExited(MouseEvent e) {

            }

            @Override
            protected void specificMouseDragged(MouseEvent e) {

            }

            @Override
            protected void specificMouseMoved(MouseEvent e) {

            }
        });
        this.add(goodslabel);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
            // 绘制标题和背景
            Juitil.JPanelNewShow(g, getWidth(), getHeight(), "分  解");
        Juitil.ImngBack(g, Juitil.tz22, 28, 40, 315, 175, 1);
        Juitil.ImngBack(g, Juitil.tz128, 190 ,115+39, 102, 19, 1);
        Juitil.TextBackground(g, "可获得守护之尘", 16, 60, 112+40, UIUtils.COLOR_goods_quantity, UIUtils.NEWTX_HY17B, UIUtils.Color_BACK);
        Juitil.TextBackground(g, quantityObtainable+"", 16, 190 , 112+41, UIUtils.COLOR_239, UIUtils.LiSu_LS16);
        Juitil.ImngBack(g, Juitil.tz258, 158, 73, 56, 57, 1);
        if (goodslabel.getIcon()==null) {
            Juitil.TextBackground(g, "待分解", 13, 164, 83, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            Juitil.TextBackground(g, "守护石", 13, 164, 83 + 20, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        }
    }
    
    // 提供访问loadPanel的方法
    public LoadGuardianJPanel getLoadPanel() {
        return loadPanel;
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    public void setLoadPanel(LoadGuardianJPanel loadPanel) {
        this.loadPanel = loadPanel;
    }

    public LoadGuardianBtn getBtndecompose() {
        return btndecompose;
    }

    public void setBtndecompose(LoadGuardianBtn btndecompose) {
        this.btndecompose = btndecompose;
    }

    public JLabel getGoodslabel() {
        return goodslabel;
    }

    public void setGoodslabel(JLabel goodslabel) {
        this.goodslabel = goodslabel;
    }

    public int getQuantityObtainable() {
        return quantityObtainable;
    }

    public void setQuantityObtainable(int quantityObtainable) {
        this.quantityObtainable = quantityObtainable;
    }
}
