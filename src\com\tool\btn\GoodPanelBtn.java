package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleLingFa;
import com.tool.tcpimg.InputBean;
import com.tool.tcpimg.UIUtils;
import jxy2.dress.DressUpFrame;
import jxy2.synt.SynthesisFrame;
import jxy2.zodiac.InlaidStarBtn;
import org.come.Frame.FactionAngelJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FactionAngelJpanel;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.TestpackJapnel;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.Coordinates;
import org.come.bean.LoginResult;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.mouslisten.Mouselistener;
import org.come.starcard.BtnStarCard;
import org.come.until.*;
import org.skill.btn.SkillTYCBtn;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 物品栏界面按钮
 * 
 * <AUTHOR>
 * 
 */
public class GoodPanelBtn extends MoBanBtn {
    public int index;

    public TestpackJapnel testpackJapnel;
    public GoodPanelBtn(String iconpath, int type, String text) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    public GoodPanelBtn(String iconpath, int type, String text, Color[] colors) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, colors);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public GoodPanelBtn(String iconpath, int type, String text,TestpackJapnel testpackJapnel,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath,0 ,type,0,0);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);

        this.index=index;
        this.testpackJapnel=testpackJapnel;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }


    public GoodPanelBtn(String iconpath, int type, String text,TestpackJapnel testpackJapnel,String prowpt,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        this.index=index;
        this.testpackJapnel=testpackJapnel;
    }

    public GoodPanelBtn(String iconpath, int type, String text, int index, TestpackJapnel testpackJapnel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,prompt,text);
        this.index = index;
        this.testpackJapnel = testpackJapnel;
    }


    public GoodPanelBtn(String iconpath, int type, String text,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.index=index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }


    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {

        // TODO Auto-generated method stubs
        switch (this.getText()) {
            case "设":
                FormsManagement.showForm(32);
                break;
            case "改":
                FormsManagement.showForm(21);
                break;
            case "加锁":
            case "解锁":
                if (Util.canBuyOrno) {
                    Util.canBuyOrno = false;
                    ImageMixDeal.userimg.Dialogue("背包已加锁！");
                    FrameMessageChangeJpanel.addtext(5, "背包已加锁！", null, null);
                    this.setText("解锁");
                } else if (RoleData.getRoleData().getLoginResult().getPassword() == null) {
                    FormsManagement.showForm(32);
                } else {
                    FormsManagement.showForm(33);
                }
                break;
            case "星录":
                FormsManagement.showForm(95);
                break;
            default:
                LoginResult result = RoleData.getRoleData().getLoginResult();
                switch (index){
                    case 0:
                        Coordinates coordinates=new Coordinates(1207, 5110, 2990);
                        InputBean inputBean=new InputBean(new BigDecimal(400252),25,coordinates);
                        Mouselistener.DJInputBean(inputBean);
                        break;
                    case 1:
                        FormsManagement.showForm(90);
                        break;
                    case 2:
                        ZhuJpanel.setUseGoodsType(1);
                        FormsManagement.showForm(67);
                        break;
                    case 3:
                        FormsManagement.showForm(78);
                        break;
                    case 4:
                        FormsManagement.showForm(65);
                    case 5:
                        FormsManagement.showForm(116);
                        SynthesisFrame.getSynthesisFrame().getSynthesisJPanl().IniVsi();
                        break;
                    case 7:
                        testpackJapnel.showIsTeamBtn(true,1);
                        break;
                    case 8:
                        testpackJapnel.HzShowBtn(true);
                        break;
                    case 9:
                        testpackJapnel.showIsTeamBtn(false, 1);
                        GoodsListFromServerUntil.arrange();
                        break;
                    case 10:
                        testpackJapnel.showIsTeamBtn(false, 1);
                        GoodsListFromServerUntil.allArrange();
                        break;
                    case 11:
                    case 12:
                    case 13:
                    case 14:
                    case 15:
                        try {
                            if (result.getOneclick()!=null&& !result.getOneclick().isEmpty()){
                                String[] vs = result.getOneclick().split("&");
                                List<Integer> indices = new ArrayList<>();
                                for (String v : vs) {
                                    String[] vss = v.split("=");
                                    String[] goodid = vss[1].split("\\|");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        for (String s : goodid) {
                                            Goodstable goodstable = GoodsListFromServerUntil.getRgid(new BigDecimal(s.split("_")[1]));
                                            if (goodstable != null) {
                                                for (int j = 0; j < GoodsListFromServerUntil.getGoodslist().length; j++) {
                                                    Goodstable goods = GoodsListFromServerUntil.getGoodslist()[j];
                                                    if (goods != null && goodstable.getRgid().compareTo(goods.getRgid()) == 0) {
                                                        ZhuFrame.getZhuJpanel().drawEquip(goods,0);
                                                        indices.add(j); // 存储索引
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                // 一次性处理所有找到的索引
                                for (int index : indices) {
                                    Goodstable goods = GoodsListFromServerUntil.getGoodslist()[index];
                                    ZhuFrame.getZhuJpanel().drawEquip(goods,0);
                                    ManNoCombat(goods, goods.getType(),index);
                                }
                            }
                            //设置小成属性----
                            if (result.getXcpractice()!=null && !result.getXcpractice().isEmpty()){
                                String[] vs = result.getXcpractice().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("@");
                                    if (vss.length<2)continue;
                                    String[] xc = vss[1].split("\\|");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        for (String s : xc) {
                                            String name = s.substring(0,3);
                                            FactionAngelJpanel factionAngelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
                                            factionAngelJpanel.changeMenuShow(1);
                                            for (int i = 0; i < factionAngelJpanel.getBtnReplacement().length; i++) {
                                                if (factionAngelJpanel.getBtnReplacement()[i].getNtext().equals(name)){
                                                    FactionBtn.SwitchAttributes(i,true,1,"X");
                                                    FormsManagement.HideForm(54);
                                                    ZhuFrame.getZhuJpanel().addPrompt2("#Y小成属性：#R"+name+"·#G切换成功");
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //设置大成属性----
                            if (result.getDcpractice()!=null && !result.getDcpractice().isEmpty()){
                                String[] vs = result.getDcpractice().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("@");
                                    if (vss.length<2)continue;
                                    String[] xc = vss[1].split("\\|");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        for (String s : xc) {
                                            String name = s.substring(0,3);
                                            FactionAngelJpanel factionAngelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
                                            factionAngelJpanel.changeMenuShow(2);
                                            for (int i = 0; i < factionAngelJpanel.getBtnReplacement().length; i++) {
                                                if (factionAngelJpanel.getBtnReplacement()[i].getNtext().equals(name)){
                                                    FactionBtn.SwitchAttributes(i,true,2,"D");
                                                    FormsManagement.HideForm(54);
                                                    ZhuFrame.getZhuJpanel().addPrompt2("#Y大成属性：#R"+name+"·#G切换成功");
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            //获取灵宝数据
                            if (result.getClicklingbao()!=null && !result.getClicklingbao().isEmpty()){
                                String[] vs = result.getClicklingbao().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("=");
                                    String[] lingbaoid = vss[1].split("\\|");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        for (String s : lingbaoid) {
                                            Lingbao lingbao = RoleLingFa.getRoleLingFa().DressUp(new BigDecimal(s.split("_")[1]));
                                            if (lingbao != null) {
                                                RoleLingFa.getRoleLingFa().choseuse(lingbao, true);
                                                ZhuFrame.getZhuJpanel().addPrompt2("#Y灵宝：#R"+lingbao.getBaoname()+"·#G切换成功");
                                            }
                                        }
                                    }
                                }
                            }
                            //设置属性----
                            if (result.getStoresa()!=null&&!result.getStoresa().isEmpty()){
//                                String b = "";
//                                String[] vs = result.getStoresa().split("@");
//                                for (String v : vs){
//                                    String[] vss = v.split("·");
//                                    //取编号
//                                    int path = Integer.parseInt(vss[0]);
//                                    //取值
//                                    String[] zhi = vss[1].split("\\|");
//                                    for (String s : zhi) {
//                                        String[] vd = s.split("&");
//                                        System.out.println(Arrays.toString(vd));
//                                        int sum = Integer.parseInt(vd[1]);
//                                        String xh = vd[0];
//                                        if (sum == 1) {
//                                            if (xh.startsWith("D_Y")) {
//                                                b = "D_Y";
//                                            } else if (xh.startsWith("D_R")) {
//                                                b = "D_R";
//                                            } else if (xh.startsWith("D_S")) {
//                                                b = "D_S";
//                                            }
//                                            if ((index - 11) == path) {
//                                                JpanelOnJalbelBtn.Refresh(b);
//                                                String mes = Agreement.getAgreement().rolechangeAgreement("Q" + b);
//                                                SendMessageUntil.toServer(mes);
//                                                break; // 一旦找到并处理，可以提前退出内层循环
//                                            }
//                                        }
//                                    }
//                                }
                            }
                            //设置星卡----
                            if (result.getStatcard()!=null&&!result.getStatcard().isEmpty()){
                                String[] vs = result.getStatcard().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("=");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        BtnStarCard.ParticipateInBattles(new BigDecimal(vss[1]),0);
                                        break;
                                    }
                                }
                            }
                            //设置星盘技能----
                            if (result.getXpskill()!=null&&!result.getXpskill().isEmpty()){
                                String[] vs = result.getXpskill().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("·");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        int index = Integer.parseInt(vss[1].split("_")[1]);
                                        Skill skill = UserMessUntil.getSkillId(vss[1].split("_")[0]);
                                        if (skill!=null){
                                            InlaidStarBtn.ActivationSkill(skill.getSkillid(),index);
                                        }
                                        break;
                                    }
                                }
                            }
                            //设置天演策技能----
                            if (result.getTycclick()!=null&&!result.getTycclick().isEmpty()){
                                String[] vs = result.getTycclick().split("&");
                                for (String v : vs) {
                                    String[] vss = v.split("·");
                                    if (Integer.parseInt(vss[0]) == (index-11)) {
                                        SkillTYCBtn.QHTYC(vss[1],false);
                                        break;
                                    }
                                }
                            }


                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        break;
                    case 16://换装设置
                        if (!FormsManagement.getframe(132).isVisible()) {
                            // 设置存款
                            FormsManagement.showForm(132);
                            DressUpFrame.getDressUpFrame().getDressUpJPanel().initData(false,0);
                            Music.addyinxiao("开关窗口.mp3");
                        } else {
                            FormsManagement.HideForm(132);
                            Music.addyinxiao("关闭窗口.mp3");
                        }
                        break;
                    case 17:
                        if (!FormsManagement.getframe(119).isVisible()) {
                            // 设置存款
                            FormsManagement.showForm(119);
                            DressUpFrame.getDressUpFrame().getDressUpJPanel().initData(false,0);
                            Music.addyinxiao("开关窗口.mp3");
                        } else {
                            FormsManagement.HideForm(119);
                            Music.addyinxiao("关闭窗口.mp3");
                        }
                        break;
                }
                if (index>=11&&testpackJapnel!=null) {
                    testpackJapnel.HzShowBtn(true);
                }


        }
    }
    public void ManNoCombat(Goodstable goodstable, long type,int good) throws Exception {
        // 使用的物品属于装备
        int EquipmentType = Goodtype.EquipmentType(type);
        if (EquipmentType != -1) {//穿装备
            UseEquipment(EquipmentType, goodstable,good);

        }
    }
    public void UseEquipment(int EquipmentType, Goodstable goodstable,int good) throws Exception  {
        int part = DevicGoodsEquiptmentUntil.getEquiptmentOrNo(goodstable);
        if (part == -2) {
            ZhuFrame.getZhuJpanel().addPrompt2("你达不到装备要求");
            return;
        }
        // 判断装备部位要求
        if (part != -1) {
            EquipmentType = part;
        } else if (EquipmentType == 10) {
            if (GoodsListFromServerUntil.getChoseGoodsList()[EquipmentType] != null) {
                EquipmentType = 11;
            }
        }
            GoodsListFromServerUntil.MutualChange(EquipmentType, good);
            ZhuFrame.getZhuJpanel().drawEquip(goodstable,0);
    }
}
