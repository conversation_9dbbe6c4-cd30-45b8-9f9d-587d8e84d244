package jxy2.jutnil;

import java.awt.*;
import java.awt.event.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * KeyEvent 工厂类 - 解决 Native Image 中 KeyEvent 构造函数问题
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class KeyEventFactory {
    
    private static Constructor<KeyEvent> keyEventConstructor6;
    private static Constructor<KeyEvent> keyEventConstructor7;
    private static Constructor<KeyEvent> keyEventConstructor8;
    private static boolean isInitialized = false;
    private static boolean isNativeImage = false;
    
    static {
        initializeConstructors();
    }
    
    /**
     * 初始化 KeyEvent 构造函数
     */
    private static void initializeConstructors() {
        if (isInitialized) {
            return;
        }
        
        isNativeImage = System.getProperty("org.graalvm.nativeimage.imagecode") != null;
        
        try {
            // 6参数构造函数: Component, int, long, int, int, char
            keyEventConstructor6 = KeyEvent.class.getDeclaredConstructor(
                Component.class, int.class, long.class, int.class, int.class, char.class
            );
            keyEventConstructor6.setAccessible(true);
            
            // 7参数构造函数: Component, int, long, int, int, char, int
            keyEventConstructor7 = KeyEvent.class.getDeclaredConstructor(
                Component.class, int.class, long.class, int.class, int.class, char.class, int.class
            );
            keyEventConstructor7.setAccessible(true);
            
            // 8参数构造函数: Component, int, long, int, int, int, char, int
            try {
                keyEventConstructor8 = KeyEvent.class.getDeclaredConstructor(
                    Component.class, int.class, long.class, int.class, int.class, int.class, char.class, int.class
                );
                keyEventConstructor8.setAccessible(true);
            } catch (NoSuchMethodException e) {
                // 8参数构造函数可能不存在，这是正常的
                ConsoleUtil.println("[KeyEvent工厂] 8参数构造函数不存在，将使用其他构造函数");
            }
            
            ConsoleUtil.println("[KeyEvent工厂] KeyEvent 构造函数初始化完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[KeyEvent工厂] KeyEvent 构造函数初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        isInitialized = true;
    }
    
    /**
     * 创建 KeyEvent (6参数版本)
     */
    public static KeyEvent createKeyEvent(Component source, int id, long when, 
                                        int modifiers, int keyCode, char keyChar) {
        try {
            if (isNativeImage && keyEventConstructor6 != null) {
                return keyEventConstructor6.newInstance(source, id, when, modifiers, keyCode, keyChar);
            } else {
                return new KeyEvent(source, id, when, modifiers, keyCode, keyChar);
            }
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[KeyEvent工厂] 创建6参数KeyEvent失败: " + e.getMessage());
            // 回退到直接构造
            try {
                return new KeyEvent(source, id, when, modifiers, keyCode, keyChar);
            } catch (Exception e2) {
                ConsoleUtil.printlnErr("[KeyEvent工厂] 直接构造KeyEvent也失败: " + e2.getMessage());
                return null;
            }
        }
    }
    
    /**
     * 创建 KeyEvent (7参数版本)
     */
    public static KeyEvent createKeyEvent(Component source, int id, long when, 
                                        int modifiers, int keyCode, char keyChar, int keyLocation) {
        try {
            if (isNativeImage && keyEventConstructor7 != null) {
                return keyEventConstructor7.newInstance(source, id, when, modifiers, keyCode, keyChar, keyLocation);
            } else {
                return new KeyEvent(source, id, when, modifiers, keyCode, keyChar, keyLocation);
            }
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[KeyEvent工厂] 创建7参数KeyEvent失败: " + e.getMessage());
            // 回退到6参数版本
            return createKeyEvent(source, id, when, modifiers, keyCode, keyChar);
        }
    }
    
    /**
     * 创建 KeyEvent (8参数版本) - 如果支持的话
     */
    public static KeyEvent createKeyEvent(Component source, int id, long when, 
                                        int modifiers, int keyCode, int extendedKeyCode, 
                                        char keyChar, int keyLocation) {
        try {
            if (isNativeImage && keyEventConstructor8 != null) {
                return keyEventConstructor8.newInstance(source, id, when, modifiers, keyCode, extendedKeyCode, keyChar, keyLocation);
            } else {
                // 回退到7参数版本
                return createKeyEvent(source, id, when, modifiers, keyCode, keyChar, keyLocation);
            }
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[KeyEvent工厂] 创建8参数KeyEvent失败: " + e.getMessage());
            // 回退到7参数版本
            return createKeyEvent(source, id, when, modifiers, keyCode, keyChar, keyLocation);
        }
    }
    
    /**
     * 智能创建 KeyEvent - 自动选择最合适的构造函数
     */
    public static KeyEvent createKeyEventSmart(Component source, int id, long when, 
                                             int modifiers, int keyCode, char keyChar) {
        // 首先尝试最简单的6参数版本
        KeyEvent event = createKeyEvent(source, id, when, modifiers, keyCode, keyChar);
        if (event != null) {
            return event;
        }
        
        // 如果失败，尝试7参数版本（添加默认的keyLocation）
        return createKeyEvent(source, id, when, modifiers, keyCode, keyChar, KeyEvent.KEY_LOCATION_STANDARD);
    }
    
    /**
     * 检查 KeyEvent 构造函数是否可用
     */
    public static boolean isKeyEventConstructorAvailable() {
        return keyEventConstructor6 != null || keyEventConstructor7 != null;
    }
    
    /**
     * 获取可用的构造函数信息
     */
    public static String getAvailableConstructors() {
        StringBuilder sb = new StringBuilder();
        sb.append("[KeyEvent工厂] 可用构造函数: ");
        
        if (keyEventConstructor6 != null) {
            sb.append("6参数 ");
        }
        if (keyEventConstructor7 != null) {
            sb.append("7参数 ");
        }
        if (keyEventConstructor8 != null) {
            sb.append("8参数 ");
        }
        
        return sb.toString();
    }
    
    /**
     * 测试 KeyEvent 创建
     */
    public static void testKeyEventCreation() {
        try {
            Component testComponent = new Canvas();
            
            ConsoleUtil.println("[KeyEvent工厂] 开始测试 KeyEvent 创建...");
            ConsoleUtil.println(getAvailableConstructors());
            
            // 测试6参数构造函数
            KeyEvent event6 = createKeyEvent(testComponent, KeyEvent.KEY_PRESSED, 
                System.currentTimeMillis(), 0, KeyEvent.VK_A, 'a');
            if (event6 != null) {
                ConsoleUtil.println("[KeyEvent工厂] ✓ 6参数 KeyEvent 创建成功");
            } else {
                ConsoleUtil.println("[KeyEvent工厂] ✗ 6参数 KeyEvent 创建失败");
            }
            
            // 测试7参数构造函数
            KeyEvent event7 = createKeyEvent(testComponent, KeyEvent.KEY_PRESSED, 
                System.currentTimeMillis(), 0, KeyEvent.VK_A, 'a', KeyEvent.KEY_LOCATION_STANDARD);
            if (event7 != null) {
                ConsoleUtil.println("[KeyEvent工厂] ✓ 7参数 KeyEvent 创建成功");
            } else {
                ConsoleUtil.println("[KeyEvent工厂] ✗ 7参数 KeyEvent 创建失败");
            }
            
            ConsoleUtil.println("[KeyEvent工厂] KeyEvent 创建测试完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[KeyEvent工厂] KeyEvent 创建测试失败: " + e.getMessage());
        }
    }
}
