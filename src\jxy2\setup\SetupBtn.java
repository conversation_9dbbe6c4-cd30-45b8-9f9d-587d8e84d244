package jxy2.setup;

import com.tool.btn.MoBanBtn;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class SetupBtn extends MoBanBtn {
    public int typeBtn;//提示
    public SetupMainJPanel setupMainJPanel;
    public SetupBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, SetupMainJPanel setupMainJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.setupMainJPanel = setupMainJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        setupMainJPanel.setIndex(typeBtn);
        setupMainJPanel.getSetupCardJPanel().getCardLayout().show(setupMainJPanel.getSetupCardJPanel(),"l"+(typeBtn+1));
        handleButtonClick(typeBtn);
    }
    public void handleButtonClick(int clickedIndex) {
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        setupMainJPanel.getSetupBtns()[clickedIndex].btnchange(2);
        for (int i = 0; i <setupMainJPanel.getSetupBtns().length; i++) {
            if (i != clickedIndex) {
                setupMainJPanel.getSetupBtns()[i].btnchange(0);

            }
        }
    }
}
