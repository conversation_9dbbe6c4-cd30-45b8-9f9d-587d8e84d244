package come.tool.handle;

import com.tool.image.ImageMixDeal;
import come.tool.Fighting.*;
import org.come.Frame.ZhuFrame;
import org.come.bean.RoleShow;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;

import java.util.ArrayList;
import java.util.List;

public class HandlePlay implements Handle {
	@Override
	public void handle(long DieTime) {

		// TODO Auto-generated method stub
		boolean end = true;
		Statelist statelist = FightingMixDeal.BattlefieldPlay.get(FightingMixDeal.CurrentRound);
		if (statelist==null) {
			FightingMixDeal.qingchusiwang();
			FightingMixDeal.changeState(HandleState.HANDLE_END);
			return;
		}
		if (statelist.getProgress() < statelist.getRound().size()) {
			if (statelist.getState() == null){
				statelist.setState(FightingMixDeal.AnalysisFightingEvents(statelist.getRound().get(statelist.getProgress()), statelist.getProgress()));			
			}
			// 判断是否执行结束
			List<StateProgress> list = statelist.getState();
			try {
				for (int i = list.size() - 1; i >= 0; i--) {// 先找到执行人的位置
					Fightingimage man = FightingMixDeal.CurrentData.get(list.get(i).getMan());
					if (man.zhixnig(list.get(i))){
						end = false;
					}
				}
				for (int i = FightingMixDeal.skills.size()-1;i>=0;i--) {
					if (FightingMixDeal.skills.get(i).getShadowMode()!=null) {
						end=false;
						break;
					}
				}
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
			if (end) {
				statelist.setProgress(statelist.getProgress() + 1);
				statelist.setState(null);
				FightingMixDeal.Music1 = "";
				List<Fightingimage> datas = null;
				for (int i = list.size() - 1; i >= 0; i--) {
					try {
						// 先找到执行人的位置
						Fightingimage mandata = FightingMixDeal.CurrentData.get(list.get(i).getMan());
						if (list.get(i).getEscape() == 1){
							if (datas==null)datas=new ArrayList<>();
							datas.add(mandata);
						}			
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				if (datas!=null) {
					for (int i = datas.size()-1; i >=0; i--) {
						if (OutFight(datas.get(i)))return;
					}	
				}
			}
		}
		if (statelist.getProgress() >= statelist.getRound().size()) {
			FightingMixDeal.qingchusiwang();
			FightingMixDeal.changeState(HandleState.HANDLE_END);
			FightingEvents fightingEvents = new FightingEvents();
			fightingEvents.setFightingsum(FightingMixDeal.FightingNumber);
			fightingEvents.setCurrentRound(FightingMixDeal.CurrentRound);
			String sendMes = Agreement.FightingRoundEndAgreement(GsonUtil.getGsonUtil().getgson().toJson(fightingEvents));
			SendMessageUntil.toServer(sendMes);
		}else if (end&&FightingMixDeal.State==HandleState.HANDLE_PLAY) {
			handle(0);
		}
	}
	/**退出战斗处理*/
	public boolean OutFight(Fightingimage mandata){
		ZhuFrame.getZhuJpanel().hideMap(true);
		ZhuFrame.getZhuJpanel().outFighting();
		int type = mandata.getFightingManData().getType();
		if (type == 0&& mandata.getFightingManData().getManname().equals(ImageMixDeal.userimg.getRoleShow().getRolename())) {
			try {
				jieli();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			FightingMixDeal.FightingEnd(null);
			return true;
		} else {
			int camp = mandata.getFightingManData().getCamp();
			int man = mandata.getFightingManData().getMan();
			if (type == 0) {
				FightingMixDeal.depath(camp, man);
				FightingMixDeal.depath(camp, man + 5);
				FightingMixDeal.depath(camp, man + 10);
				FightingMixDeal.depath(camp, man + 15);
			} else {
				FightingMixDeal.depath(camp, man);
			}
		}
		return false;
	}
	/**
	 * 解散和离开队伍
	 * @throws Exception 
	 */
    public void jieli() throws Exception{
    	RoleShow roleShow=ImageMixDeal.userimg.getRoleShow();
    	if (roleShow.getTroop_id()==null)return;
    	if (ImageMixDeal.userimg.getTeams()!=null&&roleShow.getCaptian()!=1) {return;}
    	String sendmes = Agreement.getAgreement().team5Agreement("D");
        SendMessageUntil.toServer(sendmes);
     	FormsManagement.HideForm(13);
	}
}
