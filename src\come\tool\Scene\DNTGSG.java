package come.tool.Scene;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import org.come.until.ScrenceUntil;
import org.come.until.Util;

import com.tool.tcpimg.UIUtils;

public class DNTGSG {

	private long endTime;//倒计时结束时间
	private long time;
	private String msg;
	private Image image;//底板
	private Image image2;//进度条
	private int max;//上限
	private int value0;//天庭进度值
	private int value1;//花果山进度值
	private int length0;//天庭进度条长度
	private int length1;//花果山进度条长度
	private String msg0;//天庭进度描述
	private String msg1;//花果山进度描述
	public DNTGSG() {
		// TODO Auto-generated constructor stub
		this.image=new ImageIcon("inkImg/background/S20.png").getImage();
		this.image2=new ImageIcon("inkImg/background/S21.png").getImage();
		this.endTime=System.currentTimeMillis()+30*60*1000;
		this.length0=0;
		this.length1=0;
		this.msg ="";
		this.msg0="";
		this.msg1="";
	}
//	S0天庭进度|S1花果山进度|S2结束时间&进度上限
	public void upSG(String msg){
		String[] vs=msg.split("&");
		this.endTime=Long.parseLong(vs[0]);
		this.max  =Integer.parseInt(vs[1]);
		upValue(0,value0);
		upValue(1,value1);
		
	}
    public void upValue(int camp,int value){
		if (camp==0) {
			
			value0 =value;
			length0=image2.getWidth(null)*value/max;
			msg0   =value+"/"+max;
		}else {
			value1 =value;
			length1=image2.getWidth(null)*value/max;
			msg1   =value+"/"+max;
		}
	}
	public void draw(Graphics g) {
		long cha=endTime-Util.getTime();
		if (cha>0) {
			int x=(ScrenceUntil.Screen_x-image.getWidth(null))/2;
			g.drawImage(image,x,50,null);
			cha/=1000;
			if (time!=cha) {
				time=cha;
				msg=cha/60+":"+cha%60;
			}
			g.setColor(Color.RED);
			g.setFont(UIUtils.TEXT_HY19);
			g.drawString(msg,x+5,80);
			
			g.setColor(Color.white);
			g.setFont(UIUtils.TEXT_FONT);
			
			g.drawString(msg0, x+160,68);
			g.drawImage(image2,x+100,68,length0,2,null);
		
			g.drawString(msg1, x+160,87);
			g.drawImage(image2,x+100,87,length1,2,null);
		}
	}
}
