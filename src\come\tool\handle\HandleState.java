package come.tool.handle;

/**
 * 战斗状态
 * 
 * <AUTHOR>
 * 
 */
public class HandleState {
	/** 0 非战斗状态 */
	public static final int USUAL = 0;
	/** 1 玩家操作 */
	public static final int HANDLE_PLAYER = 1;
	/** 2 召唤兽操作 */
	public static final int HANDLE_PET = 2;
	/** 3 等待指令接收 */
	public static final int HANDLE_WAIT = 3;
	/** 4 等待战斗处理 */
	public static final int HANDLE_BATTLE = 4;
	/** 5 战斗播放 */
	public static final int HANDLE_PLAY = 5;
	/** 6 战斗播放结束同步 */
	public static final int HANDLE_END = 6;

}