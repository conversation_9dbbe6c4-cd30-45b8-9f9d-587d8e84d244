package come.tool.JDialog;

import org.come.Frame.OptionsJframe;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.UserMessUntil;

public class BatchitmeJDialog implements TiShiChuLi{
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            int sum = Integer.parseInt(OptionsJframe.getOptionsJframe().getOptionsJpanel().getTextCode().getText());
            Goodstable goodstable = (Goodstable) object;
            if (sum>goodstable.getUsetime()){sum = goodstable.getUsetime();}
            String sendmes = Agreement.getAgreement().userpetAgreement(goodstable.getRgid()+"|"+UserMessUntil.getChosePetMes().getSid()+"|"+sum);
            SendMessageUntil.toServer(sendmes);
        }
    }
}
