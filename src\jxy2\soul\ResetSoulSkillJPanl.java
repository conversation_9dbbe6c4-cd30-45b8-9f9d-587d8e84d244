package jxy2.soul;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
* 重置魂技
* <AUTHOR>
* @date 2024/7/8 下午8:46
*/
public class ResetSoulSkillJPanl extends JPanel {
    public ResetSoulSkillBtn rese;
    private JScrollPane resetSkill;
    private JList<ResetModeSkills> resistanceList;
    private Map<Integer, ResetModeSkills> mapResistanceModelPanel = new HashMap<>();
    public JLabel goods_1,goods_2,mat_s,consumables,goodsSum;
    public int type =-1;//提示
    public int xz =0;//选中
    public Goodstable goodstable;
    public StringBuilder builder = new StringBuilder();
    public ResetSoulSkillJPanl() {
        this.setPreferredSize(new Dimension(230+249, 331));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        rese = new ResetSoulSkillBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "重 置", 1,this,"");
        rese.setBounds(376-37,263,59,24);
        add(rese);

        goodsSum = new JLabel();
        goodsSum.setForeground(Color.white);
        goodsSum.setFont(UIUtils.LiSu_LS13B);
        goodsSum.setVerticalTextPosition(SwingConstants.CENTER);
        goodsSum.setHorizontalTextPosition(SwingConstants.CENTER);
        goodsSum.setBounds(346,183-20,50,50);
        add(goodsSum);

        goods_1 = new JLabel();
        goods_1.setBounds(304,60,46,46);
        goods_1.addMouseListener(new ResetPromptMouse(1,this));
        add(goods_1);
        goods_2 = new JLabel();
        goods_2.setBounds(374,60,46,46);
        goods_2.addMouseListener(new ResetPromptMouse(2,this));
        add(goods_2);
        mat_s = new JLabel();
        add(mat_s);
        consumables = new JLabel();
        consumables.setBounds(343,183,50,50);
        consumables.addMouseListener(new ResetPromptMouse(0,this));
        add(consumables);



        getResetSkill();
        InitializeSkillList();
    }

    /**初始化技能列表*/
    public void InitializeSkillList(){
        if (UserMessUntil.getChosePetMes().getSkillSoul()==null)return;
        initialization();//清除初始化
        int index = 0;
        String golang = UserMessUntil.getChosePetMes().getSkillSoul();
        Goodstable goodslang = GoodsListFromServerUntil.fushis.get(new BigDecimal(golang));
        if (goodslang != null && goodslang.getType() == 8898) {
            ResetModeSkills resetModeSkills = new ResetModeSkills();
            resetModeSkills.setBounds(0, index * 55, 249, 55);
            resetModeSkills.initSkillInfo(goodslang);
            resetModeSkills.addMouseListener(new ResetSoulSkillMouse(index,resetModeSkills));
            resetModeSkills.frames.setBorder(BorderFactory.createLineBorder(Color.orange));
            resistanceList.add(resetModeSkills);
            mapResistanceModelPanel.put(0, resetModeSkills); // 使用一个特殊键值，比如 -1，来表示这是从 Map 中获取的元素
            consumables.setIcon(ResetSoulSkillMouse.GoodsName(goodslang.getGoodsname()));
            goodsSum.setText(ResetSoulSkillMouse.GoodsSum(goodslang.getGoodsname())+"/1");
            this.goodstable =goodslang;
            index++;
        }
        Goodstable[] goodstable = GoodsListFromServerUntil.getGoodslist();
        for (int i = 0; i < goodstable.length; i++) {
            if (goodstable[i] == null) {continue;}
            if (goodstable[i].getType()==8898){
                Goodstable goods = goodstable[i];
                if (goods!=null){
                    ResetModeSkills resetModeSkills = new ResetModeSkills();
                    resetModeSkills.setBounds(0, index * 55, 249, 55);
                    resetModeSkills.initSkillInfo(goods);
                    resetModeSkills.addMouseListener(new ResetSoulSkillMouse(index,resetModeSkills));
                    resistanceList.add(resetModeSkills);
                    mapResistanceModelPanel.put(index,resetModeSkills);
                    index++;
                }
            }
        }
        resistanceList.setPreferredSize(new Dimension(249, index * 55));
        Refresh();
    }

    private void initialization() {
        resistanceList.removeAll();
        mapResistanceModelPanel.clear();
        goods_1.setIcon(null);
        goods_2.setIcon(null);
    }

    public void Refresh(){
        String[] golang = UserMessUntil.getChosePetMes().getSoul().split("\\|");
        for (int i = 0; i < golang.length; i++) {
            Skill skill = UserMessUntil.getSkillId(golang[i]);
            if (builder.length()!=0) {builder.append("|");}
            builder.append(skill.getSkillid());
            ImageIcon icon = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(), 44, 44, "skill.wdf");
            Image img = Juitil.toRoundedCornerImage(icon.getImage(), 15);
            // 设置 goods_1 图标
            if (i == 0) {
                if (goods_1.getIcon()==null) {
                    goods_1.setIcon(new ImageIcon(img));
                    goods_1.setVisible(true); // 确保 goods_1 可见
                    type = i;
                    goods_1.setBounds(305,61,44,44);
                }
            }
            // 设置 goods_2 图标，仅当 golang 有两个值时
            if (i == 1) {
                if (goods_2.getIcon()==null) {
                    goods_2.setIcon(new ImageIcon(img));
                    goods_2.setVisible(true); // 确保 goods_2 可见
                    type = i;
                    goods_2.setBounds(375,61,44,44);
                }
            }
        }
        // 如果 golang 只有一个值，隐藏 goods_2
        if (golang.length == 1) {
            goods_2.setVisible(false);
            type = -1;
            goods_1.setBounds(345,61,44,44);
        }

    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz103, 0, 0, 249, 331, 1);
        Juitil.ImngBack(g, Juitil.tz103, 249, 0, 230, 331, 1);

        for (int i = 0; i < 2; i++) {
            Juitil.ImngBack(g, Juitil.good_2, type==i?302:type==-1?342:372, 58, 50, 50, 1);
        }
        Juitil.ImngBack(g, Juitil.good_2, 379-37, 180, 50, 50, 1);
        Juitil.Subtitledrawing(g, 372-37, 24, "魂技技能", Color.WHITE, UIUtils.MSYH_HY16,1);
        Juitil.Subtitledrawing(g, 372-37, 165, "重置材料", Color.WHITE, UIUtils.MSYH_HY16,1);
        Juitil.Subtitledrawing(g, 273, 323, "重置不会改变魂技拥有的技能数", Color.cyan, UIUtils.TEXT_FONT2,0);
        g.drawImage(Juitil.icon.getImage(), 305-37, 35, 200, 2, null);
        g.drawImage(Juitil.icon.getImage(), 305-37, 140, 200, 2, null);
        Juitil.Textdrawing(g,"符",100,348,UIUtils.COLOR_value,UIUtils.MSYH_HY13);



    }



    public JScrollPane getResetSkill() {
        //宠物技能展示
        if (resetSkill == null) {
            resetSkill = new JScrollPane(getResistanceList());
            resetSkill.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            resetSkill.getVerticalScrollBar().setUI(null);
            resetSkill.getVerticalScrollBar().setUnitIncrement(20);
            resetSkill.getViewport().setOpaque(false);
            resetSkill.setOpaque(false);
            resetSkill.setBounds(0, 0, 249, 325);
            resetSkill.setBorder(BorderFactory.createEmptyBorder());
            resetSkill.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            this.add(resetSkill);
        }
        return resetSkill;
    }

    public JList<ResetModeSkills> getResistanceList() {
        if (resistanceList==null){
            resistanceList = new JList<>();
            resistanceList.setSelectionBackground(new Color(122, 117, 112));
            resistanceList.setSelectionForeground(Color.white);
            resistanceList.setForeground(Color.white);
            resistanceList.setFont(UIUtils.TEXT_HY16);
            resistanceList.removeAll();
            resistanceList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
            DefaultListCellRenderer cellRenderer = new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(javax.swing.JList<?> list, Object value, int index,
                                                              boolean isSelected, boolean cellHasFocus) {
                    super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                    return this;
                }
            };
            resistanceList.setCellRenderer(cellRenderer);
            resistanceList.setOpaque(false);
        }
        return resistanceList;
    }

    public Map<Integer, ResetModeSkills> getMapResistanceModelPanel() {
        return mapResistanceModelPanel;
    }

    public void setMapResistanceModelPanel(Map<Integer, ResetModeSkills> mapResistanceModelPanel) {
        this.mapResistanceModelPanel = mapResistanceModelPanel;
    }
}
