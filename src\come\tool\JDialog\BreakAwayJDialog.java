package come.tool.JDialog;

import java.math.BigDecimal;

import org.come.bean.LoginResult;
import org.come.bean.RoleShow;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;

/**
 * 脱离帮派
 * 
 * <AUTHOR>
 */
public class BreakAwayJDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {
        // TODO Auto-generated method stub
        if (tishi) {
            try {
                RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
                LoginResult loginResult = RoleData.getRoleData().getLoginResult();
                // 存帮派id
                loginResult.setGang_id(new BigDecimal(0));
                // 存帮派名称
                loginResult.setGangname("");
                // 存帮派职务
                loginResult.setGangpost("");
                // 存帮派成就
                loginResult.setAchievement(new BigDecimal(0));
                // 清帮派抗性
                loginResult.setResistance("主-|副-");
                roleShow.setGang_id(loginResult.getGang_id());
                roleShow.setGangname(loginResult.getGangname());
                RoleProperty.ResetEw();
                // 复制写给客户端的流
                String sendMes = Agreement.GangRetreatAgreement("");
                // 向服务器发送信息
                SendMessageUntil.toServer(sendMes);
            } catch (Exception e2) {
                // TODO: handle exception
            }
            FormsManagement.HideForm(48);
        }
    }
}
