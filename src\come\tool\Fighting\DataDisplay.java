package come.tool.Fighting;

import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
import com.tool.tcpimg.UIUtils;

import java.awt.*;

public class DataDisplay {
    static int TILIU=1300;
    //tcp路径
    private String tcp;
	private Color tsc;
	private String ts;
	private String hpbig;
	private String hpsmall;
	private Color hpc;
	private String mpbig;
	private String mpsmall;
	private Color mpc;
	private long time;
	public DataDisplay() {
		// TODO Auto-generated constructor stub
	}
	//致命 怨气 吸收 怒气 狂暴  4倍 3倍
	public DataDisplay(String ts, int hp, int mp) {
		super();
		if (ts!=null) {
			if (ts.equals("致命")) {
				tcp=GetTcpPath.getMouseTcp("ztzm");
			}else if (ts.equals("怨气")) {
				tcp=GetTcpPath.getMouseTcp("ztyq");
			}else if (ts.equals("吸收")) {
				tcp=GetTcpPath.getMouseTcp("ztxs");
			}else if (ts.equals("怒气")) {
				tcp=GetTcpPath.getMouseTcp("ztnq");
			}else if (ts.equals("狂暴")) {
				tcp=GetTcpPath.getMouseTcp("ztkb");
			}else if (ts.equals("4倍")) {
				tcp=GetTcpPath.getMouseTcp("zt4b");
			}else if (ts.equals("3倍")) {
				tcp=GetTcpPath.getMouseTcp("zt3b");
			}else {
				this.ts = ts;
				tsc=Color.YELLOW;
			}
		}
		if (hp!=0) {
			String values = null;
			if (hp>0) {
				values="+"+hp;
				hpc=Color.GREEN;
			}else {
				values=hp+"";
				hpc=UIUtils.COLOR_HURTR1;
			}
			if(values.length()>=6){
				hpbig =   values.substring(0, values.length()-4);
				hpsmall = values.substring(values.length()-4,values.length());
			}else{
				hpsmall = values;
			}
		}
		if (mp!=0) {
			String values = null;
			if (hp>0) {
				values="+"+mp;
			}else {
				values=mp+"";
			}
			mpc=Color.BLUE;
			if(values.length()>=6){
				mpbig =   values.substring(0, values.length()-4);
				mpsmall = values.substring(values.length()-4,values.length());
			}else{
				mpsmall = values;
			}
		}
		this.time=0;
	}
	public String ZM="致命";
	//狂暴黄色   致命蓝色 血量红色  蓝量蓝色
	public boolean draw(Graphics g,int x,int y,int camp,long dietime){
		Graphics2D g2d = (Graphics2D) g.create();
		g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
		time+=dietime;
		if (time>TILIU)return true;
		int sum=(int) (time/15);
		y-=sum;
		if (tsc!=null) {
			int row = x-ts.length()*16-5;
			//画文字
			g2d.setFont(UIUtils.FZCY_HY15);
			g2d.setColor(UIUtils.COLOR_HURTB2);
			g2d.drawString(ts, row-1, y);
			g2d.drawString(ts, row+1, y);
			g2d.drawString(ts, row, y-1);
			g2d.drawString(ts, row, y+1);
			g2d.setColor(tsc);
			g2d.drawString(ts, row, y);
		}
		if (tcp!=null) {
			Sprite sprite=SpriteFactory.Prepare(tcp);
			if (sprite!=null) {
				sprite.updateToTime(0, 0);
				sprite.draw(g, x-40, y-15);
			}
		}
		if (hpc!=null) {
			int row1 = x;
			if (hpbig!=null) {
				g2d.setFont(UIUtils.FZCY_HY22);
				g2d.setColor(Color.BLACK);
				g2d.drawString(hpbig, x-1, y);
				g2d.drawString(hpbig, x+1, y);
				g2d.drawString(hpbig, x, y-1);
				g2d.drawString(hpbig, x, y+1);
				g2d.setColor(hpc);
				g2d.drawString(hpbig, x, y);
				row1+=hpbig.length()*10;
			}
			g2d.setFont(UIUtils.FZCY_HY15);
			g2d.setColor(Color.BLACK);
			g2d.drawString(hpsmall, row1-1, y);
			g2d.drawString(hpsmall, row1+1, y);
			g2d.drawString(hpsmall, row1, y-1);
			g2d.drawString(hpsmall, row1, y+1);
			g2d.setColor(hpc);
			g2d.drawString(hpsmall, row1, y);
		}
		y-=15;
        if (mpc!=null&&camp==FightingMixDeal.camp) {
        	int row1 = x;
			if (mpbig!=null) {
				g2d.setFont(UIUtils.FZCY_HY22);
				g2d.setColor(Color.BLACK);
				g2d.drawString(mpbig, x-1, y);
				g2d.drawString(mpbig, x+1, y);
				g2d.drawString(mpbig, x, y-1);
				g2d.drawString(mpbig, x, y+1);
				g2d.setColor(mpc);
				g2d.drawString(mpbig, x, y);
				row1+=mpbig.length()*10;
			}
			g2d.setFont(UIUtils.FZCY_HY15);
			g2d.setColor(Color.BLACK);
			g2d.drawString(mpsmall, row1-1, y);
			g2d.drawString(mpsmall, row1+1, y);
			g2d.drawString(mpsmall, row1, y-1);
			g2d.drawString(mpsmall, row1, y+1);
			g2d.setColor(mpc);
			g2d.drawString(mpsmall, row1, y);
		}
		return false;
	}
	
	public String getTs() {
		return ts;
	}

	public void setTs(String ts) {
		this.ts = ts;
	}

	public long getTime() {
		return time;
	}

	public void setTime(long time) {
		this.time = time;
	}
    
	
}
