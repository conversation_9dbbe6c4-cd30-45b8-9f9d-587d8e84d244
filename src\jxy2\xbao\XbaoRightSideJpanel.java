package jxy2.xbao;

import com.tool.tcpimg.ChatBox;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.Jpanel.TeststateJpanel;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.SrcollPanelUI;

import javax.swing.*;
import java.awt.*;

public class XbaoRightSideJpanel extends JPanel {//Details
    private JLabel xzimg,xzname,namebackimg;//选中主图标
    private JLabel[] textTitle = new JLabel[60];
    private JLabel[] xzfouricon = new JLabel[4];//4个选中图标
    private JLabel[] Storecolors = new JLabel[4];//存放4种颜色
    private JLabel[] underline = new JLabel[4];//下划线。
    private JLabel liteicon;//右上角图形
    private JLabel[] colorsicon = new JLabel[4];//颜色图形
    private JScrollPane jScrollPane;
    public ChatBox chatbox = new ChatBox();
    private XbaoRightSideDetails xbaoRightSideDetails = new XbaoRightSideDetails();
    public XbaoRightSideJpanel() {
        this.setPreferredSize(new Dimension(340,420));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        getXzimg();
        getXzname();
        getTextTitle();
        getUnderline();
        getjScrollPane();
    }




    /**装载数据对象*/
    public void LoadingData(Xuanbao xuanbao) {
        if (xuanbao==null)return;
        XbaoJpanel xbaoJpanel = XbaoFrame.getXbaoFrame().getXbaoJpanel();
        xzimg.setIcon(GoodsListFromServerUntil.xbaoWdfile(xuanbao.getBaoskin(), 168, 168));
        xzname.setText(xuanbao.getBaoname());
        boolean isCommon = xuanbao.getBaorace().equals("通用"); int index  = 0;
        xbaoRightSideDetails.getBox().removemsg();
        xbaoRightSideDetails.getBox2().removemsg();
        xbaoRightSideDetails.getBox3().removemsg();
        for (int i = 0; i < xbaoRightSideDetails.getLabimgadd().length; i++) {
            xbaoRightSideDetails.getLabimgadd()[i].setIcon(null);
        }
        for (int i = 0; i < 6; i++) {
            xbaoRightSideDetails.getSkillimg()[i].setIcon(null);
        }
        for (int i = 0; i < 3; i++) {
            xbaoRightSideDetails.getChatBoxBackImg()[i].setIcon(null);
        }
        // 设置门派相关信息
        String sects;
        int h =0;
        if (isCommon) {
            sects = "【所属门派】全种族"+xuanbao.getBaorace();
        } else {
            sects = "【所属门派】"+xuanbao.getBaorace();
        }
        String deacribe =  xuanbao.getBaodescribe().split("&")[1];


        xbaoRightSideDetails.getBox().addText("#c5c462c【玄宝类型】"+xuanbao.getBaotype()+"型#r"+sects,300,UIUtils.TEXT_FONT15);
         h = xbaoRightSideDetails.getBox().getHeight();
        xbaoRightSideDetails.getLabimg()[0].setBounds(15,h+4,292,1);
        xbaoRightSideDetails.getBox().addText(" #r#c5c462c【玄宝技能】#r",300,UIUtils.TEXT_FONT15);
        StringBuffer buffer = new StringBuffer();
        buffer.append("【消耗】10玄元#r");
        buffer.append("【冷却回合】10#r");
        buffer.append(deacribe);
        buffer.append("#r");
        buffer.append(xuanbao.getBaoHav());
        xbaoRightSideDetails.getBox().addText("#c5c462c"+buffer,300,UIUtils.TEXT_FONT2);
        h = xbaoRightSideDetails.getBox().getHeight();
        xbaoRightSideDetails.getLabimg()[1].setBounds(15,h+4,292,1);
        //玄印配置
        xbaoRightSideDetails.getBox().addText(" #r#c5c462c【玄印配置】#r",300,UIUtils.TEXT_FONT15);
        h = xbaoRightSideDetails.getBox().getHeight();


        String[] xyConfig = xuanbao.getXyConfig().split("\\|");
        for (int i = 0; i < xyConfig.length; i++) {
            xbaoRightSideDetails.getLabimgadd()[i].setIcon(Juitil.tz378);
            xbaoRightSideDetails.getLabimgadd()[i].setBounds(15,(h-6)+i*21,20,20);
            xbaoRightSideDetails.getBox().addText("#c5c462c   玄印槽" + (i + 1) + " 可镶嵌" + MsgJframe.getJframe().getJapnel().getTextCursor(xyConfig[i]) + "#c5c462c玄印", 300, UIUtils.TEXT_FONT2);
        }

        h = xbaoRightSideDetails.getBox().getHeight();
        xbaoRightSideDetails.getLabimg()[2].setBounds(15,h+4,292,1);
        xbaoRightSideDetails.getBox().addText(" #r#c5c462c【玄印技能】#r",300,UIUtils.TEXT_FONT15);

        XuanMsgFrame.getXuanMsgFrame().getXuanMsgJPanel().ObtainImagesBasedOnColor(
                xuanbao.getBaoskill().split("&")[0].split(","),
                xuanbao.getBaoskillone().split("&")[0].split(","),
                xuanbao.getBaoskilltwo().split("&")[0].split(","),xbaoRightSideDetails.getSkillimg()
        );
        // 计算实际的技能组数和文本内容
        int skillGroupCount = 0;
        String[] skillTexts = new String[3];
        if (xuanbao.getBaoskill() != null && !xuanbao.getBaoskill().isEmpty()) {
            skillTexts[skillGroupCount] = xuanbao.getBaoskill().split("&")[1];
            skillGroupCount++;
        }
        if (xuanbao.getBaoskillone() != null && !xuanbao.getBaoskillone().isEmpty()) {
            skillTexts[skillGroupCount] = xuanbao.getBaoskillone().split("&")[1];
            skillGroupCount++;
        }
        if (xuanbao.getBaoskilltwo() != null && !xuanbao.getBaoskilltwo().isEmpty()){
            skillTexts[skillGroupCount] = xuanbao.getBaoskilltwo().split("&")[1];
            skillGroupCount++;
        }

        // 为每个技能文本计算位置
        int currentY = h+37; // 起始Y坐标
        int textHeight;
        for (int i = 0; i < skillGroupCount; i++) {
            if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                // 设置chatBoxBackImg的位置在当前文本开始处
                xbaoRightSideDetails.getChatBoxBackImg()[i].setIcon(Juitil.tz373);
                xbaoRightSideDetails.getChatBoxBackImg()[i].setBounds(15, currentY, 56, 16);
                // 计算当前文本的渲染高度
                textHeight = xbaoJpanel.calculateTextHeight(skillTexts[i], 240, UIUtils.TEXT_FONT2);
                // 更新下一个文本的Y坐标
                currentY += textHeight-4; // 4像素间距
            }
        }

        // 最后设置主ChatBox的内容（合并所有文本）
        StringBuilder combinedText = new StringBuilder();
        for (int i = 0; i < skillGroupCount; i++) {
            if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                if (combinedText.length() > 0) {
                    combinedText.append("#r"); // 换行标记
                }
                combinedText.append("#c5c462c").append(skillTexts[i]);
            }
        }
        if (combinedText.length() > 0) {
            xbaoRightSideDetails.getBox2().addText(combinedText.toString(), 240 , UIUtils.TEXT_FONT2);
        }
        for (int i = 0; i < 6; i++) {
            int row = i % 2 * 15;
            int baseY = xbaoRightSideDetails.getChatBoxBackImg()[i/2].getY(); // 获取对应chatBoxBackImg的Y坐标
            xbaoRightSideDetails.getSkillimg()[i].setBounds(20 + row, baseY + 2, 12, 12); // +2是微调，使图标垂直居中对齐
        }

        h = xbaoRightSideDetails.getBox2().getHeight()+xbaoRightSideDetails.getBox().getHeight();

        xbaoRightSideDetails.getLabimg()[3].setBounds(15,h+4,292,1);
        xbaoRightSideDetails.getBox3().addText(" #r#c5c462c【玄宝历史】#r",300,UIUtils.TEXT_FONT15);
        xbaoRightSideDetails.getBox3().addText("#c5c462c"+xuanbao.getBaoraceinfo(),290,UIUtils.TEXT_FONT2);
        h = xbaoRightSideDetails.getBox2().getHeight()+xbaoRightSideDetails.getBox().getHeight()+xbaoRightSideDetails.getBox3().getHeight();

        xbaoRightSideDetails.getLabimg()[4].setBounds(15,h-5,292,1);

        xbaoRightSideDetails.setPreferredSize(new Dimension(300, xbaoRightSideDetails.getBox().getHeight()+xbaoRightSideDetails.getBox2().getHeight()+xbaoRightSideDetails.getBox3().getHeight()));
        jScrollPane.getViewport().setViewSize(new Dimension(300, xbaoRightSideDetails.getBox().getHeight()+xbaoRightSideDetails.getBox2().getHeight()+xbaoRightSideDetails.getBox3().getHeight()));
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.tz376.getImage(),24,14,24,108,null);
        Juitil.Txtpet(g, 26, 48, xzname.getText(),UIUtils.COLOR_SHS_2, UIUtils.NEWTX_HY19B);
        g.drawImage(Juitil.tz379.getImage(),10,370,163,21,null);
        g.drawImage(Juitil.tz381.getImage(),314,173,3,193,null);


    }



    public JLabel getXzimg() {
        if (xzimg == null){
            xzimg = new JLabel();
            xzimg.setBounds(84, 2, 168, 168);
            add(xzimg);
        }
        return xzimg;
    }

    public void setXzimg(JLabel xzimg) {
        this.xzimg = xzimg;
    }

    public XbaoRightSideDetails getXbaoRightSideDetails() {
        return xbaoRightSideDetails;
    }

    public void setXbaoRightSideDetails(XbaoRightSideDetails xbaoRightSideDetails) {
        this.xbaoRightSideDetails = xbaoRightSideDetails;
    }

    public JLabel getXzname() {
        if (xzname == null){
            xzname = new JLabel();
            xzname.setVisible(false);
            add(xzname);
        }
        return xzname;
    }

    public void setXzname(JLabel xzname) {
        this.xzname = xzname;
    }

    public JLabel[] getXzfouricon() {
        return xzfouricon;
    }

    public void setXzfouricon(JLabel[] xzfouricon) {
        this.xzfouricon = xzfouricon;
    }

    public JLabel getLiteicon() {
        return liteicon;
    }

    public void setLiteicon(JLabel liteicon) {
        this.liteicon = liteicon;
    }

    public JLabel[] getColorsicon() {
        return colorsicon;
    }

    public void setColorsicon(JLabel[] colorsicon) {
        this.colorsicon = colorsicon;
    }

    public JLabel getNamebackimg() {
        if (namebackimg == null){
            namebackimg = new JLabel();
            namebackimg.setBounds(0, 0, 24, 108);
            namebackimg.setIcon(Juitil.tz376);
            add(namebackimg);
        }
        return namebackimg;
    }

    public void setNamebackimg(JLabel namebackimg) {
        this.namebackimg = namebackimg;
    }

    public JScrollPane getjScrollPane() {
        if (jScrollPane==null) {
            jScrollPane = new JScrollPane(xbaoRightSideDetails);
            jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
            jScrollPane.getVerticalScrollBar().setUnitIncrement(15);
            jScrollPane.setOpaque(false);
            jScrollPane.getViewport().setOpaque(false);
            jScrollPane.setBounds(0, 175, 330, 190);
            jScrollPane.setBorder(BorderFactory.createEmptyBorder());
            jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            this.add(jScrollPane);

        }
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }


    public JLabel[] getTextTitle() {
            for (int i = 0; i < 60; i++) {
              if (textTitle[i] == null){
                  textTitle[i] = TeststateJpanel.GJpanelText(new Color(92,70,44),UIUtils.TEXT_FONT15);
                  textTitle[i].setVisible(false);
                  textTitle[i].setBounds(40, 34, 300, 200);
                  this.add(textTitle[i]);
            }
        }
        return textTitle;
    }

    public void setTextTitle(JLabel[] textTitle) {
        this.textTitle = textTitle;
    }

    public JLabel[] getUnderline() {
        for (int i = 0; i < 4; i++) {
            underline[i] = new JLabel();
            underline[i].setBounds(40, 34 + i * 20, 292, 1);
            underline[i].setVisible(false);
            underline[i].setIcon(Juitil.tz377);
            add(underline[i]);
        }
        return underline;
    }

    public void setUnderline(JLabel[] underline) {
        this.underline = underline;
    }
}
