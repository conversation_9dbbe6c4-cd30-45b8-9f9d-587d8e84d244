package jxy2;

import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
import com.tool.tcp.SpriteHead;
import jxy2.jutnil.CheckTimer;
import jxy2.jutnil.StreamTimer;
import jxy2.wdfDome.bean.ImageInfo;
import jxy2.wdfDome.bean.WasData;
import jxy2.wdfDome.bean.WasHead;
import jxy2.wdfDome.bean.WdfHead;
import jxy2.wdfDome.main.WdfMianUtil;
import jxy2.wdfDome.util.DataTool;
import jxy2.wdfDome.util.WasTool;
import jxy2.wdfDome.util.WdfTool;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.lang.ref.SoftReference;
import java.util.*;
import java.util.concurrent.*;

/**
* WdfLoaDing
* <AUTHOR>
* @date 2024/4/22 下午9:06
*/

public class WdfLoaDing {
    /**
     * 生成带有特定水印的图片。
     * @param name 图片名称。
     * @return 返回带有水印的图片。
     */
    public static BufferedImage wfduiimg(String name, String wdfname) {
        if (isValidWdfFile(wdfname)) {
            WdfHead matchingWdfHead = findMatchingWdfHeadParallel(wdfname);
            if (matchingWdfHead != null) {
                WasData matchingWasData = spriteCache.get(name);
                if (matchingWasData != null) {
                    WasHead wasHead = getWasHeadForWasData(matchingWasData,wdfname);
                    if (wasHead != null) {
                        return WasTool.was2Imagess(wasHead);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据提供的名称和WDF文件名动态创建一个Sprite对象。
     * @param name 表示Sprite的名称。
     * @param wdfname 表示与该Sprite相关的WDF文件的名称。
     * @return 返回一个根据指定名称和WDF文件名创建的Sprite对象。
     */
    public static Sprite dynamic(String name, String wdfname) {
        // 创建Sprite的逻辑
        if (isValidWdfFile(wdfname)) {
            WdfHead matchingWdfHead = findMatchingWdfHeadParallel(wdfname);
            if (matchingWdfHead != null) {
                WasData matchingWasData = spriteCache.get(name);
                if (matchingWasData != null) {
                    return getWasHeadForWasDatanew(matchingWasData,wdfname);
                }
            }
        }
        return null;
    }



    /**
     * 根据提供的名称和WDF文件名动态创建一个Sprite对象。
     * @param name 表示Sprite的名称。
     * @param wdfname 表示与该Sprite相关的WDF文件的名称。
     * @return 返回一个根据指定名称和WDF文件名创建的Sprite对象。
     * 加载单个文集大小超过10MB内存就会存在问题！！！
     * 数据库链接 DatabaseUtil.java
     *
     */
    public static Sprite newdynamic(String name, String wdfname) {
        if (isValidWdfFile(wdfname)) {
                //先找是否存在文件夹
                WasData wasData = spriteCache.get(name);
                if (wasData != null) {
                    return convertWasDataToSprite(wdfname,wasData);
                }
        }
        System.out.println("没有找到WDF文件：" + wdfname);
        return null;
    }
    // 使用软引用缓存 Sprite 对象
    public static Map<String, SoftReference<Sprite>> spriteCaches = new HashMap<>();

  private static Sprite convertWasDataToSprite(String wdfname, WasData wasData) {
      String wasname = DataTool.ten2six(String.valueOf(wasData.getId()));
      // 检查缓存中是否有该 sprite
      SoftReference<Sprite> ref = spriteCaches.get(wasname);
      if (ref != null) {
          Sprite cachedSprite = ref.get();
          if (cachedSprite != null) {
              return cachedSprite;  // 如果缓存中有且未被回收，直接返回
          }
      }
      Sprite sprite = null;
      File wdfFile = getFileByName(wdfname);
      if (wdfFile == null) {
          System.out.println("无法找到WDF文件: " + wdfname);
          return null;
      }
      try (RandomAccessFile in = new RandomAccessFile(wdfFile, "r")) {
          in.seek(wasData.getFileOffset());
          byte[] headerBuf = new byte[2];
          in.read(headerBuf);
          String fileTag = new String(headerBuf);
          int version = fileTag.equals(SpriteFactory.WAS_FILE_TAG) ? 0 : fileTag.equals(SpriteFactory.WAS_FILE_TAGTWO) ? 1 : -1;
          if (version >= 0) {
              int fileSize = (int) wasData.getFileSize();
              byte[] data = new byte[fileSize];
              int bytesRead;
              int totalBytesRead = 0;
              byte[] buffer = new byte[65535]; // 使用固定大小的缓冲区
              while (totalBytesRead < fileSize && (bytesRead = in.read(buffer)) != -1) {
                  int bytesToCopy = Math.min(bytesRead, fileSize - totalBytesRead);
                  System.arraycopy(buffer, 0, data, totalBytesRead, bytesToCopy);
                  totalBytesRead += bytesToCopy;
              }
              SpriteHead spriteHead = new SpriteHead(data);
              sprite = spriteHead.init(null, false, version);
              // 将新加载的 sprite 加入缓存（软引用）
              spriteCaches.put(wasname, new SoftReference<>(sprite));
              spriteHead.close();
              CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, new ByteArrayInputStream(data)));
              sprite.removeHead();
              return sprite;
          }

      } catch (IOException e) {
          throw new RuntimeException(e);
      }
      return sprite;
  }



    public static Map<String, WasData> spriteCache = new HashMap<>();
    /*初始化新线程来加载wdf动画*/
    static {
                loadShape1Wdf();

    }
    /**
     * 初始化Was文件数据。
     */
    private static void loadShape1Wdf() {
        for (File file : WdfMianUtil.wdfFiles) {
            if (file.isFile()) {
                WdfHead wdfHead = findMatchingWdfHeadParallel(file.getName());
                List<WasData> wasDataList = findMatchingWasDatas(wdfHead);
                    for (WasData wasData : wasDataList) {
                        String wasname  = DataTool.ten2six(String.valueOf(wasData.getId()));
                        spriteCache.put(wasname,wasData);
                    }
            }
        }
    }


    /**
     * WDF解析PNG图像信息。
     * @param name 表示图像信息的名称。
     * @return 返回一个包含指定信息的BufferedImage对象。Failed to finalize decryption
     */
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5); // Thread pool for loading images
    public static Future<BufferedImage> information(String name, String wdfname) {

        return executorService.submit(() -> {
            try {
                if (isValidWdfFile(wdfname)) {
                    WdfHead matchingWdfHead = findMatchingWdfHeadParallel(wdfname);
                    if (matchingWdfHead != null) {

                        WasData matchingWasData = spriteCache.get(name);
                        if (matchingWasData != null) {
                            ImageInfo imageInfo = new ImageInfo(getFileByName(wdfname), matchingWasData.getFileOffset());
                            // 异步加载图像
                            Future<BufferedImage> futureImage = WasTool.loadCachedImage(imageInfo);
                            // 可以在这里或稍后使用 futureImage.get() 来获取加载的图像
                            BufferedImage image = futureImage.get(); // 获取加载结果
                            CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, image));
                            return image;
                        }
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return null;
        });
    }

    private static File getFileByName(String wdfname) {
        for (int i = 0; i < WdfMianUtil.wdfFiles.size(); i++) {
            File file = WdfMianUtil.wdfFiles.get(i);
            if (file.getName().equals(wdfname)) {
                return file;
            }
        }
        return  WdfMianUtil.wdfFiles.get(0);
    }


    private static boolean isValidWdfFile(String wdfname) {
        if (WdfMianUtil.wdfFiles!=null) {
            for (int i = 0; i < WdfMianUtil.wdfFiles.size(); i++) {
                if (WdfMianUtil.wdfFiles.get(i).getName().equals(wdfname)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static WdfHead findMatchingWdfHeadParallel(String wdfname) {
        ForkJoinPool forkJoinPool = new ForkJoinPool();
        FindWdfHeadTask task = new FindWdfHeadTask(WdfMianUtil.wdfFiles,wdfname);
        return forkJoinPool.invoke(task).orElse(null);
    }

    private static class FindWdfHeadTask extends RecursiveTask<Optional<WdfHead>> {
        private final List<File> files;
        private final String  wdfname;
        public FindWdfHeadTask(List<File> files,String wdfname) {
            this.files = files;
            this.wdfname = wdfname;
        }
        @Override
        protected Optional<WdfHead> compute() {
            if (files.isEmpty()) {
                return Optional.empty();
            }
            int size = files.size();
            for (File file : files) {
                if (file.getName().equals(wdfname)) {
                    return Optional.ofNullable(WdfTool.getWdfHead(file));
                }
            }
            int mid = size / 2;
            FindWdfHeadTask leftTask = new FindWdfHeadTask(files.subList(0, mid),wdfname);
            FindWdfHeadTask rightTask = new FindWdfHeadTask(files.subList(mid, size),wdfname);
            invokeAll(leftTask, rightTask);
            Optional<WdfHead> leftResult = leftTask.join();
            Optional<WdfHead> rightResult = rightTask.join();
            return leftResult.isPresent() ? leftResult : rightResult;
        }
    }
    private static List<WasData> findMatchingWasDatas(WdfHead wdfHead) {
        List<WasData> matchingWasDataList = new ArrayList<>();
        for (Object wasDataObj : wdfHead.getWasDataList()) {
            if (wasDataObj instanceof WasData) {
                WasData wasData = (WasData) wasDataObj;
                if (wasData != null) {
                    matchingWasDataList.add(wasData);
                }
            }
        }
        return matchingWasDataList;
    }

    private static WasHead getWasHeadForWasData(WasData wasData,String wdfname) {
            for (int i = 0; i < WdfMianUtil.wdfFiles.size(); i++) {
                File file = WdfMianUtil.wdfFiles.get(i);
                if (file.isFile()&&file.getName().contains(wdfname)){
                    return WasTool.getWasHead(file, wasData.getFileOffset());
                }
            }
        return null;
    }

    public static Sprite getWasHeadForWasDatanew(WasData wasData, String wdfname) {
        for (File file : WdfMianUtil.wdfFiles) {
            if (file.isFile() && file.getName().contains(wdfname)) {
                return WasTool.getWasHeadnew(file, wasData);
            }
        }
        return null;
    }


}
