package jxy2.setup;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.SwitchUi;
import jxy2.UiBack;
import jxy2.jutnil.ImgConstants;
import org.come.Frame.TestSetupJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.mouslisten.SystemMouslisten;
import org.come.test.Main;
import org.come.until.*;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.io.*;
import java.util.Properties;

public class AudioSteupMouslisten  implements MouseListener {
    public static ImageIcon icon = CutButtonImage.getWdfPng(ImgConstants.tz84,"defaut.wdf");
    // 类型 0 800*600 1 1024*768 2音乐 3音效
    public int type;
    public AudioSteupJpanel steupJpanel;
    public AudioSteupMouslisten(AudioSteupJpanel steupJpanel,int type) {
        super();
        this.type = type;
        this.steupJpanel = steupJpanel;
    }



    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        if (FightingMixDeal.State != HandleState.USUAL) {
            ZhuFrame.getZhuJpanel().addPrompt("#R战斗中无法操作！");
            return;
        }
        switch (type){
            case 2://音乐开启
                type2();
                break;
            case 3://音效开启
                type3();
                break;
            case 4:
            case 5:
            case 6:
                type5(type-4);
                SumIndex(type-4);
                break;
            case 7:
            case 8:
            case 9:
                type4("TEXT_FONT_"+(type-7));
                FoutsIndex(type-7);
                break;
            case 10://全屏
                type10();
                break;
            case 11://窗口
                type11();
                break;

        }
        writeTxt();
    }


    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }


    public static void type11() {
        AudioSteupJpanel steupJpanel = TestSetupJframe.getTestSetupJframe().getSetupMainJPanel().getSetupCardJPanel().getAudioSteupJpanel();
        steupJpanel.getLabScree()[1].setIcon(steupJpanel.getLabScree()[1].getIcon()==null?icon:null);
        steupJpanel.getLabScree()[0].setIcon(null);
        Main.frame.exitFullscreen();

    }

    private void type10() {
        steupJpanel.getLabScree()[0].setIcon(steupJpanel.getLabScree()[0].getIcon()==null?icon:null);
        steupJpanel.getLabScree()[1].setIcon(null);
        Main.frame.toggleFullscreen();
        SystemMouslisten.type12("1024x768");
        steupJpanel.getResolutiontext().setText("1024x768");

    }

    private void FoutsIndex(int type) {
        steupJpanel.getLabfouts()[type].setIcon(icon);
        for (int i = 0; i < steupJpanel.getLabUi().length; i++) {
            if (i != type ) {
                steupJpanel.getLabfouts()[i].setIcon(null);
            }
        }
    }

    private  void SumIndex(int type) {
        steupJpanel.getLabUi()[type].setIcon(icon);
        for (int i = 0; i < steupJpanel.getLabUi().length; i++) {
            if (i != type ) {
                steupJpanel.getLabUi()[i].setIcon(null);
            }
        }
    }

    /** 音乐 */
    public static void type2() {
        AudioSteupJpanel audioSteupJpanel = TestSetupJframe.getTestSetupJframe().getSetupMainJPanel().getSetupCardJPanel().getAudioSteupJpanel();
        if (audioSteupJpanel.getLabMusic().getIcon() == null) {
            // 开启
            audioSteupJpanel.getLabMusic().setIcon(icon);
            // 开启音乐
            Music.kz1 = true;
            Music.addbeijing(Util.mapmodel.getGamemap().getMusic() + ".mp3");
        } else {
            // 关闭
            audioSteupJpanel.getLabMusic().setIcon(null);
            // 关闭音乐
            Music.kz1 = false;
            // 关闭音乐
            Music.beijing(Music.kz1);
        }
    }

    /** 音效 */
    public static void type3() {
        AudioSteupJpanel audioSteupJpanel = TestSetupJframe.getTestSetupJframe().getSetupMainJPanel().getSetupCardJPanel().getAudioSteupJpanel();
        if (audioSteupJpanel.getLabSound().getIcon() == null) {
            // 开启
            audioSteupJpanel.getLabSound().setIcon(icon);
            // 开启音效
            Music.kz2 = true;
        } else {
            // 关闭
            audioSteupJpanel.getLabSound().setIcon(null);
            // 关闭音效
            Music.kz2 = false;
            // 关闭音乐
            Music.yinxiao(Music.kz2);

        }
    }
    public static void type4(String name) {
            switch (name){
                case "TEXT_FONT_0":
                    SetupMainJPanel.setAllFont(UIUtils.TEXT_FONT_0);
                    break;
                case "TEXT_FONT_1":
                    SetupMainJPanel.setAllFont(UIUtils.FZCY_HY15);
                    break;
                case "TEXT_FONT_2":
                    SetupMainJPanel.setAllFont(UIUtils.MSYH_HY15);
                    break;
            }
    }
    //这里是切换UI的方法
    public static void type5(int name) {
        if (name==0){return;}
        Util.SwitchUI = name;
        SetupMainJPanel.setAllUi(name);
        for (int i = 0; i < FormsManagement.Forms.size(); i++) {
            int bh = FormsManagement.Forms.get(i).getFormid();
            if (bh==50)continue;
            if (FormsManagement.getframe(bh).isVisible()){
//                FormsManagement.HideForm(bh);
            }
        }

        SwitchUi.allui(name);
        UiBack.init();
        GoodsListFromServerUntil.SwitchInterfaceClass();
    }



    /** 读取配置文件初始化面板 */
    public static void readSysteminit() {
        LoginResult result = RoleData.getRoleData().getLoginResult();
        AudioSteupJpanel audioSteupJpanel = TestSetupJframe.getTestSetupJframe().getSetupMainJPanel().getSetupCardJPanel().getAudioSteupJpanel();
        Properties properties = new Properties();
        FileInputStream fis = null;
        InputStreamReader isr = null;
        try {
            fis = new FileInputStream("resource/other/Audio.txt");
            isr = new InputStreamReader(fis, "UTF-8");
            properties.load(isr);// 使用properties对象加载输入流
            String music = properties.getProperty("music");// 音乐
            String musicSound = properties.getProperty("musicSound");// 音效
            String Scree = properties.getProperty("Scree");// 音效

            if (audioSteupJpanel!=null) {
                if (music != null) {
                    if ("off".equals(music)) {
                        audioSteupJpanel.getLabMusic().setIcon(icon);
                    } else {
                        audioSteupJpanel.getLabMusic().setIcon(null);
                    }
                    type2();
                } else {
                    audioSteupJpanel.getLabMusic().setIcon(icon);
                }
                if (musicSound != null) {
                    if ("off".equals(musicSound)) {
                        audioSteupJpanel.getLabSound().setIcon(icon);
                    } else {
                        audioSteupJpanel.getLabSound().setIcon(null);
                    }
                    type3();
                } else {
                    audioSteupJpanel.getLabSound().setIcon(icon);
                }

                if (Scree!=null){
                    if ("on".equals(Scree)) {
                        audioSteupJpanel.getLabScree()[1].setIcon(icon);
                    } else {
                        audioSteupJpanel.getLabScree()[1].setIcon(null);
                    }
                }else {
                    audioSteupJpanel.getLabScree()[1].setIcon(icon);
                }


                for (int i = 0; i < audioSteupJpanel.getLabUi().length; i++) {
                    String sum = properties.getProperty("sum" + i);
                    if (sum != null) {
                        if ("off".equals(sum)) {
                            audioSteupJpanel.getLabUi()[i].setIcon(null);
                        } else {
                            audioSteupJpanel.getLabUi()[i].setIcon(icon);
                            Util.SwitchUI = i;
                        }
                    }

                }

                for (int i = 0; i < audioSteupJpanel.getLabfouts().length; i++) {
                    String sum = properties.getProperty("fouts" + i);
                    if (sum != null) {
                        if ("off".equals(sum)) {
                            audioSteupJpanel.getLabfouts()[i].setIcon(null);
                        } else {
                            audioSteupJpanel.getLabfouts()[i].setIcon(icon);
                            type4(sum);
                        }

                    }
                }


            }


        } catch (IOException e1) {
            //TODO 初始化
        } finally {
            try {
                if (isr != null) {
                    isr.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /** 改写配置文件 */
    public static void writeTxt() {
        AudioSteupJpanel audioSteupJpanel = TestSetupJframe.getTestSetupJframe().getSetupMainJPanel().getSetupCardJPanel().getAudioSteupJpanel();
        FileOutputStream outputStream = null;
        OutputStreamWriter osw = null;
        Properties properties = new Properties();
        try {
            outputStream = new FileOutputStream("resource/other/Audio.txt");
            osw = new OutputStreamWriter(outputStream, "UTF-8");
            if (audioSteupJpanel!=null) {
                if (audioSteupJpanel.getLabMusic().getIcon() != null) {
                    properties.setProperty("music", "on");
                } else {
                    properties.setProperty("music", "off");
                }

                if (audioSteupJpanel.getLabSound().getIcon() != null) {
                    properties.setProperty("musicSound", "on");
                } else {
                    properties.setProperty("musicSound", "off");
                }

                if (audioSteupJpanel.getLabScree()[1].getIcon() != null) {
                    properties.setProperty("Scree", "on");
                } else {
                    properties.setProperty("Scree", "off");
                }



                for (int i = 0; i < audioSteupJpanel.getLabUi().length; i++) {
                    if (audioSteupJpanel.getLabUi()[i].getIcon() != null) {
                        properties.setProperty("sum" + i, "on");
                    } else {
                        properties.setProperty("sum" + i, "off");
                    }

                }

                for (int i = 0; i < audioSteupJpanel.getLabfouts().length; i++) {
                    if (audioSteupJpanel.getLabfouts()[i].getIcon() != null) {
                        properties.setProperty("fouts" + i, "TEXT_FONT_" + i);
                    } else {
                        properties.setProperty("fouts" + i, "off");
                    }
                }
            }




            properties.store(osw, null);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (osw != null) {
                try {
                    osw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
