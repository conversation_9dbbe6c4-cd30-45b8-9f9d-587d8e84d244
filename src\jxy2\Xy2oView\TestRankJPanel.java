package jxy2.Xy2oView;

import com.tool.btn.ActivityBtn;
import com.tool.btn.GoodPanelBtn;
import com.tool.image.ImageMixDeal;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.bean.State;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

/**
* 境界/段位/
* <AUTHOR>
* @date 2024/4/21 下午4:01
*/
public class TestRankJPanel extends JPanel {
    private ActivityBtn token,btn59;
    private GoodPanelBtn[] dress = new GoodPanelBtn[3];
    private State state;
    private List<Goodstable> goodstable = new ArrayList<>();
    private String attribute,advanced;
    private JLabel[] jLabel,goodsIcon,rewardIcon;

    public TestRankJPanel() {
        this.setPreferredSize(new Dimension(403,496));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        token = new ActivityBtn(ImgConstants.Btn_1, 1, 8, "突破境界", Juitil.Colours(),this);
        token.setBounds(240, 422, 99, 25);
        this.add(token);
        btn59 = new ActivityBtn(ImgConstants.Btn_59, 1, 9, "领取", Juitil.Colours(),this);
        btn59.setBounds(60, 363, 59, 25);
        this.add(btn59);

        for (int i = 0; i < dress.length; i++) {
            dress[i] = new GoodPanelBtn(ImgConstants.btn_34_17, 1, "前往",i);
            dress[i].setBounds(173, 400+i*24, 34, 18);
            this.add(dress[i]);
        }

        jLabel = new JLabel[2];
        for (int i = 0; i <jLabel.length ; i++) {
            jLabel[i] = new JLabel("+0.5%"){
                @Override
                protected void paintComponent(Graphics g) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
                    //绘制黑色描边
                    g2d.setColor(Color.black);
                    g2d.setFont(UIUtils.LiSu_LS13);
                    g2d.drawString(getText(), 9 + 1, getBaseline(getWidth(), getHeight()));
                    g2d.drawString(getText(), -1 + 9, getBaseline(getWidth(), getHeight()));
                    g2d.drawString(getText(), 9, getBaseline(getWidth(), getHeight()) + 1);
                    g2d.drawString(getText(), 9, getBaseline(getWidth(), getHeight()) - 1);
                    // 绘制白色文本 去除汉字
                    g2d.setColor(Color.green);
                    g2d.drawString(getText(), 9, getBaseline(getWidth(), getHeight()));
                    g2d.dispose();
                }
            };
            jLabel[i] .setBounds(288, 225+i*19, 45, 18);
            add(jLabel[i]);
        }
        String[] img = {"204","8","200"};
        goodsIcon = new JLabel[3];
        rewardIcon = new JLabel[3];
        for (int i = 0; i < goodsIcon.length; i++) {
            goodsIcon[i] = new JLabel();
            rewardIcon[i] = new JLabel();

            goodsIcon[i].setBounds(188+i*50, 337, 43, 43);
            rewardIcon[i].setBounds(35, 226+i*40, 35, 35);
            int index = i;
            goodsIcon[i].addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    ZhuFrame.getZhuJpanel().creatgoodtext(goodstable.get(index));
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    ZhuFrame.getZhuJpanel().cleargoodtext();
                }
            });
            rewardIcon[i].setIcon(GoodsListFromServerUntil.imgpathAdaptive(img[i],35,35));
            add(goodsIcon[i]);
            add(rewardIcon[i]);
        }


    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.vnavi_1_sprite.updateToTime(ImageMixDeal.userimg.getTime(),0);
        String[] vs = {"/分钟","/分钟","/小时"};
//        String[] GoodsExeMoney = state.getJlreward().split("\\|");
//        int Goods = Integer.parseInt(state.getJlreward().split("\\|")[2].split("\\&")[1].split("\\$")[1]);

        for (int i = 0; i < 3; i++) {
            Juitil.ImngBack(g, Juitil.Radio_17, 27, 400+i*24, 144, 19, 1);
//            Juitil.ImngBack(g, Juitil.vnaviArray[1], 187+i*50, 336, 45, 45, 1);
//            g.setColor(Color.white);
//            g.setFont(UIUtils.TEXT_FONT2);
//            g.drawString(vs[i],100,250+i*40);
//            g.setFont(UIUtils.LiSu_LS13);
        }
//
        for (int i = 0; i < 2; i++) {
//            Juitil.ImngBack(g, Juitil.vnavi_1, 46+i*160, 55, 120, 120, 1);
//            Juitil.ImngBack(g, Juitil.vnaviArray[2], 30+i*170, 192, 118, 30, 1);
            Juitil.vnavi_1_sprite.draw(g,46+i*164,55,120,120);
//            g.drawString(GoodsExeMoney[i].split("\\=")[1],77,250+i*40);
        }
//        g.drawString(Goods+"",84,330);
        g.setColor(Color.white);
        g.setFont(UIUtils.TEXT_FONT2);
        g.drawString("聚灵奖励",65,212);
        g.drawString("突破奖励",235,212);
//        String[] keyv = state.getKey().split("\\|");
//        g.setColor(Color.black);
//        for (int i = 0; i < keyv.length; i++) {
//            g.drawString(keyv[i]+"加成",170,238+i*20);
//        }
//        g.setFont(UIUtils.LiSu_LS13);
//        g.drawString(state.getValue()+"%",250,238);
//        g.drawString(state.getValue1()+"%",250,258);

//        Juitil.Txtpet(g, 94, 100, attribute, Color.WHITE, UIUtils.NEWTX_HY20B);
//        Juitil.Txtpet(g, 70, 90, advanced, Color.WHITE, UIUtils.NEWTX_HY18B);
        String lv=null;
        //下一个境界
        if ("一阶".equals(advanced)) {
            lv = "二阶";
        }else if ("二阶".equals(advanced)) {
            lv = "三阶";
        }
        if (lv != null) {
            Juitil.Txtpet(g, 254, 100, attribute, Color.WHITE, UIUtils.NEWTX_HY20B);
            Juitil.Txtpet(g, 230, 90, lv, Color.WHITE, UIUtils.NEWTX_HY18B);
        }

//        TimeLong(g);

//        String[] task = state.getRequirement().split("\\|");
//        Graphics2D g2d = (Graphics2D) g;
//        int yOffset = 416;
//        for (String t : task) {
//            Juitil.drawStringWithDifferentFonts(g2d, t, 32, yOffset);
//            yOffset += 23;
//        }
    }



    private void TimeLong(Graphics g) {
        if (state.getJltime()==null)return;
        long fixedMillis = System.currentTimeMillis()-Long.parseLong(state.getJltime());
        // 计算小时数和分钟数
        long totalMinutes = fixedMillis / (1000 * 60);
        long hours = totalMinutes / 60;
        long minutes = totalMinutes % 60;
        // 格式化成时:分格式的字符串
        String timeString = String.format("%02d", hours);
        String timef = String.format("%02d", minutes);
        if (Integer.parseInt(timeString)>=15){
            timeString = "15";
            timef = "00";
        }
        g.setFont(UIUtils.TEXT_FONT2);
        g.drawString("已聚灵  小时  分钟",30,355);
        g.setFont(UIUtils.LiSu_LS13);
        g.setColor(UIUtils.COLOR_HURTR1);
        g.drawString(timeString,69,354);
        g.drawString(timef,110,354);
    }


    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    public String getAdvanced() {
        return advanced;
    }

    public void setAdvanced(String advanced) {
        this.advanced = advanced;
    }

    public List<Goodstable> getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(List<Goodstable> goodstable) {
        this.goodstable = goodstable;
    }

    public JLabel[] getGoodsIcon() {
        return goodsIcon;
    }

    public void setGoodsIcon(JLabel[] goodsIcon) {
        this.goodsIcon = goodsIcon;
    }

    public GoodPanelBtn[] getDress() {
        return dress;
    }

    public void setDress(GoodPanelBtn[] dress) {
        this.dress = dress;
    }
}
