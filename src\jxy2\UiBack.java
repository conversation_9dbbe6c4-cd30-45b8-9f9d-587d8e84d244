package jxy2;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.RichLabel;
import jxy2.jutnil.Juitil;
import org.come.bean.ImgZoom;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * UI背景管理类，负责管理和绘制UI背景和元素
 *
 * <p>配置文件格式 (ui_config.json):</p>
 * <pre>
 * {
 *   "资源类型": {                  // 例如 "BK_59X58"
 *     "面板类型": {                // JPanel类的名称，如 "FightPanel"
 *       "0": {                    // 默认UI样式 (数字为样式ID)
 *         "x": 100,               // X坐标
 *         "y": 200,               // Y坐标
 *         "width": 59,            // 宽度
 *         "height": 58            // 高度
 *       },
 *       "1": { ... },             // She UI样式
 *       "2": { ... }              // Red UI样式
 *     },
 *     "shared:Panel1,Panel2,Panel3": { // 共享配置，多个JPanel共用
 *       "0": { ... },             // 默认UI样式
 *       "1": { ... },             // She UI样式
 *       "2": { ... }              // Red UI样式
 *     },
 *     "default": {                // 默认配置，当特定面板配置缺失时使用
 *       "0": { ... },
 *       "1": { ... },
 *       "2": { ... }
 *     }
 *   }
 * }
 * </pre>
 *
 * <p>共享配置使用方法:</p>
 * <ul>
 *   <li>使用 "shared:" 前缀，后面跟逗号分隔的面板类名列表</li>
 *   <li>例如："shared:LoginPanel,RegisterPanel,MainPanel"</li>
 *   <li>这将使这三个面板共用同一套位置配置</li>
 * </ul>
 */
public class UiBack {
    private static final Map<String, Map<String, Map<Integer, Position>>> CONFIG = new HashMap<>();
    private static final int[][] STYLE_ADJUSTMENTS = {
            {0, 0, 0, 0},    // 默认样式(0)的调整: x, y, 宽度, 高度
            {2, 2, 0, 0},    // She样式(1)的调整: x, y, 宽度, 高度
            {1, 1, 0, 0}     // Red样式(2)的调整: x, y, 宽度, 高度
    };
    // 组件样式管理
    private static final Map<String, Map<String, Map<Integer, ComponentStyle>>> COMPONENT_STYLES = new HashMap<>();

    public static void init() {
        String meg = Agreement.getAgreement().UiBackAgreement("");
        SendMessageUntil.toServer(meg);
        String cmeg = Agreement.getAgreement().ComponentAgreement("");
        SendMessageUntil.toServer(cmeg);
    }
    // 加载UI配置文件
    public static void loadConfig(String jsonContent) {
            // 使用GsonUtil解析JSON配置 - 兼容旧版本Gson
            com.google.gson.JsonObject configJson = GsonUtil.getGsonUtil().getgson()
                    .fromJson(jsonContent, com.google.gson.JsonObject.class);

            // 清空现有配置
            CONFIG.clear();

            // 遍历每个UI资源类型（枚举名）
            for (UIResource resource : UIResource.values()) {
                String resourceName = resource.name();

                if (configJson.has(resourceName)) {
                    com.google.gson.JsonObject resourceConfig = configJson.getAsJsonObject(resourceName);

                    // 为每个资源类型创建一个面板配置映射
                    Map<String, Map<Integer, Position>> panelMap = new HashMap<>();
                    CONFIG.put(resourceName, panelMap);

                    // 遍历每个面板类型
                    for (Map.Entry<String, com.google.gson.JsonElement> panelEntry : resourceConfig.entrySet()) {
                        String panelName = panelEntry.getKey();
                        com.google.gson.JsonObject panelObject = panelEntry.getValue().getAsJsonObject();

                        // 为每个面板创建一个UI样式配置映射
                        Map<Integer, Position> styleMap = new HashMap<>();
                        panelMap.put(panelName, styleMap);

                        // 遍历每种UI样式（0=默认，1=she，2=red）
                        for (Map.Entry<String, com.google.gson.JsonElement> styleEntry : panelObject.entrySet()) {
                            int styleId = Integer.parseInt(styleEntry.getKey());
                            com.google.gson.JsonObject posObject = styleEntry.getValue().getAsJsonObject();

                            // 解析位置信息
                            int x = posObject.get("x").getAsInt();
                            int y = posObject.get("y").getAsInt();
                            int width = posObject.get("width").getAsInt();
                            int height = posObject.get("height").getAsInt();

                            // 创建位置对象并存储到映射中
                            styleMap.put(styleId, new Position(x, y, width, height));
                        }
                    }
                }
            }
//        ZhuFrame.getZhuJpanel().addPrompt("#GUI配置加载成功: " + CONFIG.size() + " 个资源类型已加载");
    }
    // 根据UI样式获取调整值
    private static int[] getStyleAdjustment() {
        int styleId = Util.SwitchUI;
        if (styleId >= 0 && styleId < STYLE_ADJUSTMENTS.length) {
            return STYLE_ADJUSTMENTS[styleId];
        }
        return STYLE_ADJUSTMENTS[0]; // 默认返回默认样式的调整值
    }

    // 位置信息类
    public static class Position {
        public final int x, y, width, height;

        public Position(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }

    /**
     * 组件样式类 - 用于管理组件的字体、颜色、大小等属性
     */
    public static class ComponentStyle {
        // 组件位置和大小
        public final int x, y, width, height;
        // 文字属性
        public final Font font;
        public final Color textColor;
        public final int fontSize;
        // 其他可能的样式属性
        public final Color bgColor;
        public final boolean isVisible;
        public final int alignment;
        // 多颜色数组 - 用于存储不同状态或部分的颜色
        public final Color[] colors;

        /**
         * 完整构造函数
         */
        public ComponentStyle(int x, int y, int width, int height, Font font, Color textColor,
                              int fontSize, Color bgColor, boolean isVisible, int alignment, Color[] colors) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.font = font;
            this.textColor = textColor;
            this.fontSize = fontSize;
            this.bgColor = bgColor;
            this.isVisible = isVisible;
            this.alignment = alignment;
            this.colors = colors;
        }

        /**
         * 简化版构造函数 - 仅位置和大小
         */
        public ComponentStyle(int x, int y, int width, int height) {
            this(x, y, width, height, null, null, 0, null, true, 0, null);
        }

        /**
         * 简化版构造函数 - 位置、大小和字体
         */
        public ComponentStyle(int x, int y, int width, int height, Font font, Color textColor) {
            this(x, y, width, height, font, textColor, font != null ? font.getSize() : 0, null, true, 0, null);
        }

        /**
         * 简化版构造函数 - 包含颜色数组
         */
        public ComponentStyle(int x, int y, int width, int height, Font font, Color textColor, Color[] colors) {
            this(x, y, width, height, font, textColor, font != null ? font.getSize() : 0, null, true, 0, colors);
        }

        /**
         * 应用样式到按钮组件
         * @param button 按钮组件
         */
        public void applyToButton(MoBanBtn button) {
            button.setBounds(x, y, width, height);
//            if (colors!=null)button.setColors(colors);
            button.setVisible(isVisible);
        }

        /**
         * 应用样式到标签组件
         * @param label 标签组件
         */
        public void applyToLabel(JLabel label) {
            label.setBounds(x, y, width, height);
            if (font != null) label.setFont(font);
            if (textColor != null) label.setForeground(textColor);
            if (bgColor != null) label.setBackground(bgColor);
            label.setVisible(isVisible);
        }

        /**
         * 应用样式到文本框组件
         * @param textField 文本框组件
         */
        public void applyToTextField(JTextField textField) {
            textField.setBounds(x, y, width, height);
            if (font != null) textField.setFont(font);
            if (textColor != null) textField.setForeground(textColor);
            if (bgColor != null) textField.setBackground(bgColor);
            textField.setVisible(isVisible);
        }

        /**
         * 应用样式滚动条
         */
        public void applyToScrollBar(JScrollPane jScrollPane) {
            jScrollPane.setBounds(x, y, width, height);
            jScrollPane.setVisible(isVisible);
        }

        public void applyToRichLabel(RichLabel richLabel) {
            richLabel.setBounds(x, y, width, height);
            richLabel.setVisible(isVisible);
        }
    }

    private static volatile boolean isComponentStyleLoaded = false;
    /**
     * 加载组件样式配置文件
     */
    public static void loadComponentStyles(String jsonContent) {
            isComponentStyleLoaded = true; // 标记已加载
            // 使用GsonUtil解析JSON配置
            com.google.gson.JsonObject configJson = GsonUtil.getGsonUtil().getgson()
                    .fromJson(jsonContent, com.google.gson.JsonObject.class);
            // 清空现有配置
            COMPONENT_STYLES.clear();
            // 遍历面板配置
            for (Map.Entry<String, com.google.gson.JsonElement> panelEntry : configJson.entrySet()) {
                String panelName = panelEntry.getKey();
                com.google.gson.JsonObject panelObject = panelEntry.getValue().getAsJsonObject();

                // 为每个面板创建组件映射
                Map<String, Map<Integer, ComponentStyle>> componentMap = new HashMap<>();
                COMPONENT_STYLES.put(panelName, componentMap);
                // 遍历组件配置
                for (Map.Entry<String, com.google.gson.JsonElement> compEntry : panelObject.entrySet()) {
                    String componentId = compEntry.getKey();
                    com.google.gson.JsonObject compObject = compEntry.getValue().getAsJsonObject();

                    // 为每个组件创建样式映射
                    Map<Integer, ComponentStyle> styleMap = new HashMap<>();
                    componentMap.put(componentId, styleMap);

                    // 遍历每种UI样式
                    for (Map.Entry<String, com.google.gson.JsonElement> styleEntry : compObject.entrySet()) {
                        int styleId = Integer.parseInt(styleEntry.getKey());
                        com.google.gson.JsonObject styleObject = styleEntry.getValue().getAsJsonObject();

                        // 解析位置和大小信息
                        int x = styleObject.has("x") ? styleObject.get("x").getAsInt() : 0;
                        int y = styleObject.has("y") ? styleObject.get("y").getAsInt() : 0;
                        int width = styleObject.has("width") ? styleObject.get("width").getAsInt() : 100;
                        int height = styleObject.has("height") ? styleObject.get("height").getAsInt() : 30;

                        // 解析字体信息
                        Font font = null;
                        if (styleObject.has("font")) {
                            com.google.gson.JsonObject fontObj = styleObject.get("font").getAsJsonObject();
                            String fontName = fontObj.has("name") ? fontObj.get("name").getAsString() : "Dialog";
                            int fontSize = fontObj.has("size") ? fontObj.get("size").getAsInt() : 12;
                            int fontStyle = fontObj.has("style") ? fontObj.get("style").getAsInt() : Font.PLAIN;
                            font = new Font(fontName, fontStyle, fontSize);
                        }

                        // 解析颜色信息
                        Color textColor = null;
                        if (styleObject.has("textColor")) {
                            com.google.gson.JsonObject colorObj = styleObject.get("textColor").getAsJsonObject();
                            int r = colorObj.has("r") ? colorObj.get("r").getAsInt() : 0;
                            int g = colorObj.has("g") ? colorObj.get("g").getAsInt() : 0;
                            int b = colorObj.has("b") ? colorObj.get("b").getAsInt() : 0;
                            textColor = new Color(r, g, b);
                        }

                        Color bgColor = null;
                        if (styleObject.has("bgColor")) {
                            com.google.gson.JsonObject colorObj = styleObject.get("bgColor").getAsJsonObject();
                            int r = colorObj.has("r") ? colorObj.get("r").getAsInt() : 0;
                            int g = colorObj.has("g") ? colorObj.get("g").getAsInt() : 0;
                            int b = colorObj.has("b") ? colorObj.get("b").getAsInt() : 0;
                            bgColor = new Color(r, g, b);
                        }

                        // 解析颜色数组
                        Color[] colors = null;
                        if (styleObject.has("colors")) {
                            com.google.gson.JsonArray colorsArray = styleObject.get("colors").getAsJsonArray();
                            colors = new Color[colorsArray.size()];
                            for (int i = 0; i < colorsArray.size(); i++) {
                                com.google.gson.JsonObject colorObj = colorsArray.get(i).getAsJsonObject();
                                int r = colorObj.has("r") ? colorObj.get("r").getAsInt() : 0;
                                int g = colorObj.has("g") ? colorObj.get("g").getAsInt() : 0;
                                int b = colorObj.has("b") ? colorObj.get("b").getAsInt() : 0;
                                int a = colorObj.has("a") ? colorObj.get("a").getAsInt() : 255;
                                colors[i] = new Color(r, g, b, a);
                            }
                        }

                        // 解析其他属性
                        boolean isVisible = !styleObject.has("visible") || styleObject.get("visible").getAsBoolean();
                        int alignment = styleObject.has("alignment") ? styleObject.get("alignment").getAsInt() : 0;

                        // 创建组件样式并存储
                        ComponentStyle compStyle = new ComponentStyle(
                                x, y, width, height, font, textColor,
                                font != null ? font.getSize() : 0,
                                bgColor, isVisible, alignment, colors
                        );
                        styleMap.put(styleId, compStyle);
                    }
                }
            }
//            ZhuFrame.getZhuJpanel().addPrompt("#G组件样式配置加载成功: " + COMPONENT_STYLES.size() + " 个面板配置已加载");
            SwitchUi.allui(Util.SwitchUI);

    }
    /**
     * 获取指定组件的样式
     * @param panelName 面板名称
     * @param componentId 组件ID
     * @return 组件样式
     */
    public static ComponentStyle getComponentStyle(String panelName, String componentId) {
        if (!isComponentStyleLoaded) {
            // 样式未加载，返回默认样式
            return new ComponentStyle(0, 0, 100, 30);
        }
        // 获取当前UI风格
        int styleId = Util.SwitchUI;
        if (styleId < 0 || styleId > 2) styleId = 0;
        // 检查样式配置
        Map<String, Map<Integer, ComponentStyle>> panelStyles = COMPONENT_STYLES.get(panelName);
        if (panelStyles == null) {
            // 尝试查找共享配置
            for (Map.Entry<String, Map<String, Map<Integer, ComponentStyle>>> entry : COMPONENT_STYLES.entrySet()) {
                String key = entry.getKey();
                if (key.startsWith("shared:")) {
                    String[] panels = key.substring(7).split(",");
                    for (String p : panels) {
                        if (p.trim().equals(panelName)) {
                            panelStyles = entry.getValue();
                            break;
                        }
                    }
                    if (panelStyles != null) break;
                }
            }
        }

        // 如果仍然没找到，使用默认配置
        if (panelStyles == null) {
            panelStyles = COMPONENT_STYLES.get("default");
            if (panelStyles == null) {
                // 创建一个默认样式并返回
                return new ComponentStyle(0, 0, 100, 30);
            }
        }

        // 查找组件样式
        Map<Integer, ComponentStyle> componentStyles = panelStyles.get(componentId);
        if (componentStyles == null) {
            // 尝试使用默认组件样式
            componentStyles = panelStyles.get("default");
            if (componentStyles == null) {
                // 创建一个默认样式并返回
                return new ComponentStyle(0, 0, 100, 30);
            }
        }

        // 获取当前UI风格的样式
        ComponentStyle style = componentStyles.get(styleId);
        if (style == null) {
            // 使用默认风格的样式
            style = componentStyles.get(0);
            if (style == null) {
                // 创建一个默认样式并返回
                return new ComponentStyle(0, 0, 100, 30);
            }
        }
        return style;
    }

    // 使用枚举定义UI资源
    public enum UIResource {
        IMG_ZOOM(Juitil.tz67, Juitil.she_0003, Juitil.red_0007),
        BACK(Juitil.tz22, Juitil.she_0005, Juitil.red_0006),
        BK_59X58(Juitil.good_2, Juitil.tz258, Juitil.red_0008),
        LIST(Juitil.tz21, Juitil.she_0009, Juitil.red_0009),
        LISTMAX(Juitil.tz21, Juitil.she_0009, Juitil.red_0037),
        PETBACK(Juitil.tz22, Juitil.she_0005, Juitil.red_0006),
        BACKBK(Juitil.tz22, Juitil.she_0004, Juitil.red_0038),
        backpackimg(Juitil.book, Juitil.she_0019, Juitil.red_0023),
        BACKBK_2(Juitil.tz22, Juitil.TxtImg, Juitil.red_0019),
        BACK_1(Juitil.tz128,Juitil.she_0004,Juitil.red_0018),
        entry(Juitil.tz128,Juitil.tz128,Juitil.red_0043),
        NAVIGATION_BAR(Juitil.tz21,Juitil.she_0022,Juitil.red_0036);

        private final ImgZoom defaultUI;
        private final ImgZoom sheUI;
        private final ImgZoom redUI;

        UIResource(ImgZoom defaultUI, ImgZoom sheUI, ImgZoom redUI) {
            this.defaultUI = defaultUI;
            this.sheUI = sheUI;
            this.redUI = redUI;
        }

        public ImgZoom get() {
            switch (Util.SwitchUI) {
                case 0: return defaultUI;
                case 1: return sheUI;
                default: return redUI;
            }
        }

        /**
         * 绘制UI元素
         * @param g 图形对象
         * @param panel 面板
         */
        public void draw(Graphics g, JPanel panel) {
            // 在每次使用配置前检查是否需要重新加载
//            checkAndReloadConfig();
            Position pos = getPosition(panel);
            Juitil.ImngBack(g, get(), pos.x, pos.y, pos.width, pos.height, 1);
        }
        public void draw(Graphics g, JPanel panel,int x,int y,int sum,int y1,int w,int h) {
            Position pos = getPosition(panel);
            for (int i = 0; i < sum; i++) {
                Juitil.ImngBack(g, get(), (pos.x+x)+i*y, pos.y+i*y1, w, h, 1);
            }
        }
        public void draw(Graphics g, JPanel panel,int x,int y,int sum,int y1) {
            Position pos = getPosition(panel);
            for (int i = 0; i < sum; i++) {
                Juitil.ImngBack(g, get(), (pos.x+x)+i*y, pos.y+i*y1, pos.width, pos.height, 1);
            }
        }

        /**
         * 根据当前UI样式调整坐标和大小
         * @param x 原始X坐标
         * @param y 原始Y坐标
         * @param width 原始宽度
         * @param height 原始高度
         * @return 调整后的参数数组 [adjustedX, adjustedY, adjustedWidth, adjustedHeight]
         */
        private int[] adjustParameters(int x, int y, int width, int height) {
            int[] adjustment = getStyleAdjustment();
            return new int[] {
                    x + adjustment[0],           // 调整X坐标
                    y + adjustment[1],           // 调整Y坐标
                    width + adjustment[2],       // 调整宽度
                    height + adjustment[3]       // 调整高度
            };
        }

        /**
         * 简单绘制方法，自动根据当前UI样式选择正确的参数
         * @param g 图形对象
         * @param defaultX 默认样式的X坐标
         * @param sheX She样式的X坐标
         * @param redX Red样式的X坐标
         * @param defaultY 默认样式的Y坐标
         * @param sheY She样式的Y坐标
         * @param redY Red样式的Y坐标
         * @param defaultWidth 默认样式的宽度
         * @param sheWidth She样式的宽度
         * @param redWidth Red样式的宽度
         * @param defaultHeight 默认样式的高度
         * @param sheHeight She样式的高度
         * @param redHeight Red样式的高度
         */
        public void drawStyle(Graphics g,
                              int defaultX, int sheX, int redX,
                              int defaultY, int sheY, int redY,
                              int defaultWidth, int sheWidth, int redWidth,
                              int defaultHeight, int sheHeight, int redHeight) {
            int x, y, width, height;

            switch (Util.SwitchUI) {
                case 0:
                    x = defaultX;
                    y = defaultY;
                    width = defaultWidth;
                    height = defaultHeight;
                    break;
                case 1:
                    x = sheX;
                    y = sheY;
                    width = sheWidth;
                    height = sheHeight;
                    break;
                default:
                    x = redX;
                    y = redY;
                    width = redWidth;
                    height = redHeight;
                    break;
            }

            Juitil.ImngBack(g, get(), x, y, width, height, 1);
        }

        /**
         * 简洁的样式感知绘制方法，为每个UI样式选择对应的参数
         * @param g 图形对象
         * @param x 根据UI样式选择的X坐标数组 [默认样式X, She样式X, Red样式X]
         * @param y 根据UI样式选择的Y坐标数组 [默认样式Y, She样式Y, Red样式Y]
         * @param w 根据UI样式选择的宽度数组 [默认样式宽度, She样式宽度, Red样式宽度]
         * @param h 根据UI样式选择的高度数组 [默认样式高度, She样式高度, Red样式高度]
         */
        public void draw(Graphics g, int[] x, int[] y, int[] w, int[] h) {
            int styleId = Util.SwitchUI;
            if (styleId < 0 || styleId >= 3) styleId = 0;

            // 确保数组长度足够
            int xVal = (x != null && x.length > styleId) ? x[styleId] : 0;
            int yVal = (y != null && y.length > styleId) ? y[styleId] : 0;
            int wVal = (w != null && w.length > styleId) ? w[styleId] : 59;
            int hVal = (h != null && h.length > styleId) ? h[styleId] : 58;

            Juitil.ImngBack(g, get(), xVal, yVal, wVal, hVal, 1);
        }

        /**
         * 绘制一行等间距的多个相同UI元素，支持为每种UI样式指定不同的基础坐标和间距
         * @param g 图形对象
         * @param x 不同UI样式的X起始坐标数组: [默认样式X, She样式X, Red样式X]
         * @param y 不同UI样式的Y坐标数组: [默认样式Y, She样式Y, Red样式Y]
         * @param width 宽度
         * @param height 高度
         * @param count 数量
         * @param spacing 不同UI样式的间距数组: [默认样式间距, She样式间距, Red样式间距]
         */
        public void drawRow(Graphics g, int[] x, int[] y, int width, int height, int count, int[] spacing) {
            // 获取当前UI样式
            int styleId = Util.SwitchUI;
            if (styleId < 0 || styleId >= 3) styleId = 0;
            // 确保坐标和间距数组长度足够
            int xPos = (x != null && x.length > styleId) ? x[styleId] : 0;
            int yPos = (y != null && y.length > styleId) ? y[styleId] : 0;
            int space = (spacing != null && spacing.length > styleId) ? spacing[styleId] : 0;

            // 根据当前UI样式获取坐标调整
            int[] adjustment = STYLE_ADJUSTMENTS[styleId];

            // 绘制每个元素
            int currentX = xPos;
            for (int i = 0; i < count; i++) {
                // 应用当前样式的坐标和大小调整
                int adjX = currentX + adjustment[0];
                int adjY = yPos + adjustment[1];
                int adjWidth = width + adjustment[2];
                int adjHeight = height + adjustment[3];
                // 绘制
                Juitil.ImngBack(g, get(), adjX, adjY, adjWidth, adjHeight, 1);
                // 更新下一个元素的起始X坐标
                currentX += width + space;
            }
        }

        /**
         * 绘制一行等间距的多个相同UI元素，支持为每种UI样式指定不同的基础Y坐标
         * @param g 图形对象
         * @param startX 起始X坐标
         * @param y 不同UI样式的Y坐标数组: [默认样式Y, She样式Y, Red样式Y]
         * @param width 宽度
         * @param height 高度
         * @param count 数量
         * @param spacing 基础间距
         */
        public void drawRow(Graphics g, int startX, int[] y, int width, int height, int count, int spacing) {
            // 获取当前UI样式
            int styleId = Util.SwitchUI;
            if (styleId < 0 || styleId >= 3) styleId = 0;

            // 确保Y坐标数组长度足够
            int yPos = (y != null && y.length > styleId) ? y[styleId] : 0;


            // 根据当前UI样式获取间距调整和坐标调整
            int spacingAdjust = (styleId == 1) ? 1 : 0; // She样式增加1像素间距
            int[] adjustment = STYLE_ADJUSTMENTS[styleId];

            // 绘制每个元素
            int currentX = startX;
            for (int i = 0; i < count; i++) {
                // 应用当前样式的坐标和大小调整
                int adjX = currentX + adjustment[0];
                int adjY = yPos + adjustment[1];
                int adjWidth = width + adjustment[2];
                int adjHeight = height + adjustment[3];

                // 绘制
                Juitil.ImngBack(g, get(), adjX, adjY, adjWidth, adjHeight, 1);

                // 更新下一个元素的起始X坐标
                currentX += width + spacing + spacingAdjust;
            }
        }

        /**
         * 完全自定义绘制方法，适用于不同UI样式下的完全自定义
         * @param g 图形对象
         * @param defaultX 默认样式的X坐标
         * @param defaultY 默认样式的Y坐标
         * @param defaultWidth 默认样式的宽度
         * @param defaultHeight 默认样式的高度
         * @param sheX She样式的X坐标
         * @param sheY She样式的Y坐标
         * @param sheWidth She样式的宽度
         * @param sheHeight She样式的高度
         * @param redX Red样式的X坐标
         * @param redY Red样式的Y坐标
         * @param redWidth Red样式的宽度
         * @param redHeight Red样式的高度
         */
        public void drawCustom(Graphics g,
                               int defaultX, int defaultY, int defaultWidth, int defaultHeight,
                               int sheX, int sheY, int sheWidth, int sheHeight,
                               int redX, int redY, int redWidth, int redHeight) {
            int x, y, width, height;

            switch (Util.SwitchUI) {
                case 0:
                    x = defaultX;
                    y = defaultY;
                    width = defaultWidth;
                    height = defaultHeight;
                    break;
                case 1:
                    x = sheX;
                    y = sheY;
                    width = sheWidth;
                    height = sheHeight;
                    break;
                default:
                    x = redX;
                    y = redY;
                    width = redWidth;
                    height = redHeight;
                    break;
            }

            Juitil.ImngBack(g, get(), x, y, width, height, 1);
        }

        /**
         * 绘制一个UI元素，明确指定每种UI样式的参数
         * @param g 图形对象
         * @param params 包含各UI样式参数的二维数组：
         *               params[0] = [x, y, width, height] 默认样式
         *               params[1] = [x, y, width, height] She样式
         *               params[2] = [x, y, width, height] Red样式
         */
        public void drawWithStyleParams(Graphics g, int[][] params) {
            int styleId = Util.SwitchUI;
            if (styleId >= 0 && styleId < params.length && params[styleId].length >= 4) {
                int[] param = params[styleId];
                Juitil.ImngBack(g, get(), param[0], param[1], param[2], param[3], 1);
            } else {
                // 使用默认样式
                int[] param = params[0];
                Juitil.ImngBack(g, get(), param[0], param[1], param[2], param[3], 1);
            }
        }

        /**
         * 绘制一行等间距的多个相同UI元素，根据UI样式自动调整位置和大小
         * @param g 图形对象
         * @param startX 起始X坐标
         * @param y Y坐标
         * @param width 宽度
         * @param height 高度
         * @param count 数量
         * @param spacing 基础间距
         */
        public void drawRow(Graphics g, int startX, int y, int width, int height, int count, int spacing) {
            // 创建三种UI样式的参数数组
            int[][][] styleParams = new int[3][count][4];  // [styleId][itemIndex][x,y,w,h]

            // 计算每种样式的参数
            for (int style = 0; style < 3; style++) {
                // 根据样式获取间距调整
                int spacingAdjust = (style == 1) ? 1 : 0; // She样式增加1像素间距
                int currentX = startX;

                for (int i = 0; i < count; i++) {
                    // 应用当前样式的坐标和大小调整
                    int[] adjustment = STYLE_ADJUSTMENTS[style];

                    styleParams[style][i][0] = currentX + adjustment[0]; // x
                    styleParams[style][i][1] = y + adjustment[1];       // y
                    styleParams[style][i][2] = width + adjustment[2];   // width
                    styleParams[style][i][3] = height + adjustment[3];  // height

                    // 更新下一个元素的起始X坐标
                    currentX += width + spacing + spacingAdjust;
                }
            }

            // 根据当前UI样式绘制
            int styleId = Util.SwitchUI;
            for (int i = 0; i < count; i++) {
                int[] params = styleParams[styleId < 3 ? styleId : 0][i];
                Juitil.ImngBack(g, get(), params[0], params[1], params[2], params[3], 1);
            }
        }

        /**
         * 绘制一列等间距的多个相同UI元素，根据UI样式自动调整位置和大小
         * @param g 图形对象
         * @param x X坐标
         * @param startY 起始Y坐标
         * @param width 宽度
         * @param height 高度
         * @param count 数量
         * @param spacing 基础间距
         */
        public void drawColumn(Graphics g, int x, int startY, int width, int height, int count, int spacing) {
            // 创建三种UI样式的参数数组
            int[][][] styleParams = new int[3][count][4];  // [styleId][itemIndex][x,y,w,h]

            // 计算每种样式的参数
            for (int style = 0; style < 3; style++) {
                // 根据样式获取间距调整
                int spacingAdjust = (style == 1) ? 1 : 0; // She样式增加1像素间距
                int currentY = startY;

                for (int i = 0; i < count; i++) {
                    // 应用当前样式的坐标和大小调整
                    int[] adjustment = STYLE_ADJUSTMENTS[style];

                    styleParams[style][i][0] = x + adjustment[0];       // x
                    styleParams[style][i][1] = currentY + adjustment[1]; // y
                    styleParams[style][i][2] = width + adjustment[2];   // width
                    styleParams[style][i][3] = height + adjustment[3];  // height

                    // 更新下一个元素的起始Y坐标
                    currentY += height + spacing + spacingAdjust;
                }
            }

            // 根据当前UI样式绘制
            int styleId = Util.SwitchUI;
            for (int i = 0; i < count; i++) {
                int[] params = styleParams[styleId < 3 ? styleId : 0][i];
                Juitil.ImngBack(g, get(), params[0], params[1], params[2], params[3], 1);
            }
        }

        /**
         * 绘制一个网格中的多个相同UI元素，根据UI样式自动调整位置和大小
         * @param g 图形对象
         * @param startX 起始X坐标
         * @param startY 起始Y坐标
         * @param width 宽度
         * @param height 高度
         * @param rows 行数
         * @param cols 列数
         * @param spacingX 基础X方向间距
         * @param spacingY 基础Y方向间距
         */
        public void drawGrid(Graphics g, int startX, int startY, int width, int height,
                             int rows, int cols, int spacingX, int spacingY) {
            // 创建三种UI样式的参数数组
            int totalItems = rows * cols;
            int[][][] styleParams = new int[3][totalItems][4];  // [styleId][itemIndex][x,y,w,h]

            // 计算每种样式的参数
            for (int style = 0; style < 3; style++) {
                // 根据样式获取间距调整
                int spacingXAdjust = (style == 1) ? 1 : 0; // She样式增加1像素X间距
                int spacingYAdjust = (style == 1) ? 1 : 0; // She样式增加1像素Y间距

                int index = 0;
                for (int row = 0; row < rows; row++) {
                    for (int col = 0; col < cols; col++) {
                        // 计算当前元素的基础坐标
                        int baseX = startX + col * (width + spacingX + spacingXAdjust);
                        int baseY = startY + row * (height + spacingY + spacingYAdjust);

                        // 应用当前样式的坐标和大小调整
                        int[] adjustment = STYLE_ADJUSTMENTS[style];

                        styleParams[style][index][0] = baseX + adjustment[0]; // x
                        styleParams[style][index][1] = baseY + adjustment[1]; // y
                        styleParams[style][index][2] = width + adjustment[2]; // width
                        styleParams[style][index][3] = height + adjustment[3]; // height

                        index++;
                    }
                }
            }

            // 根据当前UI样式绘制
            int styleId = Util.SwitchUI;
            for (int i = 0; i < totalItems; i++) {
                int[] params = styleParams[styleId < 3 ? styleId : 0][i];
                Juitil.ImngBack(g, get(), params[0], params[1], params[2], params[3], 1);
            }
        }

        private Position getPosition(JPanel panel) {
            String panelName = panel.getClass().getSimpleName();
            Map<String, Map<Integer, Position>> resourceConfig = CONFIG.get(name());

            if (resourceConfig == null) {
                // 资源类型不存在，使用默认位置
                return new Position(350, 250, 100, 100);
            }

            // 先检查此面板是否有直接配置
            Map<Integer, Position> panelConfig = resourceConfig.get(panelName);

            // 如果没有直接配置，查找是否有包含此面板名称的共享配置
            if (panelConfig == null) {
                // 查找共享配置
                for (Map.Entry<String, Map<Integer, Position>> entry : resourceConfig.entrySet()) {
                    String configKey = entry.getKey();
                    // 检查配置键是否为共享配置（以 "shared:" 开头）
                    if (configKey.startsWith("shared:")) {
                        // 获取面板列表
                        String[] panels = configKey.substring(7).split(",");
                        for (String p : panels) {
                            if (p.trim().equals(panelName)) {
                                // 找到了包含当前面板的共享配置
                                panelConfig = entry.getValue();
                                break;
                            }
                        }
                        if (panelConfig != null) break;
                    }
                }
            }

            // 如果仍然没有找到配置，使用默认配置
            if (panelConfig == null) {
                panelConfig = resourceConfig.get("default");
            }

            if (panelConfig == null) {
                // 面板配置不存在，使用默认位置
                return new Position(350, 250, 100, 100);
            }

            // 获取当前UI样式的位置
            Position position = panelConfig.get(Util.SwitchUI);
            if (position == null) {
                return new Position(350, 250, 100, 100);
            }

            return position;
        }
    }

    /**
     * 应用样式到按钮数组
     * @param panelName 面板名称
     * @param buttons 按钮数组
     * @param componentIdPrefix 组件ID前缀
     */
    public static void applyStylesToButtons(String panelName, MoBanBtn[] buttons, String componentIdPrefix) {
        for (int i = 0; i < buttons.length; i++) {
            if (buttons[i] != null) {
                ComponentStyle style = getComponentStyle(panelName, componentIdPrefix + "_" + i);
                style.applyToButton(buttons[i]);
            }
        }
    }

    /**
     * 应用样式到标签数组
     * @param panelName 面板名称
     * @param labels 标签数组
     * @param componentIdPrefix 组件ID前缀
     */
    public static void applyStylesToLabels(String panelName, JLabel[] labels, String componentIdPrefix) {
        for (int i = 0; i < labels.length; i++) {
            if (labels[i] != null) {
                ComponentStyle style = getComponentStyle(panelName, componentIdPrefix + "_" + i);
                style.applyToLabel(labels[i]);
            }
        }
    }

    /**
     * 应用样式到文本框数组
     * @param panelName 面板名称
     * @param textFields 文本框数组
     * @param componentIdPrefix 组件ID前缀
     */
    public static void applyStylesToTextFields(String panelName, JTextField[] textFields, String componentIdPrefix) {
        for (int i = 0; i < textFields.length; i++) {
            if (textFields[i] != null) {
                ComponentStyle style = getComponentStyle(panelName, componentIdPrefix + "_" + i);
                style.applyToTextField(textFields[i]);
            }
        }
    }
}