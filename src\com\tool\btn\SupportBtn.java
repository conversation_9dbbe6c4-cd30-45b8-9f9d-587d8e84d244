package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import org.come.Jpanel.SupportListJpanel;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class SupportBtn extends MoBanBtn {
    //0上 1下 2顶 3底
	private SupportListJpanel jpanel;
	public int p;
	public SupportBtn(String iconpath, int type, String text,int p, SupportListJpanel jpanel) {
		// TODO Auto-generated constructor stub
		super(iconpath, type, UIUtils.COLOR_BTNTEXT);
		this.setText(text);
		setFont(UIUtils.TEXT_FONT);
		this.p=p;
		this.jpanel=jpanel;
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	public SupportBtn(String iconpath, int type,int p, SupportListJpanel jpanel) {
		// TODO Auto-generated constructor stub
		super(iconpath, type,0,"","");
		setFont(UIUtils.TEXT_FONT);
		this.p=p;
		this.jpanel=jpanel;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		//获取当前选中的位置
		int w=jpanel.getListpet().getSelectedIndex();
		if (w==-1) {
			return;
		}
		RoleData data=RoleData.getRoleData();
		List<String> list;
		//0上 1下 2顶 3底
		if (p==0) {
			list=data.CHelpBb(w, w-1);
		}else if (p==1) {
			list=data.CHelpBb(w, w+1);
		}else if (p==2) {
			list=data.CHelpBb(w, 0);
		}else {
			list=data.CHelpBb(w, 99);
		}
		if (list!=null) {
			jpanel.init(list);
		}
		
	}

}
