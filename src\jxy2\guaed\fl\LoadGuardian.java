package jxy2.guaed.fl;

import com.tool.btn.MoBanBtn;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class LoadGuardian extends MoBanBtn {
    public LoadGuardianJPanel loadGuardianJPanel;
    public LoadGuardian(String iconpath, int type, Color[] colors, Font font, String text, LoadGuardianJPanel loadGuardianJPanel, int index, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, colors, prompt);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.loadGuardianJPanel = loadGuardianJPanel;
        this.index = index;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
     if (index==7){
         loadGuardianJPanel.lingFan(false);
        }else if (index==8){
         loadGuardianJPanel.lingFan(true);
        }
    }
}
