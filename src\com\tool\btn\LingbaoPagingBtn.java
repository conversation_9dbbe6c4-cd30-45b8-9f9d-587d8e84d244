package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.model.Lingbao;
import org.come.until.Util;

import com.tool.role.ExpUtil;
import com.tool.role.RoleLingFa;
import com.tool.tcpimg.UIUtils;

import come.tool.JDialog.TiShiUtil;

public class LingbaoPagingBtn extends MoBanBtn {

    private int btnname;

    public LingbaoPagingBtn(String iconpath, int type, int btnname, String text) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.btnname = btnname;
        if (text != null) {
            this.setText(text);
            setFont(UIUtils.TEXT_FONT1);
            setForeground(Color.yellow);
            setVerticalTextPosition(SwingConstants.CENTER);
            setHorizontalTextPosition(SwingConstants.CENTER);
        }
    }
    public LingbaoPagingBtn(String iconpath, int type, Color[] colors, Font font, int btnname, String text) {
        super(iconpath, type, colors);
        // TODO Auto-generated constructor stub
        this.btnname = btnname;
        if (text != null) {
            this.setText(text);
            setFont(font);
            setVerticalTextPosition(SwingConstants.CENTER);
            setHorizontalTextPosition(SwingConstants.CENTER);
        }
    }
	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		//0灵宝上一页 1灵宝下一页 2法宝上一页 3法宝下一页 
		//4转换   5删除  6擅长  7详情 8加锁 9解锁 10突破
		switch (btnname) {
		case 0:
			//0灵宝上一页 
			RoleLingFa.getRoleLingFa().lingFan(false);
			break;
		case 1:
			//1灵宝下一页
			RoleLingFa.getRoleLingFa().lingFan(true);
			break;
		case 2:
			//2法宝上一页
			RoleLingFa.getRoleLingFa().faFan(false);
			break;
		case 3:
			//3法宝下一页 
			RoleLingFa.getRoleLingFa().faFan(true);
			break;
		case 4:
			//4转换
			Lingbao lingbao=RoleLingFa.getRoleLingFa().getChoseBao();
			if (lingbao!=null) {
				 if (lingbao!=null) {
					 OptionsJframe.getOptionsJframe().getOptionsJpanel().
					 showBox(TiShiUtil.lingkang, lingbao,"#Y确定要将花  #G200000 #Y银两随机灵宝附加抗性吗??");
				 }
			}else {
				ZhuFrame.getZhuJpanel().addPrompt("先选中需要转换抗性的灵宝或者法宝");
			}
			break;
		case 5:
		    /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
//			  5删除 
			lingbao=RoleLingFa.getRoleLingFa().getChoseBao();
			if (lingbao!=null) {
               if (lingbao.getBaotype().equals("法宝")) {
            	   ZhuFrame.getZhuJpanel().addPrompt2("不能删除法宝");
            	   return;
			   }
				 if (lingbao.getFushis()==null||lingbao.getFushis().equals("")) {
					 OptionsJframe.getOptionsJframe().getOptionsJpanel().
					 showBox(TiShiUtil.lingDiscatd, lingbao,"#Y确定要将该灵宝删除吗？？？");
				}else {
					ZhuFrame.getZhuJpanel().addPrompt2("删除前先卸下符石");
				}
			 
			}else {
				ZhuFrame.getZhuJpanel().addPrompt("先选中需要删除的灵宝或者法宝");
			}
			break;
		case 6:
			
			break;
		case 10:
		    /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
			lingbao=RoleLingFa.getRoleLingFa().getChoseBao();
			if (lingbao!=null) {
			int lvl=lingbao.getLingbaolvl().intValue();
		    long exp=lingbao.getLingbaoexe().longValue();
		    long maxexp=ExpUtil.LFExp(lvl);
			if (lvl%30!=0||exp<maxexp||lvl==0) {
				ZhuFrame.getZhuJpanel().addPrompt2("还未达到突破条件");
				return;
			}
			StringBuffer buffer=new StringBuffer();
			buffer.append("#Y你是否要消耗#R");
			buffer.append(lvl/5);
			buffer.append("#Y个灵宝天威印进行突破");
			OptionsJframe.getOptionsJframe().getOptionsJpanel().
			showBox(TiShiUtil.lingtupo, lingbao,buffer.toString());
			}else {
				ZhuFrame.getZhuJpanel().addPrompt("先选中需要突破的灵宝或者法宝");
			}
			break;
		}
	}
    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }


}
