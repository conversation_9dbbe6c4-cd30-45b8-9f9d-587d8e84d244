package jxy2.qqiubook;

import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class QianqiuBookMouse extends TemplateMouseListener {
    // 定义常量高度
    private static final int COLLAPSED_HEIGHT = 63;  // 折叠高度
    private static final int EXPANDED_HEIGHT = 96;   // 展开高度
    
    private JList<QianqiuBookModel> qianqiuBookModelJList;
    
    public QianqiuBookMouse(JList<QianqiuBookModel> qianqiuBookModelJList) {
        this.qianqiuBookModelJList = qianqiuBookModelJList;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        // 获取点击位置相对于列表的坐标
        Point point = e.getPoint();
        int selectedIndex = qianqiuBookModelJList.locationToIndex(point);
        if (selectedIndex == -1) {
            return;  // 如果点击在空白处，直接返回
        }
        
        // 获取当前选中的模型
        QianqiuBookModel model = qianqiuBookModelJList.getModel().getElementAt(selectedIndex);
        if (model == null) {return;}

        // 检查是否点击了奖励图标
        JLabel rewardIcon = model.getLabRewardIcon();
        if (rewardIcon.isVisible()) {
            // 将点击坐标转换为奖励图标坐标系
            Point iconPoint = new Point(point);
            Rectangle cellBounds = qianqiuBookModelJList.getCellBounds(selectedIndex, selectedIndex);
            if (cellBounds != null) {
                iconPoint.y -= cellBounds.y;  // 转换为单元格内坐标
                // 检查是否点击在奖励图标上
                if (rewardIcon.getBounds().contains(iconPoint)) {
                    System.out.println("奖励内容：" + model.getNode().getReward());
                    return;  // 如果点击了奖励图标，不处理事件，让图标自己的事件处理器处理
                }
            }
        }
        
        // 获取当前高度并切换
        int currentHeight = model.getH();
        int newHeight = (currentHeight == COLLAPSED_HEIGHT) ? model.getNode().getId()==217?135:EXPANDED_HEIGHT : COLLAPSED_HEIGHT;

        // 更新高度
        QianqiuBookFrame.getQianqiuBookFrame().getQianqiuBookJPanel().refreshListHeight(selectedIndex, newHeight);
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
