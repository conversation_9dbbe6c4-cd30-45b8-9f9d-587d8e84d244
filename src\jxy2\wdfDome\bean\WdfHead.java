package jxy2.wdfDome.bean;

/**
 * WdfHead类用于定义一种头部信息
 * 该类包含用于标识、文件总和校验、偏移量以及一组WAS数据的信息
 */
public class WdfHead {
    private long flag; // 标志位，通常用于识别数据的特定版本或类型
    private long fileSum; // 文件总和，用于校验文件的完整性
    private long offset; // 偏移量，指出数据在文件中的起始位置
    private WasData[] wasDataList; // WAS数据列表，存储一系列WAS数据项


    public long getFlag() {
        return this.flag;
    }

    public void setFlag(long flag) {
        this.flag = flag;
    }

    public long getFileSum() {
        return this.fileSum;
    }

    public void setFileSum(long fileSum) {
        this.fileSum = fileSum;
    }

    public long getOffset() {
        return this.offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public WasData[] getWasDataList() {
        return this.wasDataList;
    }

    public void setWasDataList(WasData[] fileList) {
        this.wasDataList = fileList;
    }
}
