package jxy2.genius;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class GeniusBattleBtn extends MoBanBtn {
    public int typeBtn;//提示
    public GeniusBattleJPanel geniusBattleJPanel;
    public GeniusBattleBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, GeniusBattleJPanel geniusBattleJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.geniusBattleJPanel = geniusBattleJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (geniusBattleJPanel.getSignBtn().getText().equals("我要报名")){
            String serverMes = Agreement.getAgreement().GeniusUpAgreement("B|"+RoleData.getRoleData().getLoginResult().getRole_id());
            SendMessageUntil.toServer(serverMes);
        }else if (geniusBattleJPanel.getSignBtn().getText().equals("取消报名")){
            String serverMes = Agreement.getAgreement().GeniusUpAgreement("Q|"+RoleData.getRoleData().getLoginResult().getRole_id());
            SendMessageUntil.toServer(serverMes);
            geniusBattleJPanel.getSignBtn().setText("我要报名");
        }

    }
}
