package jxy2.guaed.fl;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.MountJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.MountJPanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.LoginResult;
import org.come.entity.Goodstable;
import org.come.entity.Mount;
import org.come.mouslisten.GoodsMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;
import java.util.Map;

public class LoadGuardianJPanel extends JPanel {
    private JLabel[] goodLabels = new JLabel[12];
    public JLabel[] mouseBorder = new JLabel[12];
    public int mountIdentification;
    public LoadGuardian left,//左移
            right;//右移
    public int yss=0;
    private boolean is= true;
    public LoadGuardianJPanel() {
        this.setPreferredSize(new Dimension(370, 170));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        for (int i = 0; i < goodLabels.length; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            int[] arr = {18+shop_x*51,23+shop_y*51};
            goodLabels[i] = new JLabel();
            goodLabels[i].setBounds(18+shop_x*51,23+shop_y*51, 50, 50);
            goodLabels[i].addMouseListener(new LoadGuardianMouse(i, this,arr));
            this.add(goodLabels[i]);
            mouseBorder[i] = new JLabel();
            mouseBorder[i].setBounds(18+shop_x*51,23+shop_y*51,50, 50);
            mouseBorder[i].setVisible(false);
            mouseBorder[i].setIcon(Juitil.tz157);
            this.add(mouseBorder[i]);
        }
        left = new LoadGuardian(ImgConstants.tz89, -1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "", this,7,"上一页");
        left.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz91,17,21,"defaut.wdf"));
        this.add(left);
        right =new LoadGuardian(ImgConstants.tz90, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "", this,8,"下一页");
        add(right);
        left.setBounds(258, 134, 17, 21);
        right.setBounds(258+27, 134, 17, 21);
    }

    public void lingFan(boolean type) {
        boolean is = yss*12  < goodLabels.length && goodLabels[yss*12] != null;
        right.setBtn(is?-1:1);
        left.setBtn(is?1:-1);

        if (type) {
            if (yss*12  < goodLabels.length && goodLabels[yss*12] != null ) {
                lingNumChange(yss+1);
                right.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz92,17,21,"defaut.wdf"));
                left.setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz89, "defaut.wdf"));
            }
        } else {

            if (yss > 0) {
                lingNumChange(yss - 1);
                left.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz91,17,21,"defaut.wdf"));
                right.setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz90, "defaut.wdf"));

            }
        }
    }
    public void Vis(boolean b) {is =b;}
    public void lingNumChange(int number) {
        yss = number;
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (is) {
            Juitil.ImngBack(g, Juitil.tz67, 0, 0, 340, 170, 1);
        }
        for (int i = 0; i < 12; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,17+shop_x*51,22+shop_y*51,51,51,1);
        }
        Juitil.ImngBack(g,Juitil.tz267,16,21,310,105,1);
        GoodsListFromServerUntil.drawGuardianOperationSuit(g, yss,16,21);

    }

    public JLabel[] getGoodLabels() {
        return goodLabels;
    }

    public void setGoodLabels(JLabel[] goodLabels) {
        this.goodLabels = goodLabels;
    }

    public JLabel[] getMouseBorder() {
        return mouseBorder;
    }

    public void setMouseBorder(JLabel[] mouseBorder) {
        this.mouseBorder = mouseBorder;
    }

    public boolean isIs() {
        return is;
    }

    public void setIs(boolean is) {
        this.is = is;
    }

    public static class LoadGuardianMouse extends TemplateMouseListener implements MouseListener {
        public int i;
        public LoadGuardianJPanel loadGuardianJPanel;
        public int[] xy;

        public LoadGuardianMouse(int i, LoadGuardianJPanel loadGuardianJPanel,int[] xy) {
            this.i = i;
            this.xy = xy;
            this.loadGuardianJPanel = loadGuardianJPanel;
        }

        @Override
        protected void specificMousePressed(MouseEvent e) {
            list.add((i)+"");
            Goodstable goodstable = GoodsListFromServerUntil.getGuardianOperationSuit(i,loadGuardianJPanel.yss);
            if (goodstable!=null&&Determine(goodstable)){
                GoodsData(goodstable);
                extracted();
            }

        }

        public static void GoodsData(Goodstable goodstable) {
            AsoulJPanel asoulJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getAsoulJPanel();
            RonglianJPanel ronglianJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel();
            MountJPanel mountjpanel = MountJframe.getMountjframe().getMountjpanel();
            ImageIcon icon = GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 48, 48);
            if (FuLingFrame.getFuLingFrame().isVisible()&&FuLingFrame.getFuLingFrame().getFuLingJPanel().getType()==0){
                asoulJPanel.setComponentVisible(false,icon);
                asoulJPanel.setGoodstable(goodstable);
            } else if (FuLingFrame.getFuLingFrame().isVisible()&&FuLingFrame.getFuLingFrame().getFuLingJPanel().getType()==1){
                switch (ronglianJPanel.getType()) {
                    case 2:
                        ronglianJPanel.setComponentVisible(false, icon);
                        ronglianJPanel.getGoodstables()[0] = goodstable;
                        break;
                    case 3:
                        ronglianJPanel.getGoodstables()[1] = goodstable;
                        ronglianJPanel.setComponentVisibleTow(false, icon);
                        RonglianJPanel.isvstop();
                        break;
                }
            }else if(OpenGuardianFrame.getOpenGuardianFrame().isVisible()){
                OpenGuardianFrame.getOpenGuardianFrame().getOpenGuardianJPanel().setGoodstable(goodstable);
                OpenGuardianFrame.getOpenGuardianFrame().getOpenGuardianJPanel().getGoodslabel().setIcon(icon);
                //解析物品的属性的条数
                int index= 0;
                String[] entries = goodstable.getValue().split("\\|");
                for (String entry : entries) {
                    String[] keyValue = entry.split("=");
                    if (keyValue.length != 2) {
                        continue; // 跳过格式错误的条目
                    }
                    String key = keyValue[0];
                    if (key.startsWith("耐久度") || key.startsWith("锻造等级")) {
                        continue; // 忽略特定的属性
                    }
                    index++;
                }
                OpenGuardianFrame.getOpenGuardianFrame().getOpenGuardianJPanel().setQuantityObtainable(index*120);
            }else {//给坐骑穿戴守护石
                LoginResult loginResult = RoleData.getRoleData().getLoginResult();
                int mountide = LoadGuardianFrame.getLoadGuardianFrame().getLoadGuardianJPanel().getMountIdentification();
                StringBuffer buffer = new StringBuffer();
                if (loginResult.getStid()==null||loginResult.getStid().isEmpty()){
                    loginResult.setStid(mountide+"_"+goodstable.getRgid());
                }else {
                    String[] st = loginResult.getStid().split("&");
                    boolean is = false;
                    for (String pair : st) {
                        String[] keyValue = pair.split("_", 2);
                        if (keyValue[0].equals(mountide+"")){
                            keyValue[1] = goodstable.getRgid().toString();
                            is = true;
                        }
                        if (buffer.length() != 0) {buffer.append("&");}
                        buffer.append(keyValue[0]).append("_").append(keyValue[1]);
                    }
                    if (!is){
                        if (buffer.length()!=0){buffer.append("&");}
                        buffer.append(mountide).append("_").append(goodstable.getRgid());
                    }
                    loginResult.setStid(buffer.toString());
                }
                mountjpanel.getGuardianStone()[mountide].setIcon(Juitil.tz323);
                String sendmes = Agreement.getAgreement().rolechangeAgreement("8"+ GsonUtil.getGsonUtil().getgson().toJson(loginResult));
                SendMessageUntil.toServer(sendmes);

                //刷新坐骑管制属性
                if (MountJPanel.ShstoneID(mountide)!=null&&mountjpanel.mountidlits.get(mountide)!=null) {
                    Mount mount = ZhuJpanel.getMountID(new BigDecimal(mountjpanel.mountidlits.get(mountide)));
                    if (mount!=null){
                        mount.setMountstone(MountJPanel.ShstoneID(mountide));
                        mountjpanel.getOkis()[mountide]  =  Juitil.number2Chinese(mount.getMountid()) + "·" + mount.getMountname();
                        String sendmess = Agreement.getAgreement().changeMountValue(GsonUtil.getGsonUtil().getgson().toJson(mount));
                        SendMessageUntil.toServer(sendmess);
                    }
                }
                    int weizh = 0;
                    for (int i = 0; i < GoodsListFromServerUntil.getGoodslist().length; i++) {
                        Goodstable goosd = GoodsListFromServerUntil.getGoodslist()[i];
                        if (goosd!=null&&goosd.getRgid().compareTo(goodstable.getRgid())==0){
                            weizh = i;
                            break;
                        }
                    }
                    goodstable.setStatus(1);
                    GoodsListFromServerUntil.fushis.put(goodstable.getRgid(), goodstable);
                    GoodsMouslisten.gooduse(goodstable, 0);
                    GoodsListFromServerUntil.Deleted(weizh);
                    ZhuFrame.getZhuJpanel().addPrompt2("守护石已镶入，选择坐骑即可使其获得加持。");
            }


        }


        @Override
        protected void specificMouseReleased(MouseEvent e) {
            FormsManagement.HideForm(142);
            FormsManagement.HideForm(24);
            list.remove(i+"");
        }

        @Override
        protected void specificMouseEntered(MouseEvent e) {

            Goodstable goodstable = GoodsListFromServerUntil.getGuardianOperationSuit(i,loadGuardianJPanel.yss);
            if (goodstable!=null){
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
                loadGuardianJPanel.getMouseBorder()[i].setVisible(true);
                for (int j = 0; j < loadGuardianJPanel.getMouseBorder().length; j++) {
                    if (j!=i){
                        loadGuardianJPanel.getMouseBorder()[j].setVisible(false);
                    }
                }
            }else {
                MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
            }
        }
        @Override
        protected void specificMouseExited(MouseEvent e) {
            Goodstable goodstable = GoodsListFromServerUntil.getGuardianOperationSuit(i,loadGuardianJPanel.yss);
            if (goodstable!=null){
                loadGuardianJPanel.getMouseBorder()[i].setVisible(false);
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }
        }

        @Override
        protected void specificMouseDragged(MouseEvent e) {

        }

        @Override
        protected void specificMouseMoved(MouseEvent e) {

        }


        private void extracted() {
            loadGuardianJPanel.getGoodLabels()[i].setBorder(BorderFactory.createLineBorder(Color.red));
            for (int j = 0; j < loadGuardianJPanel.getGoodLabels().length; j++) {
                if (j!=i){
                    loadGuardianJPanel.getGoodLabels()[j].setBorder(BorderFactory.createEmptyBorder());
                }
            }
        }
    }

    public int getMountIdentification() {
        return mountIdentification;
    }

    public void setMountIdentification(int mountIdentification) {
        this.mountIdentification = mountIdentification;
    }


    /**
     * 根据选中的标识找到中天中对应坐骑数据
     * Find the corresponding mount data in Zhongtian based on the selected identifier
     * @param selectIdentification 选中坐骑标识
     * @return Mount
     */
    public static Mount QueryMountData(int selectIdentification){
        if (RoleData.getRoleData().getLoginResult().getMountid()==null)return null;
        String[] guardMount = RoleData.getRoleData().getLoginResult().getMountid().split("\\|");
        for (String s : guardMount) {
            String[] split = s.split("=");
            int type = Integer.parseInt(split[0].split("-")[0]);
            if (type==4){
                String[] zhi = split[1].split("_");
                for (int j = 0; j < zhi.length; j++) {
                    if (selectIdentification==j){
                        return ZhuJpanel.getMountID(new BigDecimal(zhi[j]));
                    }
                }
            }
        }
        return null;
    }

    /**
     * 判断相同属性
     * @param goodstable
     * @return
     */
    public static boolean Determine(Goodstable goodstable){
        RonglianJPanel ronglianJPanel = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel();
        boolean hasMatchingAttribute = false;
        if (ronglianJPanel.getType()==3){
            Goodstable good = ronglianJPanel.getGoodstables()[0];
            if (good!=null&&goodstable!=good){
                Map<String, String> mainAttributes = GoodsListFromServerUntil.parseAttributes(good.getValue());
                // 解析当前物品的属性
                Map<String, String> currentAttributes = GoodsListFromServerUntil.parseAttributes(goodstable.getValue());
                // 检查是否有相同的属性词条
                int matchCount = 0;
                for (Map.Entry<String, String> entry : mainAttributes.entrySet()) {
                    if (currentAttributes.containsKey(entry.getKey())) {
                        matchCount++;
                        if (matchCount >= 1) {  // 至少有1个相同属性
                            hasMatchingAttribute = true;
                            break;
                        }
                    }
                }
            }
        }else {
            hasMatchingAttribute = true;
        }
        if (FuLingFrame.getFuLingFrame().getFuLingJPanel().getType()==0){
            hasMatchingAttribute =true;
        }
        if (!hasMatchingAttribute){
            ZhuFrame.getZhuJpanel().addPrompt("#R选择的副守护石没有所需的属性");
        }

        RonglianBtn.activeButtonId = 0;
        return hasMatchingAttribute;
    }
}
