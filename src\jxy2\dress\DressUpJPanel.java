package jxy2.dress;

import com.tool.role.RoleData;
import com.tool.role.RoleLingFa;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.TestpackJframe;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.LoginResult;
import org.come.bean.PathPoint;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
/**
* 换装设置面板 编号:132
* <AUTHOR>
* @date 2024/11/6 下午7:03
*/

public class DressUpJPanel extends JPanel {
    private JLabel[] choseGoodsJLabel = new JLabel[12];
    private JLabel[] choselingbaosJLabel = new JLabel[3];
    private JLabel[] choseGoodsimg = new JLabel[12];
    private Map<Integer,JLabel[]> choseGoodsJLabelMap = new HashMap<>();//存放装备
    private Map<Integer,JLabel[]> choseLingbaosJLabelMap = new HashMap<>();//存放灵宝
    private Map<Integer,String[]> storeAttributes = new HashMap<>();//存放属性
    private String[] currentAttribute = {"","","","",""};//属性展示
    private String[] xcAttribute = {"","","","",""};//小成属性设置
    private String[] dcAttribute = {"","","","",""};//大成属性设置
    private DressUpBtn[] btnBasis = new DressUpBtn[5];
    private DressUpBtn blacBuy;
    private JLabel starcard,//星卡面板
            xpskill,//星盘技能面板
            tycclick,//天演策技能面板;
            lawlick; //法门技能;
    private int type =0;
    private DressUpBtn[] btnRich = new DressUpBtn[2];
    public DressUpJPanel() {
        this.setPreferredSize(new Dimension(462, 583));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,132,462);
        for (int i = 0; i < choseGoodsimg.length; i++) {
            choseGoodsimg[i] = new JLabel();
            PathPoint pathPoint = TestpackJframe.getTestpackJframe().getTestpackJapnel().path(i);
            ImageIcon currentImage = CutButtonImage.getWdfPng("0x6FEB8" + (i+493),49,49,"defaut.wdf");
            choseGoodsimg[i].setIcon(currentImage);
            choseGoodsimg[i].setBounds(pathPoint.getX()+40, pathPoint.getY()+40, 49, 49);
            this.add(choseGoodsimg[i]);
        }
        // 初始化装备jlabel
        for (int i = 0; i < choseGoodsJLabel.length; i++) {
            choseGoodsJLabel[i] = new JLabel();
            PathPoint pathPoint = TestpackJframe.getTestpackJframe().getTestpackJapnel().path(i);
            choseGoodsJLabel[i].setBounds(pathPoint.getX()+40, pathPoint.getY()+40, 49, 49);
            choseGoodsJLabel[i].addMouseListener(new DressUpMouse(i,this,0));
            this.add(choseGoodsJLabel[i]);
        }

        for (int i = 0; i < choselingbaosJLabel.length; i++) {
            choselingbaosJLabel[i] = new JLabel();
            choselingbaosJLabel[0].setBounds(340,441,39,39);
            choselingbaosJLabel[i].addMouseListener(new DressUpMouse(i,this,1));
            add(choselingbaosJLabel[i]);
        }

        String[] basis = {"套装一","套装二","套装三","套装四","套装五"};
        for (int i = 0; i < btnBasis.length; i++) {
            // 基础属性
            btnBasis[i] = new DressUpBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, basis[i], this,i,"");
            btnBasis[i].btnchange(i==0?2:0);
            btnBasis[i].setBounds(56+i*72, 46, 66, 24);
            this.add(btnBasis[i]);
        }
        blacBuy = new DressUpBtn(ImgConstants.tz77, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "保存套装", this,5,"");
        blacBuy.setBounds(198, 521, 104, 38);
        this.add(blacBuy);

        starcard = new JLabel();
        starcard.setBounds(340,352,39,39);
        starcard.addMouseListener(new DressUpMouse(0,this,3));
        add(starcard);

        xpskill = TeststateJpanel.GJpanelText(UIUtils.COLOR_Fighting,UIUtils.FZCY_HY14);
        xpskill.addMouseListener(new DressUpMouse(0,this,2));
        xpskill.setBounds(114,294+29*6,120,39);
        add(xpskill);

        tycclick = TeststateJpanel.GJpanelText(UIUtils.COLOR_Fighting,UIUtils.FZCY_HY14);
        tycclick.addMouseListener(new DressUpMouse(0,this,-1));
        tycclick.setBounds(114,294+29*4,120,39);
        add(tycclick);

        lawlick = TeststateJpanel.GJpanelText(UIUtils.COLOR_Fighting,UIUtils.FZCY_HY14);
        lawlick.addMouseListener(new DressUpMouse(0,this,4));
        lawlick.setBounds(114,294+29*5,120,39);
        add(lawlick);

        for (int i = 0; i < btnRich.length; i++) {
            btnRich[i] = new DressUpBtn(ImgConstants.tz80, 1, UIUtils.COLOR_BTNTEXT, UIUtils.TEXT_FONT,"详",this, -1,i+"_");
            btnRich[i].setBounds(209, 330+i*30, 18, 18);
            this.add(btnRich[i]);
        }


        initData(true,0);
    }

    /**初始化装备信息数据*/
    public void initData(boolean is,int type){
        LoginResult result = RoleData.getRoleData().getLoginResult();
            dcAttribute[type] = "缺失";
            xcAttribute[type] = "缺失";
            btnBasis[type].handleButtonClick(type);
            this.type= type;
            boolean isos = false;
        if (is){
            choseGoodsJLabelMap.put(type,choseGoodsJLabel);
            choseLingbaosJLabelMap.put(type,choselingbaosJLabel);
        }
            for (Integer key : choseGoodsJLabelMap.keySet()) {
                if (key != type) {
                    JLabel[] jLabels = choseGoodsJLabelMap.get(key);
                    if (jLabels != null) {
                        for (JLabel jLabel : jLabels) {
                            jLabel.setIcon(null);
                            remove(jLabel);
                        }
                    }
                }
            }
            for (Integer key : choseLingbaosJLabelMap.keySet()) {
                if (key != type) {
                    JLabel[] jLabels = choseLingbaosJLabelMap.get(key);
                    if (jLabels != null) {
                        for (JLabel jLabel : jLabels) {
                            jLabel.setIcon(null);
                            remove(jLabel);
                        }
                    }
                }
            }



        if (result.getOneclick()!=null&& !result.getOneclick().isEmpty()){
            String[] vs = result.getOneclick().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                String[] goodid = vss[1].split("\\|");
                if (Integer.parseInt(vss[0]) == type) {
                    for (int i = 0; i < goodid.length; i++) {
                        //取装备位置
                        int path = Integer.parseInt(goodid[i].split("_")[0]);
                        //取物品ID
                        Goodstable goodstable = GoodsListFromServerUntil.getRgid(new BigDecimal(goodid[i].split("_")[1]));
                        if (goodstable != null&&choseGoodsJLabelMap.get(type)!=null) {
                            choseGoodsJLabelMap.get(type)[path].setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 49, 49));
                            add(choseGoodsJLabelMap.get(type)[path]);
                            isos = true;
                        }
                    }
                }
            }
        }


        //获取灵宝数据
        if (result.getClicklingbao()!=null && !result.getClicklingbao().isEmpty()){
            String[] vs = result.getClicklingbao().split("&");
            for (String v : vs) {
                String[] vss = v.split("=");
                String[] lingbaoid = vss[1].split("\\|");
                if (Integer.parseInt(vss[0]) == type) {
                    for (int i = 0; i < lingbaoid.length; i++) {
                        int path = Integer.parseInt(lingbaoid[i].split("_")[0]);
                        Lingbao lingbao = RoleLingFa.getRoleLingFa().DressUp(new BigDecimal(lingbaoid[i].split("_")[1]));
                        if (lingbao!=null&&choseLingbaosJLabelMap.get(type)!=null){
                            choseLingbaosJLabelMap.get(type)[path].setIcon(RoleLingFa.lingbaoimg(lingbao.getSkin(),39,39));
                            if (path==0){
                                choseLingbaosJLabelMap.get(type)[path].setBounds(340,406,39,39);
                            } else if (path==1) {
                                choseLingbaosJLabelMap.get(type)[1].setBounds(315,406+53,39,39);
                            }else {
                                choseLingbaosJLabelMap.get(type)[2].setBounds(340+29,406+53,39,39);
                            }
                            add(choseLingbaosJLabelMap.get(type)[path]);
                            isos = true;
                        }
                    }
                }
            }
        }

        //获取角色当前帮派小成属性
        if (result.getGangname()!=null&&result.getXcpractice()!=null&&!result.getGangname().isEmpty()&&!result.getXcpractice().isEmpty()){
            String[] vs = result.getXcpractice().split("&");
          for (String vc : vs){
                String[] vss = vc.split("@");
                  if (vss.length<2)return;
              //取编号
              int path = Integer.parseInt(vss[0]);
              //取值
              String zhi = vss[1].substring(0,3);

              if (type==path){
                  xcAttribute[path] = zhi;
                  isos = true;
              }else {
                  xcAttribute[path] = "";
              }
          }
        }
        //获取角色当前帮派大成属性
        if (result.getGangname()!=null&&!result.getGangname().isEmpty()&&result.getDcpractice()!=null&&!result.getDcpractice().isEmpty()){
            String[] vs = result.getDcpractice().split("&");
          for (String vc : vs){
                String[] vss = vc.split("@");
              //取编号
              int path = Integer.parseInt(vss[0]);
              //取值
              String zhi = vss[1].substring(0,3);
              if (type==path){
                  dcAttribute[path] = zhi;
                  isos = true;
              }else {
                  dcAttribute[path] = "";
              }
          }
        }

        //获取角色当前加点属性

        if (result.getStoresa()!=null&&!result.getStoresa().isEmpty()){
            int b = 0;
            String[] vs = result.getStoresa().split("@");
            for (String v : vs){
                String[] vss = v.split("·");
                //取编号
                int path = Integer.parseInt(vss[0]);
                //取值
                String[] zhi = vss[1].split("\\|");
                for (String s : zhi) {
                    String[] vd = s.split("&");
                    int sum = Integer.parseInt(vd[1]);
                    String xh = vd[0];
                    if (xh.startsWith("D_Y") && sum == 1) {
                        b = 1;
                    } else if (xh.startsWith("D_R") && sum == 1) {
                        b = 2;
                    } else if (xh.startsWith("D_S") && sum == 1) {
                        b = 3;
                    }
                    if (type == path && sum == 1) {
                        currentAttribute[path] = b == 2 ? "第二套属性" : b == 3 ? "第三套属性" : "第一套属性";
                        isos = true;
                    }
                }
            }
        }


        if (result.getStatcard()!=null&&!result.getStatcard().isEmpty()) {
            String[] pairs = result.getStatcard().split("&");
            for (String v : pairs) {
                String[] vss = v.split("=");
                if (Integer.parseInt(vss[0]) == type) {
                    Goodstable goodstable = GoodsListFromServerUntil.getRgid(new BigDecimal(vss[1]));
                    if (goodstable != null) {
                        starcard.setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),49,49));
                        isos = true;
                        break;
                    }
                }else {
                    starcard.setIcon(null);
                }
            }
        }

        //星盘技能数据
        if (result.getXpskill()!=null&&!result.getXpskill().isEmpty()){
            String[] pairs = result.getXpskill().split("&");
            for (String v : pairs) {
                String[] vss = v.split("·");
                if (Integer.parseInt(vss[0]) == type) {
                    Skill skill = UserMessUntil.getSkillId(vss[1].split("_")[0]);
                    if (skill!=null){
                        xpskill.setText(skill.getSkillname());
                        isos = true;
                        break;
                    }
                }else {
                    xpskill.setText("");
                }
            }

        }
        //天演策技能数据
        if (result.getTycclick()!=null&&!result.getTycclick().isEmpty()){
            String[] pairs = result.getTycclick().split("&");
            for (String v : pairs) {
                String[] vss = v.split("·");
                if (Integer.parseInt(vss[0]) == type) {
                    tycclick.setText(vss[1]);
                        isos = true;
                        break;
                }else {
                    tycclick.setText("");
                }
            }
        }else {
            tycclick.setText("");
        }
        //法门技能数据
        if (result.getLawlick()!=null&&!result.getLawlick().isEmpty()){
            String[] pairs = result.getLawlick().split("&");
            for (String v : pairs) {
                String[] vss = v.split("·");
                if (Integer.parseInt(vss[0]) == type) {
                    lawlick.setText(vss[1]);
                        isos = true;
                        break;
                }else {
                    lawlick.setText("");
                }
            }
        }else {
            lawlick.setText("");
        }



        blacBuy.setText(isos?"取消套装":"保存套装");

    }
    /**清除当前套装*/
    public void clear(){
        //TODO 这里不能直接清除result的值，要对应编号清除，要不然全不被清除了！！！！
        LoginResult result = RoleData.getRoleData().getLoginResult();
        DressUtil.removeClicklingbaoByType(result,type);
        DressUtil.removeEquipmentByType(result,type);
        DressUtil.removeSxtByType(result,type);
        DressUtil.removeStarCard(result,type);
        DressUtil.removeXc(result,type);
        DressUtil.removeDc(result,type);
        DressUtil.removeXp(result,type);
        DressUtil.removeTYC(result,type);
        DressUtil.removeFM(result,type);

        String mes = Agreement.getAgreement().rolechangeAgreement("8" + GsonUtil.getGsonUtil().getgson().toJson(result));
        SendMessageUntil.toServer(mes);
        for (int i = 0; i < choseGoodsJLabelMap.get(type).length; i++) {
            choseGoodsJLabelMap.get(type)[i].setIcon(null);
            remove(choseGoodsJLabelMap.get(type)[i]);
        }
        if (choseLingbaosJLabelMap.get(type)!=null) {
            for (int i = 0; i < choseLingbaosJLabelMap.get(type).length; i++) {
                choseLingbaosJLabelMap.get(type)[i].setIcon(null);
                remove(choseLingbaosJLabelMap.get(type)[i]);
            }
        }
            currentAttribute[type]  = "";
            xcAttribute[type]  = "";
            dcAttribute[type]  = "";
            starcard.setIcon(null);
            choseGoodsJLabelMap.remove(type);
            choseLingbaosJLabelMap.remove(type);
            xpskill.setText("");
            tycclick.setText("");
            lawlick.setText("");
            blacBuy.setText("保存套装");

    }
    public String ties = "换装设置";
    public String[] classifi = {"修正","小成","大成","属性","天演策","法门","天罡"};
    public String[] claifi = {"孩子","星符","灵宝","法宝"};

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),ties);
        Juitil.ImngBack(g, Juitil.tz22, 35, 256+25, 385, 245, 1);
        g.drawImage(Juitil.tz19.getImage(), 45, 68, 380, 3, null);
        Juitil.TextBackground(g, "取消勾选，则不参与切换", 14, 135, 234+25, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        Juitil.TextBackground(g, "隐藏“换”字", 14, 60, 490+25, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        for (int i = 0; i < 12; i++) {
            int shop_x = i % 2;
            int shop_y = i / 2;
            if (i<6){
                Juitil.ImngBack(g, Juitil.good_2, 57 + shop_x * 59, 89+shop_y * 57, 50, 50, 1);
            }else {
                Juitil.ImngBack(g, Juitil.good_2, 293 + shop_x * 59, 89+(shop_y-3) * 57, 50, 50, 1);
            }
        }
        for (int i = 0; i < 7; i++) {
            Juitil.ImngBack(g, Juitil.tz26, 110, 297+i*30, 119, 22, 1);
            g.drawImage(Juitil.tz83.getImage(),45,300+i*30,17,16,null);
            Juitil.TextBackground(g, classifi[i], 14, 62, 299+i*30, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        }

        for (int i = 0; i < 4; i++) {
            g.drawImage(Juitil.tz83.getImage(),250,306+i*55,17,16,null);
            Juitil.TextBackground(g, claifi[i], 14, 250+16, 305+i*55, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        }
        Juitil.ImngBack(g, Juitil.good_2, 338, 293, 45, 45, 1);
        Juitil.ImngBack(g, Juitil.tz258, 340, 350, 41, 41, 1);
        Juitil.ImngBack(g, Juitil.tz258, 340, 348+58, 41, 41, 1);
        Juitil.ImngBack(g, Juitil.tz258, 314, 344+58*2, 41, 41, 1);
        Juitil.ImngBack(g, Juitil.tz258, 314+51, 344+58*2, 41, 41, 1);
        Juitil.ImngBack(g, Juitil.tz259, 181, 118, 98, 132, 1);
        if (currentAttribute!=null){
            Juitil.TextBackground(g, currentAttribute[type], 14, 114, 299 + 90, UIUtils.COLOR_Fighting, UIUtils.FZCY_HY14);
        }
        if (xcAttribute!=null){
            Color cacheColor = xcAttribute[type].equals("缺失")?UIUtils.TEXT_NAME_NPC_COLOR:UIUtils.COLOR_Fighting;
            Juitil.TextBackground(g, xcAttribute[type], 14, 114, 299+30, cacheColor, UIUtils.FZCY_HY14);
        }
        if (dcAttribute!=null){
            Color cacheColor = dcAttribute[type].equals("缺失")?UIUtils.TEXT_NAME_NPC_COLOR:UIUtils.COLOR_Fighting;
            Juitil.TextBackground(g, dcAttribute[type], 14, 114, 299+30*2, cacheColor, UIUtils.FZCY_HY14);
        }


    }

    public DressUpBtn[] getBtnBasis() {
        return btnBasis;
    }

    public void setBtnBasis(DressUpBtn[] btnBasis) {
        this.btnBasis = btnBasis;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Map<Integer, JLabel[]> getChoseGoodsJLabelMap() {
        return choseGoodsJLabelMap;
    }

    public void setChoseGoodsJLabelMap(Map<Integer, JLabel[]> choseGoodsJLabelMap) {
        this.choseGoodsJLabelMap = choseGoodsJLabelMap;
    }

    public JLabel[] getChoseGoodsJLabel() {
        return choseGoodsJLabel;
    }

    public void setChoseGoodsJLabel(JLabel[] choseGoodsJLabel) {
        this.choseGoodsJLabel = choseGoodsJLabel;
    }

    public JLabel[] getChoseGoodsimg() {
        return choseGoodsimg;
    }

    public void setChoseGoodsimg(JLabel[] choseGoodsimg) {
        this.choseGoodsimg = choseGoodsimg;
    }

    public Map<Integer, JLabel[]> getChoseLingbaosJLabelMap() {
        return choseLingbaosJLabelMap;
    }

    public void setChoseLingbaosJLabelMap(Map<Integer, JLabel[]> choseLingbaosJLabelMap) {
        this.choseLingbaosJLabelMap = choseLingbaosJLabelMap;
    }

    public JLabel[] getChoselingbaosJLabel() {
        return choselingbaosJLabel;
    }

    public void setChoselingbaosJLabel(JLabel[] choselingbaosJLabel) {
        this.choselingbaosJLabel = choselingbaosJLabel;
    }

    public DressUpBtn getBlacBuy() {
        return blacBuy;
    }

    public void setBlacBuy(DressUpBtn blacBuy) {
        this.blacBuy = blacBuy;
    }

    public String[] getCurrentAttribute() {
        return currentAttribute;
    }

    public void setCurrentAttribute(String[] currentAttribute) {
        this.currentAttribute = currentAttribute;
    }

    public Map<Integer, String[]> getStoreAttributes() {
        return storeAttributes;
    }

    public void setStoreAttributes(Map<Integer, String[]> storeAttributes) {
        this.storeAttributes = storeAttributes;
    }

    public String[] getXcAttribute() {
        return xcAttribute;
    }

    public void setXcAttribute(String[] xcAttribute) {
        this.xcAttribute = xcAttribute;
    }

    public String[] getDcAttribute() {
        return dcAttribute;
    }

    public void setDcAttribute(String[] dcAttribute) {
        this.dcAttribute = dcAttribute;
    }

    public JLabel getXpskill() {
        return xpskill;
    }

    public void setXpskill(JLabel xpskill) {
        this.xpskill = xpskill;
    }
}
