package jxy2.friend;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.Friendtable;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;

public class FriendModel extends JPanel {
    private boolean isExpanded;
    public JLabel jLabel,tximg,stateImg;
    private Color color = UIUtils.COLOR_White;
    private int x1 = 37,y1 =7;
    public FriendModel() {
        this.setPreferredSize(new Dimension(156, 35));
        this.setOpaque(false);
        this.setLayout(null);
        this.isExpanded = true; // 默认展开
        getStateImg();
        getjLabel();
        getTximg();

    }

    public void Ixi(Friendtable friendtable){
      jLabel.setText(friendtable.getRolename());
        if (tximg.getIcon()==null){
            ImageIcon wdfPng = CutButtonImage.getWdfPng("0x6BA" + friendtable.getSpecies_id(), 30, 30, "head.wdf");
            Image img = Juitil.toRoundedCornerImage(wdfPng.getImage(), 25);
            tximg.setIcon(new ImageIcon(img));
        }

        stateImg.setIcon(friendtable.getOnlineState()==1?Juitil.tz214:Juitil.tz215);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (jLabel.getText()!=null&&!jLabel.getText().isEmpty()) {
            Juitil.TextBackground(g, jLabel.getText(), 14, x1, y1, color, UIUtils.FZCY_HY14);
        }
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public JLabel getjLabel() {
        if (jLabel==null){
            jLabel = TeststateJpanel.GJpanelText(UIUtils.COLOR_CCDDDDFF,UIUtils.FZCY_HY13);
            jLabel.setVisible(false);
            jLabel.setBounds(35, 0, 156, 30);
            this.add(jLabel);
        }
        return jLabel;
    }

    public void setjLabel(JLabel jLabel) {
        this.jLabel = jLabel;
    }

    public JLabel getTximg() {
        if (tximg==null){
            tximg = new JLabel();
            tximg.setBounds(0, 0, 30, 30);
            this.add(tximg);
        }
        return tximg;
    }

    public void setTximg(JLabel tximg) {
        this.tximg = tximg;
    }

    public Color getColor() {
        return color;
    }

    public void setColor(Color color) {
        this.color = color;
    }

    public JLabel getStateImg() {
        if (stateImg==null){
            stateImg = new JLabel();
            stateImg.setIcon(Juitil.tz215);
            stateImg.setBounds(20, 20, 14, 14);
            this.add(stateImg);
        }
        return stateImg;
    }

    public void setStateImg(JLabel stateImg) {
        this.stateImg = stateImg;
    }

    public int getX1() {
        return x1;
    }

    public void setX1(int x1) {
        this.x1 = x1;
    }

    public int getY1() {
        return y1;
    }

    public void setY1(int y1) {
        this.y1 = y1;
    }
}
