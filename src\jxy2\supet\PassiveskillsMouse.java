package jxy2.supet;

import org.come.Frame.MsgJframe;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.bean.Skill;
import org.come.mouslisten.ChosePetSkillsMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;

public class PassiveskillsMouse extends TemplateMouseListener {
    public PetSkillsJpanel petSkillsJpanel;
    public int index,x;

    public PassiveskillsMouse(PetSkillsJpanel petSkillsJpanel, int index,int x) {
        this.petSkillsJpanel = petSkillsJpanel;
        this.index = index;
        this.x = x;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        ChosePetSkillsMouslisten.TskillBtn(1,index);
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        ChosePetSkillsMouslisten.TskillBtn(0,index);
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        if (UserMessUntil.getChosePetMes().getSkill() != null && !UserMessUntil.getChosePetMes().getSkill().isEmpty()) {
            String[] petnaturalskill = UserMessUntil.getChosePetMes().getSkill().split("\\|");
            Skill skill = UserMessUntil.getSkillBean().getSkillMap().get(petnaturalskill[index]);
            if (skill!=null){
                MsgJframe.getJframe().getJapnel().Passiveskills(skill);
            }
        }
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {


        FormsManagement.HideForm(46);
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
