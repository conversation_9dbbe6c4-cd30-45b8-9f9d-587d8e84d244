package jxy2.xbao;

import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class XbaoLibraryMouse extends TemplateMouseListener {
    private JList<XbaoLibraryModel> xbaoLibraryModelJList;
    private int i;
    public XbaoLibraryMouse(JList<XbaoLibraryModel> xbaoLibraryModelJList, int i) {
        this.xbaoLibraryModelJList = xbaoLibraryModelJList;
        this.i = i;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
