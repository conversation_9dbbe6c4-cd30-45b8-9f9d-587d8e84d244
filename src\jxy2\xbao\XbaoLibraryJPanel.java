package jxy2.xbao;

import com.tool.btn.FormsOnOffBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.until.SrcollPanelUI;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class XbaoLibraryJPanel extends JPanel {
    private  JScrollPane jScrollPane;
    private FormsOnOffBtn offBtn;
    private XbaoRightSideJpanel xbaoRightSideJpanel;
    //JList模型列表
    private JList<XbaoLibraryModel> xbaoLibraryModelJList;
    private Map<Integer, XbaoLibraryModel> xbaoLibraryModelMap = new HashMap<>();



    public XbaoLibraryJPanel() {
        this.setPreferredSize(new Dimension(724,520));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        offBtn = new FormsOnOffBtn("0x6FAC1013", 1, 153, 0);
        offBtn.setBounds(724-40,  40, 26, 26);
        add(offBtn);
        //加载右侧属性
        xbaoRightSideJpanel = new XbaoRightSideJpanel();
        xbaoRightSideJpanel.setBounds(340, 60, 340, 420);
        add(xbaoRightSideJpanel);

        xbaoLibraryModelJList = new JList<XbaoLibraryModel>();
        xbaoLibraryModelJList.setSelectionForeground(Color.white);
        xbaoLibraryModelJList.setForeground(Color.white);
        xbaoLibraryModelJList.setFont(UIUtils.TEXT_HY16);
        xbaoLibraryModelJList.addMouseListener(new  XbaoLibraryMouse(xbaoLibraryModelJList ,0));
        xbaoLibraryModelJList.addMouseMotionListener(new XbaoLibraryMouse(xbaoLibraryModelJList,0));
        xbaoLibraryModelJList.setSelectedIndex(0);
        xbaoLibraryModelJList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        xbaoLibraryModelJList.removeAll();
        xbaoLibraryModelJList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        xbaoLibraryModelJList.setOpaque(false);

        jScrollPane = new JScrollPane(xbaoLibraryModelJList);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getVerticalScrollBar().setUnitIncrement(10);
        jScrollPane.setOpaque(false);
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setBounds(55, 78, 273, 370);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);
        init();
    }

    private void init() {
        int index = 0;
        ConcurrentHashMap.KeySetView<String, Xuanbao> strings = UserMessUntil.getAllXuanbaovalues().getAllXuanbaoBean().keySet();
        for (String string : strings) {
            Xuanbao xuanbao = UserMessUntil.getXuanabao(string);
            if (xuanbao==null)return;

            int row = index % 3 * 84;
            int col = index / 3 * 104;
            XbaoLibraryModel xbaoLibraryModel = new XbaoLibraryModel();
            xbaoLibraryModel.setXuanbao(xuanbao);
            xbaoLibraryModel.LoadingModel(xuanbao,false);
            xbaoLibraryModel.setBounds(row, col, 92, 108);
            xbaoLibraryModel.addMouseListener(new XbaoListMouse(xbaoLibraryModel, index, xuanbao));
            xbaoLibraryModelJList.add(xbaoLibraryModel);
            xbaoLibraryModelMap.put(index, xbaoLibraryModel);
            index++;
        }

        xbaoLibraryModelMap.get(0).getXuanbaotx().setVisible(true);
        xbaoRightSideJpanel.LoadingData(xbaoLibraryModelMap.get(0).getXuanbao());
        xbaoLibraryModelJList.setPreferredSize(new Dimension(92, UserMessUntil.getAllXuanbaovalues().getAllXuanbaoBean().size() * 36));
    }


    public void refreshList() {
        long startTime = System.nanoTime();
        System.out.println("开始刷新列表...");
    
        try {
            // 1. 加载角色玄宝列表，并转为HashSet提升contains效率
            long t1 = System.nanoTime();
            List<String> roleXuanbaoList = RoleXuanbao.getRoleXuanbao().getListname();
            Set<String> roleXuanbaoSet = new HashSet<>(roleXuanbaoList); // 用HashSet加速contains
            long t2 = System.nanoTime();
    
            // 2. 加载所有玄宝数据
            Map<String, Xuanbao> allXuanbao = UserMessUntil.getAllXuanbaovalues().getAllXuanbaoBean();
            long t3 = System.nanoTime();
    
            // 3. 排序：角色拥有的玄宝排前面
            List<Map.Entry<String, Xuanbao>> sortedEntries = allXuanbao.entrySet().stream()
                .sorted((entry1, entry2) -> {
                    boolean isInList1 = roleXuanbaoSet.contains(entry1.getValue().getBaoname());
                    boolean isInList2 = roleXuanbaoSet.contains(entry2.getValue().getBaoname());
                    if (isInList1 == isInList2) return 0;
                    else if (isInList1) return -1;
                    else return 1;
                })
                .collect(Collectors.toList());
            long t4 = System.nanoTime();
    
            // 4. 刷新UI模型
            int index = 0;
            for (Map.Entry<String, Xuanbao> entry : sortedEntries) {
                Xuanbao xuanbao = entry.getValue();
                if (xuanbao != null) {
                    boolean isInList = roleXuanbaoSet.contains(xuanbao.getBaoname());
                    XbaoLibraryModel model = xbaoLibraryModelMap.get(index);
                    if (model != null) {
                        model.LoadingModel(xuanbao, isInList);
                        model.setXuanbao(xuanbao);
                    }
                    index++;
                }
            }
            long t5 = System.nanoTime();
    
            // 分步打印耗时
            System.out.println("加载角色玄宝列表耗时: " + (t2-t1)/1_000_000 + "ms");
            System.out.println("加载所有玄宝数据耗时: " + (t3-t2)/1_000_000 + "ms");
            System.out.println("排序耗时: " + (t4-t3)/1_000_000 + "ms");
            System.out.println("刷新UI模型耗时: " + (t5-t4)/1_000_000 + "ms");
    
            long totalTime = (System.nanoTime() - startTime) / 1_000_000;
            System.out.println("刷新列表完成! 共处理 " + allXuanbao.size() + " 个物品, 总耗时: " + totalTime + "ms");
    
        } catch (Exception e) {
            System.err.println("刷新列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }


    public void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.tz354.getImage(),0,0,724,520,null);
        g.drawImage(Juitil.tz381.getImage(),312,78,3,368,null);

    }

    public JList<XbaoLibraryModel> getXbaoLibraryModelJList() {
        return xbaoLibraryModelJList;
    }

    public void setXbaoLibraryModelJList(JList<XbaoLibraryModel> xbaoLibraryModelJList) {
        this.xbaoLibraryModelJList = xbaoLibraryModelJList;
    }

    public JScrollPane getjScrollPane() {
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public FormsOnOffBtn getOffBtn() {
        return offBtn;
    }

    public void setOffBtn(FormsOnOffBtn offBtn) {
        this.offBtn = offBtn;
    }


    public Map<Integer, XbaoLibraryModel> getXbaoLibraryModelMap() {
        return xbaoLibraryModelMap;
    }

    public void setXbaoLibraryModelMap(Map<Integer, XbaoLibraryModel> xbaoLibraryModelMap) {
        this.xbaoLibraryModelMap = xbaoLibraryModelMap;
    }

    public XbaoRightSideJpanel getXbaoRightSideJpanel() {
        return xbaoRightSideJpanel;
    }

    public void setXbaoRightSideJpanel(XbaoRightSideJpanel xbaoRightSideJpanel) {
        this.xbaoRightSideJpanel = xbaoRightSideJpanel;
    }
}
