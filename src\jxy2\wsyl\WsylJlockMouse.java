package jxy2.wsyl;

import org.come.until.MessagrFlagUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class WsylJlockMouse implements MouseListener {
    public WsylJPanel wsylJPanel;
    public int index;
    public WsylJlockMouse(WsylJPanel wsylJPanel,int index) {
        this.wsylJPanel = wsylJPanel;
        this.index = index;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
//        int x = wsylJPanel.getLabLock()[index].getX();
//        int y = wsylJPanel.getLabLock()[index].getY();
//        wsylJPanel.getLabLock()[index].setBounds(x+1,y+1,16,18);
    }

    @Override
    public void mouseReleased(MouseEvent e) {
//        int x = wsylJPanel.getLabLock()[index].getX()-1;
//        int y = wsylJPanel.getLabLock()[index].getY()-1;
//        wsylJPanel.getLabLock()[index].setBounds(x,y,16,18);
//        int id  = wsylJPanel.type==0?wsylJPanel.getSkillId(index):wsylJPanel.type==1?wsylJPanel.getSpellID(index):wsylJPanel.getAuxiliaryID(index);

//        String[] param = wsylJPanel.initializeSkills().split("&");
//        if (param.length != 2) {
//            return;
//        }
//        String jn = param[1].split("=")[1];
//        String[] jineng = jn.split("\\|");
//        if (index>jineng.length){
//            ZhuFrame.getZhuJpanel().addPrompt2("#R前面的技能还没有开启呢！请依次开通灵犀技能格");
//            return;
//        }
//        OptionsJframe.getOptionsJframe().getOptionsJpanel().
//                showBox(TiShiUtil.openLxLock, UserMessUntil.getChosePetMes().getSid()+"&"+ id,
//                        "#Y此技能格处于锁定状态，确定消耗#R"+(jineng.length+9)+"#Y个#G灵犀丹#Y为#G"+UserMessUntil.getChosePetMes().getSummoningname()+"#Y开启第#R"+index+"#Y个技能格？#R（每次使用后有一定几率可开启灵犀技能格，已开启技能格越多消耗的灵犀丹越多）",index);
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
    }

//    public int LxDanNum(){
//        return wsylJPanel.getLabLock()[index].getIcon().getIconWidth();
//    }
}
