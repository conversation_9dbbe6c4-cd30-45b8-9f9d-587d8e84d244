package com.tool.btn;

import com.tool.imagemonitor.PlayerMonitor;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.*;
import org.come.Jpanel.*;
import org.come.bean.LoginResult;
import org.come.entity.Gangapplytable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.AnalysisString;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

/**
 * 帮派按钮
 * 
 * <AUTHOR>
 * @time 2020年1月11日 上午10:25:40<br>
 * @class 类名:FactionBtn<br>
 */
public class FactionBtn extends MoBanBtn {

    /**
     * 字段名 : caozuo 帮派总览菜单按钮1 帮派成员菜单按钮2 帮派战报菜单按钮3 全部成员菜单按钮5 核心成员菜单按钮6 申请人菜单按钮7
     * 赏功堂8 帮派仓库9 任职10 卸任/拒绝玩家11 逐出/踢出帮派/接收玩家12 脱离帮派/清空列表13 添加朋友14 战报详情15
     * 小成修炼菜单按钮16 大成修炼菜单按钮17 向左减少点数18 向右添加点数19 修炼20 洗点21 兑换22 确认23 大成修炼/小成修炼24 <br>
     */
    private int caozuo;
    private FactionMainJpanel factionMainJpanel;
    private FactionMemberJpanel factionMemberJpanel;
    private FactionPandectJpanel factionPandectJpanel;
    private FactionWarJpanel factionWarJpanel;
    private FactionDetailsJpanel factionDetailsJpanel;
    private FactionAngelJpanel factionAngelJpanel;
    private FactionAngelModelJpanel factionAngelModelJpanel;
    private FactionAngelPracticeJpanel factionAngelPracticeJpanel;

    public FactionBtn(String iconpath, int type, int caozuo, FactionPandectJpanel factionPandectJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.factionPandectJpanel = factionPandectJpanel;
    }

    public FactionBtn(String iconpath, int type, int caozuo, FactionAngelModelJpanel factionAngelModelJpanel) {
        super(iconpath,0, type);
        this.caozuo = caozuo;
        this.factionAngelModelJpanel = factionAngelModelJpanel;
    }

    public FactionBtn(String iconpath, int type, int caozuo, FactionMemberJpanel factionMemberJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.factionMemberJpanel = factionMemberJpanel;
    }

    public FactionBtn(String iconpath, int type, int caozuo, FactionAngelJpanel factionAngelJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.factionAngelJpanel = factionAngelJpanel;
    }

    public FactionBtn(String iconpath, int type, int caozuo, FactionMainJpanel factionMainJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.factionMainJpanel = factionMainJpanel;
    }
    public FactionBtn(String iconpath, int type, String text,FactionAngelJpanel factionAngelJpanel,String prowpt,int caozuo) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        this.caozuo=caozuo;
        this.factionAngelJpanel=factionAngelJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionPandectJpanel factionPandectJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionPandectJpanel = factionPandectJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionMemberJpanel factionMemberJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionMemberJpanel = factionMemberJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionWarJpanel factionWarJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionWarJpanel = factionWarJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionDetailsJpanel factionDetailsJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionDetailsJpanel = factionDetailsJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionAngelJpanel factionAngelJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionAngelJpanel = factionAngelJpanel;
    }

    public FactionBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            FactionAngelPracticeJpanel factionAngelPracticeJpanel) {
        super(iconpath, type, colors);
        setFont(font);
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.factionAngelPracticeJpanel = factionAngelPracticeJpanel;
    }
    //新添加
    public FactionBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo, FactionAngelJpanel factionAngelJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.caozuo = caozuo;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.factionAngelJpanel = factionAngelJpanel;
    }

    public FactionBtn(String iconpath, int type, String text,int index, FactionAngelJpanel factionAngelJpanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.caozuo = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.factionAngelJpanel = factionAngelJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        try {
            switch (caozuo) {
                case 1:
                    factionMainJpanel.getBtnMenuPandect().setIcons(CutButtonImage.cuts("inkImg/button/B270.png"));
                    factionMainJpanel.getBtnMenuMember().setIcons(CutButtonImage.cuts("inkImg/button/B267.png"));
                    factionMainJpanel.getBtnMenuWar().setIcons(CutButtonImage.cuts("inkImg/button/B269.png"));
                    factionMainJpanel.getFactionCardJpanel().getCardLayout()
                            .show(factionMainJpanel.getFactionCardJpanel(), "pandect");
                    break;
                case 2:
                    factionMainJpanel.getBtnMenuPandect().setIcons(CutButtonImage.cuts("inkImg/button/B271.png"));
                    factionMainJpanel.getBtnMenuMember().setIcons(CutButtonImage.cuts("inkImg/button/B266.png"));
                    factionMainJpanel.getBtnMenuWar().setIcons(CutButtonImage.cuts("inkImg/button/B269.png"));
                    factionMainJpanel.getFactionCardJpanel().getCardLayout()
                            .show(factionMainJpanel.getFactionCardJpanel(), "member");

                    break;
                case 3:
                    ZhuFrame.getZhuJpanel().addPrompt2("暂未开放,敬请期待");
                    return;
                // factionMainJpanel.getBtnMenuPandect().setIcons(CutButtonImage.cuts("inkImg/button/B271.png"));
                // factionMainJpanel.getBtnMenuMember().setIcons(CutButtonImage.cuts("inkImg/button/B267.png"));
                // factionMainJpanel.getBtnMenuWar().setIcons(CutButtonImage.cuts("inkImg/button/B268.png"));
                // factionMainJpanel.getFactionCardJpanel().getCardLayout()
                // .show(factionMainJpanel.getFactionCardJpanel(), "war");
                case 5:
                case 6:
                case 7:
                    int menuType = factionMemberJpanel.getMenuType();
                    if (menuType == 5) {
                        factionMemberJpanel.getBtnMenuAll().setIcons(CutButtonImage.cuts("inkImg/button/B272.png"));
                    } else if (menuType == 6) {
                        factionMemberJpanel.getBtnMenuCore().setIcons(CutButtonImage.cuts("inkImg/button/B274.png"));
                    } else if (menuType == 7) {
                        factionMemberJpanel.getBtnMenuApply().setIcons(CutButtonImage.cuts("inkImg/button/B278.png"));
                    }
                    factionMemberJpanel.showBtn(false);
                    factionMemberJpanel.setMenuType(caozuo);
                    menuType = factionMemberJpanel.getMenuType();
                    if (menuType == 5) {
                        factionMemberJpanel.getBtnMenuAll().setIcons(CutButtonImage.cuts("inkImg/button/B273.png"));
                        factionMemberJpanel.setIconCoumn(null);
                    } else if (menuType == 6) {
                        factionMemberJpanel.getBtnMenuCore().setIcons(CutButtonImage.cuts("inkImg/button/B275.png"));
                        factionMemberJpanel.setIconCoumn(null);
                    } else if (menuType == 7) {
                        factionMemberJpanel.getBtnMenuApply().setIcons(CutButtonImage.cuts("inkImg/button/B279.png"));
                        factionMemberJpanel.setIconCoumn(CutButtonImage.getImage("inkImg/background/S170.png", -1, -1));
                    }
                    factionMemberJpanel.showBtn(true);
                    factionMemberJpanel.changeTable();
                    factionMemberJpanel.showMenuMessage(factionMemberJpanel.getFactionCardJpanel().getGangResultBean());
                    break;
                case 10: // 任命
                    Abdicate();
                    break;
                case 11: // 卸任/拒绝玩家
                    if ("卸任".equals(getText())) {
                        Appointment();
                    } else if ("拒绝玩家".equals(getText())) {
                        RefuseJoin();
                    }
                    break;
                case 12: // 逐出/踢出帮派/接收玩家
                    if ("踢出帮派".equals(getText())) {
                        Kickout();
                    } else if ("逐出".equals(getText())) {

                    } else if ("接收玩家".equals(getText())) {
                        AgreeJoin();
                    }
                    break;
                case 13: // 脱离帮派/清空列表
                    if ("脱离帮派".equals(getText())) {
                        BreakAway();
                    } else if ("清空列表".equals(getText())) {
                        clearApply();
                    }
                    break;
                case 14: // 添加好友
                    if (factionMemberJpanel.getMenuType() != 7) {
                        int selectedRow = factionMemberJpanel.getTable().getSelectedRow();
                        if (selectedRow != -1) {
                            LoginResult loginResult = factionMemberJpanel.getFactionCardJpanel().getGangResultBean()
                                    .getRoleTables().get(selectedRow);
                            PlayerMonitor.addFriend(loginResult.getRole_id(), loginResult.getRolename());
                        }
                    } else {
                        int selectedRow = factionMemberJpanel.getTable().getSelectedRow();
                        if (selectedRow != -1) {
                            Gangapplytable gangapplytable = factionMemberJpanel.getFactionCardJpanel().getGangResultBean()
                                    .getGangapplytables().get(selectedRow);
                            PlayerMonitor.addFriend(gangapplytable.getRole_id(), gangapplytable.getRolename());
                        }
                    }
                    break;
                case 15:
                    FormsManagement.showForm(49);
                    break;
                case 16:
                    factionAngelJpanel.changeMenuShow(1);
                    break;
                case 17:
                    factionAngelJpanel.changeMenuShow(2);
                    break;
                case 18: {
                    FactionAngelJpanel angelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
                    if (factionAngelModelJpanel.getLvlChange() <= factionAngelModelJpanel.getLvlNow()) {
                        ZhuFrame.getZhuJpanel().addPrompt2("不可以再减少点数了");
                        return;
                    } else {
                        factionAngelModelJpanel.setLvlChange(factionAngelModelJpanel.getLvlChange() - 1);
                        factionAngelModelJpanel.refreshLvlChange(angelJpanel);
                        angelJpanel.setTypeLvlResidue(angelJpanel.getTypeLvlResidue() + 1);
                        if (factionAngelModelJpanel.getLvlChange() <= factionAngelModelJpanel.getLvlNow()) {
                            factionAngelModelJpanel.getLabDegree().setForeground(Color.white);
                            factionAngelModelJpanel.getLabLvl().setForeground(Color.WHITE);
                        }
                    }
                    break;
                }
                case 19: {
                    FactionAngelJpanel angelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
                    if (angelJpanel.getTypeLvlResidue() <= 0) {
                        ZhuFrame.getZhuJpanel().addPrompt2("剩余点数不足");
                        return;
                    }
                    int typeNumMax = 20;
                    menuType = angelJpanel.getMenuType();
                    if (menuType == 1) {
                        typeNumMax = 20;
                    } else if (menuType == 2) {
                        typeNumMax = 30;
                    }
                    if (factionAngelModelJpanel.getLvlChange() >= typeNumMax) {
                        ZhuFrame.getZhuJpanel().addPrompt2("不可以再增加点数了");
                        return;
                    } else {
                        factionAngelModelJpanel.setLvlChange(factionAngelModelJpanel.getLvlChange() + 1);
                        factionAngelModelJpanel.refreshLvlChange(angelJpanel);
                        angelJpanel.setTypeLvlResidue(angelJpanel.getTypeLvlResidue() - 1);
                        if (factionAngelModelJpanel.getLvlChange() > factionAngelModelJpanel.getLvlNow()) {
                            factionAngelModelJpanel.getLabDegree().setForeground(Color.GREEN);
                            factionAngelModelJpanel.getLabLvl().setForeground(Color.GREEN);
                        }
                    }
                    break;
                }
                case 20:
                    FormsManagement.showForm(106);
                    FactionAngelPracticeJframe.getFactionAngelPracticeJframe().getFactionAngelPracticeJpanel()
                            .showPanel(factionAngelJpanel.getMenuType());
                    break;
                case 21:
                    String[] resistance = RoleData.getRoleData().getLoginResult().getResistance(factionAngelJpanel.getNpcNameCode().getText(),factionAngelJpanel.getMenuType() == 1 ? "X" : "D");
                    if (resistance == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("你还没加点怎么洗点");
                        return;
                    }
                    OptionsJframe.getOptionsJframe().getOptionsJpanel().
                            showBox(TiShiUtil.washPoint, factionAngelJpanel.getNpcNameCode().getText()+"&"+7 + (factionAngelJpanel.getMenuType() == 1 ? "X" : "D"), "#W确定要花50W银两重置#G" + (factionAngelJpanel.getMenuType() == 1 ? "小成修炼" : "大成修炼") + "?");
                    break;
                case 23:
                    JList<FactionAngelModelJpanel> list = factionAngelJpanel.getListFactionJpanel();
                    StringBuffer buffer = new StringBuffer();
                    buffer.append(7);
                    buffer.append(factionAngelJpanel.getMenuType() == 1 ? "X" : "D");
                    boolean is = true;
                    for (int i = 0, length = factionAngelJpanel.getMenuType() == 1 ? 13 : 18; i < length; i++) {
                        FactionAngelModelJpanel jpanel = (FactionAngelModelJpanel) list.getComponent(i);
                        if (jpanel.getLvlChange() == 0) {
                            continue;
                        }
                        if (jpanel.getLvlNow() != jpanel.getLvlChange()) {
                            is = false;
                        }
                        if (buffer.length() > 2) {
                            buffer.append("#");
                        }
                        buffer.append(jpanel.getLabName().getText());
                        buffer.append("=");
                        buffer.append(jpanel.getLvlValue());
                    }
                    if (is) {
                        ZhuFrame.getZhuJpanel().addPrompt2("你还未修改");
                        return;
                    }
                    String mes = Agreement.getAgreement().rolechangeAgreement(factionAngelJpanel.getNpcNameCode().getText()+"&"+buffer);
                    SendMessageUntil.toServer(mes);
                    break;
                case 24: // 大成修炼/小成修炼
                    LoginResult loginResult = RoleData.getRoleData().getLoginResult();
                    if ("大成修炼".equals(getText())) {
                        int extraPointInt = loginResult.getExtraPointInt("X");
                        if (extraPointInt < 30) {
                            ZhuFrame.getZhuJpanel().addPrompt2("小成修炼尚未结束");
                            return;
                        }
                        int pointInt = loginResult.getExtraPointInt("D");
                        if (pointInt >= 60) {
                            ZhuFrame.getZhuJpanel().addPrompt2("大成修炼已经结束");
                            return;
                        }
                        if (loginResult.getExperience().compareTo(new BigDecimal(5000000)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("经验不足");
                            return;
                        }
                        if (loginResult.getContribution().compareTo(new BigDecimal(1500)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("帮贡不足");
                            return;
                        }
                        if (loginResult.getGold().compareTo(new BigDecimal(5000000)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                            return;
                        }
                         mes = Agreement.getAgreement().rolechangeAgreement("6D");
                        SendMessageUntil.toServer(mes);
                    } else if ("小成修炼".equals(getText())) {
                        int extraPointInt = loginResult.getExtraPointInt("X");
                        if (extraPointInt >= 30) {
                            ZhuFrame.getZhuJpanel().addPrompt2("小成修炼已经修炼完毕");
                            return;
                        }
                        if (loginResult.getExperience().compareTo(new BigDecimal(2000000)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("经验不足");
                            return;
                        }
                        if (loginResult.getContribution().compareTo(new BigDecimal(300)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("帮贡不足");
                            return;
                        }
                        if (loginResult.getGold().compareTo(new BigDecimal(2000000)) < 0) {
                            ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                            return;
                        }
                         mes = Agreement.getAgreement().rolechangeAgreement("6X");
                        SendMessageUntil.toServer(mes);
                    }
                    break;
                case 25:
                    if (factionAngelJpanel.getNpcNameCode().getText().length() > 3) {
                        ZhuFrame.getZhuJpanel().addPrompt("最大字符限制"+3+"个");
                        return;
                    }
                    for (int i = 0; i < factionAngelJpanel.getBtnReplacement().length; i++) {
                        if (factionAngelJpanel.getNpcNameCode().getText().contains(factionAngelJpanel.getBtnReplacement()[i].getNtext())){
                            ZhuFrame.getZhuJpanel().addPrompt("列表中已有相同名字");
                            return;
                        }
                    }
                    loginResult =RoleData.getRoleData().getLoginResult();
                    if (loginResult.getResistance()==null||loginResult.getResistance().isEmpty()){
                        return;
                    }
                    int sum = 0;
                    int v = factionAngelJpanel.getTypes();//
                    String currentResistance = loginResult.getResistance();
                    String[] resistances = currentResistance.split("\\|");
                    for (int i = 0; i < resistances.length; i++) {
                        String[] bb = resistances[i].split("&");
                        if (bb.length > 1){
                            if (bb[0].split("_")[0].equals(factionAngelJpanel.getBtnReplacement()[v].getNtext())){
                                sum = i;
                            }
                        }
                    }
                    int ty = factionAngelJpanel.getMenuType();
                    factionAngelJpanel.getBtnReplacement()[v].setNtext(factionAngelJpanel.getNpcNameCode().getText());
                     mes = Agreement.getAgreement().rolechangeAgreement("K"+factionAngelJpanel.getNpcNameCode().getText()+"|"+sum+"|"+(ty==1?"X":"D"));
                     SendMessageUntil.toServer(mes);
//                    写出到文件
                    ZhuFrame.getZhuJpanel().addPrompt("#G名字保存成功！");
                    break;
                case 26:
                    factionAngelJpanel.HzShowBtn(true);
                    break;
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                    SwitchAttributes(caozuo - 27,false,factionAngelJpanel.getMenuType(),"");
                    break;
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    /**切换属性*/
    public static void SwitchAttributes(int caozuo,boolean is,int menutype,String meg){
        FactionAngelJpanel factionAngelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
        factionAngelJpanel.getNpcNameCode().setText(factionAngelJpanel.getBtnReplacement()[caozuo].getNtext());
        if (!is){factionAngelJpanel.HzShowBtn(false);}
        factionAngelJpanel.DcValue(false);
        factionAngelJpanel.HzShowBtnCor(caozuo);
        RoleProperty.ResetEw();
        factionAngelJpanel.showMenuMessage();
        //我现在要告诉服务器我切换了哪个属性
        JList<FactionAngelModelJpanel> list = factionAngelJpanel.getListFactionJpanel();
                    StringBuffer buffer = new StringBuffer();
                    buffer.append(9);
                    if (!meg.isEmpty()){
                        buffer.append(meg);
                    }else {
                       buffer.append(menutype == 1 ? "X" : "D");
                    }
                    for (int i = 0, length = factionAngelJpanel.getMenuType() == 1 ? 13 : 18; i < length; i++) {
                        FactionAngelModelJpanel jpanel = (FactionAngelModelJpanel) list.getComponent(i);
                        if (jpanel.getLvlChange() == 0) {
                            continue;
                        }
                        if (buffer.length() > 2) {
                            buffer.append("#");
                        }
                        buffer.append(jpanel.getLabName().getText());
                        buffer.append("=");
                        buffer.append(jpanel.getLvlValue());
                    }
        String mes = Agreement.getAgreement().rolechangeAgreement(factionAngelJpanel.getNpcNameCode().getText()+"&"+buffer);
        SendMessageUntil.toServer(mes);

    }
    
    /** 脱离帮派 */
    public void BreakAway() {
        if (!RoleData.getRoleData().getLoginResult().getGangpost().equals("帮主")) {
            OptionsJframe.getOptionsJframe().getOptionsJpanel()
                    .showBox(TiShiUtil.BreakAway, "消息", "#Y       您确定要脱离帮派吗?");
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("帮主不能退出帮派!");
        }
    }

    /** 踢出帮派 */
    public void Kickout() {
        if (RoleData.getRoleData().getLoginResult().getGangpost().equals("帮主")) {
            int index = -1;
            // index值，这些index值由一个int array返回.
            LoginResult roleTable = null;
            index = factionMemberJpanel.getTable().getSelectedRow();
            if (index == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("请选择一个帮派人员！");
                return;
            }
            roleTable = factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().get(index);
            String tiren = "";
            if (roleTable != null
                    && !roleTable.getRolename().equals(RoleData.getRoleData().getLoginResult().getRolename())) {
                tiren = roleTable.getRolename();
            }
            if (tiren != null && !tiren.equals("")) {
                OptionsJframe.getOptionsJframe().getOptionsJpanel()
                        .showBox(TiShiUtil.KickOut, index, "#Y  您确定要将  #G" + tiren + "    #Y踢出帮派吗?");
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("不能踢你自己!");
            }
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("只有帮主才有权利踢人!");
        }
    }

    /** 允许加入帮派 */
    public void AgreeJoin() {
        if (factionMemberJpanel.Important(RoleData.getRoleData().getLoginResult().getGangpost())) {
            int index = factionMemberJpanel.getTable().getSelectedRow();
            if (index == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("请选择一个帮派人员！");
                return;
            }
            tongyi(index);
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("只有护法以上才有权利操作!");
        }
    }

    /**
     * 允许申请加入
     */
    public void tongyi(int index) {
        try {
            // 复制写给客户端的流
            String sendMes = Agreement.GangAgreeAgreement(factionMemberJpanel.getFactionCardJpanel()
                    .getGangResultBean().getGangapplytables().get(index).getRole_id().toString());
            // 向服务器发送信息
            SendMessageUntil.toServer(sendMes);
            factionMemberJpanel.getTableModel().removeRow(index);
            factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getGangapplytables().remove(index);
        } catch (Exception e2) {
        }
    }

    /** 拒绝加入帮派 */
    public void RefuseJoin() {
        if (RoleData.getRoleData().getLoginResult().getGangpost().equals("帮主")
                || RoleData.getRoleData().getLoginResult().getGangpost().equals("护法")) {
            int index = factionMemberJpanel.getTable().getSelectedRow();
            if (index == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("请选择一个帮派人员！");
                return;
            }
            jujue(index);
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("只有护法以上才有权利操作!");
        }
    }

    /**
     * 拒绝申请加入
     */
    public void jujue(int index) {

        try {
            // 复制写给客户端的流

            String sendMes = Agreement.GangRefuseAgreement(factionMemberJpanel.getFactionCardJpanel()
                    .getGangResultBean().getGangapplytables().get(index).getRole_id().toString());
            // 向服务器发送信息
            SendMessageUntil.toServer(sendMes);
            factionMemberJpanel.getTableModel().removeRow(index);
            factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getGangapplytables().remove(index);
        } catch (Exception e2) {
        }
    }

    /** 职务任命 */
    public void Abdicate() {
        if (RoleData.getRoleData().getLoginResult().getGangpost().equals("帮主")) {
            if (xuanzhong()) {
                FormsManagement.showForm(37);
            }
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("只有帮主才有权利!");
        }
    }

    /** 选中 */
    public boolean xuanzhong() {
        try {
            int index = factionMemberJpanel.getTable().getSelectedRow();
            if (index == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("请选择一个帮派人员！");
                return false;
            }
            if (factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().get(index).getRole_id()
                    .compareTo(RoleData.getRoleData().getLoginResult().getRole_id()) != 0) {
                ApointJpanel apointJpanel = ApointJframe.getApointJframe().getApointJpanel();
                apointJpanel.getLabname().setText(
                        factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().get(index)
                                .getRolename());
                apointJpanel.getLabRace().setText(
                        factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().get(index)
                                .getRace_name());
                apointJpanel.getLabLevel().setText(
                        AnalysisString.lvl(factionMemberJpanel.getFactionCardJpanel().getGangResultBean()
                                .getRoleTables().get(index).getGrade()));
                ApointJpanel.index = index;
                return true;
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("不能对自己进行操作!");
            }
        } catch (Exception e2) {

        }
        return false;
    }

    /** 退位让贤 */
    public void Appointment() {
        if (RoleData.getRoleData().getLoginResult().getGangpost().equals("帮主")) {
            int index = -1;
            // index值，这些index值由一个int array返回.
            LoginResult roleTable = null;
            try {
                index = factionMemberJpanel.getTable().getSelectedRow();
                if (index == -1) {
                    ZhuFrame.getZhuJpanel().addPrompt2("请选择一个帮派人员！");
                    return;
                }
                roleTable = factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().get(index);
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            String tiren = "";
            if (roleTable != null
                    && !roleTable.getRolename().equals(RoleData.getRoleData().getLoginResult().getRolename())) {
                tiren = roleTable.getRolename();
            }
            if (tiren != null && !tiren.equals("")) {
                OptionsJframe.getOptionsJframe().getOptionsJpanel()
                        .showBox(TiShiUtil.Abdication, index, "#Y  您确定要退位给  #G" + tiren + "    #Y吗?");
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("不能退位给你自己!");
            }
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("你不是帮主吧!");
        }
    }

    /** 清空申请人列表 */
    public void clearApply() {
        if (factionMemberJpanel.Important(RoleData.getRoleData().getLoginResult().getGangpost())) {
            // 复制写给客户端的流
            String sendMes = Agreement.GangRefuseAgreement("");
            // 向服务器发送信息
            SendMessageUntil.toServer(sendMes);
            factionMemberJpanel.getTableModel().setRowCount(0);
            factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getGangapplytables().clear();
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("你不是帮主或者护法");
        }
    }
}
