package jxy2.wormap;

import com.tool.btn.FormsOnOffBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class WorldMapJPanel extends JPanel {
    public FormsOnOffBtn offBtn;
    private WorldMapBtn[] mapBtns = new WorldMapBtn[52];
    public WorldMapBtn btnLookupNpc,btnFind;
    public WorldMapJPanel() {
        this.setPreferredSize(new Dimension(953, 501));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        // 关闭按钮事件
        offBtn = new FormsOnOffBtn(ImgConstants.tz242, 1, 129);
        offBtn.setBounds(850, 33, 25, 25);
        this.add(offBtn);
        for (int i = 0; i < mapBtns.length; i++) {
            mapBtns[i] = new WorldMapBtn("0x6FEB"+(8867+i), 1, i,this);
            int w = mapBtns[i].getIcons()[0].getImage().getWidth(null);
            int h = mapBtns[i].getIcons()[0].getImage().getHeight(null);
            mapBtns[i].setBounds(coordinates[i][0], coordinates[i][1], w, h);
            this.add(mapBtns[i]);
        }
        btnLookupNpc = new WorldMapBtn("0x6FEB8866", 1, 55,this);
        btnLookupNpc.setBounds(260,426,104,42);
        this.add(btnLookupNpc);

        btnFind = new WorldMapBtn("0x6FEB8865", 1, 56,this);
        btnFind.setBounds(135,426,104,42);
        this.add(btnFind);

    }
    // 手动指定地图按钮位置（根据地名的具体坐标设置）
    public int[][] coordinates = {
            {650, 455},  // 兰若寺 3012
            {182, 114},  // 天宫 1230
            {396, 96},  // 北俱芦洲 1267
            {584, 130},  // 傲来国 1250
            {558,221},  // 龙宫 1244
            {775, 222},  // 蓬莱
            {89+65,229},   // 长寿村 1228
            {260, 248},  // 宝象国 3180
            {477, 302},  // 长安城 1207
            {436, 420},  // 洛阳城 1236
            {585, 448},  // 地府 1211
            {205, 40},  // 蟠桃园 1241
            {141, 46},  // 广寒宫 1240
            {151, 87},  // 瑶池 1232
            {225, 89},  // 御马监 1231
            {431, 36},  // 修罗古城 3205
            {400, 55},  // 灵兽村 1296
            {471, 66},  // 龙窟
            {372, 76},  // 凤巢
            {626, 103},  // 女儿村 1248
            {623, 163},  // 花果山
            {600, 186},  // 水帘洞
            {607, 250},  // 海底迷宫
            {654, 292},  // 东海渔村 1208
            {651, 321},  // 珊瑚海岛 1213
            {660, 356},  // 地下鬼岛
            {805, 180},  // 沛州
            {824, 263},  // 淫州
            {786, 313},  // 方壶
            {756, 340},  // 蓬莱海渊
            {119, 171},  // 方寸后山
            {146, 195},  // 方寸山 1246
            {131, 276},  // 长寿村外 1229
            {113, 371},  // 普陀山 1252
            {242, 286},  // 平顶山 3307
            {219, 327},  // 火云戈壁 3210
            {212, 350},  // 女儿国 3308
            {330, 267},  // 白骨山 1280
            {314, 296},  // 万寿山 1278
            {300, 329},  // 四圣庄 1279
            {281, 362},  // 大唐边境 1251
            {269, 390},  // 狮驼岭 1259
            {392, 298},  // 五庄观 1263
            {372, 334},  // 斧头帮 1272
            {370, 359},  // 五指山 1242
            {500, 247},  // 化生寺 1254
            {454, 275},  // 大雁塔
            {538, 276},  // 皇宫 1298
            {569, 345},  // 长安东 1193
            {429, 381},  // 大唐境内 1210
            {603, 411},  // 地狱迷宫
            {533, 424},  // 轮回司 1227
    };
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.tz241.getImage(), 0, 0, 953, 501, null);


    }
}

