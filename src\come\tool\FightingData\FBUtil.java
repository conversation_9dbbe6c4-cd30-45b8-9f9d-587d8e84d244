package come.tool.FightingData;

public class FBUtil {
	/**获取系数 转生次数 人物等级  法宝总等级  法宝品质  法宝等级*/
	public static int getFBlvl(int t,int g,int zlvl,int qv,int lvl){
//		200  70基础 30是等级  100是总等级
		int pj=0;
		g+=t*25;
		pj+=g*3;
		pj+=zlvl;
		qv+=lvl;
		pj+=qv*7;
		return pj>>5;	
	}
	//获取持续回合数
	public static int getFBcx(int id,int lvl){
			if (id==5007||id==5010||id==5012||id==5015) {
				return 0;
			}else if (id==5006) {
				if (lvl>=158) {
					return 7;
				}else if (lvl>=112) {
					return 6;
				}else if (lvl>=65) {
					return 5;
				}else if (lvl>=20) {
					return 4;
				}
				return 3;
			}else if (id==5009) {
			    if (lvl>=182) {
			    	return 8;
				}else if (lvl>=138) {
					return 7;
				}else if (lvl>=94) {
					return 6;
				}else if (lvl>=51) {
					return 5;
				}else if (lvl>=7) {
					return 4;
				}
				return 3;
			}else {
				 if (lvl>=200) {
				    	return 8;
					}else if (lvl>=156) {
						return 7;
					}else if (lvl>=112) {
						return 6;
					}else if (lvl>=80) {
						return 5;
					}else if (lvl>=60) {
						return 4;
					}else if (lvl>=20) {
						return 3;
					}
					return 2;
			}	
	 }
	//获取法宝作用目标数
	public static int getFBsum(int id,int lvl){
		if (id==5003||id==5006||id==5008||id==5011||id==5014||id==5015) {
			return 1;
		}
		if (lvl>=100) {
			return 2;
		}
		return 1;
	} 
	/**根据熟练度计算个数*/
	public static int geshu(int level, int ed, String type) {
		if (type.equals("鬼火") || type.equals("火") || type.equals("水")
				|| type.equals("雷") || type.equals("风")) {
			return xian(ed, level);
		} else if (type.equals("震慑")) {
			return moz(ed, level);
		} else if (type.equals("力量") || type.equals("抗性") || type.equals("加速")
				|| type.equals("smmh")) {
			return moq(ed, level);
		} else if (type.equals("中毒") || type.equals("封印") || type.equals("昏睡")
				|| type.equals("遗忘")) {
			return renq(ed, level);
		} else if (type.equals("混乱")) {
			return renh(ed, level);
		} else if (type.equals("三尸虫")) {
			return guis(ed, level);
		} else if (type.equals("霹雳")) {
			return lpl(ed, level);
		}
		return xian(ed, level);
	}
	/**仙法个数*/
	public static int xian(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 720) {
				return 2;
			} else if (ed < 5215) {
				return 3;
			} else if (ed < 16610) {
				return 4;
			} else {
				return 5;
			}
		} else if (lvl == 5) {
			if (ed < 558) {
				return 3;
			} else if (ed < 5621) {
				return 4;
			} else {
				return 5;
			}
		}
		return 1;
	}

	/**震慑*/
	public static int moz(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 426) {
				return 2;
			} else if (ed < 3098) {
				return 3;
			} else if (ed < 9866) {
				return 4;
			} else {
				return 5;
			}
		} else if (lvl == 5) {
			if (ed < 226) {
				return 3;
			} else if (ed < 1638) {
				return 4;
			} else if (ed < 5215) {
				return 5;
			} else if (ed < 11868) {
				return 6;
			} else {
				return 7;
			}
		}
		return 1;
	}

	/**附攻、附防、加速*/
	public static int moq(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 214) {
				return 2;
			} else if (ed < 2155) {
				return 3;
			} else if (ed < 8324) {
				return 4;
			} else {
				return 5;
			}
		} else if (lvl == 5) {
			if (ed < 117) {
				return 3;
			} else if (ed < 1174) {
				return 4;
			} else if (ed < 4533) {
				return 5;
			} else if (ed < 11826) {
				return 6;
			} else {
				return 7;
			}
		}
		return 1;
	}

	/**封印、昏睡、毒系 遗忘法术*/
	public static int renq(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 428) {
				return 2;
			} else if (ed < 3098) {
				return 3;
			} else if (ed < 9866) {
				return 4;
			} else {
				return 5;
			}
		} else if (lvl == 5) {
			if (ed < 226) {
				return 3;
			} else if (ed < 1638) {
				return 4;
			} else if (ed < 5215) {
				return 5;
			} else if (ed < 11864) {
				return 6;
			} else {
				return 7;
			}
		}
		return 1;
	}

	/**混乱法术*/
	public static int renh(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 1362) {
				return 2;
			} else if (ed < 9866) {
				return 3;
			} else {
				return 4;
			}
		} else if (lvl == 5) {
			if (ed < 973) {
				return 3;
			} else if (ed < 7051) {
				return 4;
			} else {
				return 5;
			}
		}
		return 1;
	}

	/**鬼魅惑*/
	public static int guiv(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 2200) {
				return 2;
			} else if (ed < 4600) {
				return 3;
			} else if (ed < 9600) {
				return 4;
			} else {
				return 5;
			}
		} else if (lvl == 5) {
			if (ed < 2200) {
				return 3;
			} else if (ed < 4600) {
				return 4;
			} else if (ed < 9600) {
				return 5;
			} else if (ed < 12000) {
				return 6;
			} else {
				return 7;
			}
		}
		return 1;
	}

	/**鬼三尸*/
	public static int guis(int ed, int lvl) {
		if (lvl == 3) {
			if (ed < 5200) {
				return 2;
			} else if (ed < 6800) {
				return 3;
			} else {
				return 4;
			}
		} else if (lvl == 5) {
			if (ed < 2200) {
				return 3;
			} else if (ed < 6800) {
				return 4;
			} else {
				return 5;
			}
		}
		return 1;
	}
	/**龙霹雳 */
	public static int lpl(int ed, int lvl) {
		if (lvl == 3||lvl == 5) {
			if (ed < 5200) {return 2;} 
			else if (ed < 6800) {return 3;} 
			else {return 4;}
		}
		return 1;
	}
}
