package org.come.util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;

/**
 * Native Image 性能监控器
 * 监控帧率、内存使用、渲染性能等指标
 */
public class NativeImagePerformanceMonitor {
    
    private static NativeImagePerformanceMonitor instance;
    private boolean isNativeImage;
    private boolean isMonitoring = false;
    
    // 性能统计
    private long frameCount = 0;
    private long lastStatsTime = System.nanoTime();
    private double currentFPS = 0;
    private double averageFPS = 0;
    private long totalFrames = 0;
    private long startTime = System.nanoTime();
    
    // 内存统计
    private MemoryMXBean memoryBean;
    private RuntimeMXBean runtimeBean;
    
    // 渲染时间统计
    private long totalRenderTime = 0;
    private long renderCount = 0;
    private double averageRenderTime = 0;
    
    // 监控窗口
    private JFrame monitorFrame;
    private JLabel fpsLabel;
    private JLabel memoryLabel;
    private JLabel renderTimeLabel;
    private JLabel environmentLabel;
    
    private NativeImagePerformanceMonitor() {
        this.isNativeImage = System.getProperty("org.graalvm.nativeimage.imagecode") != null;
        this.memoryBean = ManagementFactory.getMemoryMXBean();
        this.runtimeBean = ManagementFactory.getRuntimeMXBean();
    }
    
    public static NativeImagePerformanceMonitor getInstance() {
        if (instance == null) {
            instance = new NativeImagePerformanceMonitor();
        }
        return instance;
    }
    
    /**
     * 开始监控
     */
    public void startMonitoring() {
        if (isMonitoring) {
            return;
        }
        
        isMonitoring = true;
        System.out.println("[性能监控] 开始监控 " + (isNativeImage ? "Native Image" : "JVM") + " 性能");
        
        // 创建监控窗口
        createMonitorWindow();
        
        // 启动监控线程
        Thread monitorThread = new Thread(this::monitorLoop);
        monitorThread.setName("PerformanceMonitor");
        monitorThread.setDaemon(true);
        monitorThread.start();
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        isMonitoring = false;
        if (monitorFrame != null) {
            monitorFrame.dispose();
        }
        System.out.println("[性能监控] 监控已停止");
    }
    
    /**
     * 记录一帧
     */
    public void recordFrame() {
        frameCount++;
        totalFrames++;
    }
    
    /**
     * 记录渲染时间
     */
    public void recordRenderTime(long renderTimeNanos) {
        totalRenderTime += renderTimeNanos;
        renderCount++;
        averageRenderTime = (double) totalRenderTime / renderCount / 1000000.0; // 转换为毫秒
    }
    
    /**
     * 创建监控窗口
     */
    private void createMonitorWindow() {
        SwingUtilities.invokeLater(() -> {
            monitorFrame = new JFrame("性能监控 - " + (isNativeImage ? "Native Image" : "JVM"));
            monitorFrame.setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
            monitorFrame.setSize(400, 300);
            monitorFrame.setAlwaysOnTop(true);
            
            JPanel panel = new JPanel(new GridLayout(6, 1, 5, 5));
            panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            
            environmentLabel = new JLabel("环境: " + (isNativeImage ? "GraalVM Native Image" : "OpenJDK JVM"));
            environmentLabel.setFont(new Font(Font.MONOSPACED, Font.BOLD, 12));
            
            fpsLabel = new JLabel("FPS: 0.0 (平均: 0.0)");
            fpsLabel.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            memoryLabel = new JLabel("内存: 0 MB / 0 MB");
            memoryLabel.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            renderTimeLabel = new JLabel("渲染时间: 0.0 ms");
            renderTimeLabel.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
            
            JButton resetButton = new JButton("重置统计");
            resetButton.addActionListener(e -> resetStats());
            
            JButton closeButton = new JButton("关闭监控");
            closeButton.addActionListener(e -> stopMonitoring());
            
            panel.add(environmentLabel);
            panel.add(fpsLabel);
            panel.add(memoryLabel);
            panel.add(renderTimeLabel);
            panel.add(resetButton);
            panel.add(closeButton);
            
            monitorFrame.add(panel);
            monitorFrame.setLocationRelativeTo(null);
            monitorFrame.setVisible(true);
        });
    }
    
    /**
     * 监控循环
     */
    private void monitorLoop() {
        while (isMonitoring) {
            try {
                long currentTime = System.nanoTime();
                
                // 计算FPS
                if (currentTime - lastStatsTime >= 1000000000L) { // 每秒更新一次
                    currentFPS = frameCount * 1000000000.0 / (currentTime - lastStatsTime);
                    averageFPS = totalFrames * 1000000000.0 / (currentTime - startTime);
                    
                    frameCount = 0;
                    lastStatsTime = currentTime;
                    
                    // 更新UI
                    updateMonitorUI();
                }
                
                Thread.sleep(100); // 每100ms检查一次
                
            } catch (InterruptedException e) {
                break;
            } catch (Exception e) {
                System.err.println("[性能监控] 监控异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 更新监控UI
     */
    private void updateMonitorUI() {
        if (monitorFrame == null || !monitorFrame.isVisible()) {
            return;
        }
        
        SwingUtilities.invokeLater(() -> {
            // 更新FPS
            fpsLabel.setText(String.format("FPS: %.1f (平均: %.1f)", currentFPS, averageFPS));
            
            // 更新内存使用
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed() / 1024 / 1024;
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax() / 1024 / 1024;
            memoryLabel.setText(String.format("内存: %d MB / %d MB", usedMemory, maxMemory));
            
            // 更新渲染时间
            renderTimeLabel.setText(String.format("渲染时间: %.2f ms", averageRenderTime));
            
            // 根据性能设置颜色
            if (currentFPS >= 120) {
                fpsLabel.setForeground(Color.GREEN);
            } else if (currentFPS >= 60) {
                fpsLabel.setForeground(Color.ORANGE);
            } else {
                fpsLabel.setForeground(Color.RED);
            }
        });
    }
    
    /**
     * 重置统计
     */
    private void resetStats() {
        frameCount = 0;
        totalFrames = 0;
        startTime = System.nanoTime();
        lastStatsTime = startTime;
        totalRenderTime = 0;
        renderCount = 0;
        averageRenderTime = 0;
        currentFPS = 0;
        averageFPS = 0;
        
        System.out.println("[性能监控] 统计已重置");
    }
    
    /**
     * 获取当前FPS
     */
    public double getCurrentFPS() {
        return currentFPS;
    }
    
    /**
     * 获取平均FPS
     */
    public double getAverageFPS() {
        return averageFPS;
    }
    
    /**
     * 是否为Native Image环境
     */
    public boolean isNativeImage() {
        return isNativeImage;
    }
    
    /**
     * 打印性能报告
     */
    public void printPerformanceReport() {
        System.out.println("=== 性能报告 ===");
        System.out.println("环境: " + (isNativeImage ? "GraalVM Native Image" : "OpenJDK JVM"));
        System.out.println("当前FPS: " + String.format("%.1f", currentFPS));
        System.out.println("平均FPS: " + String.format("%.1f", averageFPS));
        System.out.println("总帧数: " + totalFrames);
        System.out.println("平均渲染时间: " + String.format("%.2f ms", averageRenderTime));
        
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed() / 1024 / 1024;
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax() / 1024 / 1024;
        System.out.println("内存使用: " + usedMemory + " MB / " + maxMemory + " MB");
        System.out.println("运行时间: " + (System.nanoTime() - startTime) / 1000000000L + " 秒");
        System.out.println("===============");
    }
}
