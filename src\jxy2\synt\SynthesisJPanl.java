package jxy2.synt;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.NewPart;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.Goodstable;
import org.come.model.Exchange;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
* 合成
* <AUTHOR>
* @date 2024/7/11 下午9:49
*/

public class SynthesisJPanl extends JPanel {
    //合成物品列表
    private JList<SynthesisModel> listpet;
    // 列表中的对象
    private Map<Integer, SynthesisModel> mapResistanceModelPanel = new HashMap<>();
    // 滚动条
    private JScrollPane jScrollPane;
    // 列表中对象的名字颜色
    public static int idx;
    public boolean is =false;
    //展示组件
    private JLabel labname,labgoods,labbimg,labcbtimg;//选中名字
    private JLabel[] labPlus = new JLabel[2];
    private JLabel[] labGoodsName = new JLabel[2];
    private RichLabel[] labSkillName = new RichLabel[2];
    private Goodstable[] goodstables = new Goodstable[2];
    public int Initialize = 0;//初始化右侧组件标识-默认0
    public JLabel[] laItmeImg = new JLabel[8];//显示物品图像
    public JLabel[] laItmeName = new JLabel[8];//显示物品名称
    public JLabel[] laItmeNum = new JLabel[8];//显示物品数量
    private Goodstable[] goodstable = new Goodstable[8];
    //右侧材料组件
    //合成按钮组件
    private SynthesisBtn composite,excang;
    public SynthesisJPanl() {
        setPreferredSize(new Dimension(620, 432));
        setOpaque(false);
        setLayout(null);
        Juitil.addClosingButtonToPanel(this,116,620);
        getjScrollPane();
        getLabname();
        getLabgoods();
        getLabbimg();
        getLabcbtimg();
        getLabPlus();
        getLabGoodsName();
        getLabSkillName();
        getLaItmeName();
        getLaItmeNum();
        getLaItmeImg();
        composite = new SynthesisBtn(ImgConstants.Btn_59, 1, UIUtils.COLOR_BTNPUTONG, UIUtils.TEXT_HY16,"合成",this,99,-1);
        composite.setBounds(510, 379, 59, 25);
        add(composite);
        excang = new SynthesisBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14,"兑换",this,98,-1);
        excang.setBounds(363, 359, 59, 26);
        add(excang);
    }

    /**
     * 清除组件内容
     */
    public void A_isVisible(){
        isv(Initialize==0);
        for (int j = 0; j < 2; j++) {
            labSkillName[j].setText("未选择");
            labPlus[j].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz55,"defaut.wdf"));
            labPlus[j].setBounds(330+j*100,268,28,28);
            labSkillName[j].setBounds(323+j*101,308,100,40);
            goodstables[j] = null;
        }

    }

    public void IniVsi(){
        mapResistanceModelPanel.clear();
        listpet.removeAll();
        Initialize=0;
        int index = 0;
        Set<Map.Entry<Integer, Exchange>> entrySet = UserMessUntil.getAllExchange().getAllExchange().entrySet();
        for (Map.Entry<Integer, Exchange> entry : entrySet) {
            Exchange exchange = entry.getValue();
            SynthesisModel synthesisModel = new SynthesisModel(index);
                synthesisModel.Ixi(exchange);
                synthesisModel.setBounds(0, index* 30, 159, 30);
                synthesisModel.setExpanded(false);
                synthesisModel.setAllowRefresh(false); // 允许刷新
            listpet.add(synthesisModel);
            mapResistanceModelPanel.put(index,synthesisModel);
            index++;
        }

        listpet.setPreferredSize(new Dimension(159, index * 30));
        isv(false);
        Initialize = -1;
    }

    private void isv(boolean isin) {
        for (int j = 0; j < 2; j++) {
            labSkillName[j].setVisible(isin);
            labGoodsName[j].setVisible(isin);
            labPlus[j].setVisible(isin);
        }
        labname.setVisible(true);
        labgoods.setVisible(isin);
        labbimg.setVisible(isin);
        composite.setVisible(isin);
        for (int i = 0; i < 8; i++) {
            laItmeImg[i].setVisible(Initialize==2||Initialize==3);
            laItmeName[i].setVisible(Initialize==2||Initialize==3);
            laItmeNum[i].setVisible(Initialize==2||Initialize==3);
        }
        excang.setVisible(Initialize==2||Initialize==3);
        labcbtimg.setVisible(Initialize==3);
    }

    public void PetResistanceRefresh(int index) {
        isv(Initialize==0);
        int collapsedPanelHeight = 30; // 假设未展开时的面板高度为150
        //记录已展开的数
        boolean isCurrentlyExpanded = mapResistanceModelPanel.get(index).isExpanded();
        if (isCurrentlyExpanded) {
            // 当前面板已展开，收缩它
            mapResistanceModelPanel.get(index).setBounds(0, getYPosition(index, false), 144, collapsedPanelHeight);
            mapResistanceModelPanel.get(index).setExpanded(false);
            mapResistanceModelPanel.get(index).setAllowRefresh(false); // 允许刷新
        } else {
            // 当前面板未展开，展开它
            mapResistanceModelPanel.get(index).setBounds(0, getYPosition(index, true), 144, collapsedPanelHeight);
            mapResistanceModelPanel.get(index).setExpanded(true);
            mapResistanceModelPanel.get(index).setAllowRefresh(true); // 允许刷新
        }
            for (int i = 0; i < mapResistanceModelPanel.size(); i++) {
                if (i!=index){
                    SynthesisModel petresis = mapResistanceModelPanel.get(i);
                    petresis.setBounds(0, getYPosition(i, false), 144, collapsedPanelHeight);
                    petresis.setExpanded(false);
                    petresis.setAllowRefresh(false); // 允许刷新
                }
            }


        showRolesumming(index);
        // 更新所有面板的位置
        updatePanelPositions();
        // 更新容器的首选大小
        int totalHeight = calculateTotalHeight();
        listpet.setPreferredSize(new Dimension(159, totalHeight));
    }
    private void showRolesumming(int index) {
        SynthesisModel synthesisModel =  mapResistanceModelPanel.get(index);
        if (synthesisModel!=null&&synthesisModel.isAllowRefresh()){
            String[] vs = synthesisModel.getExchange().getStype().split("\\|");
            for (int j = 0; j < vs.length; j++) {
                synthesisModel.getMsg()[j]=vs[j];
                synthesisModel.getRest()[j].setBounds(22,30+j*27,122,27);
            }
        }
    }

    private int getYPosition(int index, boolean isExpanded) {
        int yOffset = 0;
        for (int i = 0; i < index; i++) {
            yOffset += mapResistanceModelPanel.get(i).isExpanded() ? 142 : 30;
        }
        return yOffset + (isExpanded ? 142 : 28);
    }

    private int calculateTotalHeight() {
        int totalHeight = 0;
        for (int i = 0; i < mapResistanceModelPanel.size(); i++) {
            SynthesisModel petresis = mapResistanceModelPanel.get(i);
            String[] vs = petresis.getExchange().getStype().split("\\|");
            totalHeight +=  petresis.isExpanded() ? i+vs.length*29 : 30;
        }
        return totalHeight;
    }

    private void updatePanelPositions() {
        int yOffset = 0;
        for (int i = 0; i < mapResistanceModelPanel.size(); i++) {
            SynthesisModel petresis = mapResistanceModelPanel.get(i);
            String[] vs = petresis.getExchange().getStype().split("\\|");
            int height = petresis.isExpanded() ? i==0?vs.length*42:i==1?vs.length*32:vs.length*29 : 30;
            petresis.setBounds(0, yOffset, 159, petresis.isExpanded() ? vs.length*45 : 30);
            yOffset += height;
        }
    }
    public NewPart newPart;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"合 成");
        Juitil.ImngBack(g, Juitil.tz22, 15, 35, 166, 396, 1);
        Juitil.ImngBack(g, Juitil.tz22, 166 + 16, 35, 418, 396, 1);
        Juitil.ImngBack(g, Juitil.tz26, 330, 46, 121, 23, 1);
        if (Initialize==0) {
            Juitil.ImngBack(g, Juitil.good_2, 366, 100, 50, 50, 1);
            g.drawImage(Juitil.icon.getImage(), 166 + 26, 372, 400, 3, null);
            g.drawImage(Juitil.tz123.getImage(), 364, 194, 56, 30, null);
            Juitil.Textdrawing(g, "成功率：100%", 348, 184, UIUtils.COLOR_orange, UIUtils.FZCY_HY14);
            for (int i = 0; i < 2; i++) {
                Juitil.ImngBack(g, Juitil.good_2, 318 + i * 100, 256, 50, 50, 1);
            }
        }else if (Initialize==2){
            g.drawImage(Juitil.tz164.getImage(), 242, 70, 299, 335, null);
            int centerX = 364; // 圆心的X坐标
            int centerY = 193; // 圆心的Y坐标
            int radius = 95;  // 圆的半径
            int  numElements = 8; // 元素数量
            double  angleIncrement = Math.PI / numElements * 2 ; // 角度增量，注意这里是π/3，因为我们只需要两次增量
            double  startAngle = Math.PI / 2; // 起始角度，指向正上方
            for (int i = 0; i < numElements; i++) {
                double angle = startAngle + i * angleIncrement; // 当前元素的角度
                int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1);
                laItmeImg[i].setBounds(x,y,47,47);
                laItmeName[i].setBounds(x+3,y,50,15);
                laItmeNum[i].setBounds(x+15,y+35 ,50,15);
            }
            if (newPart!=null){
                newPart.draw(g, 388, 280, 0, ImageMixDeal.userimg.getTime());
            }
        }else if (Initialize==3){
            Juitil.ImngBack(g, Juitil.good_2, 366, 100, 50, 50, 1);
            g.drawImage(Juitil.tz123.getImage(), 364, 194, 56, 30, null);
            Juitil.Textdrawing(g, "成功率：100%", 348, 184, UIUtils.COLOR_orange, UIUtils.FZCY_HY14);
            Juitil.ImngBack(g, Juitil.good_2, 366, 256, 50, 50, 1);
            laItmeImg[0].setBounds(366,256,47,47);
            laItmeName[0].setBounds(366+3,256,50,15);
            laItmeNum[0].setBounds(366+15,256+35 ,50,15);
            labcbtimg.setBounds(366,100,50,50);

        }
    }



    public JScrollPane getjScrollPane() {
        if (jScrollPane==null) {
            this.jScrollPane = new JScrollPane(getListpet());
            this.jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            this.jScrollPane.getVerticalScrollBar().setUI(null);
            this.jScrollPane.getVerticalScrollBar().setUnitIncrement(20);
            this.jScrollPane.getViewport().setOpaque(false);
            this.jScrollPane.setOpaque(false);
            this.jScrollPane.setBounds(17, 41, 162, 370);
            this.jScrollPane.setBorder(BorderFactory.createEmptyBorder());
            this.jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            this.add(this.jScrollPane);
        }
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public JList<SynthesisModel> getListpet() {
        if (listpet==null){
            listpet = new JList<>();
            listpet.setSelectionBackground(new Color(122, 117, 112));
            listpet.setSelectionForeground(Color.white);
            listpet.setForeground(Color.white);
            listpet.setFont(UIUtils.TEXT_HY16);
            listpet.removeAll();
            listpet.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
            DefaultListCellRenderer cellRenderer = new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(javax.swing.JList<?> list, Object value, int index,
                                                              boolean isSelected, boolean cellHasFocus) {
                    super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                    return this;
                }
            };
            listpet.setCellRenderer(cellRenderer);
            listpet.setOpaque(false);
        }
        return listpet;
    }

    public void setListpet(JList<SynthesisModel> listpet) {
        this.listpet = listpet;
    }

    public Map<Integer, SynthesisModel> getMapResistanceModelPanel() {
        return mapResistanceModelPanel;
    }

    public void setMapResistanceModelPanel(Map<Integer, SynthesisModel> mapResistanceModelPanel) {
        this.mapResistanceModelPanel = mapResistanceModelPanel;
    }

    public JLabel getLabname() {
        if (labname==null){
            labname = TeststateJpanel.GJpanelText(UIUtils.COLOR_White,UIUtils.FZCY_HY14);
            labname.setBounds(360,51,100,15);
            add(labname);
        }
        return labname;
    }

    public void setLabname(JLabel labname) {
        this.labname = labname;
    }

    public JLabel getLabgoods() {
        if (labgoods==null){
            labgoods = new JLabel();
            labgoods.setBounds(366,100,50,50);
            add(labgoods);
        }
        return labgoods;
    }

    public void setLabgoods(JLabel labgoods) {
        this.labgoods = labgoods;
    }

    public JLabel getLabbimg() {
        if (labbimg==null){
            labbimg = new JLabel();
            labbimg.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz125,140,140,"defaut.wdf"));
            labbimg.setBounds(315,58,140,140);
            add(labbimg);
        }
        return labbimg;
    }

    public void setLabbimg(JLabel labbimg) {
        this.labbimg = labbimg;
    }

    public JLabel getLabcbtimg() {
        if (labcbtimg==null){
            labcbtimg = new JLabel();
            labcbtimg.setBounds(315,58,50,50);
            add(labcbtimg);
        }
        return labcbtimg;
    }

    public void setLabcbtimg(JLabel labcbtimg) {
        this.labcbtimg = labcbtimg;
    }

    public JLabel[] getLabPlus() {
        for (int i = 0; i < labPlus.length; i++) {
            if (labPlus[i]==null){
                labPlus[i] = new JLabel();
                labPlus[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz55,"defaut.wdf"));
                labPlus[i].addMouseListener(new SynthesisMouse(i,this));
                labPlus[i].setBounds(330+i*100,268,28,28);
                add(labPlus[i]);
            }
        }
        return labPlus;
    }

    public void setLabPlus(JLabel[] labPlus) {
        this.labPlus = labPlus;
    }

    public JLabel[] getLabGoodsName() {
        for (int i = 0; i < labGoodsName.length; i++) {
            if (labGoodsName[i]==null){
                labGoodsName[i] = new JLabel();
                labGoodsName[i].setForeground(Color.white);
                labGoodsName[i].setVerticalTextPosition(SwingConstants.CENTER);
                labGoodsName[i].setHorizontalTextPosition(SwingConstants.CENTER);
                labGoodsName[i].setBounds(316+i*101,241,100,15);
                labGoodsName[i].setFont(UIUtils.MSYH_HY13);
                add(labGoodsName[i]);
            }
        }

        return labGoodsName;
    }

    public void setLabGoodsName(JLabel[] labGoodsName) {
        this.labGoodsName = labGoodsName;
    }

    public RichLabel[] getLabSkillName() {
        for (int i = 0; i < labSkillName.length; i++) {
            if (labSkillName[i]==null){
                labSkillName[i] = new RichLabel("未选择",UIUtils.MSYH_HY13,151);
                labSkillName[i].setBounds(323+i*101,308,100,40);
                Dimension d = labSkillName[i].computeSize(151);
                labSkillName[i].setSize(d);
                labSkillName[i].setPreferredSize(d);
                add(labSkillName[i]);

            }
        }
        return labSkillName;
    }

    public void setLabSkillName(RichLabel[] labSkillName) {
        this.labSkillName = labSkillName;
    }

    public Goodstable[] getGoodstables() {
        return goodstables;
    }

    public void setGoodstables(Goodstable[] goodstables) {
        this.goodstables = goodstables;
    }

    public JLabel[] getLaItmeImg() {
        for (int i = 0; i < laItmeImg.length; i++) {
            if (laItmeImg[i]==null){
                laItmeImg[i] = new JLabel();
                int finalI = i;
                laItmeImg[i].addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseEntered(MouseEvent e) {
                        super.mouseEntered(e);
                        if (goodstable!=null) {
                            ZhuFrame.getZhuJpanel().creatgoodtext(goodstable[finalI]);
                        }
                    }

                    @Override
                    public void mouseExited(MouseEvent e) {
                        super.mouseExited(e);
                        ZhuFrame.getZhuJpanel().cleargoodtext();
                    }
                });
                add(laItmeImg[i]);
            }
        }
        return laItmeImg;
    }

    public void setLaItmeImg(JLabel[] laItmeImg) {
        this.laItmeImg = laItmeImg;
    }

    public JLabel[] getLaItmeName() {
        for (int i = 0; i < laItmeName.length; i++) {
            if (laItmeName[i]==null){
                laItmeName[i] = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,UIUtils.FZCY_HY13);
                add(laItmeName[i]);
            }
        }
        return laItmeName;
    }

    public void setLaItmeName(JLabel[] laItmeName) {
        this.laItmeName = laItmeName;
    }

    public JLabel[] getLaItmeNum() {
        for (int i = 0; i < laItmeNum.length; i++) {
            if (laItmeNum[i]==null){
                laItmeNum[i] = TeststateJpanel.GJpanelText(UIUtils.COLOR_White,UIUtils.FZCY_HY13);
                add(laItmeNum[i]);
            }
        }
        return laItmeNum;
    }

    public void setLaItmeNum(JLabel[] laItmeNum) {
        this.laItmeNum = laItmeNum;
    }

    public NewPart getNewPart() {
        return newPart;
    }

    public void setNewPart(NewPart newPart) {
        this.newPart = newPart;
    }

    public Goodstable[] getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable[] goodstable) {
        this.goodstable = goodstable;
    }
}
