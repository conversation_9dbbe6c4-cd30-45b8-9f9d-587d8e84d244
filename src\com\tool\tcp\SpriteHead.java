package com.tool.tcp;


import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Vector;

/**
 * 可跳到指定位置的ByteArrayInputStrem<br>
 * seek(int pos)
 * 
 * <AUTHOR>
 * @date
 */
public class SpriteHead extends ByteArrayInputStream{
	private short[] palette;
	public SpriteHead(byte[] buf) {
		super(buf);
	}
	/**初始化   0SP版本   1SH版本*/
	public Sprite init(String value, boolean is, int version) throws IOException {
		short headerSize = readUnsignedShort();
		short animCount = readUnsignedShort();
		short frameCount = readUnsignedShort();
		short width = readUnsignedShort();
		short height = readUnsignedShort();
		short refPixelX = readUnsignedShort();
		short refPixelY = readUnsignedShort();

		int len = headerSize - SpriteFactory.TCP_HEADER_SIZE;
		if (len < 0) {
			throw new IllegalStateException("帧延时信息错误: " + len);
		} else if (len != 0) {
			seek((int) (getPosition() + len));
		}

		headerSize += 2;
		seek(headerSize);

		palette = new short[256];
		for (int i = 0; i < palette.length; i++) {
			palette[i] = readUnsignedShort();
		}

		if (value != null && !value.isEmpty()) {
			SpriteFactory.assignColor(palette, value);
		}

		seek(headerSize + 512);
		Frame[] frames = new Frame[animCount * frameCount];

		for (int i = 0; i < frames.length; i++) {
			if (is && animCount == 4 && i == frameCount) {
				seek((int) (getPosition() + frameCount * 4));
			}
			int pos = readInt();
			frames[i] = new Frame(pos != 0 ? pos + headerSize : 0);
		}
		return new Sprite(frames, (is && animCount == 4) ? 2 : animCount, frameCount, this, refPixelX, refPixelY);
	}

	/**初始化*/
	public Animation initTwo() throws IOException{
		short headerSize = readUnsignedShort();
        short animCount = readUnsignedShort();
        short frameCount = readUnsignedShort();
        short width = readUnsignedShort();
        short height = readUnsignedShort();
        short refPixelX = readUnsignedShort();
        short refPixelY = readUnsignedShort();
        int len = headerSize - SpriteFactory.TCP_HEADER_SIZE;
        if (len < 0) {
            throw new IllegalStateException("帧延时信息错误: " + len);
        }else if (len!=0){
        	seek((int)(getPosition()+len));
        }
        headerSize+=2;//添加多余数据标识
        seek(headerSize);
        palette = new short[256];
        for (int i = 0; i < 256; i++) {
            palette[i] =readUnsignedShort();
        }
        //帧偏移列表
        seek(headerSize + 512);
        Vector<Frame> frames=new Vector<Frame>();
        for (int i = 0; i < frameCount; i++) {
        	int pos=readInt();
        	if (pos!=0) {pos+=headerSize;}
        	Frame frame=new Frame(pos);
        	frames.add(frame);
		}
        for (int i = 0; i < frameCount; i++) {
        	BufferedImage image=SpriteFactory.createDrawTwo(this,frames.get(i),frameCount,refPixelX,refPixelY,width,height);
        	frames.get(i).setImage(image);	
        }
        remve();
        return new Animation(frames);
	}
    public void seek(int pos) {
		if (pos < 0 || pos > this.count) { throw new IndexOutOfBoundsException("" + pos + ":" + this.count); }
		this.pos = pos;
	}
	public long getPosition() {
		return this.pos;
	}
    public long readLong() throws IOException {
    	int ch1 = read();
		int ch2 = read();
		int ch3 = read();
		int ch4 = read();
		long ch5 = read();
		long ch6 = read();
		long ch7 = read();
		long ch8 = read();
		return ch1 + (ch2 << 8) + (ch3 << 16) + (ch4 << 24) + (ch5 << 32) + (ch6 << 40) + (ch7 << 48) + (ch8 << 56);
	}
    public int readInt() throws IOException {
		int ch1 = read();
		int ch2 = read();
		int ch3 = read();
		int ch4 = read();
		return (ch1 + (ch2 << 8) + (ch3 << 16) + (ch4 << 24));
	}
	
	public short readUnsignedShort() throws IOException {
		int ch1 = read();
		int ch2 = read();
		return (short) ((ch2 << 8) + ch1);
	}
	
	public boolean readFully(byte[]buf) throws IOException {
		read(buf);
		return false;
	}
	public short[] getPalette() { return palette; }
	public void remve(){
		try {
		   	close();
	    	this.buf = null;
	 	    this.count = 0;			
		} catch (Exception e) {
			// TODO: handle exception
		}
 	}
}
