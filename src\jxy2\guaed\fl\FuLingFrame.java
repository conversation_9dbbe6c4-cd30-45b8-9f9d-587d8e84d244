package jxy2.guaed.fl;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class FuLing<PERSON>rame extends JInternalFrame  implements MouseListener {
    private FuLingJPanel fuLingJPanel;
    private int first_x, first_y;
    public static FuLingFrame getFuLingFrame() {
        return (FuLingFrame) FormsManagement.getInternalForm(141).getFrame();
    }

    public FuLingFrame() {
        this.fuLingJPanel = new FuLingJPanel();
        this.setContentPane(fuLingJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        ((BasicInternalFrameUI) this.getUI()).setNorthPane(null);// 去除顶部的边框
        this.setBackground(UIUtils.Color_BACK);
        this.setBounds(222, 98, 633, 507);
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {

            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(),getWidth(),getHeight());
                    updateLoadGuardianFramePosition();
                }
            }
        });

    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }
    private void updateLoadGuardianFramePosition() {
        if (!LoadGuardianFrame.getLoadGuardianFrame().isVisible()) return;
        int x = getX(), y = getY();
        int offsetX = -50, offsetY = 220;

        // 根据条件动态调整偏移量
        if (fuLingJPanel.getType() == 1) {
            offsetY = 130;
            offsetX = fuLingJPanel.getCardJPanel()
                    .getRonglianJPanel()
                    .getType() == 2 ? -50 : 240;
        }

        LoadGuardianFrame.getLoadGuardianFrame().setBounds(
                x + offsetX,
                y + offsetY,
                370,
                170
        );
    }
    @Override
    public void mousePressed(MouseEvent e) {
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(141);
            FormsManagement.HideForm(142);
            AsoulJPanel asoulJPanel = fuLingJPanel.getCardJPanel().getAsoulJPanel();
            asoulJPanel.getDetermine().handleEnchantingState(2,asoulJPanel);
        }else {
            FormsManagement.Switchinglevel(141);
            if (LoadGuardianFrame.getLoadGuardianFrame().isVisible()) {
                FormsManagement.Switchinglevel(142);
            }
        }
        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public FuLingJPanel getFuLingJPanel() {
        return fuLingJPanel;
    }

    public void setFuLingJPanel(FuLingJPanel fuLingJPanel) {
        this.fuLingJPanel = fuLingJPanel;
    }

    public int getFirst_y() {
        return first_y;
    }

    public void setFirst_y(int first_y) {
        this.first_y = first_y;
    }

    public int getFirst_x() {
        return first_x;
    }

    public void setFirst_x(int first_x) {
        this.first_x = first_x;
    }
}
