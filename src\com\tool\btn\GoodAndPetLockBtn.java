package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.Xy2oView.PackMainJPanel;
import jxy2.Xy2oView.TestRankJPanel;
import jxy2.jutnil.Juitil;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Util;

import java.awt.*;
import java.awt.event.MouseEvent;

public class GoodAndPetLockBtn extends MoBanBtn {
	private String text;
	private int locktype,index;
	private PackMainJPanel panel;
	public GoodAndPetLockBtn(String iconpath, int type, int locktype) {
		super(iconpath, type);
		this.locktype = locktype;
		// TODO Auto-generated constructor stub
	}
	public GoodAndPetLockBtn(String iconpath, int type, int locktype, String text, Color[] colors, PackMainJPanel panel,int index) {
		super(iconpath, type,colors);
		this.locktype = locktype;
		this.text = text;
		this.panel = panel;
		this.index = index;
		// TODO Auto-generated constructor stub
	}

	@Override
	protected void paintComponent(Graphics g) {
		super.paintComponent(g);
		Graphics2D g2d = (Graphics2D) g.create();
		if (text != null) {
			g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
			if (type==-1){type=0;}
			Juitil.Txtpet(g, zhen==0?6:zhen==2?4:3, zhen==2?21:20,text,Color.WHITE, UIUtils.NEWTX_HY17);
		}
	}


	private static StringBuilder getStringBuilder(String text) {
		String[] chars = text.split("");
		// 创建一个新的HTML字符串，将每个字符放在一个单独的<br>换行符分隔的行上
		StringBuilder verticalTextBuilder = new StringBuilder("<html>");
		for (String charStr : chars) {
			verticalTextBuilder.append(charStr).append("<br>");
		}
		verticalTextBuilder.append("</html>");
		return verticalTextBuilder;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub



        switch (locktype) {
            case 1: //物品加锁
                MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE10);
                break;
            case 2: //物品解锁
                if (Util.canBuyOrno) {//已解锁
                    MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE11);
                } else {//未解锁
                    if (RoleData.getRoleData().getLoginResult().getPassword() == null) {
                        FormsManagement.showForm(32);
                    } else {
                        FormsManagement.showForm(33);
                    }
                }
                break;
            case 3: //召唤兽加锁
                MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE10);
                break;
            case 4: //召唤兽解锁
                if (Util.canBuyOrno) {//已解锁
                    MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE11);
                } else {//未解锁
                    if (RoleData.getRoleData().getLoginResult().getPassword() == null) {
                        FormsManagement.showForm(32);
                    } else {
                        FormsManagement.showForm(33);
                    }
                }
                break;
			case 6:
				panel.getCardJPanel().getCardLayout().show(panel.getCardJPanel(), "knapsack");
				panel.titie = "背包";
				break;
			case 7:
				panel.getCardJPanel().getCardLayout().show(panel.getCardJPanel(), "rank");
				panel.titie = "境界";

				break;
			case 8:
				panel.getCardJPanel().getCardLayout().show(panel.getCardJPanel(), "xianyi");
				panel.titie = "仙翼";
				break;




        }

		if (panel!=null){
			handleButtonClick(index);
		}
	}
	private void handleButtonClick(int clickedIndex) {
		// 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
		for (int i = 0; i <panel.vnavi.length; i++) {
			if (i != clickedIndex) {
				panel.vnavi[i].btnchange(2);
			}
		}
	}
}
