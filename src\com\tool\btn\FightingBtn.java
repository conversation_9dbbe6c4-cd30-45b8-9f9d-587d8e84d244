package com.tool.btn;

import com.tool.PanelDisplay.PetPanelShow;
import com.tool.image.ImageMixDeal;
import com.tool.imagemonitor.FightingMonitor;
import come.tool.Fighting.FightingEvents;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.Fightingimage;
import come.tool.handle.HandleState;
import jxy2.jutnil.Juitil;
import org.come.Frame.SkillMsgJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.FightOperation;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Util;

import java.awt.event.MouseEvent;

public class FightingBtn extends MoBanBtn {

    int btntype;

    public FightingBtn(int type, String text, int btntype) {
        super(Util.SwitchUI==1 ?Juitil.bt38: Juitil.bt39, btntype,type);
        setNtext(text);
        // TODO Auto-generated constructor stub
        this.btntype = btntype;
    }
    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        btnjt();
    }

    /** 判断属于哪个按钮点击调用不同的方法 */
    public void btnjt() {
        switch (btntype) {
        case 0:
            Btnzidong();
            break;
        case 1:
            Btnfashu();
            break;
        case 2:
            Btndaoju();
            break;
        case 3:
            Btnfangyu();
            break;
        case 4:
            Btnbaohu();
            break;
        case 5:
            Btnzhaohuan();
            break;
        case 6:
            Btnzhaohui();
            break;
        case 7:
            Btnbuzhua();
            break;
        case 8:
            Btntaopao();
            break;
        default:
            break;
        }
    }

    /** 自动点击 */
    public static void Btnzidong() {
        if (ZhuFrame.getZhuJpanel().getZidong().getText().equals("取消")) {
            ZhuFrame.getZhuJpanel().getZidong().setText("自动");
            FightingMixDeal.zdhh = 0;
        } else if (ZhuFrame.getZhuJpanel().getZidong().getText().equals("自动")) {
            FightingMixDeal.zdhh = 9999;
            ZhuFrame.getZhuJpanel().getZidong().setText("取消");
        } else if (ZhuFrame.getZhuJpanel().getZidong().getText().equals("离开")) {
            String[] teams = ImageMixDeal.userimg.getRoleShow().getTeam().split("\\|");
            if (teams[0].equals(ImageMixDeal.userimg.getRoleShow().getRolename())) {
                String fightMes = Agreement.getAgreement().battleConnectionAgreement("-1");
                SendMessageUntil.toServer(fightMes);
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("队员没法主动离开观战");
            }
        }
    }

    /**
     * 法术点击
     */
    public static void Btnfashu() {
        if (!FormsManagement.getInternalForm(34).getFrame().isVisible()) {
            if (FightingMixDeal.State == HandleState.HANDLE_PLAYER || FightingMixDeal.State == HandleState.HANDLE_PET) {
                Fightingimage fightingimage = FightingMixDeal.getdata(FightingMixDeal.State - 1);
                SkillMsgJframe.getSkillMsgJframe().getSkillMsgJpaenl()
                        .showSkill(fightingimage.getFightingManData().cxxx("技能"), FightingMixDeal.State);
            }
        } else {
            FormsManagement.HideForm(34);
        }
    }

    /**
     * 道具点击
     */
    public void Btndaoju() {
        if (!FormsManagement.getInternalForm(2).getFrame().isVisible()) {
            FormsManagement.showForm(2);
        } else {
            FormsManagement.HideForm(2);
        }
    }

    /** 防御点击 */
    public static void Btnfangyu() {
        FightOperation operation = FightingMonitor.getOperation();
        operation.Record(-1, -1, 5, null);
        FightingMonitor.execution(operation);
    }

    /** 保护点击 */
    public static void Btnbaohu() {
        FightingMonitor.mousesname = "保护";
        FightingMonitor.mousestate = 3;
        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE8);
        ZhuFrame.getZhuJpanel().HideBeastBtn();
    }

    /** 召唤点击 */
    public static void Btnzhaohuan() {
        if (!FormsManagement.getframe(6).isVisible()) {
            if (FightingMixDeal.State == HandleState.HANDLE_PLAYER) {
                PetPanelShow.Show();
            }
        } else {
            FormsManagement.HideForm(6);
        }
    }

    /** 召回点击 */
    public static void Btnzhaohui() {
        // 判断是否有召唤兽在
        if (FightingMixDeal.MyBeastLifeAndDeath()) {
            FightingMonitor.FightingOperation(SpellGenerate("召回"));
            if (FightingMixDeal.State == HandleState.HANDLE_PLAYER) {
                if (FightingMixDeal.MyBeastLifeAndDeath()) {
                    FightingMixDeal.changeState(HandleState.HANDLE_PET);
                    ZhuFrame.getZhuJpanel().HideBeastBtn();
                    ZhuFrame.getZhuJpanel().ShowBeastBtn();
                } else {
                    FightingMixDeal.changeState(HandleState.HANDLE_WAIT);
                    FightingMixDeal.RoundFighting();
                }
            }
        }
    }

    /**
     * 捕抓点击
     */
    public static void Btnbuzhua() {
        FightingMonitor.mousestate = 4;
        FightingMonitor.mousesname = "捕捉";
        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE7);
        ZhuFrame.getZhuJpanel().HideBeastBtn();
    }

    /**
     * 逃跑点击
     */
    public static void Btntaopao() {
        FightOperation operation = FightingMonitor.getOperation();
        operation.Record(-1, -1, 6, null);
        FightingMonitor.execution(operation);

    }

    /**
     * 将攻击者和被攻击者位置传入 生成法术攻击数据 type 0是玩家 1是召唤兽
     */
    public static FightingEvents SpellGenerate(String type) {
        FightingEvents fEvents = new FightingEvents();
        if (FightingMixDeal.State == HandleState.HANDLE_PLAYER) {
            fEvents.setOriginator(FightingMixDeal.FightingState(type, 0));
        } else {
            fEvents.setOriginator(FightingMixDeal.FightingState(type, 1));
        }
        return fEvents;
    }
}
