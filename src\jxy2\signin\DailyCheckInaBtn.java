package jxy2.signin;

import com.tool.btn.MoBanBtn;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

import java.awt.event.MouseEvent;
import java.time.LocalDateTime;

public class DailyCheckInaBtn extends MoBanBtn {
    public DailyCheckInJPanel dailyCheckInJPanel;
    public int index;
    public DailyCheckInaBtn(String iconpath, int type ,String text, DailyCheckInJPanel dailyCheckInJPanel, int index, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prompt,text);
        this.dailyCheckInJPanel = dailyCheckInJPanel;
        this.index = index;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        ApplyQianDao applyQianDao;
        String sendmes;
        if (index<3) {
            for (int i = 0; i < dailyCheckInJPanel.getQiandaoday().length; i++) {
                dailyCheckInJPanel.getQiandaoday()[i].setText("");
            }
        }
        //如何事件  当前月份是8月，那么当按钮事件是1的时候，最大提升到10月。则 index==2时 月份最大可减少到 6 月， 也就提升于减少多是2个月
        switch (index) {
            case 1:
                // 如果 index 为 1，则增加月份
                dailyCheckInJPanel.updateDayOfMonth(changeMonth(2));
                break;
            case 2:
                dailyCheckInJPanel.updateDayOfMonth(changeMonth(1));
                break;
            case 3:
                if (LocalDateTime.now().getMonthValue() != newMonth) {
                    ZhuFrame.getZhuJpanel().addPrompt2("#不是当前月无法签到！");
                    return;
                }
                applyQianDao = new ApplyQianDao();
                applyQianDao.setType(2);
                applyQianDao.setDayri(DailyCheckInJPanel.getNowDay());
                sendmes = Agreement.getAgreement().PcQiandaoAgreement(GsonUtil.getGsonUtil().getgson().toJson(applyQianDao));
                SendMessageUntil.toServer(sendmes);
                break;
            case 4: //补签
                if (dailyCheckInJPanel.chooseqiandao == null) {
                    ZhuFrame.getZhuJpanel().addPrompt2("请选择可补签的日期!!");
                    return;
                }
                int selectDate = Integer.parseInt(dailyCheckInJPanel.chooseqiandao);
                applyQianDao = new ApplyQianDao();
                applyQianDao.setType(3);
                applyQianDao.setDayri(selectDate);
                sendmes = Agreement.getAgreement().PcQiandaoAgreement(GsonUtil.getGsonUtil().getgson().toJson(applyQianDao));
                SendMessageUntil.toServer(sendmes);
                dailyCheckInJPanel.chooseqiandao = null;
                break;
        }
        if (index>=5&&index<=9){
            applyQianDao = new ApplyQianDao();
            applyQianDao.setType(4);
            applyQianDao.setDayri(index-4);
            sendmes = Agreement.getAgreement().PcQiandaoAgreement(GsonUtil.getGsonUtil().getgson().toJson(applyQianDao));
            SendMessageUntil.toServer(sendmes);
        }else if (index>=10&&index<=14){
            applyQianDao = new ApplyQianDao();
            applyQianDao.setType(5);
            applyQianDao.setDayri(index-4);
            sendmes = Agreement.getAgreement().PcQiandaoAgreement(GsonUtil.getGsonUtil().getgson().toJson(applyQianDao));
            SendMessageUntil.toServer(sendmes);
        }

    }


    public static int newMonth = LocalDateTime.now().getMonthValue();// 当前月份
    /**
     * 根据给定的索引值更改月份
     * 该方法旨在以当前月份为基础，根据索引值增加或减少月份
     * 它通过循环月份的方式来处理月份的边界情况（即12月后回到下一年的1月，1月前回到上一年的12月）
     * 同时，方法还限制了月份的变化范围，使其不能超过当前月份前后两个月
     *
     * @param index 用于指定月份变化的方向1代表增加月份，2代表减少月份
     * @return 返回更改后的月份
     */
    public static int changeMonth(int index) {
        // 最大允许的新月份
        int maxMonth = LocalDateTime.now().getMonthValue() + 2;
        // 最小允许的新月份
        int minMonth = LocalDateTime.now().getMonthValue() - 2;
        if (index == 1) {
            // 如果 index 为 1，则增加月份
            newMonth += 1;
            // 如果超过12月，则回到下一年的对应月份
            if (newMonth > 12) {
                newMonth -= 12;
            }
            // 确保新月份不超过最大允许的新月份
            if (newMonth > maxMonth) {
                newMonth = maxMonth;
            }
        } else if (index == 2) {
            // 如果 index 为 2，则减少月份
            newMonth -= 1;
            // 如果小于1月，则回到上一年的对应月份
            if (newMonth < 1) {
                newMonth += 12;
            }
            // 确保新月份不低于最小允许的新月份
            if (newMonth < minMonth) {
                newMonth = minMonth;
            }
        }
        return newMonth;
    }
}
