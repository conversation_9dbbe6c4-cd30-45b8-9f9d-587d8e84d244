package jxy2.jutnil;

import java.awt.*;
import java.awt.event.*;
import javax.swing.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * Native Image AWT 事件系统修复工具
 * 解决 Native Image 中 KeyEvent 构造函数和事件分发的问题
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class NativeImageAWTFix {
    
    private static boolean isInitialized = false;
    private static boolean isNativeImage = false;
    
    /**
     * 初始化 Native Image AWT 修复
     */
    public static void initializeAWTFix() {
        if (isInitialized) {
            return;
        }
        
        // 检测是否为 Native Image 环境
        isNativeImage = System.getProperty("org.graalvm.nativeimage.imagecode") != null;
        
        if (isNativeImage) {
            ConsoleUtil.println("[AWT修复] 检测到 Native Image 环境，开始初始化 AWT 事件系统修复...");
            
            try {
                // 1. 预热 KeyEvent 构造函数
                preloadKeyEventConstructors();
                
                // 2. 预热 MouseEvent 构造函数
                preloadMouseEventConstructors();
                
                // 3. 初始化事件分发系统
                initializeEventDispatchSystem();
                
                // 4. 设置事件队列
                setupEventQueue();

                // 5. 测试 KeyEvent 工厂
                KeyEventFactory.testKeyEventCreation();

                ConsoleUtil.println("[AWT修复] Native Image AWT 事件系统修复完成");
                
            } catch (Exception e) {
                ConsoleUtil.printlnErr("[AWT修复] AWT 事件系统修复失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        isInitialized = true;
    }
    
    /**
     * 预加载 KeyEvent 构造函数
     */
    private static void preloadKeyEventConstructors() {
        try {
            // 创建一个临时组件用于测试
            Component dummyComponent = new Canvas();
            
            // 预加载所有 KeyEvent 构造函数
            Constructor<?>[] constructors = KeyEvent.class.getDeclaredConstructors();
            for (Constructor<?> constructor : constructors) {
                constructor.setAccessible(true);
            }
            
            // 创建测试 KeyEvent 实例
            KeyEvent testEvent = new KeyEvent(
                dummyComponent,
                KeyEvent.KEY_PRESSED,
                System.currentTimeMillis(),
                0,
                KeyEvent.VK_A,
                'a'
            );
            
            ConsoleUtil.println("[AWT修复] KeyEvent 构造函数预加载完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[AWT修复] KeyEvent 构造函数预加载失败: " + e.getMessage());
        }
    }
    
    /**
     * 预加载 MouseEvent 构造函数
     */
    private static void preloadMouseEventConstructors() {
        try {
            // 创建一个临时组件用于测试
            Component dummyComponent = new Canvas();
            
            // 预加载所有 MouseEvent 构造函数
            Constructor<?>[] constructors = MouseEvent.class.getDeclaredConstructors();
            for (Constructor<?> constructor : constructors) {
                constructor.setAccessible(true);
            }
            
            // 创建测试 MouseEvent 实例
            MouseEvent testEvent = new MouseEvent(
                dummyComponent,
                MouseEvent.MOUSE_CLICKED,
                System.currentTimeMillis(),
                0,
                100, 100,
                1,
                false
            );
            
            ConsoleUtil.println("[AWT修复] MouseEvent 构造函数预加载完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[AWT修复] MouseEvent 构造函数预加载失败: " + e.getMessage());
        }
    }
    
    /**
     * 初始化事件分发系统
     */
    private static void initializeEventDispatchSystem() {
        try {
            // 确保 EventQueue 正确初始化
            EventQueue eventQueue = Toolkit.getDefaultToolkit().getSystemEventQueue();
            
            // 预热事件分发机制
            SwingUtilities.invokeLater(() -> {
                // 空操作，只是为了初始化事件分发线程
            });
            
            ConsoleUtil.println("[AWT修复] 事件分发系统初始化完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[AWT修复] 事件分发系统初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置事件队列
     */
    private static void setupEventQueue() {
        try {
            // 获取系统事件队列
            EventQueue systemQueue = Toolkit.getDefaultToolkit().getSystemEventQueue();
            
            // 设置自定义事件队列（如果需要）
            // 这里可以添加事件过滤或处理逻辑
            
            ConsoleUtil.println("[AWT修复] 事件队列设置完成");
            
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[AWT修复] 事件队列设置失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建安全的 KeyEvent 实例
     * 使用 KeyEventFactory 来处理 Native Image 兼容性
     */
    public static KeyEvent createSafeKeyEvent(Component source, int id, long when,
                                            int modifiers, int keyCode, char keyChar) {
        return KeyEventFactory.createKeyEventSmart(source, id, when, modifiers, keyCode, keyChar);
    }
    
    /**
     * 创建安全的 MouseEvent 实例
     */
    public static MouseEvent createSafeMouseEvent(Component source, int id, long when,
                                                int modifiers, int x, int y, int clickCount, boolean popupTrigger) {
        try {
            if (isNativeImage) {
                // 在 Native Image 中使用反射创建
                Constructor<MouseEvent> constructor = MouseEvent.class.getDeclaredConstructor(
                    Component.class, int.class, long.class, int.class, 
                    int.class, int.class, int.class, boolean.class
                );
                constructor.setAccessible(true);
                return constructor.newInstance(source, id, when, modifiers, x, y, clickCount, popupTrigger);
            } else {
                // 在普通 JVM 中直接创建
                return new MouseEvent(source, id, when, modifiers, x, y, clickCount, popupTrigger);
            }
        } catch (Exception e) {
            ConsoleUtil.printlnErr("[AWT修复] 创建 MouseEvent 失败: " + e.getMessage());
            // 回退到直接构造
            return new MouseEvent(source, id, when, modifiers, x, y, clickCount, popupTrigger);
        }
    }
    
    /**
     * 检查是否为 Native Image 环境
     */
    public static boolean isNativeImageEnvironment() {
        return isNativeImage;
    }
}
