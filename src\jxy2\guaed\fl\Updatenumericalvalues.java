package jxy2.guaed.fl;

import java.util.ArrayList;
import java.util.List;

/**
 * 更新同步组件数值显示数据
 * @Author: <PERSON><PERSON><PERSON>
 * @DateTime: 2025/2/8 11:09
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class Updatenumericalvalues {
    // 单例实例
    private static Updatenumericalvalues instance;

    // 监听器列表
    private List<IValueUpdateListener> listeners;

    private Updatenumericalvalues() {
        listeners = new ArrayList<>();
    }

    public static Updatenumericalvalues getInstance() {
        if (instance == null) {
            instance = new Updatenumericalvalues();
        }
        return instance;
    }

    public void addListener(IValueUpdateListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 同步数据
     */
    public static void Updaten() {
        System.out.println("当前监听器数量: " + getInstance().listeners.size());
        // 通知所有监听器
        for (IValueUpdateListener listener : getInstance().listeners) {
            try {
                listener.onValueUpdate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
