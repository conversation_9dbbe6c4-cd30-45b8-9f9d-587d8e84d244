package com.tool.btn;

import com.tool.imagemonitor.FightingMonitor;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.Fightingimage;
import come.tool.Fighting.TypeState;
import come.tool.handle.HandleState;
import jxy2.roleRelated.PetSwitchJPanel;
import jxy2.soul.ResetSoulSkillFrame;
import org.come.Frame.PetEquipmentJframe;
import org.come.Frame.RolePetResistanceJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.*;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChosePetSkillsMouslisten;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.util.List;

/**
 * 召唤兽面板按钮
 *
 * <AUTHOR>
 *
 */
public class PetPanelBtn extends MoBanBtn {
    private PetSwitchJPanel petSwitchJPanel;
    /**1是改名 2是参战 3休息 4加点 5驯养 6技能 7物品 8炼妖 9 放生 10抗性 11 召唤*/
    private Integer typeBtn;
    public int index;


    public PetPanelBtn(String iconpath, int type, int typeBtn, String labelName,
                       PetSwitchJPanel petSwitchJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.typeBtn = typeBtn;
        this.petSwitchJPanel = petSwitchJPanel;
    }

    public PetPanelBtn(String iconpath, int type, String text, int typeBtn, String prowpt, PetSwitchJPanel petSwitchJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0,UIUtils.COLOR_BTNTEXT,prowpt);
        this.typeBtn = typeBtn;
        this.petSwitchJPanel = petSwitchJPanel;
        setFont(UIUtils.TEXT_FONT);
        this.setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }


    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (UserMessUntil.getChosePetMes() == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("您还没有选中召唤兽呢!!");
            return;
        }
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        // TODO Auto-generated method stub
        if (typeBtn == 6) {
            ChosePetSkillsMouslisten.refreshPetSkills();
            // 展示技能面板
            FormsManagement.showForm(18);
        } else if (typeBtn == 7 && FightingMixDeal.State == HandleState.USUAL) {
            ZhuJpanel.setUseGoodsType(1);
            PetEquipmentJframe.getPetEquipmentJframe().getEquipmentJpanel().showPet(UserMessUntil.getChosePetMes());
            FormsManagement.HideForm(2);
            FormsManagement.upgradForm(67);
        }else if (FightingMixDeal.State == HandleState.HANDLE_PLAYER && typeBtn == 11) {
            Fightingimage fightingimage = FightingMixDeal.getdata(0);
            List<TypeState> data = fightingimage.getFightingManData().cxxx("召唤兽");
            int sid = UserMessUntil.getChosePetMes().getSid().intValue();
            for (int i = 0; i < data.size(); i++) {
                if (data.get(i).getState() != 0)
                    continue;
                if (sid == Integer.parseInt(data.get(i).getType())) {
                    if (FightingMixDeal.State != HandleState.HANDLE_PLAYER)
                        return;
                    FormsManagement.HideForm(6);
                    // 生成召唤指令
                    FightingMonitor.FightingOperation(FightingBtn.SpellGenerate("召唤&" + data.get(i).getType()));
                    FightingMonitor.operateEnd();
                    return;
                }
            }
            ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽无法召唤");
        }else if (typeBtn == 22) {
            //TODO 重置魂技技能
            FormsManagement.showForm(115);
            FormsManagement.upgradForm(115);
            ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl().InitializeSkillList();
        }else  if (typeBtn == 23) {
            // 根骨输入框转变
            pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[0].getText())-pet.getExtra("根骨"));
            // 灵性输入框
            pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[1].getText())-pet.getExtra("灵性"));
            // 力量输入框
            pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[2].getText())-pet.getExtra("力量"));
            // 敏捷输入框
            pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[3].getText())-pet.getExtra("敏捷"));
            if (pet.getTurnRount() >= 4) {
                // 定力输入框
//                pet.setCalm(Integer.parseInt(TestPetJpanel.getLabconcentrate().getText())-pet.getExtra("定力"));
            }
            String mes = Agreement.getAgreement().DemonsAgreement(
                    "D_Y_"+pet.getSid()+"_" + pet.getBone() + "=" + pet.getSpir() + "=" + pet.getPower() + "="
                            + pet.getSpeed() + "=" + pet.getCalm());
            SendMessageUntil.toServer(mes);
            ZhuFrame.getZhuJpanel().addPrompt2("#G保存成功");
        }else if (typeBtn==24){
            // 根骨输入框转变
            pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_R()[0].getText())-pet.getExtra("根骨"));
            // 灵性输入框
            pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_R()[1].getText())-pet.getExtra("灵性"));
            // 力量输入框
            pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_R()[2].getText())-pet.getExtra("力量"));
            // 敏捷输入框
            pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_R()[3].getText())-pet.getExtra("敏捷"));
            if (pet.getTurnRount() >= 4) {
                // 定力输入框
//                pet.setCalm(Integer.parseInt(TestPetJpanel.getLabconcentrate().getText())-pet.getExtra("定力"));
            }
            String mes = Agreement.getAgreement().DemonsAgreement(
                    "D_R_"+pet.getSid()+"_" + pet.getBone() + "=" + pet.getSpir() + "=" + pet.getPower() + "="
                            + pet.getSpeed() + "=" + pet.getCalm());
            SendMessageUntil.toServer(mes);
            ZhuFrame.getZhuJpanel().addPrompt2("#G保存成功");
        }else if (typeBtn == 25){
            // 根骨输入框转变
            pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_S()[0].getText())-pet.getExtra("根骨"));
            // 灵性输入框
            pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_S()[1].getText())-pet.getExtra("灵性"));
            // 力量输入框
            pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_S()[2].getText())-pet.getExtra("力量"));
            // 敏捷输入框
            pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_S()[3].getText())-pet.getExtra("敏捷"));
            if (pet.getTurnRount() >= 4) {
                // 定力输入框
//                pet.setCalm(Integer.parseInt(TestPetJpanel.getLabconcentrate().getText())-pet.getExtra("定力"));
            }
            String mes = Agreement.getAgreement().DemonsAgreement(
                    "D_S_"+pet.getSid()+"_" + pet.getBone() + "=" + pet.getSpir() + "=" + pet.getPower() + "="
                            + pet.getSpeed() + "=" + pet.getCalm());
            SendMessageUntil.toServer(mes);
            ZhuFrame.getZhuJpanel().addPrompt2("#G保存成功");
        }else if (typeBtn == 26){
            if (petSwitchJPanel.xzType!=null&&!petSwitchJPanel.xzType.isEmpty()) {
                //刷新切换的属性
                Refresh(petSwitchJPanel.xzType);
                // 增加对应的属性
                PetAddPointMouslisten.showPetValue();
                String mes = Agreement.getAgreement().DemonsAgreement("Q" + petSwitchJPanel.xzType+"&"+pet.getSid());
                SendMessageUntil.toServer(mes);
            }
        }
        if (typeBtn!=17){
            FormsManagement.HideForm(112);
        }


    }

    private void Refresh(String xzType) {
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        switch (xzType){
            case "D_Y":
                pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[0].getText())
                        - pet.getExtra("根骨"));
                // 灵性输入框
                pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[1].getText())
                        - pet.getExtra("灵性"));
                // 力量输入框
                pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[2].getText())
                        - pet.getExtra("力量"));
                // 敏捷输入框
                pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[3].getText())
                        - pet.getExtra("敏捷"));
                if (pet.getTurnRount() >= 4) {
                    // 定力输入框
                    pet.setCalm(Integer.parseInt(petSwitchJPanel.getLabnames_Y()[4].getText())
                            - pet.getExtra("定力"));
                }
                break;
            case "D_R":
                pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_R()[0].getText())
                        - pet.getExtra("根骨"));
                // 灵性输入框
                pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_R()[1].getText())
                        - pet.getExtra("灵性"));
                // 力量输入框
                pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_R()[2].getText())
                        - pet.getExtra("力量"));
                // 敏捷输入框
                pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_R()[3].getText())
                        - pet.getExtra("敏捷"));
                if (pet.getTurnRount() >= 4) {
                    // 定力输入框
                    pet.setCalm(Integer.parseInt(petSwitchJPanel.getLabnames_R()[4].getText())
                            - pet.getExtra("定力"));
                }
                break;
            case "D_S":
                pet.setBone(Integer.parseInt(petSwitchJPanel.getLabnames_S()[0].getText())
                        - pet.getExtra("根骨"));
                // 灵性输入框
                pet.setSpir(Integer.parseInt(petSwitchJPanel.getLabnames_S()[1].getText())
                        - pet.getExtra("灵性"));
                // 力量输入框
                pet.setPower(Integer.parseInt(petSwitchJPanel.getLabnames_S()[2].getText())
                        - pet.getExtra("力量"));
                // 敏捷输入框
                pet.setSpeed(Integer.parseInt(petSwitchJPanel.getLabnames_S()[3].getText())
                        - pet.getExtra("敏捷"));
                if (pet.getTurnRount() >= 4) {
                    // 定力输入框
                    pet.setCalm(Integer.parseInt(petSwitchJPanel.getLabnames_S()[4].getText())
                            - pet.getExtra("定力"));
                }
                break;
        }
    }

    public static void showRolesumming(Object model) {
        int a = -1;
        try {
            clearShuXingViews();
            for (java.lang.reflect.Field field : model.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                if (Double.parseDouble(field.get(model).toString()) != 0) {
                    String sx = null;
                    sx = JpanelOnJalbelBtn.getQuaralyPersonalName(field.getName());
                    String ping = GoodsMsgJpanel.tianjia(sx);
                    String numbers = sx.substring(sx.length() - 1, sx.length());
                    int number = Integer.parseInt(numbers);
                    String shuxingName = sx.substring(0, sx.length() - 2);

                    a++;
                    if (a % 2 == 0) {
                        if (ping.equals("%")) {
                            RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getMapResistanceModelPanel().get(number - 1)
                                    .getDlm().addElement(
                                            shuxingName
                                                    + ":"
                                                    + String.format("%.1f",
                                                    Double.parseDouble(field.get(model).toString())) + ping);

                        }else {
                            RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getMapResistanceModelPanel().get(number - 1)
                                    .getDlm().addElement(
                                            shuxingName + ":" + (int) Double.parseDouble(field.get(model).toString())
                                                    + ping);
                        }
                    } else {

                        if (ping.equals("%")) {
                            RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getMapResistanceModelPanel().get(number - 1)
                                    .getDlm1().addElement(
                                            shuxingName
                                                    + ":"
                                                    + String.format("%.1f",
                                                    Double.parseDouble(field.get(model).toString())) + ping);


                        } else {
                            RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getMapResistanceModelPanel().get(number - 1)
                                    .getDlm1().addElement(
                                            shuxingName + ":" + (int) Double.parseDouble(field.get(model).toString())
                                                    + ping);
                        }
                    }
                }


            }
            changViewSizeN();
        } catch (IllegalArgumentException | IllegalAccessException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /** 更新抗性面板大小 */
    public static void changViewSizeN() {
        for (int i = 0; i < RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel().length; i++) {
            int leftNum = RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i]
                    .getDlm().getSize();
            int rightNum = RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i]
                    .getDlm1().getSize();
            int num = Math.max(leftNum, rightNum);
            num = num > 0 ? (num * 17 + 34) : 34;
            int y = 0;
            for (int j = 0; j < i; j++) {
                y += RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[j]
                        .getHeight();
            }
            RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].setBounds(0, y,
                    290, num);
        }

        int y = 0;
        for (int i = 0; i < RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel().length; i++) {
            y += RolePetResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getHeight();
        }
        RolePetResistanceJframe.getResistancejframe().getResistancejpanel().setSize(290, y);
        RolePetResistanceJframe.getResistancejframe().setSize(290, y);
        RolePetResistanceJframe.getResistancejframe().getResistancejpanel().hight = y;
    }


    /**
     * 进行遍历展示召唤兽抗性
     */
    /**
     * 展示宠物属性到第五栏，并循环刷新其它属性栏
     * @param model 属性模型对象
     * @param index 当前操作栏索引
     * @param isExpanded 是否展开
     */
    public static void showRolesumming(Object model, int index, boolean isExpanded) {
        // 获取宠物抗性面板
        RolePetResistanceJpanel rolePetResistanceJpanel = RolePetResistanceJframe.getResistancejframe().getResistancejpanel();
        int a = -1; // 用于属性分组
        try {
            clearShuXingView(); // 清空所有属性栏显示
            // 获取当前选中的宠物对象
            RoleSummoning pet = UserMessUntil.getChosePetMes(); // 获取宠物对象，避免多次重复获取
            // 获取第五栏面板
            FaShuKangXingJpanel panel5 = rolePetResistanceJpanel.getMapResistanceModelPanel().get(4); // 获取第5栏
            // 如果第五栏存在，添加基础属性
            if (panel5 != null) {
                panel5.getDlm().addElement("成长率:" + pet.getGrowlevel()); // 添加成长率
                panel5.getDlm().addElement("气血初值:" + pet.getHp()); // 添加气血初值
                panel5.getDlm().addElement("法力初值:" + pet.getMp()); // 添加法力初值
                panel5.getDlm1().addElement("亲密度:" + pet.getFriendliness()); // 添加亲密度
                panel5.getDlm1().addElement("攻击初值:" + pet.getAp()); // 添加攻击初值
                panel5.getDlm1().addElement("速度初值:" + pet.getSp()); // 添加速度初值
                panel5.setAllowRefresh(isExpanded); // 设置是否允许刷新
                panel5.setExpanded(isExpanded); // 设置是否展开
            }
            // 反射遍历model的所有属性
            for (java.lang.reflect.Field field : model.getClass().getDeclaredFields()) {
                field.setAccessible(true); // 设置可访问
                Object valueObj = field.get(model); // 获取属性值
                if (valueObj == null) continue; // 跳过null值
                double value = Double.parseDouble(valueObj.toString()); // 转为double
                if (value != 0) { // 只处理非0属性
                    String sx = JpanelOnJalbelBtn.getQuaralyPersonalName(field.getName()); // 获取属性中文名
                    String ping = GoodsMsgJpanel.tianjia(sx); // 获取单位
                    String numbers = sx.substring(sx.length() - 1); // 获取属性编号
                    int number = Integer.parseInt(numbers); // 转为数字
                    String shuxingName = sx.substring(0, sx.length() - 2); // 获取属性名称
                    a++; // 属性分组计数

                    // 获取对应栏面板
                    FaShuKangXingJpanel panel = rolePetResistanceJpanel.getMapResistanceModelPanel().get(number - 1);
                    if (panel != null && panel.isAllowRefresh()) { // 如果面板存在且允许刷新
                        // 奇偶分组，决定加到哪个model
                        boolean toDlm = (a % 2 == 0);
                        String valueStr = ping.equals("%") ?
                                String.format("%.1f", value) + ping :
                                ((int) value) + ping; // 格式化数值
                        String element = shuxingName + ":" + valueStr; // 拼接显示字符串
                        if (toDlm) {
                            panel.getDlm().addElement(element); // 加到dlm
                        } else {
                            panel.getDlm1().addElement(element); // 加到dlm1
                        }
                    }
                }
            }
            changViewSize(index); // 调整界面大小
        } catch (Exception e) {
            e.printStackTrace(); // 打印异常
        }
    }

    /** 更新抗性面板大小 */
    public static void changViewSize(int index) {
        RolePetResistanceJpanel rolePetResistanceJpanel = RolePetResistanceJframe.getResistancejframe().getResistancejpanel();
        FaShuKangXingJpanel panel = rolePetResistanceJpanel.getMapResistanceModelPanel().get(index);
        if (panel != null && panel.isAllowRefresh()) {
            int leftNum = rolePetResistanceJpanel.getMapResistanceModelPanel().get(index)
                    .getDlm().getSize();
            int rightNum = rolePetResistanceJpanel.getMapResistanceModelPanel().get(index)
                    .getDlm1().getSize();
            rolePetResistanceJpanel.getMapResistanceModelPanel().get(index).getListNo1()
                    .setBounds(15, 34, 130, leftNum * 20);
            rolePetResistanceJpanel.getMapResistanceModelPanel().get(index).getListNo2()
                    .setBounds(150, 34, 130, rightNum * 20);
            int num = Math.max(leftNum, rightNum);
            num = num > 0 ? (num * 20 + 34) : 24;
            int y = 0;
            for (int j = 0; j < index; j++) {
                y += rolePetResistanceJpanel.getMapResistanceModelPanel().get(index)
                        .getHeight();
            }
            rolePetResistanceJpanel.getMapResistanceModelPanel().get(index).setBounds(0, y, 288, 100 + num);
        }
    }

    /** 清空抗性面板内容 */
    public static void clearShuXingView() {
        RolePetResistanceJpanel rolePetResistanceJpanel = RolePetResistanceJframe.getResistancejframe().getResistancejpanel();
        for (int i = 0; i < rolePetResistanceJpanel.getMapResistanceModelPanel().size(); i++) {
            FaShuKangXingJpanel panel = rolePetResistanceJpanel.getMapResistanceModelPanel().get(i);
            if (panel!=null) {
                panel.getDlm().removeAllElements();
                panel.getDlm1().removeAllElements();
            }
        }
    }

    /** 清空抗性面板内容 */
    public static void clearShuXingViews() {
        RolePetResistanceJpanel resistancejpanel = RolePetResistanceJframe.getResistancejframe().getResistancejpanel();
        for (int i = 0; i < resistancejpanel.getMapResistanceModelPanel().size(); i++) {
            FaShuKangXingJpanel panel = resistancejpanel.getShuXingJpanel()[i];
            if (panel!=null) {
                panel.getDlm().removeAllElements();
                panel.getDlm1().removeAllElements();
            }
        }
    }

    public Integer getTypeBtn() {
        return typeBtn;
    }

    public void setTypeBtn(Integer typeBtn) {
        this.typeBtn = typeBtn;
    }

}
