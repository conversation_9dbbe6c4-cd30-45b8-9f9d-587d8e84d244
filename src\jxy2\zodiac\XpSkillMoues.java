package jxy2.zodiac;

import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.bean.Skill;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class XpSkillMoues implements MouseListener {
    public ChineseZodiacPanel chineseZodiacPanel ;
    public int caozuo,type;
    public String skillID;
    public XpSkillMoues(ChineseZodiacPanel chineseZodiacPanel, int caozuo,String skillID,int type) {
        this.chineseZodiacPanel = chineseZodiacPanel;
        this.caozuo = caozuo;
        this.skillID = skillID;
        this.type = type;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        if (type==0) {
            for (Integer key : chineseZodiacPanel.allxpdlMap.keySet()) {
                if (key == caozuo) {
                    //触发激活技能逻辑
                    int x = ChineseZodiacFrame.getChineseZodiacFrame().getX();
                    int y = ChineseZodiacFrame.getChineseZodiacFrame().getY();
                    if (!FormsManagement.getframe(134).isVisible()) {
                        InlaidStarJPanel inlaidStarJPanel = InlaidStarFrame.getInlaidStarFrame().getInnerPanel();

                        FormsManagement.showForm(134);
                        inlaidStarJPanel.init(null, caozuo, chineseZodiacPanel.getIu(), 0);
                        InlaidStarFrame.getInlaidStarFrame().setBounds(x + 140, y + 108, 360, 255);
                        Juitil.addClosingButtonToPanel(inlaidStarJPanel, 134, 360);
                        Music.addyinxiao("开关窗口.mp3");
                    } else {
                        FormsManagement.HideForm(134);
                        Music.addyinxiao("关闭窗口.mp3");
                    }
                    break;
                }
            }
        }

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        Skill skill = UserMessUntil.getSkillId(skillID);
        if (skill != null){
            if (!chineseZodiacPanel.sumlist.isEmpty()){
                MsgJframe.getJframe().getJapnel().XPSkill(skill,chineseZodiacPanel.sumlist.get(0).equals(caozuo+""));
            }else {
                MsgJframe.getJframe().getJapnel().XPSkill(skill,false);
            }

        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        FormsManagement.HideForm(46);
    }
}
