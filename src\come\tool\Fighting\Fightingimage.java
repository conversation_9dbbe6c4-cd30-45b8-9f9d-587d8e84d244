package come.tool.Fighting;

import com.tool.image.ManimgAttribute;
import com.tool.tcp.NewPart;
import com.tool.tcpimg.FloatPanel;
import com.tool.tcpimg.UIUtils;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.bean.PathPoint;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;


/**
 * 战斗图片数据
 * 
 * <AUTHOR>
 * 
 */
public class Fightingimage {
	// 坐标
	private int x;
	private int y;
	// 数据显示 如伤害 扣蓝灯
	// 动作锁 0表示动作结束 1表示正在动作 2表示动作结束 3表示全部结束
	private int suo = 0;
	// 图像的属性
	private FightingManData fightingManData;
	// 消逝的时间
	private long time;
	private NewPart part;
	// 方向
	private int dir;
	private String[] names;
	/** 历史冒泡对话 */
	private List<FloatPanel> chatPanels = new ArrayList<>();
//	private List<ShadowMode> shadowModes;
	/*** 添加对话内容 */
	public void Dialogue(String text) {
		if (text==null||text.equals(""))return;
		chatPanels.add(new FloatPanel(text));
	}
	/** 初始化的方法 */
	public Fightingimage(FightingManData fightingManData, int my_Camp) {
		super();
		fightingManData.initState();
		this.fightingManData = fightingManData;
		
		if (this.fightingManData.ztcz("隐身")) {
			this.fightingManData.setAlpha(0.3f);
		}
		this.part=fightingManData.getPart();
		if (fightingManData.getHp_Current()<=0) {
			part.setAct(8);
		}else {
			part.setAct(7);
		}
        if (fightingManData.getCamp() == my_Camp||(fightingManData.getCamp() == 1 && my_Camp == -1)) {
			dir = 7;
		} else {
			dir = 3;
		}
		tiaoz(my_Camp);
		names=AccessTeamInfoUntil.getss(fightingManData.getManname());
		if (FightingMixDeal.CurrentRound != 1)return;
		if (fightingManData.getCamp() == 0&& fightingManData.getMan() == 2) {
			if (FightingMixDeal.BattleType == 15) {
				chatPanels.add(new FloatPanel("大胆狗贼!哪里逃"));
			} else if (FightingMixDeal.BattleType == 16) {
				chatPanels.add(new FloatPanel("现在年轻人真是没有眼力劲"));
			}
		}
		getmsg();
	}
    /**自动喊话*/
	public void getmsg(){
        if (fightingManData.getHp_Current()<=0)return;
		String msg=fightingManData.getMsg();
		if (msg==null||msg.equals(""))return;
		chatPanels.add(new FloatPanel(msg));
		FrameMessageChangeJpanel.addtext(0,msg,null,fightingManData.getManname());
	}
	/**画的方法*/
	public boolean Drawing(Graphics g, long dietime) {
		long time = this.time;
		if (part!=null) {
			fightingManData.draw2(g, time, x, y);
			if (part.getAct() == 8) {
				part.drawEnd(g, x, y, dir, fightingManData.getAlpha());
			} else {
				if (fightingManData.getType() == 3 && part.getAct() == 5) {
					part.drawBattle(g, ScrenceUntil.Screen_x / 2,ScrenceUntil.Screen_y / 2, dir, time,fightingManData.getAlpha());	
				}else {
					part.drawBattle(g, x, y, dir, time, fightingManData.getAlpha());
				}
			}
			drawArticle(g);
			fightingManData.draw1(g,time,x,y);
		}
		drawfont(g);
		return true;
	}

	public void Drawing2(Graphics g, long dietime) {
		getDisplays();
		for (int i = displays.size() - 1; i >= 0; i--) {
			if (displays.get(i).draw(g, x, y -25 + i * 20,fightingManData.getCamp(), dietime)) {
				displays.remove(i);
			}
		}
	}
	private boolean shouldDisplay(FloatPanel chatPanel) {
		return Util.getTime() - chatPanel.getCreateTime() < Util.TIME_CHAT;
	}
	public static ImageIcon[] xuelans;
	static {
		xuelans = new ImageIcon[3];
		// 底
		xuelans[0] = CutButtonImage.getImage("img/xy2uiimg/75_png.xy2uiimg.sempty.png",-1,-1);
		// hp
		xuelans[1] = CutButtonImage.getImage("img/xy2uiimg/6_png.xy2uiimg.hps.png",-1,-1);
		// mp
		xuelans[2] = CutButtonImage.getImage("img/xy2uiimg/2_png.xy2uiimg.mps.png",-1,-1);
	}
	/**
	 * 字体显示
	 */
	int size=0;
	public void drawfont(Graphics g) {
		// 人物冒泡对话内容
		if (chatPanels.size()!=0) {
			int py=part.getPy();
			if (py!=-1) {
				int chatY = y - py;
				for (int i = chatPanels.size() - 1; i >= 0; i--) {
					FloatPanel chatPanel = chatPanels.get(i);
					if (shouldDisplay(chatPanel)) {
						int chatX=x-chatPanel.getWidth()/2;
						chatY-=chatPanel.getHeight()+2;
						g.translate(chatX,   chatY);
						chatPanel.paint(g);
						g.translate(-chatX, -chatY);
					} else {
						chatPanel.remove();
						chatPanels.remove(i);
					}
				}
			}
		}
		if (names==null)return;
		g.setFont(UIUtils.TEXT_FONT42);
		int textY = y + 30;
		if (size==0) {
			size=org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(fightingManData.getManname())/2;
		}
		int textX = x - size;
		Graphics2D g2d = (Graphics2D) g.create();
		g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
		if (fightingManData.getW() != null) {
			Image hz = ManimgAttribute.getHzImg(fightingManData.getW());
			g2d.drawImage(hz, textX - 28, textY - 18, null);
		}
		if (fightingManData.getType() == 0) {
			g2d.setFont(UIUtils.nameFont);

			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			// 玩家字体颜色
			for (int i = 0; i < names.length; i++) {
				// 黑
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString(names[i], textX + 1, textY + 1);
				// 浅
				g2d.setColor(UIUtils.getcolor(fightingManData.getZs()));
				g2d.drawString(names[i], textX, textY);
				textX += fm.stringWidth(names[i])-1;
			}
		} else if (fightingManData.getType() == 1) {
			// 召唤兽字体颜色
			g.setFont(UIUtils.nameFont);
			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			for (int i = 0; i < names.length; i++) {
				// 黑
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString(names[i], textX + 1, textY + 1);
				g2d.setColor(UIUtils.getPetcolor(fightingManData.getZs()));
				g2d.drawString(names[i], textX, textY);
				textX += fm.stringWidth(names[i])-1;
			}
		}else if (fightingManData.getType() == 2) {
			// 野怪
			g.setFont(UIUtils.nameFont);
			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			if (fightingManData.getType()==3)textY+=15;
			else if (fightingManData.getType()==4)textY-=5;
			for (int i = 0; i < names.length; i++) {
					// 黑
					g2d.setColor(UIUtils.COLOR_NAME8);
					g2d.drawString(names[i], textX + 1, textY + 1);
					// 浅
					g2d.setColor(UIUtils.COLOR_Y2);
					g2d.drawString(names[i], textX, textY);
					textX +=  fm.stringWidth(names[i])-1;
			}
		} else {
			// 野怪
			g.setFont(UIUtils.nameFont);
			FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
			if (fightingManData.getType()==3)textY+=15;
			else if (fightingManData.getType()==4)textY-=5;
			for (int i = 0; i < names.length; i++) {
				// 黑
				g2d.setColor(UIUtils.COLOR_NAME8);
				g2d.drawString(names[i], textX + 1, textY + 1);
				// 浅
				g2d.setColor(UIUtils.COLOR_NPC1);
				g2d.drawString(names[i], textX, textY);
				textX +=  fm.stringWidth(names[i])-1;
			}
		} 
		g2d.dispose();
	}
	/**
	 * 血条显示
	 */
	public void drawArticle(Graphics g) {
		if (fightingManData.getType() ==3||fightingManData.getType()==4)return;
		if (fightingManData.getCamp() == FightingMixDeal.camp||FightingMixDeal.buffUtil.isMcqh) {
			// 红色
			int hp_bai = (int) ((double) fightingManData.getHp_Current()/ (double) fightingManData.getHp_Total() * 75);
			if (hp_bai > 75) {hp_bai = 75;}
			else if (hp_bai < 0) {hp_bai = 0;}
			g.drawImage(xuelans[0].getImage(), x - 35, y - 125, 75, 5, null);
			g.drawImage(xuelans[1].getImage(), x - 35, y - 125, hp_bai, 5, null);
			// 蓝色
			int mp_bai = (int) ((double) fightingManData.getMp_Current()/ (double) fightingManData.getMp_Total() * 75);
			if (mp_bai > 75) {mp_bai = 75;} 
			else if (mp_bai < 0) {mp_bai = 0;}
			g.drawImage(xuelans[0].getImage(), x - 35, y - 119, 75, 5, null);
			g.drawImage(xuelans[2].getImage(), x - 35, y - 119, mp_bai, 5, null);
		}
	}
	public boolean zhixnig(StateProgress zhixing) {
		if (zhixing.getZxzt() == 2)return false;
		if (zhixing.getZxzt() == 0) {
			addDataDisplay(zhixing);
			FightingMixDeal.LoadMusic(zhixing.getMusic(),fightingManData);
			addSkill(zhixing.getSpell());
			Dialogue(zhixing.getText());
			if (suo == 0) {
				suo = 1;
				zhixing.setZxzt(1);
				Change(zhixing.getType(), zhixing.getDir());
			} else {
				zhixing.setZxzt(2);
				fightingManData.chang(zhixing.getHp_Change(),zhixing.getMp_Change(),zhixing.getYq_Change(),zhixing.getNq_Change());
				fightingManData.addstate(zhixing.getAddchixu());
				fightingManData.deletestate(zhixing.getDeletechixu());
				FightingMixDeal.buffUtil.CS(zhixing.getBuff(), FightingMixDeal.camp);
				return false;
			}
		} else if (zhixing.getZxzt() == 1) {
			if (zhixing.getPath()!=null) {
				if (!pathmove(zhixing.getPath(),zhixing.getData2())) {
					zhixing.setZxzt(2);
					suo = 3;
					endtype(zhixing);
					fightingManData.addstate(zhixing.getAddchixu());//添加结束状态
					fightingManData.deletestate(zhixing.getDeletechixu());
					FightingMixDeal.buffUtil.CS(zhixing.getBuff(), FightingMixDeal.camp);
				}
			}else if (suo == 1 && tcpend()) {
				suo = 2;// 状态结束
				endtype(zhixing);// 添加结束状态		
			} else if (suo == 2 ) {
				suo = 3;
				zhixing.setZxzt(2);
				fightingManData.addstate(zhixing.getAddchixu());
				fightingManData.deletestate(zhixing.getDeletechixu());
				FightingMixDeal.buffUtil.CS(zhixing.getBuff(), FightingMixDeal.camp);
			}
		}
		if (suo == 3) {
			suo = 0;
			return false;
		}
		return true;
	}
	private void addSkill(SkillSpell skill) {
		// TODO Auto-generated method stub
		if (skill == null)
			return;
		if (skill.getSkinpath() == 0 || skill.getSkinpath() == 4) {
			FightingMixDeal.skills.add(skill);
		} else {
			for (int i = FightingMixDeal.skills.size()-1; i >=0; i--) {
				SkillSpell skillSpell=FightingMixDeal.skills.get(i);
				if (skillSpell.getRound()==skill.getRound()&&skillSpell.getSkillid().equals(skill.getSkillid())) {
					return;
				}
			}
			FightingMixDeal.skills.add(skill);
		}
	}
	/**
	 * 动作结束
	 */
	public void endtype(StateProgress zhixing) {
		fightingManData.chang(zhixing.getHp_Change(),zhixing.getMp_Change(),zhixing.getYq_Change(),zhixing.getNq_Change());
		Change(fightingManData.getHp_Current() > 0 ? 7 : 8, zhixing.getDirend());
	}
	/**
	 * 路径移动
	 */
	public boolean pathmove(List<PathPoint> path,String type) {
		if (type.equals("遁地")) {
			long time=getTCPTime();
			if (path.size() > 1) {
				this.x = path.get(1).getX();
				this.y = path.get(1).getY();
				path.clear();
			} else if (path.size() == 1) {
				this.x = path.get(0).getX();
				this.y = path.get(0).getY();
				path.clear();
			}
			if (this.time>time)return false;
		}else {
			if (FightingMove2.getmovetime(this, path)) {
				path.clear();
				return false;
			}
		}		
		return true;
	}
	private List<DataDisplay> displays;
	public List<DataDisplay> getDisplays() {
		if (displays == null)
			displays = new ArrayList<>();
		return displays;
	}
	/**
	 * 添加数据显示
	 */
	public void addDataDisplay(StateProgress zhixing) {
		if (zhixing.getData() == null && zhixing.getHp_Change() == 0
				&& zhixing.getMp_Change() == 0)
			return;
		if (zhixing.getData() == null|| (!zhixing.getData().equals("法力不足") &&
				!zhixing.getData().equals("无法用药") && !zhixing.getData().equals("怨气不足"))) {
			getDisplays().add(new DataDisplay(zhixing.getData(), zhixing.getHp_Change(),zhixing.getMp_Change()));
		} else {
			int myman = FightingMixDeal.myman();
			int man = fightingManData.getMan();
			if (FightingMixDeal.camp == fightingManData.getCamp()
					&& (myman == man || myman + 5 == man)) {
				getDisplays().add(new DataDisplay(zhixing.getData(), zhixing.getHp_Change(), zhixing.getMp_Change()));
			}
		}
	}
	/**
	 * 调节位置
	 */
	public void tiaoz(int mycamp) {
		if (fightingManData.getType() == 3 || fightingManData.getType() == 4) {
			PathPoint pathPoint = Position(fightingManData.getCamp(), mycamp,fightingManData.getMan() - (fightingManData.getType()-1)*5);
			this.x = pathPoint.getX();
			if (fightingManData.getCamp() == mycamp || (fightingManData.getCamp() == 1 && mycamp == -1)) {
		      if (fightingManData.getType() == 4)x += 50;
		      else x += 40;			
			} else {
			  if (fightingManData.getType() == 4)x -= 50;
			  else  x -= 40;
			}
			this.y = pathPoint.getY();
		} else {
			PathPoint pathPoint = Position(fightingManData.getCamp(), mycamp,fightingManData.getMan());
			this.x = pathPoint.getX();
			this.y = pathPoint.getY();
		}
	}
	/**输入敌我 和位置获取 对应坐标*/
	public PathPoint Position(int Camp, int my_Camp, int man) {
		// 判断敌我
		if ((fightingManData.getCamp() == 1 && my_Camp == -1)|| Camp == my_Camp) {// 我方
			if (man < 5) {
				return new PathPoint((ScrenceUntil.Screen_x - 70) - 65 * man,(ScrenceUntil.Screen_y - 325) + 65 * man);
			} else {
				man-=5;
				return new PathPoint((ScrenceUntil.Screen_x - 165) - 65* man,(ScrenceUntil.Screen_y - 390) + 65* man);
			}
		} else {// 敌方
			if (man < 5) {
				return new PathPoint(360 - 65 * man, 150 + 65 * man);
			} else {
				man-=5;
				return new PathPoint(455 - 65 * man, 215 + 65 * man);
			}
		}
	}

	/**判断动作是否执行结束*/
	public boolean tcpend() {
		return time>=part.getTime();
	}
	/**
	 * 类型和方向切换 攻击跑10 攻击9 防御 6 死8 进入战斗7攻 被击4 法术5 逃跑用跑 1
	 * @return
	 */
	public void Change(int type, int dir) {
		if (type == 0 || type == 2)
			return;
		time = 0;
		part.setAct(type);
		part.loadTcp();

		if (dir == 0)
			dir = (fightingManData.getCamp() == FightingMixDeal.camp || (fightingManData.getCamp() == 1 && FightingMixDeal.camp == -1)) ? 7: 3;
		this.dir = dir;
	}
	public int getType() {
		return part.getAct();
	}
	public void setType(int type) {
		time = 0;
		part.setAct(type);
	}
	public int getDir() {
		return dir;
	}
	public void setDir(int dir) {
		this.dir = dir;
	}
	public List<FloatPanel> getChatPanels() {
		return chatPanels;
	}
	public void setChatPanels(List<FloatPanel> chatPanels) {
		this.chatPanels = chatPanels;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public FightingManData getFightingManData() {
		return fightingManData;
	}
	public void setFightingManData(FightingManData fightingManData) {
		this.fightingManData = fightingManData;
	}
	public long getTime() {
		return time;
	}
	public void addTime(long time) {
		if (part.getAct()==9) {time*=2;}
		else if (part.getAct()==4||part.getAct()==5) {time*=1.4;}
		else {time*=1.2;}
		this.time+=time;
	}
	public void setTime(long time) {
		this.time += time;
	}
    /**获取帧总时间*/
	public long getTCPTime(){
		return part.getTime();	
	}
	/**判断是否点击内
	 * @return */
	public boolean isContains(int x,int y){
		return part.contains(x-this.x, y-this.y);	
	}
	public NewPart getPart() {
		return part;
	}
	public void setPart(NewPart part) {
		this.part = part;
	}
	
}
