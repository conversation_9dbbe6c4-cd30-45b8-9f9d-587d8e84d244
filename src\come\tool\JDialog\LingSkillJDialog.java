package come.tool.JDialog;

import java.util.List;

import org.come.Frame.LingbaoJframe;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.model.Lingbao;
import org.come.mouslisten.LingFaFuSkill;
import org.come.until.GoodsListFromServerUntil;

import com.tool.role.RoleLingFa;

public class LingSkillJDialog implements TiShiChuLi{
	
	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		LingFaFuSkill faFuSkill=(LingFaFuSkill)object;
		
		if (tishi) {
			int sum=faFuSkill.getS()*8;
			int[] goodids=new int[sum];
			for (int i = 0; i < goodids.length; i++) {
				goodids[i]=10079;
			}
			List<Integer> integers =GoodsListFromServerUntil.chaxuns(goodids);
			if (integers==null) {
				FrameMessageChangeJpanel.addtext(5,"物品不足",null,null);
				return;
			}
			GoodsListFromServerUntil.Delete(integers);
			Lingbao lingbao = RoleLingFa.getRoleLingFa().getChoseBao();	
    		//开启格子
    		lingbao.OpenGrid(false);
    		 //开启格子后发服务器
    		lingbao.UpdateLing();
	    	 //刷新展示面板
			LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel().shuxingzhanshi(lingbao);
		}
	}
}
