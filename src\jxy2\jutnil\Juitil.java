package jxy2.jutnil;

import com.tool.btn.FormsOnOffBtn;
import com.tool.btn.MoBanBtn;
import com.tool.tcp.Sprite;
import com.tool.tcpimg.UIUtils;
import jxy2.WdfLoaDing;
import jxy2.npk.NpkImageReader;
import jxy2.setup.SetupMainJPanel;
import org.checkerframework.common.reflection.qual.GetMethod;
import org.come.Frame.NPCJfram;
import org.come.bean.ImgZoom;
import org.come.until.*;

import javax.swing.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.awt.image.PixelGrabber;
import java.lang.ref.SoftReference;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
* Ui切换工具类
* <AUTHOR>
* @date 2024/4/6 15:30
*/
@SuppressWarnings("all")
public class Juitil {
    public static Color[] Colours() {
        return UIUtils.COLOR_BTNPUTONGS;
    }
    public static String offbtn (int uiType){
        return uiType==0 ? "0x6FEB8509" : uiType==1 ? "0x6FAC1013" : "0x6FAB1013";
    }
    public static String btn34_18 (int uiType){
        return uiType==0||uiType==1  ? "0x6FEB8614": "0x6FAB1014";
    }
    public static String btn60_26 (int uiType){
      return uiType==0 ? "0x6FEB8534" : uiType==1 ? "0x6FAC1008" : "0x6FAB1002";
    }
    public static String btn80_26 (int uiType){
      return uiType==0|| uiType==1 ? "0x6FAC1025" : "0x6FAB1004";
    }
    public static String btn100_26 (int uiType){
      return uiType==0|| uiType==1 ? "0x6FAC1026" : "0x6FAB1003";
    }

    /**
     * 所有水墨界面缩放素材
     * @return WindowImg_1
     */
    public static ImgZoom WindowImg_1(String path,int x,int y){
        return CutButtonImage.cuts(path, x, y, true);
    }


    /**
     * 从指定路径读取WDF格式的图像，并在指定的坐标处进行裁剪，返回裁剪后的图像。
     * @param path 图像文件的路径。
     * @param x 裁剪的起始点的X坐标。
     * @param y 裁剪的起始点的Y坐标。
     * @return 裁剪后的图像对象。
     */
    public static ImgZoom WindowImgWdf(String path,int x,int y){
        return CutButtonImage.newcutsWas(path, x, y, true, "sprite.wdf");
    }

    /**
     * 从指定路径读取PNG格式的图像，并在指定的坐标处进行裁剪，返回裁剪后的图像。
     * @param path 图像文件的路径。
     * @param x 裁剪的起始点的X坐标。
     * @param y 裁剪的起始点的Y坐标。
     * @return 裁剪后的图像对象。
     */
    public static ImgZoom WindowImgPng(String path,int x,int y){
        return NpkImageReader.NpkcutsPng(path, x, y, true,"gires4.npk");
    }

    public static ImgZoom WindowRedImgPng(String path,int x,int y){
        return CutButtonImage.newcutsPng(path, x, y, true,"redmu.wdf");
    }
    public static ImgZoom WindowSheImgPng(String path,int x,int y){
        return CutButtonImage.newcutsPng(path, x, y, true,"she.wdf");
    }

    /**
     * 根据装备精炼等级返回装备名字的颜色
     * @param lvl 等级。
     * */
    public static Color EquiColour(int lvl){
        switch (lvl){
            case 4:
            case 5:
            case 6:
                return UIUtils.COLOR_NAME;
            case 7:
            case 8:
            case 9:
                return UIUtils.COLOR_goods_blue;
            case 10:
            case 11:
            case 12:
                return UIUtils.COLOR_NAME4_PET;
            case 13:
            case 14:
            case 15:
                return UIUtils.COLOR_HURTR1;
            case 16:
                return UIUtils.COLOR_HURTY1;
            case 0:
            case 1:
            case 2:
            case 3:
            default:
                return UIUtils.COLOR_White;
        }

    }



    /**水墨水印背景**/
    public static ImageIcon Shadow(){
        return CutButtonImage.getImageWas(ImgConstants.SHADOW,-1,-1,"defaut.wdf");
    }
    /**
     * 添加关闭按钮
     * @param jpanel -当前窗体
     * @param HideForm 窗体标号
     * @param Width 窗体宽度
     */
    public static void addClosingButtonToPanel(JPanel jpanel, int HideForm, int Width) {
        // 先移除该面板上所有的FormsOnOffBtn
        Component[] components = jpanel.getComponents();
        for (Component comp : components) {
            if (comp instanceof FormsOnOffBtn) {
                jpanel.remove(comp);
            }
        }
        // 创建并添加新的关闭按钮
        int w = Util.SwitchUI==1?25:23;
        FormsOnOffBtn offBtn = new FormsOnOffBtn(Juitil.offbtn(Util.SwitchUI), 1, HideForm, 0);
        offBtn.setBounds(Width-35, HideForm==8||HideForm==58 ? 3 : 5, w, w);
        jpanel.add(offBtn);
        // 刷新面板
//        jpanel.revalidate();
//        jpanel.repaint();
    }

    /**
     * 绘制菜单小标题
     * @param g Graphics对象，用于绘制图形
     * @param x 字体绘制的起始x坐标
     * @param y 字体绘制的起始y坐标
     * @param text 要绘制的文本内容
     * @param color 绘制文本的颜色
     * @param font 绘制文本的字体
     * @param type 0关闭抗锯齿，1开启抗锯齿
     */
    public static void Subtitledrawing(Graphics g,int x,int y,String text,Color color,Font font,int type){
        // 创建Graphics2D对象，用于支持高级绘制操作
        Graphics2D g2d = (Graphics2D) g.create();
        // 设置文本抗锯齿渲染提示
        if (type==1){
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        }
        // 设置绘制颜色和字体
        g2d.setColor(color);
        g2d.setFont(font);
        // 在指定位置绘制文本
        g2d.drawString(text,x,y);
        // 释放Graphics2D资源
        g2d.dispose();
    }


    /**
     * 将文本竖直方向排列绘制在图形上
     * @param g Graphics对象，用于绘制文本的图形上下文
     * @param x 文本起始绘制点的x坐标
     * @param y 文本起始绘制点的y坐标
     * @param text 需要绘制的文本内容
     * @param color 文本颜色
     * @param font 文本字体
     * @return 返回传入的文本内容
     */
public static String Txtpet(Graphics g, int x, int y, String text, Color color, Font font) {
    Graphics2D g2d = (Graphics2D) g.create(); // 创建Graphics2D对象用于更高级别的绘图操作
    if (!font.getFontName().equals("宋体")) {
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON); // 设置文本抗锯齿
    }
    g2d.setColor(color); // 设置绘制颜色
    g2d.setFont(font); // 设置绘制字体

    // 计算每个字符的绘制间距
    int lineHeight = org.come.until.SafeFontMetrics.getFontMetrics(g2d).getHeight()-1;

    // 逐行绘制文本的每个字符
    int currentY = y; // 当前绘制的 y 坐标
    int currentX = x; // 当前绘制的 x 坐标
    int currentI = 0; // 当前字符索引

    while (currentI < text.length()) {
        char c = text.charAt(currentI);
        if (c == '&') {
            // 如果遇到 '&'，则换行
            currentX += 15; // 调整 x 坐标
            currentY = y; // 重置 y 坐标，保证两段文字高度一致
            currentI++; // 跳过 '&'
        } else {
            String character = String.valueOf(c); // 将字符转为字符串
            g2d.drawString(character, currentX, currentY); // 绘制单个字符
            currentY += lineHeight; // 更新 y 坐标
            currentI++;
        }

    }
    g2d.dispose(); // 释放Graphics2D对象的资源
    return text;
}





    /**
     * 单张绘制背景图层，拆分图像
     * @param g 绘图对象
     * @param imgZoomH 图片缩放对象
     * @param x 绘制起始点 x 坐标
     * @param y 绘制起始点 y 坐标
     * @param w 绘制宽度
     * @param h 绘制高度
     * @param type 绘制类型（0为绘制到Jpanel上，1为绘制到指定坐标上）
     * TODO 2024.04.20.解决改变大小闪烁的问题，先修改setMiddleh以及setMiddlew，然后draw绘制！
     * @return 返回绘制后的图片缩放对象
     */
    public static void ImngBack(Graphics g, ImgZoom imgZoomH, int x, int y, int w, int h, int type){
        try {
            if (imgZoomH!=null) {
                imgZoomH.setMiddleh(h - 2 * imgZoomH.getEdgew());
                if (type == 0) {
                    imgZoomH.setMiddlew((w - 12) - 2 * imgZoomH.getEdgew());
                    imgZoomH.drawJpanel(g);
                } else {
                    imgZoomH.setMiddlew(w - 2 * imgZoomH.getEdgew());
                    imgZoomH.draw(g, x, y, imgZoomH.getMiddlew(), imgZoomH.getMiddleh());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }




    public static void handleButtonState(MoBanBtn[] banBtn , int caozuo) {
        banBtn[caozuo].btnchange(2);
            // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
            for (int i = 0; i <banBtn.length; i++) {
                if (i != caozuo) {
                    banBtn[i].btnchange(0);
                }
            }

    }

    public static void TextUi(Graphics g,String text,int type,int x,int y, int x1,int y1,int index){
        Graphics2D g2d = (Graphics2D) g.create();
        if (text != null) {
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            if (type==-1){
                type=0;
            }
            switch (index){
                case 0:
                case 1:
                    g2d.setFont(UIUtils.TEXT_HYJ16B);
                    if (type == 2) {
                        g2d.drawString(text, x , y);
                        g2d.drawString(text, x , y);
                    } else {
                        g2d.drawString(text, x1, y1);
                        g2d.drawString(text, x1, y1);
                    }
                    break;
                case 3:
                    g2d.setFont(UIUtils.TEXT_HYJ19);
                    if (text.length()==2){
                        if (type == 2) {
                            g2d.drawString(text, x+17 , y);
                        } else {
                            g2d.drawString(text, x1+16, y1);
                        }
                    }else if (text.length()==3){
                        if (type == 2) {
                            g2d.drawString(text, x+8 , y);
                        } else {
                            g2d.drawString(text, x1+7, y1);
                        }
                    }else if (text.length()==4){
                        if (type == 2) {
                            g2d.drawString(text, x-4 , y);
                        } else {
                            g2d.drawString(text, x1-5, y1);
                        }
                    }else {
                        if (type == 2) {
                            g2d.drawString(text, x , y);
                        } else {
                            g2d.drawString(text, x1, y1);
                        }
                    }
                    break;
            }
        }
    }

    private static final String[] UNITS = { "", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", };
    private static final String[] NUMS = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九", };
    public static String number2Chinese(int value) {
        String result = ""; //转译结果
        for (int i = String.valueOf(value).length() - 1; i >= 0; i--) {//String.valueOf(value) 转换成String型得到其长度 并排除个位,因为个位不需要单位
            int r = (int) (value / Math.pow(10, i));//value / Math.pow(10, i) 截位匹配单位
            result += NUMS[r % 10] + UNITS[i];
        }
        result = result.replaceAll("零[十, 百, 千]", "零");//匹配字符串中的 "零[十, 百, 千]" 替换为 "零"
        result = result.replaceAll("零+", "零");//匹配字符串中的1或多个 "零" 替换为 "零"
        result = result.replaceAll("零([万, 亿])", "$1");
        result = result.replaceAll("亿万", "亿"); //亿万位拼接时发生的特殊情况

        if (result.startsWith("一十")) { //判断是否以 "一十" 开头 如果是截取第一个字符
            result = result.substring(1);
        }

        if (result.endsWith("零")) { //判断是否以 "零" 结尾 如果是截取除 "零" 外的字符
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 在图形上下文上使用不同字体绘制字符串。
     * @param g2d Graphics2D对象，用于绘制字符串的图形上下文。
     * @param text 要绘制的字符串。
     * @param x 字符串起始绘制点的X坐标。
     * @param y 字符串起始绘制点的Y坐标。
     * 该方法遍历字符串中的每个字符，如果字符是数字，则使用一种字体（LiSu_LS15）和颜色（红色）绘制，
     * 否则使用另一种字体（TEXT_FONT2）和颜色（白色）绘制。每绘制一个字符，根据该字符的宽度，
     * 更新下个字符的绘制起始点X坐标。
     */
    public static void drawStringWithDifferentFonts(Graphics2D g2d, String text, int x, int y) {
        // 获取两种不同字体的度量信息
        FontMetrics metrics1 = org.come.until.SafeFontMetrics.getFontMetrics(g2d, UIUtils.LiSu_LS15);
        FontMetrics metrics2 = org.come.until.SafeFontMetrics.getFontMetrics(g2d, UIUtils.TEXT_FONT2);
        int currentX = x; // 当前绘制字符的X坐标
        for (char c : text.toCharArray()) {
            // 根据字符类型设置颜色和字体，并绘制字符
            if (Character.isDigit(c)) {
                g2d.setColor(Color.red);
                g2d.setFont(UIUtils.LiSu_LS15);
                g2d.drawString(String.valueOf(c), currentX, y);
                currentX += metrics1.charWidth(c); // 更新下一个字符的X坐标
            } else {
                g2d.setColor(Color.white);
                g2d.setFont(UIUtils.TEXT_FONT2);
                g2d.drawString(String.valueOf(c), currentX, y);
                currentX += metrics2.charWidth(c); // 更新下一个字符的X坐标
            }
        }
    }
    public static void TextBackground(Graphics g,String text,int texHeight,int x, int y,Color color,Font font){
        TextBackground(g,text,texHeight,x,y,color,font,null);
    }

    /**
     * 绘制文本的背景
     * @param g Graphics对象，用于绘制文本的图形上下文。
     * @param text 要绘制的文本。
     * @param texHeight 文本的高度。
     * @param x 文本起始绘制点的X坐标。
     * @param y 文本起始绘制点的Y坐标。
     * @param color
     * @param font
     * @param color1
     */
    public static void TextBackground(Graphics g, String text, int texHeight, int x, int y, Color color, Font font, Color color1) {
        Graphics2D g2d = (Graphics2D) g.create();
        if (!font.getFontName().equals("宋体")) {
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        }
        if (font.getFontName().equals("隶书")) {
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
        }
        // 绘制黑色描边
        g2d.setFont(font);
        // 检查并分割字符串
        String[] lines = text.split("&");
        int offsetY = 0; // 用于记录当前行相对于起始位置的偏移量

        for (String line : lines) {
            // 绘制描边
            g2d.setColor(color1 == null ? Color.black : color1);
            g2d.drawString(line, x + 2 + 1, y + texHeight + offsetY);
            g2d.drawString(line, x + 2, y + texHeight + 1 + offsetY);
            // 绘制白色文本
            g2d.setColor(color);
            g2d.drawString(line, x + 2, y + texHeight + offsetY);
            // 更新偏移量
            FontMetrics metrics = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
            offsetY += metrics.getHeight();//显示行高
        }
        g2d.dispose();
    }

    /**绘制小标题文本*/
    public static void Textdrawing(Graphics g,String text, int x, int y,Color color,Font font){
        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setFont(font);
        g2d.setColor(color);
        g2d.drawString(text, x, y);
        g2d.drawString(text, x, y);
    }
    /**绘制文本剧中显示*/
    public static void CenterTextdrawing(Graphics g,String text, int x, int y,Color color,Font font){
        Graphics2D g2d = (Graphics2D) g.create();
        if (!font.getFontName().equals("宋体")) {
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        }
        g2d.setFont(font);
        g2d.setColor(UIUtils.Color_BACK);
        FontMetrics metrics = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
        // 文本的宽度
        int textWidth = metrics.stringWidth(text);
        // 文本的高度（注意：此处获取的是字体的上升和下降部分之和，对于居中，通常关心的是字体的总高度）
        int textHeight = metrics.getHeight();
        // 计算文本的水平居中位置
        int centerX = (x - textWidth) / 2;
        // 计算文本的垂直居中位置，这里假设需要完全居中，包括上下对齐
        // 注意：根据不同的对齐需求，可能需要调整为height/2或者其他计算方式
        int centerY = (y - textHeight) / 2 + metrics.getAscent(); // 使用getAscent()确保基线对齐
        g2d.drawString(text, centerX   + 1,  centerY);
        g2d.drawString(text, centerX  ,  centerY+1);

        g2d.setColor(color);
        g2d.drawString(text, centerX, centerY);
        g2d.dispose();
    }


     /**J组件模拟点击
     * @param jLabel J组件
     * @param type 1 - 按下 2 - 弹起
     * */
    public static void JLabelButtonState(JLabel jLabel, int type,int x,int y,int w,int h){
        jLabel.setBounds(type==1?x+1:x,type==1?y+1:y,w,h);
    }

    /***
     * J组件模拟进出
     * @param jLabel J组件
     * @param index 1 - 进 2 - 出
     */
    public static void SimulatesMove(JLabel jLabel,int w,int h,int index){
//        jLabel.setIcon(index==1?CutButtonImage.getWdfPng(ImgConstants.tz157,w,h,"defaut.wdf"):null);
//        jLabel.setBorder(index==1?BorderFactory.createLineBorder(Color.white):BorderFactory.createEmptyBorder());
    }


    /**
     * *改变图像为圆角
     * @param srcImage
     * @param cornerRadius
     * @return
     */
    public static Image toRoundedCornerImage(Image srcImage, int cornerRadius) {
        int w = srcImage.getWidth(null);
        int h = srcImage.getHeight(null);
        BufferedImage output = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);

        Graphics2D g2 = output.createGraphics();
        g2.setComposite(AlphaComposite.Src);
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setColor(Color.WHITE); // 设置背景颜色，透明则不需要这行
        g2.fill(new RoundRectangle2D.Float(0, 0, w, h, cornerRadius, cornerRadius)); // 绘制圆角矩形

        g2.setComposite(AlphaComposite.SrcAtop);
        g2.drawImage(srcImage, 0, 0, null); // 将原图像绘制到圆角矩形上

        g2.dispose();
        return output;
    }

    private static Map<String, SoftReference<ImageIcon>> imageCache = new HashMap<>();
    public static ImageIcon toRoundedCornerImageIcon(Image srcImage, int cornerRadius, String cacheKey) {
        SoftReference<ImageIcon> ref = imageCache.get(cacheKey);
        ImageIcon cachedIcon = ref != null ? ref.get() : null;
        if (cachedIcon != null) {
            return cachedIcon;
        }
        int w = srcImage.getWidth(null);
        int h = srcImage.getHeight(null);
        BufferedImage output = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = output.createGraphics();
        g2.setComposite(AlphaComposite.Src);
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.setColor(Color.WHITE); // 设置背景颜色，透明则不需要这行
        g2.fill(new RoundRectangle2D.Float(0, 0, w, h, cornerRadius, cornerRadius)); // 绘制圆角矩形
        g2.setComposite(AlphaComposite.SrcAtop);
        g2.drawImage(srcImage, 0, 0, null); // 将原图像绘制到圆角矩形上
        g2.dispose();
        ImageIcon roundedIcon = new ImageIcon(output);
        imageCache.put(cacheKey, new SoftReference<>(roundedIcon));
        return roundedIcon;
    }

    public static ImgZoom JK,good,good_2,JK_24,Radio_17,
            book,partimg,tz21,tz22,tz26,JK_1,parts,tz86,tz23,tz76
            ,tz61,tz62,Curtain,kkn,kkn1,x0413_1,x0413,tz67,
            tz103,tz128, tz159,tz173,tz174,tz177,
            tz179,tz178,JK_1_S,gdol,JK_2,JK_24_1,JK_21,Radio_75,
            tz187,tz190,tz258,
            tz216,tz218,tz219,tz221,tz222,tz223,
            tz224,tz225,tz226,tz228,tz261,tz237,tz240,
            tz249,tz253,tz255,tz259,TxtImg,maskimg,tz266,tz267,notice,tz270,tz54,tz271,tz293,tz318,tx319,tx320,tx321,
            tx322,tx323,tx324,tx325, tx326,tx327,tx328,tx329,tx330;
    //RedMu
    public static ImgZoom red_0000,red_0001,red_0005,red_0006,red_0007,red_0008,red_0009,red_0018,red_0019,red_0023,red_0036,red_0037,red_0038,red_0039,
            red_0040,red_0041,red_0042,red_0043,red_0044,red_0045,red_0046;
    //She
    public static ImgZoom she_0000,she_0001,she_0002,she_0003,she_0004,she_0005,she_0006,she_0007,she_0009,she_0019,she_0021,she_0022,she_0023,she_0024;
    //===================================================
    public static ImageIcon tz25,icon,tz19,
            con,tz100,tz123,tz124,tz125,tz157,tz161,tz164,tz165,tz171,tz155,tz156,
            tz176,tz83,tz84,tz184,tz185,QiandaoImg,
            tz212,tz213,tz214,tz215,tz232,tz233,tz236
            ,tz241,tz206,tz246,tz247,tz248,tz250,tz252,tz254,
            tz257,tz260,tz262,tz263, tz264,tz265,tz269,tz98,
            tz273,tz274,tz275,tz276,tz277,tz278,tz279,tz280,
            tz281,tz282,tz283,tz284,tz285,tz286,tz287,tz288,
            tz289,tz290,tz291,tz292,tz294,tz295,tz296,tz297,
            tz298,tz299,tz300,tz301,tz302,tz303,tz304,tz305,
            tz306,tz307,tz308,tz309,tz310,tz311,tz312,tz313,
            tz314,tz315,tz316,tz317, tz319,tz320,tz321,tz322,
            tz323,tz324,tz325, tz326,tz327,tz328,tz329,tz330,
            tz331,tz332,tz333,tz334,tz335,tz336,tz337,tz338,
            tz339,tz340,tz341,tz342,tz343,tz344,tz345,tz346,
            tz347,tz348,tz349,tz350,tz351, tz352,tz353,tz354,
            tz355,tz356,tz357,tz358, tz359,tz360,tz361,tz362,
            tz363,tz364,tz365,tz366,tz367,tz368,tz369,tz370,
            tz371,tz372,tz373,tz374,tz375,tz376,tz377,tz378,
            tz379,tz380,tz381,tz382, tz383,tz384,tz386,
            tz387,tz388,tz389,tz390,tz391,tz392,tz393,tz394,
            tz395,tz396,tz397,tz398,tz399,tz400,tz401,tz402;
    //===================================================
    public static Sprite vnavi_1_sprite,XPback,XPBackYY,XPBackTX,XPBackDL,
            dj0, dj1, dj2, dj3, dj4, dj5, dj6, dj7, dj8, dj9,
            dj10, dj11, dj12, dj13, dj14, dj15, dj16, dj17, dj18, dj19,
            dj20, dj21, dj22, dj23, dj24, dj25, dj26, dj27, dj28, dj29, dj30, dj31,JHKSILL,tx1,tx2,
            skitx;
     public static Sprite [] XPDL =new Sprite[8];
     public static Sprite [] XPJD =new Sprite[8];
     public static Sprite [] XPJH =new Sprite[8];
    public static ImageIcon[] labpetequimg =new ImageIcon[4];
    public static ImageIcon[] tz385 =new ImageIcon[12];

    public static ImageIcon[] bt1,bt2,bt3,bt4,bt5,bt6,bt7,bt8,bt9,bt10,bt11,bt12,bt13,bt14,bt15,bt16,bt17,bt18,bt19,bt20,bt21,bt22,
    bt23,bt24,bt25,bt26,bt27,bt28,bt29,bt30,bt31,bt32,bt33,bt34,bt35,bt36,bt37,bt38,bt39,bt40,bt41,bt42,bt43,bt44,bt45,
    bt46,bt47,bt48,bt49,bt50,bt51,bt52,bt53,bt54,bt55,bt56,bt57,bt58,bt59,bt60,bt61,bt62,bt63,bt64,bt65,bt66,bt67,bt68,
    bt69,bt70,bt71,bt72,bt73,bt74,bt75,bt76,bt77;
    //LoginView
    public static ImageIcon btnBackground,background2,userAccountBg,putongdl,zhangh,mim,kehu,guanwang,Sunlight;
    //AreaView
    public static ImageIcon xqbj,xqbj1,state,xqbj3,jrbj,zst,xqhtk,listServer,yjhServer,opServer;
    //RoleView
    public static ImageIcon rolebeijing,dise,dise2,btnCreateRoleBg,cyjs;
    /**
     * 在后台加载图片资源。
     * 该方法为静态方法，不需要实例化对象即可调用。
     * 参数: 无
     * 返回值: 界面
     */
    public static void loadImagesInBackground() {

        try {
            //静态通用模型===============================================================
            con = NpkImageReader.getNpkPng(ImgConstants.confirm,20,20,"gires4.npk");
            tz25  = NpkImageReader.getNpkPng(ImgConstants.tz25,"gires4.npk");
            icon  = NpkImageReader.getNpkPng(ImgConstants.tz19,"gires4.npk");
            tz19 = NpkImageReader.getNpkPng(ImgConstants.tz19,"gires4.npk");
            tz100 = NpkImageReader.getNpkPng(ImgConstants.tz100,"gires4.npk");
            tz157 = NpkImageReader.getNpkPng(ImgConstants.tz157,"gires4.npk");
            tz123 = NpkImageReader.getNpkPng(ImgConstants.tz123,"gires4.npk");
            tz161 = NpkImageReader.getNpkPng(ImgConstants.tz161,"gires4.npk");
            tz164 = NpkImageReader.getNpkPng(ImgConstants.tz164,"gires4.npk");
            tz165 = NpkImageReader.getNpkPng(ImgConstants.tz165,"gires4.npk");
            tz171 = NpkImageReader.getNpkPng(ImgConstants.tz171,"gires4.npk");
            tz176 = NpkImageReader.getNpkPng(ImgConstants.tz176,"gires4.npk");
            tz83 = NpkImageReader.getNpkPng(ImgConstants.tz83,"gires4.npk");
            tz84 = NpkImageReader.getNpkPng(ImgConstants.tz84,"gires4.npk");
            tz184 = NpkImageReader.getNpkPng(ImgConstants.tz184,"gires4.npk");
            tz185 = NpkImageReader.getNpkPng(ImgConstants.tz185,"gires4.npk");
            QiandaoImg = NpkImageReader.getNpkPng(ImgConstants.tz209, "gires4.npk");
            tz212 = NpkImageReader.getNpkPng(ImgConstants.tz212, "gires4.npk");
            tz213 = NpkImageReader.getNpkPng(ImgConstants.tz213, "gires4.npk");
            tz214 = NpkImageReader.getNpkPng(ImgConstants.tz214, "gires4.npk");
            tz215 = NpkImageReader.getNpkPng(ImgConstants.tz215, "gires4.npk");
            tz232 = NpkImageReader.getNpkPng(ImgConstants.tz232, "gires4.npk");
            tz233 = NpkImageReader.getNpkPng(ImgConstants.tz233, "gires4.npk");
            tz236 = NpkImageReader.getNpkPng(ImgConstants.tz236, "gires4.npk");
            tz241 = NpkImageReader.getNpkPng(ImgConstants.tz241, "gires4.npk");
            tz206 = NpkImageReader.getNpkPng(ImgConstants.tz206, "gires4.npk");
            tz246 = NpkImageReader.getNpkPng(ImgConstants.tz246, "gires4.npk");
            tz247 = NpkImageReader.getNpkPng(ImgConstants.tz247, "gires4.npk");
            tz248 = NpkImageReader.getNpkPng(ImgConstants.tz248, "gires4.npk");
            tz250 = NpkImageReader.getNpkPng(ImgConstants.tz250, "gires4.npk");
            tz252 = NpkImageReader.getNpkPng(ImgConstants.tz252, "gires4.npk");
            tz254 = NpkImageReader.getNpkPng(ImgConstants.tz254, "gires4.npk");
            tz257 = NpkImageReader.getNpkPng(ImgConstants.tz257, "gires4.npk");
            tz260 = NpkImageReader.getNpkPng(ImgConstants.tz260, "gires4.npk");
            tz262 = NpkImageReader.getNpkPng(ImgConstants.tz262, "gires4.npk");
            tz263 = NpkImageReader.getNpkPng(ImgConstants.tz263, "gires4.npk");
            tz264 = NpkImageReader.getNpkPng(ImgConstants.tz264, "gires4.npk");
            tz265 = NpkImageReader.getNpkPng(ImgConstants.tz265, "gires4.npk");
            tz269 = NpkImageReader.getNpkPng(ImgConstants.tz269, "gires4.npk");
            tz98  = NpkImageReader.getNpkPng(ImgConstants.tz98 , "gires4.npk");
            tz273 = NpkImageReader.getNpkPng(ImgConstants.tz273, "gires4.npk");
            tz274 = NpkImageReader.getNpkPng(ImgConstants.tz274, "gires4.npk");
            tz275 = NpkImageReader.getNpkPng(ImgConstants.tz275, "gires4.npk");
            tz276 = NpkImageReader.getNpkPng(ImgConstants.tz276, "gires4.npk");
            tz277 = NpkImageReader.getNpkPng(ImgConstants.tz277, "gires4.npk");
            tz278 = NpkImageReader.getNpkPng(ImgConstants.tz278, "gires4.npk");
            tz279 = NpkImageReader.getNpkPng(ImgConstants.tz279, "gires4.npk");
            tz280 = NpkImageReader.getNpkPng(ImgConstants.tz280, "gires4.npk");
            tz281 = NpkImageReader.getNpkPng(ImgConstants.tz281, "gires4.npk");
            tz282 = NpkImageReader.getNpkPng(ImgConstants.tz282, "gires4.npk");
            tz283 = NpkImageReader.getNpkPng(ImgConstants.tz283, "gires4.npk");
            tz284 = NpkImageReader.getNpkPng(ImgConstants.tz284, "gires4.npk");
            tz285 = NpkImageReader.getNpkPng(ImgConstants.tz285, "gires4.npk");
            tz286 = NpkImageReader.getNpkPng(ImgConstants.tz286, "gires4.npk");
            tz287 = NpkImageReader.getNpkPng(ImgConstants.tz287, "gires4.npk");
            tz288 = NpkImageReader.getNpkPng(ImgConstants.tz288, "gires4.npk");
            tz289 = NpkImageReader.getNpkPng(ImgConstants.tz289, "gires4.npk");
            tz290 = NpkImageReader.getNpkPng(ImgConstants.tz290, "gires4.npk");
            tz291 = NpkImageReader.getNpkPng(ImgConstants.tz291, "gires4.npk");
            tz292 = NpkImageReader.getNpkPng(ImgConstants.tz292, "gires4.npk");

            tz294 = NpkImageReader.getNpkPng(ImgConstants.tz294, "gires4.npk");
            tz295 = NpkImageReader.getNpkPng(ImgConstants.tz295, "gires4.npk");
            tz296 = NpkImageReader.getNpkPng(ImgConstants.tz296, "gires4.npk");
            tz297 = NpkImageReader.getNpkPng(ImgConstants.tz297, "gires4.npk");
            tz298 = NpkImageReader.getNpkPng(ImgConstants.tz298, "gires4.npk");
            tz299 = NpkImageReader.getNpkPng(ImgConstants.tz299, "gires4.npk");
            tz300 = NpkImageReader.getNpkPng(ImgConstants.tz300, "gires4.npk");
            tz301 = NpkImageReader.getNpkPng(ImgConstants.tz301, "gires4.npk");
            tz302 = NpkImageReader.getNpkPng(ImgConstants.tz302, "gires4.npk");
            tz303 = NpkImageReader.getNpkPng(ImgConstants.tz303, "gires4.npk");
            tz304 = NpkImageReader.getNpkPng(ImgConstants.tz304, "gires4.npk");
            tz305 = NpkImageReader.getNpkPng(ImgConstants.tz305, "gires4.npk");
            tz306 = NpkImageReader.getNpkPng(ImgConstants.tz306, "gires4.npk");
            tz307 = NpkImageReader.getNpkPng(ImgConstants.tz307, "gires4.npk");
            tz308 = NpkImageReader.getNpkPng(ImgConstants.tz308, "gires4.npk");
            tz309 = NpkImageReader.getNpkPng(ImgConstants.tz309, "gires4.npk");
            tz125 = NpkImageReader.getNpkPng(ImgConstants.tz125, "gires4.npk");
            tz124 = NpkImageReader.getNpkPng(ImgConstants.tz124, "gires4.npk");
            tz310 = NpkImageReader.getNpkPng(ImgConstants.tz310, "gires4.npk");
            tz311 = NpkImageReader.getNpkPng(ImgConstants.tz311, "gires4.npk");

            tz312 = NpkImageReader.getNpkPng(ImgConstants.tz312, "gires4.npk");
            tz313 = NpkImageReader.getNpkPng(ImgConstants.tz313, "gires4.npk");
            tz314 = NpkImageReader.getNpkPng(ImgConstants.tz314, "gires4.npk");
            tz315 = NpkImageReader.getNpkPng(ImgConstants.tz315, "gires4.npk");
            tz316 = NpkImageReader.getNpkPng(ImgConstants.tz316, "gires4.npk");
            tz317 = NpkImageReader.getNpkPng(ImgConstants.tz317, "gires4.npk");
            tz319 = NpkImageReader.getNpkPng(ImgConstants.tz319, "gires4.npk");
            tz320 = NpkImageReader.getNpkPng(ImgConstants.tz320, "gires4.npk");
            tz321 = NpkImageReader.getNpkPng(ImgConstants.tz321, "gires4.npk");
            tz322 = NpkImageReader.getNpkPng(ImgConstants.tz322, "gires4.npk");
            tz323 = NpkImageReader.getNpkPng(ImgConstants.tz323, "gires4.npk");
            tz324 = NpkImageReader.getNpkPng(ImgConstants.tz324, "gires4.npk");
            tz325 = NpkImageReader.getNpkPng(ImgConstants.tz325, "gires4.npk");
            tz326 = NpkImageReader.getNpkPng(ImgConstants.tz326, "gires4.npk");
            tz327 = NpkImageReader.getNpkPng(ImgConstants.tz327, "gires4.npk");
            tz328 = NpkImageReader.getNpkPng(ImgConstants.tz328, "gires4.npk");
            tz329 = NpkImageReader.getNpkPng(ImgConstants.tz329, "gires4.npk");
            tz332 = NpkImageReader.getNpkPng(ImgConstants.tz332, "gires4.npk");
            tz333 = NpkImageReader.getNpkPng(ImgConstants.tz333, "gires4.npk");
            tz334 = NpkImageReader.getNpkPng(ImgConstants.tz334, "gires4.npk");
            tz335 = NpkImageReader.getNpkPng(ImgConstants.tz335, "gires4.npk");
            tz336 = NpkImageReader.getNpkPng(ImgConstants.tz336, "gires4.npk");
            tz337 = NpkImageReader.getNpkPng("0x6FEB9019", "gires4.npk");
            tz341 = NpkImageReader.getNpkPng("0x6FEB8628", "gires4.npk");
            tz330 = NpkImageReader.getNpkPng("0x6FEB9021", "gires4.npk");
            tz343 = NpkImageReader.getNpkPng("0x6FEB9087",  "gires4.npk");//
            tz344 = NpkImageReader.getNpkPng("0x6FEB9088",  "gires4.npk");//
            tz345 = NpkImageReader.getNpkPng("0x6FEB9089",  "gires4.npk");//
            tz346 = NpkImageReader.getNpkPng("0x6FEB9090",  "gires4.npk");//
            tz352 = NpkImageReader.getNpkPng("0x6FEB9091",  "gires4.npk");//
            tz353 = NpkImageReader.getNpkPng("0x6FEB9092",  "gires4.npk");//
            tz354 = NpkImageReader.getNpkPng("0x6FEB9093",  "gires4.npk");//
            tz362 = NpkImageReader.getNpkPng("0x6FEB9101", 16,16 ,"gires4.npk");
            tz363= NpkImageReader.getNpkPng("0x6FEB9094","gires4.npk");
            tz364 = NpkImageReader.getNpkPng("0x6FEB9095", "gires4.npk");
            tz365 = NpkImageReader.getNpkPng("0x6FEB9096", "gires4.npk");
            tz366 = NpkImageReader.getNpkPng("0x6FEB9097", "gires4.npk");
            tz367 = NpkImageReader.getNpkPng("0x6FEB9102", "gires4.npk");
            tz368 = NpkImageReader.getNpkPng("0x6FEB9099", "gires4.npk");
            tz369 = NpkImageReader.getNpkPng("0x6FEB9110", "gires4.npk");
            tz372 = NpkImageReader.getNpkPng("0x6FEB9118",  "gires4.npk");
            tz373 = NpkImageReader.getNpkPng("0x6FEB9127",  "gires4.npk");
            tz374 = NpkImageReader.getNpkPng("0x6FEB9128",  "gires4.npk");
            tz375 = NpkImageReader.getNpkPng("0x6FEB9131",  "gires4.npk");
            tz376 = NpkImageReader.getNpkPng("0x6FEB9134",  "gires4.npk");
            tz377 = NpkImageReader.getNpkPng("0x6FEB9135",  "gires4.npk");
            tz378 = NpkImageReader.getNpkPng("0x6FEB9136",  "gires4.npk");
            tz379 = NpkImageReader.getNpkPng("0x6FEB9137",  "gires4.npk");
            tz380 = NpkImageReader.getNpkPng("0x6FEB9138",  "gires4.npk");
            tz381 = NpkImageReader.getNpkPng("0x6FEB9149",  "gires4.npk");
            tz384 = NpkImageReader.getNpkPng("0x6FEB9112",  "gires4.npk");
            tz156 = NpkImageReader.getNpkPng(ImgConstants.tz156,"gires4.npk");
            tz155 = NpkImageReader.getNpkPng(ImgConstants.tz155,98,9,"gires4.npk");
            for (int i = 0; i < 12; i++) {
                tz385[i] =  NpkImageReader.getNpkPng("0x6FEB8"+(693+i), 56, 56, "gires4.npk");
            }
            tz386 = NpkImageReader.getNpkPng("0x6FEB9111",  "gires4.npk");
            tz387 = NpkImageReader.getNpkPng("0x6FEB9148",  "gires4.npk");
            tz388 = NpkImageReader.getNpkPng("0x6FEB9147",  "gires4.npk");

            //水墨模型===============================================================
            tz339 = NpkImageReader.getNpkPng("0x6FAC1014",  "she.npk");
            tz342 = NpkImageReader.getNpkPng("0x6FAC1082",  "she.npk");
            tz382 = NpkImageReader.getNpkPng("0x6FAC1098",  "she.npk");
            tz370 = NpkImageReader.getNpkPng("0x6FAC1094",  "she.npk");
            tz371 = NpkImageReader.getNpkPng("0x6FAC1095",  "she.npk");
            tz355 = NpkImageReader.getNpkPng("0x6FAC1088",  "she.npk");
            tz356 = NpkImageReader.getNpkPng("0x6FAC1089",  "she.npk");
            tz357 = NpkImageReader.getNpkPng("0x6FAC1090",  "she.npk");
            tz358 = NpkImageReader.getNpkPng("0x6FAC1091",  "she.npk");
            tz351 = NpkImageReader.getNpkPng("0x6FAC1084",  "she.npk");
            tz347 = NpkImageReader.getNpkPng("0x6FAC1083",  "she.npk");
            //红木模型===============================================================
            tz348 = NpkImageReader.getNpkPng("0x6FAB1095",  "redmu.npk");
            tz340 = NpkImageReader.getNpkPng("0x6FAB1017",  "redmu.npk");
            tz338 = NpkImageReader.getNpkPng("0x6FAB1022",  "redmu.npk");
            tz383 = NpkImageReader.getNpkPng("0x6FAB1104",  "redmu.npk");
            tz359 = NpkImageReader.getNpkPng("0x6FAB1100",  "redmu.npk");
            tz360 = NpkImageReader.getNpkPng("0x6FAB1101",  "redmu.npk");
            tz361 = NpkImageReader.getNpkPng("0x6FAB1102",  "redmu.npk");
            //按钮模型===============================================================
            bt1 = NpkImageReader.NpkcutsPngBtn("0x6FAC1087","she.npk");
            bt3 = NpkImageReader.NpkcutsPngBtn("0x6FAC1092","she.npk");
            bt4 = NpkImageReader.NpkcutsPngBtn("0x6FAC1093","she.npk");
            bt7 = NpkImageReader.NpkcutsPngBtn("0x6FAC1096","she.npk");


            bt2 = NpkImageReader.NpkcutsPngBtn("0x6FAB1099","redmu.npk");
            bt5 = NpkImageReader.NpkcutsPngBtn("0x6FAB1011","redmu.npk");
            bt6 = NpkImageReader.NpkcutsPngBtn("0x6FAB1012","redmu.npk");
            bt8 = NpkImageReader.NpkcutsPngBtn("0x6FAB1103","redmu.npk");
            bt11 = NpkImageReader.NpkcutsPngBtn("0x6FAB1024","redmu.npk");
            bt13 = NpkImageReader.NpkcutsPngBtn( "0x6FAB1035","redmu.npk");


            bt9 = CutButtonImage.cutsPngBtn("0x6FEB9139","gires4.npk");
            for (int i = 0; i < 3; i++) {
                bt10 = NpkImageReader.NpkcutsPngBtn("0x6FEB847"+(i+3),"gires4.npk");
            }
            bt12 = NpkImageReader.NpkcutsPngBtn( "0x6FEB8478","gires4.npk");

            bt14 = NpkImageReader.NpkcutsPngBtn("0x6FAC1040","she.npk");
            bt15 = NpkImageReader.NpkcutsPngBtn("0x6FAC1041","she.npk");
            bt16 = NpkImageReader.NpkcutsPngBtn("0x6FAC1042","she.npk");
            bt17 = NpkImageReader.NpkcutsPngBtn("0x6FAC1043","she.npk");
            bt18 = NpkImageReader.NpkcutsPngBtn("0x6FAC1044","she.npk");
            bt19 = NpkImageReader.NpkcutsPngBtn("0x6FAC1045","she.npk");
            bt20 = NpkImageReader.NpkcutsPngBtn("0x6FAC1046","she.npk");
            bt21 = NpkImageReader.NpkcutsPngBtn("0x6FAC1047","she.npk");
            bt22 = NpkImageReader.NpkcutsPngBtn("0x6FAC1048","she.npk");
            bt23 = NpkImageReader.NpkcutsPngBtn("0x6FAC1049","she.npk");
            bt24 = NpkImageReader.NpkcutsPngBtn("0x6FAC1050","she.npk");
            bt25 = NpkImageReader.NpkcutsPngBtn("0x6FAC1051","she.npk");

            bt26 = NpkImageReader.NpkcutsPngBtn("0x6FAB1045","redmu.npk");
            bt27 = NpkImageReader.NpkcutsPngBtn("0x6FAB1046","redmu.npk");
            bt28 = NpkImageReader.NpkcutsPngBtn("0x6FAB1047","redmu.npk");
            bt29 = NpkImageReader.NpkcutsPngBtn("0x6FAB1048","redmu.npk");
            bt30 = NpkImageReader.NpkcutsPngBtn("0x6FAB1049","redmu.npk");
            bt31 = NpkImageReader.NpkcutsPngBtn("0x6FAB1050","redmu.npk");
            bt32 = NpkImageReader.NpkcutsPngBtn("0x6FAB1051","redmu.npk");
            bt33 = NpkImageReader.NpkcutsPngBtn("0x6FAB1052","redmu.npk");
            bt34 = NpkImageReader.NpkcutsPngBtn("0x6FAB1053","redmu.npk");
            bt35 = NpkImageReader.NpkcutsPngBtn("0x6FAB1054","redmu.npk");
            bt36 = NpkImageReader.NpkcutsPngBtn("0x6FAB1055","redmu.npk");
            bt37 = NpkImageReader.NpkcutsPngBtn("0x6FAB1056","redmu.npk");
            bt38 = NpkImageReader.NpkcutsPngBtn("0x6FAC1008","she.npk");
            bt39 = NpkImageReader.NpkcutsPngBtn("0x6FAB1002","redmu.npk");
            bt40 = NpkImageReader.NpkcutsPngBtn("0x6FAC1078","she.npk");
            bt41 = NpkImageReader.NpkcutsPngBtn("0x6FAB1091","redmu.npk");
            bt42 = NpkImageReader.NpkcutsPngBtn("0x6FAC1057","she.npk");
            bt43 = NpkImageReader.NpkcutsPngBtn("0x6FAB1073","redmu.npk");
            bt44 = NpkImageReader.NpkcutsPngBtn("0x6FAC1059","she.npk");
            bt45 = NpkImageReader.NpkcutsPngBtn("0x6FAB1075","redmu.npk");
            bt46 = NpkImageReader.NpkcutsPngBtn("0x6FAC1056","she.npk");
            bt47 = NpkImageReader.NpkcutsPngBtn("0x6FAB1072","redmu.npk");
            bt48 = NpkImageReader.NpkcutsPngBtn("0x6FAC1058","she.npk");
            bt49 = NpkImageReader.NpkcutsPngBtn("0x6FAB1074","redmu.npk");
            bt50 = NpkImageReader.NpkcutsPngBtn("0x6FAC1055","she.npk");
            bt51 = NpkImageReader.NpkcutsPngBtn("0x6FAB1071","redmu.npk");
            bt52 = NpkImageReader.NpkcutsPngBtn("0x6FAC1054","she.npk");
            bt53 = NpkImageReader.NpkcutsPngBtn("0x6FAB1070","redmu.npk");

            //loginView
            btnBackground = CutButtonImage.getWdfPng("0x6FEB8742","login.wdf");
            background2 = CutButtonImage.getWdfPng("0x6FEB8743","login.wdf");
            userAccountBg = CutButtonImage.getWdfPng("0x6FEB8744","login.wdf");
            putongdl = CutButtonImage.getWdfPng("0x6FEB8747","login.wdf");
            zhangh = CutButtonImage.getWdfPng("0x6FEB8745","login.wdf");
            mim = CutButtonImage.getWdfPng("0x6FEB8748","login.wdf");
            kehu = CutButtonImage.getWdfPng("0x6FEB8751","login.wdf");
            guanwang = CutButtonImage.getWdfPng("0x6FEB8752","login.wdf");
            Sunlight = CutButtonImage.getWdfPng("0x6FEB8753","login.wdf");
            //AreaView
            xqbj = CutButtonImage.getWdfPng("0x6FEB8762","login.wdf");
            xqbj1 = CutButtonImage.getWdfPng("0x6FEB8742","login.wdf");
            state = CutButtonImage.getWdfPng("0x6FEB8780","login.wdf");
            xqbj3 = CutButtonImage.getWdfPng("0x6FEB8764","login.wdf");
            jrbj = CutButtonImage.getWdfPng("0x6FEB8774","login.wdf");
            zst = CutButtonImage.getWdfPng("0x6FEB8805","login.wdf");
            xqhtk = CutButtonImage.getWdfPng("0x6FEB8806","login.wdf");
            listServer = CutButtonImage.getWdfPng("0x6FEB8775","login.wdf");
            yjhServer = CutButtonImage.getWdfPng("0x6FEB8784","login.wdf");
            opServer = CutButtonImage.getWdfPng("0x6FEB8808","login.wdf");
            //RoleView
            rolebeijing = CutButtonImage.getWdfPng("0x6FEB8779","login.wdf");
            dise = CutButtonImage.getWdfPng("0x6FEB8770","login.wdf");
            dise2 = CutButtonImage.getWdfPng("0x6FEB8771","login.wdf");
            btnCreateRoleBg =  CutButtonImage.getWdfPng("0x6FEB8814","login.wdf");//角色背景
            cyjs = CutButtonImage.getWdfPng("0x6FEB8769","login.wdf");//Cang

            //拆分拉伸图像.
            good_2 =Juitil.WindowImgPng(ImgConstants.Goods_2,7,7);
            JK_24 = Juitil.WindowImgPng("0x6FEB8491",2,2);
            book = Juitil.WindowImgPng(ImgConstants.book, 2, 2);
            partimg = Juitil.WindowImgPng(ImgConstants.part, 2, 2);
            tz21 = Juitil.WindowImgPng(ImgConstants.tz21, 27, 27);
            tz22 = Juitil.WindowImgPng(ImgConstants.tz22, 14, 7);
            tz26 = Juitil.WindowImgPng(ImgConstants.tz26, 7, 7);
            JK = Juitil.WindowImgPng(ImgConstants.JK_1_s, 2, 2);
            JK_1 = Juitil.WindowImgPng(ImgConstants.JK_2_n, 14, 14);
            parts = Juitil.WindowImgPng(ImgConstants.part, 2, 2);
            tz86 = Juitil.WindowImgPng(ImgConstants.tz86, 14, 7);
            tz23 = Juitil.WindowImgPng(ImgConstants.tz23, 2, 2);
            tz76  = Juitil.WindowImgPng(ImgConstants.tz76,2,2);
            tz61  = Juitil.WindowImgPng(ImgConstants.tz61,2,2);
            tz62  = Juitil.WindowImgPng(ImgConstants.tz62,2,2);
            kkn = Juitil.WindowImg_1(ImgConstants.Radio_67, 2, 2);
            kkn1 = Juitil.WindowImg_1(ImgConstants.Radio_108, 2, 2);
            tz67 = Juitil.WindowImgPng(ImgConstants.tz67, 28, 28);
            tz103 = Juitil.WindowImgPng(ImgConstants.tz103,7,7);
            tz128 = Juitil.WindowImgPng(ImgConstants.tz128,2,2);
            tz159 = Juitil.WindowImgPng(ImgConstants.tz159,7,7);
            tz173 = Juitil.WindowImgPng(ImgConstants.tz173,7,7);
            tz174 = Juitil.WindowImgPng(ImgConstants.tz174,7,7);
            tz177 = Juitil.WindowImgPng(ImgConstants.tz177,2, 2);
            tz179 = Juitil.WindowImgPng(ImgConstants.tz179,14,7);
            tz178 = Juitil.WindowImgPng(ImgConstants.tz178,2,2);
            tz187 = Juitil.WindowImgPng(ImgConstants.tz187, 2, 2);
            tz190 = Juitil.WindowImgPng(ImgConstants.tz190, 7, 7);
            tz226 = Juitil.WindowImgPng(ImgConstants.tz226, 2, 2);
            tz228 = Juitil.WindowImgPng(ImgConstants.tz228,2,2);
            tz237 = Juitil.WindowImgPng(ImgConstants.tz237,2,2);
            tz240 = Juitil.WindowImgPng(ImgConstants.tz240,2,2);
            tz259 = Juitil.WindowImgPng(ImgConstants.tz259,2,2);
            tz249 = Juitil.WindowImgPng(ImgConstants.tz249,7,7);
            tz258 = Juitil.WindowImgPng(ImgConstants.tz258,7,7);
            tz261 = Juitil.WindowImgPng(ImgConstants.tz261,2,2);
            TxtImg = Juitil.WindowImgPng(ImgConstants.TxtImg, 6, 6);
            maskimg = Juitil.WindowImgPng(ImgConstants.Mask,2,2);
            tz253 = CutButtonImage.newcutsPng(ImgConstants.tz253, 2, 2, true,"maps.wdf");
            tz255 = Juitil.WindowImgPng(ImgConstants.tz255, 27, 27);
            tz266 = Juitil.WindowImgPng(ImgConstants.tz266, 2,2);
            tz267 = Juitil.WindowImgPng(ImgConstants.tz267, 7,7);
            notice = Juitil.WindowImgPng(ImgConstants.tz268, 6,6);
            tz270 = Juitil.WindowImgPng(ImgConstants.tz270, 40,40);
            tz271 = Juitil.WindowImgPng(ImgConstants.tz271, 2,2);
            tz54 = Juitil.WindowImgPng(ImgConstants.tz54, 2,2);
            tz293 = Juitil.WindowImgPng(ImgConstants.tz293, 2,2);
            tz318 = Juitil.WindowImgPng(ImgConstants.tz318, 2,2);
            tx321 = Juitil.WindowImgPng("0x6FEB9020", 2,2);
            tx322 = Juitil.WindowImgPng("0x6FEB9100", 16,16);


            for (int i = 0; i < 4; i++) {
                labpetequimg[i] = NpkImageReader.getNpkPng("0x6FEB859"+(3+i),"gires4.npk");
            }
            //精灵图像(动态)
            //TODO 动态图像必须使用 newdynamic 逻辑，内存很好的释放！！！
            vnavi_1_sprite = WdfLoaDing.newdynamic("0x4447BB79","sprite.wdf");
            XPback = WdfLoaDing.newdynamic("0x9FEA0001","sprite.wdf");
            XPBackYY = WdfLoaDing.newdynamic("0x9FEA0002","sprite.wdf");
            XPBackTX = WdfLoaDing.newdynamic("0x9FEA0003","sprite.wdf");
            XPBackDL = WdfLoaDing.newdynamic("0x9FEA0005","sprite.wdf");
            JHKSILL = WdfLoaDing.newdynamic("0x6E44E677","sprite.wdf");
            dj0 = WdfLoaDing.newdynamic("0x1E98E6C", "sprite.wdf");
            dj1 = WdfLoaDing.newdynamic("0x21D5B7C", "sprite.wdf");
            dj2 = WdfLoaDing.newdynamic("0xBB315CA", "sprite.wdf");
            dj3 = WdfLoaDing.newdynamic("0x1122B147", "sprite.wdf");
            dj4 = WdfLoaDing.newdynamic("0x15C4F689", "sprite.wdf");
            dj5 = WdfLoaDing.newdynamic("0x25424FB4", "sprite.wdf");
            dj6 = WdfLoaDing.newdynamic("0x31ABF55A", "sprite.wdf");
            dj7 = WdfLoaDing.newdynamic("0x323D1D82", "sprite.wdf");
            dj8 = WdfLoaDing.newdynamic("0x4A7239F2", "sprite.wdf");
            dj9 = WdfLoaDing.newdynamic("0x52606AB3", "sprite.wdf");
            dj10 = WdfLoaDing.newdynamic("0x56573E66", "sprite.wdf");
            dj11 = WdfLoaDing.newdynamic("0x5C24C2E5", "sprite.wdf");
            dj12 = WdfLoaDing.newdynamic("0x6A790FF9", "sprite.wdf");
            dj13 = WdfLoaDing.newdynamic("0x75BBDEDC", "sprite.wdf");
            dj14 = WdfLoaDing.newdynamic("0x7650402B", "sprite.wdf");
            dj15 = WdfLoaDing.newdynamic("0x769F2F2B", "sprite.wdf");
            dj16 = WdfLoaDing.newdynamic("0x836FADC7", "sprite.wdf");
            dj17 = WdfLoaDing.newdynamic("0x8CC6CE25", "sprite.wdf");
            dj18 = WdfLoaDing.newdynamic("0x9E208435", "sprite.wdf");
            dj19 = WdfLoaDing.newdynamic("0xA1B49631", "sprite.wdf");
            dj20 = WdfLoaDing.newdynamic("0xA7BCC991", "sprite.wdf");
            dj21 = WdfLoaDing.newdynamic("0xABB7B538", "sprite.wdf");
            dj22 = WdfLoaDing.newdynamic("0xC60912A6", "sprite.wdf");
            dj23 = WdfLoaDing.newdynamic("0xC7C76E96", "sprite.wdf");
            dj24 = WdfLoaDing.newdynamic("0xCA2AE860", "sprite.wdf");
            dj25 = WdfLoaDing.newdynamic("0xD4288936", "sprite.wdf");
            dj26 = WdfLoaDing.newdynamic("0xDC2382B8", "sprite.wdf");
            dj27 = WdfLoaDing.newdynamic("0xE36C34F4", "sprite.wdf");
            dj28 = WdfLoaDing.newdynamic("0xEA378737", "sprite.wdf");
            dj29 = WdfLoaDing.newdynamic("0xF07F6EC7", "sprite.wdf");
            dj30 = WdfLoaDing.newdynamic("0xF68E9ACC", "sprite.wdf");
            dj31 = WdfLoaDing.newdynamic("0xFA4F087C", "sprite.wdf");
            tx1 = WdfLoaDing.newdynamic("0x28BC963B", "sprite.wdf");
            tx2 = WdfLoaDing.newdynamic("0xDB71A9F0", "sprite.wdf");
            skitx = WdfLoaDing.newdynamic("0xEE52DE10", "sprite.wdf");



            for (int i = 0; i < XPDL.length; i++) {
                XPDL[i] = WdfLoaDing.newdynamic("0x9FEA00"+(30+i),"sprite.wdf");
            }
            for (int i = 0; i < XPJD.length; i++) {
                XPJD[i] = WdfLoaDing.newdynamic("0x9FEA00"+(40+i),"sprite.wdf");
            }
            for (int i = 0; i < XPJH.length; i++) {
                XPJH[i] = WdfLoaDing.newdynamic("0x9FEA00"+(50+i),"sprite.wdf");
            }
            //was图像(静态)
            tz216 = Juitil.WindowImgWdf(ImgConstants.tz216,2,2);
            tz218 = Juitil.WindowImgWdf(ImgConstants.tz218,2,2);
            tz219 = Juitil.WindowImgWdf(ImgConstants.tz219,2,2);
            tz221 = Juitil.WindowImgWdf(ImgConstants.tz221,2,2);
            tz222 = Juitil.WindowImgWdf(ImgConstants.tz222,2,2);
            tz223 = Juitil.WindowImgWdf(ImgConstants.tz223,2,2);
            tz224 = Juitil.WindowImgWdf(ImgConstants.tz224,2,2);
            tz225 = Juitil.WindowImgWdf(ImgConstants.tz225,2,2);
            tx319 = Juitil.WindowImgWdf(ImgConstants.tx331,2,2);
            tz349= CutButtonImage.getImageWas("0x53586796",  "sprite.wdf");//等级
            tz343 = CutButtonImage.getImageWas("0xAB3E758E",  "sprite.wdf");//装备
            tz350 = CutButtonImage.getImageWas("0xE028AE8",  "sprite.wdf");//召唤兽

            //RedMU加载
            red_0000 = Juitil.WindowRedImgPng("0x6FAB1000", 106,60);
            red_0001 = Juitil.WindowRedImgPng("0x6FAB1001", 2,  2);
            red_0005 = Juitil.WindowRedImgPng("0x6FAB1005", 7,  7);
            red_0006 = Juitil.WindowRedImgPng("0x6FAB1006", 20, 20);
            red_0007 = Juitil.WindowRedImgPng("0x6FAB1007", 30, 30);
            red_0008 = Juitil.WindowRedImgPng("0x6FAB1008", 2, 2);
            red_0009 = Juitil.WindowRedImgPng("0x6FAB1009", 22, 20);
            red_0018 = Juitil.WindowRedImgPng("0x6FAB1018", 7, 7);
            red_0019 = Juitil.WindowRedImgPng("0x6FAB1019", 2, 2);
            red_0023 = Juitil.WindowRedImgPng("0x6FAB1023", 2, 2);
            red_0036 = Juitil.WindowRedImgPng("0x6FAB1036", 7, 7);
            red_0037 = Juitil.WindowRedImgPng("0x6FAB1037", 7, 7);
            red_0038 = Juitil.WindowRedImgPng("0x6FAB1038", 7, 7);
            red_0039 = Juitil.WindowRedImgPng("0x6FAB1058",14,7);
            red_0040 = Juitil.WindowRedImgPng("0x6FAB1057",2, 2);
            red_0041 = Juitil.WindowRedImgPng("0x6FAB1059",2, 2);
            red_0042 = Juitil.WindowRedImgPng("0x6FAB1044",2, 2);
            red_0043 = Juitil.WindowRedImgPng("0x6FAB1083",7, 7);
            red_0044 = Juitil.WindowRedImgPng("0x6FAB1084",2, 2);
            red_0045 = Juitil.WindowRedImgPng("0x6FAB1094",2, 2);
            red_0046 = Juitil.WindowRedImgPng("0x6FAB1096",7, 7);
            //She加载
            she_0001 = Juitil.WindowSheImgPng("0x6FAC1001", 2, 2);
            she_0002 = Juitil.WindowSheImgPng("0x6FAC1002", 2, 2);
            she_0003 = Juitil.WindowSheImgPng("0x6FAC1003", 14, 13);
            she_0004 = Juitil.WindowSheImgPng("0x6FAC1004", 2, 2);
            she_0005 = Juitil.WindowSheImgPng("0x6FAC1005", 2, 2);
            she_0006 = Juitil.WindowSheImgPng("0x6FAC1006", 2, 2);
            she_0007 = Juitil.WindowSheImgPng("0x6FAC1007", 2, 2);
            she_0009 = Juitil.WindowSheImgPng("0x6FAC1009", 17, 21);
            she_0019 = Juitil.WindowSheImgPng("0x6FAC1019", 5, 5);
            she_0021 = Juitil.WindowSheImgPng("0x6FAC1021", 7, 7);
            she_0022 = Juitil.WindowSheImgPng("0x6FAC1022", 7, 7);
            she_0023 = Juitil.WindowSheImgPng("0x6FAC1072", 2, 2);
            she_0024 = Juitil.WindowSheImgPng("0x6FAC1080", 2, 2);
//        };
//        ActivityJpanel.executor.submit(task);

        } catch (Exception e) {
            System.err.println("图像加载过程中出现异常: " + e.getMessage());
            e.printStackTrace();
            // 继续执行，不中断程序
        }
    }

    /**
     * 安全的图像加载方法（用于 Native Image 环境）
     */
    private static void loadImagesSafely() {
        try {
            System.out.println("Native Image 环境：跳过图像加载");
            // 在 Native Image 环境中，完全跳过图像加载
            // 只设置一些基本的标志，避免空指针异常
            // 这里可以设置一些基本的静态变量为默认值
            // 但不创建任何 AWT 相关的对象
            System.out.println("Native Image 模式图像加载完成（已跳过）");
        } catch (Exception e) {
            System.err.println("安全图像加载失败: " + e.getMessage());
            // 即使失败也不抛出异常，继续运行
        }
    }


    /**设置组件为圆形坐标*/
    public static int setRound(JComponent jComponent,int x,int y,int w,int h){
                    int centerX = 280; // 圆心的X坐标
                     int centerY = 200; // 圆心的Y坐标
                     int radius = 70;  // 圆的半径
                     int numElements = 6; // 元素数量
                    double angleIncrement =  Math.PI / numElements * 2; // 角度增量
                    for (int i = 0; i < numElements; i++) {
                        double angle = i * angleIncrement; // 当前元素的角度
//                        int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
//                        int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
//                        Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1);
                    }
        return  x;
    }


    // 定义一个并发修改安全的映射，用于存储子类别及其标识符
    // 使用ConcurrentHashMap保证在多线程环境下安全地进行子类别的增删改查操作
    public static Map<Integer,String> subCategories =new ConcurrentHashMap<>();
    static {
        subCategories.put(0,"     #W炼化公式#r#c66bbaa装备+内丹+玲珑+九彩");
        subCategories.put(1, "     #W炼化公式#r#c66bbaa1、仙器 + 仙器精华#r2、仙器 + 一阶仙器");
        subCategories.put(2,"#W公式#r#c66bbaa配饰重铸#r1、配饰 + 矿石等级+3。#r配饰培养#r1、配饰 + 配饰或精华#r护身符重铸#r1、护身符 + 矿石#r护身符培养#r1、护身符 + 护身符");
        subCategories.put(3, "#W公式#r#c66bbaa1、秘石合成*4#r使用等级相同的秘石合成#r最高5级");
        subCategories.put(4, "#W公式#r#c66bbaa1、彩晶石培养#r主彩晶石 + 副彩晶石");
        subCategories.put(5, "#W公式#r#c66bbaa1、装备培养#r主装备 + 伙伴装备精华#r2、装备升级#r满默契 + 6级矿石+1级#r3、装备重铸#r主装备 + 矿石（1级装备对应6级矿石）");
    }
    public static Map<Integer,String> EQMake =new ConcurrentHashMap<>();
    static {
        EQMake.put(0,"#W打造公式#r#c66bbaa二级装备 + 乌金#r" +
                "三级装备 + 金刚石#r" +
                "四级装备 + 寒铁#r" +
                "五级装备 + 百炼精铁#r" +
                "六级装备 + 龙鳞#r" +
                "七级装备 + 千年寒铁#r" +
                "八级装备 + 天外飞石#r" +
                "九级装备 + 盘古精铁#r" +
                "十级装备 + 补天神石#r" +
                "打造成功后获得一个随机属性的装备。");
        EQMake.put(1,"#W打造公式#r#c66bbaa十二级装备 + 天外飞石#r" +
                "十三级装备 + 盘古精铁#r" +
                "十四级装备 + 补天神石#r" +
                "十五级装备 + 六魂之玉#r" +
                "十六级装备 + 无量琉璃#r" +
                "备注1：11-14级加入1个巫铸材料，可获得定向种族的更加强力的装备。#r" +
                "备注2：15级加入4个（武，衣，帽），3个（鞋，链）巫铸材料。#r" +
                "备注3：16级加入25个（武，衣，帽），16个（鞋，链）巫铸材料。");
        EQMake.put(2,"#W公式#r#c66bbaa仙器 + 悔梦石");
        EQMake.put(3,"#W公式#r#c66bbaa八荒遗风 + 矿石");
        EQMake.put(4,"#W公式#r#c66bbaa八荒遗风 + 一阶仙器");
    }
    public static Map<Integer,String> fushitou =new ConcurrentHashMap<>();
    static {
        fushitou.put(0,"#W洗练公式#r#c66bbaa1,2,3级符石不能洗练#r5级符石用2级符石");
        fushitou.put(1,"#W升级公式#r#c66bbaa不支持5级以上合成#r合成物品需要同等级");
    }

    public static Map<Integer,String> godtext =new ConcurrentHashMap<>();
    static {
        godtext.put(0,"#W公式#r#c66bbaa神兵 + 矿石");
        godtext.put(1,"#W公式#r#c66bbaa神兵 + 神兵石");
        godtext.put(2,"#W公式#r#c66bbaa4级神兵 + 神兵");
    }

    public static Map<Integer,String> GEMSTEXT =new ConcurrentHashMap<>();
    static {
        GEMSTEXT.put(0,"#W公式#r#c66bbaa同等级宝石x2");
        GEMSTEXT.put(1,"#W公式#r#c66bbaa1、需要消耗一颗比重铸宝石等级低4级的宝石#r" +
                "2、重铸100%成功#r" +
                "3、重铸前后价值不变#r" +
                "4、重铸可选择任意种类，属性从该种类对应下的属性中随机1条#r" +
                "5、>=5级宝石可以重铸");
        GEMSTEXT.put(2,"#W公式#r#c66bbaa1、仅奇异石需要鉴定#r" +
                "2、需要消耗一颗比奇异石等级低3级的宝石#r" +
                "3、鉴定100%成功#r" +
                "4、鉴定后，种类和属性随机#r" +
                "5、鉴定后的宝石价值较高");
    }

    // 定义一个并发哈希表，用于存储类别及其描述
    // 该表的结构为：主类别ID -> 子类别ID -> 子类别描述
    // 使用ConcurrentHashMap保证在多线程环境下的线程安全
    public static Map<Integer, Map<Integer, String>> categoryDescriptions = new ConcurrentHashMap<>();
    // 静态初始化块，用于初始化categoryDescriptions
    // 在类加载时，将预定义的子类别及其描述添加到categoryDescriptions中
    static {
        categoryDescriptions.put(1, subCategories);
        categoryDescriptions.put(2, EQMake);
        categoryDescriptions.put(6, fushitou);
        categoryDescriptions.put(7, godtext);
        categoryDescriptions.put(8, GEMSTEXT);
    }

    /**
     * 根据给定的类型和最小类型获取提示信息
     * 该方法用于提供针对特定类型和最小类型的提示信息它从一个预定义的集合中检索相应的描述
     * @param type 类型的标识符，用于确定要检索提示信息的主类别
     * @param mintype 最小类型标识符，在主类别下进一步细化提示信息的子类别
     * @return 返回针对指定类型和最小类型的提示信息字符串如果找不到对应的提示信息，可能返回null
     */
    public static String getPrompt(int type, int mintype) {
       return categoryDescriptions.get(type).get(mintype);
    }

    public static String TextInedx(int minType) {
        //文本说明
        switch (minType){
            case 5:
                case 7:
                return "#W打造公式#r#c66bbaa装备开光 + 九天仙玉#r";
                case 8:
                    return "#W打造公式#r#c66bbaa装备炼器 + 落魄沙*3#r";
                    case 9:
                    return "#W精炼公式#r#c66bbaa装备 + 精炼石 + 祝福符*3#r精炼成功后等级+1，装备基础属性提升！#r精炼失败后装备等级-1#r如没有放置祝福符精炼失败后装备消失！";
                default:
                return "";
        }
    }

    /**根据矿石等级返回矿石名称*/
    public static String getMineralName(int level) {
        switch (level) {
            case 1:
                return "乌金";
            case 2:
                return "金刚石";
            case 3:
                return "寒铁";
            case 4:
                return "百炼精铁";
            case 5:
                return "龙鳞";
            case 6:
                return "千年寒铁";
            case 7:
                return "天外飞石";
            case 8:
                return"盘古精铁";
            case 9:
                return"补天神石";
            case 10:
                return"六魂之玉";
            case 11:
                return"无量琉璃";
            default:
                return "乌金";
        }
    }
    /**文本剧中绘制 */
    public static int CenterTextdrawing(Graphics g,String text,int x,int x1,int index,Font font) {
        FontMetrics metrics = org.come.until.SafeFontMetrics.getFontMetrics(g, font);
        int textWidth = metrics.stringWidth(text);
        return  x + index * x1 - textWidth / 2; // 调整X坐标，使其居中
    }

    /**
     * 通过获取图像改变图像的RGP颜色，存储到 Map imageCache 中，通过getCachedImage过去图像，减少内存占用
     * @param image 物品图片来源
     * @return 返回需要的图像
     */
    public static BufferedImage convertToGray(Image image) {
        BufferedImage grayImage = new BufferedImage(image.getWidth(null), image.getHeight(null), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = grayImage.createGraphics();
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();
        int width = grayImage.getWidth();
        int height = grayImage.getHeight();
        int[] pixels = new int[width * height];
        PixelGrabber pixelGrabber = new PixelGrabber(grayImage, 0, 0, width, height, pixels, 0, width);

        try {
            pixelGrabber.grabPixels();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        for (int i = 0; i < pixels.length; i++) {
            int pixel = pixels[i];
            int alpha = (pixel >> 24) & 0xFF;
            int gray = (int) (0.2126 * ((pixel >> 16) & 0xFF) + 0.7152 * ((pixel >> 8) & 0xFF) + 0.0722 * (pixel & 0xFF));
            int grayPixel = (alpha << 24) | (gray << 16) | (gray << 8) | gray;
            pixels[i] = grayPixel;
        }

        grayImage.setRGB(0, 0, width, height, pixels, 0, width);
        return grayImage;
    }

    /**判断技能是否主动技能*/
    public static boolean isActiveSkill(String skillId) {
        // 检查 skillId 是否为空
        if (skillId == null || skillId.isEmpty()) {
            return false;
        }

        try {
            int id = Integer.parseInt(skillId);
            // 技能ID为1600到1605的技能视为主动技能
            return id >= 1600 && id <= 1605;
        } catch (NumberFormatException e) {
            // 如果 skillId 不能转换为整数，则返回 false
            return false;
        }
    }

    /**
     * 创建一个具有自定义颜色和字体的 JTextArea
     * 此方法允许创建一个文本区域，可以自定义文本的颜色和字体，并且在绘制文本时自动换行
     *
     * @param color 文本的颜色
     * @param font  文本的字体
     * @return 自定义颜色和字体的 JTextArea 对象
     */
    public static JTextArea JTextArea(Color color,Font font){
        JTextArea textArea = new JTextArea(){
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, getWidth(), getHeight());

                // 设置字体和颜色
                g2d.setFont(font != null ? font : SetupMainJPanel.getAllFont());
                g2d.setColor(color);

                // 获取字体度量信息
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                int textWidth = getWidth() -30; // 设置一些边距
                int x = 10; // 起始 x 坐标
                int y = NPCJfram.getNpcJfram().getNpcjpanel().getHeadHeight() +fm.getAscent() + 10; // 起始 y 坐标

                // 获取文本并处理换行
                String text = getText();
                String[] words = text.split("");
                StringBuilder currentLine = new StringBuilder();
                for (String word : words) {
                    // 计算当前行加上新词的宽度
                    String testLine = currentLine + (currentLine.length() > 0 ? " " : "") + word;
                    int lineWidth = fm.stringWidth(testLine);

                    // 如果当前行宽度超出文本区域宽度，则换行
                    if (lineWidth > textWidth) {
                        g2d.drawString(currentLine.toString(), x, y);
                        y += fm.getHeight(); // 移动到下一行
                        currentLine = new StringBuilder(word); // 开始新行
                    } else {
                        if (currentLine.length() > 0) {
                            currentLine.append("");
                        }
                        currentLine.append(word);
                    }
                }


                // 绘制最后一行的文本
                if (currentLine.length() > 0) {
                    //绘制黑色描边
                    g2d.setColor(Color.black);
                    g2d.setFont(UIUtils.FZCY_HY15);
                    g2d.drawString(currentLine.toString(), x+1, y + 1);
                    g2d.drawString(currentLine.toString(), x, y);
                    g2d.setColor(color);
                    g2d.drawString(currentLine.toString(), x, y);
                }
                setLineWrap(true);
                setCaretColor(Color.yellow); // 设置光标颜色
                g2d.dispose(); // 释放资源
            }
        };

        // Native Image 兼容性处理
        org.come.util.SafeUIHelper.safeUpdateUI(textArea);

        return textArea;
    }


    public static void getLineWrap(Graphics g,Color color,Font font,int w,int h,String text){
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, w, h);

                // 设置字体和颜色
                g2d.setFont(font != null ? font : SetupMainJPanel.getAllFont());
                g2d.setColor(color);

                // 获取字体度量信息
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                int textWidth = w -30; // 设置一些边距
                int x = 14; // 起始 x 坐标
                int y = fm.getAscent() + 48; // 起始 y 坐标

                // 获取文本并处理换行
                String[] words = text.split("");
                StringBuilder currentLine = new StringBuilder();
                for (String word : words) {
                    // 计算当前行加上新词的宽度
                    String testLine = currentLine + (currentLine.length() > 0 ? " " : "") + word;
                    int lineWidth = fm.stringWidth(testLine);

                    // 如果当前行宽度超出文本区域宽度，则换行
                    if (lineWidth > textWidth) {
                        g2d.drawString(currentLine.toString(), x, y);
                        y += fm.getHeight(); // 移动到下一行
                        currentLine = new StringBuilder(word); // 开始新行
                    } else {
                        if (currentLine.length() > 0) {
                            currentLine.append("");
                        }
                        currentLine.append(word);
                    }
                }


                // 绘制最后一行的文本
                if (currentLine.length() > 0) {
                    //绘制黑色描边
                    g2d.setColor(Color.black);
                    g2d.setFont(font);
                    g2d.drawString(currentLine.toString(), x+1, y + 1);
                    g2d.drawString(currentLine.toString(), x, y);
                    g2d.setColor(color);
                    g2d.drawString(currentLine.toString(), x, y);
                }
                g2d.dispose(); // 释放资源
    }
    public static JTextField jTextField(Color color,Font font){
        JTextField textField = new JTextField(){
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, getWidth(), getHeight());
                // 设置字体和颜色
                g2d.setFont(font != null ? font : SetupMainJPanel.getAllFont());
                g2d.setColor(color);
                // 获取字体度量信息
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                int textWidth = getWidth() -30; // 设置一些边距
                int x = 10; // 起始 x 坐标
                int y = fm.getAscent() +2; // 起始 y 坐标

                // 获取文本并处理换行
                String text = getText();
                String[] words = text.split("");
                StringBuilder currentLine = new StringBuilder();
                for (String word : words) {
                    // 计算当前行加上新词的宽度
                    String testLine = currentLine + (currentLine.length() > 0 ? " " : "") + word;
                    int lineWidth = fm.stringWidth(testLine);

                    // 如果当前行宽度超出文本区域宽度，则换行
                    if (lineWidth > textWidth) {
                        g2d.drawString(currentLine.toString(), x, y);
                        y += fm.getHeight(); // 移动到下一行
                        currentLine = new StringBuilder(word); // 开始新行
                    } else {
                        if (currentLine.length() > 0) {
                            currentLine.append("");
                        }
                        currentLine.append(word);
                    }
                }


                // 绘制最后一行的文本
                if (currentLine.length() > 0) {
                    //绘制黑色描边
                    g2d.setColor(Color.black);
                    g2d.setFont(UIUtils.FZCY_HY15);
                    g2d.drawString(currentLine.toString(), x+1, y + 1);
                    g2d.drawString(currentLine.toString(), x, y);
                    g2d.setColor(color);
                    g2d.drawString(currentLine.toString(), x, y);
                }
                // 设置光标颜色
                setCaretColor(Color.yellow);

                // 手动绘制光标
                if (hasFocus() && getCaret() != null) {
                    // 获取当前光标位置
                    int caretPos = getCaretPosition();
                    // 计算光标的 x, y 坐标
                    int caretX = x + fm.stringWidth(text.substring(0, caretPos));
                    int caretY = y - fm.getAscent(); // 光标与文本行对齐
                    // 绘制光标
                    g2d.setColor(Color.white);
                    g2d.fillRect(caretX+getText().length(), caretY, 2, fm.getHeight());
                }

                g2d.dispose(); // 释放资源

            }
        };

        // Native Image 兼容性处理
        org.come.util.SafeUIHelper.safeUpdateUI(textField);

        return textField;
    }


    public static JTextField jTextField(Color color,Font font,int t){
        JTextField textField = new JTextField(){
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, getWidth(), getHeight());
                // 设置字体和颜色
                g2d.setFont(font != null ? font : SetupMainJPanel.getAllFont());
                g2d.setColor(color);
                // 获取字体度量信息
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                int x = 10; // 起始 x 坐标
                int y = fm.getAscent() +5; // 起始 y 坐标
                // 获取文本并处理换行
                String text = getText();
                    //绘制黑色描边
                    g2d.setFont(UIUtils.TEXT_NAME_FONT);
                    g2d.setColor(color);
                    g2d.drawString(text, x, y);
                // 手动绘制光标
                if (hasFocus() && getCaret() != null) {
                    // 获取当前光标位置
                    int caretPos = getCaretPosition();
                    // 计算光标的 x, y 坐标
                    int caretX = x + fm.stringWidth(text.substring(0, caretPos));
                    int caretY = y - fm.getAscent(); // 光标与文本行对齐
                    // 绘制光标
                    g2d.setColor(Color.white);
                    g2d.fillRect(caretX+getText().length() - 2, caretY+1, 1, fm.getHeight()-2);
                }
                g2d.dispose(); // 释放资源
            }
        };

        // Native Image 兼容性处理
        org.come.util.SafeUIHelper.safeUpdateUI(textField);

        return textField;
    }



    /**打开指定窗口*/
     public static void OpenJFrame(int bn){
         if (!FormsManagement.getframe(bn).isVisible()) {
             FormsManagement.showForm(bn);
             Music.addyinxiao("开关窗口.mp3");// 打开面板
         } else {
             FormsManagement.HideForm(bn);
             Music.addyinxiao("关闭窗口.mp3");
         }
    }

    /**根据数值查找找对应的地图ID*/
    public static int getMapButton(int i) {
        switch (i) {
            case 0:return 3012;
            case 1:return 1230;
            case 2:return 1267;
            case 3:return 1250;
            case 4:return 1244;
            case 5:return 0;//蓬莱;
            case 6:return 1228;
            case 7:return 3180;
            case 8:return 1207;
            case 9:return 1236;
            case 10:return 1211;
            case 11:return 1241;
            case 12:return 1240;
            case 13:return 1232;
            case 14:return 1231;
            case 15:return 3205;
            case 16:return 1296;
            case 17:return 0;//龙窟
            case 18:return 0;//凤巢
            case 19:return 1248;
            case 20:return 0;//花果山
            case 21:return 0;//水帘洞
            case 22:return 0;//海底迷宫
            case 23:return 1208;
            case 24:return 1213;
            case 25:return 0;//地下鬼岛
            case 26:return 0;//沛州
            case 27:return 0;//淫州
            case 28:return 0;//方壶
            case 29:return 0;//蓬莱海渊
            case 30:return 0;//方寸后山
            case 31:return 1246;
            case 32:return 1229;
            case 33:return 1252;
            case 34:return 3307;
            case 35:return 3210;
            case 36:return 3308;
            case 37:return 1280;
            case 38:return 1278;
            case 39:return 1279;
            case 40:return 1251;
            case 41:return 1259;
            case 42:return 1263;
            case 43:return 1272;
            case 44:return 1242;
            case 45:return 1254;
            case 46:return 0;//大雁塔;
            case 47:return 1298;
            case 48:return 1193;
            case 49:return 1210;
            case 50:return 0;//地狱迷宫;
            case 51:return 1227;
        }
        return 0;
    }

    public static String getMineralTest(int mapid) {
//        System.out.println(mapid);
        switch (mapid) {
            case 1207:return "良辰在此应住马，恐惊身后无穷业。& 紫薇拱北邀来雀，五陵击岳说万年。";
            case 1236:return "击节举抉登歌榭，媛绿拂花问酒家。但恨驰晖樽外尽，银笙一曲梦无涯。& 东都漫访东风暮，绿柳轻斜紫陌达。奕奕游侠兮剑气，喧喧市贾耀珠华。";
            case 1267:return "天地人胜冻，日月过痕轻。封魔掠空起，立雪刀犹鸣。& 溯气重峦上，飘摇远道行。冰泥留虎爪，野径落禽翎。";
            case 1263:return "八月风动，尽收一袖乾坤。& 坐观松柏繁盛，共分灵果长生，老鹅苍猿为朋。";
            case 1259:return "赤眉雄狮吞如日月，玉面老象荡乾坤。金鹏展翅转北海。& 笔峰入云八万里，溯风枕程路三分。瑟瑟骸骨成枯岭，殷殷血肉作泥尘。";
            case 1254:return "万象入经空度我，垂首何以见众生。& 澄彻此身坐于松，芳菲始知叽禅钟。";
            case 1252:return "醉里鱼龙异汕浦，醒时星汉欲曙天。四海一掣卷天雨，净瓶甘露度群生。& 浮搓来去不相迷，短掉劈浪白垂虹。瑞需浴日紫烟起，祥风漾月碧水盈。";
            case 1251:return "游丝蜕指来无肯，混铁当门华似峰。怪险奇邪安可畏，云深史走万十程。& 山途九曲生绝壁，悬水跌冲石水崩。流沙舟子惊巨液，高庄饼女芯天莲。";
            case 1250:return "舟客疑为神仙筑，蟹将虾兵竟盘检。& 东胜孤洲华奈衡，敲秀钟灵别洞天。";
            case 1248:return "女儿考解人间世，身似浮萍尺飘零。& 明月清凤絮满订，扰花流水绕屋行。";
            case 1246:return "白鹿为我引，素离啼股勤。月下每规树，灵台方十心。& 登座遇娘客，南行有仙山。云烟照日暖，松竹挂霜寒。";
            case 1244:return "蛟龙尽知系貌处，百川皆汇鱼旗声。& 水玉漫成长夜枕，明珠饱借俱蒙尘。";
            case 1242:return "佛言压续五百载，大圣脱笔名悟空。& 五行联山图行者，两界分野局善恶。";
            case 1241:
            case 1232:
                return "愿借庭蓬分瑞霭，众仙真来饮琼香。& 素手奸纤挽党裳，王母曾裁碧规忙。";
            case 1240:return "相忌较白寄别夜，月华不计照白头。& 螃宫有意贸素手，柱子无心误卷秋。";
            case 1231:return "今朝一骑战尘里，谁笑当年弱马温!& 雕螳务鞍空伏奶，关山时望志欲仲。";
            case 3307:
            case 3210:
            case 3308:
            case 1230:return "仰晚天衢东，俯览百代哀。猎猎凤满袖，闯阔不易开。& 九重华盖上，飞空结域台。云生栋梁间，风索仙师怀。";
            case 1229:
            case 1228:
                return "岁华骚骚容强改，十里村景竟长青。& 方寸山前傍英报，秀色神怡绿棉提。";
            case 1279:
            case 1278:
            case 1280:
            case 1227:
            case 1272:
            case 1296:
                return "跃马卧龙终黄土，浮沉身世付渔娘。& 刀山剑树阴都殿，鬼面鱼灯奈何桥。";
            case 1221:
            case 1222:
            case 1223:
            case 1224:
            case 1226:
            case 1225:return "纵身百尺抑生死，做尽余霾明光现。忽见须弥悬日月，事授发心步生莲。& 雁落平川消高浴，登临万例出世问。四角风动惊铃语，七层灯妄匪兵言。";
            case 3311:return "纵身百尺抑生死，做尽余霾明光现。& 四角风动惊铃语，七层灯妄匪兵言。";
            case 1208:
            case 1193:
            case 1213:return "仙妹误作罗敷女，讶上云坡。圣树烟萝，采药归来与客说。& 武陵夕照新凤暖，花影婆安。野岸闲的，渔火珊鸡翻碧波。";
            case 1211:return "跃马卧龙终黄土，浮沉身世付渔娘。& 刀山剑树阴郁殿、鬼面鱼灯奈何桥。";
            case 1210:return "南北长策查倾尽，醉歌一曲几卷秋。& 景炉烟起局远丘，春意垂怜阅江州。";
            case 3012:return "依稀情女忠魂处，党装辞统已十霜。& 落红满塌苦侵寺，碧所十溪情断肠。";
            case 3205:return "来往无名荒感客，征尘苦雨满衣袍。& 座上寒风折表草、成中斗战正喧置。";
            case 3180:return "瑞霭祥烟笼平，清风明月招格。& 云涉渺，路避迢。地虽千里外，景物一般饶。";
        }
        return "";
    }
    /**星盘物品名字显示*/
    public static String[] XpName(){
        return new String[]{"天巧","天哭","天暴","天慧","天牢","天败",
                            "天损","天罪","天竞","天剑","天寿","天退",
                            "天究","天微","天杀","天异","天速","天空",
                            "天佑","天暗","天捷","天立","天伤","天孤",
                            "天满","天富","天贵","天英","天威","天猛",
                            "天雄","天勇","天闲","天机","天罡","天魁"};
    }

    /**
     * 调用宋体绘制数量
     * @param g G
     * @param i 数量
     * @param x 坐标
     * @param y 坐标
     */
    public static void Sum(Graphics g,int i,int x,int y){
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setFont(new Font("宋体", java.awt.Font.PLAIN, 16));
            g2d.setColor(new Color(188, 188, 188));
            for (int j = 0; j < 2; j++) {
            g2d.drawString("" +i, x, y);
            g2d.drawString("" +i, x+1, y);
            }

            g2d.dispose();
    }

    public static JLabel GJpanelText(Color color,Font font){
        return GJpanelText(color,font,0);
    }

    public static JLabel GJpanelText(Color color,Font font,int index){
        return new JLabel(){
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                if (!font.getFontName().equals("宋体")) {
                    g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                }

                // 先填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, getWidth(), getHeight());
                // 计算文本的中心坐标
                FontMetrics metrics = g2d.getFontMetrics();
                int textWidth = metrics.stringWidth(getText());
                int textHeight = g2d.getFontMetrics().getHeight();
                int centerX = index>0?(index-textWidth)/2:index;
                int centerY = (getHeight() + textHeight) / 2  - 2 ;
                //绘制黑色描边
                g2d.setColor(Color.black);
                g2d.setFont(font!=null?font:SetupMainJPanel.getAllFont());
                g2d.drawString(getText(), centerX + 1, centerY + 1);
                g2d.drawString(getText(), centerX, centerY);
                // 绘制白色文本
                g2d.setColor(color);
                g2d.drawString(getText(), centerX, centerY);
            }
        };
    }


    public static JLabel GJpanel(){
        return new JLabel(){
            protected void paintComponent(Graphics g) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 先填充背景
                g2d.setColor(UIUtils.Color_BACK);
                g2d.fillRect(0, 0, getWidth(), getHeight());
                // 计算文本的中心坐标
                int textWidth = g2d.getFontMetrics().stringWidth(getText());
                int textHeight = g2d.getFontMetrics().getHeight();
                int centerX = (getWidth() - textWidth) / 2;
                int centerY = (getHeight() + textHeight) / 2  - 4 ;

                //绘制黑色描边
                g2d.setColor(Color.black);
                g2d.setFont(UIUtils.MSYH_HY16);
                g2d.drawString(getText(), centerX + 1, centerY + 1);
                g2d.drawString(getText(), centerX, centerY);
                // 绘制白色文本
                g2d.setColor(Color.white);
                g2d.drawString(getText(), centerX, centerY);
            }
        };
    }

    /**
     * 翡翠青山
     * @param g 绘制
     * @param w 宽度
     * @param h 高度
     * @param title 标题
     */
    public static ImgZoom imgZoom;
    public static void JPanelNewShow(Graphics g,int w,int h,String title){
        if (imgZoom==null){
            imgZoom = Juitil.tz270;
        }
        Juitil.ImngBack(g, imgZoom, 0, 0, w, h, 1);
        Juitil.Subtitledrawing(g, w/2-35, 24, title, UIUtils.COLOR_White, UIUtils.HYXKJ_HY20,1);
        Juitil.ImngBack(g, Juitil.tz237, 6, 31, w-18, h-43, 1);
    }

    /**
     * 红木反璞
     * @param g 绘制
     * @param w 宽度
     * @param h 高度
     * @param title 标题
     */
    public static void RedMuNewShow(Graphics g,int w,int h,String title){
        Juitil.ImngBack(g, red_0001, 5, 0, 800, 600, 1);
        Juitil.ImngBack(g, red_0000, 0, 0, w+2, h+93, 1);
        Juitil.Subtitledrawing(g, w/2-35, 20, title, UIUtils.COLOR_CL_RedMU, UIUtils.TEXT_HYJ17,1);
        Juitil.Subtitledrawing(g, w/2-35, 20, title, UIUtils.COLOR_CL_RedMU, UIUtils.TEXT_HYJ17,1);
    }

    /**
     * 水墨山河
     * @param g 绘制
     * @param w 宽度
     * @param h 高度
     * @param title 标题
     */
    public static ImgZoom imgZoomshe;
    public static void SheNewShow(Graphics g,int w,int h,String title,int bh,int foty){
        if (imgZoomshe==null){
            imgZoomshe  = Juitil.WindowSheImgPng("0x6FAC1000", 2, 2);;
        }
        Juitil.ImngBack(g, imgZoomshe, 0, 0, w, h, 0);
        Juitil.ImngBack(g, she_0001, 0, 0, w, h, 0);
        Juitil.ImngBack(g, she_0002, 0, 2, 42, bh, 1);
        Juitil.Txtpet(g, 5, foty+26, title,Color.BLACK, UIUtils.HYXKJ_HY27);
    }


    // 保存原始尺寸的Map
    public static final Map<Integer, Dimension> originalSizes = new HashMap<>();
    // 添加一个工具方法来处理窗体大小
    public static void adjustFrameSize( int w, int h, int bh) {
            try {
                JInternalFrame frame = FormsManagement.getframe(bh);
                if (frame == null) return;
                // 仅当需要初始化且尺寸不同时调整
                if (frame.getWidth() != w || frame.getHeight() != h) {
                       frame.setBounds(frame.getX(), frame.getY(), w, h);
                    }
            } catch (Exception e) {
                // 建议添加日志输出
            }
    }

    // 添加重置方法，在需要重新调整大小时调用
    public static void resetFrameAdjustment(int bh) {
        try {
            JInternalFrame frame = FormsManagement.getframe(bh);
            if (frame != null) {
                // 获取原始尺寸
                Dimension originalSize = originalSizes.get(bh);
                if (originalSize != null) {
                    // 恢复原始尺寸
                    frame.setBounds(frame.getX(), frame.getY(), 
                                  originalSize.width, originalSize.height);
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
    /**
     * 获取图片
     * @param imgs 图片标识
     * @return
     */
    public static ImageIcon[] getImgs(String imgs) {
        return CutButtonImage.cutsPngBtn(imgs, Util.SwitchUI == 0?"defaut.wdf":Util.SwitchUI == 1?"she.wdf":"redmu.wdf");
    }
    public static ImageIcon getImg(String imgs) {
        return CutButtonImage.getWdfPng(imgs, Util.SwitchUI == 0?"defaut.wdf":Util.SwitchUI == 1?"she.wdf":"redmu.wdf");
    }

    public static ImageIcon getImg(String imgs,int w,int h) {
        return CutButtonImage.getWdfPng(imgs,w,h ,Util.SwitchUI == 0?"defaut.wdf":Util.SwitchUI == 1?"she.wdf":"redmu.wdf");
    }

    public static BasicScrollBarUI createUI(int uiType) {
        switch (uiType) {
            case 0: return new SrcollPanelUI(); // 注意 SrcollPanelUI 是否是拼写错误？
            case 1: return new ShePanelUI();
            case 2: return new RedMuPanelUI();
            default: return new SrcollPanelUI();
        }
    }

    /**更改按钮组件的颜色*/
    public static void changeButtonColor(JPanel jPanel,int uiType) {
        for (int i = 0; i < jPanel.getComponents().length; i++) {
            // 检查是否是 MoBanBtn 类型
            if (jPanel.getComponents()[i] instanceof MoBanBtn) {
                // 将组件转换为 MoBanBtn 类型
                MoBanBtn mobanBtn = (MoBanBtn) jPanel.getComponents()[i];
                if (mobanBtn.getText()!=null&&!mobanBtn.getText().isEmpty()){
                    Color[] uicolors = uiType == 0  ? UIUtils.COLOR_BTNTEXT : uiType == 1?UIUtils.COLOR_ZHUJPANEL: UIUtils.COLOR_RED;
                    mobanBtn.setColors(uicolors);
                }
            }
        }
    }
    /**更改按钮组件的颜色*/
    public static void changeNtextColor(JPanel jPanel,int uiType) {
        for (int i = 0; i < jPanel.getComponents().length; i++) {
            // 检查是否是 MoBanBtn 类型
            if (jPanel.getComponents()[i] instanceof MoBanBtn) {
                // 将组件转换为 MoBanBtn 类型
                MoBanBtn mobanBtn = (MoBanBtn) jPanel.getComponents()[i];
                if (mobanBtn.getNtext()!=null&&!mobanBtn.getNtext().isEmpty()){
                    Color[] uicolors = uiType == 0  ? UIUtils.COLOR_BTNTEXT : uiType == 1?UIUtils.COLOR_W: UIUtils.COLOR_RED;
                    mobanBtn.setColors(uicolors);
                }
            }
        }
    }

    /**导航菜单专用*/
    public static void changBtuunFont(JPanel jPanel, int uiType) {
        for (int i = 0; i < jPanel.getComponents().length; i++) {
            // 检查是否是 MoBanBtn 类型
            if (jPanel.getComponents()[i] instanceof MoBanBtn) {
                // 将组件转换为 MoBanBtn 类型
                MoBanBtn mobanBtn = (MoBanBtn) jPanel.getComponents()[i];
                if (mobanBtn.getNtext()!=null&&!mobanBtn.getNtext().isEmpty()){
                    Font uicolors = uiType == 0  ? UIUtils.MSYH_HY14 : UIUtils.TEXT_HYJ18;
                    if (mobanBtn.getMenu()==1||mobanBtn.getMenu()==2){
                        mobanBtn.setFont(uicolors);
                    }
                }
            }
        }
    }

    public static Font changGrapFont(){
        return Util.SwitchUI == 0  ? UIUtils.MSYH_HY14 : UIUtils.TEXT_HYJ16;
    }
    public static Color changGrapColor(){
        return Util.SwitchUI == 0  ? UIUtils.COLOR_CCDDDDFF : Util.SwitchUI == 1? UIUtils.COLOR_White:UIUtils.COLOR_Wing1;
    }
    
    
    /**
     * 菜单导航栏
     */
    public static void MenuNavigationBar(Graphics g,int w1,int w2,int w3){
        Image image = Util.SwitchUI==0?Juitil.tz19.getImage():Util.SwitchUI==1?Juitil.tz339.getImage():Juitil.tz338.getImage();
        int x1 = Util.SwitchUI==0?18:Util.SwitchUI==1?40:18;
        int w = Util.SwitchUI==0?w1:Util.SwitchUI==1?w2:w3;
        g.drawImage(image, x1,  Util.SwitchUI==1?30:59, w,  Util.SwitchUI==1?20:Util.SwitchUI==0?2:5, null);
    }

    public static ImgZoom getImgZoom() {
        return imgZoom;
    }

    public static void setImgZoom(ImgZoom imgZoom) {
        Juitil.imgZoom = imgZoom;
    }


}
