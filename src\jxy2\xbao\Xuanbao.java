package jxy2.xbao;

import java.math.BigDecimal;

public class Xuanbao {
    private BigDecimal id;
    //玄宝名字
    private String baoname;
    //玄宝种族
    private String baorace;
    //玄宝类型
    private String baotype;
    //玄宝品质
    private String baoquality;
    //玄宝皮肤
    private String baoskin;
    //描述
    private String baodescribe;
    //玄宝技能
    private String baoskill;
    //玄宝印鸣技能一
    private String baoskillone;
    //玄宝印鸣技能二
    private String baoskilltwo;
    //玄宝基础技能
    private String baoskillthree;
    //层级属性值
    private String baoHav;
    //所属门派宣言
    private String baoraceinfo;
    //玄印配置
    private String xyConfig;
    //玄宝消耗
    private int baoconsume;
    //持续回合
    private int baoround;
    //==============sqldata===================
    // 角色ID
    private BigDecimal roleid;
    //玄印      id|id
    private String baomarkid;
    //玄宝印记
    private String baomarkname;
    //玄印激活
    private String baomarkactive;
    //玄印开启
    private String baomarkopen;
    //用于判断是否装备 1为装备
    private int equipment;
    //玄宝等级
    private BigDecimal baolvl;
    //玄宝当前进度经验
    private BigDecimal baoexe;


    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getBaoskillthree() {
        return baoskillthree;
    }

    public void setBaoskillthree(String baoskillthree) {
        this.baoskillthree = baoskillthree;
    }

    public String getBaoname() {
        return baoname;
    }

    public void setBaoname(String baoname) {
        this.baoname = baoname;
    }

    public String getBaorace() {
        return baorace;
    }

    public void setBaorace(String baorace) {
        this.baorace = baorace;
    }

    public String getBaotype() {
        return baotype;
    }

    public void setBaotype(String baotype) {
        this.baotype = baotype;
    }

    public String getBaoquality() {
        return baoquality;
    }

    public void setBaoquality(String baoquality) {
        this.baoquality = baoquality;
    }

    public String getBaoskill() {
        return baoskill;
    }

    public void setBaoskill(String baoskill) {
        this.baoskill = baoskill;
    }

    public String getBaoskillone() {
        return baoskillone;
    }

    public void setBaoskillone(String baoskillone) {
        this.baoskillone = baoskillone;
    }

    public String getBaoskilltwo() {
        return baoskilltwo;
    }

    public void setBaoskilltwo(String baoskilltwo) {
        this.baoskilltwo = baoskilltwo;
    }

    public BigDecimal getRoleid() {
        return roleid;
    }

    public void setRoleid(BigDecimal roleid) {
        this.roleid = roleid;
    }

    public String getBaomarkid() {
        return baomarkid;
    }

    public void setBaomarkid(String baomarkid) {
        this.baomarkid = baomarkid;
    }

    public String getBaomarkname() {
        return baomarkname;
    }

    public void setBaomarkname(String baomarkname) {
        this.baomarkname = baomarkname;
    }

    public String getBaomarkactive() {
        return baomarkactive;
    }

    public void setBaomarkactive(String baomarkactive) {
        this.baomarkactive = baomarkactive;
    }

    public String getBaomarkopen() {
        return baomarkopen;
    }

    public void setBaomarkopen(String baomarkopen) {
        this.baomarkopen = baomarkopen;
    }

    public int getEquipment() {
        return equipment;
    }

    public void setEquipment(int equipment) {
        this.equipment = equipment;
    }

    public BigDecimal getBaolvl() {
        return baolvl;
    }

    public void setBaolvl(BigDecimal baolvl) {
        this.baolvl = baolvl;
    }

    public BigDecimal getBaoexe() {
        return baoexe;
    }

    public void setBaoexe(BigDecimal baoexe) {
        this.baoexe = baoexe;
    }

    public String getBaoskin() {
        return baoskin;
    }

    public void setBaoskin(String baoskin) {
        this.baoskin = baoskin;
    }

    public String getBaodescribe() {
        return baodescribe;
    }

    public void setBaodescribe(String baodescribe) {
        this.baodescribe = baodescribe;
    }

    public String getBaoHav() {
        return baoHav;
    }

    public void setBaoHav(String baoHav) {
        this.baoHav = baoHav;
    }

    public String getBaoraceinfo() {
        return baoraceinfo;
    }

    public void setBaoraceinfo(String baoraceinfo) {
        this.baoraceinfo = baoraceinfo;
    }

    public int getBaoround() {
        return baoround;
    }

    public void setBaoround(int baoround) {
        this.baoround = baoround;
    }

    public int getBaoconsume() {
        return baoconsume;
    }

    public void setBaoconsume(int baoconsume) {
        this.baoconsume = baoconsume;
    }

    public String getXyConfig() {
        return xyConfig;
    }

    public void setXyConfig(String xyConfig) {
        this.xyConfig = xyConfig;
    }
}
