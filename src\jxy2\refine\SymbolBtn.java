package jxy2.refine;

import com.tool.btn.MoBanBtn;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class SymbolBtn extends MoBanBtn {
    public int typeBtn;//提示
    public SymbolJpanel symbolJpanel;
    public SymbolBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, SymbolJpanel symbolJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.symbolJpanel = symbolJpanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

        BtnMoni(typeBtn);
        symbolJpanel.setMinType(typeBtn);
        symbolJpanel.getWorkshopBtn().setText(typeBtn==0?"洗练":"升级");
        RefineFrame.getRefineFrame().getRefineJPanel().getRichLabel().setTextSize(Juitil.getPrompt(6,typeBtn),160);
        symbolJpanel.clear();
    }

    private void BtnMoni(int typeBtn){
        symbolJpanel.getBtnSymbol()[typeBtn].btnchange(2);
        for (int i = 0; i < symbolJpanel.getBtnSymbol().length; i++) {
            if (i!=typeBtn){
                symbolJpanel.getBtnSymbol()[i].btnchange(0);
            }
        }
    }

}
