package jxy2.supet;

import com.tool.tcpimg.UIUtils;
import jxy2.UniversalModel;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.RoleSummoning;
import org.come.llandudno.AtlasListCell;
import org.come.until.SrcollPanelUI;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName:召唤兽排序窗口
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/4/13 7:34
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class SipetSortJPanel extends JPanel {
    private JScrollPane jScrollPane; // 召唤兽列表滚动条
    public JList<UniversalModel> listpet;// 召唤兽列表
    public DefaultListModel<UniversalModel> listModel;// 放置召唤兽的对象
    private Map<Integer, UniversalModel> mapPetModelPanel = new HashMap<>();
    private SipetSortBtn btntop,// 向上图标
            btnbottom,// 向下图标
            btntopset,// 置顶图标
            btnbottomset;// 置底图标
    private JLabel labOpen;
    private JLabel labLocking;
    private static int warNum = -1;
    public static int idx = -1;
    public SipetSortJPanel() {
        this.setPreferredSize(new Dimension(268, 447));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,146,268);
        listModel = new DefaultListModel<>();
        // 召唤兽列表
        listpet = new JList<UniversalModel>(listModel) {
            {
                setOpaque(false);
            } // instance initializer
        };
        listpet.setSelectionBackground(new Color(33, 42, 52));
        listpet.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        listpet.setModel(listModel);
        listpet.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        listpet.setCellRenderer(new AtlasListCell(idx, Color.GRAY, 160, 35, 8));
        listpet.addMouseListener(new SipetSortMouse(listpet, this));
        listpet.addMouseMotionListener(new SipetSortMouse(listpet, this));
        // 召唤兽列表滚动条
        jScrollPane = new JScrollPane(listpet);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setOpaque(false);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setBounds(25, 67, 194, 310);
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        add(jScrollPane);

        labOpen = new JLabel();
        labOpen.setBounds(31, 261, 15, 15);
        add(labOpen);
        labLocking = new JLabel();
        labLocking.setBounds(31, 282, 15, 15);
        add(labLocking);


        // 向上图标
        btntop = new SipetSortBtn(ImgConstants.tz81, 1, 1,this);
        add(btntop);
        btnbottom = new SipetSortBtn(ImgConstants.tz82, 1, 2,this);
        add(btnbottom);
        btntopset = new SipetSortBtn(ImgConstants.tz80,1,"顶",3);
        add(btntopset);
        btnbottomset = new SipetSortBtn(ImgConstants.tz80,1, "底",4);
        add(btnbottomset);
        btntop.setBounds(228, 84+50, 18, 18);
        btnbottom.setBounds(228, 113+50, 18, 18);
        btntopset.setBounds(228, 167+50, 18, 18);
        btnbottomset.setBounds(228, 196+50, 18, 18);

        init();
    }

    //刷新数据
    public void showStar() {
        List<RoleSummoning> pets = UserMessUntil.getPetListTable();
        if (mapPetModelPanel.size()!=pets.size()){
            listpet.removeAll();
            listModel.removeAllElements();
            mapPetModelPanel.clear();
            init();
        }else {
            for (int i = 0; i < pets.size(); i++) {
                RoleSummoning pet = pets.get(i);
                UniversalModel universalModel = mapPetModelPanel.get(i);
                universalModel.PetData(i, pet,
                        157,8,
                        35,
                        true,
                        true,
                        40,1);
            }
        }
        int index = Math.max(listModel.size() *35 , 311);
        listpet.setPreferredSize(new Dimension(194, index));
    }

    /**初始化召唤兽列表数据*/
    public void init() {
        // 清除之前的面板对象
        for (int i = 0; i < UserMessUntil.getPetListTable().size(); i++) {
            RoleSummoning pet = UserMessUntil.getPetListTable().get(i);
            if (pet != null) {
                UniversalModel universalModel = new UniversalModel(194,310);
                universalModel.PetData(i,pet,
                        157,
                        2,
                        35,
                        true,
                        true,
                        40,0);
                universalModel.setBounds(0,i*35,194,310);
                listpet.add(universalModel);
                listModel.add(i,universalModel);
                mapPetModelPanel.put(i, universalModel);
            }
        }
        listpet.setPreferredSize(new Dimension(194, UserMessUntil.getPetListTable().size() * 35));
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g, getWidth(), getHeight(), "设置排序");
        Juitil.ImngBack(g,Juitil.tz21, 20, 39, 200, 340, 1);
        Juitil.Textdrawing(g, "召唤兽", 98,61, UIUtils.COLOR_CCDDDDFF,UIUtils.MSYH_HY15);

    }

    public JScrollPane getjScrollPane() {
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public SipetSortBtn getBtntop() {
        return btntop;
    }

    public void setBtntop(SipetSortBtn btntop) {
        this.btntop = btntop;
    }

    public SipetSortBtn getBtnbottom() {
        return btnbottom;
    }

    public void setBtnbottom(SipetSortBtn btnbottom) {
        this.btnbottom = btnbottom;
    }

    public SipetSortBtn getBtntopset() {
        return btntopset;
    }

    public void setBtntopset(SipetSortBtn btntopset) {
        this.btntopset = btntopset;
    }

    public SipetSortBtn getBtnbottomset() {
        return btnbottomset;
    }

    public void setBtnbottomset(SipetSortBtn btnbottomset) {
        this.btnbottomset = btnbottomset;
    }

    public JLabel getLabOpen() {
        return labOpen;
    }

    public void setLabOpen(JLabel labOpen) {
        this.labOpen = labOpen;
    }

    public JLabel getLabLocking() {
        return labLocking;
    }

    public void setLabLocking(JLabel labLocking) {
        this.labLocking = labLocking;
    }

    public static int getWarNum() {
        return warNum;
    }

    public static void setWarNum(int warNum) {
        SipetSortJPanel.warNum = warNum;
    }

    public static int getIdx() {
        return idx;
    }

    public static void setIdx(int idx) {
        SipetSortJPanel.idx = idx;
    }

    public JList<UniversalModel> getListpet() {
        return listpet;
    }

    public void setListpet(JList<UniversalModel> listpet) {
        this.listpet = listpet;
    }

    public DefaultListModel<UniversalModel> getListModel() {
        return listModel;
    }

    public void setListModel(DefaultListModel<UniversalModel> listModel) {
        this.listModel = listModel;
    }

    public Map<Integer, UniversalModel> getMapPetModelPanel() {
        return mapPetModelPanel;
    }

    public void setMapPetModelPanel(Map<Integer, UniversalModel> mapPetModelPanel) {
        this.mapPetModelPanel = mapPetModelPanel;
    }
}

