package jxy2.xsl;


import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.read.biff.BiffException;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import java.io.File;
import java.io.IOException;

public class ReadExelTool {
    public static String[][] result;
    public synchronized static String[][] getResult( String path ){
        try {
            Workbook workbook = Workbook.getWorkbook(new File(path));
            Sheet sheet = workbook.getSheet(0);
            int col = sheet.getColumns();
            int row = sheet.getRows();
            result = new String[row][col];
            Cell cell;
            for (int i = 0; i < row; i++) {
                for (int j = 0; j < col; j++) {
                    try {
                        cell = sheet.getCell(j, i);
                        result[i][j] = cell.getContents();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            return result;
        } catch (BiffException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        } catch (IOException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        return null;
    }

    // 修改并保存Excel文件
    public static void writeExcel(String path, String[][] data) {
        try {
            WorkbookSettings wbSettings = new WorkbookSettings();
            wbSettings.setEncoding("ISO-8859-1");

            // 创建一个可写的工作簿
            WritableWorkbook workbook = Workbook.createWorkbook(new File(path), wbSettings);
            WritableSheet sheet = workbook.createSheet("Sheet1", 0);
            // 写入数据到工作表
            for (int i = 0; i < data.length; i++) {
                for (int j = 0; j < data[i].length; j++) {
                    Label label = new Label(j, i, data[i][j]);
                    sheet.addCell(label);
                }
            }

            // 写入工作簿到文件
            workbook.write();
            workbook.close();
        } catch (IOException | WriteException e) {
            e.printStackTrace();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
