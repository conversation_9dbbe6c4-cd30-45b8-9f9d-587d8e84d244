package org.come.login;

import com.tool.image.ImageMixDeal;
import com.tool.tcpimg.NoticeBox;
import com.tool.tcpimg.SystemBox;
import com.tool.tcpimg.TipBox;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.FrameDialogSync;
import jxy2.chatv.ChatFrame;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.PathPoint;
import org.come.mouslisten.*;
import org.come.test.Main;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.ScrenceUntil;
import org.come.until.Util;
import org.come.util.*;
import org.come.view.View;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseListener;
import java.util.ArrayList;
import java.util.List;

public class GameView extends View {
    /** 系统提示对话 */
    public List<TipBox> SystemPrompt=new ArrayList<>();
    private List<NoticeBox> SystemNotice=new ArrayList<>();
    private JLabel mouse;
    private SystemBox systemBox;
    private JDesktopPane desktopPane;
    private JLabel labnow,labgroups,labbangs,labworld,labnotice,dangqian,labbackimg;

    // FPS监控器
    private FPSMonitor fpsMonitor;
    private boolean isGameViewActive = true;
    private boolean renderingEnabled = true;

    // 调试用：记录实际sleep时间
    private long lastSleepTime = 0;

    // 渲染循环Timer
    private javax.swing.Timer renderTimer;

    // 最小化恢复状态
    private boolean isRecoveringFromMinimize = false;
    private long minimizeRecoveryStartTime = 0;

    // 调试计数器
    private static int paintCallCount = 0;
    private static long lastPaintTime = System.currentTimeMillis();

    // 高性能渲染器
    private HighPerformanceRenderer highPerformanceRenderer;
    // 消息发送范围监听
    private ChoseDangQianMounslisten choseDangQianMounslisten;
    private ChoseNowMouslisten choseNowMouslisten; // 当前
    private ChoseGroupsMouslisten choseGroupsMouslisten; // 队伍
    private ChoseBangsMouslisten choseBangsMouslisten; // 帮派
    private ChoseWorldMouslisten choseWorldMouslisten; // 世界
    private ChoseNoticeMouslisten choseNoticeMouslisten; // 傳音
    private int index;
    public GameView(LoginJpanel loginJpanel) {
        // TODO Auto-generated constructor stub
        this.setBounds(0, 0, 1024, 720);
        frameDialogSync = new FrameDialogSync();
        frameDialogSync.getDialog().setVisible(false);
        // 初始化传音标签
        labnotice = createLabel(null, " 传音", null);
        choseNoticeMouslisten = new ChoseNoticeMouslisten(this, 20);
        labnotice.addMouseListener(choseNoticeMouslisten);
        
        // 初始化世界标签
        labworld = createLabel(null, " 世界", null);
        choseWorldMouslisten = new ChoseWorldMouslisten(this);
        labworld.addMouseListener(choseWorldMouslisten);
        
        // 初始化帮派标签
        labbangs = createLabel(null, " 帮派", null);
        choseBangsMouslisten = new ChoseBangsMouslisten(this);
        labbangs.addMouseListener(choseBangsMouslisten);
        
        // 初始化队伍标签
        labgroups = createLabel(null, " 队伍", null);
        choseGroupsMouslisten = new ChoseGroupsMouslisten(this);
        labgroups.addMouseListener(choseGroupsMouslisten);
        
        // 初始化当前标签
        labnow = createLabel(null, " 当前", null);
        choseNowMouslisten = new ChoseNowMouslisten(this);
        labnow.addMouseListener(choseNowMouslisten);

        // 初始化当前标签
        labbackimg = new JLabel(){
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Juitil.ImngBack(g, Util.SwitchUI==1?Juitil.she_0004:Juitil.red_0018, 0, 0, 43, 130, 1);

            }
        };
        labbackimg.setVisible(false);



        // 初始化"当前"按钮
        dangqian = TeststateJpanel.GJpanelText(Color.WHITE, UIUtils.NEWTX_HY17B);
        dangqian.setForeground(Color.WHITE);
        dangqian.setText("当前");
        dangqian.setVerticalTextPosition(SwingConstants.CENTER);
        dangqian.setHorizontalTextPosition(SwingConstants.CENTER);
        dangqian.setFont(UIUtils.TEXT_FONT);
        choseDangQianMounslisten = new ChoseDangQianMounslisten(this);
        dangqian.addMouseListener(choseDangQianMounslisten);
        try {
            mouse = new JLabel() {
                @Override
                protected void paintComponent(Graphics g) {
                    // TODO Auto-generated method stub
                    super.paintComponent(g);
                    // 公告显示
                    if (isNotice) {
                        int chatY=ScrenceUntil.Screen_y-500;
                        g.translate(0,chatY);
                        for (int i = SystemNotice.size()-1;i>=0;i--) {
                            NoticeBox chatPanel = SystemNotice.get(i);
                            if (chatPanel.IsTime()) {
                                g.translate(0,-chatPanel.getHeight());
                                chatY -= chatPanel.getHeight();
                                chatPanel.paint(g);
                            } else {
                                SystemNotice.remove(i);
                            }
                        }
                        g.translate(0,-chatY);
                        if (SystemNotice.isEmpty()) {
                            isNotice=false;
                        }
                    }
                    if (isBox) {
                        int chatY= ScrenceUntil.Screen_y/2;
                        g.translate(0,chatY);
                        for (int i = SystemPrompt.size()-1; i >=0; i--) {
                            TipBox chatPanel = SystemPrompt.get(i);
                            if (chatPanel.IsTime()) {
                                g.translate(0,-chatPanel.getHeight());
                                chatY -= chatPanel.getHeight();
                                chatPanel.paint(g);
                            } else {
                                SystemPrompt.remove(i);
                            }
                        }
                        g.translate(0,-chatY);
                        if (SystemPrompt.size()==0) {
                            isBox=false;
                        }
                    }
                    if (systemBox!=null) {
                        systemBox.draw(g);
                    }
                    PathPoint point = Main.frame.getLoginJpanel().mousepath();
                    try {
                        if (GoodsMouslisten.replacepath != -1) {
                            ImageIcon icon = GoodsListFromServerUntil.xbaoWdfile(GoodsListFromServerUntil.getGoodslist()[GoodsMouslisten.replace].getSkin(),60,60);
                            g.drawImage(icon.getImage(), point.getX() - 20,point.getY() - 20, 49,49, this);
                        }
                    } catch (Exception e) {
                        // TODO: handle exception
                        GoodsMouslisten.replacepath = -1;
                    }

                }
            };
            mouse.setBounds(0, 0, ScrenceUntil.Screen_x+ ScrenceUntil.ChatFram_X, ScrenceUntil.Screen_y);
            this.add(mouse);
            desktopPane = new JDesktopPane();
            this.add(desktopPane);
            this.add(ChatFrame.getChatFrame());
            this.add(ZhuFrame.getzhuframe());
            this.setBackground(Color.BLACK);
            // 3. 添加聊天标签
            this.add(labbackimg);
            this.add(labnotice);
            this.add(labworld);
            this.add(labbangs);
            this.add(labgroups);
            this.add(labnow);
            this.add(dangqian);

            // 初始化FPS监控器
            fpsMonitor = new FPSMonitor();
            fpsMonitor.setBounds(96, 42, 66, 22);
            this.add(fpsMonitor);

            // 启动渲染诊断
            RenderingDiagnostics.getInstance().startMonitoring();

            // 启动性能分析
            PerformanceProfiler.getInstance().startProfiling();

            // 初始化显示器自适应FPS
            initializeAdaptiveFPS();

            // 设置FPS管理器的GameView引用
            org.come.util.FPSSettings.getInstance().setGameView(this);

            // 4. 设置Z轴顺序，确保标签显示在最上层
            this.setComponentZOrder(mouse, 0);
            this.setComponentZOrder(labbackimg, 0);
            this.setComponentZOrder(dangqian, 0);
            this.setComponentZOrder(labnow, 0);
            this.setComponentZOrder(labgroups, 0);
            this.setComponentZOrder(labbangs, 0);
            this.setComponentZOrder(labworld, 0);
            this.setComponentZOrder(labnotice, 0);

            this.setComponentZOrder(fpsMonitor, 0); // FPS监控器置于最顶层




        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public void updateButtonImages(int uitype){
        labnotice.setBounds(6, ScrenceUntil.Screen_y - 58, 34, 18);
        labworld.setBounds(6, ScrenceUntil.Screen_y - 84, 34, 18);
        labbangs.setBounds(6, ScrenceUntil.Screen_y - 110, 34, 18);
        labgroups.setBounds(6, ScrenceUntil.Screen_y - 136, 34, 18);
        labnow.setBounds(6, ScrenceUntil.Screen_y -  162, 34, 18);
        dangqian.setBounds(uitype==1?5:2, ScrenceUntil.Screen_y - 24, 43, 18);
        labbackimg.setBounds(2, ScrenceUntil.Screen_y - 163, 43, 130);
    }

    /**
     * 设置组件层级*/
    public void ComponentZOrder(Component c, int index) {
        this.setComponentZOrder(c, index);
    }



    public static FrameDialogSync frameDialogSync;
    public static boolean isAlpha = false;

    // 动态FPS设置
    private int currentTargetFPS = 60; // 当前目标FPS
    private long currentFrameTime = 16666666L; // 当前帧时间（纳秒）

    static final int DEFAULT_CHA = 1000000;
    public static float alpha = 1.0f;
    // 绘制图像前的时间戳
    long now = System.nanoTime();
    long now2 = 0;
    // 每次绘制图像耗时（毫秒）
    long total = 0;
    @Override
    protected void paintComponent(Graphics g) {
        // TODO Auto-generated method stub
        super.paintComponent(g);


        // 调试：记录paintComponent调用
        paintCallCount++;
        long currentTime = System.currentTimeMillis();
        if (paintCallCount % 30 == 0) { // 每30次输出一次
            long timeDiff = currentTime - lastPaintTime;
            double fps = 30000.0 / timeDiff; // 30次调用的FPS
            lastPaintTime = currentTime;
        }

        // 记录渲染调用
        RenderingDiagnostics.getInstance().recordPaintCall();

        // 记录帧开始时间
        long frameStartTime = System.currentTimeMillis();

        Graphics2D g2d = (Graphics2D) g;
        if (alpha < 1) {
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
        }
        // 判断是否处于战斗状态
        if (FightingMixDeal.State == HandleState.USUAL) {
            ImageMixDeal.Drawing(g,total);
        } else {
            ImageMixDeal.move2(total);
            ImageMixDeal.userimg.setTime(total);
            FightingMixDeal.Drawing(g,total);
        }

        try {
            // 彻底简化：移除所有FPS控制逻辑，让Timer完全控制
            if (alpha < 1) {
                alpha += 0.03f;
            }

            // 允许更高FPS，但保持移动速度稳定
            now2 = System.nanoTime();

            // 计算实际帧时间
            long actualFrameTime = now2 - now;
            long frameTimeMs = actualFrameTime / DEFAULT_CHA;

            // 为了保持移动速度，使用固定的时间步长
            // 但允许更高的渲染频率
            if (frameTimeMs < 5) {
                // 如果帧时间太短，使用最小值避免移动过快
                total = 8; // 固定8ms，保持移动速度稳定
            } else if (frameTimeMs > 50) {
                // 如果帧时间太长，限制最大值
                total = 50;
            } else {
                total = frameTimeMs;
            }

            now = now2;

            // 记录帧完成，更新FPS监控器
            if (fpsMonitor != null && isGameViewActive) {
                fpsMonitor.recordFrame();
            }

            // 记录性能数据
            long renderTime = System.nanoTime() - frameStartTime * 1000000;
            PerformanceProfiler.getInstance().recordRender(renderTime, lastSleepTime);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            // 即使出现异常也要记录帧（可能是渲染问题）
            if (fpsMonitor != null) {
                fpsMonitor.recordFrame();
            }
        }

        // 移除paintComponent中的Timer，使用startRenderTimer()的持续Timer
        // paintComponent只负责渲染，不控制下一帧的触发
    }
    public JLabel getMouse() {
        return mouse;
    }
    public void setMouse(JLabel mouse) {
        this.mouse = mouse;
    }
    /**添加对话框*/
    boolean isBox=false;
    public void addPrompt(String text){
        if (text!=null) {
            SystemPrompt.add(new TipBox(text));
            isBox=true;
        }
    }
    /**添加对话框*/
    boolean isNotice=false;
    public void addNotice(String text){
        if (text!=null) {
            SystemNotice.add(new NoticeBox(text));
            isNotice=true;
        }
    }
    /**添加滚动字体 8广告滚动   9系统滚动*/
    public void addBox(String text,int type,String skin){
        if (systemBox==null) {
            systemBox=new SystemBox(text, skin!=null?skin:"HF1");
        }else {
            systemBox.addText(text);
        }
    }

    public List<TipBox> getSystemPrompt() {
        return SystemPrompt;
    }

    public void setSystemPrompt(List<TipBox> systemPrompt) {
        SystemPrompt = systemPrompt;
    }

    public JLabel getLabnow() {
        return labnow;
    }

    public void setLabnow(JLabel labnow) {
        this.labnow = labnow;
    }

    public JLabel getLabgroups() {
        return labgroups;
    }

    public void setLabgroups(JLabel labgroups) {
        this.labgroups = labgroups;
    }

    public JLabel getLabbangs() {
        return labbangs;
    }

    public void setLabbangs(JLabel labbangs) {
        this.labbangs = labbangs;
    }

    public JLabel getLabworld() {
        return labworld;
    }

    public void setLabworld(JLabel labworld) {
        this.labworld = labworld;
    }

    public JLabel getLabnotice() {
        return labnotice;
    }

    public void setLabnotice(JLabel labnotice) {
        this.labnotice = labnotice;
    }

    public JLabel getDangqian() {
        return dangqian;
    }

    public void setDangqian(JLabel dangqian) {
        this.dangqian = dangqian;
    }

    public JLabel getLabbackimg() {
        return labbackimg;
    }

    public void setLabbackimg(JLabel labbackimg) {
        this.labbackimg = labbackimg;
    }

    // 辅助方法：创建标签
    private JLabel createLabel(ImageIcon icon, String text, MouseListener listener) {
        JLabel label = new JLabel();
        label.setIcon(icon);
        label.setForeground(Color.WHITE);
        label.setBackground(UIUtils.Color_BACK);
        label.setText(text);
        label.setHorizontalTextPosition(SwingConstants.CENTER);
        label.setVerticalTextPosition(SwingConstants.CENTER);
        label.setFont(UIUtils.TEXT_FONT);
        label.setOpaque(true);  // 设置背景透明
        label.setVisible(false);  // 确保可见
        if (listener != null) {
            label.addMouseListener(listener);
        }
        return label;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    /**
     * 设置游戏视图活跃状态
     * 当用户离开游戏画面时调用此方法
     */
    public void setGameViewActive(boolean active) {
        this.isGameViewActive = active;
        this.renderingEnabled = active;

        if (fpsMonitor != null) {
            fpsMonitor.setGameActive(active);
        }

        if (!active) {
//            System.out.println("[GameView] 游戏视图变为非活跃状态，渲染暂停");
            // 暂停时不停止Timer，只是不响应Timer事件
        } else {


            // 标记可能需要恢复检测
            long currentTime = System.currentTimeMillis();
            if (minimizeRecoveryStartTime == 0 || (currentTime - minimizeRecoveryStartTime) > 1000) {
                // 如果距离上次恢复超过1秒，可能是新的最小化恢复
                minimizeRecoveryStartTime = currentTime;

            }

            // 恢复时只触发一次渲染，后续由paintComponent自驱动
            SwingUtilities.invokeLater(() -> {
                if (isGameViewActive && renderingEnabled) {
                    repaint();
                }
            });
        }
    }

    /**
     * 获取当前FPS
     */
    public int getCurrentFPS() {
        return fpsMonitor != null ? fpsMonitor.getCurrentFPS() : 0;
    }

    /**
     * 强制刷新渲染（用于恢复卡死状态）
     */
    public void forceRefresh() {

        // 重置渲染状态 - 这是关键！
        synchronized(this) {
            now = System.nanoTime(); // 重置当前时间戳
            now2 = now; // 重置上一帧时间戳
            total = 0; // 重置帧时间
            alpha = 1.0f; // 直接设为完全不透明，避免渐入效果影响性能
        }

        SwingUtilities.invokeLater(() -> {
            // 确保渲染已启用
            renderingEnabled = true;

            // 强制重绘整个组件树
            this.invalidate();
            this.revalidate();

            // 只调用一次repaint
            if (isGameViewActive && renderingEnabled) {
                this.repaint();
            }

            if (fpsMonitor != null) {
                fpsMonitor.recordFrame();
            }
        });

        // 移除延迟刷新，避免多重repaint调用
    }

    /**
     * 递归刷新所有子组件（修复递归bug）
     */
    private void refreshAllComponents(java.awt.Container container) {
        for (java.awt.Component component : container.getComponents()) {
            // 只刷新子组件，不刷新自己
            if (component != this) {
                component.repaint();
                if (component instanceof java.awt.Container) {
                    refreshAllComponents((java.awt.Container) component); // 修复：应该是component不是container
                }
            }
        }
    }

    /**
     * 初始化自适应FPS
     */
    private void initializeAdaptiveFPS() {
        DisplayInfo displayInfo = DisplayInfo.getInstance();

        // 详细调试显示器检测
        System.out.println("[显示器调试] 检测到的刷新率: " + displayInfo.getDetectedRefreshRate() + "Hz");
        System.out.println("[显示器调试] 垂直同步: " + displayInfo.isVsyncEnabled());
        System.out.println("[显示器调试] 计算的目标FPS: " + displayInfo.getTargetFPS());

        currentTargetFPS = displayInfo.getTargetFPS();
        currentFrameTime = displayInfo.getFrameTimeNanos();

        System.out.println("[GameView] 自适应FPS已启用");
        System.out.println("[GameView] " + displayInfo.getDisplaySummary());
        System.out.println("[GameView] 帧时间: " + (currentFrameTime/1000000.0) + "ms");

        // 如果检测到的不是144Hz，手动设置
        if (displayInfo.getDetectedRefreshRate() != 144) {
            System.out.println("[显示器调试] 警告：未检测到144Hz，手动设置");
            currentTargetFPS = 144;
            currentFrameTime = (long) ((1000.0 / 144) * 1000000);
        }

        // 暂时禁用独立渲染器，让LoginJpanel统一控制避免闪烁
        // startHighPerformanceRenderer();

        // 更新FPS监控器显示
        if (fpsMonitor != null) {
            // 可以在这里更新FPS监控器的目标FPS显示
        }
    }

    /**
     * 动态调整FPS
     */
    public void adjustFPS(int newTargetFPS) {
        if (newTargetFPS > 0 && newTargetFPS <= 300) {
            currentTargetFPS = newTargetFPS;
            currentFrameTime = (long) ((1000.0 / newTargetFPS) * 1000000);

            System.out.println("[GameView] FPS调整为: " + newTargetFPS +
                " (帧时间: " + (currentFrameTime/1000000.0) + "ms)");

            // 重启渲染Timer以应用新的FPS设置
            restartRenderTimer();

            // 强制退出恢复模式，立即应用新FPS
            isRecoveringFromMinimize = false;
        }
    }

    /**
     * 获取当前目标FPS
     */
    public int getCurrentTargetFPS() {
        return currentTargetFPS;
    }

    /**
     * 检测最小化恢复状态
     */
    private void checkMinimizeRecovery() {
        long currentTime = System.currentTimeMillis();

        // 检测是否刚从非活跃状态恢复
        if (!isRecoveringFromMinimize && isGameViewActive) {
            // 检查时间戳是否异常（可能刚从最小化恢复）
            if (total < -currentFrameTime * 5 || total > currentFrameTime * 5) {
                System.out.println("[GameView] 检测到最小化恢复，启动快速恢复模式");
                isRecoveringFromMinimize = true;
                minimizeRecoveryStartTime = currentTime;

                // 重置时间戳
                now = System.nanoTime();
                now2 = now;
                total = 0;
            }
        }

        // 检查恢复模式是否应该结束
        if (isRecoveringFromMinimize) {
            long recoveryDuration = currentTime - minimizeRecoveryStartTime;
            if (recoveryDuration > 2000) { // 2秒后结束恢复模式
                System.out.println("[GameView] 最小化恢复模式结束");
                isRecoveringFromMinimize = false;
            }
        }
    }

    /**
     * 计算帧延迟时间
     */
    private int calculateFrameDelay() {
        // 直接根据目标FPS计算，不依赖currentFrameTime
        int targetDelayMs = Math.max(1, 1000 / currentTargetFPS);

        if (isRecoveringFromMinimize) {
            // 恢复模式：使用更短的延迟，快速恢复到目标FPS
            int recoveryDelayMs = Math.max(1, targetDelayMs / 2);

            System.out.println("[GameView] 恢复模式 - 目标FPS:" + currentTargetFPS +
                ", 目标延迟:" + targetDelayMs + "ms, 恢复延迟:" + recoveryDelayMs + "ms");
            return recoveryDelayMs;
        } else {
            // 正常模式：使用标准延迟
            System.out.println("[GameView] 正常模式 - 目标FPS:" + currentTargetFPS +
                ", 延迟:" + targetDelayMs + "ms, currentFrameTime:" + (currentFrameTime/1000000) + "ms");
            return targetDelayMs;
        }
    }

    /**
     * 强制恢复到目标FPS（用于手动修复卡顿）
     */
    public void forceRecoverTargetFPS() {


        // 重新获取显示器信息，确保FPS设置正确
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        displayInfo.refresh();

        currentTargetFPS = displayInfo.getTargetFPS();
        currentFrameTime = displayInfo.getFrameTimeNanos();



        // 重置所有时间戳
        now = System.nanoTime();
        now2 = now;
        total = 0;

        // 退出恢复模式
        isRecoveringFromMinimize = false;

        // 确保渲染状态正确
        renderingEnabled = true;
        isGameViewActive = true;

        // 立即触发渲染
        SwingUtilities.invokeLater(() -> {
            if (isGameViewActive && renderingEnabled) {
                repaint();
            }
        });

        System.out.println("[GameView] 强制恢复完成，下一帧延迟应该是: " + (1000/currentTargetFPS) + "ms");
    }

    /**
     * 直接设置FPS（用于测试）
     */
    public void setDirectFPS(int fps) {
        System.out.println("[GameView] 直接设置FPS为: " + fps);

        currentTargetFPS = fps;
        currentFrameTime = (long) ((1000.0 / fps) * 1000000);

        // 重置时间戳
        now = System.nanoTime();
        now2 = now;
        total = 0;

        // 退出恢复模式
        isRecoveringFromMinimize = false;

        System.out.println("[GameView] 设置完成 - FPS:" + fps + ", 延迟:" + (1000/fps) + "ms");

        // 重启Timer以应用新设置
        restartRenderTimer();

        // 立即触发渲染测试
        SwingUtilities.invokeLater(() -> {
            if (isGameViewActive && renderingEnabled) {
                repaint();
            }
        });
    }

    /**
     * 强制设置144FPS（绕过显示器检测）
     */
    public void force144FPS() {
        System.out.println("[GameView] 强制设置144FPS，绕过显示器检测");
        setDirectFPS(144);
    }

    /**
     * 启动高性能渲染器
     */
    private void startHighPerformanceRenderer() {
        System.out.println("[GameView] 启动高性能渲染器，目标144FPS");

        highPerformanceRenderer = HighPerformanceRenderer.getInstance();
        highPerformanceRenderer.setGameView(this);
        highPerformanceRenderer.setTargetFPS(currentTargetFPS);
        highPerformanceRenderer.start();
    }

    /**
     * 停止高性能渲染器
     */
    private void stopHighPerformanceRenderer() {
        if (highPerformanceRenderer != null) {
            highPerformanceRenderer.stop();
        }
    }

    /**
     * 真正的144FPS（使用专用线程）
     */
    public void enableTrueHighFPS() {
        System.out.println("[GameView] 启用真正的高FPS渲染");

        // 停止旧的Timer系统
        stopRenderTimer();

        // 启动高性能渲染器
        if (highPerformanceRenderer == null) {
            startHighPerformanceRenderer();
        } else {
            highPerformanceRenderer.setTargetFPS(144);
        }
    }

    /**
     * 启动Timer驱动的渲染循环
     */
    private void startRenderTimer() {
        // 停止现有Timer
        if (renderTimer != null) {
            renderTimer.stop();
        }

        // 详细调试Timer间隔计算
        System.out.println("[Timer调试] currentTargetFPS: " + currentTargetFPS);
        System.out.println("[Timer调试] currentFrameTime: " + currentFrameTime + " 纳秒");
        System.out.println("[Timer调试] currentFrameTime/1000000: " + (currentFrameTime/1000000.0) + " 毫秒");

        // 直接用简单公式计算，不依赖currentFrameTime
        int timerInterval = Math.max(1, 1000 / currentTargetFPS);

        System.out.println("[Timer调试] 计算公式: 1000 / " + currentTargetFPS + " = " + timerInterval + "ms");
        System.out.println("[GameView] 启动渲染Timer，间隔: " + timerInterval + "ms");

        renderTimer = new javax.swing.Timer(timerInterval, e -> {
            if (isGameViewActive && renderingEnabled) {
                repaint();
            }
        });

        renderTimer.start();
    }

    /**
     * 停止渲染Timer
     */
    private void stopRenderTimer() {
        if (renderTimer != null) {
            renderTimer.stop();
            System.out.println("[GameView] 渲染Timer已停止");
        }
    }

    /**
     * 重启渲染Timer（用于FPS调整后）
     */
    private void restartRenderTimer() {
        stopRenderTimer();
        startRenderTimer();
    }


    public ChoseNoticeMouslisten getChoseNoticeMouslisten() {
        return choseNoticeMouslisten;
    }

    public void setChoseNoticeMouslisten(ChoseNoticeMouslisten choseNoticeMouslisten) {
        this.choseNoticeMouslisten = choseNoticeMouslisten;
    }

    public ChoseWorldMouslisten getChoseWorldMouslisten() {
        return choseWorldMouslisten;
    }

    public void setChoseWorldMouslisten(ChoseWorldMouslisten choseWorldMouslisten) {
        this.choseWorldMouslisten = choseWorldMouslisten;
    }

    public ChoseBangsMouslisten getChoseBangsMouslisten() {
        return choseBangsMouslisten;
    }

    public void setChoseBangsMouslisten(ChoseBangsMouslisten choseBangsMouslisten) {
        this.choseBangsMouslisten = choseBangsMouslisten;
    }

    public ChoseGroupsMouslisten getChoseGroupsMouslisten() {
        return choseGroupsMouslisten;
    }

    public void setChoseGroupsMouslisten(ChoseGroupsMouslisten choseGroupsMouslisten) {
        this.choseGroupsMouslisten = choseGroupsMouslisten;
    }

    public ChoseNowMouslisten getChoseNowMouslisten() {
        return choseNowMouslisten;
    }

    public void setChoseNowMouslisten(ChoseNowMouslisten choseNowMouslisten) {
        this.choseNowMouslisten = choseNowMouslisten;
    }

    public ChoseDangQianMounslisten getChoseDangQianMounslisten() {
        return choseDangQianMounslisten;
    }

    public void setChoseDangQianMounslisten(ChoseDangQianMounslisten choseDangQianMounslisten) {
        this.choseDangQianMounslisten = choseDangQianMounslisten;
    }

    public static FrameDialogSync getFrameDialogSync() {
        return frameDialogSync;
    }

    public static void setFrameDialogSync(FrameDialogSync frameDialogSync) {
        GameView.frameDialogSync = frameDialogSync;
    }
}
