package jxy2.synt;

import jxy2.jutnil.ImgConstants;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class ExchangSoulMouse implements MouseListener {
    public int index ;
    public ExchangModel exchangModel;
    public ExchangSoulMouse(int index,ExchangModel exchangModel) {
        this.index=index;
        this.exchangModel=exchangModel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {}

    @Override
    public void mousePressed(MouseEvent e) {

        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
        ExchangSoulJPanel exchangSoulJPanel = ExchangSoulFrame.getExchangSoulFrame().getExchangSoulJPanel();
        if (e.getButton() == MouseEvent.BUTTON3) {
            Goodstable goodstable = exchangSoulJPanel.getExchangModelMap().get(index).goodstable;
            if (synthesisJPanl.getGoodstables()[0]!=null&&goodstable.getRgid().compareTo(synthesisJPanl.getGoodstables()[0].getRgid())==0){
                ClearExisting(synthesisJPanl, 0);
            }
            if (synthesisJPanl.getGoodstables()[1]!=null&&goodstable.getRgid().compareTo(synthesisJPanl.getGoodstables()[1].getRgid())==0){
                ClearExisting(synthesisJPanl, 1);
            }
        }else {
            for (Integer key : exchangSoulJPanel.getExchangModelMap().keySet()) {
                ExchangModel exchangModel = exchangSoulJPanel.getExchangModelMap().get(key);
                exchangModel.frames.setBorder(BorderFactory.createEmptyBorder());
                JLabel selectedSkiimg = exchangSoulJPanel.getExchangModelMap().get(index).frames;
                if (selectedSkiimg != null) {
                    selectedSkiimg.setBorder(BorderFactory.createLineBorder(Color.orange));
                }
            }
            Goodstable goodstable = exchangSoulJPanel.getExchangModelMap().get(index).goodstable;
            if (synthesisJPanl.getLabSkillName()[0].getText().equals("未选择")) {
                if (synthesisJPanl.getGoodstables()[1]!=null&&synthesisJPanl.getGoodstables()[1].getRgid().compareTo(goodstable.getRgid())==0)return;
                SetupGoods(synthesisJPanl,goodstable,0);
            } else {
                //判断2次点击的物品是否相同
                if (synthesisJPanl.getGoodstables()[0].getRgid().compareTo(goodstable.getRgid())==0)return;
                SetupGoods(synthesisJPanl,goodstable,1);
            }
        }
    }

    /**
     * 设置商品信息。
     * 该方法用于在合成面板上初始化和设置商品信息，根据给定的商品表和总数来调整显示和配置。
     *
     * @param synthesisJPanl 合成面板对象，用于显示和操作商品信息。
     * @param goodstable 商品表对象，包含所有可用商品的数据。
     * @param sum 总商品数量，用于确定商品的显示和计算。
     */
    public static void SetupGoods(SynthesisJPanl synthesisJPanl,Goodstable goodstable,int sum){
        String[] id = goodstable.getValue().split("\\|");
        StringBuffer buffer =new StringBuffer();
        for (String s : id) {
            Skill skill = UserMessUntil.getSkillId(s);
            if (buffer.length()!=0) {buffer.append("#r");}
            buffer.append("["+skill.getSkillname()+"]");
            synthesisJPanl.getLabSkillName()[sum].setText(buffer.toString());
            int x = getX(synthesisJPanl, "["+skill.getSkillname()+"]",sum==0?287:389);
            synthesisJPanl.getLabSkillName()[sum].setBounds(x, 308, 100, 40 );
            synthesisJPanl.getLabPlus()[sum].setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 50, 50));
            synthesisJPanl.getLabPlus()[sum].setBounds(320+(sum*100), 258, 50, 50);
            synthesisJPanl.getGoodstables()[sum] = goodstable;
        }
    }

    /**
     * 清除特定索引位置的已存在元素。
     * 此方法用于在给定的合成面板中清除指定索引位置的元素，为新增元素腾出空间。
     *
     * @param synthesisJPanl 合成面板对象，是操作的目标对象。
     * @param indexs 需要清除元素的索引位置，可以是单个索引或多个索引的数组。
     */
    private static void ClearExisting(SynthesisJPanl synthesisJPanl, int indexs) {
        synthesisJPanl.getLabPlus()[indexs].setIcon(null);
        synthesisJPanl.getLabPlus()[indexs].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz55,"defaut.wdf"));
        synthesisJPanl.getLabPlus()[indexs].setBounds(330+(indexs*100),268, 28, 28);
        synthesisJPanl.getLabSkillName()[indexs].setText("未选择");
        synthesisJPanl.getLabSkillName()[indexs].setBounds(323+(indexs*100), 308, 100, 40);
        synthesisJPanl.getGoodstables()[indexs] =null;
    }

    /**
     * 计算文本剧中显示
     * @param synthesisJPanl
     * @param name
     * @param xs
     * @return
     */
    private static int getX(SynthesisJPanl synthesisJPanl, String name,int xs) {
        // 计算文本宽度
        Graphics g = synthesisJPanl.getLabSkillName()[1].getGraphics();
        int textWidth = org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(name);
        // 获取容器宽度
        int containerWidth = synthesisJPanl.getLabSkillName()[1].getWidth();
        // 计算x坐标，使文本居中
        // 设置y坐标和高度
        return (containerWidth - textWidth) / 2 + xs;
    }

    @Override
    public void mouseReleased(MouseEvent e) {}

    @Override
    public void mouseEntered(MouseEvent e) {}

    @Override
    public void mouseExited(MouseEvent e) {}
}
