package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.refine.CollectionJpanel;
import org.come.Frame.*;
import org.come.Jpanel.*;
import org.come.bean.JadeorGoodstableBean;
import org.come.bean.LoginResult;
import org.come.bean.PalacePkBean;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class WorkshopBtn extends MoBanBtn {

    private int caozuo;
    private PalacePKJpanel palacePKJpanel;
    private AlreadyRecordedJpanel alreadyRecordedJpanel;
    private CollectionJpanel collectionJanel;

    public WorkshopBtn(String iconpath, int type, String text, int caozuo) {
        super(iconpath,type);
        this.caozuo = caozuo;
        setText(text);
        setFont(UIUtils.TEXT_FONT1);
        setForeground(Color.orange);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public WorkshopBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public WorkshopBtn(String iconpath, int type, String text, int caozuo, AlreadyRecordedJpanel alreadyRecordedJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        setText(text);
        setFont(UIUtils.TEXT_FONT1);
        setForeground(Color.orange);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.alreadyRecordedJpanel = alreadyRecordedJpanel;
    }


    public WorkshopBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            AlreadyRecordedJpanel alreadyRecordedJpanel) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.alreadyRecordedJpanel = alreadyRecordedJpanel;
    }
    public WorkshopBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            CollectionJpanel collectionJanel) {
        super(iconpath, type,0, colors,"");
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.collectionJanel = collectionJanel;
    }

    public WorkshopBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            PalacePKJpanel palacePKJpanel) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.palacePKJpanel = palacePKJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {

        switch (caozuo) {
            case 1: // 炼器
                break;
            case 2: // 合成
                heCheng();
                break;
            case 3: // 洗炼
                xiLian();
                break;
            case 4: // 套装升级
                tzShengJi();
                break;
            case 5: // 玉符升级
                yfShengJi();
                break;
            case 6: // 转移
                zhuanYi();
                break;
            case 7: // 兑换
                duiHuan();
                break;
            case 8: // 收录（收录玉符面板）
                shouLu();
                break;
            case 9: // 删除
                shanChu();
                break;
            case 10: // 收录（已有玉符面板）
                CollectionJadeJframe.getCollectionJadeJframe().getJadeJpanel().clearInterface();
                FormsManagement.showForm(64);
                break;
            case 11: // 获得按钮（已有玉符面板）
                ExchangeValueJframe.getExchangeValueJframe().getValueJpanel().clearInterface();
                FormsManagement.showForm(63);
                break;
            case 12: // 申请挑战
                applyChallenge();
                break;
            case 13: // 生成玉符
                getJade();
                break;
            case 14: // PK取消
                if (palacePKJpanel.getWinnerType() == 2) {
                    palacePKJpanel.getPalacePkBean().setType(2);
                    String senmes = Agreement.getAgreement().bookofchalgAgreement(
                            GsonUtil.getGsonUtil().getgson().toJson(palacePKJpanel.getPalacePkBean()));
                    SendMessageUntil.toServer(senmes);
                }
                FormsManagement.HideForm(66);
                break;
            case 15: // 已有玉符激活按钮
                if (collectionJanel.getLabAct().getText().equals("激活")) {
                    PartJade jade = collectionJanel.getGoodstableBean().getPartJade();
                    if (jade == null || (jade != null && jade.getJade1() == 1)) {
                        ZhuFrame.getZhuJpanel().addPrompt("请选择你要激活的玉符..");
                        return;
                    }
                    // 判断金钱和灵修值是否足够
                    if (new BigDecimal(500000).compareTo(RoleData.getRoleData().getLoginResult().getGold()) > 0) {
                        ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                        return;
                    }
                    if (200 > RoleData.getRoleData().getLoginResult().getScoretype("灵修值").longValue()) {
                        ZhuFrame.getZhuJpanel().addPrompt("消耗了200点灵修值    扣除了50W金币..??灵修值不足，快去获取吧..");
                        return;
                    }
                    // 判断这个部件是否可收录
                    if (RoleData.getRoleData().getPackRecord().isCollect(jade.getSuitid(), jade.getPartId()) != null) {
                        ZhuFrame.getZhuJpanel().addPrompt("没有可激活的部件..");
                        return;
                    }
                    SuitOperBean operBean = new SuitOperBean();
                    jade.setJade1(0);
                    operBean.setJade(jade);
                    operBean.setType(8);
                    // 发送消息给服务器
                    String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
                    SendMessageUntil.toServer(senmes);
                    // 扣金币
                    RoleData.getRoleData().getLoginResult()
                            .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(new BigDecimal(500000)));
                    // 扣灵修值
                    RoleData.getRoleData().getLoginResult()
                            .setScore(UserData.Splice(RoleData.getRoleData().getLoginResult().getScore(), "灵修值=200", 3));
                    ZhuFrame.getZhuJpanel().addPrompt("消耗了200点灵修值    扣除了50W金币..");
                    collectionJanel.getLabAct().setBtn(-1);
                    collectionJanel.getLabAct().setForeground(Color.GRAY);
                    collectionJanel.getLabAct().setIcon(CutButtonImage.getWdfPng(ImgConstants.tz172, "defaut.wdf"));
                }

                break;
        }
    }

    /**
     * 兑换灵修值
     */
    public void duiHuan() {
        JadeorGoodstableBean bean = ExchangeValueJframe.getExchangeValueJframe().getValueJpanel()
                .getJadeorGoodstableBean();
        // ExchangeValueJframe.getExchangeValueJframe().getValueJpanel();
        String v = ExchangeValueJframe.getExchangeValueJframe().getValueJpanel().getTextNum().getText();
        int num = (v != null && !v.equals("")) ? Integer.parseInt(v) : 0;
        if (bean == null || (bean != null && bean.getType() == 0)) {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择你要兑换的玉符或玄玉。");
            return;
        }
        if (num <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("请输入你要兑换的玉符或玄玉的数量。");
            return;
        }
        int val = 0;// 可以得到的灵修值
        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(7);
        if (bean.getType() == 1) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade1() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade1(num);
                operBean.setJade(jade);
                val = num;
                // //删除玉符
                // jade.deleteJade(bean.getType(), num);
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 2) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade2() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade2(num);
                operBean.setJade(jade);
                val = num;
                // //删除玉符
                // jade.deleteJade(bean.getType(), num);
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 3) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade3() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade3(num);
                operBean.setJade(jade);
                val = num * 2;
                // //删除玉符
                // jade.deleteJade(bean.getType(), num);
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 4) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade4() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade4(num);
                operBean.setJade(jade);
                val = num * 2;
                // //删除玉符
                // jade.deleteJade(bean.getType(), num);
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 5) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade5() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade5(num);
                operBean.setJade(jade);
                val = num * 3;
                // //删除玉符
                // jade.deleteJade(bean.getType(), num);
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 6) {
            if (bean.getGoodstable() != null && bean.getGoodstable().getUsetime() >= num) {
                Goodstable goodstable = bean.getGoodstable();
                if (goodstable.getGoodlock() == 1) {
                    ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                    return;
                }
                if (GoodsListFromServerUntil.isExist(goodstable)) {
                    return;
                }
                PartJade jade = new PartJade(-1, -1);
                jade.setJade1(num);
                List<BigDecimal> goods = new ArrayList<>();
                goods.add(bean.getGoodstable().getRgid());
                operBean.setGoods(goods);
                operBean.setJade(jade);
                val = num * 3;
                bean.getGoodstable().setUsetime(bean.getGoodstable().getUsetime() - num);
                if (bean.getGoodstable().getUsetime() <= 0) {
                    // 将这个物品删除
                    GoodsListFromServerUntil.Deletebiaoid(bean.getGoodstable().getRgid());
                }
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的九天玄玉数量不足。");
                return;
            }
        }
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        ZhuFrame.getZhuJpanel().addPrompt("获得了 " + val + " 点灵修值..");
        // 清空界面
        ExchangeValueJframe.getExchangeValueJframe().getValueJpanel().clearInterface();
    }

    /**
     * 收录
     */
    public void shouLu() {
        JadeorGoodstableBean bean = CollectionJadeJframe.getCollectionJadeJframe().getJadeJpanel().getGoodstableBean();
        if (bean == null || (bean != null && bean.getPartJade() == null)) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要收录的玉符..");
            return;
        }
        // //这个套装所收录的部件数量
        int num = AccessSuitMsgUntil.getCollNum(StorageJadeJpanel2.partJade.getSuitid());
        // 所需灵修值
        BigDecimal sxlxz = new BigDecimal(50);
        // 所需金钱
        BigDecimal money = new BigDecimal((num + 1) * 1000000);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (sxlxz.compareTo(loginResult.getScoretype("灵修值")) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("灵修值点数不足，快去获取灵修值吧..");
            return;
        }
        if (money.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        // 判断这个部件是否可收录
        if (RoleData.getRoleData().getPackRecord()
                .isCollect(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId()) != null) {
            ZhuFrame.getZhuJpanel().addPrompt("已到达收录上限..");
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
        jade.setJade(bean.getType(), 1);
        operBean.setJade(jade);
        operBean.setType(8);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);

        // 扣除灵修值
        loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + sxlxz, 3));
        // 扣除金钱
        loginResult.setGold(loginResult.getGold().subtract(money));
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + sxlxz + "点灵修值       扣除了" + money + "金币..");
        // 清空界面
        CollectionJadeJframe.getCollectionJadeJframe().getJadeJpanel().clearInterface();
    }

    /**
     * 删除 (已收录的套装)
     */
    public void shanChu() {
        if (alreadyRecordedJpanel.getListSuit() != null && alreadyRecordedJpanel.getListSuit().getSelectedIndex() != -1) {
            String name = alreadyRecordedJpanel.getListSuit().getSelectedValue();
            SuitOperBean operBean = new SuitOperBean();
            PartJade jade = new PartJade(AccessSuitMsgUntil.returnSuitID(name), 0);
            operBean.setJade(jade);
            operBean.setType(8);
            // 发送消息给服务器
            String senmes = null;
            try {
                senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            } catch (Exception e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            SendMessageUntil.toServer(senmes);
        } else {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要删除的套装..");
        }
    }

    /**
     * 生成玉符
     */
    public void getJade() {
        PartJade jade = alreadyRecordedJpanel.getGoodstableBean().getPartJade();
        if (jade == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要生成的玉符..");
            return;
        }
        if (jade.getJade1() != 1) {
            ZhuFrame.getZhuJpanel().addPrompt("你还没收录过此玉符..");
            return;
        }
        if (alreadyRecordedJpanel.getTextField().getText() == null
                || alreadyRecordedJpanel.getTextField().getText().equals("")) {
            ZhuFrame.getZhuJpanel().addPrompt("请输入你要生成的玉符数量..");
            return;
        }
        long val = Long.parseLong(alreadyRecordedJpanel.getTextField().getText());
        if (val <= 0) {
            return;
        }
        BigDecimal money = new BigDecimal(1000000 * val);
        BigDecimal sxlxz = new BigDecimal(10 * val);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (money.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        if (sxlxz.compareTo(loginResult.getScoretype("灵修值")) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("灵修值不足，快去获取吧..");
            return;
        }
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(1, (int) val);
        SuitOperBean operBean = new SuitOperBean();
        operBean.setJade(jade2);
        operBean.setType(9);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 扣金币
        loginResult.setGold(loginResult.getGold().subtract(money));
        // 扣灵修值
        loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + sxlxz, 3));
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + sxlxz + "点灵修值    扣除了" + money + "金币..");
        // 清空界面
        alreadyRecordedJpanel.clearInterface();
    }

    /** 合成 */
    public void heCheng() {
        // 获得合成需要消耗的金币
        BigDecimal big = AccessSuitMsgUntil.returnMoney(SynthesisJpanel.getGoodstableBean(), 1);
        if (big == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请准备要合成的装备和玉符。");
            return;
        }
        PartJade jade = SynthesisJpanel.getGoodstableBean().getPartJade();
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (big.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        Goodstable good = SynthesisJpanel.getGoodstableBean().getGoodstable();
        if (good.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(good)) {
            return;
        }
        // 判断 玉符和对应装备类型
        int type = Goodtype.EquipmentType(SynthesisJpanel.getGoodstableBean().getGoodstable().getType());
        if (jade.getPartId() == 11) {
            if (type != 10) {
                ZhuFrame.getZhuJpanel().addPrompt("装备类型和玉符不一致..");
                return;
            }
        } else if (type != jade.getPartId()) {
            ZhuFrame.getZhuJpanel().addPrompt("装备类型和玉符不一致..");
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        List<BigDecimal> goods = new ArrayList<>();
        goods.add(SynthesisJpanel.getGoodstableBean().getGoodstable().getRgid());
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(SynthesisJpanel.getGoodstableBean().getType(), 1);
        operBean.setType(0);
        operBean.setGoods(goods);
        operBean.setJade(jade2);

        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 删除玉符
        jade.deleteJade(SynthesisJpanel.getGoodstableBean().getType(), 1);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        SynthesisJpanel.clearInterface();
        ZhuFrame.getZhuJpanel().addPrompt(
                "消耗了一个" + AccessSuitMsgUntil.returnJadeName(SynthesisJpanel.getGoodstableBean().getType()) + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了100W金币..");
    }

    /** 洗炼 */
    public void xiLian() {

        if (WashJpanel.getGoodstableBean().getGoodstable() == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要洗炼的套装..");
            return;
        }
        Goodstable good = WashJpanel.getGoodstableBean().getGoodstable();
        if (good.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(good)) {
            return;
        }
        SuitBaptizeJpanel suitBaptizeJpanel = SuitBaptizeJframe.getSuitBaptizeJframe().getBaptizeJpanel();
        suitBaptizeJpanel.getLabtz().setIcon(GoodsListFromServerUntil.imgpathAdaptive(good.getSkin(),49,49));
        // 还原界面
        for (int i = 0; i < 4; i++) {
            suitBaptizeJpanel.getOldAttr()[i].setText("");
            suitBaptizeJpanel.getNewAttr()[i].setText("");
        }

        // 将原有的属性放上去
        List<String> attr = AccessSuitMsgUntil.getSuitAttr(AccessSuitMsgUntil.getExtra(good.getValue(), "套装属性"));
        if (attr != null) {
            int index = attr.size() >= 4 ? 4 : attr.size();
            for (int i = 0; i < index; i++) {
                suitBaptizeJpanel.getOldAttr()[i].setText(attr.get(i));
            }
        }
        // 还原界面
        suitBaptizeJpanel.getBaptizeBtn2().setBtn(-1);
        suitBaptizeJpanel.getBaptizeBtn3().setBtn(-1);
        suitBaptizeJpanel.getBaptizeBtn1().setText("开始洗炼");
        // 打开洗炼的小面板
        FormsManagement.upgradForm(74);
    }

    /** 套装升级 */
    public void tzShengJi() {
        // 获得套装升级需要消耗的金币
        BigDecimal big = AccessSuitMsgUntil.returnMoney(UpgradeJpanel.getGoodstableBean(), 2);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (big == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请准备要升级的装备和玉符。");
            return;
        }
        if (big.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        PartJade jade = UpgradeJpanel.getGoodstableBean().getPartJade();
        Goodstable goodstable = UpgradeJpanel.getGoodstableBean().getGoodstable();
        if (goodstable.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(goodstable)) {
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        List<BigDecimal> goods = new ArrayList<>();
        goods.add(goodstable.getRgid());
        operBean.setType(3);
        operBean.setGoods(goods);
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(UpgradeJpanel.getGoodstableBean().getType(), 1);
        operBean.setJade(jade2);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 改变该物品的品质
        // 替换属性
        String Extras = AccessSuitMsgUntil.getExtra(goodstable.getValue(), "套装属性");
        String[] ss = goodstable.getValue().split("\\|");
        String newEx = AccessSuitMsgUntil.returnnewEx(1, Extras);
        ss[0] = "套装品质=" + AccessSuitMsgUntil.returnnewEx(3, Extras);
        String value = BaptizeBtn.newExtra(ss, 3, newEx);
        goodstable.setValue(value);
        UpgradeJpanel.setGoodstable(goodstable);
        UpgradeJpanel.getLabtz2().setIcon(
                new ImageIcon(new ImageIcon("img/item/" + goodstable.getSkin() + ".png").getImage().getScaledInstance(
                        50, 50, 10)));
        // 删除玉符
        jade.deleteJade(UpgradeJpanel.getGoodstableBean().getType(), 1);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        UpgradeJpanel.clearInterface();
        ZhuFrame.getZhuJpanel().addPrompt(
                "消耗了一个" + AccessSuitMsgUntil.returnJadeName(UpgradeJpanel.getGoodstableBean().getType()) + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了1000W金币..");
    }

    /**
     * 玉符升级
     */
    public void yfShengJi() {
        if (JadeUpJpanel.getGoodstableBean().getPartJade() == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要升级的玉符..");
            return;
        }
        BigDecimal big = AccessSuitMsgUntil.returnJadeMoney(JadeUpJpanel.getGoodstableBean().getType());
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (loginResult.getGold().compareTo(big) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        PartJade jade = JadeUpJpanel.getGoodstableBean().getPartJade();
        int num = AccessSuitMsgUntil.returnJadeNum(JadeUpJpanel.getGoodstableBean().getType());// 升级所消耗的玉符数量
        // 判断所需的玉符是否足够
        if (num > jade.getJade(JadeUpJpanel.getGoodstableBean().getType())) {
            ZhuFrame.getZhuJpanel().addPrompt("你所需的玉符数量不足..");
            return;
        }

        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(4);
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(JadeUpJpanel.getGoodstableBean().getType(), num);
        operBean.setJade(jade2);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 删除玉符
        jade.deleteJade(JadeUpJpanel.getGoodstableBean().getType(), num);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        JadeUpJpanel.clearInterface();
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + num + "个" + AccessSuitMsgUntil.returnJadeName(JadeUpJpanel.getGoodstableBean().getType())  + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了500W金币..");
    }

    /**
     * 转移
     */
    public void zhuanYi() {
        if (TransferJpanel.getGoodstableBean().getGoodstable() == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要拆解的套装..");
            return;
        }
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (TransferJpanel.getWorkshopBtn().getText().equals("拆 解")) {
            if (loginResult.getGold().compareTo(new BigDecimal(100000)) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                return;
            }
            Goodstable goodstable = TransferJpanel.getGoodstableBean().getGoodstable();
            if (goodstable.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (GoodsListFromServerUntil.isExist(goodstable)) {
                return;
            }
            // 扣金币
            loginResult.setGold(loginResult.getGold().subtract(new BigDecimal(100000)));
            List<BigDecimal> goods = new ArrayList<>();
            goods.add(goodstable.getRgid());
            SuitOperBean operBean = new SuitOperBean();
            operBean.setGoods(goods);
            operBean.setType(5);
            // 发送消息给服务器
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            // 清空界面
            TransferJpanel.clearInterface();
            ZhuFrame.getZhuJpanel().addPrompt("消耗了10W金币..");

        } else if (TransferJpanel.getWorkshopBtn().getText().equals("转 移")) {
            if (TransferJpanel.getGoodstable() == null) {
                ZhuFrame.getZhuJpanel().addPrompt("请选择你要转移属性的装备..");
                return;
            }
            if (loginResult.getGold().compareTo(new BigDecimal(10000000)) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                return;
            }
            long value = AccessSuitMsgUntil.getSxlxz(TransferJpanel.getGoodstableBean().getGoodstable().getValue());
            if (loginResult.getScoretype("灵修值").longValue() < value) {
                ZhuFrame.getZhuJpanel().addPrompt("灵修值不足..");
                return;
            }
            Goodstable good1 = TransferJpanel.getGoodstableBean().getGoodstable();
            Goodstable good2 = TransferJpanel.getGoodstable();

            if (good1.getGoodlock() == 1 || good2.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (GoodsListFromServerUntil.isExist(good1) || GoodsListFromServerUntil.isExist(good2)) {
                return;
            }
            // 判断装备类型是否对应
            if (Goodtype.EquipmentType(good1.getType()) != Goodtype.EquipmentType(good2.getType())) {
                ZhuFrame.getZhuJpanel().addPrompt("装备类型不一致..");
                return;
            }
            // 扣金币
            loginResult.setGold(loginResult.getGold().subtract(new BigDecimal(10000000)));
            // 扣灵修值
            loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + value, 3));
            List<BigDecimal> goods = new ArrayList<>();
            goods.add(good1.getRgid());
            goods.add(good2.getRgid());
            SuitOperBean operBean = new SuitOperBean();
            operBean.setGoods(goods);
            operBean.setType(6);
            // 发送消息给服务器
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            // 清空界面
            TransferJpanel.clearInterface();
            ZhuFrame.getZhuJpanel().addPrompt("消耗了1000W金币..");
            ZhuFrame.getZhuJpanel().addPrompt("消耗了" + value + "点灵修值..");
        }
    }

    /**
     * 申请挑战（皇宫PK）
     */
    public void applyChallenge() {
        // 获取要挑战者的姓名，以及下注的金钱和仙玉数量
        String name = PalacePKJframe.getPalacePKJframe().getPkJpanel().getTextName().getText();
        // String text2 =
        // PalacePKJframe.getPalacePKJframe().getPkJpanel().getTextGold().getText();
        BigDecimal dahuabi = new BigDecimal(palacePKJpanel.getFundString()[0]);
        BigDecimal xianyu = new BigDecimal(palacePKJpanel.getFundString()[1]);
        BigDecimal exp = new BigDecimal(palacePKJpanel.getFundString()[2]);
        String sendStr = palacePKJpanel.getSendBelTextArea().getText();

        // 计算
        BigDecimal gold = new BigDecimal(0);
        BigDecimal xianyuMax = new BigDecimal(0);
        gold = gold.add(dahuabi);
        xianyuMax = xianyuMax.add(xianyu);
        // 判断最低下注金额
        if (gold.compareTo(new BigDecimal(10000000)) < 0 && xianyuMax.compareTo(new BigDecimal(1000)) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("游戏币最低下注金额： 1千万游戏币。或者仙玉最低下注金额： 1000仙玉。");
            return;
        }
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();

        // 判断游戏币是否充足支付下注金额
        if (loginResult.getGold().compareTo(gold) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足以支付投入金额");
            return;
        }
        // 判断仙玉是否充足支付下注仙玉
        if (loginResult.getCodecard().compareTo(xianyuMax) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("仙玉不足以支付投入仙玉");
            return;
        }
        // 判断经验是否充足支付下注经验
        if (loginResult.getExperience().compareTo(exp) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("经验不足以支付投入经验");
            return;
        }
        // 判断游戏币是否充足支付全服公告
        if (palacePKJpanel.isChallengeBool()) {
            gold = gold.add(new BigDecimal(20000000));
            if (loginResult.getGold().compareTo(gold) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足以支付全服公告金额");
                return;
            }
        }
        // 判断游戏币是否充足支付战书费用
        gold = gold.add(new BigDecimal(2000000));
        if (loginResult.getGold().compareTo(gold) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足以战书费用");
            return;
        }
        // 判断仙玉是否充足支付传音铃铛
        if (palacePKJpanel.isSendBellBool()) {
            xianyuMax = xianyuMax.add(new BigDecimal(100));
            if (loginResult.getCodecard().compareTo(xianyuMax) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("仙玉不足以支付铃铛金额");
                return;
            }
        }
        // 皇宫PK发起战书
        if (palacePKJpanel.getWinnerType() == 0) {
            if (name.equals("")) {
                ZhuFrame.getZhuJpanel().addPrompt2("请输入您要挑战者的名称。");
                return;
            }
            if (name != null && name.equals(loginResult.getRolename())) {
                ZhuFrame.getZhuJpanel().addPrompt2("您不能挑战自己！！");
                return;
            }
        } else if (palacePKJpanel.getWinnerType() == 1) {
            if (gold.compareTo(new BigDecimal(1000000000)) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("最高下注金额： 10亿游戏币。");
                return;
            }
            if (xianyu.compareTo(new BigDecimal(100000)) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("仙玉最高下注金额： 10万仙玉。");
                return;
            }
        } else if (palacePKJpanel.getWinnerType() == 2) {

        } else if (palacePKJpanel.getWinnerType() == 3) {

        }
        // private int type;//类型 0正常发起皇宫PK 1同意 2是拒绝 11是擂台赛我要下战书 12是我要应战 13我要观战
        PalacePkBean palacePkBean = new PalacePkBean();
        if (palacePKJpanel.getWinnerType() == 0) {
            palacePkBean.setUsername(name);
            palacePkBean.setType(0);
        } else if (palacePKJpanel.getWinnerType() == 1) {

            palacePkBean.setType(11);
            palacePkBean.setNtype(Integer.parseInt(NPCJfram.getNpcJfram().getNpcjpanel().getNpctype()));
        } else if (palacePKJpanel.getWinnerType() == 2) {
            palacePkBean.setType(1);
            palacePkBean.setPId(palacePKJpanel.getPalacePkBean().getPId());
        } else if (palacePKJpanel.getWinnerType() == 3) {
            palacePkBean.setNtype(Integer.parseInt(NPCJfram.getNpcJfram().getNpcjpanel().getNpctype()));
            palacePkBean.setType(1);
            palacePkBean.setPId(palacePKJpanel.getPalacePkBean().getPId());
        }
        palacePkBean.setGold(dahuabi);
        palacePkBean.setXianyu(xianyu);
        palacePkBean.setExp(exp);

        palacePkBean.setSendStr(palacePKJpanel.isSendBellBool() ? sendStr : null);
        palacePkBean.setChoices(palacePkBean.getChoices() + ((palacePKJpanel.isChallengeBool() ? 1 : 0) << 0));
        palacePkBean.setChoices(palacePkBean.getChoices() + ((palacePKJpanel.isSendBellBool() ? 1 : 0) << 0));
        // 向服务器请求报名
        String senmes = null;
        try {
            senmes = Agreement.getAgreement().bookofchalgAgreement(
                    GsonUtil.getGsonUtil().getgson().toJson(palacePkBean));
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        SendMessageUntil.toServer(senmes);
    }
}
