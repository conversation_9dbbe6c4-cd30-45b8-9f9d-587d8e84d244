package jxy2.eqcon;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;

public class GoodsFrameListener extends ComponentAdapter {
    private JInternalFrame goodsFrame;
    private JInternalFrame eqFrame;

    public GoodsFrameListener(JInternalFrame goodsFrame, JInternalFrame eqFrame) {
        this.goodsFrame = goodsFrame;
        this.eqFrame = eqFrame;
    }

    @Override
    public void componentMoved(ComponentEvent e) {
        updateEqFramePosition();
    }

    @Override
    public void componentResized(ComponentEvent e) {
        updateEqFramePosition();
    }

    private void updateEqFramePosition() {
        Point location = goodsFrame.getLocation();
        Dimension size = goodsFrame.getSize();
        int x = (int) (location.getX() + size.getWidth());
        int y = (int) location.getY();
        eqFrame.setLocation(x-55, y);
    }
}
