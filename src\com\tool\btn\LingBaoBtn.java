package com.tool.btn;

import java.awt.event.MouseEvent;

import org.come.Jpanel.LingbaoJpanel;
import org.come.mouslisten.LingFaFanYeMouslisten;
import org.come.until.CutButtonImage;

/**
 * 灵宝按钮
 * 
 * <AUTHOR>
 * 
 */
public class LingBaoBtn extends MoBanBtn {

    /** 灵宝装备1 灵宝属性2 */
    private int caozuo;
    private LingbaoJpanel lingbaoJpanel;

    public LingBaoBtn(String iconpath, int type, int caozuo, LingbaoJpanel lingbaoJpanel) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        this.lingbaoJpanel = lingbaoJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        try {
            switch (caozuo) {
            case 1:
                lingbaoJpanel.getBtnEquipment().setIcons(CutButtonImage.cuts("inkImg/button/B82.png"));
                lingbaoJpanel.getBtnAttribute().setIcons(CutButtonImage.cuts("inkImg/button/B81.png"));
                LingFaFanYeMouslisten.shijian = true;
                lingbaoJpanel.getLingbaoCardJpanel().getCar().show(lingbaoJpanel.getLingbaoCardJpanel(), "l1");
                // RoleLingFa.choseuse
                break;
            case 2:
                lingbaoJpanel.getBtnEquipment().setIcons(CutButtonImage.cuts("inkImg/button/B83.png"));
                lingbaoJpanel.getBtnAttribute().setIcons(CutButtonImage.cuts("inkImg/button/B80.png"));

                LingFaFanYeMouslisten.shijian = false;
                lingbaoJpanel.getLingbaoCardJpanel().getCar().show(lingbaoJpanel.getLingbaoCardJpanel(), "l2");
                break;

            default:
                break;
            }
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }

}
