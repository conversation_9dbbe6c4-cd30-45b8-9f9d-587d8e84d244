package jxy2.supet;

import com.tool.btn.FightingBtn;
import com.tool.btn.MoBanBtn;
import com.tool.imagemonitor.FightingMonitor;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.Fightingimage;
import come.tool.Fighting.TypeState;
import come.tool.JDialog.TiShiUtil;
import come.tool.handle.HandleState;
import jxy2.UniversalModel;
import jxy2.jutnil.Juitil;
import jxy2.roleRelated.PetSwitchFrame;
import jxy2.roleRelated.PetSwitchJPanel;
import jxy2.wsyl.ChosePetLxMouslisten;
import org.come.Frame.OptionsJframe;
import org.come.Frame.PetEquipmentJframe;
import org.come.Frame.PetSkillsJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.entity.RoleSummoning;
import org.come.llandudno.AtlasListCell;
import org.come.mouslisten.ChosePetSkillsMouslisten;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class SipetBtn extends MoBanBtn {
    public UniversalModel universalModel;
    public SipetJPanel sipetJPanel;
    public int index;

    public SipetBtn(String iconpath, int type, Color[] colors, Font font, UniversalModel universalModel, int index, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        setFont(font);
        this.index = index;
        this.universalModel = universalModel;
    }

    public SipetBtn(String iconpath, int type, int index, String labelName,
                             SipetJPanel sipetJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.index = index;
        this.sipetJPanel = sipetJPanel;
    }

    public SipetBtn(String iconpath, int type, String text,int index,String prowpt) {
        // TODO Auto-generated constructor stub???
        super(iconpath, type, 0,UIUtils.COLOR_BTNTEXT, prowpt);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.index=index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        SipetJPanel sipet = SipetFrame.getSipetFrame().getSipetJPanel();
        if (index!=-1){
            switch (index){
                case 1:
                     if (FightingMixDeal.State == HandleState.USUAL) {
                       if(Util.isCanBuyOrno()){
                        return;
                    }
                    if (UserMessUntil.getChosePetMes()==null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("没有选中的召唤兽");
                        return;
                    }
                    OptionsJframe.getOptionsJframe().getOptionsJpanel().
                            showBox(TiShiUtil.Release, UserMessUntil.getChosePetMes(),"#W确定要将召唤兽:#G"+UserMessUntil.getChosePetMes().getSummoningname()+"#W放生吗?");
                }
                    break;
                case 2:
                    if (FightingMixDeal.State == HandleState.USUAL){
                        SipetUtil.changname();
                    }
                    break;
                case 3://驯养
                    if (FightingMixDeal.State == HandleState.USUAL){
                        SipetUtil.xunyang();
                    }
                    break;
                case 4://参战
                    if (FightingMixDeal.State == HandleState.USUAL){
                        SipetUtil.canzhan();
                    }
                    break;
                case 5://休息
                    if (FightingMixDeal.State == HandleState.USUAL){
                        SipetUtil.xiuxi();
                    }
                    break;
                case 6://确认加点
                    if (FightingMixDeal.State == HandleState.USUAL){
                        SipetUtil.jiadian();
                    }
                    break;
                case 7://切属性
                    PetSwitchJPanel petSwitchJPanel = PetSwitchFrame.getPetSwitchFrame().getPetSwitchJPanel();
                    petSwitchJPanel.initPetInfo(UserMessUntil.getChosePetMes());
                    Util.StopFrame(128);
                    break;
                case 8://载书
                    break;
                case 9://物品
                    if (FightingMixDeal.State == HandleState.USUAL) {
                        if (UserMessUntil.getChosePetMes() == null) {
                            FrameMessageChangeJpanel.addtext(6, "请选择你要查看抗性的召唤兽！", null, null);
                        }else {
                            PetEquipmentJframe.getPetEquipmentJframe().getEquipmentJpanel().showPet(UserMessUntil.getChosePetMes());
                            Util.StopFrame(67);
                        }
                    }
                    break;
                case 10://炼妖
                    if (FightingMixDeal.State == HandleState.USUAL) {
                        SipetUtil.lianyao();
                    }
                    break;
               case 11://灵犀
                   Util.StopFrame(123);
                   ChosePetLxMouslisten.refreshPetSkills();
                   break;
               case 12://抗性
                   if (FightingMixDeal.State == HandleState.USUAL) {
                       SipetUtil.petZhanshi();
                   }
                   break;
                   case 13://技能
                       if (FightingMixDeal.State == HandleState.USUAL) {
                           if (!FormsManagement.getframe(18).isVisible()) {
                               RoleSummoning roleSummoning= sipetJPanel.OBTAIN_THE_SELECTED_SUMMONED_BEAST(sipetJPanel.sipetModelJPanelJList.getSelectedIndex());
                               UserMessUntil.setChosePetMes(roleSummoning);
                               PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().IniData();
                               ChosePetSkillsMouslisten.refreshPetSkills();
                               FormsManagement.showForm(18);
                               Music.addyinxiao("开关窗口.mp3");// 打开面板

                           } else {
                               PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().setPetskillID("");
                               FormsManagement.HideForm(18);
                               Music.addyinxiao("关闭窗口.mp3");
                           }
                       }

                       break;
                case 14:
                    // 刷新面板
                    SipetSortFrame.getSipetSortFrame().getSipetSortJPanel().showStar();
                    Util.StopFrame(146);
                    break;
                case 15://召唤
                    if (FightingMixDeal.State == HandleState.HANDLE_PLAYER) {
                        Fightingimage fightingimage = FightingMixDeal.getdata(0);
                        List<TypeState> data = fightingimage.getFightingManData().cxxx("召唤兽");
                        int sid = UserMessUntil.getChosePetMes().getSid().intValue();
                        for (TypeState datum : data) {
                            if (datum.getState() != 0)
                                continue;
                            if (sid == Integer.parseInt(datum.getType())) {
                                if (FightingMixDeal.State != HandleState.HANDLE_PLAYER)
                                    return;
                                FormsManagement.HideForm(144);
                                // 生成召唤指令
                                FightingMonitor.FightingOperation(FightingBtn.SpellGenerate("召唤&" + datum.getType()));
                                FightingMonitor.operateEnd();
                                return;
                            }
                        }
                        ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽无法召唤");
                    }
                    break;
                    case 16:
                        sipet.openpipe = !sipet.openpipe;
                        int h  = sipet.openpipe?38:20;
                        sipet.sipetModelJPanelJList.setCellRenderer(new AtlasListCell(SipetJPanel.idx, Color.gray, 143, h, 9));
                        sipet.RefreshPetViewport();
                        String tx= sipet.openpipe?(Util.SwitchUI==1 ? "0x6FAC1076": "0x6FAB1089"):(Util.SwitchUI==1 ? "0x6FAC1077": "0x6FAB1090");
                        sipet.btnPipe.setIcons(Juitil.getImgs(tx));
                        break;
                    case 17://扩展组件
                        extracted(sipet,Util.SwitchUI==1?492:517);
                        break;

                default:
                    break;
            }
        }
    }

    public static void extracted(SipetJPanel sipet,int h) {
        sipet.extc = !sipet.extc;
        int uiType = Util.SwitchUI;
        //调整窗体大小
        if (uiType==1){//589 513
            Juitil.adjustFrameSize(sipet.extc?589:403, sipet.extc?513:492, 144);
        }else {
            Juitil.adjustFrameSize(sipet.extc?562:378, sipet.extc?538:517, 144);
        }
        int ofo = sipet.extc?uiType==1?590:572:uiType==1?403:388;
        Juitil.addClosingButtonToPanel(sipet,144,ofo);
        String exc= sipet.extc?(Util.SwitchUI==1 ? "0x6FAC1073": "0x6FAB1086"):(Util.SwitchUI==1 ? "0x6FAC1074": "0x6FAB1087");
        sipet.btnextc.setIcons(Juitil.getImgs(exc));
        int g = sipet.extc?h-5:h-23;
        sipet.btnextc.setBounds(uiType==1?14:0,g,23,23);
        sipet.RefreshPetViewport();
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
