package com.tool.imagemonitor;

public class ScriptEnd {

	private int type;//0 验证位置            
	private int map;
	private int x;
	private int y;
	public ScriptEnd(int type, int map, int x, int y) {
		super();
		this.type = type;
		this.map = map;
		this.x = x;
		this.y = y;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getMap() {
		return map;
	}
	public void setMap(int map) {
		this.map = map;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}

	@Override
	public String toString() {
		return "ScriptEnd{" +
				"type=" + type +
				", map=" + map +
				", x=" + x +
				", y=" + y +
				'}';
	}
}
