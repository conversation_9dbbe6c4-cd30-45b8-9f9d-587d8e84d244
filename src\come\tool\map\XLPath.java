package come.tool.map;

import com.tool.imagemonitor.ScriptEnd;
import com.tool.imagemonitor.ScriptOpen;
import org.come.model.Door;
import org.come.model.Npctable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XLPath {
	
	
	static List<XXX> openList = new ArrayList<XXX>();
	static List<Integer> openInts = new ArrayList<Integer>();//地图当前地图id没有到终点的位置
	static Map<Integer, XLMap> map = new HashMap<>();

	public static XLMap getXLMap(int mapId) {
		XLMap xlMap = map.get(mapId);
		if (xlMap == null) {
			xlMap = new XLMap(mapId);
			map.put(mapId, xlMap);
		}
		return xlMap;
	}
	/**true为障碍物*/
	public static boolean mskpanduan(byte[][] bs,int x,int y){
		return bs[y][x]==0;
	}
	public synchronized static List<Object> ZDXL(int x, int y, int mapId,int xNew, int yNew, int mapIdNew) {
		openInts.clear();
		openList.clear();
		XXX termPos = null;
		if (mapId != mapIdNew) {
			openList.add(new XXX(mapId, x, y));
			do {
				XXX currentPos = openList.get(0);
				for (int i = openList.size() - 1; i >= 0; i--) {
					if (currentPos.G > openList.get(i).G) {
						currentPos = openList.get(i);
					}
				}
				openList.remove(currentPos);
				XLMap xlMap = getXLMap(currentPos.map);
				List<XLP> list = xlMap.getXlpOne();
				if (list == null) {
					continue;
				}
				boolean isMap=true;
				for (int i = list.size() - 1; i >= 0; i--) {
					XLP p = list.get(i);
					if (isMap&&p.map==mapIdNew) {isMap=false;}
					if (currentPos.isDoor(p.door) || currentPos.isMap(p.map) || openInts.contains(p.map)) {
						continue;
					}
					XXX tmpPos = new XXX(p, currentPos);
					openList.add(tmpPos);
				}
				if (isMap) {
					openInts.add(currentPos.map);
				}
				for (int j = openList.size() - 1; j >= 0; j--) {
					if (termPos != null && termPos.G <= openList.get(j).G) {
						openList.remove(j);
					} else if (openList.get(j).map == mapIdNew) {
						if (termPos == null) {
							termPos = openList.get(j);
						} else {
							termPos = openList.get(j);
						}
					}
				}
			} while (openList.size() != 0);
		} else {
			termPos = new XXX(mapId, x, y);
		}
		if (termPos == null) {// 目的不可达
			return null;
		}
		List<Object> list = new ArrayList<>();
		ScriptOpen SOpen = new ScriptOpen(xNew, yNew);
		ScriptEnd SEnd = new ScriptEnd(0, mapIdNew, xNew, yNew);
		list.add(SEnd);
		list.add(SOpen);

		XXX end = new XXX(mapIdNew, xNew, yNew);
		end.fa = termPos;
		while (end.fa != null) {
			if (end.xlp == null) {
				if (end.map == end.fa.map) {
					XXX xxx = MapShort(end.fa, end);
					addScript(xxx, list);
				}
			} else if (end.getMap() == end.fa.getMap()) {
				XXX xxx = MapShort(end.fa.recordXXX(), end.recordXXX());
				addScript(xxx, list);
			}
			end = end.fa;
			if (end.xlp != null) {
				if (end.xlp.npc != 0) {
					SEnd = new ScriptEnd(0, end.map, end.x, end.y);
					list.add(SEnd);
					SOpen = new ScriptOpen(3, 0, end.xlp.door);
					list.add(SOpen);
					SOpen = new ScriptOpen(1, end.xlp.npc, 0);
					list.add(SOpen);
					SEnd = new ScriptEnd(0, end.xlp.dmap, end.xlp.dx, end.xlp.dy);
					list.add(SEnd);
					SOpen = new ScriptOpen(end.xlp.dx, end.xlp.dy);
					list.add(SOpen);
				} else {
					SEnd = new ScriptEnd(0, end.map, end.x, end.y);
					list.add(SEnd);
					SOpen = new ScriptOpen(end.xlp.dx, end.xlp.dy);
					list.add(SOpen);
				}
			}
		}
		return list;
	}

	public static XXX MapShort(XXX one, XXX two) {
		openList.clear();
		XXX termPos = null;
		openList.add(new XXX(one.map, one.x, one.y));
		XLMap xlMap = getXLMap(one.map);
		List<XLP> list = xlMap.getXlpTwo();
		do {
			XXX currentPos = openList.get(0);
			for (int i = openList.size() - 1; i >= 0; i--) {
				if (currentPos.G > openList.get(i).G) {
					currentPos = openList.get(i);
				}
			}
			openList.remove(currentPos);
			if (currentPos.x!=two.x||currentPos.y!=two.y) {
				if (list != null) {
					for (int i = list.size() - 1; i >= 0; i--) {
						XLP p = list.get(i);
						if (!currentPos.isDoor(p.door)) {
							XXX tmpPos = new XXX(p, currentPos);
							tmpPos.G+=10000;
							openList.add(tmpPos);
						}
					}
				}
				openList.add(new XXX(two.map, two.x, two.y, currentPos));		
			}
			for (int j = openList.size() - 1; j >= 0; j--) {
				if (termPos != null && termPos.G <= openList.get(j).G) {
					openList.remove(j);
				} else if (openList.get(j).x == two.x && openList.get(j).y == two.y) {
					if (termPos == null) {
						termPos = openList.get(j);
					} else {
						termPos = openList.get(j);
					}
				}
			}
		} while (openList.size() != 0);
		return termPos;
	}

	public static void addScript(XXX xxx, List<Object> list) {
		if (xxx==null)return;
		ScriptOpen SOpen;
		ScriptEnd SEnd;
		xxx = xxx.fa;
		while (xxx.fa != null) {
			if (xxx.xlp.npc != 0) {
				SEnd = new ScriptEnd(0, xxx.map, xxx.x, xxx.y);
				list.add(SEnd);
				SOpen = new ScriptOpen(3, 0, xxx.xlp.door);
				list.add(SOpen);
				SOpen = new ScriptOpen(1, xxx.xlp.npc, 0);
				list.add(SOpen);
				SEnd = new ScriptEnd(0, xxx.xlp.dmap, xxx.xlp.dx, xxx.xlp.dy);
				list.add(SEnd);
				SOpen = new ScriptOpen(xxx.xlp.dx, xxx.xlp.dy);
				list.add(SOpen);
			} else {
				SEnd = new ScriptEnd(0, xxx.map, xxx.x, xxx.y);
				list.add(SEnd);
				SOpen = new ScriptOpen(xxx.xlp.dx, xxx.xlp.dy);
				list.add(SOpen);
			}
			xxx = xxx.fa;
		}
	}

	static class XXX {
		private int map,x,y;//触发之后的位置
		private XLP xlp;//触发前的位置
		private int G;//总代价
		private transient XXX fa;
		
		public XXX(int map, int x, int y) {
			this.map = map;
			this.x = x;
			this.y = y;
		}

		public XXX(XLP xlp, XXX pos) {
			this.map = xlp.map;
			this.x = xlp.x;
			this.y = xlp.y;
			this.xlp = xlp;
			this.fa = pos;
			this.G = pos.G + calcD(pos.x, pos.y, xlp.dx, xlp.dy)+10000;
		}

		public XXX(int map, int x, int y, XXX pos) {
			this.map = map;
			this.x = x;
			this.y = y;
			this.fa = pos;
			this.G = pos.G + calcD(pos.x, pos.y, x, y);
		}

		public boolean isDoor(int door) {
			if (xlp == null) {
				return false;
			} else if (xlp.door == door) {
				return true;
			} else if (fa != null) {
				return fa.isDoor(door);
			}
			return false;
		}

		public boolean isMap(int map) {
			if (xlp == null) {
				return false;
			} else if (xlp.dmap == map) {
				return true;
			} else if (fa != null) {
				return fa.isMap(map);
			}
			return false;
		}

		public int getMap() {
			// TODO Auto-generated method stub
			return xlp != null ? xlp.dmap : map;
		}

		public XXX recordXXX() {
			// TODO Auto-generated method stub
			if (xlp != null) {
				return new XXX(xlp.dmap, xlp.dx, xlp.dy);
			} else {
				return this;
			}
		}
	}

	static class XLP {
		private int door;
		private int npc;
		private int map, x, y;// 触发之后的位置
		private int dmap, dx, dy;// 触发前的位置

		public XLP(Door door, int dmap) {
			init(door);
			int[] rect = door.getRects();
			this.dmap = dmap;
			this.dx = (rect[0] + rect[1]) / 2;
			this.dy = (rect[2] + rect[3]) / 2;
		}

		public XLP(Door door, Npctable npctable, int dmap) {
			init(door);
			this.dmap = dmap;
			this.dx = Integer.parseInt(npctable.getTx());
			this.dy = Integer.parseInt(npctable.getTy());
			this.npc = Integer.parseInt(npctable.getNpcid());
		}

		public void init(Door door) {
			this.door = Integer.parseInt(door.getDoorid());
			this.map = Integer.parseInt(door.getDoormap());
			String[] point = door.getDoorpoint().split("\\|");
			this.x = Integer.parseInt(point[0]);
			this.y = Integer.parseInt(point[1]);
		}

		public int getMap() {
			// TODO Auto-generated method stub
			return map;
		}
	}

	/** 2点间的直接距离 */
	private static int calcD(int x, int y, int tx, int ty) {
		x -= tx;
		y -= ty;
		x *= x;
		y *= y;
		return x + y;
	}
}
