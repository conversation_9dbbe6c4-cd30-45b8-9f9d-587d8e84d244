package com.tool.tcp;

import java.awt.Graphics;

public interface NewPart {
	/**是否点击*/
	public boolean contains(int x,int y);
	/**清空指针*/
	public void recycle();
	/**获取素材总时间*/
	public int getTime();
	/**加载素材*/
	public void loadTcp();
	/**获取动作*/
	public int getAct();
	public  double getBodyAlpha();
	public void setBodyAlpha(double  alpha);
	/**切换动作*/
	public void setAct(int Act);
	/**添加图层*/
	public NewPart addPart(NewPart newPart);
	/**移除*/
	public NewPart removePart(String rSkin);
	/**获取等级*/
	public int getLvl();
	/**获取下一部件*/
	public NewPart getPart();
	/**修改 下一部件*/
	public void setPart(NewPart part);
	/**重置图层*/
	public NewPart setPart(int lvl,String skin);
	public NewPart setPart(int lvl,long skin,HHOne[] ones);
	/**获取偏移Y*/
	public int getPy();
	/**复制Y*/
	public NewPart clonePart();
	/**获取总方向数*/
	public int getAnimationCount();
	public void draw(Graphics g, int x, int y, int dir, long time);
	public void draw(Graphics g, int x, int y, int dir, long time, float alpha);
	public void draw(Graphics g, int x, int y, int dir, long time, int type);
	public void drawEnd(Graphics g, int x, int y, int dir, float alpha);
	public void drawBattle(Graphics g, int x, int y, int dir, long time,float alpha);
	public void setFly(String skin);
	public void drawFly(Graphics g, int x, int y, int dir, long time, float alpha);
	/**
	 * 获取当前影子偏移量
	 * @return 当前影子的Y轴偏移量
	 */
	public float getCurrentShadowOffset();
}
