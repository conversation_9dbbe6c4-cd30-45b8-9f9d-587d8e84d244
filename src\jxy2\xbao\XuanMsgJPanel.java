package jxy2.xbao;

import com.tool.tcpimg.ChatBox;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.Skill;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.AlphaComposite;

public class XuanMsgJPanel extends JPanel {
    private JLabel  labavatar,//图像
                    labname,//名称
                    labdescribe,//描述
                    labquality,//品质
                     labtype,//类型
                     labsect,// 门派
                     lablvel,// 等级
                    labskillcon,//技能消耗
                    labXuanzhi,//玄支
                    labxunyin,//玄印文本
                    labjihuo,//激活技能文本
                    labcooling;//冷却

    private JLabel[] Xyinimg = new JLabel[5];
    private ChatBox shi=new ChatBox();
    private ChatBox skillbox=new ChatBox();
    private ChatBox activation =new ChatBox();
    private JLabel[] skillimg = new JLabel[6];//放置三个激活技能图标
    private float[] skillAlpha = new float[6]; // 每个技能图标的透明度
    private JLabel[] chatBoxBackImg = new JLabel[3];
    private RichLabel richLabel;//基础技能
    private int gheight;//面板的高度
    public XuanMsgJPanel() {
        this.setPreferredSize(new Dimension(480,429));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        gheight  = 429;
        // 初始化透明度为0.5F（未激活状态）
        for (int i = 0; i < 6; i++) {
            skillAlpha[i] = 0.5f;
        }
        // 顶部信息区域
        labquality = TeststateJpanel.GJpanelText(UIUtils.COLOR_XB, UIUtils.TEXT_FONT1);
        labquality.setBounds(41, 12, 90, 20);
        this.add(labquality);
        
        lablvel = TeststateJpanel.GJpanelText(UIUtils.COLOR_XB, UIUtils.TEXT_FONT1);
        this.add(lablvel);
        
        labname = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labname);

        // 图像区域
        labavatar = new JLabel();
        labavatar.setBounds(10, 40, 120, 120);
        this.add(labavatar);

        // 基本属性区域
        labdescribe = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labdescribe);

        labtype = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labtype);

        labsect = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labsect);

        labskillcon = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labskillcon);

        labcooling = TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labcooling);
        labXuanzhi =TeststateJpanel.GJpanelText(UIUtils.COLOR_White, UIUtils.TEXT_FONT1);
        this.add(labXuanzhi);

        labcooling.setBounds(135, 114, 340, 20);
        labskillcon.setBounds(135, 97, 340, 20);
        labsect.setBounds(135, 80, 340, 20);
        labXuanzhi.setBounds(135, 63, 340, 20);
        labtype.setBounds(135, 46, 340, 20);
        labdescribe.setBounds(135, 29, 340, 20);
        labname.setBounds(135, 12, 275, 20);
        lablvel.setBounds(200, 12, 60, 20);

        // 玄印区域
        labxunyin = TeststateJpanel.GJpanelText(new Color(214,182,141), UIUtils.TEXT_FONT15);
        labxunyin.setText("【玄印】");
        labxunyin.setVisible(false);
        labxunyin.setBounds(135, 240, 340, 20);
        this.add(labxunyin);

        // 玄印格子
        for (int i = 0; i < Xyinimg.length; i++) {
            Xyinimg[i] = new JLabel();
            Xyinimg[i].setBounds(135, 265 + i*25, 18, 18);
            this.add(Xyinimg[i]);
        }

        // 初始化技能图标
        for (int i = 0; i < 6; i++) {
            final int index = i;  // 用于内部类访问
            skillimg[i] = new JLabel() {
                @Override
                protected void paintComponent(Graphics g) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    // 设置透明度
                    g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, skillAlpha[index]));
                    super.paintComponent(g2d);
                    g2d.dispose();
                }
            };

            skillimg[i].setBounds(135 + (i % 2) * 12, 265 + (i / 2) * 12, 12, 12);
            this.add(skillimg[i]);
        }
        // 添加玄宝印鸣技能标题
        labjihuo = TeststateJpanel.GJpanelText(new Color(0,255,0), UIUtils.TEXT_FONT15);
        labjihuo.setText("【玄宝印鸣技能】");
        labjihuo.setBounds(135, 370, 340, 20);
        this.add(labjihuo);

        for (int i = 0; i < chatBoxBackImg.length; i++) {
            chatBoxBackImg[i] = new JLabel();
            this.add(chatBoxBackImg[i]);
        }
    }
    private ImageIcon xykaiqi = Juitil.tz362;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        // 绘制背景
        Juitil.ImngBack(g, Juitil.JK_24, 2, 2, getWidth()-5, getHeight()-5, 1);
        Juitil.ImngBack(g,Juitil.tx322,0,0,getWidth(), getHeight(),1);
        
        // 绘制技能描述
        Graphics g2g = g.create(135,30, 324, skillbox.getHeight());
        skillbox.paint(g2g);
        g2g.dispose();
        
        // 计算玄印描述的位置
        int neh =  skillbox.getHeight();

        // 绘制玄印描述
        Graphics g3 = g.create(155, neh+22, 324, shi.getHeight());
        shi.paint(g3);
        g3.dispose();
        neh =  skillbox.getHeight()+shi.getHeight() +22;
//        // 绘制激活技能描述180
        Graphics g4 = g.create(180, neh + 18, 290, activation.getHeight());
        // 添加边框以便调试
        activation.paint(g4);
        g4.dispose();


    }
    public boolean isCommon;
    public int lingbaoshow(Xuanbao xuanbao) {
        if (xuanbao==null)return gheight;
        Xuanbao nexuanbao = UserMessUntil.getXuanabao(xuanbao.getBaoname());
        if (nexuanbao==null)return gheight;
        for (int i = 0; i < 5; i++) {
            Xyinimg[i].setIcon(null);
            Xyinimg[i].setVisible(false);
        }
        for (int i = 0; i < skillimg.length; i++) {
            skillimg[i].setIcon(null);
            skillimg[i].setVisible(false);
        }
        StringBuffer buffer = new StringBuffer();
        int h = 0;
        // 设置基本信息
        labavatar.setIcon(GoodsListFromServerUntil.xbaoWdfile(nexuanbao.getBaoskin(), 120, 120));
        labquality.setText("品质 "+nexuanbao.getBaoquality());
        labname.setText(nexuanbao.getBaoname());
        lablvel.setText(xuanbao.getBaolvl()+"级");
        // 重新设置所有组件的位置
        labname.setBounds(135, 12, 275, 20);
        lablvel.setBounds(200, 12, 60, 20);
        // 更新技能描述
        skillbox.removemsg();
        activation.removemsg();
        shi.removemsg();
        buffer.append("#W");
        String qc = nexuanbao.getBaodescribe().split("&")[0].replace("#c4f798b","");
        buffer.append(qc.replace("#r","，"));
        buffer.append("#r");
        buffer.append("【玄宝类型】").append(nexuanbao.getBaotype()).append("型");
        buffer.append("#r");
        // 根据种族设置显示
        boolean isCommon = nexuanbao.getBaorace().equals("通用");
        this.isCommon = isCommon;
        if (!isCommon) {
            buffer.append("【所属玄支】缺失");
            buffer.append("#r");
        }
        // 设置门派相关信息
        if (isCommon) {
            buffer.append("【所属门派】全种族").append(nexuanbao.getBaorace());
            buffer.append("#r");
        } else {
            buffer.append("【所属门派】").append(nexuanbao.getBaorace()).append("门派");
            buffer.append("#r");
        }
        // 设置技能消耗和冷却回合
        if (nexuanbao.getBaoconsume()!=0){
            buffer.append("【技能消耗】").append(nexuanbao.getBaoconsume()).append("玄元");
            buffer.append("#r");
        }
        if (nexuanbao.getBaoround()!=0) {
            buffer.append("【冷却回合】").append(nexuanbao.getBaoround());
            buffer.append("#r");
        }

        ObtainImagesBasedOnColor(
                nexuanbao.getBaoskill().split("&")[0].split(","),
                nexuanbao.getBaoskillone().split("&")[0].split(","),
                nexuanbao.getBaoskilltwo().split("&")[0].split(","),skillimg
                );

        Skill skill = UserMessUntil.getXuanbaoSkill(nexuanbao.getBaoname());
        if (skill != null) {
            buffer.append("#G").append(skill.getRemark());
        }
        buffer.append("#r #r");
        buffer.append("#cd6b68d【玄印】#r");
        skillbox.addText(buffer.toString(), 324, UIUtils.TEXT_FONT15);
        h = skillbox.getHeight();
        String[] xyConfig = nexuanbao.getXyConfig().split("\\|");
        for (int i = 0; i < xyConfig.length; i++) {
            xuanyinxiugai(i, "#cd6b68d未开启", xykaiqi);
            Xyinimg[i].setBounds(135, (h+24) + i*22, 18, 18);
            Xyinimg[i].setVisible(true);  // 确保需要显示的图标是可见的
        }

        h = skillbox.getHeight()+shi.getHeight();
        labjihuo.setBounds(135, h+22, 340, 20);

        // 计算实际的技能组数和文本内容
        int skillGroupCount = 0;
        String[] skillTexts = new String[3];
        if (nexuanbao.getBaoskill() != null && !nexuanbao.getBaoskill().isEmpty()) {
            skillTexts[skillGroupCount] = nexuanbao.getBaoskill().split("&")[1];
            skillGroupCount++;
        }
        if (nexuanbao.getBaoskillone() != null && !nexuanbao.getBaoskillone().isEmpty()) {
            skillTexts[skillGroupCount] = nexuanbao.getBaoskillone().split("&")[1];
            skillGroupCount++;
        }
        if (nexuanbao.getBaoskilltwo() != null && !nexuanbao.getBaoskilltwo().isEmpty()){
            skillTexts[skillGroupCount] = nexuanbao.getBaoskilltwo().split("&")[1];
            skillGroupCount++;
        }
        int currentY = h+37; // 起始Y坐标
        int textHeight;
        for (int i = 0; i < skillGroupCount; i++) {
            if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                // 设置chatBoxBackImg的位置在当前文本开始处
                // 计算当前文本的渲染高度
                chatBoxBackImg[i].setBounds(15, currentY, 56, 16);
                textHeight = XbaoFrame.getXbaoFrame().getXbaoJpanel().calculateTextHeight("#c9f9f9f未激活", 290, UIUtils.TEXT_FONT2);
                // 更新下一个文本的Y坐标
                currentY += textHeight-2; // 4像素间距
            }
        }
        // 最后设置主ChatBox的内容（合并所有文本）
        StringBuilder combinedText = new StringBuilder();
        for (int i = 0; i < skillGroupCount; i++) {
            if (skillTexts[i] != null && !skillTexts[i].isEmpty()) {
                if (combinedText.length() > 0) {
                    combinedText.append("#r"); // 换行标记
                }
                //未激活skillTexts[i]
                combinedText.append("#c9f9f9f未激活");
            }
        }

        if (combinedText.length()>0){
            activation.addText(combinedText.toString(), 290, UIUtils.TEXT_FONT15);
        }
//        //TODO 还需要根据已激活，跟未激活来调整 图标的位置
        for (int i = 0; i < 6; i++) {
            int row = i % 2 * 17;
            int baseY = chatBoxBackImg[i/2].getY();
            skillimg[i].setBounds(141 + row, baseY +10, 12, 12);
            skillimg[i].setVisible(true);
        }
        // 设置固定面板高度
        gheight =  skillbox.getHeight() + shi.getHeight() + activation.getHeight() + 42;
        return gheight;
    }

    public int getGheight() {
        return gheight;
    }
    /**
     * 修改玄印描述图像
     */
    public void xuanyinxiugai(int i,String text,ImageIcon imageIcon){
        shi.addText(text, 324,UIUtils.TEXT_FONT15);
        Xyinimg[i].setIcon(imageIcon);
    }

    /**
     * XuanbaoYinmingSkill
     */
    public void XuanbaoYinmingSkill(ImageIcon imageIcon){
        // 更新激活技能图标
    }


    public void ObtainImagesBasedOnColor(String[] colors,String[] colors1,String[] colors2,JLabel[] jLabels){

        // 清除所有图标
        for (int i = 0; i < 6; i++) {
            jLabels[i].setIcon(null);
        }

        // 处理第一组颜色
        if (colors != null && colors.length > 0) {
            setSkillIcon(colors[0], jLabels[0]);
            if (colors.length > 1) {
                setSkillIcon(colors[1], jLabels[1]);
            }
        }

        // 处理第二组颜色
        if (colors1 != null && colors1.length > 0) {
            setSkillIcon(colors1[0], jLabels[2]);
            if (colors1.length > 1) {
                setSkillIcon(colors1[1], jLabels[3]);
            }
        }

        // 处理第三组颜色
        if (colors2 != null && colors2.length > 0) {
            setSkillIcon(colors2[0], jLabels[4]);
            if (colors2.length > 1) {
                setSkillIcon(colors2[1], jLabels[5]);
            }
        }
    }

    private void setSkillIcon(String color, JLabel label) {
        if (color == null || color.trim().isEmpty()) return;
        
        ImageIcon icon = null;
        switch (color.trim()) {
            case "蓝":
                icon = Juitil.tz363;
                break;
            case "红":
                icon = Juitil.tz364;
                break;
            case "黄":
                icon = Juitil.tz365;
                break;
            case "绿":
                icon = Juitil.tz366;
                break;
        }
        if (icon != null) {
            label.setIcon(icon);
        }
    }

    // 设置技能图标的透明度
    public void setSkillAlpha(int index, float alpha) {
        if (index >= 0 && index < skillAlpha.length) {
            skillAlpha[index] = alpha;
            skillimg[index].repaint();
        }
    }

    // 设置技能图标的激活状态
    public void setSkillActive(int index, boolean active) {
        setSkillAlpha(index, active ? 1.0f : 0.5f);
    }


    public JLabel getLabsect() {
        return labsect;
    }

    public void setLabsect(JLabel labsect) {
        this.labsect = labsect;
    }

    public JLabel getLabavatar() {
        return labavatar;
    }

    public void setLabavatar(JLabel labavatar) {
        this.labavatar = labavatar;
    }

    public JLabel getLabname() {
        return labname;
    }

    public void setLabname(JLabel labname) {
        this.labname = labname;
    }

    public JLabel getLabdescribe() {
        return labdescribe;
    }

    public void setLabdescribe(JLabel labdescribe) {
        this.labdescribe = labdescribe;
    }

    public JLabel getLabquality() {
        return labquality;
    }

    public void setLabquality(JLabel labquality) {
        this.labquality = labquality;
    }

    public JLabel getLabtype() {
        return labtype;
    }

    public void setLabtype(JLabel labtype) {
        this.labtype = labtype;
    }

    public JLabel getLabskillcon() {
        return labskillcon;
    }

    public void setLabskillcon(JLabel labskillcon) {
        this.labskillcon = labskillcon;
    }

    public JLabel getLabcooling() {
        return labcooling;
    }

    public void setLabcooling(JLabel labcooling) {
        this.labcooling = labcooling;
    }

    public RichLabel getRichLabel() {
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }
}

