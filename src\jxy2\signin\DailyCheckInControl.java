package jxy2.signin;

import org.come.Frame.ZhuFrame;
import org.come.action.FromServerAction;
import org.come.until.GsonUtil;

public class DailyCheckInControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        ApplyQianDao applyQianDao = GsonUtil.getGsonUtil().getgson().fromJson(mes, ApplyQianDao.class);
        switch (applyQianDao.getType()){
            case 2:
                ZhuFrame.getZhuJpanel().addPrompt2("签到成功");
                break;
            case 3:
                ZhuFrame.getZhuJpanel().addPrompt2("补签成功");
                break;
            case 4:
                ZhuFrame.getZhuJpanel().addPrompt2("抽奖成功");
                break;
        }
        DailyCheckInFrame.getDailyCheckInFrame().getDailyCheckInJPanel().initChoujiang(applyQianDao.getChoujianBean(), applyQianDao.getChpujiangjihe(),applyQianDao.getObtainCumulativeDays(),applyQianDao.getLxCjBean(),applyQianDao.getDAYS().trim());
        DailyCheckInFrame.getDailyCheckInFrame().getDailyCheckInJPanel().initQiandao(applyQianDao.getQiandaoday());
    }
}
