package jxy2.flight;

import org.come.action.FlyControl;
import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class FlightJMouse extends TemplateMouseListener {

    public int index;
    public FlyJPanel flightJPanel;
    public FlightJMouse(FlyJPanel flightJPanel, int index) {
        this.index = index;
        this.flightJPanel = flightJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {

        Fly fly = FlyJPanel.getFlyList().get(index);
        if (fly!=null){
            flightJPanel.setAssemble(fly.getFlyid());
            flightJPanel.setfkypart(FlyControl.GetFlyName(fly.getFlyname())+fly.getFlysteps());
            flightJPanel.updateCharacter(fly);
            flightJPanel.advanced.setBtn(fly.getFlylv()==FlyJPanel.getTSExp(fly.getFlysteps())&&fly.getFlysteps()<5?1:-1);
        }



        flightJPanel.getFlyiconback()[index].setBorder(BorderFactory.createLineBorder(Color.yellow,2));
            for (int i = 0; i < flightJPanel.getFlyiconback().length; i++) {
                if (i != index){
                    flightJPanel.getFlyiconback()[i].setBorder(BorderFactory.createEmptyBorder());
                }
            }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
