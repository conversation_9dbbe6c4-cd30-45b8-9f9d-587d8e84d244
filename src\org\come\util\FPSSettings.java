package org.come.util;

import org.come.login.GameView;

/**
 * FPS设置管理器
 * 提供FPS相关的设置和控制功能
 */
public class FPSSettings {
    
    private static FPSSettings instance;
    private GameView gameView;
    
    // FPS预设
    public static final int FPS_30 = 30;
    public static final int FPS_60 = 60;
    public static final int FPS_120 = 120;
    public static final int FPS_144 = 144;
    public static final int FPS_UNLIMITED = 300;
    
    private FPSSettings() {}
    
    public static FPSSettings getInstance() {
        if (instance == null) {
            instance = new FPSSettings();
        }
        return instance;
    }
    
    /**
     * 设置GameView引用
     */
    public void setGameView(GameView gameView) {
        this.gameView = gameView;
    }
    
    /**
     * 应用显示器自适应FPS
     */
    public void applyAdaptiveFPS() {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        displayInfo.refresh(); // 重新检测显示器
        
        int targetFPS = displayInfo.getTargetFPS();
        if (gameView != null) {
            gameView.adjustFPS(targetFPS);
        }
        
        System.out.println("[FPS设置] 应用自适应FPS: " + targetFPS);
    }
    
    /**
     * 设置固定FPS
     */
    public void setFixedFPS(int fps) {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        displayInfo.setTargetFPS(fps);
        
        if (gameView != null) {
            gameView.adjustFPS(fps);
        }
        
        System.out.println("[FPS设置] 设置固定FPS: " + fps);
    }
    
    /**
     * 启用/禁用垂直同步
     */
    public void setVsync(boolean enabled) {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        displayInfo.setVsyncEnabled(enabled);
        
        // 重新应用FPS设置
        int newTargetFPS = displayInfo.getTargetFPS();
        if (gameView != null) {
            gameView.adjustFPS(newTargetFPS);
        }
        
        System.out.println("[FPS设置] 垂直同步" + (enabled ? "启用" : "禁用") + 
            "，FPS调整为: " + newTargetFPS);
    }
    
    /**
     * 获取FPS预设选项
     */
    public String[] getFPSPresets() {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        int refreshRate = displayInfo.getDetectedRefreshRate();
        
        if (refreshRate >= 144) {
            return new String[]{
                "30 FPS (省电模式)",
                "60 FPS (标准)",
                "120 FPS (高性能)",
                "144 FPS (匹配显示器)",
                "自适应 (推荐)"
            };
        } else if (refreshRate >= 120) {
            return new String[]{
                "30 FPS (省电模式)",
                "60 FPS (标准)",
                "120 FPS (匹配显示器)",
                "自适应 (推荐)"
            };
        } else {
            return new String[]{
                "30 FPS (省电模式)",
                "60 FPS (匹配显示器)",
                "自适应 (推荐)"
            };
        }
    }
    
    /**
     * 应用FPS预设
     */
    public void applyFPSPreset(int presetIndex) {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        int refreshRate = displayInfo.getDetectedRefreshRate();
        
        int targetFPS;
        if (refreshRate >= 144) {
            switch (presetIndex) {
                case 0: targetFPS = 30; break;
                case 1: targetFPS = 60; break;
                case 2: targetFPS = 120; break;
                case 3: targetFPS = 144; break;
                case 4: 
                default: 
                    applyAdaptiveFPS();
                    return;
            }
        } else if (refreshRate >= 120) {
            switch (presetIndex) {
                case 0: targetFPS = 30; break;
                case 1: targetFPS = 60; break;
                case 2: targetFPS = 120; break;
                case 3:
                default:
                    applyAdaptiveFPS();
                    return;
            }
        } else {
            switch (presetIndex) {
                case 0: targetFPS = 30; break;
                case 1: targetFPS = 60; break;
                case 2:
                default:
                    applyAdaptiveFPS();
                    return;
            }
        }
        
        setFixedFPS(targetFPS);
    }
    
    /**
     * 获取当前FPS设置信息
     */
    public String getCurrentFPSInfo() {
        DisplayInfo displayInfo = DisplayInfo.getInstance();
        return String.format("当前设置: %s", displayInfo.getDisplaySummary());
    }
}
