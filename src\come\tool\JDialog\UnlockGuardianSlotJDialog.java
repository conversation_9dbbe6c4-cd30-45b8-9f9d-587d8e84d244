package come.tool.JDialog;

import org.come.Frame.MountJframe;
import org.come.Jpanel.MountJPanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class UnlockGuardianSlotJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi) {
            MountJPanel mountjpanel = MountJframe.getMountjframe().getMountjpanel();
            int sequence = (int) object;
            String mes = Agreement.getAgreement().rolechangeAgreement("X"+mountjpanel.getGuardType()+"|"+sequence);
            SendMessageUntil.toServer(mes);
        }
    }
}
