package com.tool.memory;

import org.come.bean.Mapmodel;
import org.come.model.Door;
import org.come.model.Gamemap;
import org.come.until.CutButtonImage;

import java.awt.*;
import java.io.File;
import java.lang.ref.SoftReference;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 地图内存优化器 - 解决地图加载时的内存增长问题
 */
public class MapMemoryOptimizer {
    
    // 地图数据缓存
    private static final ConcurrentHashMap<Integer, SoftReference<MapData>> mapCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, SoftReference<Image>> imageCache = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Integer, SoftReference<List<Door>>> doorCache = new ConcurrentHashMap<>();
    
    // 缓存限制
    private static final int MAX_MAP_CACHE = 3;  // 最多缓存3个地图
    private static final int MAX_IMAGE_CACHE = 10; // 最多缓存10个图像
    
    // 内存监控
    private static long mapMemoryUsed = 0;
    private static final long MAX_MAP_MEMORY = 50 * 1024 * 1024; // 限制地图内存50MB
    
    // 统计信息
    private static long totalMapLoads = 0;
    private static long cacheHits = 0;
    private static long memoryFreed = 0;
    
    // 定时清理器
    private static final ScheduledExecutorService mapCleaner = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "MapMemoryCleaner");
        t.setDaemon(true);
        return t;
    });
    
    static {
        // 每60秒清理一次地图缓存
        mapCleaner.scheduleAtFixedRate(MapMemoryOptimizer::cleanupMapMemory, 60, 60, TimeUnit.SECONDS);
    }
    
    /**
     * 地图数据结构
     */
    public static class MapData {
        public final byte[][] mapRules;
        public final int width;
        public final int height;
        public final String mapPath;
        public final long loadTime;
        public final long memorySize;
        
        public MapData(byte[][] mapRules, int width, int height, String mapPath) {
            this.mapRules = mapRules;
            this.width = width;
            this.height = height;
            this.mapPath = mapPath;
            this.loadTime = System.currentTimeMillis();
            
            // 计算内存大小
            long size = 0;
            if (mapRules != null) {
                size += mapRules.length * mapRules[0].length; // byte数组
            }
            size += mapPath != null ? mapPath.length() * 2 : 0; // 字符串
            this.memorySize = size;
        }
    }
    
    /**
     * 优化地图加载 - 修复缓存后画面不更新的问题
     */
    public static boolean loadMapOptimized(Mapmodel mapmodel, int mapId, boolean isNightMode) {
        totalMapLoads++;

        System.out.println("[地图优化] 开始加载地图: " + mapId + (isNightMode ? " (夜间模式)" : ""));

        // 构建地图文件路径
        String mapFileName = isNightMode ?
            "resource/map/" + mapId + "1.map" :
            "resource/map/" + mapId + ".map";

        // 检查缓存 - 但仍需要重新加载地图文件以确保画面更新
        MapData cachedData = getCachedMapData(mapId);
        if (cachedData != null && cachedData.mapPath.equals(mapFileName)) {
            cacheHits++;
            System.out.println("[地图优化] 发现缓存数据，但仍需重新加载以更新画面: " + mapId);
        } else {
            System.out.println("[地图优化] 缓存未命中，加载新地图文件: " + mapId);
        }

        // 检查内存使用
        if (mapMemoryUsed > MAX_MAP_MEMORY) {
            System.out.println("[地图优化] 内存使用过高，强制清理缓存");
            forceCleanupMapMemory();
        }

        // 加载地图文件
        File mapFile = new File(mapFileName);
        if (!mapFile.exists()) {
            System.err.println("[地图优化] 地图文件不存在: " + mapFileName);
            return false;
        }

        // 执行实际加载 - 确保地图画面正确更新
        boolean success = mapmodel.getjMap().loadMap(mapFile);
        if (success) {
            // 缓存地图数据（仅用于统计和内存管理）
            cacheMapData(mapId, mapmodel, mapFileName);
            System.out.println("[地图优化] 地图加载成功: " + mapId);
        } else {
            System.err.println("[地图优化] 地图加载失败: " + mapId);
        }

        return success;
    }
    
    /**
     * 获取缓存的地图数据
     */
    private static MapData getCachedMapData(int mapId) {
        SoftReference<MapData> ref = mapCache.get(mapId);
        if (ref != null) {
            MapData data = ref.get();
            if (data != null) {
                return data;
            } else {
                mapCache.remove(mapId);
            }
        }
        return null;
    }
    
    /**
     * 地图加载前的内存清理
     */
    public static void preMapLoadCleanup() {
        System.out.println("[地图优化] 执行地图加载前的内存清理...");

        // 清理旧的图像缓存
        if (imageCache.size() > 5) {
            int removed = 0;
            java.util.Iterator<java.util.Map.Entry<String, SoftReference<Image>>> iterator = imageCache.entrySet().iterator();
            while (iterator.hasNext() && removed < 3) {
                iterator.next();
                iterator.remove();
                removed++;
            }
            System.out.println("[地图优化] 清理了 " + removed + " 个旧图像缓存");
        }

        // 建议垃圾回收
        System.gc();
    }
    
    /**
     * 缓存地图数据
     */
    private static void cacheMapData(int mapId, Mapmodel mapmodel, String mapPath) {
        try {
            // 获取地图规则数据
            byte[][] mapRules = mapmodel.getjMap().getMaprules();
            int width = mapmodel.getjMap().getMapWidth();
            int height = mapmodel.getjMap().getMapHeight();
            
            // 创建地图数据
            MapData mapData = new MapData(mapRules, width, height, mapPath);
            
            // 检查缓存大小
            if (mapCache.size() >= MAX_MAP_CACHE) {
                removeOldestMapCache();
            }
            
            // 缓存数据
            mapCache.put(mapId, new SoftReference<>(mapData));
            mapMemoryUsed += mapData.memorySize;
            
            System.out.println("[地图优化] 缓存地图数据: " + mapId + " (内存: " + mapData.memorySize / 1024 + "KB)");
        } catch (Exception e) {
            System.err.println("[地图优化] 缓存地图数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 优化小地图图像加载
     */
    public static Image loadSmallMapOptimized(int mapId, boolean isNightMode) {
        String imageKey = mapId + (isNightMode ? "_night" : "_day");
        
        // 检查图像缓存
        SoftReference<Image> ref = imageCache.get(imageKey);
        if (ref != null) {
            Image cachedImage = ref.get();
            if (cachedImage != null) {
                System.out.println("[地图优化] 使用缓存的小地图图像: " + imageKey);
                return cachedImage;
            } else {
                imageCache.remove(imageKey);
            }
        }
        
        // 加载新图像
        String imagePath = isNightMode ? 
            "resource/smap/s" + mapId + "1.png" : 
            "resource/smap/s" + mapId + ".png";
        
        try {
            Image image = CutButtonImage.getImage(imagePath, -1, -1).getImage();
            
            // 缓存图像
            if (imageCache.size() >= MAX_IMAGE_CACHE) {
                removeOldestImageCache();
            }
            
            imageCache.put(imageKey, new SoftReference<>(image));
            System.out.println("[地图优化] 加载并缓存小地图图像: " + imageKey);
            
            return image;
        } catch (Exception e) {
            System.err.println("[地图优化] 加载小地图图像失败: " + imagePath);
            return null;
        }
    }
    
    /**
     * 优化传送门列表
     */
    public static List<Door> getDoorListOptimized(int mapId, List<String> doorIds) {
        // 检查传送门缓存
        SoftReference<List<Door>> ref = doorCache.get(mapId);
        if (ref != null) {
            List<Door> cachedDoors = ref.get();
            if (cachedDoors != null) {
                System.out.println("[地图优化] 使用缓存的传送门列表: " + mapId);
                return cachedDoors;
            } else {
                doorCache.remove(mapId);
            }
        }
        
        // 创建新的传送门列表
        java.util.List<Door> doors = new java.util.ArrayList<>();
        if (doorIds != null) {
            for (String doorId : doorIds) {
                try {
                    Door door = org.come.until.UserMessUntil.getDoor(doorId);
                    if (door != null) {
                        doors.add(door);
                    }
                } catch (Exception e) {
                    System.err.println("[地图优化] 加载传送门失败: " + doorId);
                }
            }
        }
        
        // 缓存传送门列表
        doorCache.put(mapId, new SoftReference<>(doors));
        System.out.println("[地图优化] 缓存传送门列表: " + mapId + " (数量: " + doors.size() + ")");
        
        return doors;
    }
    
    /**
     * 清理地图内存
     */
    public static void cleanupMapMemory() {
        long beforeCleanup = mapMemoryUsed;
        
        // 清理失效的SoftReference
        mapCache.entrySet().removeIf(entry -> entry.getValue().get() == null);
        imageCache.entrySet().removeIf(entry -> entry.getValue().get() == null);
        doorCache.entrySet().removeIf(entry -> entry.getValue().get() == null);
        
        // 重新计算内存使用
        mapMemoryUsed = 0;
        for (SoftReference<MapData> ref : mapCache.values()) {
            MapData data = ref.get();
            if (data != null) {
                mapMemoryUsed += data.memorySize;
            }
        }
        
        long cleaned = beforeCleanup - mapMemoryUsed;
        if (cleaned > 0) {
            memoryFreed += cleaned;
            System.out.println("[地图优化] 清理地图内存: " + cleaned / 1024 + "KB");
        }
    }
    
    /**
     * 强制清理地图内存
     */
    public static long forceCleanupMapMemory() {
        long beforeCleanup = mapMemoryUsed;
        
        System.out.println("[地图优化] 开始强制清理地图内存...");
        
        // 清理所有缓存
        mapCache.clear();
        imageCache.clear();
        doorCache.clear();
        mapMemoryUsed = 0;
        
        // 强制垃圾回收
        System.gc();
        
        long cleaned = beforeCleanup;
        memoryFreed += cleaned;
        
        System.out.println("[地图优化] 强制清理完成，释放: " + cleaned / 1024 / 1024 + "MB");
        return cleaned;
    }
    
    /**
     * 移除最旧的地图缓存
     */
    private static void removeOldestMapCache() {
        long oldestTime = Long.MAX_VALUE;
        Integer oldestKey = null;
        
        for (java.util.Map.Entry<Integer, SoftReference<MapData>> entry : mapCache.entrySet()) {
            MapData data = entry.getValue().get();
            if (data != null && data.loadTime < oldestTime) {
                oldestTime = data.loadTime;
                oldestKey = entry.getKey();
            }
        }
        
        if (oldestKey != null) {
            SoftReference<MapData> removed = mapCache.remove(oldestKey);
            if (removed != null) {
                MapData data = removed.get();
                if (data != null) {
                    mapMemoryUsed -= data.memorySize;
                    System.out.println("[地图优化] 移除旧地图缓存: " + oldestKey);
                }
            }
        }
    }
    
    /**
     * 移除最旧的图像缓存
     */
    private static void removeOldestImageCache() {
        if (!imageCache.isEmpty()) {
            String firstKey = imageCache.keys().nextElement();
            imageCache.remove(firstKey);
            System.out.println("[地图优化] 移除旧图像缓存: " + firstKey);
        }
    }
    
    /**
     * 获取地图优化统计
     */
    public static String getMapOptimizationStats() {
        double hitRate = totalMapLoads > 0 ? (double) cacheHits / totalMapLoads * 100 : 0;
        long memoryMB = mapMemoryUsed / 1024 / 1024;
        long maxMemoryMB = MAX_MAP_MEMORY / 1024 / 1024;
        long freedMB = memoryFreed / 1024 / 1024;
        
        return String.format("地图优化统计: 缓存命中率%.1f%% (%d/%d), 内存%dMB/%dMB, 已释放%dMB, 地图缓存%d个, 图像缓存%d个", 
                           hitRate, cacheHits, totalMapLoads, memoryMB, maxMemoryMB, freedMB, 
                           mapCache.size(), imageCache.size());
    }
    
    /**
     * 预加载常用地图
     */
    public static void preloadCommonMaps(int[] mapIds) {
        System.out.println("[地图优化] 开始预加载常用地图...");
        
        for (int mapId : mapIds) {
            try {
                // 这里可以预加载地图的基本信息
                System.out.println("[地图优化] 预加载地图: " + mapId);
                
                // 避免预加载过多导致内存问题
                if (mapCache.size() >= MAX_MAP_CACHE) {
                    break;
                }
            } catch (Exception e) {
                System.err.println("[地图优化] 预加载地图失败: " + mapId);
            }
        }
        
        System.out.println("[地图优化] 预加载完成");
    }
    
    /**
     * 关闭优化器
     */
    public static void shutdown() {
        mapCleaner.shutdown();
        forceCleanupMapMemory();
    }
}
