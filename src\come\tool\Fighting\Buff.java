package come.tool.Fighting;

import java.awt.Graphics;

import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;

//化无 遗患 悬刃
//双管齐下 讨命
//技能与讨命技能存在覆盖关系,同时此技能在场时讨命技能无效,也不会和遗患 悬刃伤害叠加。
public class Buff {
    //特效id
	private int id;
	//buff类型
	private String type;
	//归属阵营
	private int camp;
	//特效皮肤
	private String skin;
    //特效时间
	private long time;
	//是否显示特效
	private boolean isv;
	//位置
	private int x;
	private int y;
	public Buff(String[] v) {
		// TODO Auto-generated constructor stub
		this.id=Integer.parseInt(v[1]);
		this.camp=Integer.parseInt(v[2]);
		this.type=v[3];
		this.skin=GetTcpPath.getBuffTcp(v[3]);
	}
	public void draw(Graphics g){
		time+=18;
		if (isv) {
			Sprite sprite=SpriteFactory.Prepare(skin);
			if (sprite!=null) {
				sprite.updateToTime(time,0);
				sprite.draw(g, x, y);
			}
		}
	}
	public void draw2(Graphics g){
		if (isv) {
			Sprite sprite=SpriteFactory.Prepare(skin);
			if (sprite!=null) {
				sprite.updateToTime(time,0);
				sprite.draw(g, x, y);
			}
		}
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public int getCamp() {
		return camp;
	}
	public void setCamp(int camp) {
		this.camp = camp;
	}
	public String getSkin() {
		return skin;
	}
	public void setSkin(String skin) {
		this.skin = skin;
	}
	public long getTime() {
		return time;
	}
	public void setTime(long time) {
		this.time = time;
	}
	public boolean isIsv() {
		return isv;
	}
	public void setIsv(boolean isv) {
		this.isv = isv;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	
}
