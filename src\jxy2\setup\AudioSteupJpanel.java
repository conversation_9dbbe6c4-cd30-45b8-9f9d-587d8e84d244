package jxy2.setup;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.mouslisten.SystemMouslisten;
import org.come.until.Music;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
* 影音设置
* <AUTHOR>
* @date 2024/8/7 下午3:56
*/

public class AudioSteupJpanel extends JPanel {
    private JLabel labFullscreen,// 全屏模式
            labWindow,// 窗口模式
            labMusic,// 音乐
            labSound;// 音效
    private AudioSteupBtn pull_down_button,cacheClear,uiRepair;//
    private static JSlider jSlider,jSlider1;// 银两拉动条
    private AuidoSteuoBox optionJpanel;
    private JLabel resolutiontext;
    private JLabel[] labUi = new JLabel[3];
    private JLabel[] labfouts = new JLabel[3];
    private JLabel[] labScree = new JLabel[2];
    public int leftFlag = 1;
    public AudioSteupJpanel() {
        this.setLayout(null);
        this.setOpaque(false);
        //音乐大小控制滑块
        jSlider = new JSlider(SwingConstants.HORIZONTAL, 0, 100, 80);
        jSlider.setBounds(110, 251, 146, 18);
        jSlider.setBackground(UIUtils.Color_BACK);
//        jSlider.setUI(new MySliderUI());
        jSlider.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseReleased(MouseEvent e) {
                super.mouseReleased(e);
                float sec = jSlider.getValue();
                Music.setVolume(sec);
            }
        });
        this.add(jSlider);
        //音效大小控制滑块
        //音效大小控制滑块
        jSlider1 = new JSlider(SwingConstants.HORIZONTAL, 0, 100, 100);
        jSlider1.setBounds(110, 251+25, 146, 18);
        jSlider1.setBackground(UIUtils.Color_BACK);
        jSlider1.setVisible(true);
//        jSlider1.setUI(new MySliderUI());
        jSlider1.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseReleased(MouseEvent e) {
                super.mouseReleased(e);
                float sec = jSlider1.getValue();
                Music.setVolumex(sec);
            }
        });
        this.add(jSlider1);

        // 分辨率1024 X 768
        labFullscreen = new JLabel();
        labFullscreen.setBounds(264 + 22, 51 + 3, 16, 14);
        labFullscreen.addMouseListener(new AudioSteupMouslisten(this,1));
        this.add(labFullscreen);

        // 分辨率800 X 600
        labWindow = new JLabel();
        labWindow.setBounds(392 + 37, 51 + 3, 16, 14);
        labWindow.addMouseListener(new AudioSteupMouslisten(this,0));
        this.add(labWindow);

        // 音乐
        labMusic = new JLabel();
        labMusic.setBounds(23, 253, 16, 16);
        labMusic.addMouseListener(new AudioSteupMouslisten(this,2));
        this.add(labMusic);

        // 音效
        labSound = new JLabel();
        labSound.setBounds(23, 253+25, 16, 16);
        labSound.addMouseListener(new AudioSteupMouslisten(this,3));
        this.add(labSound);

        pull_down_button = new AudioSteupBtn(ImgConstants.tz106, 1, UIUtils.COLOR_BTNTEXT, UIUtils.FZCY_HY14, "", 0, this, "");
        pull_down_button.setBounds(150, 79, 18, 18);
        this.add(pull_down_button);

        cacheClear = new AudioSteupBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "清理缓存", 1, this, "点击一次足够，多点无义");
        cacheClear.setBounds(17, 311, 82,29);
        this.add(cacheClear);
        uiRepair = new AudioSteupBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "UI修复", 2, this, "当UI出现白色边框后点击即可恢复");
        uiRepair.setBounds(17+90, 311, 82,29);
        this.add(uiRepair);


        String[] resolutionData = {"800x600","1024x768","1366x768"};
        optionJpanel = new AuidoSteuoBox(80, 66, resolutionData);
        optionJpanel.getJlist().addMouseListener(new MouseAdapter() {
                 @Override
                 public void mousePressed(MouseEvent e) {
                     resolutiontext.setText(optionJpanel.getJlist().getSelectedValue());
                     SystemMouslisten.type12(optionJpanel.getJlist().getSelectedValue());
                     optionJpanel.setVisible(false);
                     leftFlag = 1;
                     SystemMouslisten.writeTxt();
                 }
             });
        optionJpanel.setVisible(false);
        optionJpanel.setBounds(68, 100, 80, 66);
        this.add(optionJpanel);

        resolutiontext  = new JLabel();
        resolutiontext.setForeground(UIUtils.COLOR_66BBAAFF);
        resolutiontext.setFont(UIUtils.FZCY_HY14);
        resolutiontext.setBounds(76, 78, 121, 20);
        add(resolutiontext);

        for (int i = 0; i < labUi.length; i++) {
            labUi[i] = new JLabel();
            labUi[i].setBounds(22+i*85, 109, 17, 17);
            labUi[i].addMouseListener(new AudioSteupMouslisten(this,4+i));
            this.add(labUi[i]);
        }
        for (int i = 0; i < labfouts.length; i++) {
            labfouts[i] = new JLabel();
            labfouts[i].setBounds(22+i*85, 139, 17, 17);
            labfouts[i].addMouseListener(new AudioSteupMouslisten(this,7+i));
            this.add(labfouts[i]);
        }

        for (int i = 0; i < labScree.length; i++) {
            labScree[i] = new JLabel();
            labScree[i].setBounds(172+i*50, 80, 17, 17);
            labScree[i].addMouseListener(new AudioSteupMouslisten(this,10+i));
            this.add(labScree[i]);
        }
    }

    public String[] getNames() {
        return new String[]{ "音乐开启", "音效开启"};
    }
    public String[] interfacialData = {"翡翠青山","水墨山河","红木返璞"};
    public String[] fontsizeData = {"宋体","方正粗圆","微软雅黑"};
    public String[] Scree  = {"全屏","窗口"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz22, 15, 235, 270, 90, 1);

        for (int i = 0; i < getNames().length; i++) {
            Juitil.TextBackground(g, getNames()[i], 14, 40, 251+i*25, UIUtils.COLOR_CL_Setting, UIUtils.FZCY_HY14);
            g.drawImage(Juitil.tz83.getImage(),23,253+i*25,14,14,null);
        }

        Juitil.TextBackground(g, "分辨率", 14, 21, 80, UIUtils.COLOR_CL_Setting, UIUtils.FZCY_HY14);
        Juitil.ImngBack(g, Juitil.tz26, 68, 76, 80, 23, 1);
        g.drawImage(Juitil.tz184.getImage(),110,251,146,18,null);
        g.drawImage(Juitil.tz184.getImage(),110,283,146,18,null);

        for (int i = 0; i < 3; i++) {
            g.drawImage(Juitil.tz83.getImage(),23+i*85,110,14,14,null);
            Juitil.TextBackground(g, interfacialData[i], 14, 40+i*85, 108, UIUtils.COLOR_CL_Setting, UIUtils.FZCY_HY14);

            g.drawImage(Juitil.tz83.getImage(),23+i*85,140,14,14,null);
            Juitil.TextBackground(g, fontsizeData[i], 14, 40+i*85, 138, UIUtils.COLOR_CL_Setting, UIUtils.FZCY_HY14);
        }

        for (int i = 0; i < 2; i++) {
            g.drawImage(Juitil.tz83.getImage(),172+i*50,80,14,14,null);
            Juitil.TextBackground(g, Scree[i], 14, 187+i*50, 78, UIUtils.COLOR_CL_Setting, UIUtils.FZCY_HY14);


        }
    }


    public JLabel getLabSound() {
        return labSound;
    }

    public void setLabSound(JLabel labSound) {
        this.labSound = labSound;
    }

    public JLabel getLabFullscreen() {
        return labFullscreen;
    }

    public void setLabFullscreen(JLabel labFullscreen) {
        this.labFullscreen = labFullscreen;
    }

    public JLabel getLabWindow() {
        return labWindow;
    }

    public void setLabWindow(JLabel labWindow) {
        this.labWindow = labWindow;
    }

    public JLabel getLabMusic() {
        return labMusic;
    }

    public void setLabMusic(JLabel labMusic) {
        this.labMusic = labMusic;
    }

    public static JSlider getjSlider1() {
        return jSlider1;
    }

    public static void setjSlider1(JSlider jSlider1) {
        AudioSteupJpanel.jSlider1 = jSlider1;
    }

    public static JSlider getjSlider() {
        return jSlider;
    }

    public static void setjSlider(JSlider jSlider) {
        AudioSteupJpanel.jSlider = jSlider;
    }

    public AudioSteupBtn getPull_down_button() {
        return pull_down_button;
    }

    public void setPull_down_button(AudioSteupBtn pull_down_button) {
        this.pull_down_button = pull_down_button;
    }

    public AuidoSteuoBox getOptionJpanel() {
        return optionJpanel;
    }

    public void setOptionJpanel(AuidoSteuoBox optionJpanel) {
        this.optionJpanel = optionJpanel;
    }

    public JLabel getResolutiontext() {
        return resolutiontext;
    }

    public void setResolutiontext(JLabel resolutiontext) {
        this.resolutiontext = resolutiontext;
    }

    public JLabel[] getLabUi() {
        return labUi;
    }

    public void setLabUi(JLabel[] labUi) {
        this.labUi = labUi;
    }

    public JLabel[] getLabfouts() {
        return labfouts;
    }

    public void setLabfouts(JLabel[] labfouts) {
        this.labfouts = labfouts;
    }

    public JLabel[] getLabScree() {
        return labScree;
    }

    public void setLabScree(JLabel[] labScree) {
        this.labScree = labScree;
    }
}
