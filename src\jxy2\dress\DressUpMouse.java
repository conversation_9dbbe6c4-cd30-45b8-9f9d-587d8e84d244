package jxy2.dress;

import com.tool.role.RoleLingFa;
import jxy2.zodiac.ChineseZodiacFrame;
import jxy2.zodiac.ChineseZodiacPanel;
import org.come.Frame.MsgJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class DressUpMouse implements MouseListener {
    public int index,type;
    public DressUpJPanel dressUpJPanel;
    public DressUpMouse(int index,DressUpJPanel dressUpJPanel,int type) {
        this.index = index;
        this.type = type;
        this.dressUpJPanel=dressUpJPanel;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        switch (type) {
            case 1:
                if (DressUtil.LingBaoRid(index, dressUpJPanel.getType()) == null) return;
                Lingbao lingbao = RoleLingFa.getRoleLingFa().DressUp(DressUtil.LingBaoRid(index, dressUpJPanel.getType()));
                if (lingbao != null) {
                    ZhuFrame.getZhuJpanel().creatlingtext(lingbao);
                }
                break;
            case 0:
                if (DressUtil.EuRid(index, dressUpJPanel.getType()) == null) return;
                Goodstable goodstable = GoodsListFromServerUntil.getRgid(DressUtil.EuRid(index, dressUpJPanel.getType()));
                if (goodstable != null) {
                    ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
                }
                break;
            case 2:
                if (DressUtil.XpSkillRid(dressUpJPanel.getType()) == null) return;
                String[] vss = DressUtil.XpSkillRid(dressUpJPanel.getType());
                if (vss==null)return;
                String[] xpskillid = vss[1].split("_");
                Skill skillid = UserMessUntil.getSkillId(xpskillid[0]);
                if (skillid != null) {
                    ChineseZodiacPanel chineseZodiacPanel = ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel();
                    boolean is = chineseZodiacPanel.sumlist.get(0).equals(xpskillid[1]);
                    MsgJframe.getJframe().getJapnel().XPSkill(skillid,is);
                }
                break;
            case 3:
                if (DressUtil.XKSRid(dressUpJPanel.getType())==null)return;
                goodstable = GoodsListFromServerUntil.getRgid(DressUtil.XKSRid(dressUpJPanel.getType()));
                if (goodstable != null) {
                    ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
                }
                break;
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        switch (type) {
            case 0:
            case 3:
                ZhuFrame.getZhuJpanel().cleargoodtext();
                break;
            case 1:
                ZhuFrame.getZhuJpanel().clearlingtext();
                break;
            case 2:
                FormsManagement.HideForm(46);
                break;

        }
    }
}
