package com.tool.btn;

import com.tool.role.RoleData;
import jxy2.refine.GodJpanel;
import jxy2.refine.SymbolJpanel;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.RuneOperateJpanel;
import org.come.bean.NpcComposeBean;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DazaoBtn extends MoBanBtn {

    private GodJpanel godJpanel;
    private RuneOperateJpanel runeOperateJpanel;
    private SymbolJpanel symbolJpanel;

    public DazaoBtn(String iconpath, int type, Color[] colors, Font font,String text, GodJpanel godJpanel) {
        super(iconpath, type,0, colors,"");
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.godJpanel = godJpanel;
    }
    public DazaoBtn(String iconpath, int type, Color[] colors, Font font, String text,
            RuneOperateJpanel runeOperateJpanel) {
        super(iconpath, type, colors);
        // TODO Auto-generated constructor stub
        this.runeOperateJpanel = runeOperateJpanel;
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    public DazaoBtn(String iconpath, int type, Color[] colors, Font font, String text,
                    SymbolJpanel symbolJpanel) {
        super(iconpath, type, 0,colors,"");
        // TODO Auto-generated constructor stub
        this.symbolJpanel = symbolJpanel;
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {

        /**判断是否解锁*/
        if(Util.isCanBuyOrno()){
            return;
        }
        // 判断金钱
        // 判断合成左右槽都有物品
        String type = null;
        BigDecimal money = null;
        Goodstable[] goods = null;
        if (godJpanel != null) {
            type = godJpanel.getType();
            money = godJpanel.getMoney();
            goods = godJpanel.goods;
        } else if (symbolJpanel != null && symbolJpanel.getMinType() == 0) {// 我要洗练符石
            type = "我要洗练符石";
            money = symbolJpanel.getMoney();
            goods = symbolJpanel.goods;
        } else if (symbolJpanel != null && symbolJpanel.getMinType() == 1) {// 我要合成符石
            type = "我要合成符石";
            money = symbolJpanel.getMoney();
            goods = symbolJpanel.goods1;
        } else {
            return;
        }

        if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
            ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
            return;
        }
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("请凑齐物品再来");
                return;
            } else {
                if (goods[i].getGoodlock() == 1) {
                    ZhuFrame.getZhuJpanel().addPrompt2("该物品已被加锁");
                    return;
                }
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return;
                }
                if (Goodtype.EquipmentType(goods[i].getType()) != -1) {
                    if (AccessSuitMsgUntil.getExtra(goods[i].getValue(), BaptizeBtn.Extras[3]) != null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("套装无法用于打造系列");
                        return;
                    }
                    if (AccessSuitMsgUntil.getExtra(goods[i].getValue(), BaptizeBtn.Extras[4]) != null||(goods[i].getQhv()!=null&&goods[i].getQhv()>0)) {
                        ZhuFrame.getZhuJpanel().addPrompt2("已镶嵌宝石无法用于打造系列");
                        return;
                    }
                }
            }
        }
        //判断需求
        if (type(type, goods)) {
            return;
        }
        if (type.equals("我要升级神兵") || type.equals("精炼神兵")) {
            RoleData.getRoleData().getLoginResult()
                    .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));
            SuitOperBean operBean = new SuitOperBean();
            operBean.setType(16);
            List<BigDecimal> rgids = new ArrayList<>();
            for (int i = 0; i < goods.length; i++) {
                if (i == 0) {
                    goods[i].setUsetime(0);
                } else {
                    goods[i].setUsetime(goods[i].getUsetime() - 1);
                }
                rgids.add(goods[i].getRgid());
                if (goods[i].getUsetime() <= 0) {
                    GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                    goods[i] = null;
                    qkth(type, i);

                }
            }
            operBean.setGoods(rgids);
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            return;
        }

        UserData.uptael(money.intValue());
        List<BigDecimal> goodstables = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (i == 0) {
                goods[i].setUsetime(0);
            } else {
                goods[i].setUsetime(goods[i].getUsetime() - 1);
            }
            goodstables.add(goods[i].getRgid());
            if (goods[i].getUsetime() <= 0) {
                GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                goods[i] = null;
                qkth(type, i);
            }
        }
        NpcComposeBean npcComposeBean = new NpcComposeBean();
        npcComposeBean.setComposetype(type);
        npcComposeBean.setGoodstables(goodstables);
        String sendMes = Agreement.getAgreement().npccomposeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(npcComposeBean));
        SendMessageUntil.toServer(sendMes);// 向服务器发送信息
    }

    /** 界面清除或者替换 */
    public void qkth(String type, int i) {
        if (godJpanel != null) {
            godJpanel.ClickSuit(null,i + 24);
        } else if (symbolJpanel != null){// 我要洗练符石
            symbolJpanel.ClickSuit(null, i + 24);
        }
    }

    /** 判断调用那个合成方法 */
    public boolean type(String type, Goodstable[] goods) {
        if (type != null) {
            if (type.equals("我要合成仙器")) {
                return GoodItem_1(goods);
            } else if (type.equals("我要升级仙器")) {
                return GoodItem_2(goods);
            } else if (type.equals("我要洗炼仙器")) {
                return GoodItem_3(goods);
            } else if (type.equals("打造11-16级装备")) {
                return GoodItem_4(goods);
            } else if (type.equals("我要打造普通装备")) {
                return GoodItem_5(goods);
            } else if (type.equals("我要升级神兵")) {
                return GoodItem_6(goods);
            } else if (type.equals("我要合成炼妖石")) {
                return GoodItem_7(goods);
            } else if (type.equals("我要炼器")) {
                return GoodItem_8(goods);
            } else if (type.equals("我要培养饰品")) {
                return GoodItem_9(goods);
            } else if (type.equals("我要重铸饰品")) {
                return GoodItem_10(goods);
            } else if (type.equals("我要合成符石")) {
                return GoodItem_11(goods);
            } else if (type.equals("我要洗练符石")) {
                return GoodItem_12(goods);
            } else if (type.equals("炼化装备")) {
                return GoodItem_13(goods);
            } else if (type.equals("我要上神兵石") || type.equals("炼化神兵")) {
                return GoodItem_14(goods);
            } else if (type.equals("我要培养护身符")) {
                return GoodItem_15(goods);
            } else if (type.equals("我要重铸护身符")) {
                return GoodItem_16(goods);
            } else if (type.equals("炼化仙器")) {
                return GoodItem_17(goods);
            } else if (type.equals("培养彩晶石")) {
                return GoodItem_18(goods);
            } else if (type.equals("精炼神兵")) {
                return GoodItem_19(goods);
            }
        }
        return true;
    }

    /** 我要合成仙器 */
    public boolean GoodItem_1(Goodstable[] goods) {
        // 瓶子描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = null;
        if (Bottletext != null && !Bottletext.equals("")) {
            gongneng = Goodtype.StringParsing(Bottletext);
        }
        // 仙器阶数
        String god = Goodtype.StringParsing(goods[1].getValue())[0];
        // 判断是否是新瓶子
        if (gongneng == null) {
            if (god.equals("阶数=6")) {
                FrameMessageChangeJpanel.addtext(5, "6阶打进瓶子???哎!还是卖太便宜了", null, null);
                return true;
            }
        } else {
            if (!gongneng[0].equals(god)) {
                FrameMessageChangeJpanel.addtext(5, "阶数不相等", null, null);
                return true;
            } else if (ReikiFull(gongneng)) {// 判断瓶子是否满了
                FrameMessageChangeJpanel.addtext(5, "灵气已经满了", null, null);
                return true;
            } else if (AnalysisString.jiaoyi(goods[0].getQuality())!=AnalysisString.jiaoyi(goods[1].getQuality())) {
            	FrameMessageChangeJpanel.addtext(5, "绑定和不绑定不能混合", null, null);
            	return true;
            }
        }
        return false;
    }

    /** 我要升级仙器 */
    public boolean GoodItem_2(Goodstable[] goods) {
        // 瓶子描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = Goodtype.StringParsing(Bottletext);
        // 石头等级
        int kslvl = goods[1].getGoodsid().intValue() - 300;
        // 判断是否是新瓶子
        if (!ReikiFull(gongneng)) {
            FrameMessageChangeJpanel.addtext(5, "瓶子灵气未满！", null, null);
            return true;
        }
        int lvl = Integer.parseInt(gongneng[0].split("=")[1]);
        if (lvl + 5 != kslvl) {
            FrameMessageChangeJpanel.addtext(5, zw(lvl) + "阶仙器请用" + (lvl + 5) + "级矿石升级!", null, null);
            return true;
        } else if (lvl >= 6) {
            FrameMessageChangeJpanel.addtext(5, "不支持六阶仙器升级!", null, null);
            return true;
        } else {
            return false;
        }
    }

    /** 我要洗炼仙器 */
    public boolean GoodItem_3(Goodstable[] goods) {
        // 装备等级
        return false;
    }

    /** 打造11-16级装备 */
    public boolean GoodItem_4(Goodstable[] goods) {
        // 装备物品id
        int goodid = goods[0].getGoodsid().intValue();
        // 装备等级
        int zblvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (zblvl < 10) {
            FrameMessageChangeJpanel.addtext(5, "打造1-10级装备去长安城打铁铺找冯铁匠!", null, null);
            return true;
        }
        boolean up = false;
        if (zblvl >= 10 && zblvl <= 13) {
            if (kslvl != 8 && kslvl != 9) {
                FrameMessageChangeJpanel.addtext(5, "打造11-14级装备使用9级矿石!", null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸10-13级装备使用8级矿石!", null, null);
                return true;
            } else if (kslvl == 9) {
                up = true;
            }
        } else if (zblvl == 14) {
            if (kslvl != 9 && kslvl != 10) {
                FrameMessageChangeJpanel.addtext(5, "打造15级装备使用10级矿石!", null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸14级装备使用9级矿石!", null, null);
                return true;
            } else if (kslvl == 10) {
                up = true;
            }
        } else if (zblvl == 15) {
            if (kslvl != 10 && kslvl != 11) {
                FrameMessageChangeJpanel.addtext(5, "打造16级装备使用11级矿石!", null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸15级装备使用10级矿石!", null, null);
                return true;
            } else if (kslvl == 11) {
                up = true;
            }
        } else if (zblvl == 16) {
            if (kslvl != 11) {
                FrameMessageChangeJpanel.addtext(5, "重铸16级装备使用11级矿石!", null, null);
                return true;
            }
        } else {
            FrameMessageChangeJpanel.addtext(5, "错误公式", null, null);
            return true;
        }
        return false;
    }

    /** 我要打造普通装备 */
    public boolean GoodItem_5(Goodstable[] goods) {
        // 装备物品id
        int goodid = goods[0].getGoodsid().intValue();
        // 装备等级
        int zblvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (zblvl >= 10) {
            FrameMessageChangeJpanel.addtext(5, "打造11-16级装备去长安桥头!", null, null);
            return true;
        } else if (kslvl > 9) {
            FrameMessageChangeJpanel.addtext(5, "打造1-10级装备最高只能使用9级矿石!", null, null);
            return true;
        }
        return false;
    }

    /** 我要升级神兵 */
    public boolean GoodItem_6(Goodstable[] goods) {
        // 神兵描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = Goodtype.StringParsing(Bottletext);
        int godlvl = Numerical(gongneng[0]);
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (godlvl < 1 || godlvl > 5) {
            FrameMessageChangeJpanel.addtext(5, "不支持6级神兵升级!", null, null);
            return true;
        }
        if (godlvl != (kslvl - 5)) {
            FrameMessageChangeJpanel.addtext(5, "请使用" + (godlvl + 5) + "级矿石进行升级!", null, null);
            return true;
        }
        return false;
    }

    /** 我要合成炼妖石 */
    public boolean GoodItem_7(Goodstable[] goods) {
        return false;
    }

    /** 我要炼器 */
    public boolean GoodItem_8(Goodstable[] goods) {
        return true;
    }

    /** 我要培养饰品 */
    public boolean GoodItem_9(Goodstable[] goods) {
        // 饰品描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = Goodtype.StringParsing(Bottletext);

        int max = 0;
        int flvl = 0;
        for (int i = 0; i < gongneng.length; i++) {
            if (gongneng[i].length() >= 2 && gongneng[i].substring(0, 2).equals("培养")) {
                String[] num = gongneng[i].split("=")[1].split("/");
                max = Integer.parseInt(num[1]);
            }
        }
        if (max == 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("该配饰无法培养");
            return true;
        }
        if (Goodtype.Accessories(goods[1].getType())) {
            String[] vs = goods[1].getValue().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                String[] v = vs[i].split("=");
                if (v[0].equals("等级")) {
                    flvl = Integer.parseInt(v[1]);
                }
            }
            if (flvl > 2) {
                ZhuFrame.getZhuJpanel().addPrompt2("无法用2级以上的配饰培养");
                return true;
            }
        }
        if (Numerical(gongneng[0]) <= 6) {
            return false;
        } else {
            FrameMessageChangeJpanel.addtext(5, "当前饰品无法升级!", null, null);
            return true;
        }

    }

    /**
     * 我要重铸饰品
     */
    public boolean GoodItem_10(Goodstable[] goods) {
        // 饰品描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = Goodtype.StringParsing(Bottletext);
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (Numerical(gongneng[0]) == (kslvl - 3)) {
            return false;
        } else {
            FrameMessageChangeJpanel.addtext(5, "请使用相应的矿石进行重铸!", null, null);
            return true;
        }
    }

    /**
     * 我要合成符石
     */
    public boolean GoodItem_11(Goodstable[] goods) {
        int lvl = Numerical(Goodtype.StringParsing(goods[0].getValue())[0]);
        if (lvl >= 5) {
            FrameMessageChangeJpanel.addtext(5, "不支持5级以上合成", null, null);
            return true;
        }
        for (int i = 1; i < goods.length; i++) {
            if (lvl != Numerical(Goodtype.StringParsing(goods[i].getValue())[0])) {
                FrameMessageChangeJpanel.addtext(5, "合成物品需要同等级", null, null);
                return true;
            }
        }
        lvl++;
        return false;
    }

    /**
     * 我要洗练符石
     */
    public boolean GoodItem_12(Goodstable[] goods) {
        // 第一个符石的等级
        int ore_1 = Numerical(Goodtype.StringParsing(goods[0].getValue())[0]);
        int ore_2 = Numerical(Goodtype.StringParsing(goods[1].getValue())[0]);
        if (ore_1 == 1 || ore_1 == 2 || ore_1 == 3) {
            FrameMessageChangeJpanel.addtext(5, "1,2,3级符石不能洗练", null, null);
            return true;
        }
        if (ore_1 - 3 != ore_2) {
            FrameMessageChangeJpanel.addtext(5, ore_1 + "级符石用" + (ore_1 - 3) + "符石洗练", null, null);
            return true;
        }
        return false;
    }

    private boolean GoodItem_13(Goodstable[] goods) {
        // TODO Auto-generated method stub
        return false;
    }

    private boolean GoodItem_14(Goodstable[] goods) {
        // TODO Auto-generated method stub
        if (godJpanel.getType().equals("我要上神兵石")) {
            String value = goods[0].getValue(); // 获取装备等级
            if (value.indexOf("神兵属性") != -1) {
                FrameMessageChangeJpanel.addtext(5, "该装备已经有上了神兵石了", null, null);
                return true;
            }
        }
        return false;
    }

    private boolean GoodItem_15(Goodstable[] goods) {
        // TODO Auto-generated method stub
        String[] vs = goods[0].getValue().split("\\|");
        int pz = 0;
        for (int i = 0; i < vs.length; i++) {
            String[] vsz = vs[i].split("=");
            if (vsz[0].equals("品质")) {
                pz = Integer.parseInt(vsz[1].split("/")[0]);
                break;
            }
        }
        int up = 800;
        String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼化属性");
        if (extra != null) {
            String[] vvs = extra.split("&");
            s: for (int i = 0; i < vvs.length; i++) {
                String[] vvvs = vvs[i].split("=");
                if (vvvs[0].equals("特技")) {
                    for (int j = 1; j < vvvs.length; j++) {
                        if (vvvs[j].equals("8031")) {
                            up = 900;
                            break s;
                        }
                    }
                }
            }
        }
        if (pz > up) {
            FrameMessageChangeJpanel.addtext(5, "该护身符品质大于" + up + "后无法培养", null, null);
            return true;
        }
        return false;
    }

    private boolean GoodItem_16(Goodstable[] goods) {
        // TODO Auto-generated method stub
        String[] vs = goods[0].getValue().split("\\|");
        int pz = 0;
        for (int i = 0; i < vs.length; i++) {
            String[] vsz = vs[i].split("=");
            if (vsz[0].equals("品质")) {
                pz = Integer.parseInt(vsz[1].split("/")[0]);
                break;
            }
        }
        if (pz < 300) {
            FrameMessageChangeJpanel.addtext(5, "该护身符品质低于300的需要培养", null, null);
            return true;
        }
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (kslvl != 9 && kslvl != 10) {
            FrameMessageChangeJpanel.addtext(5, "护身符重铸使用9级矿石!", null, null);
            FrameMessageChangeJpanel.addtext(5, "护身符升级使用10级矿石!", null, null);
            return true;
        }
        int lvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
        if (kslvl == 10) {
            lvl++;
            if (lvl > 7) {
                FrameMessageChangeJpanel.addtext(5, "护身符等级最高为7级", null, null);
                return true;
            }
        }
        return false;
    }

    private boolean GoodItem_17(Goodstable[] goods) {
        // 仙器阶数
        if (goods[1].getType() == 7005) {
            String god = Goodtype.StringParsing(goods[1].getValue())[1];
            if (!god.equals("阶数=1")) {
                FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                return true;
            }
        } else {
            String god = Goodtype.StringParsing(goods[1].getValue())[0];
            if (!god.equals("阶数=1")) {
                FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                return true;
            }
        }
        return false;
    }

    private boolean GoodItem_18(Goodstable[] goods) {
        return false;
    }

    private boolean GoodItem_19(Goodstable[] goods) {
        // 神兵描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = Goodtype.StringParsing(Bottletext);
        int godlvl = Numerical(gongneng[0]);
        if (godlvl < 1 || godlvl > 5) {
            FrameMessageChangeJpanel.addtext(5, "不支持6级神兵升级!", null, null);
            return true;
        }else if (godlvl<=3) {
        	  FrameMessageChangeJpanel.addtext(5, "4级及以上的神兵才可以精练", null, null);
              return true;
		}
        int godlvl2 = Numerical(goods[1].getValue().split("\\|")[0]);

        if (godlvl2 > 3) {
            FrameMessageChangeJpanel.addtext(5, "用3级以下的神兵精练", null, null);
            return true;
        }
        return false;
    }

    /** 返回普通装备等级 */
    public int zblvl(int id) {
        // 普通装备id由1001-5016
        return ((id - 1001) % 16) + 1;
    }

    /** 判断瓶子灵气是否满了 */
    public boolean ReikiFull(String[] vlaue) {
        if (vlaue[0].equals("阶数=1") || vlaue[0].equals("阶数=2")) {
            if (Reikisum(vlaue[1]) >= 8) {
                return true;
            }
        } else if (vlaue[0].equals("阶数=3")) {
            if (Reikisum(vlaue[1]) >= 6) {
                return true;
            }
        } else if (vlaue[0].equals("阶数=4")) {
            if (Reikisum(vlaue[1]) >= 5) {
                return true;
            }
        } else {
            if (Reikisum(vlaue[1]) >= 3) {
                return true;
            }
        }
        return false;
    }

    /** 放回阶数的中文名称 */
    public String zw(int lvl) {
        switch (lvl) {
        case 1:
            return "一";
        case 2:
            return "二";
        case 3:
            return "三";
        case 4:
            return "四";
        case 5:
            return "五";
        case 6:
            return "六";
        case 7:
            return "七";
        case 8:
            return "八";
        case 9:
            return "九";
        case 10:
            return "十";
        }
        return "零";
    }

    /** 返回等号后面的值 */
    public static int Numerical(String vlaue) {
        if (vlaue.split("\\=").length == 1) {
            return 0;
        }
        return Integer.parseInt(vlaue.split("\\=")[1]);
    }

    /** 判断灵气点数 */
    public int Reikisum(String vlaue) {
        Pattern pattern = Pattern.compile("=(.*?)点");// 匹配的模式
        Matcher m = pattern.matcher(vlaue);
        while (m.find()) {
            int i = 1;
            return Integer.parseInt(m.group(i));
        }
        return 0;
    }
}
