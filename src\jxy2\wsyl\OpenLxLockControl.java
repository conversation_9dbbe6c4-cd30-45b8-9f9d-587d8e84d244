package jxy2.wsyl;

import org.come.action.FromServerAction;
import org.come.entity.RoleSummoning;
import org.come.until.GsonUtil;

public class OpenLxLockControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        RoleSummoning pet = GsonUtil.getGsonUtil().getgson().fromJson(mes,RoleSummoning.class);
        WsylJPanel wsylJPanel = WsylJFrame.getWsylJFrame().getWsylJPanel();
        wsylJPanel.init();
        LXiulianMainFrame.getLXiulianMainFrame().getLXiulianMainPanel().panelGetData(pet,wsylJPanel);
    }
}
