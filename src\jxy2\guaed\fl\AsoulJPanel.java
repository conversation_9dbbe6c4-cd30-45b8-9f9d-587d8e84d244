package jxy2.guaed.fl;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.Config;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.GoodsMsgJpanel;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
* 守护石附灵界面
* <AUTHOR>
* @date 2024/12/24 上午9:45
*/

public class AsoulJPanel extends JPanel implements IValueUpdateListener {
    private FuLingBtn plus;
    private JLabel addGoodsImg,addStoneImg;
    private Goodstable goodstable,lyjgoods;
    public AsoulBtn oneclickupbtn,spage,determine,upgradeBtn;
    public boolean enchanting = false;
    public boolean newProperty = true;
    public boolean xz = true;
    public Goodstable Newitemattributes;
    public Goodstable xzgoods;
    public Goodstable jtgoods;
    private int[] forging_grade = new int[2];
    //加载守护石
    public AsoulJPanel() {
        this.setPreferredSize(new Dimension(633, 507));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        // 注册为监听器
        Updatenumericalvalues.getInstance().addListener(this);
        plus = new FuLingBtn(ImgConstants.tz310, 1, UIUtils.COLOR_ZHUJPANEL, UIUtils.MSYH_HY14, "", 3, this, "");
        plus.setBounds(85, 184, 20, 20);
        this.add(plus);

        addGoodsImg = new JLabel();
        addGoodsImg.setBounds(70, 169, 48, 48);
        addGoodsImg.addMouseListener(new AsoulMousetr(this,0));
        addGoodsImg.setVisible(false);
        this.add(addGoodsImg);

        addStoneImg = new JLabel();
        addStoneImg.setBounds(215, 168, 48, 48);
        addStoneImg.addMouseListener(new AsoulMousetr(this,1));
        addStoneImg.setVisible(false);
        this.add(addStoneImg);

        oneclickupbtn =new AsoulBtn(ImgConstants.tz85, 1,  1, "开始附灵", this,"");
        oneclickupbtn.setBounds(288, 440, 82,29);
        add(oneclickupbtn);

        spage =new AsoulBtn(ImgConstants.tz227, 1,  2, "", this,"");
        spage.setVisible(false);
        spage.setBounds(46, 36, 26,129);
        add(spage);

        determine =new AsoulBtn(ImgConstants.tz34, 1,  3, "确 定", this,"");
        determine.setVisible(false);
        determine.setBounds(288, 440, 59,24);
        add(determine);

        upgradeBtn = new AsoulBtn(ImgConstants.tz303, 1,  "继续附灵", this,"",4);
        upgradeBtn.setVisible(false);
        upgradeBtn.setBounds(480, 80, 120, 33);
        add(upgradeBtn);

        checkLingyuan();
    }

    /**设置组件可见度*/
    public void setComponentVisible(boolean visible,ImageIcon icon) {
       plus.setVisible(visible&&!enchanting);
       addGoodsImg.setIcon(icon);
       //判断灵元晶
        checkLingyuan();
    }
    public static Map<String, Integer> valuesMap = new HashMap<>();
    static {
        valuesMap.put("附加攻击", 4000);
        valuesMap.put("附加气血", 6000);
        valuesMap.put("附加魔法", 6000);
        valuesMap.put("附加速度", 50);
        valuesMap.put("抗混乱", 10);
        valuesMap.put("抗封印", 10);
        valuesMap.put("抗昏睡", 10);
        valuesMap.put("抗遗忘", 10);
        valuesMap.put("抗三尸虫", 3000);
        valuesMap.put("抗毒伤害", 8000);
        valuesMap.put("抗震慑", 8);
        valuesMap.put("物理吸收率", 15);
        valuesMap.put("防御", 10000);
        valuesMap.put("抗风法狂暴几率", 15);
        valuesMap.put("抗雷法狂暴几率", 15);
        valuesMap.put("抗水法狂暴几率", 15);
        valuesMap.put("抗火法狂暴几率", 15);
        valuesMap.put("抗鬼火狂暴几率", 15);
        valuesMap.put("抗三尸狂暴几率", 15);
        valuesMap.put("抗物理狂暴几率", 15);
        valuesMap.put("抗致命几率", 12);
        valuesMap.put("忽视防御几率", 6);
        valuesMap.put("忽视防御程度", 3);
        valuesMap.put("连击率", 8);
        valuesMap.put("致命几率", 8);
        valuesMap.put("命中率", 8);
        valuesMap.put("狂暴几率", 8);
        valuesMap.put("反震率", 15);
        valuesMap.put("反震程度", 15);
        valuesMap.put("强力克木", 15);
        valuesMap.put("强力克水", 15);
        valuesMap.put("强力克火", 15);
        valuesMap.put("强力克金", 15);
        valuesMap.put("强力克土", 15);
        valuesMap.put("对无属性目标伤害", 10);
        valuesMap.put("抗强力克木", 20);
        valuesMap.put("抗强力克水", 20);
        valuesMap.put("抗强力克火", 20);
        valuesMap.put("抗强力克金", 20);
        valuesMap.put("抗强力克土", 20);
        valuesMap.put("忽视抗风几率", 5);
        valuesMap.put("忽视抗雷几率", 5);
        valuesMap.put("忽视抗水几率", 5);
        valuesMap.put("忽视抗火几率", 5);
        valuesMap.put("忽视鬼火几率", 5);
        valuesMap.put("加强火", 15);
        valuesMap.put("加强风", 15);
        valuesMap.put("加强雷", 15);
        valuesMap.put("加强水", 15);
        valuesMap.put("加强鬼火",15);
        valuesMap.put("躲闪率",5);
        valuesMap.put("抗灵宝伤害",15);
        valuesMap.put("每回合HP",4000);
        valuesMap.put("每回合MP",2000);
        valuesMap.put("抗反震",10000);
    }
    /**初始化检测灵元晶是否存在*/
    public void checkLingyuan() {
            Goodstable goods = GoodsListFromServerUntil.getGoods(new BigDecimal(81362));
            addStoneImg.setVisible(goods != null&&!enchanting);
            addGoodsImg.setVisible(!plus.isVisible()&&!enchanting);
            spage.setVisible(enchanting);
            if (goods != null&&addGoodsImg.getIcon()!=null) {
                ImageIcon icons = GoodsListFromServerUntil.imgpathWdfile(goods.getSkin(), 48, 48);
                addStoneImg.setIcon(icons);
                lyjgoods = goods;
            }
    }


    private BigDecimal money = new BigDecimal(Config.getInt("SpendGuardianMoney_Value3"));
    @Override
    public void onValueUpdate() {
        System.out.println("RonglianJPanel正在更新数值...");
        System.out.println("守护之尘旧值: " + this.money);

        this.money = new BigDecimal(Config.getInt("SpendGuardianMoney_Value3"));
    }
    public String[] monuy = {"消耗金钱","拥有金钱"};
    public String[] names = {"守护石","灵元晶"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (!enchanting) {
            g.drawImage(Juitil.tz314.getImage(), 326, 117, 260, 248, null);
            for (int i = 0; i < 2; i++) {
                g.drawImage(Juitil.tz316.getImage(), 70 + i * 144, 168, 52, 52, null);
                Juitil.TextBackground(g, monuy[i], 16, 66, 252 + i * 30, UIUtils.COLOR_NAME_BACKGROUND, UIUtils.NEWTX_HY16B, UIUtils.Color_BACK);
                Juitil.ImngBack(g, Juitil.tz128, 143, 254 + i * 30, 132, 20, 1);
                Juitil.TextBackground(g, names[i], 16, 70 + i * 144, 134, UIUtils.COLOR_NAME_BACKGROUND, UIUtils.NEWTX_HY16B, UIUtils.Color_BACK);
            }
            BigDecimal money = RoleData.getRoleData().getLoginResult().getGold();
            Util.drawPrice(g, money, 152, 300, UIUtils.LiSu_LS15);
            Util.drawPrice(g, this.money, 152, 300 - 30, UIUtils.LiSu_LS15);
            g.drawImage(Juitil.tz317.getImage(), 80, 233, 153, 4, null);
            g.drawImage(Juitil.tz312.getImage(), 161, 175, 30, 42, null);
            if (addGoodsImg.getIcon() == null || addStoneImg.getIcon() == null) {
                Juitil.TextBackground(g, "请选择需要附灵的守护石", 13, 372, 235, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
            }

            if (addGoodsImg.getIcon() != null && addStoneImg.getIcon() != null) {
                RefiningAttributeDisplay(g,498,139,28,372,345,132,goodstable);
            }
        }else {
            for (int i = 0; i < 2; i++) {
                g.drawImage(Juitil.tz314.getImage(), 35+i*300, 127, 260, 248, null);
            }
            g.drawImage(Juitil.tz320.getImage(), 70, 88, 510, 16, null);
            Juitil.TextBackground(g, "使用当前材料，你还可以附灵"+GoodsListFromServerUntil.getGoodNum(BigDecimal.valueOf(81362))+"次", 13, 188, 89, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT15, UIUtils.Color_BACK);
            Juitil.TextBackground(g, "原属性", 22, 130, 140, UIUtils.COLOR_SHS, UIUtils.NEWTX_HY22, UIUtils.COLOR_SHS_2);
            Juitil.TextBackground(g, "新属性", 22, 430, 140, UIUtils.COLOR_TREASURE, UIUtils.NEWTX_HY22, UIUtils.COLOR_SHS_2);
            //炼化属性展示
            RefiningAttributeDisplay(g,203,172,24,75,50,165,goodstable);
        }

        if (!xz){
            RefiningAttributeDisplay(g,498,172,24,372,345,165,xzgoods);
        }

        if (!newProperty&&enchanting&&xz){
            RefiningAttributeDisplay(g,498,172,24,372,345,165,Newitemattributes);
        }


    }

    // 静态变量定义
    private static int[] forgeGrades = new int[2];
    public String key,value;
    /**守护石炼化属性展示*/
    public void RefiningAttributeDisplay(Graphics g,int x,int y,int spacing,int txtx,int Titleicon,int TitleiconX,Goodstable goodstable) {
        int offset = 0,gs= 0;
        // 根据x坐标判断是左侧还是右侧的物品
        int forgeGradeIndex = (x >= 400) ? 1 : 0;  // x >= 400 表示右侧物品
        // 解析物品的属性
        String[] properties = goodstable.getValue().split("&");
        for (String property : properties) {
            if (property.startsWith("特技")){
                String[] zhi = property.split("=");
                if (zhi[0].equals("特技")) {
                    for (int k = 1; k < zhi.length; k++) {
                        Skill skill = UserMessUntil.getSkillId(zhi[k]);
                        if (skill != null) {
                            Juitil.TextBackground(g, skill.getSkillname()+"（等级 15）", 13, txtx, y + (!enchanting?128:117) + k * spacing, UIUtils.tjcoor, UIUtils.TEXT_FONT1);
                            gs = k;
                        }
                    }
                }
            }else {
                String[] entries = property.split("\\|");
                for (String entry : entries) {
                    String[] keyValue = entry.split("=");
                    if (keyValue.length != 2) {
                        continue;
                    }
                    key = keyValue[0];
                    value = keyValue[1];
                    
                    if (key.startsWith("锻造等级")) {
                        Pattern pattern = Pattern.compile("（(\\d+)）");
                        Matcher matcher = pattern.matcher(value);
                        if (matcher.find()) {
                            try {
                                int grade = Integer.parseInt(matcher.group(1));
                                forgeGrades[forgeGradeIndex] = grade;
                            } catch (Exception e) {
                                System.err.println("锻造等级解析错误: " + e.getMessage());
                            }
                        }
                        continue;
                    }
                    if (key.startsWith("耐久度")) {
                        continue; // 忽略特定的属性
                    }
                    Juitil.TextBackground(g, key, 13, txtx, y + offset * spacing, UIUtils.COLOR_SHS, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
                    Juitil.ImngBack(g, Juitil.tz128, x, y + offset * spacing, 69, 16, 1);
                    int maxValue = valuesMap.get(key);
                    double currentValue = Double.parseDouble(value); // 解析当前值为整数
                    // 计算比例
                    double ratio = currentValue / maxValue;
                    // 根据比例计算宽度
                    int width = (int) (58 * ratio); // 最大宽度为 68 像素
                    Juitil.ImngBack(g, Juitil.tz318, x+1, y+1 + offset * spacing, width + 10, 14, 1);
                    Juitil.TextBackground(g,value+ GoodsMsgJpanel.tianjia(key), 14, x+10, y-1 + offset * spacing, UIUtils.COLOR_SHS_1, UIUtils.TEXT_FONT1, UIUtils.COLOR_SHS_2);
                    offset++;
                }
            }
        }
        int remainingSlots = 5 - offset;
        int min = 5;
        for (int i = 0; i < min; i++) {
            offset =  i *spacing;
            g.drawImage((min - remainingSlots) <= i ? Juitil.tz322.getImage() : Juitil.tz321.getImage(), Titleicon, TitleiconX + offset, 27, 32, null);
            Juitil.TextBackground(g, (min - remainingSlots) <= i ? "灵窍无灵气" : "", 14, txtx, y + offset, UIUtils.COLOR_FM_gaormount, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
        }

        int tuow = 2 - gs;
        int max = 2;
        for (int i = 0; i < max; i++) {
            offset =  i * spacing + (!enchanting?15:0);
            g.drawImage((max - tuow) <= i ? Juitil.tz322.getImage() : Juitil.tz321.getImage(), Titleicon, 141+TitleiconX + offset, 27, 32, null);
            Juitil.TextBackground(g, (max - tuow) <= i ? "灵窍无灵气" : "", 14, txtx, y+ 141 + offset, UIUtils.COLOR_FM_gaormount, UIUtils.TEXT_FONT1, UIUtils.Color_BACK);
        }

    }
    // 提供一个静态方法获取锻造等级
    public static int[] getForgeGrades() {
        return Arrays.copyOf(forgeGrades, forgeGrades.length);
    }

    public String[] getNames() {
        return names;
    }

    public void setNames(String[] names) {
        this.names = names;
    }

    public String[] getMonuy() {
        return monuy;
    }

    public void setMonuy(String[] monuy) {
        this.monuy = monuy;
    }

    public FuLingBtn getPlus() {
        return plus;
    }

    public void setPlus(FuLingBtn plus) {
        this.plus = plus;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public JLabel getAddGoodsImg() {
        return addGoodsImg;
    }

    public void setAddGoodsImg(JLabel addGoodsImg) {
        this.addGoodsImg = addGoodsImg;
    }

    public JLabel getAddStoneImg() {
        return addStoneImg;
    }

    public void setAddStoneImg(JLabel addStoneImg) {
        this.addStoneImg = addStoneImg;
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    public Goodstable getLyjgoods() {
        return lyjgoods;
    }

    public void setLyjgoods(Goodstable lyjgoods) {
        this.lyjgoods = lyjgoods;
    }

    public AsoulBtn getOneclickupbtn() {
        return oneclickupbtn;
    }

    public void setOneclickupbtn(AsoulBtn oneclickupbtn) {
        this.oneclickupbtn = oneclickupbtn;
    }

    public AsoulBtn getSpage() {
        return spage;
    }

    public void setSpage(AsoulBtn spage) {
        this.spage = spage;
    }

    public boolean isEnchanting() {
        return enchanting;
    }

    public void setEnchanting(boolean enchanting) {
        this.enchanting = enchanting;
    }

    public static Map<String, Integer> getValuesMap() {
        return valuesMap;
    }

    public static void setValuesMap(Map<String, Integer> valuesMap) {
        AsoulJPanel.valuesMap = valuesMap;
    }

    public AsoulBtn getDetermine() {
        return determine;
    }

    public void setDetermine(AsoulBtn determine) {
        this.determine = determine;
    }

    public AsoulBtn getUpgradeBtn() {
        return upgradeBtn;
    }

    public void setUpgradeBtn(AsoulBtn upgradeBtn) {
        this.upgradeBtn = upgradeBtn;
    }

    public Goodstable getNewitemattributes() {
        return Newitemattributes;
    }

    public void setNewitemattributes(Goodstable newitemattributes) {
        Newitemattributes = newitemattributes;
    }

    public boolean isNewProperty() {
        return newProperty;
    }

    public void setNewProperty(boolean newProperty) {
        this.newProperty = newProperty;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isXz() {
        return xz;
    }

    public void setXz(boolean xz) {
        this.xz = xz;
    }

    public Goodstable getXzgoods() {
        return xzgoods;
    }

    public void setXzgoods(Goodstable xzgoods) {
        this.xzgoods = xzgoods;
    }

    public Goodstable getJtgoods() {
        return jtgoods;
    }

    public void setJtgoods(Goodstable jtgoods) {
        this.jtgoods = jtgoods;
    }

    public int[] getForging_grade() {
        return forging_grade;
    }

    public void setForging_grade(int[] forging_grade) {
        this.forging_grade = forging_grade;
    }
}
