package com.tool.tcpimg;

import jxy2.chatv.ChatRichLabel;
import jxy2.jutnil.ImgConstants;
import org.come.bean.ImgZoom;
import org.come.test.Main;
import org.come.until.CutButtonImage;
import org.come.until.ScrenceUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

/**
 * 对话框
 * 
 * <AUTHOR>
 * 
 */
public class ChatBox extends JPanel {
	private static final long serialVersionUID = 1L;
	private List<RichLabel> labels;
	private List<ChatRichLabel> chatlabels;
	// 显示偏差
	private int deviation = 0;
	// 是否禁言 flase禁言
	private boolean Gag = true;
	// 屏幕透明度
	private float Alpha = 0.0f;
	// 判断显示位置(显示在左 true)
	private boolean display = false;
	// 聊天框的大小
	private int w = 0;
	private int h = 0;

	public ChatBox() {
		labels = new ArrayList<>();
		chatlabels = new ArrayList<>();
		this.setBackground(UIUtils.Color_BACK);
	}

	/** 监听 */
	public InputBean isMonitor(int x, int y, MouseEvent e) {
		int History = 0;
		int hig = -deviation * 22;
		for (int i = 0; i < labels.size(); i++) {
			RichLabel c = labels.get(i);
			History = c.getHeight();
			hig += History;
			if (hig >= 0 && hig >= y) {
				return c.isMonitor(x, y - (hig - History),e);
			}
		}
		return null;
	}

	@Override
	public void paint(Graphics g) {
		if (labels.size() != 0) {
			Graphics2D g2d = (Graphics2D) g.create();
			g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, Alpha));
			g2d.setColor(Color.BLACK);
			if (h == 0) {
				g2d.fillRoundRect(1, 1, getWidth()+5, getHeight(), 0, 0);
				g2d.fillRoundRect(0, 0, getWidth()+5, getHeight(), 0, 0);
			} else {
				g2d.fillRoundRect(1, 1, w+5,h, 0, 0);
				g2d.fillRoundRect(0, 0, w+5,h, 0, 0);
			}
			g2d.dispose();
			// 记录上一个窗体所用的高度
			int History = 0;
			int hig = -deviation * 22;
			g.translate(0, hig);
			for (int i = 0; i < labels.size(); i++) {
				Component c = labels.get(i);
				g.translate(c.getX(), History);
				History = c.getHeight();
				hig += History;
				if (hig >= 0) {
					c.paint(g);
				}
			}
			g2d.dispose();
		}
	}


	public static ImgZoom imgZoom = CutButtonImage.newcutsPng(ImgConstants.TxtImg, 14, 7, true,"defaut.wdf");
	public void paintimg(Graphics g) {
		if (!labels.isEmpty()) {
			Graphics2D g2d = (Graphics2D) g.create();
			g2d.setColor(Color.BLACK);
			imgZoom.draw(g2d);
			imgZoom.setMiddlew(getWidth() - 2 * imgZoom.getEdgew());
			imgZoom.setMiddleh(getHeight() - 2 * imgZoom.getEdgeh());
			int History = 0;
			int hig = -deviation * 22;
			g2d.translate(0, hig);
			for (int i = 0; i < labels.size(); i++) {
				Component c = labels.get(i);
				g2d.translate(c.getX()+2 , History);
				History = c.getHeight();
				hig += History;
				if (hig >= 0) {
					c.paint(g2d);
				}
			}
			g2d.dispose();
		}
	}

	public void paintSSS(Graphics g) {
		if (!labels.isEmpty()) {
			Graphics2D g2d = (Graphics2D) g.create();
			g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, Alpha));
			g2d.setColor(Color.BLACK);
			if (h == 0) {
				g2d.fillRoundRect(1, 1, getWidth()+5, getHeight(), 0, 0);
				g2d.fillRoundRect(0, 0, getWidth()+5, getHeight(), 0, 0);
			} else {
				g2d.fillRoundRect(1, 1, w+5,h, 0, 0);
				g2d.fillRoundRect(0, 0, w+5,h, 0, 0);
			}
			g2d.dispose();
			// 记录上一个窗体所用的高度
			int History = 0;
			int hig = -deviation * 22;
			g.translate(0, hig);
			for (int i = 0; i < labels.size(); i++) {
				Component c = labels.get(i);
				g.translate(c.getX(), History);
				History = c.getHeight();
				hig += History;
				if (hig >= 0) {
					c.paint(g);
				}
			}
		}
	}


	public void addtext(RichLabel label, int size) {
		if (labels.size() > 100) {
			AddAndSubtract(2);
		}
		if (label != null) {
			this.labels.add(label);

		}

		Formsize(size);
		if (ScreenFull() != 0) {
			while (deviation < ScreenFull()) {
				deviation++;
			}
		}

	}

	// * //系统200 信息201 世界202 当前203 队伍204 帮派205
	// * #T 系统* #X 信息* #J 世界* #Q 当前* #D 队伍* #P 帮派
	public void addtext(String text, int size, Font font) {
		if (labels.size() > 100) {
			AddAndSubtract(2);
		}
		addText(text, size, font);
		if (ScreenFull() != 0) {
			while (deviation < ScreenFull()) {
				deviation++;
			}
		}
	}

	/**
	 * 自带的字体
	 * 
	 * @param chatText
	 * @param size
	 */
	public void addText(String chatText, int size) {
		RichLabel label = new RichLabel(chatText, null);
		Dimension d = label.computeSize(size);
		label.setSize(d);
		if (label != null) {
			this.labels.add(label);
		}
		add(label);
		Formsize(size);
	}
	/**
	 * 自定义字体
	 * 
	 * @param chatText
	 * @param size
	 */
	public RichLabel addText(String chatText, int size, Font font) {
		RichLabel label = new RichLabel(chatText, font);
		Dimension d = label.computeSize(size);
		label.setSize(d);
        this.labels.add(label);
        Formsize(size);
		return label;
	}


	
	/**TODO 移除式添加*/
    public RichLabel removeAddText(String chatText, int size, Font font) {
        removeAll();
        labels.clear();
		RichLabel label = new RichLabel(chatText, font);
		Dimension d = label.computeSize(size);
		label.setSize(d);
        this.labels.add(label);
        Formsize(size);
        return label;
    }

	/**
	 * 修改窗体大小
	 */
	public void Formsize(int size) {
		int h = 6;
		for (int i = 0; i < labels.size(); i++) {
			h += labels.get(i).getSize().getHeight();
		}

		setSize(size, h);
	}
	/**
	 * 修改窗体大小
	 */
	public void ChatFormsize(int size) {
		int h = 6;
		for (int i = 0; i < chatlabels.size(); i++) {
			h += chatlabels.get(i).getSize().getHeight();
		}
		setSize(size, h);
	}

	/**
	 * 点击上下清屏按钮 左(移位) 0 上 1下 2清屏 3禁言 4左 5调大聊天框 6调小聊天框大小 7透明度改变
	 */
	public void AddAndSubtract(int type) {

		switch (type) {
		case 0:
			if (ScreenFull() != 0) {
				if (deviation > 0) {
					deviation--;
				}
			}
			break;
		case 1:
			if (ScreenFull() != 0) {
				if (deviation < ScreenFull()) {
					deviation++;
				}
			}
			break;
		case 2:
			removemsg();
			deviation = 0;
			Formsize(280);
			break;
		case 3:
			Gag = !Gag;
			break;
		case 4:
//			RemoveForms();
			break;
		case 5:
			if (h < ScrenceUntil.Screen_y - 300) {
				h += 20;
			}
			break;
		case 6:
			if (h > 80) {
				h -= 20;
			}
			break;
		case 7:
			if (display) {
				Alpha -= 0.2f;
				if (Alpha <= 0.0f) {
					Alpha = 0.6f;
				}
			}

			break;
		default:
			break;
		}
	}

	/**
	 * 判断是否满屏
	 */
	public int ScreenFull() {
		int size = 0;
		for (int i = 0; i < labels.size(); i++) {
			size += labels.get(i).getSize().getHeight();
		}
		if (size - 22 >= h) {
			return (size - h) / 22 + 2;
		}
		return 0;
	}

	/**
	 * 判断是否需要取消右窗体
	 */
	public void RemoveForms() {
		if (!display) {
			Alpha = 0.6f;
			w = 300;
			h = 200;
//			TestGameMain.frameDialogSync.dialog.setVisible(false);// 让面板显示为不可见
			Main.frame.setSize(ScrenceUntil.Screen_x + 6,
					ScrenceUntil.Screen_y + 28);
		} else {
			Alpha = 0.0f;
			w = 300;
			h = ScrenceUntil.Screen_y / 2 - 20;
//			TestGameMain.frameDialogSync.dialog.setVisible(true);// 让面板显示为可见
			Main.frame.setSize(ScrenceUntil.Screen_x, ScrenceUntil.Screen_y + 28);

		}
		display = !display;
	}

	/**清除对话记录*/
	public void removemsg() {
		removeAll();
		labels.clear();
	}

	public List<RichLabel> getLabels() {
		return labels;
	}

	public void setLabels(List<RichLabel> labels) {
		this.labels = labels;
	}

	public int getDeviation() {
		return deviation;
	}

	public void setDeviation(int deviation) {
		this.deviation = deviation;
	}

	public boolean isGag() {
		return Gag;
	}

	public void setGag(boolean gag) {
		Gag = gag;
	}

	public float getAlpha() {
		return Alpha;
	}

	public void setAlpha(float alpha) {
		Alpha = alpha;
	}

	public boolean isDisplay() {
		return display;
	}

	public void setDisplay(boolean display) {
		this.display = display;
	}

	public int getW() {
		return w;
	}

	public void setW(int w) {
		this.w = w;
	}

	public int getH() {
		return h;
	}

	public void setH(int h) {
		this.h = h;
	}

}
