package com.tool.btn;

import com.tool.tcpimg.UIUtils;
import org.come.Jpanel.OptionsJpanel;
import org.come.until.MessagrFlagUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class OptionsBtn extends MoBanBtn {
	public int index;
	private OptionsJpanel jpanel;
	public OptionsBtn(String iconpath, int type,String text,OptionsJpanel jpanel,int index ) {
		super(iconpath, type ,UIUtils.COLOR_BTNPUTONG);
		// TODO Auto-generated constructor stub
		setText(text);
		setFont(UIUtils.TEXT_HY16);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.jpanel=jpanel;
		this.index=index;
	}
	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
        jpanel.tipBox(index==1);
		if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
			MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		}
	}
}
