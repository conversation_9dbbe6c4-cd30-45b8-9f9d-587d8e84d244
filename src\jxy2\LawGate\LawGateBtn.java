package jxy2.LawGate;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import org.come.bean.LoginResult;
import org.come.bean.PrivateData;
import org.come.bean.Skill;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserMessUntil;
import org.skill.frame.SkillMainFrame;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class LawGateBtn extends MoBanBtn {
    public int BtnId;
    public LawGateJPanel lawGateJPanel;
    public LawGateBtn(String iconpath, int type, int BtnId, String labelName,
                      LawGateJPanel lawGateJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.lawGateJPanel = lawGateJPanel;
    }


    public LawGateBtn(String iconpath, int type, String text,int BtnId, LawGateJPanel lawGateJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.BtnId=BtnId;
        this.lawGateJPanel=lawGateJPanel;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch(BtnId){
            case 0:
            case 1:
            case 2:
                PrivateData data = RoleData.getRoleData().getPrivateData();
                StringBuffer buffer = new StringBuffer();
                   String title = LawGateMainJPanel.lawGateJPanels[BtnId].title;
                   buffer.append(title+BtnId);
                   for (int i = 0; i < LawGateMainJPanel.lawGateJPanels[BtnId].skillids.size(); i++) {
                       buffer.append("#");
                       buffer.append(LawGateMainJPanel.lawGateJPanels[BtnId].skillids.get(i));
                       buffer.append("_");
                       buffer.append(0);
                   }
                   String skill = buffer.toString();
                   if (skill.equals(title)) {return;}

                   data.setSkills("F",skill);
                   SendRoleAndRolesummingUntil.sendRole(data);
                   sendRole(BtnId);
                   lawGateJPanel.zhi = title;
                   //刷新窗口


                //怎么实现12004_0#12504_0
                StringBuffer buffersld = new StringBuffer();
                for (int i = 0; i < LawGateMainJPanel.lawGateJPanels[BtnId].skillids.size(); i++) {
                    buffersld.append(LawGateMainJPanel.lawGateJPanels[BtnId].skillids.get(i));
                    buffersld.append("_");
                    buffersld.append(0);
                    if (i < LawGateMainJPanel.lawGateJPanels[BtnId].skillids.size() - 1) {
                        buffersld.append("#");
                    }
                }

                String skillsld = buffersld.toString();
                String mes = Agreement.getAgreement().rolechangeAgreement("G"+skillsld);
                SendMessageUntil.toServer(mes);


                SkillMainFrame.getSkillMainFrame().getSkillMainPanel().changeBtnPanel(2);
                break;
            default:
                buffer = new StringBuffer();
                if (!FormsManagement.getframe(138).isVisible()) {
                    // 设置存款
                    FormsManagement.showForm(138);
                    Music.addyinxiao("开关窗口.mp3");
                    //获取技能ID

                    String[] fmid = RoleData.getRoleData().getPrivateData().getSkill("F");

                    String ID = "";
                    String NAMME = "";
                    for (int i = 0; i < fmid.length; i++) {
                        if (fmid[i].length()>3){
                                String[] vs = fmid[i].split("_");
                                //获取技能ID
                                String skillid = vs[0];
                                //获取技能熟练度//手动修改熟练度可以绕过服务器

                                int skillLevel = Integer.parseInt(vs[1]);
                                Skill skills= UserMessUntil.getSkillId(skillid);
                                buffer.append("#").append(skills.getSkillid()).append("_").append(skillLevel);
                                if ((BtnId-3)==i){
                                    ID = skills.getSkillid();
                                    NAMME = skills.getSkillname();
                                }
                                LawGateXLFrame.getLawGateXLFrame().getLawGateXLJPanel().ObtainVariousParameters(skills,NAMME,skillLevel,lawGateJPanel.zhi,buffer.toString(),ID);

                        }
                    }
                    LoginResult result = RoleData.getRoleData().getLoginResult();
                    if (result.getFmsld()!=null){
                        LawGateXLFrame.getLawGateXLFrame().getLawGateXLJPanel().updateSkillLevel(Integer.parseInt(LawGateControl.getSkillLevel(result)));
                    }

                } else {
                    FormsManagement.HideForm(138);
                    Music.addyinxiao("关闭窗口.mp3");
                }
            break;

        }

    }

    public static void sendRole(int BtnId){
        LawGateMainJPanel.lawGateJPanels[BtnId].K = 26;
        LawGateMainJPanel.lawGateJPanels[BtnId].isv = true;
        LawGateMainJPanel.lawGateJPanels[BtnId].testMes.setVisible(false);
        for (int i = 0; i < 2; i++) {
            LawGateMainJPanel.lawGateJPanels[BtnId].practice[i].setVisible(true);
        }
        for (int i = 0; i < LawGateMainJPanel.lawGateJPanels.length; i++) {
            if (i!=BtnId){
                LawGateMainJPanel.lawGateJPanels[i].testMes.setVisible(true);
                LawGateMainJPanel.lawGateJPanels[i].K = 0;
                LawGateMainJPanel.lawGateJPanels[i].isv = false;
                for (int k = 0; k < 2; k++) {
                    LawGateMainJPanel.lawGateJPanels[i].practice[k].setVisible(false);
                }
            }
            LawGateMainJPanel.lawGateJPanels[i].study.setVisible(false);
        }
    }

}
