package jxy2.refine;

import com.tool.btn.SwitchPageBtn;
import com.tool.btn.WorkshopBtn;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.JadeorGoodstableBean;
import org.come.mouslisten.IncludedPartsMpuslisten;
import org.come.mouslisten.IncludedSuitMouslisten;
import org.come.mouslisten.SimuMouseTextUtil;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.SrcollPanelUI;
import org.come.until.Util;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.math.BigDecimal;

/**
* 套装收录（已收录）
* <AUTHOR>
* @date 2024/7/31 上午6:46
*/

public class CollectionJpanel extends JPanel {
//    public JLabel[] sixEquipment = new JLabel[6];
    public CollectionBtn suitBtn;//套装合成小菜单按钮
    private int minType = 0;
    private CollectionBtn workshopBtn;// 炼化装备按钮
    private JList<String> listSuit; // 已收录的套装列表
    private DefaultListModel<String> listModel;// 放置套装的对象
    private JScrollPane jScrollPane; // 已收录的套装列表滚动条
    private JTextField textField;// 生成个数输入框
    private JadeorGoodstableBean goodstableBean = new JadeorGoodstableBean(); // 存放要生成/激活的玉符
    private BigDecimal money,// 所需金钱
            sxlxz;// 所需灵修值
    public static int suitNum;// 已收录的套装数
    private SwitchPageBtn addNumBtn;// 加收录套装上限按钮
    private JLabel[] labSuitParts = new JLabel[9];
    private IncludedPartsMpuslisten[] mpuslisten = new IncludedPartsMpuslisten[9];
    private int suitid; // 存储选中的套装id
    private WorkshopBtn labAct;// 激活按钮
    public CollectionJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
            suitBtn = new CollectionBtn(ImgConstants.tz45, -1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "已收录", 0, this,"");
            suitBtn.btnchange(2);
            suitBtn.setBounds(138, 48, 66, 24);
            add( suitBtn);


        // 炼化装备按钮
        workshopBtn = new CollectionBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 99, this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);

        listModel = new DefaultListModel<String>();
        // 已收录的套装列表
        listSuit = new JList<String>();
        listSuit.setOpaque(false);
        listSuit.setSelectionBackground(UIUtils.Color_BACK);
        listSuit.setSelectionForeground(Color.green);
        listSuit.setForeground(Color.white);
        listSuit.setFont(new Font("微软雅黑", Font.BOLD, 14));
        listSuit.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        listSuit.setModel(listModel);
        listSuit.addMouseListener(new IncludedSuitMouslisten(this));

        // 已收录的套装列表滚动条
        jScrollPane = new JScrollPane(listSuit);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setOpaque(false);
        jScrollPane.setBounds(465, 103, 155, 170);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);

        // 已收录套装部件
        for (int i = 0, size = labSuitParts.length; i < size; i++) {
            labSuitParts[i] = new JLabel();
            mpuslisten[i] = new IncludedPartsMpuslisten(i, this);
            labSuitParts[i].addMouseListener(mpuslisten[i]);
            this.add(labSuitParts[i]);
        }

        // 加收录套装上限按钮
        addNumBtn = new SwitchPageBtn(ImgConstants.tz109, 1, 11);
        addNumBtn.setBounds(600, 285, 18, 18);
        addNumBtn.addMouseListener(new SimuMouseTextUtil("增加可收录套装数需1亿两"));
        this.add(addNumBtn);


        // 激活按钮
        labAct = new WorkshopBtn(ImgConstants.tz114, 1, UIUtils.COLOR_BTNTEXT, UIUtils.TEXT_FONT, "激活", 15, this);
        labAct.setBounds(390, 410, 34, 17);
        labAct.setBtn(-1);
        labAct.setForeground(Color.GRAY);
        labAct.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz172, "defaut.wdf"));
        this.add(labAct);

        // 生成个数输入框
        textField = new JTextField();
        textField.setBounds(504, 308, 121, 23);
        textField.setFont(UIUtils.TEXT_FONT1);
        textField.setOpaque(false);
        textField.setBorder(BorderFactory.createEmptyBorder());
        textField.setCaretColor(Color.white);
        textField.setForeground(Color.white);
        textField.addKeyListener(new KeyListener() {
            @Override
            public void keyTyped(KeyEvent e) {
                // TODO Auto-generated method stub
                int charstr = e.getKeyChar();
                // control the value between 0 and 9
                if (charstr < KeyEvent.VK_0 || charstr > KeyEvent.VK_9) {
                    e.consume();
                    return;
                }
                String str = textField.getText();
                if (str.length() == 0)
                    textField.setText("");
                if (str.length() == 1)
                    if (Long.parseLong(str) == 0)
                        textField.setText("");
                if (str.length() >= 3) {
                    e.consume();
                    return;
                } // 不超过100个生成数量
            }

            @Override
            public void keyPressed(KeyEvent e) {
            }

            @Override
            public void keyReleased(KeyEvent e) {

            }
        });
        textField.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void removeUpdate(DocumentEvent e) {
                String str = textField.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        money = new BigDecimal(Long.parseLong(str) * 10000000);
                        sxlxz = new BigDecimal(Long.parseLong(str) * 10);
                    }
                } else {
                    money = null;
                    sxlxz = null;
                }
            }

            @Override
            public void insertUpdate(DocumentEvent e) {
                String str = textField.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        money = new BigDecimal(Long.parseLong(str) * 10000000);
                        sxlxz = new BigDecimal(Long.parseLong(str) * 10);
                    }
                } else {
                    money = null;
                    sxlxz = null;
                }
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                String str = textField.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        money = new BigDecimal(Long.parseLong(str) * 10000000);
                        sxlxz = new BigDecimal(Long.parseLong(str) * 10);
                    }
                } else {
                    money = null;
                    sxlxz = null;
                }
            }
        });
        this.add(textField);
    }



    public void clear() {
        int s1 = Math.max(24*CollectionJpanel.suitNum, 171);
        listSuit.setPreferredSize(new Dimension(155,s1));
        textField.setText("");
        money = null;
        sxlxz = null;
        goodstableBean.setType(0);
        goodstableBean.setPartJade(null);
        workshopBtn.setText("?");
        suitid = 0;

    }
    public String[] vs = new String[]{"生成个数","所需金钱","消耗灵修值","现有灵修值"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        int centerX = 282; // 圆心的X坐标
        int centerY = 203; // 圆心的Y坐标
        int radius = 95;  // 圆的半径
        int  numElements = 8; // 元素数量
        double  angleIncrement = Math.PI / numElements * 2 ; // 角度增量，注意这里是π/3，因为我们只需要两次增量
        double  startAngle = Math.PI / 2; // 起始角度，指向正上方
        for (int i = 0; i < numElements; i++) {
            double angle = startAngle + i * angleIncrement; // 当前元素的角度
            int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
            int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
            Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1); // 假设Juitil.ImngBack是用来绘制图像的方法
            labSuitParts[i].setBounds(x,y,50,50);
        }
        Juitil.ImngBack(g, Juitil.good_2, 282, 202, 50, 50, 1); // 假设Juitil.ImngBack是用来绘制图像的方法
        labSuitParts[8].setBounds(282,202,50,50);
        Juitil.TextBackground(g, "同一个套装仅剩一个部件未收录时，可点击            获得", 13, 140, 410, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        Juitil.ImngBack(g, Juitil.tz21, 458, 76, 166, 175+30, 1);
        Juitil.ImngBack(g, Juitil.tz76, 601, 102, 19, 140+30, 1);
        Juitil.TextBackground(g, "已收录套装列表", 13, 462, 82, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
       // 画已收录的套装部件
        GoodsListFromServerUntil.drawIncludedSuit(g,suitid);
        for (int i = 0; i < 4; i++) {
            Juitil.ImngBack(g, Juitil.tz26, 500, 307+i*25, 121, 23, 1);
            Juitil.TextBackground(g, getPrompt(15)[i], 13, 430, 310+i*25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        }
        // 画拥有的灵修值
        if (RoleData.getRoleData().getLoginResult().getScoretype("灵修值") != null) {
            Util.drawPrice(g, RoleData.getRoleData().getLoginResult().getScoretype("灵修值"), 504, 374+25);
        }
        // 画拥有金钱
        Util.drawMoney(g, 504, 374);
        // 画消耗金钱
        if (money != null)
            Util.drawPrice(g, money, 504, 374-25);
        if (suitNum >= 0)
            Juitil.TextBackground(g, "可收录套装数："+suitNum + "/" + RoleData.getRoleData().getPackRecord().getSuitNum(), 13, 460, 285, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);

    }
    public String[] getPrompt(int type) {
        String[] result = EqartificeJapanel.PROMPT_MAP.get(type);
        return result != null ? result.clone() : null;
    }

    public CollectionBtn getSuitBtn() {
        return suitBtn;
    }

    public void setSuitBtn(CollectionBtn suitBtn) {
        this.suitBtn = suitBtn;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public CollectionBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(CollectionBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }

    public JList<String> getListSuit() {
        return listSuit;
    }

    public void setListSuit(JList<String> listSuit) {
        this.listSuit = listSuit;
    }

    public DefaultListModel<String> getListModel() {
        return listModel;
    }

    public void setListModel(DefaultListModel<String> listModel) {
        this.listModel = listModel;
    }

    public JScrollPane getjScrollPane() {
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public JTextField getTextField() {
        return textField;
    }

    public void setTextField(JTextField textField) {
        this.textField = textField;
    }

    public JadeorGoodstableBean getGoodstableBean() {
        return goodstableBean;
    }

    public void setGoodstableBean(JadeorGoodstableBean goodstableBean) {
        this.goodstableBean = goodstableBean;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getSxlxz() {
        return sxlxz;
    }

    public void setSxlxz(BigDecimal sxlxz) {
        this.sxlxz = sxlxz;
    }

    public static int getSuitNum() {
        return suitNum;
    }

    public static void setSuitNum(int suitNum) {
        CollectionJpanel.suitNum = suitNum;
    }

    public SwitchPageBtn getAddNumBtn() {
        return addNumBtn;
    }

    public void setAddNumBtn(SwitchPageBtn addNumBtn) {
        this.addNumBtn = addNumBtn;
    }

    public JLabel[] getLabSuitParts() {
        return labSuitParts;
    }

    public void setLabSuitParts(JLabel[] labSuitParts) {
        this.labSuitParts = labSuitParts;
    }

    public IncludedPartsMpuslisten[] getMpuslisten() {
        return mpuslisten;
    }

    public void setMpuslisten(IncludedPartsMpuslisten[] mpuslisten) {
        this.mpuslisten = mpuslisten;
    }

    public int getSuitid() {
        return suitid;
    }

    public void setSuitid(int suitid) {
        this.suitid = suitid;
    }

    public WorkshopBtn getLabAct() {
        return labAct;
    }

    public void setLabAct(WorkshopBtn labAct) {
        this.labAct = labAct;
    }
}

