package com.tool.pet;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.come.entity.Baby;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

/**
 * 宝宝属性
 * <AUTHOR>
 */
public class BabyProperty {

	private static BabyProperty babyProperty;
    private Map<String,Integer> maps=new HashMap<>();
	public static BabyProperty getBabyProperty() {
		if (babyProperty==null)babyProperty=new BabyProperty();
		return babyProperty;
	}
	private void addmap(String k,int v){
		if (maps.get(k)==null)return;
		maps.put(k, maps.get(k)+v);
	}
	/**重置*/
	private void reset(Baby baby){
		maps.put("气质", baby.getQizhi());
		maps.put("内力", baby.getNeili());
		maps.put("智力", baby.getZhili());
		maps.put("耐力", baby.getNaili());
		maps.put("名气", baby.getMingqi());
		maps.put("道德", baby.getDaode());
		maps.put("叛逆", baby.getPanni());
		maps.put("玩性", baby.getWanxing());
		maps.put("孝心", baby.getXiaoxin());
		maps.put("皮肤", baby.getChildSex()==0?100001:100002);
	}
	public Map<String,Integer> getProperty(Baby baby,BigDecimal[] bids){
		reset(baby);
		if (bids!=null) {
			int size=0;
			for (int i = 0; i < bids.length; i++) {
				Goodstable goodstable=GoodsListFromServerUntil.fushis.get(bids[i]);
				if (goodstable!=null) {
					String[] v=goodstable.getValue().split("\\|");
					for (int j = 1; j < v.length; j++) {
						String[] v2=v[j].split("=");
						if (v2[0].equals("等级")) {
							size+=Integer.parseInt(v2[1])>=3?1:0;
						}else {
							addmap(v2[0],Integer.parseInt(v2[1]));	
						}		
					}
				}
			}
			if (size==4) {
				maps.put("皮肤", baby.getChildSex()==0?100005+Util.random.nextInt(3)*2:100006+Util.random.nextInt(3)*2);
			}
		}
		return maps;
	}
}
