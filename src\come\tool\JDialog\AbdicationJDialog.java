package come.tool.JDialog;

import org.come.Frame.FactionMainJframe;
import org.come.Jpanel.FactionCardJpanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

/**
 * 退位让贤
 * 
 * <AUTHOR>
 * 
 */
public class AbdicationJDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {
        // TODO Auto-generated method stub
        int index = (int) object;
        if (tishi) {
            try {
                FactionCardJpanel factionCardJpanel = FactionMainJframe.getFactionMainJframe().getFactionMainJpanel()
                        .getFactionCardJpanel();
                factionCardJpanel.getGangResultBean().getRoleTables().get(index).setGangpost("帮主");
                String sendMes = Agreement.GangChangeAgreement(factionCardJpanel.getGangResultBean().getRoleTables()
                        .get(index).getRole_id().toString());
                // 向服务器发送信息
                SendMessageUntil.toServer(sendMes);
            } catch (Exception e2) {
                // TODO: handle exception
            }
        }
    }
}
