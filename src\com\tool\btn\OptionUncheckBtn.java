package com.tool.btn;

import java.awt.Color;
import java.awt.event.MouseEvent;

import javax.swing.JPanel;
import javax.swing.SwingConstants;

import org.come.Jpanel.FundBuyJpanel;
import org.come.Jpanel.MonthlyCardJpanel;
import org.come.until.CutButtonImage;

import com.tool.tcpimg.UIUtils;

/**
 * 选项卡按钮 未选中
 * 
 * <AUTHOR>
 * @time 2019-5-8
 * 
 */
public class OptionUncheckBtn extends MoBanBtn {

	private String caozuo;
	private JPanel jPanel;

	// ★★★选项卡按钮★★★
	// 汉仪小隶书简 19px
	// 常态 #171216 R23,G18,B22
	// 经过 #284428 R240,G68,B40
	// 点击 #284428 R240,G68,B40 字体向左、向下各移动1px
	// ☆☆☆☆☆☆☆☆☆☆☆

	// 选中
	// ★★★选项卡按钮★★★
	// 汉仪小隶书简 16px
	// 常态 #FFFFFF R255,G255,B255
	// 经过 #FFFFFF R255,G255,B255
	// 点击 #FFFFFF R255,G255,B255 字体向左、向下各移动1px
	// ☆☆☆☆☆☆☆☆☆☆☆

	public OptionUncheckBtn(String iconpath, int type, String text, String caozuo, JPanel jpanel) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub
		this.caozuo = caozuo;
		this.jPanel = jpanel;
		this.setText(text);
		setFont(UIUtils.TEXT_HY19);
		setForeground(new Color(255, 255, 255));
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		FundBuyJpanel buyJpanel;
		MonthlyCardJpanel monthlyCardJpanel;
		try {
			switch (caozuo) {
			case "30基金":
				buyJpanel = (FundBuyJpanel) jPanel;
				buyJpanel.getThridFund().setIcons(CutButtonImage.cuts("inkImg/button/21.png"));
				buyJpanel.getSixthFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.getNinethFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.setType("30");
				break;
			case "60基金":
				buyJpanel = (FundBuyJpanel) jPanel;
				buyJpanel.getThridFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.getSixthFund().setIcons(CutButtonImage.cuts("inkImg/button/21.png"));
				buyJpanel.getNinethFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.setType("60");
				break;
			case "90基金":
				buyJpanel = (FundBuyJpanel) jPanel;
				buyJpanel.getThridFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.getSixthFund().setIcons(CutButtonImage.cuts("inkImg/button/20.png"));
				buyJpanel.getNinethFund().setIcons(CutButtonImage.cuts("inkImg/button/21.png"));
				buyJpanel.setType("90");
				break;

			default:
				break;
			}
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
	}

}
