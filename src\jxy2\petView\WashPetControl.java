package jxy2.petView;

import jxy2.qqiubook.QiqiuTreeNode;
import org.come.Frame.ZhuFrame;
import org.come.action.FromServerAction;
import org.come.until.GsonUtil;

public class WashPetControl implements FromServerAction {
    private boolean isProcessing = false;  // 是否正在处理数据
    private boolean hasReceivedData = false;  // 是否收到过数据
    
    @Override
    public void controlMessFromServer(String mes, String type) {
        if (mes == null || mes.trim().isEmpty()) {
            // 如果收到空消息，并且之前收到过数据，说明是结束标记
            if (hasReceivedData) {
                sendFinalRequest();
            }
            return;
        }
        try {
            // 标记已收到数据
            hasReceivedData = true;
            // 如果已经在处理中，直接返回
            if (isProcessing) {
                return;
            }
            
            isProcessing = true;
            QiqiuTreeNode node = GsonUtil.getGsonUtil().getgson().fromJson(mes, QiqiuTreeNode.class);
            ZhuFrame.getZhuJpanel().showThousand(node);
            isProcessing = false;
            
        } catch (Exception e) {
            e.printStackTrace();
            isProcessing = false;
            hasReceivedData = false;
        }
    }
    
    private void sendFinalRequest() {
        // 发送最终请求
//        String meg = Agreement.getAgreement().QianqiuAgreement("2");
//        SendMessageUntil.toServer(meg);
        // 重置状态
        hasReceivedData = false;
        isProcessing = false;
    }
}
