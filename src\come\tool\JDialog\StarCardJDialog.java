package come.tool.JDialog;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

/**
 * 星卡星阵属性删除操作提示
 * <p>
 * Title : StarCardJDialog
 * </p>
 * 
 * <AUTHOR> HGC
 * @date : 2019年8月20日 下午3:30:45
 * @version : 1.0.0
 */
public class StarCardJDialog implements TiShiChuLi {

	@Override
	public void tipBox(boolean tishi, Object object) {
		if (tishi) {
			Goodstable goodstable = (Goodstable) object;
			SuitOperBean suitOperBean = new SuitOperBean();
			List<BigDecimal> goods = new ArrayList<>();
			goods.add(goodstable.getRgid());
			suitOperBean.setGoods(goods);
			suitOperBean.setType(58);
			String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
			SendMessageUntil.toServer(sendmes);
		}
	}
}
