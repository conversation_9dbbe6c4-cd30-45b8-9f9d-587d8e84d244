package org.come.util;

import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * DDS 图像工具类
 * 提供便捷的 DDS 图像处理方法
 */
public class DDSImageUtil {
    
    /**
     * 加载 DDS 图像并创建 ImageIcon
     */
    public static ImageIcon loadDDSAsIcon(String filePath) {
        try {
            BufferedImage image = DDSReader.readDDS(filePath);
            return new ImageIcon(image);
        } catch (IOException e) {
            return Juitil.tz402;
        }

    }
    /**
     * 加载 DDS 图像并缩放到指定大小
     */
    public static BufferedImage loadDDSAndScale(String filePath, int width, int height) {
        try {
            BufferedImage originalImage = DDSReader.readDDS(filePath);
            return scaleImage(originalImage, width, height);
        } catch (IOException e) {
            System.err.println("加载和缩放 DDS 图像失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 缩放图像
     */
    public static BufferedImage scaleImage(BufferedImage original, int width, int height) {
        if (original == null) {
            return null;
        }
        
        BufferedImage scaled = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = scaled.createGraphics();
        
        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        
        return scaled;
    }
    
    /**
     * 创建 DDS 图像查看器窗口
     */
    public static void showDDSImage(String filePath) {
        try {
            BufferedImage image = DDSReader.readDDS(filePath);
            showImageInWindow(image, "DDS 图像查看器 - " + new File(filePath).getName());
        } catch (IOException e) {
            JOptionPane.showMessageDialog(null, 
                "无法加载 DDS 图像: " + e.getMessage(), 
                "错误", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 在窗口中显示图像
     */
    public static void showImageInWindow(BufferedImage image, String title) {
        if (image == null) {
            return;
        }
        
        JFrame frame = new JFrame(title);
        frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        
        // 创建图像面板
        JPanel imagePanel = new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                if (image != null) {
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    
                    // 计算缩放比例以适应面板
                    int panelWidth = getWidth();
                    int panelHeight = getHeight();
                    int imageWidth = image.getWidth();
                    int imageHeight = image.getHeight();
                    
                    double scaleX = (double) panelWidth / imageWidth;
                    double scaleY = (double) panelHeight / imageHeight;
                    double scale = Math.min(scaleX, scaleY);
                    
                    int scaledWidth = (int) (imageWidth * scale);
                    int scaledHeight = (int) (imageHeight * scale);
                    
                    int x = (panelWidth - scaledWidth) / 2;
                    int y = (panelHeight - scaledHeight) / 2;
                    
                    g2d.drawImage(image, x, y, scaledWidth, scaledHeight, null);
                    g2d.dispose();
                }
            }
        };
        
        imagePanel.setPreferredSize(new Dimension(
            Math.min(800, image.getWidth()), 
            Math.min(600, image.getHeight())
        ));
        imagePanel.setBackground(Color.GRAY);
        
        // 添加滚动面板
        JScrollPane scrollPane = new JScrollPane(imagePanel);
        scrollPane.setPreferredSize(new Dimension(800, 600));
        
        frame.add(scrollPane);
        frame.pack();
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }
    
    /**
     * 获取 DDS 图像信息
     */
    public static String getDDSImageInfo(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return "文件不存在";
            }
            
            if (!DDSReader.isDDSFile(filePath)) {
                return "不是有效的 DDS 文件";
            }
            
            BufferedImage image = DDSReader.readDDS(filePath);
            
            StringBuilder info = new StringBuilder();
            info.append("文件名: ").append(file.getName()).append("\n");
            info.append("文件大小: ").append(formatFileSize(file.length())).append("\n");
            info.append("图像尺寸: ").append(image.getWidth()).append(" x ").append(image.getHeight()).append("\n");
            info.append("颜色模型: ").append(getColorModelName(image.getType())).append("\n");
            info.append("是否有透明通道: ").append(image.getColorModel().hasAlpha() ? "是" : "否").append("\n");
            
            return info.toString();
            
        } catch (IOException e) {
            return "读取文件信息失败: " + e.getMessage();
        }
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取颜色模型名称
     */
    private static String getColorModelName(int imageType) {
        switch (imageType) {
            case BufferedImage.TYPE_INT_RGB:
                return "RGB";
            case BufferedImage.TYPE_INT_ARGB:
                return "ARGB";
            case BufferedImage.TYPE_INT_ARGB_PRE:
                return "ARGB (预乘)";
            case BufferedImage.TYPE_3BYTE_BGR:
                return "BGR";
            case BufferedImage.TYPE_4BYTE_ABGR:
                return "ABGR";
            case BufferedImage.TYPE_BYTE_GRAY:
                return "灰度";
            default:
                return "其他 (" + imageType + ")";
        }
    }
    
    /**
     * 批量转换 DDS 文件到 PNG
     */
    public static void convertDDSToPNG(String ddsFilePath, String pngFilePath) {
        try {
            BufferedImage image = DDSReader.readDDS(ddsFilePath);
            javax.imageio.ImageIO.write(image, "PNG", new File(pngFilePath));
            System.out.println("转换成功: " + ddsFilePath + " -> " + pngFilePath);
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建 DDS 文件选择器
     */
    public static JFileChooser createDDSFileChooser() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileFilter() {
            @Override
            public boolean accept(File f) {
                if (f.isDirectory()) {
                    return true;
                }
                String name = f.getName().toLowerCase();
                return name.endsWith(".dds");
            }
            
            @Override
            public String getDescription() {
                return "DDS 图像文件 (*.dds)";
            }
        });
        return fileChooser;
    }
    
    /**
     * 简单的使用示例
     */
    public static void main(String[] args) {
        // 示例：显示 DDS 图像
        if (args.length > 0) {
            String filePath = args[0];
            if (DDSReader.isDDSFile(filePath)) {
                System.out.println("DDS 文件信息:");
                System.out.println(getDDSImageInfo(filePath));
                showDDSImage(filePath);
            } else {
                System.out.println("不是有效的 DDS 文件: " + filePath);
            }
        } else {
            System.out.println("用法: java DDSImageUtil <dds文件路径>");
        }
    }
}
