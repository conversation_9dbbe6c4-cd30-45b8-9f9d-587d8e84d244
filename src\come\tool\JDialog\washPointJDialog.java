package come.tool.JDialog;

import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserData;
import org.skill.frame.SkillMainFrame;

import java.math.BigDecimal;

public class washPointJDialog implements TiShiChuLi {

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		if (tishi) {
			if (object instanceof String) {
				if (RoleData.getRoleData().getLoginResult().getGold().compareTo(new BigDecimal(500000)) < 0) {
                    ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                    return;
                }
				String mes = Agreement.getAgreement().rolechangeAgreement((String)object);
                SendMessageUntil.toServer(mes);
			}else {
				int dian=(int) object;
				if (UserData.uptael(dian)) {
					RoleData.getRoleData().getPrivateData().setSkills("T", null);
					RoleData.getRoleData().getLoginResult().setTycskill(null);
					RoleData.getRoleData().getLoginResult().setTycclick(null);
					SendRoleAndRolesummingUntil.sendRole(RoleData.getRoleData().getPrivateData());
					SendRoleAndRolesummingUntil.sendRoleResult(RoleData.getRoleData().getLoginResult());
					RoleProperty.ResetEw();
					SkillMainFrame.getSkillMainFrame().getSkillMainPanel().changeBtnPanel(1);
				}	
			}		
		}
	}
}
