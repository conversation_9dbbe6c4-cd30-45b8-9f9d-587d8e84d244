package jxy2.zodiac;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RStarSoulsJPanel extends JPanel {
    private JLabel[] goodLabels = new JLabel[36];
    private JLabel[] lastLabels = new JLabel[3];
    private List<BigDecimal> goodsrgid = new ArrayList<>();
    private StarChartBtn conversion;
    public BigDecimal money =new BigDecimal(100000);
    private BigDecimal xhGoods; // 当前选中的翅膀
    public RStarSoulsJPanel() {
        this.setPreferredSize(new Dimension(365, 480+32));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,137,365);
        for (int i = 0; i < goodLabels.length; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            goodLabels[i] = new JLabel();
            goodLabels[i].setBounds(31+shop_x*51,57+shop_y*51, 51, 51);
            goodLabels[i].addMouseListener(new RStarSoulsMouse(i, this,0));
            this.add(goodLabels[i]);
        }
        for (int i = 0; i < lastLabels.length; i++) {
            lastLabels[i] = new JLabel();
            if (i==2){
                lastLabels[i].setBounds(68+59*3,397, 58, 57);
            }else {
                lastLabels[i].setBounds(65+i*65,397, 58, 57);
            }
            lastLabels[i].addMouseListener(new RStarSoulsMouse(i, this,1));
            add(lastLabels[i]);
        }


        conversion = new StarChartBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "重炼", 4, this,"");
        conversion.setBounds(246, 459, 59, 24);
        this.add(conversion);
    }
    public BigDecimal firstLv = null;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"重 炼");

        Juitil.TextBackground(g, "只能使用两颗同级星魂进行重炼。", 14, 31, 35, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        for (int i = 0; i < 36; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,31+shop_x*51,57+shop_y*51,51,51,1);

            int x = RStarSoulsMouse.list.contains(i+"")?1:0;
            Goodstable goodstable = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[i]);
            g.drawImage(goodstable!=null?Juitil.tz264.getImage():Juitil.tz265.getImage(),x+33+shop_x*51,x+58+shop_y*51,50,50,null);
            Juitil.Txtpet(g, x+50+shop_x*51, x+79+shop_y*51, Juitil.XpName()[i], goodstable!=null?UIUtils.COLOR_NAME8:UIUtils.COLOR_White, UIUtils.TEXT_HY16B);
            if (goodstable!=null) {
                Juitil.Sum(g,goodstable.getUsetime(),33+shop_x*51,72+shop_y*51);
            }
        }
        Juitil.ImngBack(g,Juitil.tz267,31,57,308,308,1);
        Juitil.ImngBack(g, Juitil.tz22, 28, 365, 315, 143, 1);
        Juitil.TextBackground(g, "重炼后，随机生成一颗更高级的星魂或同级星魂。", 13, 45, 372, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        for (int i = 0; i < 3; i++) {
            if (i==2){
                Juitil.ImngBack(g, Juitil.tz258, 68+59*3, 397, 59, 58, 1);
            }else {
                Juitil.ImngBack(g, Juitil.tz258, 65+i*65, 397, 59, 58, 1);
                Juitil.TextBackground(g, "星魂", 15, 78+i*65, 416, UIUtils.COLOR_CL_BACK, UIUtils.FZCY_HY15);
            }
        }
        g.drawImage(Juitil.tz269.getImage(),200,407,34,38,null);
        Juitil.ImngBack(g, Juitil.tz26, 136, 459, 98, 23, 1);
        Juitil.TextBackground(g, "消耗金钱", 13, 68, 463, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);

        int  i = 0;
        if (!goodsrgid.isEmpty()){
            for (BigDecimal bigDecimal : goodsrgid) {
                Goodstable goodstable = GoodsListFromServerUntil.getRgid(bigDecimal);
                if (goodstable != null) {
                    g.drawImage(Juitil.tz264.getImage(), 68 + i *65, 399, 52, 52, null);
                    Juitil.Sum(g, 1, 66 + i * 65, 415);
                    Juitil.Txtpet(g, 85 + i * 65, 422, goodstable.getGoodsname().substring(0, 2), UIUtils.COLOR_NAME8, UIUtils.TEXT_HY16B);
                    if (firstLv == null) {
                        firstLv = BigDecimal.valueOf(Integer.parseInt(goodstable.getValue().split("=")[1]));
                    }
                }
                i++;
            }
            // 绘制 money 乘以第一个元素的 lv
            if (firstLv != null) {
                Util.drawPrice(g, money.multiply(firstLv), 140, 475);
            }
        }

        if (xhGoods!=null){
            Goodstable goodstable = GoodsListFromServerUntil.getRgid(xhGoods);
            if (goodstable != null) {
                g.drawImage(Juitil.tz264.getImage(), 72+59*3, 399, 52, 52, null);
                Juitil.Sum(g, 1, 70 +59*3, 415);
                Juitil.Txtpet(g, 90 +59*3, 422, goodstable.getGoodsname().substring(0, 2), UIUtils.COLOR_FightingRound_Black, UIUtils.TEXT_HY16);
            }
        }


    }

    public JLabel[] getGoodLabels() {
        return goodLabels;
    }

    public void setGoodLabels(JLabel[] goodLabels) {
        this.goodLabels = goodLabels;
    }

    public JLabel[] getLastLabels() {
        return lastLabels;
    }

    public void setLastLabels(JLabel[] lastLabels) {
        this.lastLabels = lastLabels;
    }

    public List<BigDecimal> getGoodsrgid() {
        return goodsrgid;
    }

    public void setGoodsrgid(List<BigDecimal> goodsrgid) {
        this.goodsrgid = goodsrgid;
    }

    public BigDecimal getXhGoods() {
        return xhGoods;
    }

    public void setXhGoods(BigDecimal xhGoods) {
        this.xhGoods = xhGoods;
    }
}
