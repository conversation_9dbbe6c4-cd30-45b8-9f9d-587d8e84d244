package jxy2.jutnil;

import org.come.bean.ImgZoom;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ImageLoader {
    public static void loadImagesParallel(List<String> imagePaths, int x, int y) {
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<CompletableFuture<ImgZoom>> futures = new ArrayList<>();

        for (String imagePath : imagePaths) {
            CompletableFuture<ImgZoom> future = CompletableFuture.supplyAsync(() -> Juitil.WindowImg_1(imagePath, x, y), executor);
            futures.add(future);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allFutures.join(); // 等待所有图像加载完成

        // 可以将加载完成的图像存储起来或进行其他操作
    }
}
