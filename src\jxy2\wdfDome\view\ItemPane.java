package jxy2.wdfDome.view;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class ItemPane extends JPanel implements MouseListener {
    private JFrame frame;
    private String name;
    private AnimationPane apane;

    public ItemPane(JFrame frame, String name, AnimationPane apane) {
        super(new BorderLayout());
        this.frame = frame;
        this.name = name;
        this.apane = apane;
        apane.setZoom(true);
        this.add(apane, "Center");
        this.apane.addMouseListener(this);
    }

    @Override
    public void mouseClicked(MouseEvent e) {
        StringSelection stsel;
        if (e.getButton() == 1) {
            stsel = new StringSelection(this.name);
            Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stsel, stsel);
            new ItemFrame(this.frame, this.apane.copyOne());
        } else {
            stsel = new StringSelection(this.name);
            Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stsel, stsel);
        }

    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }
}
