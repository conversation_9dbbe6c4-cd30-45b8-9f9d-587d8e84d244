package com.tool.btn;

import java.awt.Color;
import java.awt.Desktop;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.net.URI;

import javax.swing.SwingConstants;

import org.come.bean.LoginResult;
import org.come.socket.GameClient;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;

/**
 * 文字按钮
 * 
 * <AUTHOR>
 * @time 2019-5-8
 * 
 */
public class TextBtn extends MoBanBtn {

	// ★★★文字按钮★★★
	// 宋体 12px
	// 常态 #FFFFFF R255,G255,B255
	// 经过 #FFFFFF R255,G255,B255
	// 点击 #FFFFFF R255,G255,B255 字体向左、向下各移动1px
	// ☆☆☆未激活☆☆☆
	// 宋体 12px #7f7f7f R127,G127,B127
	// ☆☆☆☆☆☆☆☆☆☆☆

	private int caozuo;
	private String petList;

	public TextBtn(String iconpath, int type, String text, String caozuo) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub

		this.petList = caozuo;
		this.setText(text);
		setFont(UIUtils.TEXT_FONT1);
		setForeground(new Color(255, 255, 255));
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	public TextBtn(String iconpath, int type, String text, int caozuo) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub
		this.caozuo = caozuo;
		this.setText(text);
		setFont(UIUtils.TEXT_FONT);
		// setForeground(new Color(127, 127, 127));
		if (caozuo == 5) {
			setForeground(Color.yellow);
		} else {
			setForeground(Color.white);
		}
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
	}

	@Override
	public void nochoose(MouseEvent e) {
		if (petList != null) {
			String[] list = petList.split(",");
			if ("petlist".equals(list[0])) {
			}

		} else {
			switch (caozuo) {
			// 首页(基金面板)
			case 1:
				if (FightingMixDeal.State == HandleState.USUAL) {
					try {
						LoginResult login = RoleData.getRoleData().getLoginResult();
						StringBuffer buffer = new StringBuffer();
						// http://ip:8080/TestMaven/requestPay30pay?username=123&rolename=兰少&money=1&quid=25001&type=2

						buffer.append("http://www.dongmengzhongchou.com/pay_config/requestPay30?username=");
						buffer.append(login.getUserName());
						// buffer.append("&rolename=");
						buffer.append("&rolename=");
						buffer.append(login.getRolename());
						buffer.append("&money=30&quid=");
						buffer.append(GameClient.atid);
						buffer.append("&type=1");
						buffer.append("&serverMeString=");
						buffer.append(login.getServerMeString());
						Desktop desktop = Desktop.getDesktop();
						URI uri = new URI(buffer.toString()); // 创建URI统一资源标识符
						desktop.browse(uri);
					} catch (IOException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					} // 使用默认浏览器打开超链接
					catch (Exception e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
				break;
			// 末页(基金面板)
			case 2:

				break;
			// 首页(月卡面板)
			case 3:

				break;
			// 末页(月卡面板)
			case 4:

				break;

			default:
				break;
			}
		}

	}

}
