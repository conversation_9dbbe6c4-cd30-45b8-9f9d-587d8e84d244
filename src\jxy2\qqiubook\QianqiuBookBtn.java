package jxy2.qqiubook;

import com.tool.btn.MoBanBtn;

import java.awt.*;
import java.awt.event.MouseEvent;

public class QianqiuBookBtn extends MoBanBtn {
    public QianqiuBookJPanel qianqiuBookJPanel;
    public int caozuo;
    public QianqiuBookBtn(String iconpath, int type, Color[] colors, String text, Integer caozuo, QianqiuBookJPanel qianqiuBookJPanel, String prompt) {
        super(iconpath, type, 0, colors, prompt,text);
        this.caozuo = caozuo;
        this.qianqiuBookJPanel = qianqiuBookJPanel;
    }



    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
            btnchange(2);
    }
}
