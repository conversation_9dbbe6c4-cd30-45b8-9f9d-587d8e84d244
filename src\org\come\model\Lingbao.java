package org.come.model;

import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.AnalysisString;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.Util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
/**
 * 灵宝
 * <AUTHOR>
 * @date 2017年12月24日 下午6:58:04
 * 
 */ 
public class Lingbao {
	// 灵宝名称
	private String baoname;
	// 灵宝品质   
	private String baoquality;
	// 获得难度   
	private String gethard;
	// 类型    以及标志法宝的类型
	private String baotype;
	// 活跃
	private Integer baoactive;
	// 速度
	private String baospeed;
	// 法宝回复
	private String baoreply;
	// 落宝几率
	private String baoshot;
	// 法宝伤害
	private String baoap;
	// 抗落宝
	private String resistshot;
	// 援助几率
	private String assistance;
	// 擅长技能          
	private String goodskill;
	//天赋技能
	private String tianfuskill;
	// 皮肤
	private String skin;
	// 角色ID
	private BigDecimal roleid;
	//灵宝符石      id|id
	private String fushis;
	//灵宝道行
	private BigDecimal lingbaolvl;
	//灵宝当前进度经验
	private BigDecimal lingbaoexe;
	//灵宝契合度
    private long lingbaoqihe;
	// 灵宝技能集合    "疾风骤雨=1|疾风骤雨=2|疾风骤雨=3"
	private String skills;
	//操作字段  为空不操作 删除="删除" 新增="增加"
	private String operation;	
	//灵宝附加抗性  "风=1.5"
	private String kangxing;
	//技能开启数
	private int skillsum; 
	//符石开启数
    private int fusum;
	// 灵宝ID
	private BigDecimal baoid;
	//用于判断是否装备 1为装备
	private int equipment;
	/**获取灵宝活跃属性*/
	public int hyz() {
		int sp = baoactive;
		if (fushis != null && !fushis.isEmpty()) {
			String[] v = fushis.split("\\|");
            for (String string : v) {
                Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
                if (good != null) {
                    sp = (int) (sp + AnalysisString.valuejie(good.getValue(), "活跃"));
                }
            }
		}
		//加天赋加成
		double xs = 1.0;
		if (tianfuskill != null) {
			String[] v = tianfuskill.split("\\|");
            for (String string : v) {
                if (string.equals("低级活跃")) {
                    xs += 0.1;
                } else if (string.equals("高级活跃")) {
                    xs += 0.2;
                }
            }
		}
		return (int) (sp * xs);
	}
	
	/**
	 * 获取灵宝速度属性
	 * @return
	 */
	public int spz(){
		int sp=(int)Double.parseDouble(baospeed);
	if (fushis!=null&& !fushis.isEmpty()) {
		String[] v =fushis.split("\\|");
		for (int i = 0; i < v.length; i++) {
			Goodstable good=GoodsListFromServerUntil.fushis.get(new BigDecimal(v[i]));
			if (good!=null) {
				  sp=(int) (sp
						  +AnalysisString.valuejie(good.getValue(),"速度")
						  -AnalysisString.valuejie(good.getValue(),"负敏"));		
			}
		}
		}
	//加天赋加成
	 if (tianfuskill!=null) {
	String[] v=tianfuskill.split("\\|");
         for (String string : v) {
             if (string.equals("低级敏捷")) {
                 sp += 200;
             } else if (string.equals("高级敏捷")) {
                 sp += 300;
             }
         }
	}
	return sp;
	}
	/**
	 * 获取灵宝回复
	 * @return
	 */
	public double replyz() {
		double sp = Double.parseDouble(baoreply);
		if (fushis != null && !fushis.isEmpty()) {
			String[] v = fushis.split("\\|");
			for (String string : v) {
				Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
				if (good != null) {
					sp = sp + AnalysisString.valuejie(good.getValue(), "回复");
				}
			}
		}
		double xs = 0;
		if (tianfuskill != null) {
			String[] v = tianfuskill.split("\\|");
			for (String string : v) {
				if (string.length() != 6) continue;
				String type = string.substring(4, 6);
				if (!type.equals("回生")) continue;
				double xss;
				String dj = string.substring(0, 2);
				if (dj.equals("高级")) {
					xss = 0.001;
				} else {
					xss = 0.0005;
				}
				LoginResult loginResult = RoleData.getRoleData().getLoginResult();
				String jc = string.substring(2, 4);
				if (jc.equals("根骨")) {
					xss = xss * RoleProperty.getBone(loginResult) / 4;
				} else if (jc.equals("灵性")) {
					xss = xss * RoleProperty.getSpir(loginResult) / 4;
				} else if (jc.equals("力量")) {
					xss = xss * RoleProperty.getPower(loginResult) / 4;
				} else if (jc.equals("敏捷")) {
					xss = xss * RoleProperty.getSpeed(loginResult) / 4;
				}
				xs += xss;
			}
		}
		return sp + xs;
	}
	/**
	 * 获取法宝落宝概率
	 * @return
	 */
	public double shotz(){
		double sp=Double.parseDouble(baoshot);
		if (fushis!=null&& !fushis.isEmpty()) {
			String[] v =fushis.split("\\|");
            for (String string : v) {
                Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
                if (good != null) {
                    sp = sp + AnalysisString.valuejie(good.getValue(), "落宝");
                }
            }
		}
	    return sp;
	}
	/**
	 * 获取法宝伤害
	 * @return
	 */
	public double apz() {
		double sp = Double.parseDouble(baoap);
		if (fushis != null && !fushis.isEmpty()) {
			String[] v = fushis.split("\\|");
			for (String string : v) {
				Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
				if (good != null) {
					sp = sp + AnalysisString.valuejie(good.getValue(), "伤害");
				}
			}
		}
		double xs = 0;
		if (tianfuskill != null) {
			String[] v = tianfuskill.split("\\|");
			for (String string : v) {
				if (string.length() != 6) continue;
				String type = string.substring(4, 6);
				if (!type.equals("增强")) continue;
				double xss;
				String dj = string.substring(0, 2);
				if (dj.equals("高级")) {
					xss = 0.001;
				} else {
					xss = 0.0005;
				}
				LoginResult loginResult = RoleData.getRoleData().getLoginResult();
				String jc = string.substring(2, 4);
				if (jc.equals("根骨")) {
					xss = xss * RoleProperty.getBone(loginResult) / 4;
				} else if (jc.equals("灵性")) {
					xss = xss * RoleProperty.getSpir(loginResult) / 4;
				} else if (jc.equals("力量")) {
					xss = xss * RoleProperty.getPower(loginResult) / 4;
				} else if (jc.equals("敏捷")) {
					xss = xss * RoleProperty.getSpeed(loginResult) / 4;
				}
				xs += xss;
			}
		}
		return sp + xs;
	}
	/**
	 * 获取抗落宝
	 * @return
	 */
	public double resistshopz() {
		double sp = Double.parseDouble(resistshot);
		if (fushis != null && !fushis.isEmpty()) {
			String[] v = fushis.split("\\|");
			for (String string : v) {
				Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
				if (good != null) {
					sp = sp + AnalysisString.valuejie(good.getValue(), "抗落宝");
				}
			}
		}
		//加天赋加成
		if (tianfuskill != null) {
			String[] v = tianfuskill.split("\\|");
			for (String string : v) {
				if (string.equals("低级抵抗")) {
					sp += 0.1;
				} else if (string.equals("高级抵抗")) {
					sp += 0.2;
				}
			}
		}
		return sp;
	}
	/**
	 * 获取援助
	 * @return
	 */
	public double zyz(){
		double sp=Double.parseDouble(assistance);
		if (fushis!=null&& !fushis.isEmpty()) {
			String[] v =fushis.split("\\|");
            for (String string : v) {
                Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(string));
                if (good != null) {
                    sp = sp + AnalysisString.valuejie(good.getValue(), "支援");
                }
            }
		}
		return sp;
	}	
	/**
	 * 获取契合度
	 */
	public long qhz(){
		return lingbaoqihe;
	}
	
	/**
	 * 技能
	 * 判断当前格子是否开启 如果未开启返回下一个等待开启的数
	 * @return
	 */
	public int SkillOpen(int path){return skillsum<=path?skillsum+1:-1;}
	/**
	 * 符石技能
	 * 判断当前格子是否开启 如果未开启返回下一个等待开启的数
	 * @return
	 */
	public int FushiOpen(int path){return fusum<=path?fusum+1:-1;}
	/**
	 * 开启一个格子
	 * true符石
	 * @return
	 */
	public void OpenGrid(boolean type){if (type) {fusum++;}else {skillsum++;}}
	/**将当前物品信息发给服务器*/
	public void UpdateLing(){
		String sendMes = Agreement.UpdateLing(GsonUtil.getGsonUtil().getgson().toJson(this));
	    SendMessageUntil.toServer(sendMes);
	}
    /**
     * 更改符石集合   
     * @return
     */
	public void fashijihe(String id) {
		List<String> jihe = new ArrayList<>();
		boolean s = true;
		if (fushis != null && !fushis.isEmpty()) {
			String[] v = fushis.split("\\|");
			for (String string : v) {
				if (!string.equals(id)) {
					jihe.add(string);
				} else {
					s = false;
				}
			}
		}
		if (s) {
			jihe.add(id);
		}
		StringBuffer genggai = new StringBuffer();
		for (String string : jihe) {
			if (!genggai.toString().isEmpty()) {
				genggai.append("|").append(string);
			} else {
				genggai.append(string);
			}
		}
		this.fushis = genggai.toString();
	}
	/**
	 * 判断当前格子是否有符石
	 * 有返回符石id
	 * @return
	 */
	public BigDecimal isfushi(int path){if (fushis!=null&&!fushis.equals("")) {String[] v=fushis.split("\\|");if (v.length>path) {return new BigDecimal(v[path]);}}return null;}
	/**
	 * 判断当前格子是否有技能返回技能名=合计数
	 */
	public String isskill(int path){if (skills!=null&&!skills.equals("")) {String[] v=skills.split("\\|");if (v.length>path) {return v[path];}}return null;}
	/**更改技能集合*/
	public boolean skilljihe(Skill skill){
		int type=Integer.parseInt(skill.getSkilltype());
		if (type==0&&!baotype.equals("攻击")) {
			ZhuFrame.getZhuJpanel().addPrompt2("学习技能失败,无法学习该类型的技能");
			return false;
		}else if (type==1&&!baotype.equals("辅助")) {
			ZhuFrame.getZhuJpanel().addPrompt2("学习技能失败,无法学习该类型的技能");
			return false;
		}else if (type==2&&!baotype.equals("落宝")) {
			ZhuFrame.getZhuJpanel().addPrompt2("学习技能失败,无法学习该类型的技能");
			return false;
		}
		String skillname = skill.getSkillname();
		String lvl=skill.getSkilllevel();
		int min =Integer.parseInt(lvl.substring(0,1));
		int max =Integer.parseInt(lvl.substring(1,2));
		//先判断是否有足够的格子
		if ((skills==null||skills.equals(""))&&skillsum>0){		
			skills=skillname+"="+(Util.random.nextInt(max-min+1)+min);
			return true;
		}else if(skills!=null&&!skills.equals("")&&skillsum>skills.split("\\|").length) {
			String[] v = skills.split("\\|");
			int sum=(Util.random.nextInt(max-min+1)+min);
			skillname=skillname+"="+sum;	
			for (int i = 0; i < v.length; i++) {
				if (v[i].equals(skillname)) {
					ZhuFrame.getZhuJpanel().addPrompt2("学习技能失败,技能重复");
					return false;
				}
			}
			skills=skills+"|"+skillname;
			return true;
		}
		ZhuFrame.getZhuJpanel().addPrompt2("学习技能失败,技能格子已满");
		return false;
	}
	/**判断该技能是否是灵宝擅长技能*/
	public boolean shanchang(String v){
		if (goodskill==null||goodskill.equals(""))return false;
		String[] vs=goodskill.split("\\|");
		for (int i = 0; i < vs.length; i++) {
			if (vs[i].equals(v)) {
				return true;
			}
		}
		return false;
	}
	/**
	 * 返回当前位置的技能描述
	 * @return
	 */
	public String skillmsg(int path){
		if (skills!=null&&!skills.equals("")) {
			String[] vs=skills.split("\\|");
			if (vs.length>path) {
				return vs[path];
			}
		}
		
		return null;	
	}

	public String getBaoname() {
		return baoname;
	}
	public void setBaoname(String baoname) {
		this.baoname = baoname;
	}
	public String getGethard() {
		return gethard;
	}
	public void setGethard(String gethard) {
		this.gethard = gethard;
	}
	public String getBaotype() {
		return baotype;
	}
	public void setBaotype(String baotype) {
		this.baotype = baotype;
	}
	public Integer getBaoactive() {
		return baoactive;
	}
	public void setBaoactive(Integer baoactive) {
		this.baoactive = baoactive;
	}
	public String getBaospeed() {
		return baospeed;
	}
	public void setBaospeed(String baospeed) {
		this.baospeed = baospeed;
	}
	public String getBaoreply() {
		return baoreply;
	}
	public void setBaoreply(String baoreply) {
		this.baoreply = baoreply;
	}
	public double getBaoshot() {
		return Double.parseDouble(baoshot);
	}
	public void setBaoshot(String baoshot) {
		this.baoshot = baoshot;
	}
	public String getBaoap() {
		return baoap;
	}
	public void setBaoap(String baoap) {
		this.baoap = baoap;
	}
	public double getResistshop() {
		return Double.parseDouble(resistshot);
	}
	public void setResistshop(String resistshop) {
		this.resistshot = resistshop;
	}
	public double getAssistance() {
		return Double.parseDouble(assistance);
	}
	public void setAssistance(String assistance) {
		this.assistance = assistance;
	}
	public String getGoodskill() {
		return goodskill;
	}
	public void setGoodskill(String goodskill) {
		this.goodskill = goodskill;
	}
	public BigDecimal getRoleid() {
		return roleid;
	}
	public void setRoleid(BigDecimal roleid) {
		this.roleid = roleid;
	}

	public BigDecimal getBaoid() {
		return baoid;
	}
	public void setBaoid(BigDecimal baoid) {
		this.baoid = baoid;
	}
	public String getSkin() {
		return skin;
	}
	public void setSkin(String skin) {
		this.skin = skin;
	}
	
	public String getFushis() {
		return fushis;
	}
	public void setFushis(String fushis) {
		this.fushis = fushis;
	}
	public BigDecimal getLingbaolvl() {
		return lingbaolvl;
	}
	public void setLingbaolvl(BigDecimal lingbaolvl) {
		this.lingbaolvl = lingbaolvl;
	}
	public BigDecimal getLingbaoexe() {
		return lingbaoexe;
	}
	public void setLingbaoexe(BigDecimal lingbaoexe) {
		this.lingbaoexe = lingbaoexe;
	}
	public long getLingbaoqihe() {
		return lingbaoqihe;
	}
	public void setLingbaoqihe(long lingbaoqihe) {
		this.lingbaoqihe = lingbaoqihe;
	}
	public String getSkills() {
		return skills;
	}
	public void setSkills(String skills) {
		this.skills = skills;
	}
	public String getOperation() {
		return operation;
	}
	public void setOperation(String operation) {
		this.operation = operation;
	}
	public String getKangxing() {
		return kangxing;
	}
	public void setKangxing(String kangxing) {
		this.kangxing = kangxing;
	}
	public int getSkillsum() {
		return skillsum;
	}
	public void setSkillsum(int skillsum) {
		this.skillsum = skillsum;
	}
	public int getFusum() {
		return fusum;
	}
	public void setFusum(int fusum) {
		this.fusum = fusum;
	}
	public int getEquipment() {
		return equipment;
	}
	public void setEquipment(int equipment) {
		this.equipment = equipment;
	}
	public String getBaoquality() {
		return baoquality;
	}
	public void setBaoquality(String baoquality) {
		this.baoquality = baoquality;
	}
	public String getTianfuskill() {
		return tianfuskill;
	}
	public void setTianfuskill(String tianfuskill) {
		this.tianfuskill = tianfuskill;
	}
}
