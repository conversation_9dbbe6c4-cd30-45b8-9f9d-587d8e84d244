package com.tool.btn;

import java.awt.Color;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.IphoneVerifyPanel;
import org.come.bean.PhoneNumberSGBean;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

/**
 * HGC<br>
 * 手机验证按钮
 * 
 * <AUTHOR>
 * 
 */
public class IphoneVerifyBtn extends MoBanBtn {

	private int caozuo;
	private IphoneVerifyPanel iphoneVerifyPanel;

	public IphoneVerifyBtn(String iconpath, int type, int caozuo, IphoneVerifyPanel iphoneVerifyPanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.iphoneVerifyPanel = iphoneVerifyPanel;
		this.setVerticalTextPosition(SwingConstants.CENTER);
		this.setHorizontalTextPosition(SwingConstants.CENTER);
		this.setForeground(Color.white);

	}

	@Override
	public void chooseyes() {

	}

	@Override
	public void chooseno() {

	}

	@Override
	public void nochoose(MouseEvent e) {
		if (caozuo == 1 || caozuo == 2) {
			iphoneVerifyPanel.changeMenu(caozuo);
		} else if (caozuo == 3) {
			String verification = iphoneVerifyPanel.getVerificationText().getText();
			if (verification == null || "".equals(verification) || iphoneVerifyPanel.getVerification().equals("error") || !iphoneVerifyPanel.getVerification().equals(verification)) {
				ZhuFrame.getZhuJpanel().addPrompt2("验证码为空或者不正确");
				return;
			}
			PhoneNumberSGBean numberSGBean = new PhoneNumberSGBean();
			String iphone = iphoneVerifyPanel.getIphoneText().getText();
			if (iphone != null && !"".equals(iphone) && iphone.equals(iphoneVerifyPanel.getIphoneNumber())) {
				numberSGBean.setPhone(iphone);
			} else {
				ZhuFrame.getZhuJpanel().addPrompt2("手机号为空或者不正确");
				return;
			}
			String safety = iphoneVerifyPanel.getSafetyText().getText();
			if (safety != null && !"".equals(safety)) {
				numberSGBean.setSafenumber(safety);
			} else {
				ZhuFrame.getZhuJpanel().addPrompt2("安全码不能为空");
				return;
			}
			String sendmes = null;
			if (iphoneVerifyPanel.getTypeMenu() == 1) {// 绑定
				sendmes = Agreement.getAgreement().PhoneBangAgreement(GsonUtil.getGsonUtil().getgson().toJson(numberSGBean));
			} else if (iphoneVerifyPanel.getTypeMenu() == 2) {// 解绑
				sendmes = Agreement.getAgreement().UnPhoneBangAgreement(GsonUtil.getGsonUtil().getgson().toJson(numberSGBean));
			}
			SendMessageUntil.toServer(sendmes);
		} else if (caozuo == 4) {
			iphoneVerifyPanel.sendIphonegetVerification();
		}
	}

}
