package jxy2.xbao;

import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.GoodsListFromServerUntil;

import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

public class XuanyinInlayMouse extends TemplateMouseListener {
    public  XuanyinInlayJPanel xuanyinInlayJPanel;
    public static List<String> list =new ArrayList<>();
    public int i;
    public XuanyinInlayMouse(int i, XuanyinInlayJPanel xuanyinInlayJPanel) {
        this.i = i;
        this.xuanyinInlayJPanel = xuanyinInlayJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        list.add(i+"");
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        list.remove(i+"");
        // 检查界面位置i是否有效
        if (i >= xuanyinInlayJPanel.getList().size()) return;
        // 直接从list中获取物品
        Goodstable goodstable = xuanyinInlayJPanel.getList().get(i);
        if (goodstable==null)return;
        Xuanyin(goodstable, i);
    }

    private void Xuanyin(Goodstable goodstable, int i) {
        Xuanbao xuanbao= RoleXuanbao.getRoleXuanbao().choseBao;
        if (xuanbao==null){
            ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的玄宝");
            return;
        }
        //先判断灵宝是否有足够的格子
        String[] fuids=null;
        if (xuanbao.getBaomarkid()!=null&& !xuanbao.getBaomarkid().isEmpty()) {
            fuids=xuanbao.getBaomarkid().split("\\|");
        }
        String[] split = xuanbao.getBaomarkactive().split("\\|");
        if ((fuids==null&&split[i].equals("1"))||(fuids!=null&&fuids.length<split.length)) {
            //在判断否有由同类型的符石存在
            if (RoleXuanbao.getRoleXuanbao().Xuanyinty(fuids, goodstable.getGoodsname())) {
                //开始装备
                RoleXuanbao.getRoleXuanbao().xuanyinchange(xuanbao, goodstable, true);
                // 使用原始包裹位置删除物品
                Integer originalIndex = xuanyinInlayJPanel.getSumMap().get(i);
                if (originalIndex != null) {
                    GoodsListFromServerUntil.Deleted(originalIndex + GoodsListFromServerUntil.Pagenumber*24);
                }
            }else {
                ZhuFrame.getZhuJpanel().addPrompt2("你已经穿戴了同类型的玄印");
            }
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        // 检查界面位置i是否有效
        if (i >= xuanyinInlayJPanel.getList().size()) return;
        // 直接从list中获取物品
        Goodstable goodstable = xuanyinInlayJPanel.getList().get(i);
        if (goodstable==null)return;
        ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }


}
