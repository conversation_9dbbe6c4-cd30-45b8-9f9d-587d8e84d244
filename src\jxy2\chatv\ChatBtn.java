package jxy2.chatv;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.login.GameView;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseMotionAdapter;

public class ChatBtn extends MoBanBtn {
    // 用于记录拖动时的鼠标位置
    private int dragStartX, dragStartY;
    private boolean isDragging = false;
    public ChatJPanel chatJPanel;
    public Integer typeBtn;
    public int index;
    public ChatBtn(String iconpath, int type, ChatJPanel chatJPanel, int index,String names) {
        super(iconpath,0 ,type,0,0);
        this.chatJPanel = chatJPanel;
        this.index = index;
        if (!names.isEmpty()){
            this.setNtext(names);
        }
        // 添加鼠标移动监听器
        this.addMouseMotionListener(new MouseMotionAdapter() {
            @Override
            public void mouseDragged(MouseEvent e) {
                if (index == 17 && isDragging) {
                    // 计算鼠标移动距离
                    int dx = e.getXOnScreen() - dragStartX;
                    int dy = e.getYOnScreen() - dragStartY;
                    // 更新窗口位置
                    chatJPanel.RunYexcp(dx, dy, 2);
                    // 更新起始位置
                    dragStartX = e.getXOnScreen();
                    dragStartY = e.getYOnScreen();
                }
            }
        });
    }


    private boolean whether = false;// 判断点击第二下之后的效果
    private float currentAlpha = 1.0f; // 当前透明度，默认为1.0（不透明）

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        super.mousePressed(e); // 调用父类方法
        if (index == 17) { // 只有拖动按钮响应
            isDragging = true;
            dragStartX = e.getXOnScreen();
            dragStartY = e.getYOnScreen();
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        super.mouseReleased(e); // 调用父类方法
        if (index == 17) {
            isDragging = false;
        }
    }

    @Override
    public void nochoose(MouseEvent e) {
        if (index!=19&&index<8){
            String msg = chatJPanel.getBtnBasis().get(index).getNtext();
            if (e.getButton() == MouseEvent.BUTTON3){
                FrameMessageChangeJpanel.chatbox.removemsg();
            }
            EquiButtonClick(chatJPanel,index);
            switch (msg) {
                case "全":
                    FrameMessageChangeJpanel.chatbox.is = false;
                    break;
                case "帮":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#P";
                    break;
                case "战":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#Z";
                    break;
                case "系":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#T";
                    break;
                case "队":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#D";
                    break;
                case "信":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#X";
                    break;
                case "当":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#Q";
                    break;
                case "世":
                    FrameMessageChangeJpanel.chatbox.is = true;
                    FrameMessageChangeJpanel.chatbox.string = "#J";
                    break;
            }

            int w = ChatFrame.getChatFrame().getWidth()-38;
            FrameMessageChangeJpanel.chatbox.ChatFormsize(w);
            FrameMessageChangeJpanel.Out(true);
            SwingUtilities.invokeLater(() -> {
                Point point = new Point(0, FrameMessageChangeJpanel.chatbox.getHeight());
                chatJPanel.getjScrollPane().getViewport().setViewPosition(point);
            });
        }else {
            switch (index) {
                case 14:
                    //字体大小改变
                    chatJPanel.RunYexcp(0, 0, 1);
                    chatJPanel.is = !chatJPanel.is;
                    break;
                case 15:
                    ChatFrame.getChatJPanel().setVisible(false);
                    GameView.getFrameDialogSync().getDialog().setVisible(true);
                    GameView.getFrameDialogSync().getChangeJpanel().setChatModify();

                    // 设置视口视图
                    Component view = ChatFrame.getChatJPanel().getjScrollPane().getViewport().getView();
                    GameView.getFrameDialogSync().getChangeJpanel().getScrollPane().setViewportView(view);
                    FrameMessageChangeJpanel.refreshHeight(GameView.getFrameDialogSync().getChangeJpanel().getScrollPane());

                  // 清空目标容器，防止残留
                    FrameMessageChangeJpanel.chatbox1.getChatlabels().clear();
                    FrameMessageChangeJpanel.chatbox2.getChatlabels().clear();

                    // 只遍历一次，严格分配
                    for (ChatRichLabel label : FrameMessageChangeJpanel.chatbox.getChatlabels()) {
                        String text = label.getText().trim(); // 去除首尾空白字符
                        if (text.startsWith("#J") || text.startsWith("#T")) {
                            // 以#J或#j开头的消息，只加入chatbox1
                            FrameMessageChangeJpanel.chatbox1.getChatlabels().add(label);
                        } else {
                            // 其余的消息，只加入chatbox2
                            FrameMessageChangeJpanel.chatbox2.getChatlabels().add(label);
                        }

                    }
                    ChatFrame.getChatJPanel().RunYexcp(0,0,1);
                    break;
                case 16://滚动按钮
                    FrameMessageChangeJpanel.setAutoScrollEnabled(whether);
                    chatJPanel.BOTTOM_ICON(whether);
                    whether = !whether;
                    break;
                case 18://透明度调整
                    // 在0.2F、0.5F、1.0F之间循环切换透明度
                    if (currentAlpha == 1.0f) {
                        currentAlpha = 0.2f;
                    } else if (currentAlpha == 0.2f) {
                        currentAlpha = 0.5f;
                    } else {
                        currentAlpha = 1.0f;
                    }
                    ChatJPanel.alps = currentAlpha;
                    break;
                case 19: //聊天记录

                    break;
            }
        }

    }
    public static void EquiButtonClick(ChatJPanel chatJPanel,int clickedIndex) {
        if (clickedIndex==-1)return;
        chatJPanel.getBtnBasis().get(clickedIndex).btnchange(1);
        chatJPanel.getBtnBasis().get(clickedIndex).setBtn(-1);
        chatJPanel.getBtnBasis().get(clickedIndex).setColors(new Color[]{null});
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <chatJPanel.getBtnBasis().size(); i++) {
            if (i != clickedIndex) {
                chatJPanel.getBtnBasis().get(i).btnchange(0);
                chatJPanel.getBtnBasis().get(i).setBtn(1);
                chatJPanel.getBtnBasis().get(i).setColors(Util.SwitchUI==1? UIUtils.COLOR_BTNTEXT:UIUtils.COLOR_RED);
            }
        }
    }
}
