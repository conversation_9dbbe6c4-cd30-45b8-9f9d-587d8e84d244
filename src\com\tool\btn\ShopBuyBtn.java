package com.tool.btn;

import java.awt.Color;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.GoodDetailedJframe;
import org.come.Frame.ZhuFrame;
import org.come.model.Eshop;
import org.come.until.FormsManagement;
import org.come.until.Util;

import com.tool.tcpimg.UIUtils;

public class ShopBuyBtn extends MoBanBtn {

	/** 商城购买按钮1 */
	private int caozuo;
	private Eshop eshop;

	public ShopBuyBtn(String iconpath, int type, String text, int caozuo, Eshop eshop) {
		super(iconpath, type);
		// TODO Auto-generated constructor stub
		this.caozuo = caozuo;
		this.eshop = eshop;
		this.setText(text);
		setFont(UIUtils.TEXT_FONT);
		setForeground(Color.white);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	@Override
	public void chooseyes() {

	}

	@Override
	public void chooseno() {

	}

	@Override
	public void nochoose(MouseEvent e) {
		switch (caozuo) {
		case 1:
			if (eshop == null) {
				return;
			}
			if (!Util.canBuyOrno) {
				ZhuFrame.getZhuJpanel().addPrompt2("背包没有解锁!!");
				return;
			}
			GoodDetailedJframe.getGoodDetailedJframe().getGoodDetailedJpanel().gainGoodsMessage(eshop);
			FormsManagement.showForm(44);
			break;
		default:
			break;
		}
	}

	public int getCaozuo() {
		return caozuo;
	}

	public void setCaozuo(int caozuo) {
		this.caozuo = caozuo;
	}

	public Eshop getEshop() {
		return eshop;
	}

	public void setEshop(Eshop eshop) {
		this.eshop = eshop;
	}

}
