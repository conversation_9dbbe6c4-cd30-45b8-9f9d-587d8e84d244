package jxy2.guaed.fl;

import javax.swing.*;
import java.awt.*;

public class FuLingCourdJPanel extends JPanel {
    private CardLayout cardLayout;
    private AsoulJPanel asoulJPanel;//附灵魂
    private RonglianJPanel ronglianJPanel;//融炼
    public FuLingCourdJPanel() {
        setBounds(0, 0, 633, 507);
        setOpaque(false);
        cardLayout = new CardLayout();
        setLayout(cardLayout);
        asoulJPanel = new AsoulJPanel();
        add(asoulJPanel, "asoul");
        ronglianJPanel = new RonglianJPanel();
        add(ronglianJPanel, "ronglian");
    }

    public CardLayout getCardLayout() {
        return cardLayout;
    }

    public void setCardLayout(CardLayout cardLayout) {
        this.cardLayout = cardLayout;
    }

    public AsoulJPanel getAsoulJPanel() {
        return asoulJPanel;
    }

    public void setAsoulJPanel(AsoulJPanel asoulJPanel) {
        this.asoulJPanel = asoulJPanel;
    }

    public RonglianJPanel getRonglianJPanel() {
        return ronglianJPanel;
    }

    public void setRonglianJPanel(RonglianJPanel ronglianJPanel) {
        this.ronglianJPanel = ronglianJPanel;
    }
}
