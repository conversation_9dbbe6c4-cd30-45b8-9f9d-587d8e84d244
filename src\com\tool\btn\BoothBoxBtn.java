package com.tool.btn;

import java.awt.Color;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.TradeJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.BoothBoxJpanel;
import org.come.bean.RoleShow;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import com.tool.Stall.Commodity;
import com.tool.Stall.Stall;
import com.tool.Stall.StallBean;
import com.tool.image.ImageMixDeal;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;

public class BoothBoxBtn extends MoBanBtn {

    private BoothBoxJpanel boxJpanel;

    /**
     * 
     * @param path
     * @param type
     * @param text
     * @param caozuo
     *            1上架 2下架 3摆摊 4收摊
     * @param boxJpanel
     */
    public BoothBoxBtn(String path, int type, Color[] colors, String text, BoothBoxJpanel boxJpanel) {
        super(path, type, colors);
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(UIUtils.TEXT_HY16);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.boxJpanel = boxJpanel;
    }

    // public BoothBoxBtn(String path, int type, String text, BoothBoxJpanel
    // boxJpanel) {
    // super(path, type);
    // this.setText(text);
    // setFont(UIUtils.TEXT_FONT1);
    // setForeground(UIUtils.getColor("orange"));
    // setVerticalTextPosition(SwingConstants.CENTER);
    // setHorizontalTextPosition(SwingConstants.CENTER);
    // this.boxJpanel = boxJpanel;
    // }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        Stall stall = boxJpanel.getStall();
        if ("上架".equals(getText())) {
            if (stall.getId() > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你已经在摆摊了");
                return;
            }
            if (FormsManagement.getframe(14).isVisible()) {
                return;
            }
            boxJpanel.SJ();
        } else if ("下架".equals(getText())) {
            if (stall.getId() > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你已经在摆摊了");
                return;
            }
            Commodity commodity = boxJpanel.getCommodity();
            if (commodity == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选中的商品");
                return;
            }
            int i = boxJpanel.getStall().Buy(commodity);
            if (i == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("不是已上架的商品");
            } else {

                if (commodity.getGood() != null) {
                    boxJpanel.GoodsListLabel[i].setIcon(null);
                    boxJpanel.Usetime[i] = null;
                    GoodsListFromServerUntil.stall2(commodity.getGood());
                } else if (commodity.getPet() != null) {
                    boxJpanel.PetsListLabel[i].setIcon(null);
                    // 补充
                    int p = 0;
                    for (int j = UserMessUntil.getPetListTable().size() - 1; j >= 0; j--) {
                        if (UserMessUntil.getPetListTable().get(j).getSid().compareTo(commodity.getPet().getSid()) == 0) {
                            p = -1;
                        }
                    }
                    if (p != -1) {
                        UserMessUntil.getPetListTable().add(commodity.getPet());
                        TradeJframe.getTradejframe().getTradejpanel().getModelname()
                                .addElement(commodity.getPet().getSummoningname());
                    }
                }
                boxJpanel.XZBuy(null);
            }
        } else if ("摆摊".equals(getText())) {
            // 5977x3099
            // 8290x4174
            RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
            if (roleShow.getMapid() != 1236) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getX() < 5977 || roleShow.getX() > 8290) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getY() < 3099 || roleShow.getY() > 4174) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getTroop_id() != null || FormsManagement.getframe(14).isVisible() || FightingMixDeal.State != HandleState.USUAL) {
                ZhuFrame.getZhuJpanel().addPrompt2("当前状态不能摆摊！");
            }
            if (stall.getId() > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你已经在摆摊了");
                return;
            }
            if (roleShow.getGrade() <= 102) {
                ZhuFrame.getZhuJpanel().addPrompt2("转生之后才可以摆摊");
                return;
            }
            if (roleShow.getMount_id() != 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("在摆摊了还要骑着坐骑干什么");
                return;
            }
            stall.setMapid(Util.ditubianma);
            stall.setRoleid(roleShow.getRole_id());
            stall.setRole(roleShow.getRolename());
            stall.setStall(roleShow.getRolename());

            stall.setState(StallBean.PREPARE);
            stall.setId(1);
            stall.setX(roleShow.getX() - 50);
            stall.setY(roleShow.getY() - 135);
            String sendMes = Agreement.getAgreement().stallAgreement(GsonUtil.getGsonUtil().getgson().toJson(stall));
            SendMessageUntil.toServer(sendMes);
            setText("收摊");
            FormsManagement.HideForm(15);
            FormsManagement.HideForm(16);
        } else if ("收摊".equals(getText())) {
            RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
            stall.setMapid(Util.ditubianma);
            stall.setRoleid(roleShow.getRole_id());
            stall.setRole(roleShow.getRolename());
            stall.setStall(roleShow.getRolename() + "的摊位");
            stall.setState(StallBean.NO);
            String sendMes = Agreement.getAgreement().stallAgreement(GsonUtil.getGsonUtil().getgson().toJson(stall));
            SendMessageUntil.toServer(sendMes);
            setText("摆摊");
        }
    }

}
