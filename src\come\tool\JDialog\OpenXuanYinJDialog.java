package come.tool.JDialog;

import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class OpenXuanYinJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            String[] msgs = ((String) object).split("\\|");
            String msg = Agreement.getAgreement().XuanbaoOpenXyinAgreement("L"+ msgs[0]+"|"+(Integer.parseInt(msgs[1])+1));
            SendMessageUntil.toServer(msg);
        }
    }
}
