package com.tool.btn;

import java.awt.event.MouseEvent;

import org.come.view.TaskGuideView;

public class TaskGuideBtn extends MoBanBtn {

	private int caozuo;//0左右扩展 1上下扩展
	private TaskGuideView taskGuideView;
	
	public TaskGuideBtn(String iconpath, int type, int caozuo, TaskGuideView taskGuideView) {
		// TODO Auto-generated constructor stub
		super(iconpath, type);
		this.caozuo=caozuo;
		this.taskGuideView=taskGuideView;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub
		taskGuideView.guideShow(caozuo);
		if (caozuo==1) {
			taskGuideView.DJ(e.getX(), e.getY());
		}
	}

}
