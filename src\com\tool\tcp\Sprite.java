package com.tool.tcp;

import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;


/**精灵*/
public class Sprite {
	private int Use=0;//使用记录
	private int load=-1;
	private Frame[] frames;
	private Frame currFrame;// 当前帧
	private int animCount;//方向数
	private int frameCount;//帧数
	private int refPixelX;
	private int refPixelY;
	private int hightMax;//最高距离
	private SpriteHead head;
	public Sprite(Image image) {
		// TODO Auto-generated constructor stub
		frames=new Frame[1];
		frames[0]=new Frame(image,0,0);
		currFrame=frames[0];
		animCount=1;
		frameCount=1;
	}
	public Sprite(Frame[] frames, int animCount, int frameCount,SpriteHead head,int refPixelX,int refPixelY) {
		super();
		this.frames = frames;
		this.currFrame=frames[0];
		this.animCount = animCount;
		this.frameCount = frameCount;
		this.head = head;
		this.refPixelX=refPixelX;
		this.refPixelY=refPixelY;
	}
	public boolean contains(int x, int y) {
        try {
        	if (currFrame.getImage()==null) {return false;}	
        	Image bi=currFrame.getImage();
        	if (currFrame.getPos()!=0) {
        		x=currFrame.getRefPixelX()-x;
        	}else { 
        		x+=currFrame.getRefPixelX();

        	}   	
        	y+=currFrame.getRefPixelY();
        	if ((x >= 0 && x < bi.getWidth(null)) && (y >= 0 && y < bi.getHeight(null))) {
        		if (bi instanceof BufferedImage) {
            		BufferedImage bimage = (BufferedImage) bi;
            		return bimage.getRGB(x, y) != 0;
    			} else {
    				return true;
    			}	
        	}
    		
        } catch (Exception e) {
        	e.printStackTrace();
        }
        return false;
    }
	private static AffineTransform transform=new AffineTransform();


	public void draw(Graphics g, int x, int y) {
		if (currFrame.getImage()!=null) {
			if (currFrame.getPos()==0) {
				x -= currFrame.getRefPixelX();
		        y -= currFrame.getRefPixelY();
		        g.drawImage(currFrame.getImage(), x, y,currFrame.getWidth(),currFrame.getHeight(), null);
			}else {//反向绘制
				Graphics2D g2 = (Graphics2D)g;
			    x += currFrame.getRefPixelX();
			    y -= currFrame.getRefPixelY();
			    transform.setToIdentity();
				transform.scale(-1, 1);
				transform.translate(-x  , y);	
			    g2.drawImage(currFrame.getImage(),transform,null);
		    }
	    }
    }
	public void draw(Graphics g, int x, int y,int win) {
		if (currFrame.getImage()!=null) {
			if (currFrame.getPos()==0) {
				x -= currFrame.getRefPixelX();
		        y -= currFrame.getRefPixelY();
		        g.drawImage(currFrame.getImage(), x, y,win,currFrame.getHeight(), null);
			}else {//反向绘制
				Graphics2D g2 = (Graphics2D)g;
			    x += currFrame.getRefPixelX();
			    y -= currFrame.getRefPixelY();
			    transform.setToIdentity();
				transform.scale(-1, 1);
				transform.translate(-x  , y);
			    g2.drawImage(currFrame.getImage(),transform,null);
		    }
	    }
    }
	public void draw(Graphics g, int x, int y,int wid,int heig) {
		if (currFrame.getImage()!=null) {
			if (currFrame.getPos()==0) {
				x -= currFrame.getRefPixelX();
				y -= currFrame.getRefPixelY();
				currFrame.width =wid;
				currFrame.height =heig;
				g.drawImage(currFrame.getImage(), x, y,currFrame.width,currFrame.height, null);
			}else {//反向绘制
				Graphics2D g2 = (Graphics2D)g;
				x += currFrame.getRefPixelX();
				y -= currFrame.getRefPixelY();
				transform.setToIdentity();
				transform.scale(-1, 1);
				transform.translate(-x  , y);
				g2.drawImage(currFrame.getImage(),transform,null);
			}
		}
	}

	public static AlphaComposite Composite06=AlphaComposite.getInstance(AlphaComposite.SRC_OVER,0.6f);
    public void draw(Graphics g, int x, int y, float alpha) {
    	if (alpha==1.0F) {
        	draw(g, x, y); 	
		}else {
			Graphics2D g2 = (Graphics2D) g;
			Composite composite =null;
			try {
				if (alpha==0.6f) {
					composite =g2.getComposite();
					g2.setComposite(Composite06);	
				}else {
					composite =g2.getComposite();
					g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER,alpha));	
				}
				draw(g2, x, y);	
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			} finally {
				if (composite!=null) {
					g2.setComposite(composite);	
				}	
			}	
		}
    }
    public void updateToTime(long playTime,int dir){
        try {
        	if (playTime<0) {
        		playTime%=getTime();
        		playTime+=getTime();
			}
        	Use=1;
            dir%=animCount;
            playTime/=SpriteFactory.ANIMATION_INTERVAL;
            playTime%=frameCount;
            currFrame=frames[(int)(dir*frameCount+playTime)];
            if (load==-1&&currFrame.getImage()==null) {
            	load=dir;
            	SpriteFactory.addLoad(this);
			}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public int getAnimationCount() { return animCount; }
    public int getFrameCount() { return frameCount; }
    public int getTime() { return frameCount*SpriteFactory.ANIMATION_INTERVAL; }
	public SpriteHead getHead() { return head; }
	public void setHead(SpriteHead head) { this.head = head; }
	public int getLoad() { return load; }
	public void setLoad(int load) { this.load = load; }
	public Frame getLoadFrame(int dir) { return frames[dir*frameCount]; }
	public Frame[] getFrames() { return frames; }
	public void removeHead() { //都加载完了 就清除
		this.load=-1;
		for (int i = 0; i < frames.length; i++) {
			if (frames[i].getImage()==null) {setHightMax();return;}
		}
		this.head.remve();
		this.head=null;
		setHightMax();
	}
	public void setHightMax() {
		int max=-99999;
		for (int i = 0; i < frames.length; i++) {
			Frame frame=frames[i];
			if (frame.getImage()==null) { continue; }
			if (frame.getRefPixelY()+frame.getHeight()>max) { max=frame.getRefPixelY()+frame.getHeight(); }
		}
		if (max==-99999) { max=0; }
		hightMax=max;
		if (hightMax>=200) { hightMax-=110; }
	}
	public int getHightMax() { return hightMax; }
	public int getUse() { return Use; }
	public void setUse(int use) { Use = use; }
	public Frame getCurrFrame() { return currFrame; }
	public int getRefPixelX() { return refPixelX; }
	public int getRefPixelY() { return refPixelY; }
	/**旋转共用*/
	public void translate(int dir1,int dir2) {
		for (int i = 0; i < frameCount; i++) {
			Frame frame=frames[dir1*frameCount+i];
			Frame frame2=frames[dir2*frameCount+i];
			frame2.setImage(frame.getImage(),frame.getRefPixelX(),frame.getRefPixelY());
		}
	}

}

