package jxy2.supet;

import jxy2.UniversalModel;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class SipetSortMouse extends TemplateMouseListener {
    public JList<UniversalModel> listpet;// 召唤兽列表
    public SipetSortJPanel sipetSortJPanel;

    public SipetSortMouse(JList<UniversalModel> listpet, SipetSortJPanel sipetSortJPanel) {
        this.listpet = listpet;
        this.sipetSortJPanel = sipetSortJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (listpet.getSelectedIndex()!=-1) {
            RoleSummoning roleSummoning = UserMessUntil.getPetListTable().get(listpet.getSelectedIndex());
//            System.out.println(roleSummoning.getSummoningname());
            UserMessUntil.setChosePetMes(roleSummoning);
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        SipetSortJPanel.idx =-1;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        if (listpet!=null){
            if (e.getY() / 35 < sipetSortJPanel.listModel.getSize()){
                SipetSortJPanel.idx = sipetSortJPanel.listpet.locationToIndex(e.getPoint());
            }
        }
    }
}
