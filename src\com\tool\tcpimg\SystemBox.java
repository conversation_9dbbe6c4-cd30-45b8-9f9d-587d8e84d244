package com.tool.tcpimg;

import java.awt.Graphics;
import java.util.ArrayList;
import java.util.List;

import org.come.until.ScrenceUntil;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.Frame;
import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;

public class SystemBox {

	private RichLabel text;
	private int length;
	private int value;
	private int height;
	private List<String> line;
	private Sprite tcp;
	private String skin;
	public SystemBox(String text,String skin) {
		// TODO Auto-generated constructor stub
		this.skin=skin;
		CZ(text);
	}
	public void draw(Graphics g) {
		// TODO Auto-generated method stub
		if (text==null) {
			return;
		}
		value++;
		if (value<length) {
			Graphics g2 = g.create(0,ScrenceUntil.Screen_y-90,ScrenceUntil.Screen_x,60);
			if (tcp==null) {
				tcp=SpriteFactory.Prepare(GetTcpPath.getMouseTcp(skin));
			}else {
				tcp.updateToTime(ImageMixDeal.userimg.getTime(), 0);
				Frame frame=tcp.getCurrFrame();
				if (frame!=null&&frame.getImage()!=null) {
					g2.drawImage(frame.getImage(),2-frame.getRefPixelX(),2-frame.getRefPixelY(),ScrenceUntil.Screen_x,frame.getHeight(), null);
				}
			}
			g2.translate(ScrenceUntil.Screen_x-value,height);
			text.paint(g2);
			g2.dispose();
		}else {
			CZ(null);			
		}
	}
	public void addText(String text){
	    if (this.text==null) {
	    	CZ(text);
		}else {
			if (line==null) {
				line =new ArrayList<>();
			}
			line.add(text);
		}
	}
	public void CZ(String text){
		if (text==null) {
			if (line!=null&&line.size()!=0) {
				text=line.remove(0);
			}
		}else if (line!=null) {
			line.remove(text);
		}
		if (text==null) {
			this.tcp=null;
			this.text=null;
			return;
		}
		if (this.text==null) {
			this.text=new RichLabel(text, UIUtils.TEXT_NAME_FONT,9999);
		}else {
			this.text.setTextSize(text,9999);
		}
		this.length=ScrenceUntil.Screen_x+this.text.getWidth();
		this.value=0;
		this.height=(int)(38-this.text.getSize().getHeight());
	}	
	
}
