package jxy2.friend;

import com.tool.tcpimg.UIUtils;
import org.come.Frame.FriendChatMessageJframe;
import org.come.Jpanel.FriendChatMessageJpanel;
import org.come.Jpanel.TestfriendlistJapnel;
import org.come.entity.Friendtable;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class FriendMouse implements MouseListener {
    public TestfriendlistJapnel testfriendlistJapnel;
    public int  index;
    public Friendtable friendtable;

    public FriendMouse(TestfriendlistJapnel testfriendlistJapnel, int index,Friendtable friendtable) {
        this.testfriendlistJapnel = testfriendlistJapnel;
        this.index = index;
        this.friendtable = friendtable;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        FriendChatMessageJframe chatFrame = FormsManagement.getOrCreateFriendChatFrame(friendtable.getRolename());
        FriendChatMessageJpanel chatPanel = chatFrame.getJpanel();
        if (chatPanel == null) {
            chatPanel = new FriendChatMessageJpanel();
            chatFrame.setChatPanel(friendtable.getRolename(), chatPanel);
        }
        chatPanel.showFriend(friendtable, MessagrFlagUntil.getRichLabel(friendtable.getRolename()));
        chatFrame.setBounds(5+index*60 ,5+index*60,356,410);
        // 显示聊天窗口
        chatFrame.showFriend(chatPanel);  // 显示独立的聊天窗口
        testfriendlistJapnel.getFriendModelMap().get(index).setX1(38);
        testfriendlistJapnel.getFriendModelMap().get(index).setY1(8);
        testfriendlistJapnel.getFriendModelMap().get(index).setColor(UIUtils.COLOR_TREASURE);
        for (int i = 0; i < testfriendlistJapnel.getFriendModelMap().size(); i++) {
            if (i!=index){
                testfriendlistJapnel.getFriendModelMap().get(i).setColor(UIUtils.COLOR_White);
            }
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        testfriendlistJapnel.getFriendModelMap().get(index).setX1(37);
        testfriendlistJapnel.getFriendModelMap().get(index).setY1(7);
    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }
}
