package jxy2.zodiac;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.bean.ImgZoom;
import org.come.bean.LoginResult;
import org.come.login.SpriteBtn;

import javax.swing.Timer;
import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.*;

/**
* 十二生肖/星盘/天罡八卦主窗口/星盘
* Zodiac Star Chart, Heavenly Gang, Eight Trigrams, Main Window Star Chart
* <AUTHOR>
* @date 2024/11/18 下午10:37
*/
public class ChineseZodiacPanel extends JPanel {
    //创建8个动态按钮
    private SpriteBtn[] DynamicBtn = new SpriteBtn[8];
    private SpriteBtn XPBtnXP;
    private JLabel[] xpdj;
    private JLabel XPbacMgr,XPActivated;
    public Map<Integer, String[]> xpdlMap = new HashMap<>(); // 创建 Map 对象
    private int iu = 0;
    public ChineseZodiacPanel() {
        this.setPreferredSize(new Dimension(646, 464));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        XPBtnXP = new SpriteBtn("0x9FEA0004", 122, 378, true, 0);
        XPBtnXP.setBounds(122, 378, 63, 58);
        XPBtnXP.addMouseListener(new ChineseZodiacMouse(10, XPBtnXP, this));
        this.add(XPBtnXP);

        //初始化map值
        map.put(0, new int[][]{{250, 160}, {285, 240}, {388, 192},});
        mapname.put(0, new String[][]{{"天魁"}, {"天罡"}, {"天机"}, {"天猛"}});
        sdkillmap.put(0, new int[]{298, 88});

        XPbacMgr = new JLabel();
        XPbacMgr.setBounds(sdkillmap.get(iu)[0], sdkillmap.get(iu)[1], 52, 52);
        XPbacMgr.addMouseListener(new XpSkillMoues(this,iu,initSkillData(iu),0));
        add(XPbacMgr);

        XPActivated = new JLabel();
        XPActivated.setBounds(461, 356, 96, 92);
        XPActivated.addMouseListener(new XpSkillMoues(this,iu,"23009",1));
        add(XPActivated);

        xpdj = new JLabel[3];
        for (int k = 0; k < xpdj.length; k++) {
            xpdj[k] = new JLabel();
            xpdj[k].setBounds(map.get(iu)[k][0], map.get(iu)[k][1], 24, 24);
            xpdj[k].addMouseListener(new ChineseZodiacDjMouse(this, k, mapname.get(0)));
            this.add(xpdj[k]);
        }
           // 每个按钮的宽度和高度，按顺序排列
        int[][] buttonSizes = {
                {137, 65},    // 第1个按钮的宽度和高度
                {115, 113},   // 第2个按钮的宽度和高度
                {65, 137},    // 第3个按钮的宽度和高度
                {114, 115},   // 第4个按钮的宽度和高度
                {137, 65},    // 第5个按钮的宽度和高度
                {114, 116},   // 第6个按钮的宽度和高度
                {65, 140},    // 第7个按钮的宽度和高度
                {114, 113}    // 第8个按钮的宽度和高度
        };
        int[][] buxy = {
                {245, 3},    // 第1个按钮的X和Y
                {388, 28},   // 第2个按钮的X和Y
                {470, 140},    // 第3个按钮的X和Y
                {395, 281},   // 第4个按钮的X和Y
                {262, 367},    // 第5个按钮的X和Y
                {146, 291},   // 第6个按钮的X和Y
                {115, 154},    // 第7个按钮的X和Y
                {138, 38}    // 第8个按钮的X和Y
        };

        for (int i = 0; i < DynamicBtn.length; i++) {
            // 获取当前按钮的宽度和高度
            int buttonWidth = buttonSizes[i][0];
            int buttonHeight = buttonSizes[i][1];
            int x = buxy[i][0];
            int y = buxy[i][1];
            DynamicBtn[i] = new SpriteBtn("0x9FEA00" + (11 + i), x, y, true, 0);
            DynamicBtn[i].setBounds(x, y, buttonWidth, buttonHeight);
            DynamicBtn[iu].btn(1);
            DynamicBtn[iu].setXz(2);
            DynamicBtn[i].addMouseListener(new ChineseZodiacMouse(i, DynamicBtn[i], this));
            this.add(DynamicBtn[i]);
        }

//        setDynamicBtn(iu);
        RefreshDisplayEffect();
        arrayxp();

    }

 /** 初始化技能ID */
public String initSkillData(int i) {
    // 定义一个数组存储 skillid
    String[] skillIds = {"23008","23007","23006","23005","23004","23003","23002","23001"}; // 默认值
    // 检查索引是否有效
    if (i >= 1 && i <= skillIds.length - 1) {
        return skillIds[i];
    } else {
        return skillIds[0]; // 返回默认值
    }
}

    /**
     * 根据不同的ID开显示不同的组件
     */
    public void setDynamicBtn(int i) {
        clear();
        if (!map.containsKey(i)) {
            map.put(i, array(i));
        }
        if (!mapname.containsKey(i)) {
            mapname.put(i, arrayNmae(i));
        }
        if (!sdkillmap.containsKey(i)){
            sdkillmap.put(i, ActivateSkillDisplay(i));
        }
        iu = i;
        chang = GetTheCurrentNumberOFStarCharts(i);
        XPbackY = Juitil.WindowImgWdf("0x9FEA00" + (20 + i), 2, 2);


        XPbacMgr = new JLabel();
        XPbacMgr.setBounds(sdkillmap.get(iu)[0], sdkillmap.get(iu)[1], 52, 52);
        XPbacMgr.addMouseListener(new XpSkillMoues(this,iu,initSkillData(iu),0));
        add(XPbacMgr);

        xpdj = new JLabel[map.get(iu).length];
        for (int k = 0; k < xpdj.length; k++) {
            xpdj[k] = new JLabel();
            xpdj[k].setBounds(map.get(iu)[k][0], map.get(iu)[k][1], 24, 24);
            xpdj[k].addMouseListener(new ChineseZodiacDjMouse(this, k, mapname.get(iu)));
            if (iu==6&&k==3){
                xpdj[k].setBounds(map.get(iu)[k][0]-20, map.get(iu)[k][1]-20, 55, 55);
            }
            this.add(xpdj[k]);
        }
        arrayxp();
        RefreshDisplayEffect();
        //判断激活后按钮效果
        ActivatedSkillQuery(false);
    }
    /**激活技能查询*/
    public List<String> sumlist = new ArrayList<>();
    public void ActivatedSkillQuery(boolean is) {
         if(is){sumlist.clear();}
        String[] vs = RoleData.getRoleData().getPrivateData().getSkills().split("\\|");
        for (String v : vs) {
            if (v.startsWith("X")) {
                String[] vss = v.split("#");
                vss[0] = vss[0].substring(1);
                for (String s : vss) {
                    String[] ss = s.split("_");
                    if (!sumlist.contains(ss[1])&&!ss[1].equals("8")) {
                        sumlist.add(ss[1]);
                        setDynamicBtn(Integer.parseInt(ss[1]));
                        iu = Integer.parseInt(ss[1]);
                        DynamicBtn[iu].btn(1);
                        DynamicBtn[iu].setXz(2);
                         for (int j = 0; j < DynamicBtn.length; j++) {
                             if (iu != j) {
                                 DynamicBtn[j].btn(0);
                                 DynamicBtn[j].setXz(0);
                             }
                         }
                        break;
                    }
                }
            }
        }
    }

    /**清除组件*/
    private void clear() {
        xpdlMap.clear();
        sdkillmap.clear();
        if (xpdj != null) {
            for (JLabel jLabel : xpdj) {
                remove(jLabel);
            }
        }
        if (XPbacMgr!=null){
            remove(XPbacMgr);
        }

    }

    /**
     * 刷新显示效果
     */
    public void RefreshDisplayEffect() {
        //更新是否激活
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getOpenxp() != null && !result.getOpenxp().isEmpty()) {
            String[] pairs = result.getOpenxp().split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue[0].equals(iu + "")) {
                    String[] xpdl = keyValue[1].split("\\|");
                     Arrays.sort(xpdl); // 对数组进行排序
                    xpdlMap.put(iu, xpdl); // 将 xpdl 加入 Map
                    break;
                }
            }
        }
    }


   /**
 * 找到对应的坐标
 * @param i
 * @return
 */
public int[][] array(int i) {
    // 定义一个多维数组存储坐标
    int[][][] coordinateArray = {
        {{250, 160}, {285, 240}, {388, 192}}, // 默认值
        {{250, 130}, {225, 205}, {275, 265}, {335, 235}},
        {{225, 260}, {242, 197}, {297, 242}, {340, 115}, {395, 140}},
        {{260, 241}, {290, 198}, {318, 241}, {362, 111}, {386, 164}},
        {{229, 229}, {240, 176}, {307, 209}, {396, 187}, {386, 140}, {295, 130}},
        {{281, 119}, {352, 120}, {346, 176}, {423, 191}, {328, 260}},
        {{290, 100}, {283, 158}, {354, 202}, {385, 285}},
        {{390, 133}, {340, 198}, {232, 203}, {240, 265}}
    };

    // 检查索引是否有效
    if (i >= 1 && i < coordinateArray.length) {
        return coordinateArray[i];
    } else {
        return coordinateArray[0]; // 返回默认值
    }
}


   /**
 * 激活技能显示位置
 * @param i
 * @return
 */
public int[] ActivateSkillDisplay(int i) {
    // 定义一个数组存储技能显示位置
    int[][] skillPositions = {
        {298, 88}, // 默认值
        {368, 120},
        {403, 186},
        {370, 255},
        {298, 290},
        {231, 255},
        {188, 186},
        {231, 120}
    };

    // 检查索引是否有效
    if (i >= 1 && i < skillPositions.length) {
        return skillPositions[i];
    } else {
        return skillPositions[0]; // 返回默认值
    }
}



  /**
 * 加载对应的名称
 */
public String[][] arrayNmae(int i) {
    // 定义一个多维数组存储 buxy
    String[][][] buxyArray = {
        {{"天魁"}, {"天罡"}, {"天机"}, {"天猛"}}, // 默认值
        {{"天暗"}, {"天佑"}, {"天空"}, {"天速"}},
        {{"天异"}, {"天杀"}, {"天微"}, {"天究"}, {"天退"}},
        {{"天寿"}, {"天剑"}, {"天竞"}, {"天罪"}, {"天损"}},
        {{"天败"}, {"天牢"}, {"天慧"}, {"天暴"}, {"天哭"}, {"天巧"}},
        {{"天满"}, {"天孤"}, {"天伤"}, {"天立"}, {"天捷"}},
        {{"天威"}, {"天英"}, {"天贵"}, {"天富"}},
        {{"天闲"}, {"天勇"}, {"天雄"}, {"天猛"}}
    };

    // 检查索引是否有效
    if (i >= 1 && i <= buxyArray.length - 1) {
        return buxyArray[i];
    } else {
        return buxyArray[0]; // 返回默认值
    }
}



  /**
 * 获取当前星盘个数
 * @param i
 * @return
 */
private int GetTheCurrentNumberOFStarCharts(int i) {
    // 定义一个数组存储星盘个数
    int[] starChartCounts = {3, 4, 5, 5, 6, 5, 4, 4};
    // 检查索引是否有效
    if (i >= 0 && i < starChartCounts.length) {
        return starChartCounts[i];
    } else {
        return starChartCounts[0]; // 返回默认值
    }
}


    public int chang = 3;
    public Timer timer;
    private long time = 0;

    public void PlaySprite() {
        if (timer != null && timer.isRunning()) {
            time = 0;
            return;
        }//不重复创建
        timer = new Timer(10, e -> {
            // 每次定时器触发时增加进度
            time += 28L; // 假设每次增加5%
            // 当进度达到100%时停止定时器并执行逻辑
            if (time >= 1800) {
                timer.stop();
                time = 0;
            }
        });
        // 启动定时器
        timer.start();
    }

    public Map<Integer, int[][]> map = new HashMap<>();
    public Map<Integer, int[]> sdkillmap = new HashMap<>();
    public Map<Integer, String[][]> mapname = new HashMap<>();
    public ImgZoom XPbackY = Juitil.WindowImgWdf("0x9FEA0020", 2, 2);

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.XPback.updateToTime(ImageMixDeal.userimg.getTime(), 0);
        Juitil.XPback.draw(g, 0, 0);
        Juitil.ImngBack(g, XPbackY, 190, 80, 274, 274, 1);
        for (int i = 0; i < chang; i++) {
            JHTX(g, i);//显示已激活的连线效果
        }
        int skx = sdkillmap.get(iu)[0];
        int sky = sdkillmap.get(iu)[1];
        //判断是否已激活技能！技能只能激活一个，但是可以切换技能，有就是切换之后可以再次激活，每次消耗10点魂值
        if (!sumlist.isEmpty() &&sumlist.get(0).equals(iu+"")){
            g.drawImage(Juitil.tz263.getImage(), skx+2, sky+2, 52, 52, this);
        }else {
            g.drawImage(Juitil.convertToGray(Juitil.tz263.getImage()), skx+2, sky+2, 52, 52, this);
        }
        Juitil.ImngBack(g,Juitil.tz267,skx, sky,56,56,1);
        if (alltrue) {//全部镶嵌后激活技能特效
            Juitil.JHKSILL.updateToTime(ImageMixDeal.userimg.getTime(), 0);
            Juitil.JHKSILL.draw(g, skx-6, sky-6);
        }



        for (int i = 0; i < chang; i++) {
            int x = map.get(iu)[i][0];
            int y = map.get(iu)[i][1];
            if (xpdlMap.containsKey(iu) && containsValue(xpdlMap.get(iu), i + "")) {
                Juitil.XPBackDL.updateToTime(ImageMixDeal.userimg.getTime(), 0);
                Juitil.XPBackDL.draw(g, x + 10, y + 10);
            } else {
                g.drawImage(Juitil.tz262.getImage(), x, y, 24, 24, this);
            }
            Juitil.TextBackground(g, mapname.get(iu)[i][0], 13, x - 5, y - 16, UIUtils.COLOR_CCDDDDFF, UIUtils.FZCY_HY16);
        }

        //tz263
        Juitil.XPBackTX.updateToTime(ImageMixDeal.userimg.getTime(), 0);
        Juitil.XPBackTX.draw(g, 330, 210);
        if (time < 1800 && time != 0) {
            Juitil.XPBackYY.updateToTime(time, 0);
            Juitil.XPBackYY.draw(g, 325, 215);
        }
        for (SpriteBtn spriteBtn : DynamicBtn) {
            spriteBtn.draw(g);
        }
        XPBtnXP.draw(g);
        for (Integer key: allxpdlMap.keySet()) {
                  int[][] buxy = {
                {245, 3},    // 第1个按钮的X和Y
                {388, 28},   // 第2个按钮的X和Y
                {470, 140},    // 第3个按钮的X和Y
                {395, 281},   // 第4个按钮的X和Y
                {262, 367},    // 第5个按钮的X和Y
                {146, 291},   // 第6个按钮的X和Y
                {115, 154},    // 第7个按钮的X和Y
                {138, 38}    // 第8个按钮的X和Y
        };
            int x1 = buxy[key][0];
            int y1 = buxy[key][1];
            Juitil.XPDL[key].updateToTime(ImageMixDeal.userimg.getTime(), 0);
            Juitil.XPDL[key].draw(g, x1, y1);
            Juitil.XPJD[key].updateToTime(ImageMixDeal.userimg.getTime(), 0);
            Juitil.XPJD[key].draw(g, 508, 405);
        }
         for (int i = 0; i < sumlist.size(); i++) {
                     int[][] buxy = {
                {245, 8},    // 第1个按钮的X和Y
                {388, 28},   // 第2个按钮的X和Y
                {470, 140},    // 第3个按钮的X和Y
                {395, 281},   // 第4个按钮的X和Y
                {262, 367},    // 第5个按钮的X和Y
                {146, 291},   // 第6个按钮的X和Y
                {115, 154},    // 第7个按钮的X和Y
                {138, 30}    // 第8个按钮的X和Y
        };
            int Select = Integer.parseInt(sumlist.get(i));

            int x1 = buxy[Select][0];
            int y1 = buxy[Select][1];
            Juitil.XPJH[Select].updateToTime(ImageMixDeal.userimg.getTime(), 0);
            Juitil.XPJH[Select].draw(g, x1, y1);
           }

    }
     /**初始化连线参数*/
    public static  Map<Integer, List<Connection>> connectionMap = new HashMap<>();
      static {
          connectionMap.put(0, Arrays.asList(
                  new Connection(1, "0", Juitil.dj18, 278, 215),
                  new Connection(2, "1", Juitil.dj22, 350, 230)
          ));
          // 其他 case 的连接逻辑可以类似地添加到这里
          connectionMap.put(1, Arrays.asList(
                  new Connection(1, "0", Juitil.dj19, 250, 178),
                  new Connection(2, "1", Juitil.dj9, 250, 245),
                  new Connection(3, "2", Juitil.dj30, 320, 263)
          ));
          connectionMap.put(2, Arrays.asList(
                  new Connection(1, "0", Juitil.dj19, 246, 236),
                  new Connection(2, "1", Juitil.dj9, 280, 235),
                  new Connection(2, "4", Juitil.dj31, 360, 200),
                  new Connection(3, "2", Juitil.dj7, 300, 166),
                  new Connection(3, "4", Juitil.dj1, 380, 140),
                  new Connection(4, "3", Juitil.dj3, 272, 264)
          ));
          connectionMap.put(3, Arrays.asList(
                  new Connection(1, "0", Juitil.dj14, 290, 236),
                  new Connection(1, "2", Juitil.dj26, 318, 240),
                  new Connection(1, "4", Juitil.dj15, 342, 194),
                  new Connection(2, "4", Juitil.dj21, 360, 213),
                  new Connection(4, "3", Juitil.dj26, 380, 146)
          ));
          connectionMap.put(4, Arrays.asList(
                  new Connection(1, "0", Juitil.dj8, 245, 205),
                  new Connection(2, "1", Juitil.dj27, 280, 200),
                  new Connection(3, "2", Juitil.dj20, 363, 207),
                  new Connection(4, "3", Juitil.dj25, 402, 176),
                  new Connection(4, "5", Juitil.dj0, 348, 144),
                  new Connection(1, "5", Juitil.dj29, 278, 160)
          ));
          connectionMap.put(5, Arrays.asList(
                  new Connection(1, "0", Juitil.dj13, 320, 134),
                  new Connection(2, "1", Juitil.dj5, 360, 160),
                  new Connection(3, "2", Juitil.dj4, 390, 196),
                  new Connection(4, "3", Juitil.dj10, 386, 240)
          ));
          connectionMap.put(6, Arrays.asList(
                  new Connection(1, "0", Juitil.dj5, 300, 134),
                  new Connection(2, "1", Juitil.dj23, 333, 193),
                  new Connection(3, "2", Juitil.dj18, 385, 260)
          ));
          connectionMap.put(7, Arrays.asList(
                  new Connection(1, "0", Juitil.dj12, 380, 178),
                  new Connection(2, "1", Juitil.dj24, 290, 213),
                  new Connection(3, "2", Juitil.dj16, 243, 236),
                  new Connection(3, "1", Juitil.dj17, 305, 243)
          ));
      }
    /**
     * 绘制连线动态效果
     */
    public boolean alltrue = false;
      public void JHTX(Graphics g, int i) {
        String[] activated = xpdlMap.get(iu);
        if (activated == null) return;
        boolean containsI = Arrays.asList(activated).contains(String.valueOf(i));
        if (!containsI) return;
        List<Connection> connections = connectionMap.get(iu);
        if (connections != null) {
            for (Connection connection : connections) {
                if (connection.shouldDraw(i, activated)) {
                    connection.draw(g);
                }
            }
        }
    }
    /**判断8个星盘是否全部激活*/
    public Map<Integer, String[]> allxpdlMap = new HashMap<>(); // 创建 Map 对象
    private List<Integer> jhsum = new ArrayList<>();
    public void arrayxp() {
        allxpdlMap.clear();
        jhsum.clear();
         // 定义每个星盘的数据数量
        int[] changValues = {3, 4, 5, 5, 6, 5, 4, 4};
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (result.getOpenxp() != null && !result.getOpenxp().isEmpty()) {
            String[] pairs = result.getOpenxp().split("&");
            for (int i = 0; i < pairs.length; i++) {
                String[] keyValue = pairs[i].split("=", 2);
                for (int j = 0; j < keyValue.length; j++) {
                    String[] xpdl = keyValue[1].split("\\|");
                    Arrays.sort(xpdl); // 对数组进行排序
                    if (changValues[Integer.parseInt(keyValue[0])] == xpdl.length) {
                        allxpdlMap.put(Integer.parseInt(keyValue[0]), xpdl); // 将 xpdl 加入 Map
                    }
                }
            }
            // 检查每个星盘的数据是否全部激活
            for (Integer key: allxpdlMap.keySet()) {
                if (key.equals(iu)){
                    alltrue = true;
                    break;
                }else {
                    alltrue = false;
                }

            }
            if (allxpdlMap.size()>=8){
                System.out.println("当前角色所有星盘都已激活！！");
            }else {
                for (Integer key: allxpdlMap.keySet()) {
                        System.out.println("当前角色激活了"+key+"星盘都已激活！！");
                }
            }
            jhsum.addAll(allxpdlMap.keySet());
        }
    }

    /**
     * // 辅助方法，用于检查数组中是否包含指定的值
     *
     * @param array
     * @param value
     * @return
     */
    public boolean containsValue(String[] array, String value) {
        if (array == null) {
            return false;
        }
        for (String item : array) {
            if (item.equals(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * // 辅助方法，用于检查数组中是全部相等否包含指定的值
     *
     * @param array
     * @param value
     * @return
     */
    public boolean allcontainsValue(String[] array, String value) {
          if (array == null || array.length == 0) {
        return false;
    }

    // 如果 value 不在数组中，直接返回 false
    if (!Arrays.asList(array).contains(value)) {
        return false;
    }

    // 检查数组中是否所有值都等于 value
    for (String element : array) {
        if (!value.equals(element)) {
            return false;
        }
    }
    return true;
    }


    public SpriteBtn[] getDynamicBtn() {
        return DynamicBtn;
    }

    public void setDynamicBtn(SpriteBtn[] dynamicBtn) {
        DynamicBtn = dynamicBtn;
    }

    public int getIu() {
        return iu;
    }

    public void setIu(int iu) {
        this.iu = iu;
    }

    public List<Integer> getJhsum() {
        return jhsum;
    }

    public void setJhsum(List<Integer> jhsum) {
        this.jhsum = jhsum;
    }
}

