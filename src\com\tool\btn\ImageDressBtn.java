package com.tool.btn;

import java.awt.event.MouseEvent;

import org.come.Jpanel.ImageDressJpanel;
import org.come.until.CutButtonImage;

import com.tool.role.RoleTX;

public class ImageDressBtn extends MoBanBtn {

	private int caozuo;
	private ImageDressJpanel imageDressJpanel;

	public ImageDressBtn(String iconpath, int type, int caozuo, String text, ImageDressJpanel imageDressJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.imageDressJpanel = imageDressJpanel;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		// TODO Auto-generated method stub

		try {
			// 人物面板点击
			if (caozuo == 1) {// 特效
				imageDressJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B120.png"));
				imageDressJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B121.png"));
				imageDressJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B123.png"));
				imageDressJpanel.getBtnWing().setIcons(CutButtonImage.cuts("inkImg/button/B125.png"));
			} else if (caozuo == 2) {// 装饰品
				imageDressJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B119.png"));
				imageDressJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B122.png"));
				imageDressJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B123.png"));
				imageDressJpanel.getBtnWing().setIcons(CutButtonImage.cuts("inkImg/button/B125.png"));
			} else if (caozuo == 3) {// 足迹
				imageDressJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B119.png"));
				imageDressJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B121.png"));
				imageDressJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B124.png"));
				imageDressJpanel.getBtnWing().setIcons(CutButtonImage.cuts("inkImg/button/B125.png"));
			} else if (caozuo == 4) {// 翅膀
				imageDressJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B119.png"));
				imageDressJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B121.png"));
				imageDressJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B123.png"));
				imageDressJpanel.getBtnWing().setIcons(CutButtonImage.cuts("inkImg/button/B126.png"));
			}
			RoleTX.getRoleTX().Toggle(caozuo - 1, 0);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}

	}

}
