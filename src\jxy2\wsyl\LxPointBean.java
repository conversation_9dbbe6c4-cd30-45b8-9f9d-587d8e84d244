package jxy2.wsyl;

public class LxPointBean {
    /**行*/
    private Integer lv;
    /**表ID*/
    private Integer tableId;
    /**最大点数*/
    private Integer maxPoint;
    /**当前学习点数*/
    private Integer nowPoint;

    public LxPointBean(int lv, int id, int max, int now) {
        this.lv = lv;
        setTableId(id);
        setMaxPoint(max);
        setNowPoint(now);
    }

    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }

    public Integer getLv() {
        return lv;
    }

    public void setLv(Integer lv) {
        this.lv = lv;
    }

    public Integer getMaxPoint() {
        return maxPoint;
    }

    public void setMaxPoint(Integer maxPoint) {
        this.maxPoint = maxPoint;
    }

    public Integer getNowPoint() {
        return nowPoint;
    }

    public void setNowPoint(Integer nowPoint) {
        this.nowPoint = nowPoint;
    }
    public String setPoint(Integer nowPoint) {
        setNowPoint(nowPoint);
        if (getNowPoint()>getMaxPoint()) {
            return getMaxPoint().toString();
        }
        return getNowPoint()+"/"+getMaxPoint();
    }
}
