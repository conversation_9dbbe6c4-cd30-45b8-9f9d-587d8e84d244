package come.tool.Fighting;

import com.tool.image.ManimgAttribute;
import org.come.bean.PathPoint;
import org.come.bean.RoleShow;

import java.util.List;

public class FightingMove2 {
	/**
	 * 移动方案二 已知起点和终点 所需时间 时间点 换算位置
	 * 
	 * <AUTHOR>
	 */
	public static int getmove2(int s, int e, long time, long endtime) {
		return s + (int)((e - s) * getdou(time, endtime));
	}
	/**
	 * 百分比转换
	 */
	public static double getdou(long time, long endtime) {
		double b = (double) time / (double) endtime;
		return b > 1 ? 1 : b;
	}
	/**速度和流逝时间换算位置*/
	public static boolean getmovetime(Object object,List<PathPoint> list) {
		if (list==null||list.size()==0) { return true; }
		else if(list.size()==1){
			if (object instanceof Fightingimage) {
				Fightingimage fightingimage=(Fightingimage)object;
				fightingimage.setX(list.get(0).getX());
				fightingimage.setY(list.get(0).getY());
			}else if (object instanceof ShadowMode) {
				ShadowMode shadowMode=(ShadowMode)object;
				shadowMode.setX(list.get(0).getX());
				shadowMode.setY(list.get(0).getY());
			}
			return true; 
		}
		int x=0;
		int y=0;
		if (object instanceof Fightingimage) {
			Fightingimage fightingimage=(Fightingimage)object;
			x=fightingimage.getX();
			y=fightingimage.getY();
		}else if (object instanceof ShadowMode) {
			ShadowMode shadowMode=(ShadowMode)object;
			x=shadowMode.getX();
			y=shadowMode.getY();
		}
		PathPoint point1 = list.get(0);
		PathPoint point2 = list.get(1);
		long endtime1 = gettime(point2.getX()-point1.getX(), point2.getY()-point1.getY(), 1.5);
		long endtime2 = gettime(point2.getX()-x, point2.getY()-y, 1.5);
		endtime2=endtime1-endtime2+20;
		if (endtime2>=endtime1) {
			x = point2.getX();
			y = point2.getY();
			list.remove(0);
		}else {
			x = getmove2(point1.getX(),point2.getX(),endtime2,endtime1);
			y = getmove2(point1.getY(),point2.getY(),endtime2,endtime1);
		}
		if (object instanceof Fightingimage) {
			Fightingimage fightingimage=(Fightingimage)object;
			fightingimage.setX(x);
			fightingimage.setY(y);
			fightingimage.setDir(getdirTwo(point2.getX()-point1.getX(), point2.getY()-point1.getY()));
		}else if (object instanceof ShadowMode) {
			ShadowMode shadowMode=(ShadowMode)object;
			shadowMode.setX(x);
			shadowMode.setY(y);
		}
		return list.size()<=1; 
	}
	/**速度和流逝时间换算位置*/
	public static long getmovetime(ManimgAttribute attribute,RoleShow roleShow, double sp,long time, int dir) {
		// 计算长度
		List<PathPoint> list = roleShow.getPlayer_Paths();
		int x = roleShow.getX();
		int y = roleShow.getY();
		while (time > 0 && list.size() >1) {
			PathPoint point = list.get(0);
			PathPoint point2 = list.get(1);
			int dx = point2.getX()-point.getX();
			int dy = point2.getY()-point.getY();
			dir = getdir(dx, dy, dir);
			long endtime = gettime(dx, dy, sp);
			if (time >= endtime) {
				time -= endtime;
				x = point2.getX();
				y = point2.getY();
				list.remove(0);
				continue;
			}
			x = getmove2(point.getX(),point2.getX(),time,endtime);
			y = getmove2(point.getY(),point2.getY(),time,endtime);
			break;
		}
		roleShow.setX(x);
		roleShow.setY(y);
		attribute.setDir(dir);
		return time;
	}
	public static int getmove(int x,int min,int max,long time,long time2) {
		double sp=Math.abs(max-min);
		sp=sp/time2;
		time+=Math.abs(x-min)/sp;
		return getmove2(min,max, time, time2);
	}
	/**
	 * 根据速度和距离换算时间
	 */
	public static long gettime(long l, long m, double sp) {
		double move = Math.sqrt(l * l + m * m);
		return (long) (move / sp);
	}
	/**方向换算*/
	public static int getdirTwo(int dx, int dy) {
		int x = Math.abs(dx);
		int y = Math.abs(dy);
		if (dx>0&&dx>0) {
			if (x*3<y) {
				return 7;
			}else if (y*3<x) {
				return 3;
			}else {
				return 1;
			}
		}else if (dx<0&&dx<0) {
			if (x*3<y) {
				return 3;
			}else if (y*3<x) {
				return 7;
			}else {
				return 5;
			}
		}else if (dx>0) {
			return 3;
		}else {
			return 7;
		}
	}
	/**方向换算*/
	public static int getdir(int dx, int dy, int dir) {
		if (dx == 0 && dy == 0)
			return dir;
		int x = Math.abs(dx);
		int y = Math.abs(dy);
		if (dx == 0) {
			if (dy < 0) {
				dir = 6;
			} else {
				dir = 4;
			}
		} else if (dy == 0) {
			if (dx > 0) {
				dir = 7;
			} else {
				dir = 5;
			}
		} else if (x > 3 * y) {
			if (dx > 0) {
				dir = 7;
			} else {
				dir = 5;
			}
		} else if (y > 3 * x) {
			if (dy > 0) {
				dir = 4;
			} else {
				dir = 6;
			}
		} else if (dx > 0 && dy > 0) {
			dir = 0;
		} else if (dx > 0 && dy < 0) {
			dir = 3;
		} else if (dx < 0 && dy < 0) {
			dir = 2;
		} else if (dx < 0 && dy > 0) {
			dir = 1;
		}
		return dir;
	}
}
