package com.tool.ModerateTask;

import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.SplitStringTool;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import java.util.List;

public class TaskMixDeal {

	/**
	 * 判断是否领取过任务
	 * 放回显示的对话内容
	 */
	public static String taskoption(String id){
		TaskData taskData=UserMessUntil.getTaskData(Integer.parseInt(id));
		if (taskData==null)return null;
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			Task task=hero.getDoingTasks().get(i);
			if (task.getTaskData().getTaskName().equals(taskData.getTaskName())) {
				if (task.getTaskState()==TaskState.doTasking) {
					return "我要取消"+taskData.getTaskName();
				}else if (task.getTaskState()==TaskState.completeTask) {
					return "我要领取"+taskData.getTaskName()+"奖励";
				}
			}
		}
		if (taskData.getTaskID()>=3157&&taskData.getTaskID()<=3500) {
			int ID=TaskRoleData.SumReceive(taskData.getTaskSetID(), 3);
			if (taskData.getTaskID()!=ID+1) {return null;}
		}
		return taskData.getTaskOpen();
	}
	/**
	 * 判断是否有击杀npc
	 */
	public static TaskProgress KillNpc(int npcid){
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			Task task=hero.getDoingTasks().get(i);
			for (int j = 0; j < task.getProgress().size(); j++) {
				TaskProgress progress=task.getProgress().get(j);
				if (progress.getType()==2&&progress.getSum()<progress.getMax()) 
				if (progress.getDId()==npcid)return progress;
			}
		}
		return null;
	}
	public static long time;
	/**判断选项内容是否触发*/
	public static boolean isoption(int taskSetID,String msg,String npcway){
		if (npcway==null||npcway.equals("")) return false;
		Hero hero=Hero.getHero();
		String[] v=npcway.split(" ");
		S: for (int i = 0; i < v.length; i++) {
			List<String> taskids = SplitStringTool.splitString(v[i]);
			TaskData taskData = UserMessUntil.getTaskData(Integer.parseInt(taskids.get(0)));
			if (taskData == null) continue;
			for (int j = 0; j < hero.getDoingTasks().size(); j++) {
				Task task = hero.getDoingTasks().get(j);
				if (task.getTaskData().getTaskID() == taskData.getTaskID()) {
					if (task.getTaskState() == TaskState.doTasking) {
						if (msg == null) {continue S;}
						if (msg.equals("我要取消" + taskData.getTaskName())) {
							if (Util.getTime() < time + 600) {
								ZhuFrame.getZhuJpanel().addPrompt2("你需要等待1分钟才能再一次取消任务");
								return true;
							}
							time = Util.getTime();
							String mes = Agreement.getAgreement().TaskNAgreement("E" + task.getTaskId());
							SendMessageUntil.toServer(mes);
							return true;
						}
					}
				}
			}
			if (taskSetID != 0) {
				if (taskSetID==taskData.getTaskSetID()) {
					String taskId = taskids.get(Util.random.nextInt(taskids.size()));
					String mes = Agreement.getAgreement().TaskNAgreement("L" + taskId);
					SendMessageUntil.toServer(mes);
					return true;	
				}
			} else {
				if (taskData.getTaskOpen().equals(msg)) {
					String taskId = taskids.get(Util.random.nextInt(taskids.size()));
					String mes = Agreement.getAgreement().TaskNAgreement("L" + taskId);
					SendMessageUntil.toServer(mes);
					return true;
				}
			}
		}
		return false;
	}
	/**
	 * 判断击杀npc是属于那个任务
	 */
	public static Task isnpc(int npcid){
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			Task task=hero.getDoingTasks().get(i);
			for (int j = 0; j < task.getProgress().size(); j++) {
				TaskProgress progress=task.getProgress().get(j);
				if (progress.getType()==2&&progress.getDId()==npcid) {
					return task;
				}
			}
		}
		return null;
	}
	/**
	 * 判断任务野怪属于那个任务
	 */
	public static Task isrobot(TaskProgress taskProgress){
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			Task task=hero.getDoingTasks().get(i);
			for (int j = 0; j < task.getProgress().size(); j++) {
				TaskProgress progress=task.getProgress().get(j);
				if ((progress.getType()==0||progress.getType()==1)&&taskProgress.getDId()==progress.getDId()) {
					return task;
				}
				
			}
		}
		return null;
	}
	/**
	 * 判断是否可以触发
	 */
	public static boolean istrigger(int npcid,TaskProgress taskProgress){
		Task task=null;
		if (taskProgress!=null) 
		task=isrobot(taskProgress);
		else task=isnpc(npcid);

		if (task!=null)return CreateTask.getCreateTask().isReceive2(task);
		return false;
	}
	/**
	 * 判断是否可以传送
	 */
	public static boolean istp(){
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			TaskData taskData=hero.getDoingTasks().get(i).getTaskData();
			if (taskData.getIsTP()==1) return false;
		}
		return true;
	}
	/**
	 * 根据npc名称
	 */
	public static boolean isgive(String npc){
		Hero hero=Hero.getHero();
		for (int i = 0; i < hero.getDoingTasks().size(); i++) {
			Task task=hero.getDoingTasks().get(i);
			for (int j = 0; j < task.getProgress().size(); j++) {
				TaskProgress taskProgress=task.getProgress().get(j);
				if (taskProgress.getType()==4) {
					if (taskProgress.getGName().equals(npc)) {
						return true;
					}
				}
			}
		}
		return false;
	}
}
