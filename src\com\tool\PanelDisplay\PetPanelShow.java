package com.tool.PanelDisplay;

import com.tool.role.RoleData;
import jxy2.supet.JupoDanMouse;
import jxy2.supet.SipetFrame;
import jxy2.supet.SipetJPanel;
import org.come.Frame.MsgJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.*;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2017年11月23日 下午2:19:31
 */

// 点击召唤兽监听
public class PetPanelShow extends TemplateMouseListener {
    @Override
    protected void specificMousePressed(MouseEvent e) {
        ZhuJpanel.getLabpetimg().setBounds(getButtonBounds(1));
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        ZhuJpanel.getLabpetimg().setBounds(getButtonBounds(0));
        Show();
        if (e.getButton()==3){
            ZhuJpanel.setUseGoodsType(1);
            Util.StopFrame(67);
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        MsgJframe.getJframe().getJapnel().HPMax_min(4);
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        FormsManagement.HideForm(46);
        FormsManagement.HideForm(24);
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }

    /**
     * 将选中的召唤兽信息展示在面板信息里面
     */
    public static void ShowMesForJpanel() {
        // 进行面板展示
        // 使用异步处理显示宠物值
        try {
            PetAddPointMouslisten.showPetValue();
        } catch (Exception e) {
            System.err.println("[ERROR] 加载召唤兽面板数据失败: " + e.getMessage());
            e.printStackTrace();
        }

    }


    public static void Show() {
        if (!FormsManagement.getframe(144).isVisible()) {
            FormsManagement.showForm(144);
            Music.addyinxiao("开关窗口.mp3");// 打开面板
        } else {
            FormsManagement.HideForm(144);
            Music.addyinxiao("关闭窗口.mp3");
            return; // 如果是关闭面板，不需要执行后续代码
        }
        // 将召唤兽信息放入到列表中 - 预先获取列表数据
        List<RoleSummoning> pets = UserMessUntil.getPetListTable();
            // 选择召唤兽
            if (!pets.isEmpty()) {
                if (UserMessUntil.getChosePetMes() != null) {
                    // 每次点击召唤兽头像重置选中的召唤兽为参战的召唤兽
                    BigDecimal summoningId = RoleData.getRoleData().getLoginResult().getSummoning_id();
                    if (summoningId != null) {
                        RoleSummoning petRgid = UserMessUntil.getPetRgid(summoningId);
                        if (petRgid != null) {
                            UserMessUntil.setChosePetMes(petRgid);
                            SipetJPanel.newPart = UserMessUntil.getChosePetMes().getPart();
                            SipetFrame.getSipetFrame().getSipetJPanel().setChosePetMes(petRgid);
                        }
                    }
                } else {
                    UserMessUntil.setChosePetMes(pets.get(0));
                }
            }
            
            // 按顺序执行UI更新操作，避免多次重绘
            ShowMesForJpanel();
            JupoDanMouse.refreshPetSkills(UserMessUntil.getChosePetMes());
            SipetJPanel.getInstance().RefreshPetViewport();
            SipetFrame.getSipetFrame().getSipetJPanel().RefreshPetViewport();
            SipetFrame.getSipetFrame().getSipetJPanel().BUFFIN();
    }

    /**
     * 召唤兽头像位置
     * @return
     */
    public static Rectangle getButtonBounds(int type) {
        int types = ZhuFrame.getZhuJpanel().getType();
        Rectangle bounds;
                if (ZhuJpanel.fighting){
                    if (type==1){
                        bounds = new Rectangle(6, types==0?7:9, types==0?48:42, types==0?48:44);
                    }else{
                        bounds = new Rectangle(5, types==0?6:8, types==0?48:42, types==0?48:44);
                    }
                }else {
                    int  nx = Util.SwitchUI==1?0:-2;
                    int  nxt = Util.SwitchUI==1?9:8;
                    int  nxS = Util.SwitchUI==1?8:7;
                    int w = Util.SwitchUI==1?42:41;
                    int h = Util.SwitchUI==1?44:42;
                    if (type==1){
                        bounds = new Rectangle(ScrenceUntil.Screen_x - (types==0?(nx+310):272), types==0?7:nxt, types==0?48:w, types==0?48:h);
                    }else{
                        bounds = new Rectangle(ScrenceUntil.Screen_x - (types==0?(nx+311):273), types==0?6:nxS, types==0?48:w, types==0?48:h);
                    }
                }
        return bounds;
    }
}
