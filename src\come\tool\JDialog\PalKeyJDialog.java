package come.tool.JDialog;

import java.util.List;

import org.come.Frame.PartnerJframe;
import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.entity.Pal;
import org.come.model.PalData;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;

/**
 * <AUTHOR>
 * @time 2019年12月21日 上午10:12:00<br>
 * @class 类名:PalKeyJDialog 激活功能<br>
 */
public class PalKeyJDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi) {
            if (object instanceof PalData) {
                PalData palDate = (PalData) object;
                String sendmes = Agreement.getAgreement().userpalAgreement("C" + palDate.getPalId());
                SendMessageUntil.toServer(sendmes);
            } else if (object instanceof Pal) {
                ZhuFrame.getZhuJpanel().addPrompt2("该道具已经失效");
                return;
//                Pal pal = (Pal) object;
//                List<Goodstable> list = GoodsListFromServerUntil.chaxunss(7500);
//                int sum = 0;
//                for (int i = 0; i < list.size(); i++) {
//                    sum += list.get(i).getUsetime();
//                }
//                int breakLevel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel().getPartnerCardJpanel()
//                        .getPartnerEquipJpanel().isBreakLevel(pal.getLvl());
//                long palExp = PartnerJframe.getPartnerJframe().getPartnerMainJpanel().getPartnerCardJpanel()
//                        .getPartnerEquipJpanel().palExp(pal.getLvl());
//                if (palExp > pal.getExp()) {
//                    ZhuFrame.getZhuJpanel().addPrompt2("经验没有升满");
//                    return;
//                }
//                if (breakLevel < 0) {
//                    ZhuFrame.getZhuJpanel().addPrompt2("等级没有抵达突破要求");
//                    return;
//                }
//                if (sum < breakLevel) {
//                    ZhuFrame.getZhuJpanel().addPrompt2("等级突破丹数量不足");
//                    return;
//                }
//                String sendmes = Agreement.getAgreement().userpalAgreement(pal.getId() + "|" + list.get(0).getRgid());
//                SendMessageUntil.toServer(sendmes);
            }
        }
    }
}
