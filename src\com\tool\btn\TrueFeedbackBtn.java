package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.JLabel;
import javax.swing.SwingConstants;

import org.come.Frame.TrueFeedbackMainJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TrueFeedbackLotteyJPanel;
import org.come.Jpanel.TrueFeedbackMainJpanel;
import org.come.Jpanel.TrueFeedbackScPanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;

public class TrueFeedbackBtn extends MoBanBtn {

    private int caozuo;
    private TrueFeedbackMainJpanel trueFeedbackMainJpanel;
    private TrueFeedbackScPanel trueFeedbackScPanel;
    private TrueFeedbackLotteyJPanel trueFeedbackLotteyJPanel;
    

    /**0.累计充值1.限时神宠2.抽奖面板*/
    public TrueFeedbackBtn(String iconpath, int type, int caozuo, TrueFeedbackMainJpanel trueFeedbackMainJpanel) {
        super(iconpath, type);
        this.trueFeedbackMainJpanel = trueFeedbackMainJpanel;
        this.caozuo = caozuo;
    }
    /**4.上一页5.下一页*/
    public TrueFeedbackBtn(String iconpath, int type, int caozuo, TrueFeedbackScPanel trueFeedbackScPanel) {
        super(iconpath, type);
        this.trueFeedbackScPanel = trueFeedbackScPanel;
        this.caozuo = caozuo;
    }
    /**3.首页6.末页7.奖励一览8.领取累计充值*/
    public TrueFeedbackBtn(String iconpath, int type, int caozuo, String text, Color[] colors,Font font, TrueFeedbackScPanel trueFeedbackScPanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
        this.trueFeedbackScPanel = trueFeedbackScPanel;
        this.caozuo = caozuo;
    }
    /**9.单次抽奖*/
    public TrueFeedbackBtn(String iconpath, int type, int caozuo, String text, Color[] colors,Font font, TrueFeedbackLotteyJPanel trueFeedbackLotteyJPanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
        this.trueFeedbackLotteyJPanel = trueFeedbackLotteyJPanel;
        this.caozuo = caozuo;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        try {
            if (caozuo == 0) {
                trueFeedbackMainJpanel.getBtnAddRecharge().setIcons(CutButtonImage.cuts("inkImg/button/B318.png"));
                trueFeedbackMainJpanel.getBtntimeSummon().setIcons(CutButtonImage.cuts("inkImg/button/B321.png"));
                trueFeedbackMainJpanel.getBtnLottey().setIcons(CutButtonImage.cuts("inkImg/button/B319.png"));
                trueFeedbackMainJpanel.getTrueFeedbackCardJPanel().changeShowView(caozuo);
                sendMes("0");
            } else if (caozuo == 1) {
                trueFeedbackMainJpanel.getBtnAddRecharge().setIcons(CutButtonImage.cuts("inkImg/button/B317.png"));
                trueFeedbackMainJpanel.getBtntimeSummon().setIcons(CutButtonImage.cuts("inkImg/button/B322.png"));
                trueFeedbackMainJpanel.getBtnLottey().setIcons(CutButtonImage.cuts("inkImg/button/B319.png"));
                trueFeedbackMainJpanel.getTrueFeedbackCardJPanel().changeShowView(caozuo);
                sendMes("1");
            } else if (caozuo == 2) {
                trueFeedbackMainJpanel.getBtnAddRecharge().setIcons(CutButtonImage.cuts("inkImg/button/B317.png"));
                trueFeedbackMainJpanel.getBtntimeSummon().setIcons(CutButtonImage.cuts("inkImg/button/B321.png"));
                trueFeedbackMainJpanel.getBtnLottey().setIcons(CutButtonImage.cuts("inkImg/button/B320.png"));
                trueFeedbackMainJpanel.getTrueFeedbackCardJPanel().changeShowView(caozuo);
                sendMes("2");
            }else if(caozuo == 3){
                if(trueFeedbackScPanel.getPage()<=1){
                    ZhuFrame.getZhuJpanel().addPrompt2("已经是首页了");
                    return;
                }
                trueFeedbackScPanel.setPage(1);
                int typeMenu = TrueFeedbackMainJframe.getTrueFeedbackMainJframe().getTrueFeedbackMainJpanel().getTrueFeedbackCardJPanel().getTypeMenu();
                trueFeedbackScPanel.addData(typeMenu);
            }else if(caozuo == 4){
                if(trueFeedbackScPanel.getPage()<=1){
                    ZhuFrame.getZhuJpanel().addPrompt2("已经是首页了");
                    return;
                }
                trueFeedbackScPanel.setPage(trueFeedbackScPanel.getPage() -1);
                int typeMenu = TrueFeedbackMainJframe.getTrueFeedbackMainJframe().getTrueFeedbackMainJpanel().getTrueFeedbackCardJPanel().getTypeMenu();
                trueFeedbackScPanel.addData(typeMenu);
            }else if(caozuo == 5){
                if(trueFeedbackScPanel.getPage()>=trueFeedbackScPanel.getMaxPage()){
                    ZhuFrame.getZhuJpanel().addPrompt2("已经是末页了");
                    return;
                }
                trueFeedbackScPanel.setPage(trueFeedbackScPanel.getPage() +1);
                int typeMenu = TrueFeedbackMainJframe.getTrueFeedbackMainJframe().getTrueFeedbackMainJpanel().getTrueFeedbackCardJPanel().getTypeMenu();
                trueFeedbackScPanel.addData(typeMenu);
            }else if(caozuo == 6){
                if(trueFeedbackScPanel.getPage()>=trueFeedbackScPanel.getMaxPage()){
                    ZhuFrame.getZhuJpanel().addPrompt2("已经是末页了");
                    return;
                }
                trueFeedbackScPanel.setPage(trueFeedbackScPanel.getMaxPage());
                int typeMenu = TrueFeedbackMainJframe.getTrueFeedbackMainJframe().getTrueFeedbackMainJpanel().getTrueFeedbackCardJPanel().getTypeMenu();
                trueFeedbackScPanel.addData(typeMenu);
            }else if(caozuo == 7){
                FormsManagement.HiddenDisplay(110);
            }else if(caozuo == 8){
                sendMes(String.valueOf(10+TrueFeedbackMainJframe.getTrueFeedbackMainJframe().getTrueFeedbackMainJpanel().getTrueFeedbackCardJPanel().getTypeMenu()));
            }else if(caozuo == 9){
                int num = trueFeedbackLotteyJPanel.getNum();
                if(num!=0){
                    ZhuFrame.getZhuJpanel().addPrompt2("抽奖还没有结束");
                    return;
                }
                int rank = trueFeedbackLotteyJPanel.getRank();
                JLabel labRank = trueFeedbackLotteyJPanel.getLabRank();
                if(rank>0){
                    rank--;
                    trueFeedbackLotteyJPanel.setRank(rank);
                    labRank.setText(String.valueOf(rank));
                    sendMes("12");
                }else{
                    ZhuFrame.getZhuJpanel().addPrompt2("抽奖次数不足");
                    return;
                }
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }
    
    public void sendMes(String type){
        String sendmes = Agreement.getAgreement().laborAgreement(type);
        SendMessageUntil.toServer(sendmes);
    }

}
