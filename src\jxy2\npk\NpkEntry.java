package jxy2.npk;

/**
 * NPK文件条目结构
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkEntry {
    private String fileName;           // 文件名
    private long offset;               // 文件偏移
    private long compressedSize;       // 压缩后大小
    private long originalSize;         // 原始大小
    private int compressionType;       // 压缩类型
    private long crc32;                // CRC32校验
    private byte[] data;               // 文件数据

    public NpkEntry() {
    }

    public NpkEntry(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getOffset() {
        return offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public long getCompressedSize() {
        return compressedSize;
    }

    public void setCompressedSize(long compressedSize) {
        this.compressedSize = compressedSize;
    }

    public long getOriginalSize() {
        return originalSize;
    }

    public void setOriginalSize(long originalSize) {
        this.originalSize = originalSize;
    }

    public int getCompressionType() {
        return compressionType;
    }

    public void setCompressionType(int compressionType) {
        this.compressionType = compressionType;
    }

    public long getCrc32() {
        return crc32;
    }

    public void setCrc32(long crc32) {
        this.crc32 = crc32;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    /**
     * 获取压缩率
     */
    public double getCompressionRatio() {
        if (originalSize == 0) return 0.0;
        return (double) compressedSize / originalSize;
    }

    /**
     * 是否已压缩
     */
    public boolean isCompressed() {
        return compressionType > 0 && compressedSize < originalSize;
    }

    /**
     * 格式化文件大小
     */
    public String getFormattedSize() {
        return formatFileSize(originalSize);
    }

    /**
     * 格式化压缩后大小
     */
    public String getFormattedCompressedSize() {
        return formatFileSize(compressedSize);
    }

    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }

    @Override
    public String toString() {
        return String.format(
                "NPK Entry: %s (原始: %s, 压缩: %s, 压缩率: %.1f%%, 类型: %d)",
                fileName, getFormattedSize(), getFormattedCompressedSize(),
                getCompressionRatio() * 100, compressionType
        );
    }
}
