package jxy2;

import com.tool.btn.RoleCaoZuoBtn;
import com.tool.btn.RoleOperationPanelBtn;

import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;

/**
* 回车事件
* <AUTHOR>
* @date 2023/8/21 17:17
*/
public class MyKeyAdapter extends KeyAdapter {
    public int index;
    public MyKeyAdapter(int index) {
        this.index =index;
    }
    @Override
    public void keyPressed(KeyEvent e) {
        if(e.getKeyCode() == KeyEvent.VK_ENTER) {
            switch (index){
                case 0:
                    RoleOperationPanelBtn.unLockPack();
                    break;
                case 1:
                    RoleCaoZuoBtn.sendMessageUntil();
                    break;
            }


        }
    }
}
