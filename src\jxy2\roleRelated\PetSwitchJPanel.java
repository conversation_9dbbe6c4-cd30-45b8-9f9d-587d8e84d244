package jxy2.roleRelated;

import com.tool.btn.PetPanelBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.RoleSummoning;
import org.come.until.AnalysisString;
import org.come.until.MessagrFlagUntil;
import org.come.until.UserMessUntil;
import org.soaring.btn.CharacterBtn;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
/**
 * ClassName:召唤兽双属性切换
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/3/22 21:10
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class PetSwitchJPanel extends JPanel {
    private JLabel[] labnames =  new JLabel[5];
    private JLabel[] labnames_Y =  new JLabel[5];
    private JLabel[] labnames_R =  new JLabel[5];
    private JLabel[] labnames_S =  new JLabel[5];
    private PetPanelBtn[] btnsure = new PetPanelBtn[3];
    private CharacterBtn[] dians = new CharacterBtn[10];
    private CharacterBtn[] diansone = new CharacterBtn[10];
    private CharacterBtn[] dianstow = new CharacterBtn[10];
    private JLabel[] labzx =  new JLabel[3];
    private PetPanelBtn btnSwitch;
    public String xzType;
    private static JLabel labpointnumber,labpointnumberone,labpointnumbertow;
    public PetSwitchJPanel() {
        this.setPreferredSize(new Dimension(500, 360));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,127,500);
        for (int i = 0; i < labnames.length; i++) {
            labnames[i] = new JLabel();
            labnames_Y[i] = new JLabel();
            labnames_R[i] = new JLabel();
            labnames_S[i] = new JLabel();
            add(labnames_S[i]);
            add(labnames_R[i]);
            add(labnames_Y[i]);
            add(labnames[i]);
        }

        for (int i = 0; i < btnsure.length; i++) {
            // 确认加点
            btnsure[i] = new PetPanelBtn(ImgConstants.tz127, 1, "确认加点", 23+i,"",this);
            btnsure[i].setBounds(88+i*160, 259, 68, 17);
            this.add(btnsure[i]);
        }


        for (int i = 0; i < diansone.length; i++) {
            if (i % 2 == 0) {
                diansone[i] = new CharacterBtn(ImgConstants.tz27, 1, 10 + i);
            } else {
                diansone[i] = new CharacterBtn(ImgConstants.tz28, 1, 10 + i);
            }
            diansone[i].setBounds(120 + i % 2 * 14, 138 + i / 2 * 25, 16, 15);
            diansone[i].addMouseListener(new PetPointMouslisten(i,0));
            this.add(diansone[i]);
        }
        for (int i = 0; i < dians.length; i++) {
            if (i % 2 == 0) {
                dians[i] = new CharacterBtn(ImgConstants.tz27, 1, 10 + i);
            } else {
                dians[i] = new CharacterBtn(ImgConstants.tz28, 1, 10 + i);
            }
            dians[i].setBounds(278 + i % 2 * 14, 138 + i / 2 * 25, 16, 15);
            dians[i].addMouseListener(new PetPointMouslisten(i,1));
            this.add(dians[i]);
        }

        for (int i = 0; i < dianstow.length; i++) {
            if (i % 2 == 0) {
                dianstow[i] = new CharacterBtn(ImgConstants.tz27, 1, 10 + i);
            } else {
                dianstow[i] = new CharacterBtn(ImgConstants.tz28, 1, 10 + i);
            }
            dianstow[i].setBounds(440 + i % 2 * 14, 138 + i / 2 * 25, 16, 15);
            dianstow[i].addMouseListener(new PetPointMouslisten(i,2));
            this.add(dianstow[i]);
        }
        labpointnumber = TeststateJpanel.GJpanelText(Color.WHITE,UIUtils.FZCY_HY14);
        this.add(labpointnumber);
        labpointnumberone = TeststateJpanel.GJpanelText(Color.WHITE,UIUtils.FZCY_HY14);
        this.add(labpointnumberone);
        labpointnumbertow = TeststateJpanel.GJpanelText(Color.WHITE,UIUtils.FZCY_HY14);
        this.add(labpointnumbertow);
        labpointnumber.setBounds(86, 111, 41, 19);
        labpointnumberone.setBounds(246, 111, 41, 19);
        labpointnumbertow.setBounds(405, 111, 41, 19);
        for (int i = 0; i < labzx.length; i++) {
            labzx[i] = new JLabel();
            labzx[i].setBounds(7+i*160,55, 160, 230);
            int index = i;
            labzx[i].addMouseListener(new MouseAdapter() {
                @Override
                public void mousePressed(MouseEvent e) {
                    super.mousePressed(e);
                    switch (index){
                        case 0:
                            xzType = "D_Y";
                            break;
                        case 1:
                            xzType = "D_R";
                            break;
                        case 2:
                            xzType = "D_S";
                            break;
                    }
                    labzx[index].setBorder(BorderFactory.createLineBorder(Color.CYAN,2));
                    for (int j = 0; j < labzx.length; j++) {
                        if (index!=j){
                            labzx[j].setBorder(BorderFactory.createEmptyBorder());
                        }
                    }

                }
                @Override
                public void mouseEntered(MouseEvent e) {
                    if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
                        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
                    }
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
                        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
                    }
                }
            });
            add(labzx[i]);
        }
        btnSwitch = new PetPanelBtn(ImgConstants.tz85, 1,  26, "切换属性", this,"");
        btnSwitch.setBounds(220, 300, 82, 26);
        this.add(btnSwitch);

        insi();
    }

    public void  insi(){
        //TODO 模拟剩余点数
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        int gg = pet.getBone() - (pet.getBone()-AnalysisString.petLvlint(pet.getGrade()));
        int lx = pet.getSpir() - (pet.getSpir()-AnalysisString.petLvlint(pet.getGrade()));
        int ll = pet.getPower() - (pet.getPower()-AnalysisString.petLvlint(pet.getGrade()));
        int mj = pet.getSpeed() - (pet.getSpeed()-AnalysisString.petLvlint(pet.getGrade()));
        int dl = pet.getCalm() - (pet.getCalm()- AnalysisString.petLvlint(pet.getGrade()));
        setTurnBtnVisible(pet.getTurnRount()>=4);
        labnames_Y[0].setText(String.valueOf(gg));
        labnames_Y[1].setText(String.valueOf(lx));
        labnames_Y[2].setText(String.valueOf(ll));
        labnames_Y[3].setText(String.valueOf(mj));
        //
        labnames_S[0].setText(String.valueOf(gg));
        labnames_S[1].setText(String.valueOf(lx));
        labnames_S[2].setText(String.valueOf(ll));
        labnames_S[3].setText(String.valueOf(mj));
        //
        labnames_R[0].setText(String.valueOf(gg));
        labnames_R[1].setText(String.valueOf(lx));
        labnames_R[2].setText(String.valueOf(ll));
        labnames_R[3].setText(String.valueOf(mj));

        int canpoint = pet.getCanpoint();
        //修改不受已加点数的影响
        labpointnumber.setText(canpoint+"");
        labpointnumberone.setText((canpoint)+"");
        labpointnumbertow.setText((canpoint)+"");
        potins[0] = "第一套";
        potins[1] = "第二套";
        potins[2] = "第三套";

    }

    /**设置转生按钮可见度*/
    public void setTurnBtnVisible(boolean b) {
        labnames_Y[4].setVisible(b);
        labnames_S[4].setVisible(b);
        labnames_R[4].setVisible(b);
        diansone[8].setVisible(b);
        diansone[9].setVisible(b);
        dians[8].setVisible(b);
        dians[9].setVisible(b);
        dianstow[8].setVisible(b);
        dianstow[9].setVisible(b);
    }

    /**
     * 0-4 获取根骨灵性力量敏捷可分配点
     */
    public int getdian(int type,int index) {
        try {
            switch (index){
                case 0:
                    if (type == 0) {
                        type = Integer.parseInt(labnames_Y[0].getText());
                    } else if (type == 1) {
                        type = Integer.parseInt(labnames_Y[1].getText());
                    } else if (type == 2) {
                        type = Integer.parseInt(labnames_Y[2].getText());
                    } else if (type == 3) {
                        type = Integer.parseInt(labnames_Y[3].getText());
                    } else if (type == 4) {
                        type = Integer.parseInt(labnames_Y[4].getText());
                    } else if (type == 5) {
                        type = Integer.parseInt(labpointnumber.getText());
                    }
                    break;
                case 1:
                    if (type == 0) {
                        type = Integer.parseInt(labnames_R[0].getText());
                    } else if (type == 1) {
                        type = Integer.parseInt(labnames_R[1].getText());
                    } else if (type == 2) {
                        type = Integer.parseInt(labnames_R[2].getText());
                    } else if (type == 3) {
                        type = Integer.parseInt(labnames_R[3].getText());
                    } else if (type == 4) {
                        type = Integer.parseInt(labnames_R[4].getText());
                    } else if (type == 5) {
                        type = Integer.parseInt(labpointnumberone.getText());
                    }
                    break;
                case 2:
                    if (type == 0) {
                        type = Integer.parseInt(labnames_S[0].getText());
                    } else if (type == 1) {
                        type = Integer.parseInt(labnames_S[1].getText());
                    } else if (type == 2) {
                        type = Integer.parseInt(labnames_S[2].getText());
                    } else if (type == 3) {
                        type = Integer.parseInt(labnames_S[3].getText());
                    } else if (type == 4) {
                        type = Integer.parseInt(labnames_S[4].getText());
                    } else if (type == 5) {
                        type = Integer.parseInt(labpointnumbertow.getText());
                    }
                    break;
            }

        } catch (Exception e) {
            // TODO: handle exception
            type = 0;
        }

        return type;
    }
    public void adddian(int type, int point, int index) {
        JLabel[] labnames = null;
        JLabel labpointnumber = null;

        switch (index) {
            case 0:
                labnames = labnames_Y;
                labpointnumber = this.labpointnumber;
                break;
            case 1:
                labnames = labnames_R;
                labpointnumber = labpointnumberone;
                break;
            case 2:
                labnames = labnames_S;
                labpointnumber =labpointnumbertow;
                break;
        }

        if (labnames != null && labpointnumber != null) {
            if (type >= 0 && type < 5) {
                int currentPoint = Integer.parseInt(labnames[type].getText());
                labnames[type].setText(String.valueOf(currentPoint + point));

                int totalPoints = Integer.parseInt(labpointnumber.getText());
                labpointnumber.setText(String.valueOf(totalPoints - point));
            }
        }
    }

    /**初始化角色属性信息*/
    public void initPetInfo(RoleSummoning pet){
        if (pet.getSpoint()==null){
            insi();
            return;
        }
        if (pet.getSpoint()!=null||!pet.getSpoint().isEmpty()){
            String[] str = pet.getSpoint().split("\\|");
            for (String string : str) {
                String[] vs = string.split("&")[0].split("=");
                int gg = Integer.parseInt(vs[0].split("_")[3]);//根骨
                int lx = Integer.parseInt(vs[1]);//灵力
                int ll = Integer.parseInt(vs[2]);//力量
                int mj = Integer.parseInt(vs[3]);//敏捷
                int dl = Integer.parseInt(vs[4]);//定力
                int canpoint = pet.getCanpoint();
                canpoint -= gg;
                canpoint -= lx;
                canpoint -= ll;
                canpoint -= mj;
                canpoint -= dl;

                canpoint += pet.getZBone();
                canpoint += pet.getZSpir();
                canpoint += pet.getZPower();
                canpoint += pet.getZSpeed();
                canpoint += pet.getZCalm();
                boolean b = pet.getTurnRount()>=4;
                if (string.startsWith("D_Y")) {
                    labnames_Y[0].setText(String.valueOf(gg));
                    labnames_Y[1].setText(String.valueOf(lx));
                    labnames_Y[2].setText(String.valueOf(ll));
                    labnames_Y[3].setText(String.valueOf(mj));
                    if (pet.getTurnRount()>=4){
                        labnames_Y[4].setText(String.valueOf(dl));
                    }

                    labpointnumber.setText(canpoint+"");
                    setTurnBtnVisible(b);
                }else if (string.startsWith("D_R")) {
                    labnames_R[0].setText(String.valueOf(gg));
                    labnames_R[1].setText(String.valueOf(lx));
                    labnames_R[2].setText(String.valueOf(ll));
                    labnames_R[3].setText(String.valueOf(mj));
                    if (pet.getTurnRount()>=4) {
                        labnames_R[4].setText(String.valueOf(dl));
                    }
                    labnames_R[4].setVisible(pet.getTurnRount()>=4);
                    labpointnumberone.setText(canpoint+"");
                    setTurnBtnVisible(b);
                }else if (string.startsWith("D_S")) {
                    labnames_S[0].setText(String.valueOf(gg));
                    labnames_S[1].setText(String.valueOf(lx));
                    labnames_S[2].setText(String.valueOf(ll));
                    labnames_S[3].setText(String.valueOf(mj));
                    if (pet.getTurnRount()>=4) {
                        labnames_S[4].setText(String.valueOf(dl));
                    }
                    labnames_S[4].setVisible(pet.getTurnRount()>=4);
                    labpointnumbertow.setText(canpoint+"");
                    setTurnBtnVisible(b);
                }
            }

            String[] sum = pet.getSpoint().split("\\|");
            for (String string : sum) {
                if (string.split("&").length!=1) {
                    if (string.startsWith("D_Y")) {
                        int vs = Integer.parseInt(string.split("&")[1]);
                        potins[0] = vs == 1 ? "当前启用" : "第一套";
                    } else if (string.startsWith("D_R")) {
                        int vs = Integer.parseInt(string.split("&")[1]);
                        potins[1] = vs == 1 ? "当前启用" : "第二套";
                    } else if (string.startsWith("D_S")) {
                        int vs = Integer.parseInt(string.split("&")[1]);
                        potins[2] = vs == 1 ? "当前启用" : "第三套";
                    }
                }
            }
        }
    }

    public String ties = "召唤兽切换属性";
    public String[] names = {"可分配点","根骨","灵性","力量","敏捷","定力"};
    public String[] potins = {"第一套","第二套","第三套"};

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, getWidth(), 32, 1);
        Juitil.ImngBack(g, Juitil.JK_1, 0, 32, getWidth(), getHeight()-32, 1);
        Juitil.Subtitledrawing(g, getWidth()/2 -76, 24, ties, Color.WHITE, UIUtils.HYXKJ_HY20,1);
        Juitil.TextBackground(g, "切换任意属性每次收取10W游戏币！", 14, 18, 36, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        //遍历3套属性所需图像
        for (int i = 0; i < 3; i++) {
            Juitil.ImngBack(g, Juitil.tz21, 7+i*160, 55, 160, 230, 1);
            for (int jj = 0; jj < names.length; jj++) {
                if (jj == 5 && UserMessUntil.getChosePetMes().getTurnRount() < 4) {
                    continue; // 跳过本次循环迭代
                }
                Juitil.CenterTextdrawing(g,names[jj], jj==0?85+i*325:60+i*325 ,241+jj*50,UIUtils.COLOR_Fighting,UIUtils.FZCY_HY14);
                Juitil.ImngBack(g, Juitil.tz26,jj==0?80+i*160:60+i*160, 110+jj*25, jj==0?70:90, 21, 1);
            }
            Juitil.ImngBack(g, Juitil.tz26,18+i*160, 85, 132, 23, 1);
            Juitil.TextBackground(g, potins[i], 13, 18+i*160, 63, UIUtils.COLOR_NAME, UIUtils.FZCY_HY16);

        }
        for (int k = 0; k < 5; k++) {
            Juitil.CenterTextdrawing(g, labnames_Y[k].getText(), 152, 294+k*50, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
            Juitil.CenterTextdrawing(g, labnames_R[k].getText(), 795-325, 294+k*50, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
            Juitil.CenterTextdrawing(g, labnames_S[k].getText(), 795, 294+k*50, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        }

    }

    public JLabel[] getLabnames_S() {
        return labnames_S;
    }

    public void setLabnames_S(JLabel[] labnames_S) {
        this.labnames_S = labnames_S;
    }

    public JLabel[] getLabnames_R() {
        return labnames_R;
    }

    public void setLabnames_R(JLabel[] labnames_R) {
        this.labnames_R = labnames_R;
    }

    public JLabel[] getLabnames_Y() {
        return labnames_Y;
    }

    public void setLabnames_Y(JLabel[] labnames_Y) {
        this.labnames_Y = labnames_Y;
    }

    public JLabel[] getLabzx() {
        return labzx;
    }

    public void setLabzx(JLabel[] labzx) {
        this.labzx = labzx;
    }
}

