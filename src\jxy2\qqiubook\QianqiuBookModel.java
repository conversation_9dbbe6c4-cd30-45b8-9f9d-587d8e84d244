package jxy2.qqiubook;

import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.Arrays;

public class QianqiuBookModel extends JPanel {
    private int h;
    private QiqiuTreeNode node;
    private JLabel labName,labIcon,labText,labTime,labValue,labMeritvalue,labRewardIcon,labCompleteIcon;
    private JTextArea labConclusion;
    public QianqiuBookModel(int width,int height) {
        setPreferredSize(new Dimension(width, height));
        setOpaque(false);
        setLayout(null);
        //96
        h = height;

        getLabName();
        getLabIcon();
        getLabText();
        getLabTime();
        getLabValue();
        getLabMeritvalue();
        getLabRewardIcon();
        getLabCompleteIcon();
        getLabConclusion();

    }

    /**刷新jlabel组件*/
    public void refreshData(){
        labConclusion.setVisible(h>=96);
        labTime.setVisible(h==96);
        labTime.setVisible(node.getComplete()==1&&h==96);
        labTime.setBounds(256,76,390,15);
    }


    public void initData(QiqiuTreeNode node){
        this.node = node;
        if (node.getId()==100){
            labName.setText(node.getName());
        }else {
            labValue.setText(!node.getValue().isEmpty()?"-"+node.getValue():"");
            String kk = node.getValue().length()==3?"    级":"   级";
            labName.setText(!node.getValue().isEmpty()?node.getName()+kk:node.getName());
        }

        labText.setText(node.getDescription());
        labMeritvalue.setText(node.getMeritvalue());
        labMeritvalue.setBounds(node.getMeritvalue().length()==2?362:365,6,390,15);
        labIcon.setIcon(getIcon(node.getParentId()));
        // 设置奖励图标可见性
        labRewardIcon.setVisible(!node.getReward().isEmpty());
        // 获取字体度量
        FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(labName.getFont());
        // 计算labName和labValue的文本宽度，并加上一些边距
        int nameWidth = fm.stringWidth(labName.getText());
        int valueWidth = fm.stringWidth(labValue.getText());
        int totalWidth = nameWidth + valueWidth + 10; // 10像素的边距
        // 设置图标位置，使其紧跟在所有文本后面
        labRewardIcon.setBounds(78 + totalWidth, 5, 21, 21);
        //TODO 开发服务端数据采集，奖励领取记录，数据持久化
        labCompleteIcon.setVisible(node.getComplete()==1);
        labCompleteIcon.setBounds(254, 2, 65, 55);
        // 直接设置文本，JTextArea会自动处理换行
        labConclusion.setText(node.getConclusion());
        labTime.setText("完成时间："+node.getTime());
        labTime.setVisible(node.getComplete()==1&&h==96);
        labConclusion.setVisible(h==96);
    }

    public static ImageIcon getIcon(int id) {
        switch (id) {
            case 11:
                return Juitil.tz343;
            case 12:
                return Juitil.tz349;
            case 13:
                return Juitil.tz350;
            default:
                return null;
        }
    }




    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Util.SwitchUI ==1?Juitil.she_0003:Juitil.red_0007,5,0,getWidth()-9,h,1);
        g.drawImage(Juitil.tz342.getImage(),340,5,55,17,null);
        //将100改为更大的值（如150），线条会向右移动
        //将190改为更小的值（如140），线条会变短
        //将170改为更大的值（如200），线条会向下移动
        //将170改为更小的值（如140），线条会向上移动  结束语

        if (getHeight()==96){
            boolean containsId = Arrays.stream(progressBarId).anyMatch(id -> id == node.getId());
            if (containsId) {
                UiBack.UIResource.entry.draw(g,this);
            }

        }

            g.setColor(new Color(98,129,123));
            g.drawLine(51, 29, 390, 29);
    }

    //需要进度条的ID
    public static final int[] progressBarId = {
            164,166,174,195,225,227,232,238,252,253
    };


    public int getH() {
        return h;
    }

    public void setH(int h) {
        this.h = h;
    }

    public JLabel getLabName() {
        if (labName==null){
            labName = TeststateJpanel.GJpanelText(Util.SwitchUI==1?new Color(16,24,24):
                    UIUtils.COLOR_Wing1,
                    Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B);
            labName.setBounds(59,10,390,15);
            add(labName);
        }
        return labName;
    }

    public void setLabName(JLabel labName) {
        this.labName = labName;
    }

    public JLabel getLabIcon() {
        if (labIcon==null){
            labIcon = new JLabel();
            labIcon.setBounds(8,8,44,43);
            add(labIcon);
        }
        return labIcon;
    }

    public void setLabIcon(JLabel labIcon) {
        this.labIcon = labIcon;
    }

    public JLabel getLabText() {
        if (labText==null){
            labText = TeststateJpanel.GJpanelText(Util.SwitchUI==1?new Color(72,104,88):
                    UIUtils.COLOR_White,UIUtils.TEXT_FONT1);
            labText.setBounds(59,35,390,15);
            add(labText);
        }
        return labText;
    }

    public void setLabText(JLabel labText) {
        this.labText = labText;
    }

    public JLabel getLabTime() {
        if (labTime==null){
            labTime = TeststateJpanel.GJpanelText(Util.SwitchUI==1?Color.BLACK:Color.WHITE,UIUtils.TEXT_FONT);
            labTime.setBounds(51,25,390,15);
            add(labTime);
        }
        return labTime;
    }

    public void setLabTime(JLabel labTime) {
        this.labTime = labTime;
    }

    public JLabel getLabValue() {
        if (labValue==null){
            labValue = TeststateJpanel.PJpanelText(Util.SwitchUI==1?new Color(16,24,24):
                    UIUtils.COLOR_Wing1,UIUtils.TEXT_FONT_17);
            labValue.setBounds(132,11,390,15);
            add(labValue);
        }
        return labValue;
    }

    public void setLabValue(JLabel labValue) {
        this.labValue = labValue;
    }

    public JLabel getLabMeritvalue() {
        if (labMeritvalue==null){
            labMeritvalue = TeststateJpanel.GJpanelText(Color.yellow,UIUtils.TEXT_FONT1);
            labMeritvalue.setBounds(124,11,390,15);
            add(labMeritvalue);
        }
        return labMeritvalue;
    }

    public void setLabMeritvalue(JLabel labMeritvalue) {
        this.labMeritvalue = labMeritvalue;
    }

    public JLabel getLabRewardIcon() {
        if (labRewardIcon==null){
            labRewardIcon = new JLabel();
            labRewardIcon.setIcon(Juitil.tz344);
            labRewardIcon.setCursor(new Cursor(Cursor.HAND_CURSOR));
            // 设置图标位置
            labRewardIcon.setBounds(51, 25, 21, 21);
            add(labRewardIcon);
        }
        return labRewardIcon;
    }

    public void setLabRewardIcon(JLabel labRewardIcon) {
        this.labRewardIcon = labRewardIcon;
    }

    public QiqiuTreeNode getNode() {
        return node;
    }

    public void setNode(QiqiuTreeNode node) {
        this.node = node;
    }

    public JLabel getLabCompleteIcon() {
        if (labCompleteIcon == null) {
            labCompleteIcon = new JLabel();
            labCompleteIcon.setBounds(51, 25, 65, 55);
            labCompleteIcon.setIcon(Juitil.tz346);
            add(labCompleteIcon);
        }
        return labCompleteIcon;
    }

    public void setLabCompleteIcon(JLabel labCompleteIcon) {
        this.labCompleteIcon = labCompleteIcon;
    }


    public JTextArea getLabConclusion() {
        if (labConclusion == null) {
            // 创建JTextArea并设置基本属性
            labConclusion = new JTextArea();
            labConclusion.setEditable(false);  // 设置为不可编辑
            labConclusion.setLineWrap(true);   // 启用自动换行
            labConclusion.setWrapStyleWord(true); // 在单词边界换行
            labConclusion.setOpaque(false);    // 设置透明背景
            labConclusion.setForeground(Util.SwitchUI==1 ? Color.BLACK : UIUtils.COLOR_Wing1);
            labConclusion.setFont(UIUtils.TEXT_FONT);
            labConclusion.setBounds(10, 60, 390, 65);
            labConclusion.setMargin(new Insets(0, 0, 0, 0)); // 设置边距为0
            add(labConclusion);
        }
        return labConclusion;
    }

    public void setLabConclusion(JTextArea labConclusion) {
        this.labConclusion = labConclusion;
    }
    
    // 添加一个方法来设置文本，保持与之前相同的接口
    public void setConclusionText(String text) {
        getLabConclusion().setText(text);
    }
}
