package com.tool.role;

import java.math.BigDecimal;

import org.come.until.UserMessUntil;

/**
 * 获取经验
 * <AUTHOR>
 */
public class GetExp {
	public static long getRoleExp(int TurnAround,int grade){
		if (grade>199) grade=199;
		long exp=UserMessUntil.getExp().getRolePetExpMap().get(grade).longValue();
		if (TurnAround>=3)exp*=3;
		if (grade>100&&exp<5000000) {
			exp=new BigDecimal("6181894660").longValue();
		}
		return exp;
	}
	public static long getBBExp(int TurnAround,int grade){
		if (grade>199) grade=199;
		long exp=UserMessUntil.getExp().getRolePetExpMap().get(grade).longValue();
		if (TurnAround>=3)exp*=3;
		return exp;
	}
	/**内丹经验*/
	public static long getBBNeiExp(int TurnAround,int grade){
		return (long) (getBBExp(TurnAround, grade)*0.7);
	}
	public static int getMountExp(int grade){
		if (grade>100) {grade-=90;}
		//升到下一级所需经验
		int nextexp = (grade+1)*(grade+1)*15;
		return nextexp;
	}
	/**获取当前等级所需的经验值*/
	public static int getTSExp(int lvl){
		int x=9;
		int dd=0;
		for (int i = 0; i < lvl; i++) {
			if (i%2==0) {dd++;}
			if (i%38==4) {dd++;}
			x+=dd;
		}
		return x;
	}
	/**根据点数获取当前的修为值*/
	public static int getTSX(int P){
		int x=9;
		int dd=0;
		int i=0;
		while (true) {
			if (i%2==0) {dd++;}
			if (i%38==4) {dd++;}
			x+=dd;
			if (P>=x) {P-=x;i++;
			}else {return P;}	
		}
	}
	/**根据点数获取当前的天枢点*/
	public static int getTSP(int P){
		int x=9;
		int dd=0;
		int i=0;
		while (true) {
			if (i%2==0) {dd++;}
			if (i%38==4) {dd++;}
			x+=dd;
			if (P>=x) {P-=x;i++;
			}else {return i;}	
		}
	}
}


