package jxy2.petView;

import com.tool.btn.MoBanBtn;
import jxy2.supet.SipetUtil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.WuLingJPanel;
import org.come.bean.PetOperationDTO;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.Arrays;

public class WuLingBtn extends MoBanBtn {
    public WuLingJPanel wuLingJPanel;
    public int BtnId;
    public WuLingBtn(String iconpath, int type, int BtnId, String labelName, WuLingJPanel wuLingJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.wuLingJPanel = wuLingJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (wuLingJPanel.getPetskillid() == null || wuLingJPanel.getPetskillid().isEmpty()){
            ZhuFrame.getZhuJpanel().addPrompt("请选择技能");
            return;
        }
        boolean containsId = Arrays.stream(SipetUtil.lingfan).anyMatch(id -> id == Integer.parseInt(wuLingJPanel.getPetskillid()));
        if (!containsId){
            ZhuFrame.getZhuJpanel().addPrompt("当前技能无法悟灵");
            return;
        }
        PetOperationDTO dto = new PetOperationDTO();
        dto.setPetId(UserMessUntil.getChosePetMes().getSid());
        dto.setEventType(wuLingJPanel.getPetskillid());
        dto.setItmeId(new BigDecimal(wuLingJPanel.getGoodrid()));
        dto.setOperationType("PET_OPEN_QL");
        dto.setOpenClose("1");
        SendRoleAndRolesummingUntil.sendRoleSumming(dto);
    }
}
