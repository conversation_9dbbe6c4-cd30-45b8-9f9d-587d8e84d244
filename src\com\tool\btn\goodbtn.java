package com.tool.btn;

import jxy2.jutnil.Juitil;
import org.come.Frame.TestpackJframe;
import org.come.Jpanel.*;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class goodbtn extends MoBanBtn {

    private JPanel jpanel;
    // 按钮位置额外
    private int path;
    // 标识按钮所属的界面类型，用于区分不同界面的按钮
    public goodbtn(int type, JPanel jpanel, int path) {
        super(Util.SwitchUI==1 ? Juitil.bt10:Juitil.bt11, path,type);
        this.jpanel = jpanel;
        this.path = path;
    }


    /**
     * 点击设置
     *
     * @return
     */
    public void dianjisz(int leixing) {
        type = 2;
        btnchange(2);
        if (jpanel instanceof TestpackJapnel) {
            TestpackJapnel testpackJapnel = (TestpackJapnel) jpanel;
            BtnUtil.btnBinding(testpackJapnel.getBtnrights(), path);
        } else if (jpanel instanceof ForgeJpanel) {
            ForgeJpanel testpackJapnel = (ForgeJpanel) jpanel;
            BtnUtil.btnBinding(testpackJapnel.getBtnrights(), path);
        } else if (jpanel instanceof GiveJpanel) {
            GiveJpanel testpackJapnel = (GiveJpanel) jpanel;
            BtnUtil.btnBinding(testpackJapnel.getBtnrights(), path);
        } else if (jpanel instanceof TradeJpanel) {
            TradeJpanel testpackJapnel = (TradeJpanel) jpanel;
            BtnUtil.btnBinding(testpackJapnel.getBtnrights(), path);
        } else if (jpanel instanceof RefinersJpanel) {
            RefinersJpanel refinersJpanel = (RefinersJpanel) jpanel;
            BtnUtil.btnBinding(refinersJpanel.getBtnrights(), path);
        } else if (jpanel instanceof ExchangeValueJpanel) {
            ExchangeValueJpanel valueJpanel = (ExchangeValueJpanel) jpanel;
            BtnUtil.btnBinding(valueJpanel.getBtnrights(), path);
        } else if (jpanel instanceof CollectionJadeJpanel) {
            CollectionJadeJpanel collectionJadeJpanel = (CollectionJadeJpanel) jpanel;
            BtnUtil.btnBinding(collectionJadeJpanel.getBtnrights(), path);
        } else if (jpanel instanceof PetEquipmentJpanel) {
            PetEquipmentJpanel equipmentJpanel = (PetEquipmentJpanel) jpanel;
            BtnUtil.btnBinding(equipmentJpanel.getBtnrights(), path);
        } else if (jpanel instanceof RefiningEquiJpanel) {
            RefiningEquiJpanel equiJpanel = (RefiningEquiJpanel) jpanel;
            BtnUtil.btnBinding(equiJpanel.getBtnrights(), path);
        } else if (jpanel instanceof TransJpanel) {
            TransJpanel transJpanel = (TransJpanel) jpanel;
            BtnUtil.btnBinding(transJpanel.getBtnrights(), path);
        } else if (jpanel instanceof RuneOperateJpanel) {
            RuneOperateJpanel runeOperateJpanel = (RuneOperateJpanel) jpanel;
            BtnUtil.btnBinding(runeOperateJpanel.getBtnrights(), path);
        }
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub
        // 先执行按钮点击逻辑
            // 如果replace不等于-1，说明有物品需要替换，不跳转下一页
        if (GoodsMouslisten.replace != -1) {
            // 如果当前页已经是最后一页，直接返回
            if (GoodsListFromServerUntil.is == GoodsListFromServerUntil.Pagenumber + 1) {
                return;
            }
            // 执行物品替换逻辑
            GoodsMouslisten.goodreplace(GoodsMouslisten.replace, GoodsListFromServerUntil.vacancy(path));
            TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights()[path].btnchange(0);
        } else {
            // replace等于-1时，正常跳转下一页
            GoodsListFromServerUntil.PageNumberChange(path);
            TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnrights()[path].dianjisz(2);
        }

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
    }

}
