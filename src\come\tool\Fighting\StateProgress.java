package come.tool.Fighting;

import java.util.List;

import org.come.bean.PathPoint;


/**
 * 状态进度
 * <AUTHOR>
 *
 */
public class StateProgress {
     //执行人位置
	private int man;
    //接受事件后hp改变量
	private int hp_Change;
    //接受事件后mp改变量
	private int mp_Change;
    //接受事件后怨气改变量
	private int yq_Change;
	//接受事件后怒气改变量
	private int nq_Change;
	//接受事件的特殊显示
	private String data;
	//接受事件的类型
	private String data2;
	//状态
	private int type;
	//执行后是否有增加持续状态
	private String addchixu; 
	//执行后是否有状态删除
	private String deletechixu;
	//执行完后是否删除单位  1逃跑
	private int Escape;
	//方向
	private int dir;
	//移动结束的方向
	private int dirend;
	//移动的路径
	private List<PathPoint> path;
	//需要的音效音乐
	private String Music; 
	//0表示还没执行 1表示正在执行 2表示执行完毕
	private int zxzt;
	//法术
	private SkillSpell spell;	
	//执行时喊话
	private String text;
	//执行时喊话
	private String buff;
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getDir() {
		return dir;
	}
	public void setDir(int dir) {
		this.dir = dir;
	}

	public int getMan() {
		return man;
	}
	public void setMan(int man) {
		this.man = man;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public List<PathPoint> getPath() {
		return path;
	}
	public void setPath(List<PathPoint> path) {
		this.path = path;
	}
	public int getHp_Change() {
		return hp_Change;
	}
	public void setHp_Change(int hp_Change) {
		this.hp_Change = hp_Change;
	}
	public int getMp_Change() {
		return mp_Change;
	}
	public void setMp_Change(int mp_Change) {
		this.mp_Change = mp_Change;
	}
	public int getDirend() {
		return dirend;
	}
	public void setDirend(int dirend) {
		this.dirend = dirend;
	}
	public String getAddchixu() {
		return addchixu;
	}
	public void setAddchixu(String addchixu) {
		this.addchixu = addchixu;
	}
	public String getDeletechixu() {
		return deletechixu;
	}
	public void setDeletechixu(String deletechixu) {
		this.deletechixu = deletechixu;
	}
	public int getEscape() {
		return Escape;
	}
	public void setEscape(int escape) {
		Escape = escape;
	}
	public String getMusic() {
		return Music==null?"":Music;
	}
	public void setMusic(String music) {
		Music = music;
	}
	public String getData() {
		return data;
	}
	public void setData(String data) {
		this.data = data;
	}
	public int getZxzt() {
		return zxzt;
	}
	public void setZxzt(int zxzt) {
		this.zxzt = zxzt;
	}
	public SkillSpell getSpell() {
		return spell;
	}
	public void setSpell(SkillSpell spell) {
		this.spell = spell;
	}
	public String getData2() {
		if (data2==null)data2="";
		return data2;
	}
	public void setData2(String data2) {
		this.data2 = data2;
	}
	public int getYq_Change() {
		return yq_Change;
	}
	public void setYq_Change(int yq_Change) {
		this.yq_Change = yq_Change;
	}
	public int getNq_Change() {
		return nq_Change;
	}
	public void setNq_Change(int nq_Change) {
		this.nq_Change = nq_Change;
	}
	public String getBuff() {
		return buff;
	}
	public void setBuff(String buff) {
		this.buff = buff;
	}
	
}
