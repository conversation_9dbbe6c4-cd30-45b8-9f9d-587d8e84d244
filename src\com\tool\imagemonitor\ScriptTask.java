package com.tool.imagemonitor;


public class ScriptTask {
	private int taskSetID;//脚本 任务
	private int sum;//剩余的完成次数
	private int npcID;//接取任务的npcID
	private int map;//npc地图id
	private int x;//NPC X
	private int y;//NPC Y
	public ScriptTask(String[] vs,int taskSetID) {
		// TODO Auto-generated constructor stub
		this.taskSetID=taskSetID;
		this.map=Integer.parseInt(vs[0]);
		this.x=Integer.parseInt(vs[1]);
		this.y=Integer.parseInt(vs[2]);
		this.npcID=Integer.parseInt(vs[3]);
		this.sum=Integer.parseInt(vs[4]);
	}
	public int getTaskSetID() {
		return taskSetID;
	}
	public void setTaskSetID(int taskSetID) {
		this.taskSetID = taskSetID;
	}
	public int upSum() {
		return sum--;
	}
	public int getNpcID() {
		return npcID;
	}
	public void setNpcID(int npcID) {
		this.npcID = npcID;
	}
	public int getMap() {
		return map;
	}
	public void setMap(int map) {
		this.map = map;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	
}
