package jxy2.supet;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class SipetSortFrame extends JInternalFrame  implements MouseListener {
    private SipetSortJPanel sipetSortJPanel;
    private int first_x;
    private int first_y;
    public static SipetSortFrame getSipetSortFrame() {
        return (SipetSortFrame) FormsManagement.getInternalForm(146).getFrame();
    }
    public SipetSortFrame() {
        sipetSortJPanel = new SipetSortJPanel();
        this.getContentPane().add(sipetSortJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        ((BasicInternalFrameUI) this.getUI()).setNorthPane(null);// 去除顶部的边框
        this.setBounds(375, 140, 268, 437);
        this.setBackground(UIUtils.Color_BACK);
        this.pack();
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setVisible(false);
        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {
            public void mouseMoved(MouseEvent e) {
                if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
                    MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
                }
            }

            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(), getWidth(), getHeight());
                }

            }
        });
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        Music.addyinxiao("关闭窗口.mp3");
         boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {
            FormsManagement.HideForm(146);
        } else {
            FormsManagement.Switchinglevel(146);
        }

        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public SipetSortJPanel getSipetSortJPanel() {
        return sipetSortJPanel;
    }

    public void setSipetSortJPanel(SipetSortJPanel sipetSortJPanel) {
        this.sipetSortJPanel = sipetSortJPanel;
    }

    public int getFirst_x() {
        return first_x;
    }

    public void setFirst_x(int first_x) {
        this.first_x = first_x;
    }

    public int getFirst_y() {
        return first_y;
    }

    public void setFirst_y(int first_y) {
        this.first_y = first_y;
    }
}
