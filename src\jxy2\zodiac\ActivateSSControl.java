package jxy2.zodiac;

import com.tool.role.RoleData;
import org.come.action.FromServerAction;
import org.come.bean.LoginResult;
import org.come.until.GsonUtil;

//ThirtySixHeavenlyGangsStar
public class ActivateSSControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        LoginResult result = GsonUtil.getGsonUtil().getgson().fromJson(mes,LoginResult.class);
        RoleData.getRoleData().getLoginResult().setOpenxp(result.getOpenxp());
        ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel().RefreshDisplayEffect();
        ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel().arrayxp();
    }
}
