package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.GemRefineryMainJpanel;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.GsonUtil;

public class GemRefineryBtn extends MoBanBtn {

    private int caozuo;
    private GemRefineryMainJpanel gemRefineryMainJpanel;
    private SuitOperBean suitOperBean;

    /** 0合成 1(镶嵌/拆卸) 2开孔 3上一页 4下一页 5锁 6解锁 */
    public GemRefineryBtn(String icon, int type, int caozuo, GemRefineryMainJpanel gemRefineryMainJpanel) {
        super(icon, type);
        this.caozuo = caozuo;
        this.gemRefineryMainJpanel = gemRefineryMainJpanel;
    }

    /** 7合成、镶嵌、开孔 8拆卸 */
    public GemRefineryBtn(String icon, int type, int caozuo, String text, Color[] colors, Font font,
            GemRefineryMainJpanel gemRefineryMainJpanel) {
        super(icon, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.gemRefineryMainJpanel = gemRefineryMainJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        try {
            if (caozuo == 0) {
                gemRefineryMainJpanel.getBtnCompound().setIcons(CutButtonImage.cuts("inkImg/Button/B101.png"));
                gemRefineryMainJpanel.getBtnDisassembly().setIcons(CutButtonImage.cuts("inkImg/Button/B313.png"));
                gemRefineryMainJpanel.getBtnTrepanning().setIcons(CutButtonImage.cuts("inkImg/Button/B315.png"));
                gemRefineryMainJpanel.initView(caozuo);
            } else if (caozuo == 1) {
                gemRefineryMainJpanel.getBtnCompound().setIcons(CutButtonImage.cuts("inkImg/Button/B100.png"));
                gemRefineryMainJpanel.getBtnDisassembly().setIcons(CutButtonImage.cuts("inkImg/Button/B314.png"));
                gemRefineryMainJpanel.getBtnTrepanning().setIcons(CutButtonImage.cuts("inkImg/Button/B315.png"));
                gemRefineryMainJpanel.initView(caozuo);
            } else if (caozuo == 2) {
                gemRefineryMainJpanel.getBtnCompound().setIcons(CutButtonImage.cuts("inkImg/Button/B100.png"));
                gemRefineryMainJpanel.getBtnDisassembly().setIcons(CutButtonImage.cuts("inkImg/Button/B313.png"));
                gemRefineryMainJpanel.getBtnTrepanning().setIcons(CutButtonImage.cuts("inkImg/Button/B316.png"));
                gemRefineryMainJpanel.initView(caozuo);
            } else if (caozuo == 3) {
                if (gemRefineryMainJpanel.getPage() <= 0) {
                    return;
                }
                gemRefineryMainJpanel.setPage(gemRefineryMainJpanel.getPage() - 1);
                if (gemRefineryMainJpanel.getPage() <= 0) {
                    this.setBtn(-1);
                    this.setIcon(CutButtonImage.getImage("inkImg/button/61.png", -1, -1));
                }
                if (gemRefineryMainJpanel.getBtnNextPage().getBtn() == -1) {
                    gemRefineryMainJpanel.getBtnNextPage().setBtn(1);
                    gemRefineryMainJpanel.getBtnNextPage().setIcons(CutButtonImage.cuts("inkImg/button/9.png"));
                }
            } else if (caozuo == 4) {
                if (gemRefineryMainJpanel.getChooseGoods(21) == null) {
                    return;
                }
                gemRefineryMainJpanel.setPage(gemRefineryMainJpanel.getPage() + 1);

                if (gemRefineryMainJpanel.getChooseGoods(21) == null) {
                    this.setIcon(CutButtonImage.getImage("inkImg/button/60.png", -1, -1));
                    this.setBtn(-1);
                }
                if (gemRefineryMainJpanel.getBtnLastPage().getBtn() == -1) {
                    gemRefineryMainJpanel.getBtnLastPage().setBtn(1);
                    gemRefineryMainJpanel.getBtnLastPage().setIcons(CutButtonImage.cuts("inkImg/button/10.png"));
                }
            } else if (caozuo == 7) {
                if (suitOperBean == null) {
                    suitOperBean = new SuitOperBean();
                    if (suitOperBean.getGoods() == null) {
                        suitOperBean.setGoods(new ArrayList<BigDecimal>());
                    }
                }
                if (getText().equals("合成")) {
                    Goodstable chooseGoodstable = gemRefineryMainJpanel.getChooseGoodstable();
                    if (chooseGoodstable == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请选择要合成的宝石");
                        return;
                    }
                    suitOperBean.setType(101);
                    List<BigDecimal> goods = suitOperBean.getGoods();
                    goods.clear();
                    goods.add(chooseGoodstable.getRgid());
                    String text2 = gemRefineryMainJpanel.getTextNum().getText();
                    if (text2 != null && !"".equals(text2)) {
                        suitOperBean.setJade(new PartJade(Integer.parseInt(text2), 0));
                    }
                } else if (getText().equals("镶嵌")) {
                    Goodstable chooseGoodstable = gemRefineryMainJpanel.getChooseGoodstable();
                    if (chooseGoodstable == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请选择要镶嵌的武器");
                        return;
                    }
                    if (gemRefineryMainJpanel.getGemBackNum() <= 0) {
                        ZhuFrame.getZhuJpanel().addPrompt2("选择的武器并没有开孔");
                        return;
                    }
                    if (gemRefineryMainJpanel.getGemBackNum() <= gemRefineryMainJpanel.getGemNum()) {
                        ZhuFrame.getZhuJpanel().addPrompt2("选择的武器并没有剩余的孔可以镶嵌宝石了");
                        return;
                    }
                    Goodstable twoGoodstable = gemRefineryMainJpanel.getTwoGoodstable();
                    if (twoGoodstable == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("宝石没有了");
                        return;
                    }
                    suitOperBean.setType(103);
                    List<BigDecimal> goods = suitOperBean.getGoods();
                    goods.clear();
                    goods.add(chooseGoodstable.getRgid());
                    goods.add(twoGoodstable.getRgid());
                } else if (getText().equals("开孔")) {
                    Goodstable twoGoodstable = gemRefineryMainJpanel.getTwoGoodstable();
                    if (twoGoodstable == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("开孔材料没有了");
                        return;
                    }
                    Goodstable chooseGoodstable = gemRefineryMainJpanel.getChooseGoodstable();
                    if (chooseGoodstable == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请选择要开孔的武器");
                        return;
                    }
                    suitOperBean.setType(104);
                    List<BigDecimal> goods = suitOperBean.getGoods();
                    goods.clear();
                    goods.add(chooseGoodstable.getRgid());
                    goods.add(twoGoodstable.getRgid());
                }

                String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
                SendMessageUntil.toServer(sendmes);
            } else if (caozuo == 8) {
                if (suitOperBean == null) {
                    suitOperBean = new SuitOperBean();
                    if (suitOperBean.getGoods() == null) {
                        suitOperBean.setGoods(new ArrayList<BigDecimal>());
                    }
                }
                Goodstable chooseGoodstable = gemRefineryMainJpanel.getChooseGoodstable();
                if (chooseGoodstable == null) {
                    ZhuFrame.getZhuJpanel().addPrompt2("请选择要拆卸的武器");
                    return;
                }
                suitOperBean.setType(102);
                List<BigDecimal> goods = suitOperBean.getGoods();
                goods.clear();
                goods.add(chooseGoodstable.getRgid());
                String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
                SendMessageUntil.toServer(sendmes);
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }
}
