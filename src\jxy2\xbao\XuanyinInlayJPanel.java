package jxy2.xbao;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

public class XuanyinInlayJPanel extends JPanel {
    private JLabel[] labels= new JLabel[30];
    public XuanyinInlayJPanel() {
        this.setPreferredSize(new Dimension(396,435));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);

        for (int i = 0; i < 30; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            labels[i] = new JLabel();
            labels[i].setBounds(60+shop_x*51, 49+shop_y*51, 51, 51);
            this.add(labels[i]);
        }

    }
    /**
 * 传输颜色信息方法
 * 该方法用于处理颜色的传输，可能涉及到颜色的解析、转换或发送操作
 * 此处的参数text预期包含与颜色相关的信息，但具体实现细节未提供
 *
 * @param text 包含颜色信息的字符串，具体格式和内容需根据上下文或文档确定
 */
public void TransmittingColors(String text) {
    for (int i = 0; i < 30; i++) {
        labels[i].setIcon(null);
    }
    // 方法的具体实现内容未提供，可能在此处进行颜色的解析、转换或发送等操作
    int labelIndex = 0; // 独立的labels索引计数器
    for (int i = 0; i < 24; i++) {
        if (GoodsListFromServerUntil.getGoodslist()[i]!=null) {
            Goodstable goodstable = GoodsListFromServerUntil.getGoodslist()[i];
            boolean containsAll = text.chars()
                    .allMatch(c -> goodstable.getGoodsname().contains(String.valueOf((char)c)));
            if (goodstable.getType()==690&&containsAll){
                // 使用独立的labelIndex而不是i来设置labels
                if (labelIndex < labels.length) {
                    labels[labelIndex].setIcon(GoodsListFromServerUntil.xbaoWdfile(goodstable.getSkin(),51,51));
                    labelIndex++; // 递增labels索引
                }
            }

        }

    }

}

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "玄印镶嵌");
        } else {
            Juitil.SheNewShow(g, 396, getHeight(), "玄印镶嵌", 300  , 55);
        }
        for (int i = 0; i < 30; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,60+shop_x*51,49+shop_y*51,51,51,1);
        }
        Juitil.ImngBack(g,Juitil.tz267,59,47,310,259,1);
    }


}
