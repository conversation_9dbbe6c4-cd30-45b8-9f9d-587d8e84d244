package jxy2.wdfDome.util;

import jxy2.wdfDome.bean.WasData;
import jxy2.wdfDome.bean.WdfHead;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;

public class WdfTool {
    public static WdfHead getWdfHead(File file) {
        try {
            RandomAccessFile in = new RandomAccessFile(file, "r");
            WdfHead wdfHead = new WdfHead();
            byte[] buf = new byte[12];
            in.read(buf);
            // 读取标志位
            long flag = DataTool.byte2uint32(buf, 0);
            wdfHead.setFlag(flag);
            // 读取文件总和
            wdfHead.setFileSum(DataTool.byte2uint32(buf, 4));
            // 读取偏移量
            wdfHead.setOffset(DataTool.byte2uint32(buf, 8));
            // 读取文件列表
            ArrayList<WasData> list = new ArrayList<>();
            byte[] filelist = new byte[16 * (int) wdfHead.getFileSum()];
            in.seek(wdfHead.getOffset());
            in.read(filelist);
            buf = new byte[16];

            for (long i = 0L; i < wdfHead.getFileSum(); ++i) {
                System.arraycopy(filelist, (int) (i * 16L), buf, 0, 16);
                // 获取文件偏移量
                long fileoffset = DataTool.byte2uint32(buf, 4);
                // 处理文件
                in.seek(fileoffset);
                byte[] buf2 = new byte[2];
                in.read(buf2);
                WasData wasFile = new WasData();
                wasFile.setId(DataTool.byte2uint32(buf, 0));
                wasFile.setFileOffset(fileoffset);
                wasFile.setFileSize(DataTool.byte2uint32(buf, 8));
                wasFile.setFileSpace(DataTool.byte2uint32(buf, 12));
                list.add(wasFile);
            }
            // 将数据存储到 WdfHead 中
            WasData[] array = new WasData[list.size()];
            list.toArray(array);
            wdfHead.setWasDataList(array);
            in.close();
            return wdfHead;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
    public static File[] getWdfFiles(File file) {
        return file.listFiles(new Wdfone());
    }


}
