package com.tool.btn;

import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.login.RegisterView;

public class CodeBtn extends MoBanBtn {

	private RegisterView registerView;

	public CodeBtn(String iconpath, int type, Font font, String text, RegisterView registerView) {
		super(iconpath, type);
		setText(text);
		setFont(font);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.registerView = registerView;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
//		String phone = registerView.getTextPhone().getText();
//		if (phone != null && !"".equals(phone)) {
//			setBtn(-1);
//			registerView.setAgainCode(true);
//			setText("300");
//			String sendmes = Agreement.getAgreement().PhoneNumberAgreement("{\"phone\":"+phone+"}");
//	    /** zrikka 2020 0414 */
//			//SendMessageUntil.toServer(sendmes);
//        SendMessageUntil.loginToServer(sendmes);
//        /***/
//		} else {
//			registerView.getLabMsgTip().setText("手机号不能为空");
//		}
	}
}
