package jxy2.synt;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.ScrenceUntil;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class SynthesisFrame extends JI<PERSON>nalFrame implements MouseListener {
    private SynthesisJPanl synthesisJPanl;
    private int first_x,first_y;//x、y坐标

    public static SynthesisFrame getSynthesisFrame() {
        return (SynthesisFrame) FormsManagement.getInternalForm(116).getFrame();
    }
    public SynthesisFrame() {
        this.synthesisJPanl = new SynthesisJPanl();
        this.getContentPane().add(synthesisJPanl);
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        ((BasicInternalFrameUI)this.getUI()).setNorthPane(null);//去除顶部的边框
        this.setBounds(ScrenceUntil.Screen_x / 2 - 620/2,ScrenceUntil.Screen_y / 2 - 432/2,620,432);
        this.setBackground(UIUtils.Color_BACK);
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {//判断窗口移动的位置
            @Override
            public void mouseMoved(MouseEvent e) {}
            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(),getWidth(),getHeight());
                }
            }
        });
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(116);
        }else {
            FormsManagement.Switchinglevel(116);
        }
        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public SynthesisJPanl getSynthesisJPanl() {
        return synthesisJPanl;
    }

    public void setSynthesisJPanl(SynthesisJPanl synthesisJPanl) {
        this.synthesisJPanl = synthesisJPanl;
    }
}
