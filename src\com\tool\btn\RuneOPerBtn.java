package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import com.tool.tcpimg.UIUtils;

public class RuneOPerBtn extends MoBanBtn {

    private int caozuo;

    public RuneOPerBtn(String iconpath, int type, int caozuo) {
        super(iconpath, type);
        this.caozuo = caozuo;
        if (caozuo == 1) {
            setText("重 铸");
        } else if (caozuo == 2) {
            setText("升 级");
        }
        setFont(UIUtils.nameFont);
        setForeground(Color.orange);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public RuneOPerBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 1) {// 重铸

        } else if (caozuo == 2) {// 升级

        } else if (caozuo == 3) {// 重铸规则

        } else if (caozuo == 4) {// 升级规则

        }
    }

}
