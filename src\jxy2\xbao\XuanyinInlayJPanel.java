package jxy2.xbao;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class XuanyinInlayJPanel extends JPanel {
    private JLabel[] labels= new JLabel[30];
    private List<Goodstable> list = new ArrayList<>();//在玄印盒子单独绘制
    private List<Goodstable> listback = new ArrayList<>();//用于背包物品调用
    private Map<Integer,Integer> sumMap = new ConcurrentHashMap<>();
    public XuanyinInlayJPanel() {
        this.setPreferredSize(new Dimension(396,435));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);

        for (int i = 0; i < 30; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            labels[i] = new JLabel();
            labels[i].setBounds(60+shop_x*51, 49+shop_y*51, 51, 51);
            labels[i].addMouseListener(new XuanyinInlayMouse(i,this));
            this.add(labels[i]);
        }

    }
    /**
 * 传输颜色信息方法
 * 该方法用于处理颜色的传输，可能涉及到颜色的解析、转换或发送操作
 * 此处的参数text预期包含与颜色相关的信息，但具体实现细节未提供
 *
 * @param text 包含颜色信息的字符串，具体格式和内容需根据上下文或文档确定
 */
public void TransmittingColors(String text) {
    // 清空所有显示
    for (int i = 0; i < 30; i++) {
        labels[i].setIcon(null);
    }
    list.clear();
    listback.clear();
    sumMap.clear();

    // 从包裹中查找符合条件的物品
    for (int i = 0; i < 24; i++) {
        if (GoodsListFromServerUntil.getGoodslist()[i] != null) {
            Goodstable goodstable = GoodsListFromServerUntil.getGoodslist()[i];
            // 检查goodsname中是否包含text中的任意一个字符
            boolean containsAny = text.chars()
                    .anyMatch(c -> goodstable.getGoodsname().contains(String.valueOf((char)c)));

            if (goodstable.getType() == 690 && containsAny) {
                // 按顺序添加到list中（用于界面显示）
                list.add(goodstable);
                // 记录原始包裹位置（用于操作时找回原位置）
                listback.add(goodstable);
                // 记录界面位置与包裹位置的映射关系
                sumMap.put(list.size() - 1, i);

                System.out.println("找到物品: " + goodstable.getGoodsname() + " 包裹位置:" + i + " 界面位置:" + (list.size() - 1));
            }
        }
    }

    // 刷新界面显示
    repaint();
}

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "玄印镶嵌");
        } else {
            Juitil.SheNewShow(g, 396, getHeight(), "玄印镶嵌", 300  , 55);
        }
        for (int i = 0; i < 30; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,60+shop_x*51,49+shop_y*51,51,51,1);
        }
        Juitil.ImngBack(g,Juitil.tz267,59,47,310,259,1);

        //绘制物品
        for (int i = 0; i < list.size(); i++) {
            Goodstable goodstable = list.get(i);
            int shop_x = i % 6;
            int shop_y = i / 6;
            if (goodstable!=null){
               int xy =  XuanyinInlayMouse.list.contains(i+"")?1:0;
                g.drawImage(GoodsListFromServerUntil.xbaoWdfile(goodstable.getSkin(), 51, 51).getImage(),
                        xy+60+shop_x*51,xy+49+shop_y*51,
                        50, 50, null);
            }
        }
    }


    public List<Goodstable> getList() {
        return list;
    }

    public void setList(List<Goodstable> list) {
        this.list = list;
    }

    public JLabel[] getLabels() {
        return labels;
    }

    public void setLabels(JLabel[] labels) {
        this.labels = labels;
    }

    public List<Goodstable> getListback() {
        return listback;
    }

    public void setListback(List<Goodstable> listback) {
        this.listback = listback;
    }

    public Map<Integer, Integer> getSumMap() {
        return sumMap;
    }

    public void setSumMap(Map<Integer, Integer> sumMap) {
        this.sumMap = sumMap;
    }
}
