package jxy2.xbao;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

public class XbPracticeJPanel extends JPanel {
    public XbaoBrn sortGoods;
    private JLabel[] labicon = new JLabel[7];
    public XbPracticeJPanel() {
        this.setPreferredSize(new Dimension(346,390));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        for (int i = 0; i < labicon.length; i++) {
            labicon[i] = new JLabel();
            labicon[i].setBounds(15, 49 + i * 26, 134, 19);
            labicon[i].setVisible(false);
            this.add(labicon[i]);
        }
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        sortGoods = new XbaoBrn(iconResource, 1, "转换", 7);
        sortGoods.setBounds(55, 297, 60, 26);
        this.add(sortGoods);
    }
    public void initialization(Xuanbao xuanbao){
        if (xuanbao == null) return;
        labicon[0].setText(xuanbao.getBaoname());
        labicon[1].setText(xuanbao.getBaolvl()+"级");
        labicon[2].setText(xuanbao.getBaoexe()+"/"+ XbaoFrame.getXbaoFrame().getXbaoJpanel().expTable[xuanbao.getBaolvl().intValue()]);
        labicon[3].setText("30000000");
        labicon[4].setText(RoleData.getRoleData().getLoginResult().getExperience()+"");
        labicon[5].setText("1500");
        labicon[6].setText("0/30");

    }


    public String[] text = {
            "玄宝名称",
            "玄宝等级",
            "玄宝当前玄蕴",
            "本次所需经验",
            "当前剩余经验",
            "本次获得玄蕴",
            "本周转换次数"};
    public void paintComponent(Graphics g) {
        switch (Util.SwitchUI) {
            case 1:
                Juitil.SheNewShow(g, getWidth(), getHeight(), "玄蕴转换", 304, 57);
                break;
            case 2:
                Juitil.RedMuNewShow(g, getWidth(), getHeight(), "玄蕴转换");
                break;
        }


        for (int i = 0; i < 7; i++) {
            if (i == 2) {
                // 第3行单独处理
                int y = 49 + i * 26;
                int textY = (Util.SwitchUI == 1 ? 47 : 49) + i * 26;
                // 这里可以设置i==2时的特殊参数
                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0043,
                        150, y, 134,
                        Util.SwitchUI == 1 ? 19 : 23, 1);
                Juitil.TextBackground(g, text[i], 16, 42, y,
                        Util.SwitchUI == 1 ? Color.RED : UIUtils.COLOR_Wing1, // 举例：i==2时左侧字体为红色
                        Util.SwitchUI == 1 ? UIUtils.NEWTX_HY16B : UIUtils.TEXT_HYJ16B,
                        UIUtils.Color_BACK);

                int maxWidth = Util.SwitchUI == 1 ? 130 : 124; // 最大长度
                int x = Util.SwitchUI == 1 ? 152 : 124;
                int progressWidth;
                Xuanbao choseBao = RoleXuanbao.getRoleXuanbao().choseBao;
               int zexe =  XbaoFrame.getXbaoFrame().getXbaoJpanel().expTable[choseBao.getBaolvl().intValue()];
                double progressRatio = (zexe > 0) ?
                        (double) choseBao.getBaoexe().longValue() / zexe : 0;
                progressWidth = (int) (maxWidth * Math.min(progressRatio,1.0)); // 确保不超过最大长度

                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.she_0024 : Juitil.red_0045,
                        x, y+2, progressWidth,
                        Util.SwitchUI == 1 ? 15 : 12, 1);

                Juitil.TextBackground(g, labicon[i].getText(), 16, 188, textY,
                        UIUtils.COLOR_White, UIUtils.TEXT_FONT15, UIUtils.Color_BACK);


            } else if (i <= 2) {
                // 前三行（不含i==2）
                int y = 49 + i * 26;
                int textY = (Util.SwitchUI == 1 ? 47 : 49) + i * 26;

                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0043,
                        150, y, 134,
                        Util.SwitchUI == 1 ? 19 : 23, 1);

                Juitil.TextBackground(g, text[i], 16, 42, y,
                        Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1,
                        Util.SwitchUI == 1 ? UIUtils.NEWTX_HY16B : UIUtils.TEXT_HYJ16B,
                        UIUtils.Color_BACK);

                Juitil.TextBackground(g, labicon[i].getText(), 16, 150, textY,
                        UIUtils.COLOR_White, UIUtils.TEXT_FONT15, UIUtils.Color_BACK);

            } else {
                // 后四行
                int y = 49 + (i + 3) * 26;
                int textY = (Util.SwitchUI == 1 ? 47 : 49) + (i + 3) * 26;
                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0043,
                        150, y, 134,
                        Util.SwitchUI == 1 ? 19 : 23, 1);
                Juitil.TextBackground(g, text[i], 16, 42, y,
                        Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1,
                        Util.SwitchUI == 1 ? UIUtils.NEWTX_HY16B : UIUtils.TEXT_HYJ16B,
                        UIUtils.Color_BACK);
                Juitil.TextBackground(g, labicon[i].getText(), 16, 150, textY,
                        UIUtils.COLOR_White, UIUtils.TEXT_FONT15, UIUtils.Color_BACK);
            }
        }
        g.drawImage(Util.SwitchUI==1?Juitil.tz382.getImage():Juitil.tz383.getImage(), Util.SwitchUI==1?50:23, 156, null);
        sortGoods.setBounds(130  , 340, 60, 26);
    }


    public void updateButtonImages(int uiType) {
        String panelName = this.getClass().getSimpleName();
        Juitil.addClosingButtonToPanel(this, 0, uiType == 1 ? 346 : 320);
        if (uiType!=1){
            Juitil.adjustFrameSize(320, 415, 154);
        }else {
            Juitil.adjustFrameSize(346, 390, 154);
        }
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        sortGoods.setIcons(Juitil.getImgs(iconResource));
    }
}
