package jxy2.refine;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;

public class GemsBtn extends MoBanBtn {
    public int typeBtn;//提示
    public GemsTJpanel gemsTJpanel;
    public GemsBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, GemsTJpanel gemsTJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.gemsTJpanel = gemsTJpanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn==99){
            String v = gemsTJpanel.getWorkshopBtn().getText();
            if (v.equals("合成")){
                SYNTHETIC_GEMSTONE(gemsTJpanel,17);
            }else if (v.equals("重铸")){
                SYNTHETIC_GEMSTONE(gemsTJpanel,18);
            }else {
                SYNTHETIC_GEMSTONE(gemsTJpanel,19);
            }

        }else {
            BtnMoni(typeBtn);
            gemsTJpanel.setMinType(typeBtn);
            gemsTJpanel.clear();
            RefineFrame.getRefineFrame().getRefineJPanel().getRichLabel().setTextSize(Juitil.getPrompt(8,typeBtn),160);
            if (typeBtn==1){gemsTJpanel.getGemstoneOperationRecastTypePanel().repaintGemstoneIcon(gemsTJpanel);
                gemsTJpanel.getRecastGemstone().setIcon(null);
            }
        }

    }

    private void SYNTHETIC_GEMSTONE(GemsTJpanel gemsTJpanel,int typePanel) {
            SuitOperBean suitOperBean = new SuitOperBean();
            suitOperBean.setGoods(new ArrayList<BigDecimal>());
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(new BigDecimal("5000000")) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("金额不足");
                return;
            }
            for (int i = 0; i < gemsTJpanel.goods.length; i++) {
                if (gemsTJpanel.goods[i] != null) {
                    Goodstable goodstableBack = gemsTJpanel.goods[i];
                    if (goodstableBack.getGoodlock() == 1) {
                        ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                        return;
                    }
                    if (GoodsListFromServerUntil.isExist(goodstableBack)) {
                        return;
                    }
                    int sum = 1;
                    for (int j = 0; j < suitOperBean.getGoods().size(); j++) {
                        if (goodstableBack.getRgid().compareTo(suitOperBean.getGoods().get(j)) == 0) {
                            sum++;
                        }
                    }
                    if (sum > goodstableBack.getUsetime()) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请凑齐物品再来");
                        return;
                    }
                    suitOperBean.getGoods().add(goodstableBack.getRgid());
                }
            }
            suitOperBean.setType(typePanel);
        if (typePanel == 18) {
            Goodstable reelectGoods = gemsTJpanel.getReelectGoods();
            suitOperBean.setJade(new PartJade(reelectGoods.getType().intValue(), 0));
        }
            // 返回背包剩余格数
            int packNumber = GoodsListFromServerUntil.Surplussum("-1", "-1", 999);
            if (packNumber <= 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("背包空间不足");
                return;
            }
            String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
            SendMessageUntil.toServer(sendmes);
            gemsTJpanel.clear();

    }

    private void BtnMoni(int typeBtn){
        gemsTJpanel.getBtnGems()[typeBtn].btnchange(2);
        for (int i = 0; i < gemsTJpanel.getBtnGems().length; i++) {
            if (i!=typeBtn){
                gemsTJpanel.getBtnGems()[i].btnchange(0);
            }
        }
    }
}
