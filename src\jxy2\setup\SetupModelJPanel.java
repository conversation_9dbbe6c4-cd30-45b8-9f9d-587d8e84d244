package jxy2.setup;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;

import javax.swing.*;
import java.awt.*;

public class SetupModelJPanel extends JPanel {
    private JLabel labimg,labtext;
    public SetupModelJPanel() {
        setPreferredSize(new Dimension(260, 20));
        setOpaque(false);
        setLayout(null);
        getLabimg();
        getLabtext();

    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.tz83.getImage(),0,0,14,14,null);
    }
    //文本
    public JLabel getLabimg() {
        if (labimg==null){
            labimg = TeststateJpanel.GJpanelText(UIUtils.COLOR_CL_Setting,UIUtils.FZCY_HY14);
            labimg.setBounds(20,0,249,14);
            labimg.setHorizontalAlignment(SwingConstants.CENTER);
            add(labimg);
        }
        return labimg;
    }

    public void setLabimg(JLabel labimg) {
        this.labimg = labimg;
    }
    //图像
    public JLabel getLabtext() {
        if (labtext==null){
            labtext = new JLabel();
            labtext.setBounds(0,0,14,14);
            add(labtext);
        }
        return labtext;
    }

    public void setLabtext(JLabel labtext) {
        this.labtext = labtext;
    }
}
