package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.role.GetExp;
import com.tool.role.RoleData;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.jutnil.Juitil;
import org.come.Frame.MountJframe;
import org.come.Frame.MountSkillsJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.MountJPanel;
import org.come.Jpanel.MountSkillsJpanel;
import org.come.Jpanel.PetModelJPanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.LoginResult;
import org.come.bean.RoleShow;
import org.come.entity.Mount;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChangeMouseSymbolMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class MountPanelBtn extends MoBanBtn {

	private MountJPanel mountJPanel;
	private int typeBtn;

	public MountPanelBtn(String iconpath, int type, int typeBtn, String labelName, MountJPanel mountJPanel, String string) {
		super(iconpath, type,0,string,labelName);
		this.typeBtn = typeBtn;
		this.mountJPanel = mountJPanel;
	}
	public MountPanelBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, MountJPanel mountJPanel, String prompt) {
		// TODO Auto-generated constructor stub
		super(iconpath, type,0,colors,prompt);
		this.setText(text);
		setFont(font);
		this.typeBtn = typeBtn;
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.mountJPanel = mountJPanel;
	}
	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		if (getNtext()!=null) {
			if (this.getNtext().equals("乘 骑") && FightingMixDeal.State == HandleState.USUAL) {
				rideOrReset();
			} else if (this.getNtext().equals("休 息") && FightingMixDeal.State == HandleState.USUAL) {
				rideOrReset();
			} else if (this.getNtext().equals("管制") && FightingMixDeal.State == HandleState.USUAL) {
//				controlPet();
			} else if (this.getNtext().equals("喂 养") && FightingMixDeal.State == HandleState.USUAL) {
				feedMount();
			} else if (this.getNtext().equals("技 能") && FightingMixDeal.State == HandleState.USUAL) {
				mountSkill();
			}
		}else {//守护按钮
			initializeComponents(mountJPanel,typeBtn);
		}
		FormsManagement.HideForm(140);
		mountJPanel.refreshMount(mountJPanel.mounts);
	}

	public static void initializeComponents(MountJPanel mountJPanel,int typeBtn){
		mountJPanel.setType(typeBtn==0?1:2);
		mountJPanel.MountVisible();
		mountJPanel.getMainBtns()[typeBtn].btnchange(2);
		for (int i = 0; i < mountJPanel.getMainBtns().length; i++) {
			if (i!=typeBtn){mountJPanel.getMainBtns()[i].btnchange(0);}
		}

		if (mountJPanel.getGuardType()==4&&typeBtn!=0){
			mountJPanel.ComponentDisplay(4);
		}
		register();
	}



	public static void register(){
		//这只当前每个的等级！
		MountJPanel mountJPanel=MountJframe.getMountjframe().getMountjpanel();
		String[] vs = {"朱雀","白虎","青龙","玄武","中天"};
		for (int i = 0; i < vs.length; i++) {
			LoginResult loginResult = RoleData.getRoleData().getLoginResult();
			int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(vs[i]));
			int tsp = GetExp.getTSP(parseInt);
			mountJPanel.getLvl()[i].setText(tsp+"");
		}
	}

	/**
	 * 骑乘/休息
	 */
	public void rideOrReset() {
		MountJPanel mountJPanel=MountJframe.getMountjframe().getMountjpanel();
		RoleShow roleShow=ImageMixDeal.userimg.getRoleShow();
		if (mountJPanel.getModelJPanelJList().getSelectedValue() != null) {
			int index = mountJPanel.getModelJPanelJList().getSelectedIndex();
			if (mountJPanel.getBtnRiding().getNtext().equals("乘 骑")) {
				if (roleShow.getMount_id() !=0) {
					mountJPanel.getModelJPanelMapMap().get(index).getMountNameLabel().setText(ZhuJpanel.getListMount().get(index).getMountname()+"(*)");
					for (int i = 0; i < ZhuJpanel.getListMount().size(); i++)
						if (ZhuJpanel.getListMount().get(i).getMountid() == roleShow.getMount_id()){
							mountJPanel.getModelJPanelMapMap().get(i).getMountNameLabel().setText(ZhuJpanel.getListMount().get(i).getMountname());
							break;
					}
					roleShow.setMount_id(ZhuJpanel.getListMount().get(index).getMountid());
				} else {
					mountJPanel.getModelJPanelMapMap().get(index).getMountNameLabel().setText(ZhuJpanel.getListMount().get(index).getMountname()+"(*)");
					roleShow.setMount_id(ZhuJpanel.getListMount().get(index).getMountid());
				}
				ExpIncreaseUntil.showMountValue(ZhuJpanel.getListMount().get(index));
				ImageMixDeal.userimg.changeskin(null);
				String mes = Agreement.getAgreement().rolechangeAgreement("M"+roleShow.getMount_id()+"="+ZhuJpanel.getListMount().get(index).getMid());
				SendMessageUntil.toServer(mes);
				MountJframe.getMountjframe().getMountjpanel().getBtnRiding().setNtext("休 息");
			} else if (MountJframe.getMountjframe().getMountjpanel().getBtnRiding().getNtext().equals("休 息")) {
				mountJPanel.getModelJPanelMapMap().get(index).getMountNameLabel().setText(ZhuJpanel.getListMount().get(index).getMountname());
				roleShow.setMount_id(0);
				ImageMixDeal.userimg.changeskin(null);
				String mes = Agreement.getAgreement().rolechangeAgreement("M");
				SendMessageUntil.toServer(mes);
				MountJframe.getMountjframe().getMountjpanel().getBtnRiding().setNtext("乘 骑");
			}
		} else {
			ZhuFrame.getZhuJpanel().addPrompt2("请选择您要骑乘/休息的坐骑！");
		}
	}

	/**
	 * 管制
	 */
	public static void controlPet(int index) {
		MountJPanel mountJPanel=MountJframe.getMountjframe().getMountjpanel();
		PetModelJPanel modelJPanel= mountJPanel.getPetModelJPanelMap().get(index);
		int index1 =mountJPanel.getModelJPanelJList().getSelectedIndex();
		RoleSummoning pet = UserMessUntil.getPetListTable().get(index);
		Mount mount = ZhuJpanel.getListMount().get(index1);
		Mount mount2=ZhuJpanel.getPetMount(pet.getSid());

		if (mount2!=null&&mount!=mount2) {
			ZhuFrame.getZhuJpanel().addPrompt2("该召唤兽已被其他坐骑管制");
			return;
		}
		if (mount.getSid()!=null&&mount.getSid().compareTo(pet.getSid())==0){
			ZhuFrame.getZhuJpanel().addPrompt2("该召唤兽已被其他坐骑管制");
			return;
		}
		if (mount.getOthrersid()!=null&&mount.getOthrersid().compareTo(pet.getSid())==0){
			ZhuFrame.getZhuJpanel().addPrompt2("该召唤兽已被其他坐骑管制");
			return;
		}
		if (mount.getSid3()!=null&&mount.getSid3().compareTo(pet.getSid())==0){
			ZhuFrame.getZhuJpanel().addPrompt2("该召唤兽已被其他坐骑管制");
			return;
		}

		// 判断是取消管制还是 管制
		int type = 0;
		 if (mount.getSid() == null) {
			mount.setSid(pet.getSid());
			mountJPanel.getPetImg()[0].setIcon(CutButtonImage.getWdfPng("0x6B" + pet.getSummoningskin(),-1,-1,"head.wdf"));
			type = 1;
		} else if (mount.getOthrersid() == null) {
			mount.setOthrersid(pet.getSid());
			mountJPanel.getPetImg()[1].setIcon(CutButtonImage.getWdfPng("0x6B" + pet.getSummoningskin(),-1,-1,"head.wdf"));
			type = 2;
		} else if (mount.getMountlvl()>100&&mount.getSid3() == null) {
			mount.setSid3(pet.getSid());
			mountJPanel.getPetImg()[2].setIcon(CutButtonImage.getWdfPng("0x6B" + pet.getSummoningskin(),-1,-1,"head.wdf"));
			type = 3;
		}
		if (type == 0) {
			ZhuFrame.getZhuJpanel().addPrompt2("目前坐骑管制上限!");
			return;
		} else {
			ChangeMouseSymbolMouslisten.addProperties(pet, mount);
			modelJPanel.moutnNameLabel.setText("["+Juitil.number2Chinese(mount.getMountid())+"坐骑]"+mount.getMountname());
			modelJPanel.color = Color.green;
		}

		String sendmes = Agreement.getAgreement().changeMountValue(GsonUtil.getGsonUtil().getgson().toJson(mount));
		SendMessageUntil.toServer(sendmes);
	}

	/**
	 * 喂养
	 */
	public void feedMount() {
		// 将使用物品的状态改为坐骑喂养状态
		ZhuJpanel.setUseGoodsType(2);
		FormsManagement.upgradForm(2);
	}

	/**
	 * 技能
	 */
	public void mountSkill() {
//		// 先判断是否有坐骑
		if (ZhuJpanel.getListMount() != null) {
//			// 判断是否有选中坐骑
			if (MountJframe.getMountjframe().getMountjpanel().getModelJPanelJList().getSelectedIndex() != -1) {
				int index = MountJframe.getMountjframe().getMountjpanel().getModelJPanelJList().getSelectedIndex();
//				//将召唤兽技能熟练度展示出来
				MountSkillsJframe.getMountSkillsJframe().getMountSkillsJpanel().getLabProficiency().setText(ZhuJpanel.getListMount().get(index).getProficiency() + "");
//				// 先判断这只召唤兽是否有技能
				if (!ZhuJpanel.getListMount().get(index).getMountskill().isEmpty()) {
					refreshMountSkills(index);
				} else {
					MountSkillsJframe.getMountSkillsJframe()
							.getMountSkillsJpanel().getModelskilleffect()
							.removeAllElements();
					MountSkillsJframe.getMountSkillsJframe()
							.getMountSkillsJpanel().getModelmountskill()
							.removeAllElements();
					// 提示信息
					ZhuFrame.getZhuJpanel().addPrompt2("该坐骑还没有技能，赶紧去学习吧！");
				}
					FormsManagement.showForm(20);
				} else{
					// 提示信息
					ZhuFrame.getZhuJpanel().addPrompt2("您还没有坐骑，赶快去获取吧！");
				}
			}
	}
	
	/**
	 * 放生
	 */
	public void releaseMount(){
//		//先判断是否有选择坐骑
//		if(MountJframe.getMountjframe().getMountjpanel().getListmount().getSelectedIndex() == -1){
//			ZhuFrame.getZhuJpanel().addPrompt("请选择您要放生的坐骑！");
//			return;
//		}
//		int index = MountJframe.getMountjframe().getMountjpanel().getListmount().getSelectedIndex();
//		Mount mount=ZhuJpanel.getListMount().get(index);
//		//判断这只坐骑是否有骑乘
//		if(ImageMixDeal.userimg.getRoleShow().getMount_id() != 0 && ImageMixDeal.userimg.getRoleShow().getMount_id() == mount.getMountid()){
//			ZhuFrame.getZhuJpanel().addPrompt("您的坐骑还被您骑着呢！");
//			return;
//		}
//		//判断这只坐骑是否有管制召唤兽
//		if(mount.getSid() != null || mount.getOthrersid() != null || mount.getSid3()!=null){
//			ZhuFrame.getZhuJpanel().addPrompt("您的坐骑管制着召唤兽呢！");
//			return;
//		}
//		// 出现放生的界面
//		OptionsJframe.getOptionsJframe().getOptionsJpanel().
//		showBox(TiShiUtil.Release, mount,"#W确定要将坐骑:#G"+mount.getMountname()+"#W放生吗?");
	}

	// 刷新坐骑技能的方法
	public static void refreshMountSkills(int index) {
		MountSkillsJpanel jpanel=MountSkillsJframe.getMountSkillsJframe().getMountSkillsJpanel();
		jpanel.getModelmountskill().removeAllElements();
		jpanel.getListmountskill().removeAll();
		// 将技能展示出来
		for (int i = 0; i < ZhuJpanel.getListMount().get(index).getMountskill().size(); i++) {
			jpanel.getModelmountskill().add(i,ZhuJpanel.getListMount().get(index).getMountskill().get(i).getSkillname());
		}
		//将第一个技能的技能效果展示出来
		jpanel.getListmountskill().setSelectedIndex(0);
		if (Util.calculateAddition(ZhuJpanel.getListMount().get(index), ZhuJpanel.getListMount().get(index).getMountskill().get(0).getSkillname()) != null) {
			String[] mountaut = Util.calculateAddition(ZhuJpanel.getListMount().get(index), ZhuJpanel.getListMount().get(index).getMountskill().get(0).getSkillname()).split("\\|");
			jpanel.getModelskilleffect().removeAllElements();
			// 将技能属性一个个展示出来
			for (int j = 0; j < mountaut.length; j++) {
				jpanel.getModelskilleffect().add(j, Util.changeToPercentage(mountaut[j]));
			}
		}
	}
}
