package jxy2;

import jxy2.chatv.ChatFrame;
import jxy2.flight.FlyFrame;
import jxy2.flight.FlyPracticeFrame;
import jxy2.qqiubook.QianqiuBookFrame;
import jxy2.supet.JupoDanFrame;
import jxy2.supet.SipetFrame;
import jxy2.xbao.XbPracticeFrame;
import org.come.Frame.*;
import org.come.login.GameView;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.test.Main;

public class SwitchUi {
    public static void allui(int uiType){
        Thread.ofVirtual().start(() -> {
            SipetFrame.getSipetFrame().getSipetJPanel().updateButtonImages(uiType);
            FlyFrame.getFlightFrame().getFlightJPanel().updateButtonImages(uiType);
            FlyPracticeFrame.getFlyPracticeFrame().getFlyPracticeJPanel().updateButtonImages(uiType);
            QianqiuBookFrame.getQianqiuBookFrame().getQianqiuBookJPanel().updateButtonImages(uiType);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().updateButtonImages(uiType);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyJpanel().updateButtonImages(uiType);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemySpellJpanel().updateButtonImages(uiType);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyExtractJpanel().updateButtonImages(uiType);
            AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyReturnJpanel().updateButtonImages(uiType);
            PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().updateButtonImages(uiType);
            JupoDanFrame.getJupoDanFrame().getJupoDanJPanel().updateButtonImages(uiType);
            WuLingFrame.getWuLingFrame().getWuLingJPanel().updateButtonImages(uiType);
            ChatFrame.getChatJPanel().updateButtonImages(uiType);
            ZhuFrame.getZhuJpanel().updateButtonImages(uiType);
            GameView.getFrameDialogSync().getChangeJpanel().updateButtonImages(uiType);
            Teststatejframe.getTeststatejframe().getTeststateJpanel().updateButtonImages(uiType);
            XbPracticeFrame.getXbPracticeFrame().getXbPracticeJPanel().updateButtonImages(uiType);
            TestpackJframe.getTestpackJframe().getTestpackJapnel().updateButtonImages(uiType);
            Main.frame.getLoginJpanel().getGameView().updateButtonImages(uiType);
            serverqianqiu();
        });

    }

    public static void serverqianqiu() {
        // 批量发送请求，每批10个
        int batchSize = 10;
        for (int i = 11; i <= 99; i += batchSize) {
            for (int j = 0; j < batchSize && (i + j) <= 99; j++) {
                String meg = Agreement.getAgreement().QianqiuAgreement(String.valueOf(i + j));
                SendMessageUntil.toServer(meg);
            }
            // 每批之间添加延迟
            try {
                Thread.sleep(100); // 100毫秒延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
