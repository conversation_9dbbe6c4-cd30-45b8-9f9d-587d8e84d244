package jxy2.signin;

import org.come.Frame.MsgJframe;
import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.time.LocalDateTime;
import java.util.List;

/**
* 签到鼠标事件
* <AUTHOR>
* @date 2024/8/24 上午6:51
*/

public class DailyCheckInMouse implements MouseListener {
    public int index;
    public DailyCheckInJPanel dailyCheckInJPanel;
    public DailyCheckInMouse(DailyCheckInJPanel dailyCheckInJPanel,int index) {
    this.index = index;
    this.dailyCheckInJPanel = dailyCheckInJPanel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //大于当前日期，则不可点击
        if (index<32){
            checkIn(index);
        }

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        StringBuffer buffer = new StringBuffer();
        //展示奖品名称
        if (index>=32&&index<=36){
            List<Goodstable> goodstables = dailyCheckInJPanel.getGoodstables().get(index-32);
            for (int i = 0; i < goodstables.size(); i++) {
                Goodstable goodstable = goodstables.get(i);
                if (goodstable!=null){
                    buffer.append(goodstable.getGoodsname());
                    buffer.append(" x ");
                    buffer.append(goodstable.getUsetime());
                    buffer.append("#r"); // 可以添加逗号和空格来分隔不同的商品名称
                    MsgJframe.getJframe().getJapnel().QdiGoodmsg(buffer.toString());
                }
            }
        }else if (index>=37&&index<=41){
            List<Goodstable> goodstables = dailyCheckInJPanel.getGoodsontin().get(index-37);
            for (int i = 0; i < goodstables.size(); i++) {
                Goodstable goodstable = goodstables.get(i);
                if (goodstable!=null){
                    buffer.append(goodstable.getGoodsname());
                    buffer.append(" x ");
                    buffer.append(goodstable.getUsetime());
                    buffer.append("#r"); // 可以添加逗号和空格来分隔不同的商品名称
                    MsgJframe.getJframe().getJapnel().LxQdiGoodmsg(buffer.toString());
                }
            }
        }

    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }

    /**
     * 执行签到操作
     * 该方法用于检查用户是否可以进行签到，并根据当前日期和系统状态执行相应的签到逻辑
     *
     * @param index 签到的日期索引，表示用户试图签到的具体日期
     */
    public void checkIn(int index) {
        // 默认允许签到
        boolean canCheckIn = true;
        // 初始化签到失败提示信息为空
        String message = null;

        // 检查试图签到的日期是否超出今日日期，防止未来日期的签到
        if (index > DailyCheckInJPanel.getNowDay()) {
            message = "还没到日子，无法签到";
            canCheckIn = false;
        }

        // 检查是否为新的月份，确保只能在当月进行签到
        if (LocalDateTime.now().getMonthValue() != DailyCheckInaBtn.newMonth) {
            message = "#不是当前月无法签到！";
            canCheckIn = false;
        }
        if (DailyCheckInJPanel.getNowDay()==index){
            message = "#当前日期无法补签！";
            canCheckIn = false;
        }

        // 检查所选日期是否可以补签，对于已过日期且未签到的情况允许补签
        if (!dailyCheckInJPanel.sureqiandaoOrno(dailyCheckInJPanel.days, index) && DailyCheckInJPanel.getNowDay() >= index) {
            dailyCheckInJPanel.chooseqiandao = String.valueOf(index);

        } else {
            // 如果当前选择的日期不可补签，则显示相应提示
            message = "当前选择日期不可补签！！";
            canCheckIn = false;
        }

        // 设置签到面板的操作可用性
        dailyCheckInJPanel.SetTheOperability(0, canCheckIn);
        // 如果有签到失败的提示信息，则将其添加到主框架的提示区域
        if (message != null) {
            ZhuFrame.getZhuJpanel().addPrompt2(message);
        }
    }

}
