package come.tool.FightingEffect;

import com.tool.tcp.NewPart;
import com.tool.tcp.SpriteFactory;
import come.tool.Fighting.*;
import org.come.Frame.ZhuFrame;
import org.come.until.ScrenceUntil;

import java.awt.event.KeyEvent;

/**
 * 
 * <AUTHOR>
 *
 */
public enum  EffectType {
     
	/**0  none*/
	Null(null),
	/** 1 移动  */
	Move(new MoveEffect()),
	/** 2 动画效果  */
	Attack(new AttackEffect()),
	/** 3 召唤召回闪现处理  */
	Pet(new Pet()),
	/** 4逃跑效果  */
	Escape(new EscapeEffect()),
	/** 5捕捉效果  */
	PetCatch(new PetCatch()),
	/** 6技能移动效果  */
	MoveSkill(new MoveSkillEffect()),
	;
    private Effect action;
	
	private EffectType (Effect action){
		this.action = action;
	}
	public Effect getTarget() {
		return action;
	}
	public static Effect getEffectById(int effectId){
		EffectType[] values = EffectType.values();
		EffectType effectType = values[effectId];
		return effectType.getTarget();
	}
	/** 方向调整 
	 *      71  35
		    53  17*/
	public static int getdir(int dir,int camp){
		if (camp==FightingMixDeal.camp||(camp==1&&FightingMixDeal.camp==-1)) {
          if (dir==7)dir=3;
          else if (dir==3)dir=7;
          else if (dir==5)dir=1;
          else if (dir==1)dir=5;}
		return dir;
	}
	/**
	 * 数据显示
	 */
	public static StateProgress getProgress(FightingState State,int path){
		StateProgress progress=new StateProgress();
		progress.setMan(path);
		progress.setData2(State.getStartState());
		progress.setData(State.getProcessState());
		progress.setBuff(State.getBuff());
		if (State.getHp_Change()!=null)progress.setHp_Change(State.getHp_Change().intValue());
		if (State.getMp_Change()!=null)progress.setMp_Change(State.getMp_Change().intValue());
		if (State.getYq_c()!=null)progress.setYq_Change(State.getYq_c().intValue());
		if (State.getNq_c()!=null)progress.setNq_Change(State.getNq_c().intValue());
		progress.setAddchixu(State.getEndState_1());
		progress.setDeletechixu(State.getEndState_2());
		progress.setMusic(State.getSkillsy());
		progress.setText(State.getText());
		Fightingimage image=FightingMixDeal.CurrentData.get(path);
		if (State.getUp()!=null&& !State.getUp().isEmpty()) {
			String[] vs=State.getUp().split("\\|");
            for (String v : vs) {
                if (v.startsWith("HP")) {
                    image.getFightingManData().setHp_Total(Integer.parseInt(v.split("=")[1]));
                } else if (v.startsWith("MP")) {
                    image.getFightingManData().setMp_Total(Integer.parseInt(v.split("=")[1]));
                }
            }
		}
		if (State.getSkin()!=null&& !State.getSkin().isEmpty()) {
			image.getFightingManData().setModel(State.getSkin());
			NewPart part=image.getFightingManData().getPart();
			if (image.getFightingManData().getHp_Current()<=0) {part.setAct(8);}
			else {part.setAct(7);}
			image.setPart(part);
		}
        if (State.getSkillskin()!=null&&!State.getSkillskin().equals("4000")) {
        	SkillSpell skill=new SkillSpell();
        	String skin=State.getSkillskin();
        	int skinID=0;
        	try {
        		if (skin.charAt(0)>=KeyEvent.VK_0&&skin.charAt(0)<=KeyEvent.VK_9) {
        			skinID=Integer.parseInt(skin);
        		}		
			} catch (Exception e) {
				// TODO: handle exception
			}
        	
        	if (skinID==1830||skinID==1867||skinID==1090||(skinID>=3036&&skinID<=3044)) {
				if (State.getCamp()==FightingMixDeal.camp) {
					skill.setX(ScrenceUntil.Screen_x/4*3);
					skill.setDir(1);
				}else {
					skill.setX(ScrenceUntil.Screen_x / 4);
				}
				skill.setY(ScrenceUntil.Screen_y/2);	
				skill.setSkinpath(1);
			}else if (skinID==1075||skinID==1070||skinID==1035||skinID==1085||
					  skinID==1095||skinID==1100||skinID==1234||skinID==1236||skinID==7012||skin.equals("捕捉")) {
                skill.setX(ScrenceUntil.Screen_x/2);
				skill.setY(ScrenceUntil.Screen_y/2);	
				skill.setSkinpath(1);
			}else if (skinID==4001||skinID==4002||skinID==4003||skinID==4004) {
				int dir=EffectType.getdir(3,image.getFightingManData().getCamp());	
				NewPart part=SpriteFactory.createPart(skin, -1, 0, null);
				ShadowMode shadowMode=new ShadowMode(part, dir==3?image.getX()+70:image.getX()-70,  dir==3?image.getY()+30:image.getY()-30, null, dir==3?1:0);
				skill.setShadowMode(shadowMode);
			}else if (skinID ==1065){
				System.out.println(State.getFightingManData());
				skill.setX(image.getX());
				skill.setY(image.getY());
				skill.setSkinpath(0);
				skill.setId(skinID);
			}else {
				skill.setX(image.getX());
				skill.setY(image.getY());
				skill.setSkinpath(0);
			}
        	skill.setSkillid(skin);
        	progress.setSpell(skill);
		}
		if (State.getMsg()!=null&&State.getCamp()==FightingMixDeal.camp&&FightingMixDeal.myman()==State.getMan()) {
			ZhuFrame.getZhuJpanel().addPrompt2(State.getMsg());
		}
		return progress;	
	}
}
