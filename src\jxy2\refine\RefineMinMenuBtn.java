package jxy2.refine;

import com.tool.btn.MoBanBtn;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class RefineMinMenuBtn extends MoBanBtn {
    public EqartificeJapanel refineJPanel;
    public int typeBtn;
    public RefineMinMenuBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, EqartificeJapanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        refineJPanel.setMinType(typeBtn);
        EquiButtonClick(typeBtn);
        refineJPanel.RefiningDisplay();
        RefineFrame.getRefineFrame().getRefineJPanel().getRichLabel().setTextSize(Juitil.getPrompt(1,typeBtn),160);
    }


    public void EquiButtonClick(int clickedIndex) {
        if (clickedIndex==-1)return;
        refineJPanel.getMinMenuBtn()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <refineJPanel.getMinMenuBtn().length; i++) {
            if (i != clickedIndex) {
                refineJPanel.getMinMenuBtn()[i].btnchange(0);
            }
        }
    }
}
