package com.tool.tcp;

import java.io.IOException;
import java.io.RandomAccessFile;

public class FileRandom {
	public RandomAccessFile randomFile;
	private int use;//重置时间 
	public FileRandom(String fileName) {
		// TODO Auto-generated constructor stub
		try {
			randomFile = new RandomAccessFile(fileName, "r");
		} catch (Exception e) {
			// TODO: handle exception
		}
	}
	/**读取*/
	public void read(long pos,byte[] bs) throws IOException {
		synchronized (this) {
			use=0;
			randomFile.seek(pos);
			randomFile.read(bs);
		}
	}
	/**移除*/
	public boolean isEnd(){
		synchronized (this) {
			if (use++>7) {
				try {
					randomFile.close();
					randomFile=null;
				} catch (Exception e) {
					// TODO: handle exception
					e.printStackTrace();
				}
				return true;
			}	
		}
		return false;
	}
	public int getUse() { return use; }
	public void setUse(int use) { this.use = use; }
	
}
