package jxy2.refine;

import com.tool.btn.MoBanBtn;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.bean.NpcComposeBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
* 装备打造小单带按钮事件
* <AUTHOR>
* @date 2024/7/25 下午6:29
*/

public class EqMakeBtn extends MoBanBtn {

    public EqmMakeJpanel refineJPanel;
    public int typeBtn;
    public EqMakeBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, EqmMakeJpanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {


//        String text  = Juitil.TextInedx(refineJPanel.getMinType());
//        refineJPanel.getRichLabel().setTextSize(text,160);
        if (typeBtn==99){
            String v = RefiningUtil.detection(refineJPanel.goods, 3);
            if (v.equals("普通装备")){
                if (!GoodItem_5(refineJPanel.goods)) {
                    cao5(refineJPanel.goods, refineJPanel.money);
                }
            }else if (v.equals("高级装备")){
                if (!GoodItem_4(refineJPanel.goods)) {
                    cao6(refineJPanel.goods, refineJPanel.money);
                }
            }else if (v.equals("打造")){
                if (!GoodItem_3(refineJPanel.goods)){
                    cao7(refineJPanel.goods,refineJPanel.money,"我要洗炼仙器");
                }
            }else if (v.equals("升级")){
                if (!GoodItem_2(refineJPanel.goods)){
                    cao7(refineJPanel.goods,refineJPanel.money,"我要升级仙器");
                }
            }else if (v.equals("合成")){
                if (!GoodItem_1(refineJPanel.goods)){
                    cao7(refineJPanel.goods,refineJPanel.money,"我要合成仙器");
                }
            }
        }else {
            EquiButtonClick(typeBtn);
            refineJPanel.setMinType(typeBtn);//接装装备炼化，0-4   5开始
            refineJPanel.clear();
            RefineFrame.getRefineFrame().getRefineJPanel().getRichLabel().setTextSize(Juitil.getPrompt(2,typeBtn),160);
        }
    }
    /** 我要合成仙器 */
    public boolean GoodItem_1(Goodstable[] goods) {
        // 瓶子描述信息
        String Bottletext = goods[0].getValue();
        String[] gongneng = null;
        if (Bottletext != null && !Bottletext.equals("")) {
            gongneng = Goodtype.StringParsing(Bottletext);
        }
        // 仙器阶数
        String god = Goodtype.StringParsing(goods[1].getValue())[0];
        // 判断是否是新瓶子
        if (gongneng == null) {
            if (god.equals("阶数=6")) {
                FrameMessageChangeJpanel.addtext(5, "6阶打进瓶子???哎!还是卖太便宜了", null, null);
                return true;
            }
        } else {
            if (!gongneng[0].equals(god)) {
                FrameMessageChangeJpanel.addtext(5, "阶数不相等", null, null);
                return true;
            } else if (ReikiFull(gongneng)) {// 判断瓶子是否满了
                FrameMessageChangeJpanel.addtext(5, "灵气已经满了", null, null);
                return true;
            } else if (AnalysisString.jiaoyi(goods[0].getQuality())!=AnalysisString.jiaoyi(goods[1].getQuality())) {
                FrameMessageChangeJpanel.addtext(5, "绑定和不绑定不能混合", null, null);
                return true;
            }
        }
        return false;
    }
    /** 我要升级仙器 */
    public boolean GoodItem_2(Goodstable[] goods) {
        // 瓶子描述信息
        String Bottletext = goods[0].getValue();
        if (Bottletext.isEmpty()){
            FrameMessageChangeJpanel.addtext(5, "瓶子灵气未满！", null, null);
            return true;
        }
        String[] gongneng = Goodtype.StringParsing(Bottletext);

        // 石头等级
        int kslvl = goods[1].getGoodsid().intValue() - 300;
        // 判断是否是新瓶子
        if (!ReikiFull(gongneng)) {
            FrameMessageChangeJpanel.addtext(5, "瓶子灵气未满！", null, null);
            return true;
        }
        int lvl = Integer.parseInt(gongneng[0].split("=")[1]);
        if (lvl + 5 != kslvl) {
            FrameMessageChangeJpanel.addtext(5, zw(lvl) + "阶仙器请用" + (lvl + 5) + "级矿石升级!", null, null);
            return true;
        } else if (lvl >= 6) {
            FrameMessageChangeJpanel.addtext(5, "不支持六阶仙器升级!", null, null);
            return true;
        } else {
            return false;
        }
    }
    /** 判断瓶子灵气是否满了 */
    public boolean ReikiFull(String[] vlaue) {
        if (vlaue[0].equals("阶数=1") || vlaue[0].equals("阶数=2")) {
            if (Reikisum(vlaue[1]) >= 8) {
                return true;
            }
        } else if (vlaue[0].equals("阶数=3")) {
            if (Reikisum(vlaue[1]) >= 6) {
                return true;
            }
        } else if (vlaue[0].equals("阶数=4")) {
            if (Reikisum(vlaue[1]) >= 5) {
                return true;
            }
        } else {
            if (Reikisum(vlaue[1]) >= 3) {
                return true;
            }
        }
        return false;
    }
    /** 放回阶数的中文名称 */
    public String zw(int lvl) {
        switch (lvl) {
            case 1:
                return "一";
            case 2:
                return "二";
            case 3:
                return "三";
            case 4:
                return "四";
            case 5:
                return "五";
            case 6:
                return "六";
            case 7:
                return "七";
            case 8:
                return "八";
            case 9:
                return "九";
            case 10:
                return "十";
        }
        return "零";
    }
    /** 判断灵气点数 */
    public int Reikisum(String vlaue) {
        Pattern pattern = Pattern.compile("=(.*?)点");// 匹配的模式
        Matcher m = pattern.matcher(vlaue);
        while (m.find()) {
            int i = 1;
            return Integer.parseInt(m.group(i));
        }
        return 0;
    }


    private boolean GoodItem_3(Goodstable[] goods) {
        if (goods[1].getType()!=212){
            return true;
        }else
            return !Goodtype.GodEquipment_xian(goods[0].getType());
    }


    private void cao7(Goodstable[] goods, BigDecimal money,String type) {
        UserData.uptael(money.intValue());
        List<BigDecimal> goodstables = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (i == 0) {
                goods[i].setUsetime(0);
            } else {
                goods[i].setUsetime(goods[i].getUsetime() - 1);
            }
            goodstables.add(goods[i].getRgid());
            if (goods[i].getUsetime() <= 0) {
                GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                goods[i] = null;
                refineJPanel.ClickGood(null, i + 24);
            }
        }

        NpcComposeBean npcComposeBean = new NpcComposeBean();
        npcComposeBean.setComposetype(type);
        npcComposeBean.setGoodstables(goodstables);
        String sendMes = Agreement.getAgreement().npccomposeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(npcComposeBean));
        SendMessageUntil.toServer(sendMes);// 向服务器发送信息
    }

    private void cao6(Goodstable[] goods, BigDecimal money) {
        UserData.uptael(money.intValue());
        List<BigDecimal> goodstables = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] == null) {continue;}
            if (i == 0) {
                goods[i].setUsetime(0);
            } else {
                goods[i].setUsetime(goods[i].getUsetime() - 1);
            }
            goodstables.add(goods[i].getRgid());
            if (goods[i].getUsetime() <= 0) {
                GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                goods[i] = null;
                refineJPanel.ClickGood(null, i + 24);
            }
        }
        NpcComposeBean npcComposeBean = new NpcComposeBean();
        npcComposeBean.setComposetype("打造11-16级装备");
        npcComposeBean.setGoodstables(goodstables);
        String sendMes = Agreement.getAgreement().npccomposeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(npcComposeBean));
        SendMessageUntil.toServer(sendMes);// 向服务器发送信息

    }





    /** 打造11-16级装备 */
    public boolean GoodItem_4(Goodstable[] goods) {
        // 装备等级
        int zblvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (zblvl < 10) {
            FrameMessageChangeJpanel.addtext(5, "打造1-10级请点击普通装备打造!", null, null);
            return true;
        }
        if (zblvl >= 10 && zblvl <= 13) {
            if (kslvl != 8 && kslvl != 9) {
                FrameMessageChangeJpanel.addtext(5, "打造11-14级装备使用9级矿石!"+Juitil.getMineralName(9), null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸10-13级装备使用8级矿石!"+Juitil.getMineralName(8), null, null);
                return true;
            }
        } else if (zblvl == 14) {
            if (kslvl != 9 && kslvl != 10) {
                FrameMessageChangeJpanel.addtext(5, "打造15级装备使用10级矿石!"+Juitil.getMineralName(10), null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸14级装备使用9级矿石!"+Juitil.getMineralName(9), null, null);
                return true;
            }
        } else if (zblvl == 15) {
            if (kslvl != 10 && kslvl != 11) {
                FrameMessageChangeJpanel.addtext(5, "打造16级装备使用11级矿石!"+Juitil.getMineralName(11), null, null);
                FrameMessageChangeJpanel.addtext(5, "重铸15级装备使用10级矿石!"+Juitil.getMineralName(10), null, null);
                return true;
            }
        } else if (zblvl == 16) {
            if (kslvl != 11) {
                FrameMessageChangeJpanel.addtext(5, "重铸16级装备使用11级矿石!"+Juitil.getMineralName(11), null, null);
                return true;
            }
        } else {
            FrameMessageChangeJpanel.addtext(5, "错误公式", null, null);
            return true;
        }
        return false;
    }


    private void cao5(Goodstable[] goods, BigDecimal money) {
        UserData.uptael(money.intValue());
        List<BigDecimal> goodstables = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] == null) {continue;}
            if (i == 0) {
                goods[i].setUsetime(0);
            } else {
                goods[i].setUsetime(goods[i].getUsetime() - 1);
            }
            goodstables.add(goods[i].getRgid());
            if (goods[i].getUsetime() <= 0) {
                GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                goods[i] = null;
                refineJPanel.ClickGood(null, i + 24);
            }
        }
        NpcComposeBean npcComposeBean = new NpcComposeBean();
        npcComposeBean.setComposetype("我要打造普通装备");
        npcComposeBean.setGoodstables(goodstables);
        String sendMes = Agreement.getAgreement().npccomposeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(npcComposeBean));
        SendMessageUntil.toServer(sendMes);// 向服务器发送信息

    }

    /** 我要打造普通装备 */
    public boolean GoodItem_5(Goodstable[] goods) {
        // 装备等级
        int zblvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
        // 矿石等级
        int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
        if (zblvl >= 10) {
            FrameMessageChangeJpanel.addtext(5, "打造11-16级装备点击高级装备!", null, null);
            return true;
        } else if (kslvl > 9) {
            FrameMessageChangeJpanel.addtext(5, "打造1-10级装备最高只能使用9级矿石!", null, null);
            return true;
        } else if (zblvl != kslvl) {
            FrameMessageChangeJpanel.addtext(5, "请放入对应的矿石：#R"+ Juitil.getMineralName(zblvl), null, null);
            return true;
        }
        return false;
    }

    public void EquiButtonClick(int clickedIndex) {
        if (clickedIndex==-1)return;
        refineJPanel.getEqMake()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <refineJPanel.getEqMake().length; i++) {
            if (i != clickedIndex) {
                refineJPanel.getEqMake()[i].btnchange(0);
            }
        }
    }
}
