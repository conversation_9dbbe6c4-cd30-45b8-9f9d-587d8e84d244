package jxy2.xbao;

import javax.swing.*;

public class Vevas extends JPanel {
//    private static FormsOnOffBtn offBtn;
//    public static int width;
//
//    /**
//     *
//     * @param w 窗口宽度
//     * @param  h 窗口高度
//     * @param bh 窗口有ID
//     */
//    public Vevas(int w,int h,int bh) {
//        super();
//        width = w;
//        this.setPreferredSize(new Dimension(w,h));
//        this.setBackground(UIUtils.Color_BACK);
//        this.setLayout(null);
//        offBtn = new FormsOnOffBtn(ImgConstants.stop, 1, bh, 0);
//        offBtn.setBounds(w, 5, 23, 22);
//        add(offBtn);
//    }
//
//    public static void updateButtonImages(int uiType) {
//        int bh = uiType==1? 25:23;
//        offBtn.setBounds(width,5,bh,bh);
//    }
}
