package come.tool.JDialog;

import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class ChildRearingJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		String serverMes = Agreement.getAgreement().BabyCustodayAgreement(tishi?"yes":"no");
        // 发送给服务器
        SendMessageUntil.toServer(serverMes);
	}

}
