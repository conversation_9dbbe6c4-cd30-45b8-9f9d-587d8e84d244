package com.tool.btn;

import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Jpanel.SetsynthesisJpanel;
import org.come.Jpanel.UpgradeJpanel;
import org.come.Jpanel.WorkshopRefiningCardJpanel;
import org.come.Jpanel.WorkshopRefiningJpanel;
import org.come.until.CutButtonImage;

public class WorkshopRefiningBtn extends MoBanBtn {

    /** 炼化装备1 炼化配饰2 炼器3 套装合成4 合成10 洗炼11 套装升级12 玉符升级13 拆解/转移 14 */
    private int caozuo;
    private WorkshopRefiningCardJpanel cardJpanel;
    private WorkshopRefiningJpanel finingJpanel;

    private SetsynthesisJpanel setsynthesisJpanel;

    public WorkshopRefiningBtn(String iconpath, int type, int caozuo, WorkshopRefiningCardJpanel cardJpanel,
            WorkshopRefiningJpanel finingJpanel) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        this.cardJpanel = cardJpanel;
        this.finingJpanel = finingJpanel;
    }

    public WorkshopRefiningBtn(String iconpath, int type, String text, int caozuo) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public WorkshopRefiningBtn(String iconpath, int type, int caozuo, SetsynthesisJpanel setsynthesisJpanel) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.caozuo = caozuo;
        this.setsynthesisJpanel = setsynthesisJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        try {
            switch (caozuo) {
            case 1:
                finingJpanel.getBtnEqui().setIcons(CutButtonImage.cuts("inkImg/button/B92.png"));
                finingJpanel.getBtnAcc().setIcons(CutButtonImage.cuts("inkImg/button/B91.png"));
                finingJpanel.getBtnRefiners().setIcons(CutButtonImage.cuts("inkImg/button/B95.png"));
                finingJpanel.getBtnSetsynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B97.png"));
                finingJpanel.getBtnGemRefinery().setIcons(CutButtonImage.cuts("inkImg/button/B312.png"));
                cardJpanel.getEquiJpanel().changeFrom(caozuo);
                cardJpanel.getCar().show(cardJpanel, "l1");
                break;
            case 2:
                finingJpanel.getBtnEqui().setIcons(CutButtonImage.cuts("inkImg/button/B93.png"));
                finingJpanel.getBtnAcc().setIcons(CutButtonImage.cuts("inkImg/button/B90.png"));
                finingJpanel.getBtnRefiners().setIcons(CutButtonImage.cuts("inkImg/button/B95.png"));
                finingJpanel.getBtnSetsynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B97.png"));
                finingJpanel.getBtnGemRefinery().setIcons(CutButtonImage.cuts("inkImg/button/B312.png"));
                cardJpanel.getEquiJpanel().changeFrom(caozuo);
                cardJpanel.getCar().show(cardJpanel, "l1");
                break;
            case 3:
                finingJpanel.getBtnEqui().setIcons(CutButtonImage.cuts("inkImg/button/B93.png"));
                finingJpanel.getBtnAcc().setIcons(CutButtonImage.cuts("inkImg/button/B91.png"));
                finingJpanel.getBtnRefiners().setIcons(CutButtonImage.cuts("inkImg/button/B94.png"));
                finingJpanel.getBtnSetsynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B97.png"));
                finingJpanel.getBtnGemRefinery().setIcons(CutButtonImage.cuts("inkImg/button/B312.png"));
                cardJpanel.getCar().show(cardJpanel, "l3");
                break;
            case 4:
                finingJpanel.getBtnEqui().setIcons(CutButtonImage.cuts("inkImg/button/B93.png"));
                finingJpanel.getBtnAcc().setIcons(CutButtonImage.cuts("inkImg/button/B91.png"));
                finingJpanel.getBtnRefiners().setIcons(CutButtonImage.cuts("inkImg/button/B95.png"));
                finingJpanel.getBtnSetsynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B96.png"));
                finingJpanel.getBtnGemRefinery().setIcons(CutButtonImage.cuts("inkImg/button/B312.png"));
                cardJpanel.getCar().show(cardJpanel, "l4");
                break;
            case 5:
                finingJpanel.getBtnEqui().setIcons(CutButtonImage.cuts("inkImg/button/B93.png"));
                finingJpanel.getBtnAcc().setIcons(CutButtonImage.cuts("inkImg/button/B91.png"));
                finingJpanel.getBtnRefiners().setIcons(CutButtonImage.cuts("inkImg/button/B95.png"));
                finingJpanel.getBtnSetsynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B97.png"));
                finingJpanel.getBtnGemRefinery().setIcons(CutButtonImage.cuts("inkImg/button/B311.png"));
                cardJpanel.getCar().show(cardJpanel, "l5");
                cardJpanel.getGemRefineryMainJpanel().initView(-1);
                break;
            case 10:// 合成10
                UpgradeJpanel.clearInterface();
                setsynthesisJpanel.getLabSynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B101.png"));
                setsynthesisJpanel.getLabWash().setIcons(CutButtonImage.cuts("inkImg/button/B104.png"));
                setsynthesisJpanel.getLabUpgrade().setIcons(CutButtonImage.cuts("inkImg/button/B102.png"));
                setsynthesisJpanel.getLabJadeUp().setIcons(CutButtonImage.cuts("inkImg/button/B106.png"));
                setsynthesisJpanel.getLabTransfer().setIcons(CutButtonImage.cuts("inkImg/button/B98.png"));
                setsynthesisJpanel.getCardJpanel().getCar().show(setsynthesisJpanel.getCardJpanel(), "l1");
                break;
            case 11:// 洗炼11
                UpgradeJpanel.clearInterface();
                setsynthesisJpanel.getLabSynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B100.png"));
                setsynthesisJpanel.getLabWash().setIcons(CutButtonImage.cuts("inkImg/button/B105.png"));
                setsynthesisJpanel.getLabUpgrade().setIcons(CutButtonImage.cuts("inkImg/button/B102.png"));
                setsynthesisJpanel.getLabJadeUp().setIcons(CutButtonImage.cuts("inkImg/button/B106.png"));
                setsynthesisJpanel.getLabTransfer().setIcons(CutButtonImage.cuts("inkImg/button/B98.png"));
                setsynthesisJpanel.getCardJpanel().getCar().show(setsynthesisJpanel.getCardJpanel(), "l2");
                break;
            case 12:// 套装升级12
                UpgradeJpanel.clearInterface();
                setsynthesisJpanel.getLabSynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B100.png"));
                setsynthesisJpanel.getLabWash().setIcons(CutButtonImage.cuts("inkImg/button/B104.png"));
                setsynthesisJpanel.getLabUpgrade().setIcons(CutButtonImage.cuts("inkImg/button/B103.png"));
                setsynthesisJpanel.getLabJadeUp().setIcons(CutButtonImage.cuts("inkImg/button/B106.png"));
                setsynthesisJpanel.getLabTransfer().setIcons(CutButtonImage.cuts("inkImg/button/B98.png"));
                setsynthesisJpanel.getCardJpanel().getCar().show(setsynthesisJpanel.getCardJpanel(), "l3");
                break;
            case 13:// 玉符升级13
                UpgradeJpanel.clearInterface();
                setsynthesisJpanel.getLabSynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B100.png"));
                setsynthesisJpanel.getLabWash().setIcons(CutButtonImage.cuts("inkImg/button/B104.png"));
                setsynthesisJpanel.getLabUpgrade().setIcons(CutButtonImage.cuts("inkImg/button/B102.png"));
                setsynthesisJpanel.getLabJadeUp().setIcons(CutButtonImage.cuts("inkImg/button/B107.png"));
                setsynthesisJpanel.getLabTransfer().setIcons(CutButtonImage.cuts("inkImg/button/B98.png"));

                UpgradeJpanel.setGoodstable(null);
                UpgradeJpanel.getLabtz2().setIcon(null);
                setsynthesisJpanel.getCardJpanel().getCar().show(setsynthesisJpanel.getCardJpanel(), "l4");
                break;
            case 14:// 拆解/转移 14
                UpgradeJpanel.clearInterface();
                setsynthesisJpanel.getLabSynthesis().setIcons(CutButtonImage.cuts("inkImg/button/B100.png"));
                setsynthesisJpanel.getLabWash().setIcons(CutButtonImage.cuts("inkImg/button/B104.png"));
                setsynthesisJpanel.getLabUpgrade().setIcons(CutButtonImage.cuts("inkImg/button/B102.png"));
                setsynthesisJpanel.getLabJadeUp().setIcons(CutButtonImage.cuts("inkImg/button/B106.png"));
                setsynthesisJpanel.getLabTransfer().setIcons(CutButtonImage.cuts("inkImg/button/B99.png"));
                setsynthesisJpanel.getCardJpanel().getCar().show(setsynthesisJpanel.getCardJpanel(), "l5");
                break;
            default:
                break;
            }

        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }

    }

    /** 套装合成面板子面板切换 */
    // public void SetsynthesChangeViewMenu() throws Exception {
    // UpgradeJpanel.clearInterface(); // 清空界面
    // for (int i = 0; i < setsynthesisJpanel.getMenuBtn().length; i++) {
    // if ((i + 10) == caozuo) {
    // setsynthesisJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/63.png"));
    // } else {
    // setsynthesisJpanel.getMenuBtn()[i].setIcons(CutButtonImage.cuts("inkImg/button/62.png"));
    // }
    // }
    // }
}
