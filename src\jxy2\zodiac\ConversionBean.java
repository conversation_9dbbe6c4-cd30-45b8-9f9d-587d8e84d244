package jxy2.zodiac;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConversionBean {
    private Map<BigDecimal, Integer> goodstableMap = new HashMap<>();
    private List<BigDecimal> goodsrgid = new ArrayList<>();

    public Map<BigDecimal, Integer> getGoodstableMap() {
        return goodstableMap;
    }

    public void setGoodstableMap(Map<BigDecimal, Integer> goodstableMap) {
        this.goodstableMap = goodstableMap;
    }

    public List<BigDecimal> getGoodsrgid() {
        return goodsrgid;
    }

    public void setGoodsrgid(List<BigDecimal> goodsrgid) {
        this.goodsrgid = goodsrgid;
    }
}
