package come.tool.handle;

import org.come.Frame.ZhuFrame;
import org.come.bean.FightOperation;

import com.tool.imagemonitor.FightingMonitor;
import come.tool.Fighting.FightingMixDeal;

public class HandlePlayer  implements Handle{

	@Override
	public void handle(long DieTime) {
		// TODO Auto-generated method stub
		if (ZhuFrame.getZhuJpanel().getZidong().getText().equals("取消")){	
			FightingMixDeal.CorrectTime+=DieTime;
			if (FightingMixDeal.CorrectTime>FightingMixDeal.AUTOMATION_PLAYER) {
				FightingMixDeal.CorrectTime=0;
				FightOperation operation=FightingMonitor.getOperation();
				FightingMonitor.execution(operation,true);
			}
		}
		if (FightingMixDeal.time>FightingMixDeal.ROUND_TIME) {
			if (FightingMixDeal.State==HandleState.HANDLE_PLAYER) {
				FightOperation operation=FightingMonitor.getOperation();
				FightingMonitor.execution(operation,true);
			}
			if (FightingMixDeal.State==HandleState.HANDLE_PET) {
				FightOperation operation=FightingMonitor.getOperation();
				FightingMonitor.execution(operation,true);
			}
		}
	}

}
