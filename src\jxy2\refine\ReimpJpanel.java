package jxy2.refine;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Goodtype;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 装备炼器面板
* <AUTHOR>
* @date 2024/7/28 下午8:13
*/

public class ReimpJpanel extends JPanel {
    private  FinereBtn[] finerBtn =new FinereBtn[3];
    public String[] finer;
    private int minType = 0;
    public JLabel[] labMark = new JLabel[5];// 放置炼化装备以及炼化材料
    public Goodstable[] goods = new Goodstable[5];
    private FinereBtn workshopBtn;// 炼化装备按钮
    public BigDecimal money;
    public ReimpJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        money = new BigDecimal(100000);
        finer = new String[]{"开光","炼器","清除"};
        for (int i = 0; i < finerBtn.length; i++) {
            finerBtn[i] = new FinereBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, finer[i], i, this,"");
            finerBtn[i].btnchange(i==0?2:0);
            finerBtn[i].setBounds(138+i*66, 48, 66, 24);
            add(finerBtn[i]);
        }
        for (int i = 0; i < 5; i++) {
            labMark[i] = new JLabel();
            if (minType == 0) {
                labMark[i].setBounds(215 + i * 134, 202,50,50);
            } else {
                int row = i % 2;
                int col = i / 2;
                labMark[i].setBounds(250 + row * 67, 79 + col * 65, 53, 51);
            }
            labMark[i].addMouseListener(new RefineMouse(this, i + 24));
            this.add(labMark[i]);
        }
        // 炼化装备按钮
        workshopBtn = new FinereBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 4, this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);
    }


    /** 点击good */
    public void ClickGood(Goodstable good, int path) {
        if (path < 24) {
                if (Goodtype.Weapons(good.getType())) {
                    change(good, 0);
                } else {
                    if (good.getType() == 505 || good.getType() == 507) {
                        path = vacantGood();
                        if (path > 0) {
                            change(good, path);
                        }
                    }
                }
        } else {
            change(null, path - 24);
        }
        String v = detection();
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }

    /** 切换指定位置·炼器 */
    public void change(Goodstable good, int path) {
        goods[path] = good;
        if (good != null) {
            labMark[path].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        } else {
            labMark[path].setIcon(null);
        }
    }

    /** 检测公式 */
    public String detection() {
        int path = vacantGood();
        if (path == 0) {
            return "?";
        }
        Goodstable good = goods[0];
        if (!Goodtype.Weapons(good.getType())) {
            return "?";
        }
        if (path == 1&&minType==2) {
            if (good.getValue().contains("炼器属性")) {
                return "清除";
            }
        } else if (path == 2&&minType==0) {
            if (goods[1].getType() == 505) {// 505
                return "开光";
            }
        } else if (path == 4&&minType==1) {
            if (goods[1].getType() == 507 || goods[2].getType() == 507 || goods[3].getType() == 507) {// 507
                return "炼器";
            }
        }
        return "?";
    }
    /** 返回空余位置 */
    public int vacantGood() {
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] == null) {
                return i;
            }
        }
        return -1;
    }


    /**清除*/
    public void clear() {
        for (int i = 0; i < 4; i++) {
            labMark[i].setIcon(null);
            goods[i] = null;
        }
        if (minType==1) {
            for (int i = 0; i < 4; i++) { // 控制循环次数，minType==0时为6，minType==1时为2
                int centerX = 282; // 圆心的X坐标
                int centerY = 203; // 圆心的Y坐标
                int radius = 70;  // 圆的半径
                int numElements = 4; // 元素数量
                double angleIncrement = Math.PI / numElements * 2; // 角度增量
                double angle = i * angleIncrement; // 当前元素的角度
                int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                labMark[i].setBounds(x, y, 50, 50);
                labMark[i].setVisible(true);
            }
        }else if (minType==0){
            for (int i = 0; i < 4; i++) {
                labMark[i].setBounds(215 + i * 134, 202,50,50);
                labMark[i].setVisible(true);
            }
        }else {
            labMark[0].setBounds(282, 202,50,50);
            labMark[0].setVisible(true);
        }
//
        int xhsum = minType==0?2:minType==1?4:1;
        // 隐藏多余的元素
        for (int i = xhsum; i < 4; i++) {
            labMark[i].setVisible(false);
        }


    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (minType==0){
            for (int i = 0; i < 2; i++) {
                Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
                if (labMark[i].getIcon()==null){
                                String text = getPrompt(minType)[i];
                    Font font = UIUtils.FZCY_HY13;
                    FontMetrics metrics = org.come.until.SafeFontMetrics.getFontMetrics(font);
                    int textWidth = metrics.stringWidth(text);
                    int x = 238 + i * 134 - textWidth / 2; // 调整X坐标，使其居中
                    Juitil.TextBackground(g, getPrompt(minType+7)[i], 13, x, 220, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                }

            }
        }else if (minType==1){
            int centerX = 282; // 圆心的X坐标
            int centerY = 203; // 圆心的Y坐标
            int radius = 70;  // 圆的半径
            int numElements = 4; // 元素数量
            double angleIncrement =  Math.PI / numElements * 2; // 角度增量
            for (int i = 0; i < numElements; i++) {
                double angle = i * angleIncrement; // 当前元素的角度
                int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1);
                if (labMark[i].getIcon()==null){
               Juitil.TextBackground(g, getPrompt(minType+7)[i],13, x+9,y+20,UIUtils.COLOR_goods_quantity,UIUtils.FZCY_HY13);
                }
            }
        }else {
            Juitil.ImngBack(g, Juitil.good_2, 282, 202, 50, 50, 1);
            if (labMark[0].getIcon()==null) {
                Juitil.TextBackground(g, "清除", 13, 292, 220, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }
        }
    }
    public String[] getPrompt(int type) {
        String[] result = EqartificeJapanel.PROMPT_MAP.get(type);
        return result != null ? result.clone() : null;
    }

    public FinereBtn[] getFinerBtn() {
        return finerBtn;
    }

    public void setFinerBtn(FinereBtn[] finerBtn) {
        this.finerBtn = finerBtn;
    }


    public void setMinType(int minType) {
        this.minType = minType;
    }

    public int getMinType() {
        return minType;
    }

    public FinereBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(FinereBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }
}
