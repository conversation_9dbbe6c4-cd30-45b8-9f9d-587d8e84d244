package com.tool.role;

import com.tool.btn.BaptizeBtn;
import com.tool.btn.JpanelOnJalbelBtn;
import com.tool.time.Limit;
import com.tool.time.TimeLimit;
import jxy2.dress.Faction;
import jxy2.zodiac.ChineseZodiacFrame;
import org.come.Frame.FactionAngelJframe;
import org.come.Frame.TestpackJframe;
import org.come.Jpanel.FactionAngelJpanel;
import org.come.bean.LoginResult;
import org.come.bean.PrivateData;
import org.come.bean.RoleSuitBean;
import org.come.entity.Baby;
import org.come.entity.Goodstable;
import org.come.model.Lingbao;
import org.come.model.Title;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.until.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

/** 人物抗性和人物属性 */
public class RoleProperty {

	public static RoleProperty property;

	public static RoleProperty getRoleProperty() {

		if (property == null)
			initial();
		return property;
	}

	/** 初始化人物抗性 */
	public static void initial() {
		property = new RoleProperty();
		property.grade = new HashMap<>();
		property.born = new HashMap<>();
		property.equip = new HashMap<>();
		property.additional = new HashMap<>();
		property.xianzhi = new HashMap<>();
		property.quality = new Ql();
		property.stunts = new ArrayList<>();
        property.meridiansVector=new Vector<>();

		LoginResult loginResult = RoleData.getRoleData().getLoginResult();
		BigDecimal hp=loginResult.getHp();
		BigDecimal mp=loginResult.getMp();
		Resetgrade(loginResult.getGrade(), loginResult.getRace_id());
		property.upMeridians(loginResult.getMeridians());
		PrivateData data = RoleData.getRoleData().getPrivateData();
		Resetborn(null, data.getBorn());
		ResetEw();
		if (hp.longValue()!=0) {
			loginResult.setHp(hp);
			loginResult.setMp(mp);
		}
		Article.manxie(loginResult);
		ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel().ActivatedSkillQuery(false);
	}

	// 人物元素属性
	// 等级属性
	Map<String, Double> grade;
	// 修正属性
	Map<String, Double> born;
	// 装备属性
	Map<String, Double> equip;
	// 额外属性
	Map<String, Double> additional;
	// 条件限制
	Map<String, Double> xianzhi;
	public  static List<Integer> integers = new ArrayList<>();
	// 记录生效的特技 套装技能
	List<Stunt> stunts;
	double hp = 0;
	double mp = 0;
	double ap = 0;
	int qhv;//强化等级
	int refinelv;//精炼等级
	// 抗性类 每次使用都要重置
	Ql quality;
    public Vector<BaseMeridians> meridiansVector;

	public Ql getQuality() {
		quality.Reset();
		List<String> keys = allProperty();
		if (keys.contains("四抗")) {
			String value = "抗封印";
			if (!keys.contains(value)) {
				keys.add(value);
			}
			value = "抗混乱";
			if (!keys.contains(value)) {
				keys.add(value);
			}
			value = "抗昏睡";
			if (!keys.contains(value)) {
				keys.add(value);
			}
			value = "抗遗忘";
			if (!keys.contains(value)) {
				keys.add(value);
			}
		}
		for (int i = 0; i < keys.size(); i++) {
			QualityZW.insertValues(quality, keys.get(i), getvalue(keys.get(i)));
		}
		if (keys.contains("水魔附身")) {
			double value = getvalue("水魔附身");
			if (value > 0) {
				quality.setRolewxj(0);
				quality.setRolewxm(0);
				quality.setRolewxh(0);
				quality.setRolewxt(0);
				quality.setRolewxs(100);
				quality.setRolewxqkh(quality.getRolewxqkh() + 100);
			}
		}
		return quality;
	}

	/**
	 * 上限过滤判断 仙族　抗人法/遗忘上限110　(其它抗性无上限) 　 魔族　抗人法/遗忘上限110　(其它抗性无上限) 　
	 * 人族　抗人法/遗忘上限140　(其它抗性无上限) 　 鬼族　抗人法上限120%/遗忘上限140%(其它抗无上限)
	 * 人物飞升后每升10级可提升一定(冰混睡忘)抗性上限 　 每升10级：人族+1.2　魔族+1.1　仙族1　鬼族+0.9
	 */
	public double filterUp(String key, double grade, double equip, double born, double additional) {
		if (key.endsWith("上限") || key.equals("四抗")) {
			return grade + equip + born + additional;
		} else if (key.equals("抗混乱") || key.equals("抗昏睡") || key.equals("抗封印") || key.equals("抗遗忘")) {
			LoginResult loginResult = RoleData.getRoleData().getLoginResult();
			double sx = BaseValue.Upper(key, loginResult.getRace_id())+refinelv*0.4;
			sx = sx * (1 + (getvalue(key + "上限") + getvalue("四抗上限")) / 100.0);
			double z = grade + equip + born + getvalue("四抗");
			return (z > sx ? sx : z) + additional;
		} else if (key.equals("抗三尸") || key.equals("抗浩然正气") || key.equals("抗青面獠牙") || key.equals("抗天魔解体")
				|| key.equals("抗小楼夜哭") || key.equals("抗分光化影")) {

		} else if (key.equals("物理吸收") || key.startsWith("抗")) {
			equip = Math.min(equip, 75 + refinelv * 1.5);
		} else if (key.equals("连击率")) {
			return grade + equip + born + additional > 75 ? 75 : grade + equip + born + additional;
		}
		return grade + equip + born + additional;
	}

	// 获取2个map集合所以的key值
	// 获取值
	public double getvalue(String key) {
		return filterUp(key, getgrade(key), getequip(key), getborn(key), getadditional(key));
	}

	public double getgrade(String key) {
		if (grade.containsKey(key))
		return grade.get(key) - 195764;
		return 0;
	}

	public double getequip(String key) {
		if (equip.containsKey(key))
		return equip.get(key) - 195764;
		return 0;
	}

	public double getborn(String key) {
		if (born.containsKey(key))
		return born.get(key) - 195764;
		return 0;
	}

	public double getadditional(String key) {
		if (additional.containsKey(key))
		return additional.get(key) - 195764;
		return 0;
	}

	public void addgrade(String key, double value) {
		grade.put(key, getgrade(key) + value + 195764);
	}

	public void addequip(String key, double value) {
		equip.put(key, getequip(key) + value + 195764);
	}

	public void addborn(String key, double value) {
		born.put(key, getborn(key) + value + 195764);
	}

	public void addadditional(String key, double value) {
		additional.put(key, getadditional(key) + value + 195764);
	}

	/**
	 * 取所有的抗性
	 */
	public static List<String> allProperty() {
		List<String> Propertys = new ArrayList<String>();
		RoleProperty roleProperty = getRoleProperty();
		for (Entry<String, Double> entry : roleProperty.born.entrySet()) {
			if (!Propertys.contains(entry.getKey()))
				Propertys.add(entry.getKey());
		}
		for (Entry<String, Double> entry : roleProperty.grade.entrySet()) {
			if (!Propertys.contains(entry.getKey()))
				Propertys.add(entry.getKey());
		}
		for (Entry<String, Double> entry : roleProperty.equip.entrySet()) {
			if (!Propertys.contains(entry.getKey()))
				Propertys.add(entry.getKey());
		}
		for (Entry<String, Double> entry : roleProperty.additional.entrySet()) {
			if (!Propertys.contains(entry.getKey()))
				Propertys.add(entry.getKey());
		}
		return Propertys;
	}
	/**炼化属性操作*/
	public void equipWearOff2(String value,EquipBase equipBase) {
		String[] vStrings = value.split("\\&");
		for (int k = 1; k < vStrings.length; k++) {
			String[] mes = vStrings[k].split("=");
			if (mes[0].equals("特技")) {
				for (int l = 1; l < mes.length; l++) {
					int id=Integer.parseInt(mes[l]);
					addStunt(id, 0, 0, 1);
					if (id==8016) {equipBase.xs=-999;}
					else if (id==8017) {equipBase.isL=true;}
				}
			}else if (!mes[0].equals("星阵属性")) {
				property.addequip(mes[0], Double.parseDouble(mes[1]));
			}
		}
	}
	/**炼器属性操作*/
	public void equipWearOff3(String value,EquipBase equipBase) {
		String[] vStrings = value.split("\\&");
		for (int k = 2; k < vStrings.length; k++) {
			String[] mes = vStrings[k].split("=");
			property.addequip(mes[0], Double.parseDouble(mes[1]));
		}
	}
	/**神兵属性操作*/
	public void equipWearOff4(String value,EquipBase equipBase) {
		String[] vStrings = value.split("\\&");
		for (int k = 1; k < vStrings.length; k++) {
			String[] mes = vStrings[k].split("=");
			if (mes[0].startsWith("属性需求")) {
				double zhi=Double.parseDouble(mes[1])/100D;
				if (mes[0].indexOf("减少")!=-1) {zhi=-zhi;}
				if (equipBase.xs!=-999) {equipBase.xs=equipBase.xs+zhi;}
			}else {
				property.addequip(mes[0], Double.parseDouble(mes[1]));	
			}
			
		}
	}
	/**套装属性操作*/
	public void equipWearOff5(String value,EquipBase equipBase) {
		String[] vStrings = value.split("\\&");
		for (int k = 4; k < vStrings.length; k++) {
			String[] mes = vStrings[k].split("=");
			property.addequip(mes[0], Double.parseDouble(mes[1]));
		}
		Double sum = xianzhi.get(vStrings[1]);
		if (sum == null) {sum = 0D;}
		sum++;
		xianzhi.put(vStrings[1], sum);
		int suitid = Integer.parseInt(vStrings[1]);
		RoleSuitBean suit = UserMessUntil.getSuit(suitid);
		if (suit != null) {
			String[] vs = suit.getHaveSkill().split("\\|");
			for (int k = 0; k < vs.length; k++) {
				String[] vss = vs[k].split("-");
				int maxsum = Integer.parseInt(vss[0]);
				int id = Integer.parseInt(vss[1]);
				addStunt(id, suitid, RoleLingFa.getQv(vStrings[3]) / 10, maxsum);
			}
		}
	}
	/**宝石属性操作*/
	public void equipWearOff6(String value,EquipBase equipBase) {
		String[] vStrings = value.split("\\&");
		for (int k = 1; k < vStrings.length; k++) {
			Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(vStrings[k]));
			if (good != null) {
				String[] bs = good.getValue().split("\\|")[1].split("=");
				property.addequip(bs[0], Double.parseDouble(bs[1]));
			}
		}
	}
	class EquipBase{
		private String[] v;
		private Integer qhv;
		private Integer refinelv;//精炼等级
		private int lm;//力量要求
		private int lx;//灵性要求
		private int gg;//根骨要求
		private int mj;//敏捷要求
		private double xs;//属性需求   -999是无属性
		private int zs;//最小转生
		private int lvl;//最小等级
		private int zsMax;//最大转生
		private int lvlMax;//最大等级
		private boolean isL;//true表示无级别
		public EquipBase(String[] v,Integer qhv,Integer refinelv) {
			// TODO Auto-generated constructor stub
			this.v=v;
			this.qhv=qhv;
			this.refinelv=refinelv;
			reset();
		}
		public void uplvl(int type,String value){
			String[] lvls=value.split("转");
			if (lvls.length==1) {
				if (type==0) {lvl=Integer.parseInt(lvls[0]);}
				else {lvlMax=Integer.parseInt(lvls[0]);}
			}else {
				if (lvls[0].equals("飞升")) {
					if (type==0) {zs=4;}
					else {zsMax=4;}
				}else {
					if (type==0) {zs=Integer.parseInt(lvls[0]);}
					else {zsMax=Integer.parseInt(lvls[0]);}
				}
				if (type==0) {lvl=Integer.parseInt(lvls[1]);}
				else {lvlMax=Integer.parseInt(lvls[1]);}
			}
		}
		public void reset(){
			this.lm=0;//力量要求
			this.lx=0;//灵性要求
			this.gg=0;//根骨要求
			this.mj=0;//敏捷要求
			this.xs=1D;//属性需求   -999是无属性
			this.zs=0;//最小转生
			this.lvl=0;//最小等级
			this.zsMax=4;//最大转生
			this.lvlMax=200;//最大等级
			this.isL=false;
		}
	}
	/**强化宝石加成系数*/
	public static double getQHGemXS(int lvl){
		double xs=0D;
		for (int i = 1; i <=lvl; i++) {
			xs+=((i-1)/3+1)*0.8;
		}
		return xs;
	}
	/** 刷新装备属性 */
	public void equipWearOff() {
		int refinelvs = 0;
		LoginResult loginResult = RoleData.getRoleData().getLoginResult();
		equip.clear();
		integers.clear();
		Goodstable[] goodstables = GoodsListFromServerUntil.getChoseGoodsList();
		EquipBase[] a=new EquipBase[15];
		for (int i = 0; i < goodstables.length; i++) {
			Goodstable goodstable = goodstables[i];
			String value = goodstable!=null?goodstable.getValue():null;
			if (value == null || value.equals("")){continue;}
			String[] v = value.split("\\|");
			a[i]=new EquipBase(v, goodstable.getQhv(),goodstable.getRefinelv());
			refinelvs += goodstable.getRefinelv()==null?1000:1000+goodstable.getRefinelv()*888;
			TestpackJframe.getTestpackJframe().getTestpackJapnel().pingf = ""+refinelvs;
		}
		S:while (true) {
			equip.clear();
			xianzhi.clear();
			stunts.clear();
			this.qhv=0;
			this.refinelv=0;
			int size=0;
			int qhv=0;
			int refinelv=0;
			for (int i = 0; i < a.length; i++) {
				EquipBase equipBase=a[i];
				String[] v = equipBase!=null?equipBase.v:null;
				if (v==null) {continue;}
				double qhXS=1D;
				if (equipBase.qhv!=null) {
					size++;
					if (qhv==0||qhv>equipBase.qhv) {qhv=equipBase.qhv;}
					qhXS+=getQHGemXS(equipBase.qhv)/100D;
				}else if (equipBase.refinelv!=null){
					size++;
					if (refinelv==0||refinelv>equipBase.refinelv) {refinelv=equipBase.refinelv;}
					qhXS+=getQHGemXS(equipBase.refinelv)/100D;
				}
				if (i==14) {
					int pz = RoleLingFa.getQv(v[0].split("=")[1])/10;
					int lvl = Integer.parseInt(v[1].split("=")[1]);
					for (int j = 4; j < v.length; j++) {
						if (v[j].startsWith("根骨=") || v[j].startsWith("灵性=") || v[j].startsWith("力量=") || v[j].startsWith("敏捷=") || v[j].startsWith("定力=")) {
							String[] mes = v[j].split("=");
							if (mes.length != 2) {continue;}
							int value = Integer.parseInt(mes[1]);
							value *= (1 + lvl * 0.1);
							value *= (1 + (pz == 6 ? 3.2 : pz == 5 ? 1.6 : pz == 4 ? 0.8 : pz == 3 ? 0.4 : pz == 2 ? 0.2 : 0));
							property.addequip(mes[0], value);
						} else if (v[j].startsWith(BaptizeBtn.Extras[0])) {// 炼化
							equipWearOff2(v[j],equipBase);
						}
					}
				}else {
					for (int j = 0; j < v.length; j++) {
						if (v[j].startsWith(BaptizeBtn.Extras[0])) {// 炼化
							equipWearOff2(v[j],equipBase);
						}else if (v[j].startsWith(BaptizeBtn.Extras[1])) {// 炼器
							equipWearOff3(v[j],equipBase);
						}else if (v[j].startsWith(BaptizeBtn.Extras[2])) {// 神兵
							equipWearOff4(v[j],equipBase);
						}else if (v[j].startsWith(BaptizeBtn.Extras[3])) {// 套装
							equipWearOff5(v[j],equipBase);
						}else if (v[j].startsWith(BaptizeBtn.Extras[4])) {// 宝石
							equipWearOff6(v[j],equipBase);
						}else {
							String[] mes = v[j].split("=");
							if (mes.length != 2) {continue;}
							if (mes[0].equals("装备角色")) {//角色判断 马上判断
								if (mes[1].indexOf(loginResult.getLocalname()) == -1) {
									a[i]=null;
								    continue S;
							    }
							}else if (mes[0].equals("性别要求")||mes[0].equals("性别")) {//性别判断 马上判断
								if (!mes[1].equals("2")) {
									String sexBtn = Integer.valueOf(mes[1]) == 1 ? "男" : "女";
									if (!loginResult.getSex().equals(sexBtn)) {
										a[i]=null;
									    continue S;
									}
								}
							}else if (mes[0].equals("等级要求")) {//等级判断 延后判断
								equipBase.uplvl(0, mes[1]);
							}else if (mes[0].equals("最高携带等级")) {//等级判断 延后判断
								equipBase.uplvl(1, mes[1]);
							}else if (mes[0].equals("根骨要求")) {
								equipBase.gg=Integer.parseInt(mes[1]);
							}else if (mes[0].equals("灵性要求")) {
								equipBase.lx=Integer.parseInt(mes[1]);
							}else if (mes[0].equals("力量要求")) {
								equipBase.lm=Integer.parseInt(mes[1]);
							}else if (mes[0].equals("敏捷要求")) {
								equipBase.mj=Integer.parseInt(mes[1]);
							}else if (mes[0].equals("品质")||mes[0].equals("颜色")||mes[0].equals("星级")||mes[0].equals("经验")) {
								continue;
							}
							property.addequip(mes[0], getdouble(mes[1])*qhXS);
						}
					}
				}
			}
			resetStunt();
			if (size==5&&qhv!=0) {
				property.addequip("HP", 2000*qhv);
				property.addequip("MP", 1000*qhv);
				this.qhv=qhv;
				//================================
				property.addequip("HP", 2000*refinelv);
				property.addequip("MP", 1000*refinelv);
				this.refinelv=refinelv;
			}
			for (int i = 0; i < a.length; i++) {
				EquipBase equipBase=a[i];
				if (equipBase==null) {continue;}
				if (!equipBase.isL) {//判断装备等级
					int zs=AnalysisString.lvltrue(loginResult.getGrade());
					int lvl=AnalysisString.lvlint(loginResult.getGrade());
					if (zs<equipBase.zs) {
						a[i]=null;
					    continue S;
					}else if (zs==equipBase.zs&&lvl<equipBase.lvl) {
						a[i]=null;
					    continue S;
					}else if (zs>equipBase.zsMax) {
						a[i]=null;
					    continue S;
					}else if (zs==equipBase.zsMax&&lvl>equipBase.lvlMax) {
						a[i]=null;
					    continue S;
					}
				}
				if (equipBase.xs!=-999) {//判断属性需求
					if (equipBase.gg!=0&&RoleProperty.getBone(loginResult) < equipBase.gg*equipBase.xs) {
						a[i]=null;
					    continue S;
					}else if (equipBase.lx!=0&&RoleProperty.getSpir(loginResult) < equipBase.lx*equipBase.xs) {
						a[i]=null;
					    continue S;
					}else if (equipBase.lm!=0&&RoleProperty.getPower(loginResult) < equipBase.lm*equipBase.xs) {
						a[i]=null;
					    continue S;
					}else if (equipBase.mj!=0&&RoleProperty.getSpeed(loginResult) < equipBase.mj*equipBase.xs) {
						a[i]=null;
					    continue S;
					}
				}
			}
			break;
		}
		int hpz = getHp(loginResult);
		int mpz = getMp(loginResult);
		if (loginResult.getHp().intValue()==0) {
			loginResult.setHp(new BigDecimal(hpz));
			loginResult.setMp(new BigDecimal(mpz));
		}else {
			if (loginResult.getHp().intValue() > hpz) {
				loginResult.setHp(new BigDecimal(hpz));
			}
			if (loginResult.getMp().intValue() > mpz) {
				loginResult.setMp(new BigDecimal(mpz));
			}
		}
		// 刷新面板
		try {
			JpanelOnJalbelBtn.testReflect(getQuality());
			PetAddPointMouslisten.getplayerValue();
			TestpackJframe.getTestpackJframe().getTestpackJapnel().isShowGemImg(qhv);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 重置人物等级抗性
	 * 
	 * @return
	 */
	public static void Resetgrade(int grade, BigDecimal raceid) {
		int lvl = AnalysisString.lvlint(grade);
		int turn = AnalysisString.lvltrue(grade);
		if (turn > 4 || lvl > 200) {
			JmSum.xiugaiqi();
			return;
		}
		RoleProperty property = getRoleProperty();
		property.grade.clear();
		String v = RoleUpGrade.getGradeKX().upGrade(lvl, raceid);
		String[] vs = v.split("\\|");
		for (int i = 0; i < vs.length; i++) {
			String[] a = vs[i].split("=");
			property.addgrade(a[0], Double.parseDouble(a[1]));
		}
		if (turn == 4) {
			lvl -= 140;
			lvl /= 10;
			if (raceid.intValue() == 10001) {
				property.addgrade("四抗上限", lvl * 1.2);
			} else if (raceid.intValue() == 10002) {
				property.addgrade("四抗上限", lvl * 1.1);
			} else if (raceid.intValue() == 10003) {
				property.addgrade("四抗上限", lvl * 1.0);
			} else if (raceid.intValue() == 10004) {
				property.addgrade("四抗上限", lvl * 0.9);
			}
		}
	}

	/** 重置修正抗性抗性 */
	public static String Resetborn(String[] skills, String yuben) {
		RoleProperty property = getRoleProperty();
		property.born.clear();
		String v = RoleReborn.reborn(skills, yuben);
		if (v == null || v.equals(""))
			return v;
		String[] vs = v.split("\\|");
		for (int i = 0; i < vs.length; i++) {
			String[] a = vs[i].split("=");
			property.addborn(a[0], Double.parseDouble(a[1]));
		}
		return v;
	}

	/**
	 * 重置额外抗性
	 * 
	 * @return
	 */
	public static void ResetEw() {
		RoleProperty property = getRoleProperty();
		property.additional.clear();
		Lingbao[] equipBao = RoleLingFa.getRoleLingFa().equipBao;
		for (int i = 0; i < equipBao.length; i++) {
			if (equipBao[i] != null) {
				String[] a = equipBao[i].getKangxing().split("=");
				double value = Double.parseDouble(a[1]);
				if (!a[0].startsWith("抗") || value > 10.1) {
					JmSum.xiugaiqi();
					return;
				}
				property.addadditional(a[0], value);
			}
		}
		// 获取FactionAngelJframe实例中的FactionAngelJpanel对象
		FactionAngelJpanel angelJpanel = FactionAngelJframe.getFactionAngelJframe().getFactionAngelJpanel();
		// 遍历FactionAngelJpanel对象中的faction列表
		for (int i = 0; i < angelJpanel.factionList.size(); i++) {
		    // 获取当前遍历的Faction对象
		    Faction faction = angelJpanel.factionList.get(i);
		    // 检查Faction对象的key是否为"X"或"D"
		    if (faction.getKey().equals("X")|| faction.getKey().equals("D")) {
		        // 获取Faction对象的value值
		        String value = faction.getValue();
		        // 如果value值为空或为null，则跳过当前迭代
		        if (value == null || value.isEmpty())
		            continue;
		        // 将value值按 "#" 分隔为多个字符串
		        String[] v = value.split("#");
		        // 遍历每个字符串
		        for (String s : v) {
		            // 将字符串按 "=" 分隔为键值对
		            String[] mes = s.split("=");
		            // 如果分割后的数组长度不大于1，则跳过当前迭代
		            if (mes.length <= 1) {continue;}
		            // 调用property对象的addadditional方法，传入键和解析后的双精度浮点数值
		            property.addadditional(mes[0], Double.parseDouble(mes[1]));
		        }
		    }
		}


		// 属性卡
		TimeLimit card = TimeLimit.getLimits();
		for (int i = 0; i < card.limits.size(); i++) {
			Limit limit = card.limits.get(i);
			if (limit.getType().equals("变身卡") || limit.getType().equals("强法型") || limit.getType().equals("加抗型") ||
					limit.getType().equals("增益型")|| limit.getType().equals("VIP") || limit.getType().equals("帮派") || limit.getType().equals("单人竞技场")) {
				String value = limit.getValue();
				if (value == null || value.isEmpty())
					continue;
				String[] v = value.split("\\|");
				for (int z = 0; z < v.length; z++) {
					try {
						String[] mes = v[z].split("=");
						if (mes.length <= 1){
							continue;
						}
						property.addadditional(mes[0], Double.parseDouble(mes[1]));
					} catch (Exception e) {
						// TODO: handle exception
					}
				}
			}
		}
		String[] v = RoleData.getRoleData().getPrivateData().getSkill("T");
		if (v != null) {
			for (int i = 1; i < v.length; i++) {
				String[] vs = v[i].split("_");
				int id = Integer.parseInt(vs[0]);
				int lvl = Integer.parseInt(vs[1]);
				if (id > 9100) {
					continue;
				}
				if (lvl > 5) {
					JmSum.xiugaiqi();
					return;
				}
				if (id == 9001) {
					property.addadditional("MP", lvl * 2000);
				} else if (id == 9002) {
					property.addadditional("HP", lvl * 4000);
				} else if (id == 9003) {
					property.addadditional("HP", lvl * 1000);
				} else if (id == 9004) {
					property.addadditional("根骨", lvl);
				} else if (id == 9005) {
					property.addadditional("力量", lvl);
				} else if (id == 9006) {
					property.addadditional("AP", lvl * 100);
				} else if (id == 9007) {
					property.addadditional("SP", lvl);
				} else if (id == 9008) {
					property.addadditional("抗风", lvl);
				} else if (id == 9009) {
					property.addadditional("抗火", lvl);
				} else if (id == 9010) {
					property.addadditional("抗水", lvl);
				} else if (id == 9011) {
					property.addadditional("抗雷", lvl);
				} else if (id == 9012) {
					property.addadditional("抗鬼火", lvl);
				} else if (id == 9013) {
					property.addadditional("抗三尸", lvl * 100);
				} else if (id == 9014) {
					property.addadditional("抗反震", lvl * 500);
				} else if (id == 9015) {
					property.addadditional("风法狂暴", lvl);
				} else if (id == 9016) {
					property.addadditional("火法狂暴", lvl);
				} else if (id == 9017) {
					property.addadditional("水法狂暴", lvl);
				} else if (id == 9018) {
					property.addadditional("鬼火伤害", lvl * 100);
				} else if (id == 9019) {
					property.addadditional("强力克金", lvl);
				} else if (id == 9020) {
					property.addadditional("强力克木", lvl);
				} else if (id == 9021) {
					property.addadditional("强力克火", lvl);
				} else if (id == 9022) {
					property.addadditional("强力克土", lvl);
				} else if (id == 9023) {
					property.addadditional("物理吸收", lvl);
				} else if (id == 9024) {
					property.addadditional("躲闪率", lvl);
				} else if (id == 9025) {
					property.addadditional("强震慑", lvl);
				} else if (id == 9026) {
					property.addadditional("反击率", lvl);
				} else if (id == 9027) {
					property.addadditional("反击次数", lvl);
				} else if (id == 9028) {
					property.addadditional("狂暴率", lvl);
				} else if (id == 9029) {
					property.addadditional("反震率", lvl);
				} else if (id == 9030) {
					property.addadditional("敏捷", lvl);
				} else if (id == 9031) {
					property.addadditional("恢复气血", lvl * 400);
				}
			}
		}
		LoginResult loginResult = RoleData.getRoleData().getLoginResult();
		// 称谓
		if (loginResult.getTitle() != null && !loginResult.getTitle().equals("")) {
			Title title = UserMessUntil.getTitle(loginResult.getTitle());
			if (title != null && title.getValue() != null) {
				String[] v2 = title.getValue().split("\\|");
				for (int z = 0; z < v2.length; z++) {
					String[] mes = v2[z].split("=");
					if (mes.length <= 1)
						continue;
					property.addadditional(mes[0], Double.parseDouble(mes[1]));
				}
			}
		}
		//修炼属性
        for (int i = 0; i < property.meridiansVector.size(); i++) {
            BaseMeridians meridians=property.meridiansVector.get(i);
            if (meridians.getKey().equals("抗仙法")) {
                property.addadditional("抗风", meridians.getKeyValue());
                property.addadditional("抗火", meridians.getKeyValue());
                property.addadditional("抗水", meridians.getKeyValue());
                property.addadditional("抗雷", meridians.getKeyValue());
            }else {
                property.addadditional(meridians.getKey(), meridians.getKeyValue());    
            }
        }
		int hpz = getHp(loginResult);
		if (loginResult.getHp().intValue() > hpz) {
			loginResult.setHp(new BigDecimal(hpz));
		}
		int mpz = getMp(loginResult);
		if (loginResult.getMp().intValue() > mpz) {
			loginResult.setMp(new BigDecimal(mpz));
		}
		try {
			JpanelOnJalbelBtn.testReflect(property.getQuality());
			PetAddPointMouslisten.getplayerValue();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * 重置宝宝加成
	 */
	public static void ResetBaby(Baby baby) {
		RoleProperty property = getRoleProperty();
		property.hp = 0;
		property.mp = 0;
		property.ap = 0;
		if (baby != null) {
			String Talents = baby.getTalents();
			if (Talents != null && !Talents.equals("")) {
				String[] v = Talents.split("\\|");
				for (int i = 0; i < v.length; i++) {
					String[] vs = v[i].split("=");
					int id = Integer.parseInt(vs[0]);
					if (id == 1) {
						property.ap = 0.01;
					} else if (id == 2) {
						property.hp = 0.01;
					} else if (id == 3) {
						property.mp = 0.01;
					}

				}
			}
		}
		try {
			LoginResult loginResult = RoleData.getRoleData().getLoginResult();
			int hpz = getHp(loginResult);
			if (loginResult.getHp().intValue() > hpz)
				loginResult.setHp(new BigDecimal(hpz));
			int mpz = getMp(loginResult);
			if (loginResult.getMp().intValue() > mpz)
				loginResult.setMp(new BigDecimal(mpz));
			PetAddPointMouslisten.getplayerValue();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	// 总hp
	public static int getHp(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) ((BaseValue.getRoleValue(loginResult.getRace_id(), getBone(loginResult),
				AnalysisString.lvlint(loginResult.getGrade()), 0)
				+ property.getvalue("hp") + property.getvalue("HP") + property.getvalue("加气血"))
				* (property.getvalue("HP成长") + property.hp + 1) * (property.getvalue("加强气血") / 100 + 1));
	}
	// 总mp
	public static int getMp(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) ((BaseValue.getRoleValue(loginResult.getRace_id(), getSpir(loginResult),
				AnalysisString.lvlint(loginResult.getGrade()), 1)
				+ property.getvalue("mp") + property.getvalue("MP") + property.getvalue("加魔法")) * (property
				.getvalue("加强魔法") / 100 + property.getvalue("MP成长") + property.mp + 1));
	}

	// 总ap
	public static int getAp(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) ((BaseValue.getRoleValue(loginResult.getRace_id(), getPower(loginResult),
				AnalysisString.lvlint(loginResult.getGrade()), 2)
				+ property.getvalue("AP") + property.getvalue("ap") + property.getvalue("攻击") + property
					.getvalue("加攻击")) * (property.getvalue("加强攻击") / 100 + property.getvalue("AP成长") + property.ap + 1));
	}

	// 总sp
	public static int getSp(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) ((BaseValue.getRoleValue(loginResult.getRace_id(), getSpeed(loginResult),
				AnalysisString.lvlint(loginResult.getGrade()), 3)
				+ property.getvalue("sp") + property.getvalue("SP") + property.getvalue("速度") + property
					.getvalue("加速度")) * (property.getvalue("加强速度") / 100 + property.getvalue("SP成长") + 1));
	}

	// 总cdz(禅定值)
	public static int getCdz(LoginResult loginResult) {
		return (BaseValue.getRoleValue(loginResult.getRace_id(), getCalm(loginResult),
				AnalysisString.lvlint(loginResult.getGrade()), 4));
	}

	// 总根骨
	public static int getBone(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) (loginResult.getBone() + property.getvalue("根骨"));
	}

	// 总灵性
	public static int getSpir(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) (loginResult.getSpir() + property.getvalue("灵性"));
	}

	// 总力量
	public static int getPower(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) (loginResult.getPower() + property.getvalue("力量"));
	}

	// 总敏捷
	public static int getSpeed(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) (loginResult.getSpeed() + property.getvalue("敏捷"));
	}

	// 总定力
	public static int getCalm(LoginResult loginResult) {
		RoleProperty property = getRoleProperty();
		return (int) (loginResult.getCalm() + property.getvalue("定力"));
	}

	/**
	 * 判断执行成功条件
	 */
	public static boolean contriolContinue(String v, String v2) {
		// 判断要求
		switch (v) {
		case "装备角色":
			return v2.indexOf(RoleData.getRoleData().getLoginResult().getLocalname()) != -1;
		case "力量要求":
			return RoleProperty.getPower(RoleData.getRoleData().getLoginResult()) >= Integer.valueOf(v2);
		case "灵性要求":
			return RoleProperty.getSpir(RoleData.getRoleData().getLoginResult()) >= Integer.valueOf(v2);
		case "根骨要求":
			return RoleProperty.getBone(RoleData.getRoleData().getLoginResult()) >= Integer.valueOf(v2);
		case "等级要求":
			return AnalysisString.lvlfull(RoleData.getRoleData().getLoginResult().getGrade(), v2);
		case "敏捷要求":
			return RoleProperty.getSpeed(RoleData.getRoleData().getLoginResult()) >= Integer.valueOf(v2);
		case "性别要求":
		case "性别":
			String sexBtn = Integer.valueOf(v2) == 1 ? "男" : "女";
			return RoleData.getRoleData().getLoginResult().getSex().equals(sexBtn);
		}
		return true;
	}
	/**转double*/
	public static double getdouble(String v) {
		try {
			return Double.parseDouble(v);
		} catch (Exception e) {
		
		}
		return 0;
	}
	/** 添加特技 */
	public void addStunt(int id, int suitid, int lvl, int maxsum) {
		for (int i = stunts.size() - 1; i >= 0; i--) {
			Stunt stunt = stunts.get(i);
			if (stunt.getSkillId() == id && stunt.getSuitid() == suitid) {
				stunt.addSum();
				if (lvl < stunt.getLvl()) {
					stunt.setLvl(lvl);
				}
				stunt.setMaxSum(maxsum);
				return;
			}
		}
		Stunt stunt = new Stunt(id, suitid, lvl, maxsum);
		stunt.addSum();
		stunts.add(stunt);
	}
	/** 重置特技属性 */
	public void resetStunt() {
		for (int i = stunts.size() - 1; i >= 0; i--) {
			Stunt stunt = stunts.get(i);
			stuntAffect(stunt, 1);
			stuntAffect(stunt, 0);
		}
	}
	/** 特技对属性影响 0添加特技 1移除特技 */
	public void stuntAffect(Stunt stunt, int type) {
		if (!stunt.isValid()) {
			return;
		}
		// 判断特技是否属于对属性加成
		if (!stunt.isAffect()) {
			return;
		}
		String zhi = SkillUtil.getsuitSkill(stunt.getSkillId());
		if (zhi == null) {
			return;
		}
		if (type == 0) {// 添加特技属性
			double value = SkillUtil.getSuitValue(stunt.getSkillId(), stunt.getLvl());
			if (zhi.equals("加强法术")) {
				property.addequip("加强风", value);
				property.addequip("加强雷", value);
				property.addequip("加强水", value);
				property.addequip("加强火", value);
				property.addequip("加强鬼火", value);
			} else if (zhi.equals("提抗上限")) {
				property.addequip("四抗", -value);
				property.addequip("四抗上限", value);
			} else {
				property.addequip(zhi, value);
			}
			stunt.setValue(value);
		} else {// 移除特技属性
			double value = stunt.getValue();
			if (zhi.equals("加强法术")) {
				property.addequip("加强风", -value);
				property.addequip("加强雷", -value);
				property.addequip("加强水", -value);
				property.addequip("加强火", -value);
				property.addequip("加强鬼火", -value);
			} else if (zhi.equals("提抗上限")) {
				property.addequip("四抗", value);
				property.addequip("四抗上限", -value);
			} else {
				property.addequip(zhi, -value);
			}
		}
	}

	/** 获取装备数量 */
	public int getSuitSum(String id) {
		Double value = xianzhi.get(id);
		if (value == null) {
			return 0;
		}
		return value.intValue();
	}

	public List<Stunt> getStunts() {
		return stunts;
	}

	/** 获取指定id的特技且生效 */
	public Stunt getStuntId(int id) {
		for (int i = stunts.size() - 1; i >= 0; i--) {
			if (stunts.get(i).getSkillId() == id) {
				if (stunts.get(i).isValid()) {
					return stunts.get(i);
				} else {
					return null;
				}
			}
		}
		return null;
	}
	public int getQhv() {
		return qhv;
	}
	/**修改修炼属性*/
    public BaseMeridians upMeridians(String meridians){
        if (meridians==null||meridians.equals("")) {return null;}
        BaseMeridians baseMeridiansR=null;
        String[] vs=meridians.split("\\|");
        S:for (int i = 0; i < vs.length; i++) {
            String[] vss=vs[i].split("_");
            if (vss.length!=5) { continue; }
            int bh=Integer.parseInt(vss[0]);
            for (int j = 0; j < meridiansVector.size(); j++) { 
                if (meridiansVector.get(j).getBh()==bh) {
                    baseMeridiansR=meridiansVector.get(j);
                    baseMeridiansR.setExp(Integer.parseInt(vss[1]));
                    baseMeridiansR.init(Integer.parseInt(vss[2]), vss[3], Double.parseDouble(vss[4]));
                    continue S;     
                }
            }
            baseMeridiansR=new BaseMeridians(bh, 
                    Integer.parseInt(vss[1]), Integer.parseInt(vss[2]), 
                    vss[3], Double.parseDouble(vss[4]));
            meridiansVector.add(baseMeridiansR);
        }
        return baseMeridiansR;
    }
}
