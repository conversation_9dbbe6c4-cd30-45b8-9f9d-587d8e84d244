package jxy2.guaed;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.MountJframe;
import org.come.Jpanel.MoutnModelJPanel;

import javax.swing.*;
import java.awt.*;

public class MountListJPanel extends JPanel {
    private JScrollPane jScrollPane1;// 坐骑名字滚动条
    private MountListBtn btn1;// 确定 取消
    private int id;
    public MountListJPanel() {
        this.setPreferredSize(new Dimension(265,375));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,140,265);
        getjScrollPane1();
        btn1 = new MountListBtn(ImgConstants.Btn_59, 1, "确定", this,1);
        btn1.setBounds(119, 105, 59, 25);
        this.add(btn1);
    }

    /**初始化数据*/
    public void initData(int id) {
        this.id = id;
        JList<MoutnModelJPanel> modelJPanelJList = MountJframe.getMountjframe().getMountjpanel().getModelJPanelJList();
        jScrollPane1.getViewport().setView(modelJPanelJList);
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"选择坐骑");
        Juitil.ImngBack(g, Juitil.tz21, 33-8, 55, 210, 258, 1);
        Juitil.Textdrawing(g, "坐骑列表", 46-8, 81-5, UIUtils.COLOR_Fighting, UIUtils.MSYH_HY14);

        btn1.setBounds(100, 320, 59, 25);
    }

    public JScrollPane getjScrollPane1() {
        if (jScrollPane1==null){
            // 坐骑名字列表滚动条
            jScrollPane1 = new JScrollPane(MountJframe.getMountjframe().getMountjpanel().getModelJPanelJList());
            jScrollPane1.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            jScrollPane1.getVerticalScrollBar().setUI(null);
            jScrollPane1.getVerticalScrollBar().setUnitIncrement(20);
            jScrollPane1.getViewport().setOpaque(false);
            jScrollPane1.setOpaque(false);
            jScrollPane1.setBounds(41-8, 83, 195, 225);
            jScrollPane1.setBorder(BorderFactory.createEmptyBorder());
            jScrollPane1.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            this.add(jScrollPane1);
        }
        return jScrollPane1;
    }

    public void setjScrollPane1(JScrollPane jScrollPane1) {
        this.jScrollPane1 = jScrollPane1;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}

