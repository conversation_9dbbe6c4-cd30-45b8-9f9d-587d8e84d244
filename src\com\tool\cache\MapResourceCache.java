package com.tool.cache;

import java.awt.Image;
import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 地图资源LRU缓存管理器
 */
public class MapResourceCache {
    private static final int MAX_CACHE_SIZE = 5; // 最多缓存5个地图
    
    // 地图资源数据
    public static class MapResource {
        public Image smallMapImage;
        public byte[][] mapRules;
        public String mapPath;
        public long lastAccessTime;
        
        public MapResource(Image smallMapImage, byte[][] mapRules, String mapPath) {
            this.smallMapImage = smallMapImage;
            this.mapRules = mapRules;
            this.mapPath = mapPath;
            this.lastAccessTime = System.currentTimeMillis();
        }
        
        public void cleanup() {
            if (smallMapImage != null) {
                smallMapImage.flush();
                smallMapImage = null;
            }
            mapRules = null;
            mapPath = null;
        }
        
        public void updateAccessTime() {
            this.lastAccessTime = System.currentTimeMillis();
        }
        
        public long getMemorySize() {
            long size = 0;
            if (smallMapImage != null) {
                size += smallMapImage.getWidth(null) * smallMapImage.getHeight(null) * 4; // 假设ARGB
            }
            if (mapRules != null) {
                size += mapRules.length * mapRules[0].length; // byte数组
            }
            return size;
        }
    }
    
    // LRU缓存实现
    private static final LinkedHashMap<Integer, MapResource> cache = 
        new LinkedHashMap<Integer, MapResource>(16, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<Integer, MapResource> eldest) {
                if (size() > MAX_CACHE_SIZE) {
                    System.out.println("[地图缓存] 清理旧地图资源: " + eldest.getKey());
                    eldest.getValue().cleanup();
                    return true;
                }
                return false;
            }
        };
    
    // 统计信息
    private static long cacheHits = 0;
    private static long cacheMisses = 0;
    private static long totalMemoryFreed = 0;
    
    /**
     * 获取地图资源
     */
    public static MapResource get(int mapId) {
        MapResource resource = cache.get(mapId);
        if (resource != null) {
            cacheHits++;
            resource.updateAccessTime();
            System.out.println("[地图缓存] 命中缓存: " + mapId);
            return resource;
        } else {
            cacheMisses++;
            return null;
        }
    }
    
    /**
     * 缓存地图资源
     */
    public static void put(int mapId, MapResource resource) {
        if (resource != null) {
            cache.put(mapId, resource);
            System.out.println("[地图缓存] 缓存地图资源: " + mapId + " (内存: " + (resource.getMemorySize() / 1024) + "KB)");
        }
    }
    
    /**
     * 检查是否存在缓存
     */
    public static boolean contains(int mapId) {
        return cache.containsKey(mapId);
    }
    
    /**
     * 手动清理指定地图
     */
    public static void remove(int mapId) {
        MapResource resource = cache.remove(mapId);
        if (resource != null) {
            totalMemoryFreed += resource.getMemorySize();
            resource.cleanup();
            System.out.println("[地图缓存] 手动清理地图: " + mapId);
        }
    }
    
    /**
     * 清空所有缓存
     */
    public static void clear() {
        for (MapResource resource : cache.values()) {
            totalMemoryFreed += resource.getMemorySize();
            resource.cleanup();
        }
        cache.clear();
        System.out.println("[地图缓存] 清空所有缓存");
    }
    
    /**
     * 强制清理最旧的缓存
     */
    public static void evictOldest() {
        if (!cache.isEmpty()) {
            Map.Entry<Integer, MapResource> eldest = cache.entrySet().iterator().next();
            totalMemoryFreed += eldest.getValue().getMemorySize();
            eldest.getValue().cleanup();
            cache.remove(eldest.getKey());
            System.out.println("[地图缓存] 强制清理最旧缓存: " + eldest.getKey());
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        double hitRate = (cacheHits + cacheMisses) > 0 ? (double) cacheHits / (cacheHits + cacheMisses) * 100 : 0;
        long totalMemory = 0;
        for (MapResource resource : cache.values()) {
            totalMemory += resource.getMemorySize();
        }
        
        return String.format("地图缓存统计: 大小=%d/%d, 命中=%d, 未命中=%d, 命中率=%.1f%%, 内存=%.1fMB, 已释放=%.1fMB", 
                           cache.size(), MAX_CACHE_SIZE, cacheHits, cacheMisses, hitRate, 
                           totalMemory / 1024.0 / 1024.0, totalMemoryFreed / 1024.0 / 1024.0);
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStats() {
        cacheHits = 0;
        cacheMisses = 0;
        totalMemoryFreed = 0;
    }
    
    /**
     * 获取当前缓存的地图ID列表
     */
    public static String getCachedMapIds() {
        return cache.keySet().toString();
    }
}
