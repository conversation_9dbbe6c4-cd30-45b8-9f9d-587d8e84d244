package com.tool.ModerateTask;

import com.tool.role.RoleData;
import org.come.Frame.DreamlandTrialMainJframe;
import org.come.Frame.PartnerArenaExchangeJframe;
import org.come.Frame.PartnerArenaJframe;
import org.come.Frame.PartnerArenaWarJframe;
import org.come.Jpanel.DreamlandTrialMainJpanel;
import org.come.bean.PrivateData;
import org.come.until.FormsManagement;

public class TaskRoleData {

    /** 获取任务的领取次数 1是领取次数 2是完成次数 3是最新id 4*/
    public static int SumReceive(int taskSetID, int type) {
        PrivateData data = RoleData.getRoleData().getPrivateData();
        String taskComplete = data.getTaskComplete();
        if (taskComplete == null || taskComplete.equals("")) {
            if (type==4) {return 10;}
        	return 0;
        }
        String qz = taskSetID + "-";
        String[] vs = taskComplete.split("\\|");
        for (int i = 0; i < vs.length; i++) {
            if (vs[i].startsWith(qz)) {
                String[] vss = vs[i].split("-");
                if (type==4) {
					return 10-Integer.parseInt(vss[2])+Integer.parseInt(vss[1]);
				}else if (type == 3) {
					if (vss.length == 4) {
                        return Integer.parseInt(vss[3]);
                    } else {
                        return 0;
                    }
				} else {
					return Integer.parseInt(vss[type]);
	            }
            }
        }
        if (type==4) {return 10;}
        return 0;
    }

    public static void upTaskComplete(String[] values) {
        // C修改任务记录=R=L=N1000 R 添加领取次数 L添加完成次数 N修改最新进度
        int taskSetId = Integer.parseInt(values[0]);
        int r = 0, l = 0, n = 0;
        for (int i = 1; i < values.length; i++) {
            if (values[i].equals("L")) {
                l = 1;
            } else if (values[i].equals("R")) {
                r = 1;
            } else if (values[i].startsWith("N")) {
                n = Integer.parseInt(values[i].substring(1));
            }
        }
        if (r == 0 && l == 0 && n == 0) {
            return;
        }
        PrivateData data = RoleData.getRoleData().getPrivateData();
        String taskComplete = data.getTaskComplete();
        if (taskComplete == null || taskComplete.equals("")) {
            taskComplete = taskSetId + "-" + r + "-" + l;
            if (n != 0) {
                taskComplete += "-" + n;
            }
        } else {
            String qz = taskSetId + "-";
            boolean is = true;
            StringBuffer buffer = new StringBuffer();
            String[] vs = taskComplete.split("\\|");
            for (int i = 0; i < vs.length; i++) {
                if (buffer.length() != 0) {
                    buffer.append("|");
                }
                if (vs[i].startsWith(qz)) {
                    String[] ts = vs[i].split("-");
                    buffer.append(ts[0]);
                    buffer.append("-");
                    buffer.append(Integer.parseInt(ts[1]) + r);
                    buffer.append("-");
                    buffer.append(Integer.parseInt(ts[2]) + l);
                    if (n != 0) {
                        buffer.append("-");
                        buffer.append(n);
                    } else if (ts.length == 4) {
                        buffer.append("-");
                        buffer.append(ts[3]);
                    }
                    is = false;
                } else {
                    buffer.append(vs[i]);
                }
            }
            if (is) {
                if (buffer.length() != 0) {
                    buffer.append("|");
                }
                buffer.append(taskSetId);
                buffer.append("-");
                buffer.append(r);
                buffer.append("-");
                buffer.append(l);
                if (n != 0) {
                    buffer.append("-");
                    buffer.append(n);
                }
            }
            taskComplete = buffer.toString();
        }
        data.setTaskComplete(taskComplete);

        if (FormsManagement.getInternalForm2(40) != null) {
            if (FormsManagement.getframe(40).isVisible()) {
//                ActivityJframe.getActivityJframe().getActivityJpanel().partRefreshView(taskSetId);
            }
        }
        
        if (taskSetId == 3) {//竞技挑战次数刷新
            if (FormsManagement.getInternalForm2(5) != null) {
                if (FormsManagement.getframe(5).isVisible()) {
                    PartnerArenaJframe.getPartnerArenaJframe().getPartnerArenaMainPanel().refreshWarNum();
                }
            }
        } else if (taskSetId == 4) {//竞技胜场次数刷新
            if (FormsManagement.getInternalForm2(80) != null) {
                if (FormsManagement.getframe(80).isVisible()) {
                    PartnerArenaWarJframe.getPartnerArenaWarJframe().getPartnerArenaWarPanel().refreshRecord();
                }
            }
            if (FormsManagement.getInternalForm2(107) != null) {
                if (FormsManagement.getframe(107).isVisible()) {
                    PartnerArenaExchangeJframe.getPartnerArenaExchangeJframe().getPartnerArenaExchangePanel().showView();
                }
            }
        }else if(taskSetId == 6){
            if (FormsManagement.getInternalForm2(111) != null) {
                if (FormsManagement.getframe(111).isVisible()) {
                    DreamlandTrialMainJpanel dreamlandTrialMainJpanel = DreamlandTrialMainJframe.getDreamlandTrialMainJframe().getDreamlandTrialMainJpanel();
                    dreamlandTrialMainJpanel.showLvlTier(dreamlandTrialMainJpanel.getPageNow());
                }
            }
        }
    }
    /** 添加任务领取次数 */
    // public static void addReceive(int taskID,String taskSetID){
    // PrivateData data=RoleData.getRoleData().getPrivateData();
    // String taskComplete=data.getTaskComplete();
    // if (taskComplete==null||taskComplete.equals("")) {
    // taskComplete=taskSetID+"-"+1+"-0";
    // }else {
    // String qz=taskSetID+"-";
    // String[] vs=taskComplete.split("\\|");
    // boolean is=true;
    // StringBuffer buffer=new StringBuffer();
    // for (int i = 0; i < vs.length; i++) {
    // if (buffer.length()!=0) {
    // buffer.append("|");
    // }
    // if (vs[i].startsWith(qz)) {
    // String[] ts=vs[i].split("-");
    // buffer.append(ts[0]);
    // buffer.append("-");
    // buffer.append(Integer.parseInt(ts[1])+1);
    // buffer.append("-");
    // buffer.append(ts[2]);
    // if (ts.length==4) {
    // buffer.append("-");
    // buffer.append(ts[3]);
    // }
    // is=false;
    // }else {
    // buffer.append(vs[i]);
    // }
    // }
    // if (is) {
    // if (buffer.length()!=0) {
    // buffer.append("|");
    // }
    // buffer.append(taskSetID);
    // buffer.append("-");
    // buffer.append(1);
    // buffer.append("-0");
    // }
    // taskComplete=buffer.toString();
    // }
    // data.setTaskComplete(taskComplete);
    // }
    /** 添加完成次数 */
    // public static void addComplete(int taskID,String taskSetID){
    // PrivateData data=RoleData.getRoleData().getPrivateData();
    // String taskComplete=data.getTaskComplete();
    // if (taskComplete==null||taskComplete.equals("")) {
    // if (taskID>=3157&&taskID<=3500) {
    // taskComplete=taskSetID+"-0-0-"+taskID;
    // }else {
    // taskComplete=taskSetID+"-0-"+1;
    // }
    // }else {
    // String qz=taskSetID+"-";
    // String[] vs=taskComplete.split("\\|");
    // boolean is=true;
    // StringBuffer buffer=new StringBuffer();
    // for (int i = 0; i < vs.length; i++) {
    // if (buffer.length()!=0) {
    // buffer.append("|");
    // }
    // if (vs[i].startsWith(qz)) {
    // String[] ts=vs[i].split("-");
    // buffer.append(ts[0]);
    // buffer.append("-");
    // buffer.append(ts[1]);
    // buffer.append("-");
    // if (taskID>=3157&&taskID<=3500) {
    // buffer.append(ts[2]);
    // buffer.append("-");
    // buffer.append(taskID);
    // }else {
    // buffer.append(Integer.parseInt(ts[2])+1);
    // if (ts.length==4) {
    // buffer.append("-");
    // buffer.append(ts[3]);
    // }
    // }
    // is=false;
    // }else {
    // buffer.append(vs[i]);
    // }
    // }
    // if (is) {
    // if (buffer.length()!=0) {
    // buffer.append("|");
    // }
    // buffer.append(taskSetID);
    // buffer.append("-0-");
    // if (taskID>=3157&&taskID<=3500) {
    // buffer.append("0-");
    // buffer.append(taskID);
    // }else {
    // buffer.append("1");
    // }
    // }
    // taskComplete=buffer.toString();
    // }
    // data.setTaskComplete(taskComplete);
    // }
}
