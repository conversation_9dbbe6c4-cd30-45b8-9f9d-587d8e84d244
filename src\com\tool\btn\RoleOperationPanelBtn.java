package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.role.RoleTX;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.Scene.Scene;
import come.tool.handle.HandleState;
import org.come.Frame.*;
import org.come.Jpanel.Change_titleJpanel;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.mouslisten.SurePawnMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class RoleOperationPanelBtn extends MoBanBtn {

    private Change_titleJpanel change_titleJpanel;
    public int index;
    public RoleOperationPanelBtn(String iconpath, int type, String text, Change_titleJpanel change_titleJpanel,int index) {
        super(iconpath, type, UIUtils.COLOR_BTNPUTONG);
        this.setText(text);
        setFont(UIUtils.TEXT_HY16);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.change_titleJpanel = change_titleJpanel;
    }

    public RoleOperationPanelBtn(String iconpath, int type, Color[] colors, Font font, String text) {
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (this.getText().equals("更改称谓") && FightingMixDeal.State == HandleState.USUAL) {
        	sureChangeTtile();
        } else if (this.getText().equals("隐藏称谓") && FightingMixDeal.State == HandleState.USUAL) {
            hideTtile();
        } else if (this.getText().equals("显示称谓") && FightingMixDeal.State == HandleState.USUAL) {
            hideTtile();
        } else if (this.getText().equals("给予") && FightingMixDeal.State == HandleState.USUAL) {
            /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
            GiveJframe.getGivejframe().getGivejpanel().giveMethod();
        } else if (this.getText().equals("修改密码") && FightingMixDeal.State == HandleState.USUAL) {
            changePassword();
        } else if (this.getText().equals("解  锁") && FightingMixDeal.State == HandleState.USUAL) {
            unLockPack();
        } else if (this.getText().equals("确认典当") && FightingMixDeal.State == HandleState.USUAL) {
            SurePawnMouslisten.surePawn();
        } else if (this.getText().equals("设置密码") && FightingMixDeal.State == HandleState.USUAL) {
            setPackPwd();
        } else if (this.getText().equals("投放") && FightingMixDeal.State == HandleState.USUAL) {
            //GiveJframe.getGivejframe().getGivejpanel().throwinMethod();
        }
    }
    /** 确认更改称谓 */
    public void sureChangeTtile() {
        if (Util.ditubianma == 3315 || (ImageMixDeal.scene != null && ImageMixDeal.scene.getSceneId() == Scene.DNTGID)) {
            ZhuFrame.getZhuJpanel().addPrompt("该场景不能更换称谓");
            return;
        }
        if (change_titleJpanel.getListname().getSelectedValue() != null) {
            String value = change_titleJpanel.getListname().getSelectedValue();
            String sendmes = Agreement.getAgreement().TitleChangeAgreement(value);
            SendMessageUntil.toServer(sendmes);
            change_titleJpanel.getLabnamech().setText(value);
            ImageMixDeal.userimg.getRoleShow().setTitle(value);
            RoleData.getRoleData().getLoginResult().setTitle(value);
            RoleProperty.ResetEw();
            RoleTX.getRoleTX().skin();
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择您要更改的称谓！");
        }
    }

    /** 隐藏称谓 */
    public void hideTtile() {
        // 判断是要隐藏还是显示
        if (change_titleJpanel.getBtnhide().getText().equals("隐藏称谓")) {
            Util.hideTitle = false;
            change_titleJpanel.getBtnhide().setText("显示称谓");
        } else if (change_titleJpanel.getBtnhide().getText().equals("显示称谓")) {
            Util.hideTitle = true;
            change_titleJpanel.getBtnhide().setText("隐藏称谓");
        }
        FormsManagement.HideForm(10);
    }

    /** 修改背包密码的方法 */
    public static void changePassword() {
        if (ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getOldpassword().getText()
                .equals("")
                || ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getNewpassword().getText()
                        .equals("")) {
            FrameMessageChangeJpanel.addtext(5, "新密码或者旧密码格式错误！", null, null);
        } else {
            if (RoleData
                    .getRoleData()
                    .getLoginResult()
                    .getPassword()
                    .equals(ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getOldpassword()
                            .getText())) {
                // 设置密码
                RoleData.getRoleData()
                        .getLoginResult()
                        .setPassword(
                                ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel()
                                        .getNewpassword().getText().trim());
                // 设置背包密码成功
                ZhuFrame.getZhuJpanel().addPrompt2("背包密码修改成功!!!");
                ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getOldpassword().setText("");
                ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getNewpassword().setText("");
                FormsManagement.HideForm(21);
                // 发送到服务端
                String mes = Agreement.getAgreement().rolechangeAgreement(
                        "1" + RoleData.getRoleData().getLoginResult().getPassword());
                SendMessageUntil.toServer(mes);
            } else {
                FrameMessageChangeJpanel.addtext(5, "原始密码错误", null, null);
                ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getOldpassword().setText("");
                ChangePasswordJframe.getChangePasswordJframe().getChangePasswordJpanel().getNewpassword().setText("");
                FormsManagement.HideForm(21);
            }
        }
    }

    /** 解锁 */
    public  static void unLockPack() {
        // 解锁
        if (UnLockJframe.getUnLockJframe().getUnLockJpanel().getTextUnlockpwd().equals("")) {
            // 解锁密码空
            FrameMessageChangeJpanel.addtext(5, "请输入解锁密码!！", null, null);
        } else {
            if (UnLockJframe.getUnLockJframe().getUnLockJpanel().getTextUnlockpwd().getText()
                    .equals(RoleData.getRoleData().getLoginResult().getPassword())) {
                // 密码匹配成功
                Util.canBuyOrno = true;
                FrameMessageChangeJpanel.addtext(5, "背包解锁成功！", null, null);
                UnLockJframe.getUnLockJframe().getUnLockJpanel().getTextUnlockpwd().setText("");
                ZhuFrame.getZhuJpanel().addPrompt("解锁成功！！！");
                FormsManagement.HideForm(33);
                TestpackJframe.getTestpackJframe().getTestpackJapnel().getArrange().setText("加锁");
            } else {
                // 密码匹配失败
                FrameMessageChangeJpanel.addtext(5, "背包解锁密码错误！！", null, null);
                UnLockJframe.getUnLockJframe().getUnLockJpanel().getTextUnlockpwd().setText("");
                ZhuFrame.getZhuJpanel().addPrompt("密码错误！！！");
                FormsManagement.HideForm(33);
            }
        }
    }

    /** 设置密码的方法 */
    public void setPackPwd() {
        if (!SetPassJfram.getSetPassJframe().getSetPasswordJpanel().getPassAreaText().getText().equals("")) {
            // 设置按钮为修改密码
            TestpackJframe.getTestpackJframe().getTestpackJapnel().getBtnchangepwd().setText("修改密码");
            // 面板重新绘制
            TestpackJframe.getTestpackJframe().getTestpackJapnel().repaint();
            // 设置背包密码
            RoleData.getRoleData()
                    .getLoginResult()
                    .setPassword(
                            SetPassJfram.getSetPassJframe().getSetPasswordJpanel().getPassAreaText().getText().trim());
            // 提示设置成功
            ZhuFrame.getZhuJpanel().addPrompt2("密码设置成功!!!");
            // 关闭这个窗口
            FormsManagement.HideForm(32);
            // 发送到服务端
            String mes = Agreement.getAgreement().rolechangeAgreement(
                    "1" + RoleData.getRoleData().getLoginResult().getPassword());
            SendMessageUntil.toServer(mes);
        }
    }
}
