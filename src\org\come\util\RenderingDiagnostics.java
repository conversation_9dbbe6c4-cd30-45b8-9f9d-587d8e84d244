package org.come.util;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 渲染诊断工具
 * 帮助诊断渲染循环问题
 */
public class RenderingDiagnostics {
    
    private static RenderingDiagnostics instance;
    private Timer diagnosticTimer;
    private long lastPaintTime = 0;
    private int paintCallCount = 0;
    private boolean isMonitoring = false;
    
    private RenderingDiagnostics() {}
    
    public static RenderingDiagnostics getInstance() {
        if (instance == null) {
            instance = new RenderingDiagnostics();
        }
        return instance;
    }
    
    /**
     * 开始监控渲染调用
     */
    public void startMonitoring() {
        if (isMonitoring) return;
        
        isMonitoring = true;
        paintCallCount = 0;
        lastPaintTime = System.currentTimeMillis();

        
        diagnosticTimer = new Timer(2000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                reportRenderingStats();
            }
        });
        diagnosticTimer.start();
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) return;
        
        isMonitoring = false;
        if (diagnosticTimer != null) {
            diagnosticTimer.stop();
        }
    }
    
    /**
     * 记录一次paint调用
     */
    public void recordPaintCall() {
        if (!isMonitoring) return;
        
        paintCallCount++;
        lastPaintTime = System.currentTimeMillis();
    }
    
    /**
     * 报告渲染统计
     */
    private void reportRenderingStats() {
        // 重置计数器
        paintCallCount = 0;
    }
    
    /**
     * 获取当前渲染状态
     */
    public String getRenderingStatus() {
        if (!isMonitoring) {
            return "未监控";
        }
        
        long timeSinceLastPaint = System.currentTimeMillis() - lastPaintTime;
        return String.format("调用次数: %d, 上次paint: %dms前", paintCallCount, timeSinceLastPaint);
    }
}
