package come.tool.Scene;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;
import java.math.BigDecimal;

import javax.swing.ImageIcon;

import org.come.strength.JframeStrengthMain;
import org.come.until.FormsManagement;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import com.tool.tcpimg.UIUtils;

public class DNTGScene implements Scene{
	//所属阵营
	private int camp;
	//大闹金币
	private BigDecimal DN_JB;
	//大闹积分
	private BigDecimal DN_JF;
	//阵营科技值
	private String DN_TT_KJ;
	private String DN_HGS_KJ;
	
	//神力加持
	private String DN_SLs;
	//活动倒计时
	private CountDown countDown;
	//女武神试炼排行榜
    private DNTGNV DNTGNv;
	//上古战场横幅战况
    private DNTGSG DNTGSg;
    
    private Image image;
    private boolean is1;//是否显示   积分榜 
	private boolean is2;//是否显示  女武神排行榜
    private Image XZ;//向左
    private Image XY;//向右
    
	public DNTGScene(String[] vs) {
		// TODO Auto-generated constructor stub
		this.is1=true;
		this.is2=true;
		this.image=new ImageIcon("inkImg/background/S23.png").getImage();
		this.XZ=new ImageIcon("inkImg/button/B27.png").getImage();
		this.XY=new ImageIcon("inkImg/button/B28.png").getImage();
		this.DN_JB=new BigDecimal(0);
		this.DN_JF=new BigDecimal(0);
		this.DN_TT_KJ="0";
		this.DN_HGS_KJ="0";
		UPData(vs);		
	}
	@Override
	public void draw(Graphics g2, long DieTime) {
		// TODO Auto-generated method stub
		for (int i = ImageMixDeal.mapMonsterlist.size()-1; i >=0 ; i--) {
			ImageMixDeal.mapMonsterlist.get(i).move2(DieTime);
			ImageMixDeal.mapMonsterlist.get(i).Drawing(g2,DieTime);
		}		
		for (ManimgAttribute value : ImageMixDeal.Playerimgmap.values()) {
			value.Drawing(g2,DieTime);
		}
		for (int i = ImageMixDeal.npcimglist.size()-1; i >=0 ; i--) {
			ImageMixDeal.npcimglist.get(i).Drawing(g2,DieTime);
		}
		g2.setFont(UIUtils.TEXT_MSG);
		g2.setColor(Color.CYAN);	
		ImageMixDeal.userimg.Drawing(g2,DieTime);
		ImageMixDeal.userimg.draweffects(g2);
		if (is1) {
			g2.drawImage(image,0,120,null);
			Util.drawPrice(g2, DN_JB, 25,253);
			Util.drawPrice(g2, DN_JF, 25,303);
			g2.setColor(Color.white);
			g2.drawString(DN_HGS_KJ,  90,377);
			g2.drawString(DN_TT_KJ, 90,454);
			g2.drawImage(XZ ,160,180,null);
		}else {
			g2.drawImage(XY,0,180,null);
		}
		if (countDown!=null) {
			countDown.draw(g2);
		}
		if (DNTGNv!=null) {
			if (is2) {
				DNTGNv.draw(g2);
				g2.drawImage(XY ,ScrenceUntil.Screen_x-DNTGNv.getImage().getWidth(null)+10,190,null);
			}else {
				g2.drawImage(XZ ,ScrenceUntil.Screen_x-20,190,null);
			}
		}else if (DNTGSg!=null) {
			DNTGSg.draw(g2);
		}
	}

	@Override
	public boolean Monitor(int x, int y) {
		// TODO Auto-generated method stub
//		is1=!is1;
		x-=Util.mapmodel.getShot_x();
		y-=Util.mapmodel.getShot_y();
		if (is1) {
			if (x>=160&&x<=180&&y>=180&&y<=205) {
				is1=!is1;
				return true;
			}
		}else {
			if (x>=0&&x<=20&&y>=180&&y<=205) {
				is1=!is1;
				return true;
			}
		}
		if (DNTGNv!=null) {
			if (is2) {
				if (y>=190&&y<=215&&x>=ScrenceUntil.Screen_x-DNTGNv.getImage().getWidth(null)+10
						&&x<=ScrenceUntil.Screen_x-DNTGNv.getImage().getWidth(null)+30) {
					is2=!is2;
					return true;
				}
			}else {
				if (x>=ScrenceUntil.Screen_x-20&&x<=ScrenceUntil.Screen_x&&y>=190&&y<=215) {
					is2=!is2;
					return true;
				}
			}	
		}
		return false;					
	}
	@Override
	public void UPData(String[] vs) {
		// TODO Auto-generated method stub
//		C 所属阵营
//		M 金币
//		J 积分
//		K 科技值
//		L 神力加持数据
//		D 倒计时数据
//		N 女武神试炼数据
//		S 上古数据
//		"!", "|", "*", "&", "@", "#", "$", "%", "^", "/" 
//		|  &   $
//		SceneID|C0|M123|J123|LID$LVL&ID$LVL|DTYPE&TIME|N2(玩家自身数据)击杀次数$名次|N0/1(阵营排行榜)名称$击杀次数&名称$击杀次数|S0天庭进度|S1花果山进度|S2结束时间&进度上限
		for (int i = 1; i < vs.length; i++) {
			if (vs[i].startsWith("M")) {
				this.DN_JB=new BigDecimal(vs[i].substring(1));
			}else if (vs[i].startsWith("J")) {
				this.DN_JF=new BigDecimal(vs[i].substring(1));
			}else if (vs[i].startsWith("K")) {
				if (vs[i].startsWith("K0")) {
					this.DN_TT_KJ=vs[i].substring(2);
				}else {
					this.DN_HGS_KJ=vs[i].substring(2);
				}
			}else if (vs[i].startsWith("N")) {
				if (vs[i].length()==1) {
					DNTGNv=null;
				}else {
					if (DNTGNv==null) {
						DNTGNv=new DNTGNV();
					}
					if (vs[i].startsWith("N0")) {
						DNTGNv.upRanking(0, vs[i].substring(2));
					}else if (vs[i].startsWith("N1")) {
						DNTGNv.upRanking(1, vs[i].substring(2));
					}else {
						String[] v=vs[i].substring(2).split("\\$");
						DNTGNv.upRole(camp, Integer.parseInt(v[0]), Integer.parseInt(v[1]));
					}
				}
			}else if (vs[i].startsWith("S")) {
				if (vs[i].length()==1) {
					DNTGSg=null;
				}else {
					if (DNTGSg==null) {
						DNTGSg=new DNTGSG();
					}
					if (vs[i].startsWith("S0")) {
						DNTGSg.upValue(0, Integer.parseInt(vs[i].substring(2)));
					}else if (vs[i].startsWith("S1")) {
						DNTGSg.upValue(1, Integer.parseInt(vs[i].substring(2)));
					}else {
						DNTGSg.upSG(vs[i].substring(2));
					}
				}
			}else if (vs[i].startsWith("D")) {
				if (vs[i].length()==1) {
					countDown=null;
				}else {
					String[] v=vs[i].substring(1).split("\\$");
					countDown=new CountDown(Integer.parseInt(v[0]), Long.parseLong(v[1]));
				}
			}else if (vs[i].startsWith("C")) {
				this.camp=Integer.parseInt(vs[i].substring(1));
			}else if (vs[i].startsWith("L")) {
				this.DN_SLs=vs[i].substring(1);
				JframeStrengthMain.getJframeStrengthMain().getJpanelStrengthMain().refreshSkills(DN_SLs);
			}
		}
	}
	public void showSL(){
	    JframeStrengthMain.getJframeStrengthMain().getJpanelStrengthMain().refreshSkills(DN_SLs);
	    FormsManagement.showForm(98);	
		
	}
	@Override
	public int getSceneId() {
		// TODO Auto-generated method stub
		return Scene.DNTGID;
	}
	@Override
	public void end() {
		// TODO Auto-generated method stub
		
	}
	public int getCamp() {
		return camp;
	}

	public void setCamp(int camp) {
		this.camp = camp;
	}
	public BigDecimal getDN_JB() {
		return DN_JB;
	}
	public void setDN_JB(BigDecimal dN_JB) {
		DN_JB = dN_JB;
	}
	public BigDecimal getDN_JF() {
		return DN_JF;
	}
	public void setDN_JF(BigDecimal dN_JF) {
		DN_JF = dN_JF;
	}
}
