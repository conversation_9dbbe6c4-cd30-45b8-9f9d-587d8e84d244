package jxy2.Xy2oView;

import com.tool.btn.GoodAndPetLockBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class PackMainJPanel extends JPanel {
    private PackCourtCardJPanel cardJPanel;
    public GoodAndPetLockBtn[] vnavi = new GoodAndPetLockBtn[3];//右侧导航

    public PackMainJPanel() {
        setPreferredSize(new Dimension(403, 496));
        setOpaque(false);
        setLayout(null);
        Juitil.addClosingButtonToPanel(this,2,377);
        cardJPanel = new PackCourtCardJPanel();
        cardJPanel.setBounds(0, 0, 403, 496);
        add(cardJPanel);

        String[] name = {"背包","境界","仙翼"};
        // 单个物品解锁按钮
        for (int i = 0; i < vnavi.length; i++) {
            vnavi[i] = new GoodAndPetLockBtn(ImgConstants.vnavi, 1, 6+i,name[i],UIUtils.COLOR_BTNTEXT,this,i);
            if (i==0){
                vnavi[i].btnchange(0);
            }else {
                vnavi[i].btnchange(2);
            }
            vnavi[i].setBounds(369, 35+i*49, 30, 48);
            this.add(vnavi[i]);
        }


    }
    public String titie = "背包";
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth()-25,getHeight(),titie);

    }

    public PackCourtCardJPanel getCardJPanel() {
        return cardJPanel;
    }

    public void setCardJPanel(PackCourtCardJPanel cardJPanel) {
        this.cardJPanel = cardJPanel;
    }
}
