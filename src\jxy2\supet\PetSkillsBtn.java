package jxy2.supet;

import com.tool.btn.MoBanBtn;
import org.come.Frame.MsgJframe;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.Jpanel.WuLingJPanel;
import org.come.entity.RoleSummoning;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class PetSkillsBtn extends MoBanBtn implements MouseListener {
    public int index;
    public PetSkillsJpanel petSkillsJpanel;
    public WuLingJPanel wuLingJPanel;
    public JupoDanJPanel jupoDanJPanel;
    public PetSkillsBtn(String iconpath, int type, int index,PetSkillsJpanel petSkillsJpanel,String ping) {
        super(iconpath, 0,type);
        this.index = index;
        this.petSkillsJpanel = petSkillsJpanel;
    }
    public PetSkillsBtn(String iconpath, int type, int index,WuLingJPanel wuLingJPanel) {
        super(iconpath, 0,type);
        this.index = index;
        this.wuLingJPanel = wuLingJPanel;
    }
    public PetSkillsBtn(String iconpath, int type, int index,JupoDanJPanel jupoDanJPanel) {
        super(iconpath, 0,type);
        this.index = index;
        this.jupoDanJPanel = jupoDanJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // TODO Auto-generated method stub
        if(getZhen()==1)return;
        if (index>=10&&index<=18){
            int nx = Util.SwitchUI==1?31:6;
            int ny = Util.SwitchUI==1?-19:-10;
            int x = wuLingJPanel.getPetSkillsJpanel().positions[index-10][0]+nx;
            int y = wuLingJPanel.getPetSkillsJpanel().positions[index-10][1]+ny;
            int zhen =wuLingJPanel.getPetSkillsBtns()[index-10].getZhen()==0?0:2;
            Image image =  wuLingJPanel.getPetSkillsBtns()[index-10].getIcons()[zhen].getImage().getScaledInstance(42, 42, Image.SCALE_SMOOTH);
            wuLingJPanel.getPetSkillsBtns()[index-10].setIcon(new ImageIcon(image));
            wuLingJPanel.getPetSkillsBtns()[index-10].setBounds(x+144, y-77, 42, 42);
        }else if (index>=20&&index<=28){
            int nx = Util.SwitchUI==1?25:0;
            int ny = Util.SwitchUI==1?-14:0;
            int x = jupoDanJPanel.getPetSkillsJpanel().positions[index-20][0]+nx;
            int y = jupoDanJPanel.getPetSkillsJpanel().positions[index-20][1]+ny;
            int zhen =jupoDanJPanel.getPetSkillsBtns()[index-20].getZhen()==0?0:2;
            Image image =  jupoDanJPanel.getPetSkillsBtns()[index-20].getIcons()[zhen].getImage().getScaledInstance(42, 42, Image.SCALE_SMOOTH);
            jupoDanJPanel.getPetSkillsBtns()[index-20].setIcon(new ImageIcon(image));
            jupoDanJPanel.getPetSkillsBtns()[index-20].setBounds(x+148, y-88, 42, 42);
        }else {
            int nx = Util.SwitchUI==1?26:0;
            int ny = Util.SwitchUI==1?-18:0;
            int x = petSkillsJpanel.positions[index][0]; // 获取X坐标
            int y = petSkillsJpanel.positions[index][1]; // 获取Y坐标
            int zhen =petSkillsJpanel.getPetSkillsBtns()[index].getZhen()==0?0:2;
            Image image =  petSkillsJpanel.getPetSkillsBtns()[index].getIcons()[zhen].getImage().getScaledInstance(42, 42, Image.SCALE_SMOOTH);
            petSkillsJpanel.getPetSkillsBtns()[index].setIcon(new ImageIcon(image));
            petSkillsJpanel.getPetSkillsBtns()[index].setBounds(nx+x-19, ny+y+22, 42, 42);
        }

    }
    @Override
    public void mouseReleased(MouseEvent e) {
        if(getZhen()==1)return;

        if (index>=10&&index<=18){
            int nx = Util.SwitchUI==1?31:6;
            int ny = Util.SwitchUI==1?-19:-10;
            int x = wuLingJPanel.getPetSkillsJpanel().positions[index-10][0]+nx;
            int y = wuLingJPanel.getPetSkillsJpanel().positions[index-10][1]+ny;
            int zhen =wuLingJPanel.getPetSkillsBtns()[index-10].getZhen()==0?0:2;
            Image image =  wuLingJPanel.getPetSkillsBtns()[index-10].getIcons()[zhen].getImage().getScaledInstance(50, 50, Image.SCALE_SMOOTH);
            wuLingJPanel.getPetSkillsBtns()[index-10].setIcon(new ImageIcon(image));
            wuLingJPanel.getPetSkillsBtns()[index-10].setBounds(x+140, y-82, 50, 50);
        }else if (index>=20&&index<=28){
            int nx = Util.SwitchUI==1?25:0;
            int ny = Util.SwitchUI==1?-14:0;
            int x = jupoDanJPanel.getPetSkillsJpanel().positions[index-20][0]+nx;
            int y = jupoDanJPanel.getPetSkillsJpanel().positions[index-20][1]+ny;
            int zhen =jupoDanJPanel.getPetSkillsBtns()[index-20].getZhen()==0?0:2;
            Image image =  jupoDanJPanel.getPetSkillsBtns()[index-20].getIcons()[zhen].getImage().getScaledInstance(50, 50, Image.SCALE_SMOOTH);
            jupoDanJPanel.getPetSkillsBtns()[index-20].setIcon(new ImageIcon(image));
            jupoDanJPanel.getPetSkillsBtns()[index-20].setBounds(x+144, y-92, 50, 50);
        }else {
            // TODO Auto-generated method stub
            int nx = Util.SwitchUI==1?26:0;
            int ny = Util.SwitchUI==1?-18:0;
            int x = petSkillsJpanel.positions[index][0]; // 获取X坐标
            int y = petSkillsJpanel.positions[index][1]; // 获取Y坐标
            int zhen =petSkillsJpanel.getPetSkillsBtns()[index].getZhen()==0?0:2;
            Image image = petSkillsJpanel.getPetSkillsBtns()[index].getIcons()[zhen].getImage().getScaledInstance(50, 50, Image.SCALE_SMOOTH);
            petSkillsJpanel.getPetSkillsBtns()[index].setIcon(new ImageIcon(image));
            petSkillsJpanel.getPetSkillsBtns()[index].setBounds(nx+x - 24, ny+y + 19, 50, 50);
        }
    }
    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (btn!=1) {btnchange(0);}
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        StringBuffer buffer = new StringBuffer();
        if (pet!=null){
            if (getZhen()==0) {
                buffer.append(" 技能格（封印）#r");
                buffer.append(" 解封技能格方式：#r");
                buffer.append(" 1.明雷战斗可随机开启；#r");
                buffer.append(" 2.地宫积分开启；#r");
                buffer.append(" 3.使用一定数量的渡玄丹开启；#r");
                MsgJframe.getJframe().getJapnel().Explanation(buffer.toString(),220);
            }else if (getZhen()==2) {
                buffer.append(" #R技能格（未获得）#r#Y");
                buffer.append(" 获得技能格方式：#r");
                buffer.append(" 1、召唤兽点化后，每次升级有几率获得（封印状态）；#r");
                buffer.append(" 2、召唤兽点化后，使用提炼过的聚魄丹时有几率获得；#r");
                buffer.append(" 3、召唤兽启灵时，有几率获得（封印状态）；#r");
                buffer.append(" 4、在木息（蓬莱瀛洲，52，42）处使用积分和金钱兑换有几率获得（封印状态）；#r");
                buffer.append(" 5、召唤兽点化后使用一定数量的渡玄丹可获得；#r");
                MsgJframe.getJframe().getJapnel().Explanation(buffer.toString(),310);
            }else {
                if (petSkillsJpanel==null)return;
                if (petSkillsJpanel.getLabPetskills()[index].getIcon()!=null) {
                    petSkillsJpanel.getPetSkillsBtns()[index].setVisible(false);
                }else {
                    petSkillsJpanel.getPetSkillsBtns()[index].setVisible(true);
                    buffer.append(" 技能格（已解封）#r");
                    buffer.append(" 领悟技能条件：#r");
                    buffer.append(" 1.点击右上角领悟图标可随机领悟技能；#r");
                    buffer.append(" 2.明雷战斗可随机获得；#r");
                    buffer.append(" 3.购买技能书获得；#r");
                    buffer.append(" 4.使用聚魄丹获得；#r");
                    MsgJframe.getJframe().getJapnel().Explanation(buffer.toString(), 220);
                }


            }

        }

    }
    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }
}
