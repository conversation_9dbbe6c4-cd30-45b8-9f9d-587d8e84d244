package jxy2.roleRelated;

import org.come.Frame.ZhuFrame;
import org.come.entity.RoleSummoning;
import org.come.until.AnalysisString;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class PetPointMouslisten  implements MouseListener {

    private int type,index;
    public PetPointMouslisten(int type,int index) {
        super();
        this.type = type;
        this.index = index;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        point(type,(e.isShiftDown()?10:1)*(type%2==0?-1:1));
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    /**
     * 加点
     */
    public void point(int type,int point){
        petpoint(UserMessUntil.getChosePetMes(), type, point);
    }
    /**
     * 召唤兽加点
     */
    public void petpoint(RoleSummoning pet, int type, int point){
        if (pet==null) {
            ZhuFrame.getZhuJpanel().addPrompt2("没有选中的召唤兽");
            return;
        }
        type=type/2;
        PetSwitchJPanel jpanel= PetSwitchFrame.getPetSwitchFrame().getPetSwitchJPanel();
        point=addpoint(getdz(type, pet), jpanel.getdian(type,index),jpanel.getdian(5,index), point);
        if (point==0){
            ZhuFrame.getZhuJpanel().addPrompt2("已无法改变点数");
            return;
        }
        jpanel.adddian(type, point,index);
    }


    public int addpoint(int d,int z,int f,int point){
        if (d>z+point) point=d-z;
        if (f-point<0) point=f;
        return point;
    }

    public int getdz(int type, RoleSummoning pet){
        if (pet.getSpoint()==null){
            int gg = pet.getBone() - (pet.getBone()- AnalysisString.petLvlint(pet.getGrade()));
            int lx = pet.getSpir() - (pet.getSpir()-AnalysisString.petLvlint(pet.getGrade()));
            int ll = pet.getPower() - (pet.getPower()-AnalysisString.petLvlint(pet.getGrade()));
            int mj = pet.getSpeed() - (pet.getSpeed()-AnalysisString.petLvlint(pet.getGrade()));
            int dl = pet.getCalm() - (pet.getCalm()- AnalysisString.petLvlint(pet.getGrade()));
            if (type==0) {
                type= gg;
            }else if (type==1) {
                type=lx;
            }else if (type==2) {
                type=ll;
            }else if (type==3) {
                type=mj;
            }else if (type==4) {
                type=dl;
            }
        }else {
            String[] str = pet.getSpoint().split("\\|");
            for (String string : str) {
                String[] vs = string.split("&")[0].split("=");
                int  gg = Integer.parseInt(vs[0].split("_")[3]);//根骨
                int  lx = Integer.parseInt(vs[1]);//灵力
                int  ll = Integer.parseInt(vs[2]);//力量
                int  mj = Integer.parseInt(vs[3]);//敏捷
                int  dl = Integer.parseInt(vs[4]);//定力
                if (index == 0 && string.startsWith("D_Y") || index == 1 && string.startsWith("D_R") || index==2&&string.startsWith("D_S")){
                    if (type==0) {
                        type= gg;
                    }else if (type==1) {
                        type=lx;
                    }else if (type==2) {
                        type=ll;
                    }else if (type==3) {
                        type=mj;
                    }else if (type==4) {
                        type=dl;
                    }
                }

            }
        }



        return type;
    }
}
