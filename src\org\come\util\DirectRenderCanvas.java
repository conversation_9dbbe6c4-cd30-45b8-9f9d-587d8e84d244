package org.come.util;

import java.awt.*;
import java.awt.image.BufferStrategy;
import javax.swing.JFrame;

/**
 * 直接渲染Canvas - 绕过Swing重绘限制
 */
public class DirectRenderCanvas extends Canvas {
    
    private BufferStrategy bufferStrategy;
    private volatile boolean isActive = true;
    
    // 渲染回调接口
    public interface RenderCallback {
        void render(Graphics2D g2d);
    }
    
    private RenderCallback renderCallback;
    
    public DirectRenderCanvas() {
        setIgnoreRepaint(true); // 忽略系统重绘，我们自己控制
        setBackground(Color.BLACK);
    }
    
    /**
     * 初始化缓冲策略
     */
    public void initBufferStrategy() {
        createBufferStrategy(2); // 双缓冲
        bufferStrategy = getBufferStrategy();
        System.out.println("[DirectCanvas] 缓冲策略已初始化");
    }
    
    /**
     * 设置渲染回调
     */
    public void setRenderCallback(RenderCallback callback) {
        this.renderCallback = callback;
    }
    
    /**
     * 直接渲染 - 绕过Swing重绘系统
     */
    public void directRender() {
        if (!isActive || bufferStrategy == null || renderCallback == null) {
            return;
        }
        
        try {
            // 获取绘制图形上下文
            Graphics g = bufferStrategy.getDrawGraphics();
            Graphics2D g2d = (Graphics2D) g;
            
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
            
            // 清空画布
            g2d.setColor(getBackground());
            g2d.fillRect(0, 0, getWidth(), getHeight());
            
            // 调用渲染回调
            renderCallback.render(g2d);
            
            // 释放图形上下文
            g.dispose();
            
            // 显示缓冲区内容
            if (!bufferStrategy.contentsLost()) {
                bufferStrategy.show();
            }
            
            // 同步显示
            Toolkit.getDefaultToolkit().sync();
            
        } catch (Exception e) {
            System.err.println("[DirectCanvas] 渲染异常: " + e.getMessage());
        }
    }
    
    /**
     * 设置活跃状态
     */
    public void setActive(boolean active) {
        this.isActive = active;
    }
    
    /**
     * 是否活跃
     */
    public boolean isActive() {
        return isActive;
    }
}
