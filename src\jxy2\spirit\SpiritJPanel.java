package jxy2.spirit;

import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
/**
* 神魂淬炼
* <AUTHOR>
* @date 2024/4/29 上午7:49
*/
public class SpiritJPanel extends JPanel {
    public SpiritJPanel() {
        setPreferredSize(new Dimension(600, 496));
        setOpaque(false);
        setLayout(null);
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, 759, 496, 1);
    }
}
