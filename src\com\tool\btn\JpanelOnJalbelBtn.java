package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.roleRelated.RoleSwitchFrame;
import jxy2.roleRelated.RoleSwitchJPanel;
import jxy2.xbao.RoleXuanbao;
import org.come.Frame.*;
import org.come.Jpanel.GoodsMsgJpanel;
import org.come.Jpanel.TeststateJpanel;
import org.come.Jpanel.TesttaskJapnel;
import org.come.bean.LoginResult;
import org.come.bean.RoleShow;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;
import org.come.until.Util;
import org.skill.frame.SkillMainFrame;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

/**
 * <AUTHOR> 面板监听按钮类库 灵宝1、确认加点0、角色抗性2、更改称谓3、技能查看4、在线摆摊5、6修改密码 路径 ，按钮类型 ，按钮编号
 *         ，按钮名称 ，面板 ，面板 ，确认加点名称
 */
public class JpanelOnJalbelBtn extends MoBanBtn {
    // 按钮编号
    private int BtnId;
    // 人物信息面板
    private TeststateJpanel teststateJpanel;
    private TesttaskJapnel testtaskJapnel;
    private RoleSwitchJPanel roleSwitchJPanel;

    public JpanelOnJalbelBtn(String iconpath, int type, int BtnId, String labelName,
            TeststateJpanel teststateJpanel,String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.teststateJpanel = teststateJpanel;
    }

    public JpanelOnJalbelBtn(String iconpath, int type, int BtnId, String labelName,
                             RoleSwitchJPanel roleSwitchJPanel,String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.roleSwitchJPanel = roleSwitchJPanel;
    }

    public JpanelOnJalbelBtn(String iconpath, int type, String text, int BtnId,String prowpt,RoleSwitchJPanel roleSwitchJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0,UIUtils.COLOR_BTNTEXT,prowpt);
        this.BtnId = BtnId;
        this.roleSwitchJPanel = roleSwitchJPanel;
        setFont(UIUtils.TEXT_FONT);
        this.setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public JpanelOnJalbelBtn(String iconpath, int type, String text, int BtnId,TeststateJpanel teststateJpanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0,UIUtils.COLOR_BTNTEXT,"");
        this.BtnId = BtnId;
        this.teststateJpanel = teststateJpanel;
        setFont(UIUtils.TEXT_FONT);
        this.setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }


    public JpanelOnJalbelBtn(String iconpath, int type, Color[] colors, int BtnId, String labelName,
            TesttaskJapnel testtaskJapnel) {
        super(iconpath, type, colors);
        this.BtnId = BtnId;
        this.testtaskJapnel = testtaskJapnel;
        this.setText(labelName);
        setFont(UIUtils.TEXT_FONT);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch (BtnId) {
        case 0:
            // 点击确认加点
            // 发送最新点数给服务器
            RoleProperty property = RoleProperty.getRoleProperty();
            LoginResult loginResult = RoleData.getRoleData().getLoginResult();
            // 根骨输入框转变
            loginResult.setBone(Integer.parseInt(teststateJpanel.getLabrootbone().getText())
                    - (int) property.getvalue("根骨"));
            // 灵性输入框
            loginResult.setSpir(Integer.parseInt(teststateJpanel.getLabintelligence().getText())
                    - (int) property.getvalue("灵性"));
            // 力量输入框
            loginResult.setPower(Integer.parseInt(teststateJpanel.getLabpower().getText())
                    - (int) property.getvalue("力量"));
            // 敏捷输入框
            loginResult.setSpeed(Integer.parseInt(teststateJpanel.getLabspeed().getText())
                    - (int) property.getvalue("敏捷"));
            if (loginResult.getTurnAround() >= 4) {
                // 定力输入框
                loginResult.setCalm(Integer.parseInt(teststateJpanel.getLabability().getText())
                        - (int) property.getvalue("定力"));
            }
            // 增加对应的属性
            PetAddPointMouslisten.getplayerValue();
            String mes = Agreement.getAgreement().rolechangeAgreement(
                    "D_M" + loginResult.getBone() + "=" + loginResult.getSpir() + "=" + loginResult.getPower() + "="
                            + loginResult.getSpeed() + "=" + loginResult.getCalm());
            SendMessageUntil.toServer(mes);
            break;




        case 1:
            // 灵宝
            FormsManagement.showForm(43);
            break;
        case 2:
            // 角色抗性
            try {
                if (ImageMixDeal.userimg.getRoleShow().getFighting() == 0 || FightingMixDeal.camp == -1) {
                    testReflect(RoleProperty.getRoleProperty().getQuality());
                } else {// 获取战斗内的抗性
                    SendMessageUntil.toServer(Agreement.getAgreement().fightQlAgreement(""));
                }
                if (!FormsManagement.getframe(8).isVisible()) {
                    FormsManagement.showForm(8);
                } else {
                    FormsManagement.HideForm(8);
                }
            } catch (Exception e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            break;
        case 4:// 技能查看
            SkillMainFrame.getSkillMainFrame().getSkillMainPanel().changeBtnPanel(0);
            FormsManagement.showForm(82);
            break;
        case 5:
            /** 判断是否解锁 */
            if (Util.isCanBuyOrno()) {
                return;
            }
            // //在线摆摊
            // //判断是否有在可以摆摊的地图上(可以摆摊的地图ID:1207、1228、1236)
            RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
            if (roleShow.getMapid() != 1236) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getX() < 5977 || roleShow.getX() > 8290) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getY() < 3099 || roleShow.getY() > 4174) {
                ZhuFrame.getZhuJpanel().addPrompt("只能在洛阳集市摆摊");
                return;
            }
            if (roleShow.getTroop_id() == null && !FormsManagement.getframe(14).isVisible()
                    && FightingMixDeal.State == HandleState.USUAL) {
                stall();
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("当前状态不能摆摊！");
            }
            break;
        case 6:
            // 修改密码
            if (this.getText().equals("修改密码")) {
                FormsManagement.showForm(21);
            } else {
                FormsManagement.showForm(32);
            }
            break;
        case 7:
            /** 判断是否解锁 */
            if (Util.isCanBuyOrno()) {
                return;
            }
            if (!FormsManagement.getframe(61).isVisible()) {
                FormsManagement.showForm(61);
            }
            break;
        case 8:
            SeventyTwoChangesJframe
                    .getSeventyTwoChangesJframe()
                    .getSeventyTwoChangesJpanel()
                    .changeMenuBtnSeventyTwoChanges(
                            SeventyTwoChangesJframe.getSeventyTwoChangesJframe().getSeventyTwoChangesJpanel()
                                    .getChooseMoneyType());
            FormsManagement.showForm(89);
            break;
        case 9:// 玄宝
            RoleXuanbao.getRoleXuanbao().choseBao = RoleXuanbao.getRoleXuanbao().equipBao[0];
            Util.StopFrame(149);
             break;
        case 10:// 取消任务
            testtaskJapnel.removeTask();
            break;
        case 11://属性一
             property = RoleProperty.getRoleProperty();
             loginResult = RoleData.getRoleData().getLoginResult();
            // 根骨输入框转变
            loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[0].getText())
                    - (int) property.getvalue("根骨"));
            // 灵性输入框
            loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[1].getText())
                    - (int) property.getvalue("灵性"));
            // 力量输入框
            loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[2].getText())
                    - (int) property.getvalue("力量"));
            // 敏捷输入框
            loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[3].getText())
                    - (int) property.getvalue("敏捷"));
            if (loginResult.getTurnAround() >= 4) {
                // 定力输入框
                loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[4].getText())
                        - (int) property.getvalue("定力"));
            }
             mes = Agreement.getAgreement().rolechangeAgreement(
                    "D_Y" + loginResult.getBone() + "=" + loginResult.getSpir() + "=" + loginResult.getPower() + "="
                            + loginResult.getSpeed() + "=" + loginResult.getCalm());
            SendMessageUntil.toServer(mes);
            break;
        case 12://属性二
            property = RoleProperty.getRoleProperty();
            loginResult = RoleData.getRoleData().getLoginResult();
            // 根骨输入框转变
            loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[0].getText())
                    - (int) property.getvalue("根骨"));
            // 灵性输入框
            loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[1].getText())
                    - (int) property.getvalue("灵性"));
            // 力量输入框
            loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[2].getText())
                    - (int) property.getvalue("力量"));
            // 敏捷输入框
            loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[3].getText())
                    - (int) property.getvalue("敏捷"));
            if (loginResult.getTurnAround() >= 4) {
                // 定力输入框
                loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[4].getText())
                        - (int) property.getvalue("定力"));
            }
            mes = Agreement.getAgreement().rolechangeAgreement(
                    "D_R" + loginResult.getBone() + "=" + loginResult.getSpir() + "=" + loginResult.getPower() + "="
                            + loginResult.getSpeed() + "=" + loginResult.getCalm());
            SendMessageUntil.toServer(mes);
            break;
        case 13://属性三
            property = RoleProperty.getRoleProperty();
            loginResult = RoleData.getRoleData().getLoginResult();
            // 根骨输入框转变
            loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[0].getText())
                    - (int) property.getvalue("根骨"));
            // 灵性输入框
            loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[1].getText())
                    - (int) property.getvalue("灵性"));
            // 力量输入框
            loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[2].getText())
                    - (int) property.getvalue("力量"));
            // 敏捷输入框
            loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[3].getText())
                    - (int) property.getvalue("敏捷"));
            if (loginResult.getTurnAround() >= 4) {
                // 定力输入框
                loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[4].getText())
                        - (int) property.getvalue("定力"));
            }
            mes = Agreement.getAgreement().rolechangeAgreement(
                    "D_S" + loginResult.getBone() + "=" + loginResult.getSpir() + "=" + loginResult.getPower() + "="
                            + loginResult.getSpeed() + "=" + loginResult.getCalm());
            SendMessageUntil.toServer(mes);
            break;
            case 14://切换属性
                if (roleSwitchJPanel.xzType!=null&&!roleSwitchJPanel.xzType.isEmpty()) {
                    //刷新切换的属性
                    Refresh(roleSwitchJPanel.xzType);
                    mes = Agreement.getAgreement().rolechangeAgreement("Q" + roleSwitchJPanel.xzType);
                    SendMessageUntil.toServer(mes);
                }
                break;

        default:
            break;
        }

    }

    public static void Refresh(String xzType) {
        RoleSwitchJPanel roleSwitchJPanel = RoleSwitchFrame.getRoleSwitchFrame().getRoleSwitchJPanel();
        RoleProperty property = RoleProperty.getRoleProperty();
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        switch (xzType){
            case "D_Y":
                for (int i = 0; i < roleSwitchJPanel.getLabnames_Y().length; i++) {
                    if (roleSwitchJPanel.getLabnames_Y()[i].getText().isEmpty()) {
                        return;
                    }
                }
                loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[0].getText())
                        - (int) property.getvalue("根骨"));
                // 灵性输入框
                loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[1].getText())
                        - (int) property.getvalue("灵性"));
                // 力量输入框
                loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[2].getText())
                        - (int) property.getvalue("力量"));
                // 敏捷输入框
                loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[3].getText())
                        - (int) property.getvalue("敏捷"));
                if (loginResult.getTurnAround() >= 4) {
                    // 定力输入框
                    loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_Y()[4].getText())
                            - (int) property.getvalue("定力"));
                }
                break;
            case "D_R":
                for (int i = 0; i < roleSwitchJPanel.getLabnames_R().length; i++) {
                    if (roleSwitchJPanel.getLabnames_R()[i].getText().isEmpty()) {
                        return;
                    }
                }
                loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[0].getText())
                        - (int) property.getvalue("根骨"));
                // 灵性输入框
                loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[1].getText())
                        - (int) property.getvalue("灵性"));
                // 力量输入框
                loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[2].getText())
                        - (int) property.getvalue("力量"));
                // 敏捷输入框
                loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[3].getText())
                        - (int) property.getvalue("敏捷"));
                if (loginResult.getTurnAround() >= 4) {
                    // 定力输入框
                    loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_R()[4].getText())
                            - (int) property.getvalue("定力"));
                }
                break;
            case "D_S":
                for (int i = 0; i < roleSwitchJPanel.getLabnames_S().length; i++) {
                    if (roleSwitchJPanel.getLabnames_S()[i].getText().isEmpty()) {
                        return;
                    }
                }
                loginResult.setBone(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[0].getText())
                        - (int) property.getvalue("根骨"));
                // 灵性输入框
                loginResult.setSpir(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[1].getText())
                        - (int) property.getvalue("灵性"));
                // 力量输入框
                loginResult.setPower(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[2].getText())
                        - (int) property.getvalue("力量"));
                // 敏捷输入框
                loginResult.setSpeed(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[3].getText())
                        - (int) property.getvalue("敏捷"));
                if (loginResult.getTurnAround() >= 4) {
                    // 定力输入框
                    loginResult.setCalm(Integer.parseInt(roleSwitchJPanel.getLabnames_S()[4].getText())
                            - (int) property.getvalue("定力"));
                }
                break;
        }
    }

    /**
     * 摆摊按钮
     */
    public static void stall() {
        if (BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getStall().getId() == 0) {
            try {
                BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnTheshelves()
                        .setIcons(CutButtonImage.cuts("inkImg/button/32.png"));
                BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnTheshelves().setBtn(1);
                BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnshelves()
                        .setIcons(CutButtonImage.cuts("inkImg/button/32.png"));
                BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnshelves().setBtn(1);
            } catch (Exception e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
            BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnStall().setText("摆摊");
        } else {
            if (ImageMixDeal.userimg.getRoleShow().getBooth_id() != null) {
                try {
                    BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnTheshelves()
                            .setIcon(CutButtonImage.getImage("inkImg/button/B62.png", -1, -1));
                    BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnTheshelves().setBtn(-1);
                    BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnshelves()
                            .setIcon(CutButtonImage.getImage("inkImg/button/B62.png", -1, -1));
                    BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnshelves().setBtn(-1);
                } catch (Exception e1) {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                }
                BoothBoxJframe.getBoothboxjframe().getBoothboxjpanel().getBtnStall().setText("收摊");
            }
        }
        TradeJframe.getTradejframe().getTradejpanel().getModelname().removeAllElements();
        if (UserMessUntil.getPetListTable() != null) {
            for (int i = 0; i < UserMessUntil.getPetListTable().size(); i++) {
                TradeJframe.getTradejframe().getTradejpanel().getModelname()
                        .add(i, UserMessUntil.getPetListTable().get(i).getSummoningname());
            }
        }
        BoothBoxJframe.getBoothboxjframe().setLocation(20, 70);
        FormsManagement.showForm(15);
        FormsManagement.showForm(16);
        FormsManagement.HideForm(0);
    }

    /**
     * @throws Exception
     */
    public static void testReflect(Object model) throws Exception {
        clearShuXingView();
        int a = 0;
        for (java.lang.reflect.Field field : model.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            if (Double.parseDouble(field.get(model).toString()) != 0) {
                String sx = null;
                sx = getQuaralyPersonalName(field.getName());
                if (sx != null && sx.length() > 2) {
                    mianBanDevide(sx.substring(sx.length() - 1, sx.length()), sx.substring(0, sx.length() - 2), field
                            .get(model).toString(), a);
                    a++;
                }
            }
        }
        changViewSize();
    }

    // 进行属性面板区分
    public static void mianBanDevide(String number, String shuxingName, String shuxingvalue, int a) {
        int valuseFroPanel = Integer.valueOf(number);
        shuXingAdd(shuxingName, shuxingvalue, valuseFroPanel, a);

    }

    /** 各个属性面板的添加 */
    public static void shuXingAdd(String shuxingName, String shuxingvalue, int number, int a) {
        if (a % 2 == 0) {
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[number - 1].getDlm()
                    .addElement(
                            shuxingName + ":" + String.format("%.1f", Double.parseDouble(shuxingvalue))
                                    + GoodsMsgJpanel.tianjia(shuxingName));
        } else {
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[number - 1].getDlm1()
                    .addElement(
                            shuxingName + ":" + String.format("%.1f", Double.parseDouble(shuxingvalue))
                                    + GoodsMsgJpanel.tianjia(shuxingName));
        }
    }

    /** 更新抗性面板大小 */
    public static void changViewSize() {
        for (int i = 0; i < RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel().length; i++) {
            int leftNum = RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i]
                    .getDlm().getSize();
            int rightNum = RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i]
                    .getDlm1().getSize();
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getListNo1()
                    .setBounds(20, 26, 130, leftNum * 17);
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getListNo2()
                    .setBounds(150, 26, 130, rightNum * 17);
            int num = leftNum > rightNum ? leftNum : rightNum;
            num = num > 0 ? (num * 17 + 34) : 24;
            int y = 0;
            for (int j = 0; j < i; j++) {
                y += RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[j].getHeight();
            }
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].setBounds(0, y, 290,
                    num);
        }
        int y = 0;
        for (int i = 0; i < RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel().length; i++) {
            y += RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getHeight();
        }
        // RoleResistanceJframe.getResistancejframe().getResistancejpanel().setBounds(RoleResistanceJframe.getResistancejframe().getResistancejpanel().getX(),RoleResistanceJframe.getResistancejframe().getResistancejpanel().getY(),290,y);
        RoleResistanceJframe.getResistancejframe().getResistancejpanel().setSize(290, y);
        RoleResistanceJframe.getResistancejframe().setSize(290, y);
    }

    /** 清空抗性面板内容 */
    public static void clearShuXingView() {
        for (int i = 0; i < RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel().length; i++) {
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getDlm()
                    .removeAllElements();
            RoleResistanceJframe.getResistancejframe().getResistancejpanel().getShuXingJpanel()[i].getDlm1()
                    .removeAllElements();
        }
    }

    /**
     * 返回的文字数据 returnName="强中毒|1" 1表示属性分类标识，总共分为5个类 分类标准： 1法术抗性
     * 抗三尸、抗中毒、抗遗忘、抗鬼火、抗混乱、抗昏睡、抗封印、抗雷、抗水、抗火、抗风、抗震慑 2法术增强 强水法、强火法、强震慑、忽视封印、忽视中毒
     * 、强风法、忽视火法、忽视混乱、忽视昏睡、忽视风法、忽视雷法、忽视水法、强中毒、强混乱、强封印、强昏睡、强雷法、忽视遗忘、忽视鬼火、
     * 风法伤害、雷法伤害
     * 、水法伤害、火法伤害、毒伤害、鬼火伤害、三尸伤害、雷法狂暴、风法狂暴、水法狂暴、火法狂暴、强鬼火、加强三尸虫、强三尸回血、鬼火狂暴
     * 、三尸虫狂暴、仙法连击率、仙法连击次数、忽视仙法抗性率、忽视仙法抗性程度、加强遗忘、加强攻击法术、加强防御法术、 3物理属性
     * 忽视防御程度、忽视防御几率、抗物理、躲闪率、反击率、反击次数、连击率、连击次数、命中率、狂暴率、致命率、忽视躲闪、忽视反击、 4五行属性
     * 强力克金、强力克木、强力克土、强力克水、强力克火、抵御强力克金、木、水、火、土 5其它属性 反震率、反震程度、中毒率、无属性伤害
     * 
     */
    public static String getQuaralyPersonalName(String mes) {
        String returnName = null;
        switch (mes) {
        case "qzds":
            returnName = "强中毒伤害|2";
            break;
        case "qzd":
            returnName = "强中毒|2";
            break;
        case "qhl":
            returnName = "强混乱|2";
            break;
        case "qfy":
            returnName = "强封印|2";
            break;
        case "qhs":
            returnName = "强昏睡|2";
            break;
        case "qlf":
            returnName = "强雷法|2";
            break;
        case "qsf":
            returnName = "强水法|2";
            break;
        case "qhf":
            returnName = "强火法|2";
            break;
        case "qzs":
            returnName = "强震慑|2";
            break;
        case "hfy":
            returnName = "忽视封印|2";
            break;
        case "hzd":
            returnName = "忽视中毒|2";
            break;
        case "qff":
            returnName = "强风法|2";
            break;
        case "hhf":
            returnName = "忽视火法|2";
            break;
        case "hhl":
            returnName = "忽视混乱|2";
            break;
        case "hhs":
            returnName = "忽视昏睡|2";
            break;
        case "hff":
            returnName = "忽视风法|2";
            break;
        case "hlf":
            returnName = "忽视雷法|2";
            break;
        case "hsf":
            returnName = "忽视水法|2";
            break;
        case "hyw":
            returnName = "忽视遗忘|2";
            break;
        case "hgh":
            returnName = "忽视鬼火|2";
            break;
        case "hzs":
            returnName = "忽视抗震慑|2";
            break;
        case "ksc":
            returnName = "抗三尸|1";
            break;
        case "hfyv":
            returnName = "忽视防御程度|3";
            break;
        case "hfyl":
            returnName = "忽视防御几率|3";
            break;
        case "kzd":
            returnName = "抗中毒|1";
            break;
        case "kzds":
            returnName = "抗毒伤|1";
            break;
        case "kyw":
            returnName = "抗遗忘|1";
            break;
        case "kgh":
            returnName = "抗鬼火|1";
            break;
        case "khl":
            returnName = "抗混乱|1";
            break;
        case "khs":
            returnName = "抗昏睡|1";
            break;
        case "kfy":
            returnName = "抗封印|1";
            break;
        case "klf":
            returnName = "抗雷|1";
            break;
        case "ksf":
            returnName = "抗水|1";
            break;
        case "khf":
            returnName = "抗火|1";
            break;
        case "kwl":
            returnName = "抗物理|3";
            break;
        case "kzs":
            returnName = "抗震慑|1";
            break;
        case "kff":
            returnName = "抗风|1";
            break;
        case "eds":
            returnName = "躲闪率|3";
            break;
        case "efjl":
            returnName = "反击率|3";
            break;
        case "efjv":
            returnName = "反击次数|3";
            break;
        case "eljl":
            returnName = "连击率|3";
            break;
        case "eljv":
            returnName = "连击次数|3";
            break;
        case "emzl":
            returnName = "命中率|3";
            break;
        case "ekbl":
            returnName = "狂暴率|3";
            break;
        case "efzl":
            returnName = "反震率|5";
            break;
        case "efzcd":
            returnName = "反震程度|5";
            break;
        case "wxj":
            returnName = "金|4";
            break;
        case "wxm":
            returnName = "木|4";
            break;
        case "wxt":
            returnName = "土|4";
            break;
        case "wxs":
            returnName = "水|4";
            break;
        case "wxh":
            returnName = "火|4";
            break;
        case "wxqj":
            returnName = "强力克金|4";
            break;
        case "wxqm":
            returnName = "强力克木|4";
            break;
        case "wxqt":
            returnName = "强力克土|4";
            break;
        case "wxqs":
            returnName = "强力克水|4";
            break;
        case "wxqh":
            returnName = "强力克火|4";
            break;
        case "swsx":
            returnName = "无属性伤害|5";
            break;
        case "sff":
            returnName = "风法伤害|2";
            break;
        case "slf":
            returnName = "雷法伤害|2";
            break;
        case "ssf":
            returnName = "水法伤害|2";
            break;
        case "shf":
            returnName = "火法伤害|2";
            break;
        case "szd":
            returnName = "毒伤害|2";
            break;
        case "sgh":
            returnName = "鬼火伤害|2";
            break;
        case "ssc":
            returnName = "三尸伤害|2";
            break;
        case "blf":
            returnName = "雷法狂暴|2";
            break;
        case "bff":
            returnName = "风法狂暴|2";
            break;
        case "bsf":
            returnName = "水法狂暴|2";
            break;
        case "bhf":
            returnName = "火法狂暴|2";
            break;
        case "qgh":
            returnName = "强鬼火|2";
            break;
        case "qsc":
            returnName = "加强三尸虫|2";
            break;
        case "qschx":
            returnName = "强三尸回血|2";
            break;
        case "ezml":
            returnName = "致命率|3";
            break;
        case "kzml":
            returnName = "抗致命率|3";
            break;
        case "bgh":
            returnName = "鬼火狂暴|2";
            break;
        case "bsc":
            returnName = "三尸虫狂暴|2";
            break;
        case "hds":
            returnName = "忽视躲闪|3";
            break;
        case "hfj":
            returnName = "忽视反击|3";
            break;
        case "exfljl":
            returnName = "仙法连击率|2";
            break;
        case "exfljs":
            returnName = "仙法连击次数|2";
            break;
        case "hxfkl":
            returnName = "忽视仙法抗性率|2";
            break;
        case "hxfcd":
            returnName = "忽视仙法抗性程度|2";
            break;
        case "qyw":
            returnName = "加强遗忘|2";
            break;
        case "qgjf":
            returnName = "加强攻击法术|2";
            break;
        case "qfyf":
            returnName = "加强防御法术|2";
            break;
        case "qsdf":
            returnName = "加强速度法术|2";
            break;
        case "qqk":
            returnName = "增加强克效果|4";
            break;
        case "kqk":
            returnName = "抵御强克效果|4";
            break;
        case "klb":
            returnName = "抗灵宝伤害|5";
            break;
        case "kwsx":
            returnName = "抗无属性伤害|5";
            break;
        case "kzshp":
            returnName = "抗震慑气血|1";
            break;
        case "kzsmp":
            returnName = "抗震慑魔法|1";
            break;
        case "qzhs":
            returnName = "对召唤兽伤害|5";
            break;
        case "kjge":
            returnName = "抗金箍|5";
            break;
        case "kqw":
            returnName = "抗情网|5";
            break;
        case "khr":
            returnName = "抗浩然正气|5";
            break;
        case "kqm":
            returnName = "抗青面獠牙|5";
            break;
        case "ktm":
            returnName = "抗天魔解体|5";
            break;
        case "kxl":
            returnName = "抗小楼夜哭|5";
            break;
        case "kfg":
            returnName = "抗分光化影|5";
            break;
        case "f_f":
            returnName = "附封攻击|5";
            break;
        case "f_h":
            returnName = "附混攻击|5";
            break;
        case "f_d":
            returnName = "附毒攻击|5";
            break;
        case "f_zs":
            returnName = "附震慑攻击|5";
            break;
        case "f_sc":
            returnName = "附三尸攻击|5";
            break;
        case "f_xf":
            returnName = "附风攻击|5";
            break;
        case "f_xh":
            returnName = "附火攻击|5";
            break;
        case "f_xs":
            returnName = "附水攻击|5";
            break;
        case "f_xl":
            returnName = "附雷攻击|5";
            break;
        case "kbf":
            returnName = "抗风法狂暴|1";
            break;
        case "kbh":
            returnName = "抗火法狂暴|1";
            break;
        case "kbs":
            returnName = "抗水法狂暴|1";
            break;
        case "kbl":
            returnName = "抗雷法狂暴|1";
            break;
        case "kbg":
            returnName = "抗鬼火狂暴|1";
            break;
        case "qmh":
            returnName = "加强魅惑|2";
            break;
        case "qjg":
            returnName = "强金箍|5";
            break;
        case "qqw":
            returnName = "强情网|5";
            break;
        case "bfy":
            returnName = "封印狂暴|2";
            break;
        case "bhl":
            returnName = "混乱狂暴|2";
            break;
        case "bhs":
            returnName = "昏睡狂暴|2";
            break;
        case "bzd":
            returnName = "毒法狂暴|2";
            break;
        case "bjf":
            returnName = "加防狂暴|2";
            break;
        case "bjg":
            returnName = "加攻狂暴|2";
            break;
        case "bzs":
            returnName = "震慑狂暴|2";
            break;
        case "byw":
            returnName = "遗忘狂暴|2";
            break;
        case "bmh":
            returnName = "魅惑狂暴|2";
            break;
        case "efsds":
            returnName = "法术躲闪|5";
            break;
        case "ejs":
            returnName = "伤害减免|5";
            break;

        case "qlpl":
            returnName = "加强霹雳效果|2";
            break;
        case "qlfy":
            returnName = "加强扶摇效果|2";
            break;
        case "qlcb":
            returnName = "加强沧波效果|2";
            break;
        case "qlglv":
            returnName = "甘霖回血值|2";
            break;
        case "qlglc":
            returnName = "甘霖回血程度|2";
            break;

        case "blfcd":
            returnName = "雷法狂暴程度|2";
            break;
        case "bffcd":
            returnName = "风法狂暴程度|2";
            break;
        case "bsfcd":
            returnName = "水法狂暴程度|2";
            break;
        case "bhfcd":
            returnName = "火法狂暴程度|2";
            break;
        case "bghcd":
            returnName = "鬼火狂暴程度|2";
            break;
        case "bsccd":
            returnName = "三尸虫狂暴程度|2";
            break;

        case "dzs":
            returnName = "震慑躲闪|5";
            break;
        case "dhf":
            returnName = "火法躲闪|5";
            break;
        case "dlf":
            returnName = "雷法躲闪|5";
            break;
        case "dff":
            returnName = "风法躲闪|5";
            break;
        case "dsf":
            returnName = "水法躲闪|5";
            break;
        case "ddf":
            returnName = "毒法躲闪|5";
            break;
        case "dfy":
            returnName = "封印躲闪|5";
            break;
        case "dhl":
            returnName = "混乱躲闪|5";
            break;
        case "dhs":
            returnName = "昏睡躲闪|5";
            break;
        case "dyw":
            returnName = "遗忘躲闪|5";
            break;
        case "dgh":
            returnName = "鬼火躲闪|5";
            break;
        case "dsc":
            returnName = "三尸虫躲闪|5";
            break;

        case "jsf":
            returnName = "水法伤害减免|5";
            break;
        case "jff":
            returnName = "风法伤害减免|5";
            break;
        case "jlf":
            returnName = "雷法伤害减免|5";
            break;
        case "jhf":
            returnName = "火法伤害减免|5";
            break;
        case "jgh":
            returnName = "鬼火伤害减免|5";
            break;
        case "kqgh":
            returnName = "抗强力克火|5";
        case "kqgl":
            returnName = "抗强力克雷|5";
        case "kqgf":
            returnName = "抗强力克风|5";
        case "kqgs":
            returnName = "抗强力克水|5";
        }
        return returnName;
    }
}
