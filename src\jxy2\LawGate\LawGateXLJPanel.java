package jxy2.LawGate;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.LoginResult;
import org.come.bean.Skill;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class LawGateXLJPanel extends JPanel {
    public String lawGateSkilled,lawGateName,lawGateSum,zhi,lawGateskillid;
    public LawGateXLBtn study;
    private List<String> lawGateSkillIdList = new ArrayList<>();
    public LawGateXLJPanel() {
        this.setPreferredSize(new Dimension(371, 384+32));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,138,371);
        study = new LawGateXLBtn(ImgConstants.tz34, 1,  1, "修 炼", this,"");
        study.setBounds(150, 340, 59, 24);
        add(study);
    }

    /**获取各项参数*/
    public void ObtainVariousParameters(Skill skill, String NAME, int sld, String zhi, String buffer, String ID){
        lawGateskillid = zhi+buffer;
        lawGateName = NAME;
        lawGateSkilled  = sld+"";
        lawGateSum = ID;
        if (!lawGateSkillIdList.contains(skill.getSkillid())) {
            lawGateSkillIdList.add(skill.getSkillid());
        }
    }
    public void updateSkillLevel( int level) {
        lawGateSkilled = level+"";
    }
    public BigDecimal money = new BigDecimal(1000000);
    public BigDecimal by = new BigDecimal(182416);
    public String[] title = {"当前法术","当前熟练","本周修炼次数","提升熟练度所需","所需经验","拥有经验","所需绑玉","拥有绑玉","拥有金钱"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"法术修炼");
        for (int i = 0; i < title.length; i++) {
            if (i == 3) {
                Juitil.TextBackground(g, title[i], 13, 45, 135, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY16);
            } else if (i > 3) {
                Juitil.TextBackground(g, title[i], 13, 45, 80 + i * 25, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY16);
            } else {
                Juitil.TextBackground(g, title[i], 13, 45, 50 + i * 23, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY16);
            }
        }

        for (int i = 0; i < 8; i++) {
            if (i>2){
                Juitil.ImngBack(g, Juitil.tz26, 160, 100+i*26, 164, 23, 1);
            }else {
                Juitil.ImngBack(g, Juitil.tz26, 160, 48+i*25, 164, 23, 1);

            }
            g.drawImage(Juitil.tz19.getImage(), 170, 140, 147, 2, null);
        }

        int cang = Integer.parseInt(lawGateSkilled);

        Juitil.TextBackground(g, lawGateName, 13, 164, 53, UIUtils.COLOR_White, UIUtils.MSYH_HY15);
        Juitil.TextBackground(g, cang>10000?10000+"/"+10000:cang+"/"+10000, 13, 164, 53+24, UIUtils.COLOR_White, UIUtils.MSYH_HY15);
        Juitil.TextBackground(g, "0/30", 13, 164, 53+24*2, UIUtils.COLOR_White, UIUtils.MSYH_HY15);
        long exp = RoleData.getRoleData().getLoginResult().getExperience().longValue();

        Util.drawPrice(g, money, 166, 195);//消耗经验
        Util.drawPrice(g, by, 166, 247);//消耗经验
        LoginResult login = RoleData.getRoleData().getLoginResult();
        Juitil.TextBackground(g, exp+"", 14, 164, 195+12, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        Util.drawPrice(g, login.getGold(), 166, 299);
        Util.drawPrice(g, login.getSavegold(), 166, 299-26);//绑玉
        Juitil.TextBackground(g, "绑玉不足时金钱消耗", 14, 166, 308, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
    }


    public String getZhi() {
        return zhi;
    }

    public void setZhi(String zhi) {
        this.zhi = zhi;
    }

    public String getLawGateskillid() {
        return lawGateskillid;
    }

    public void setLawGateskillid(String lawGateskillid) {
        this.lawGateskillid = lawGateskillid;
    }

    public String getLawGateSum() {
        return lawGateSum;
    }

    public void setLawGateSum(String lawGateSum) {
        this.lawGateSum = lawGateSum;
    }

    public List<String> getLawGateSkillIdList() {
        return lawGateSkillIdList;
    }

    public void setLawGateSkillIdList(List<String> lawGateSkillIdList) {
        this.lawGateSkillIdList = lawGateSkillIdList;
    }

    public String getLawGateSkilled() {
        return lawGateSkilled;
    }

    public void setLawGateSkilled(String lawGateSkilled) {
        this.lawGateSkilled = lawGateSkilled;
    }
}
