package jxy2.guaed.fl;


import com.sun.jna.Native;
import com.sun.jna.Structure;
import com.sun.jna.win32.StdCallLibrary;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class DisplaySettings {

    // 添加错误代码常量
    private static final int DISP_CHANGE_BADDUALVIEW = -6;
    private static final int DISP_CHANGE_BADFLAGS = -4;
    private static final int DISP_CHANGE_BADMODE = -2;
    private static final int DISP_CHANGE_BADPARAM = -5;
    private static final int DISP_CHANGE_NOTUPDATED = -3;
    private static final int DISP_CHANGE_RESTART = 1;
    private static final int DISP_CHANGE_SUCCESSFUL = 0;

    public interface User32 extends StdCallLibrary {
        User32 INSTANCE = Native.load("user32", User32.class);

        boolean EnumDisplaySettingsA(String lpszDeviceName, int iModeNum, DEVMODE lpDevMode);
        int ChangeDisplaySettingsA(DEVMODE lpDevMode, int dwFlags);

        // 枚举显示设置的常量
        int ENUM_CURRENT_SETTINGS = -1;
        int ENUM_REGISTRY_SETTINGS = -2;

        // ChangeDisplaySettings 的标志
        int CDS_UPDATEREGISTRY = 0x00000001;
        int CDS_TEST = 0x00000002;
        int CDS_FULLSCREEN = 0x00000004;
        int CDS_GLOBAL = 0x00000008;
        int CDS_SET_PRIMARY = 0x00000010;
        int CDS_RESET = 0x40000000;
        int CDS_NORESET = 0x10000000;

        // 返回值常量
        int DISP_CHANGE_SUCCESSFUL = 0;
        int DISP_CHANGE_RESTART = 1;
        int DISP_CHANGE_FAILED = -1;
        int DISP_CHANGE_BADMODE = -2;
        int DISP_CHANGE_NOTUPDATED = -3;
        int DISP_CHANGE_BADFLAGS = -4;
        int DISP_CHANGE_BADPARAM = -5;
        int DISP_CHANGE_BADDUALVIEW = -6;
    }


    // 定义DEVMODE结构
    @Structure.FieldOrder({
            "dmDeviceName", "dmSpecVersion", "dmDriverVersion", "dmSize", "dmDriverExtra",
            "dmFields", "dmOrientation", "dmPaperSize", "dmPaperLength", "dmPaperWidth",
            "dmScale", "dmCopies", "dmDefaultSource", "dmPrintQuality", "dmColor",
            "dmDuplex", "dmYResolution", "dmTTOption", "dmCollate", "dmFormName",
            "dmLogPixels", "dmBitsPerPel", "dmPelsWidth", "dmPelsHeight", "dmDisplayFlags",
            "dmDisplayFrequency"
    })
    public static class DEVMODE extends Structure {
        public byte[] dmDeviceName = new byte[32];
        public short dmSpecVersion;
        public short dmDriverVersion;
        public short dmSize;
        public short dmDriverExtra;
        public int dmFields;
        public short dmOrientation;
        public short dmPaperSize;
        public short dmPaperLength;
        public short dmPaperWidth;
        public short dmScale;
        public short dmCopies;
        public short dmDefaultSource;
        public short dmPrintQuality;
        public short dmColor;
        public short dmDuplex;
        public short dmYResolution;
        public short dmTTOption;
        public short dmCollate;
        public byte[] dmFormName = new byte[32];
        public short dmLogPixels;
        public int dmBitsPerPel;
        public int dmPelsWidth;
        public int dmPelsHeight;
        public int dmDisplayFlags;
        public int dmDisplayFrequency;

        public DEVMODE() {
            super();
            dmSize = (short) size();
        }
    }

    // 添加缺少的常量
    private static final int DM_BITSPERPEL = 0x00040000;
    private static final int DM_PELSWIDTH = 0x00080000;
    private static final int DM_PELSHEIGHT = 0x00100000;
    private static final int DM_DISPLAYFLAGS = 0x00200000;
    private static final int DM_DISPLAYFREQUENCY = 0x00400000;

    private int originalWidth;
    private int originalHeight;
    private int originalFrequency;
    private int originalBitsPerPel;  // 添加原始位深度
    private boolean isResolutionChanged = false;

    public DisplaySettings() {
        // 保存当前分辨率设置，包括位深度
        DEVMODE mode = new DEVMODE();
        User32.INSTANCE.EnumDisplaySettingsA(null, User32.ENUM_CURRENT_SETTINGS, mode);
        originalWidth = mode.dmPelsWidth;
        originalHeight = mode.dmPelsHeight;
        originalFrequency = mode.dmDisplayFrequency;
        originalBitsPerPel = mode.dmBitsPerPel;  // 保存原始位深度
    }


    /**
     * 获取所有支持的分辨率
     */
    public List<Dimension> getSupportedResolutions() {
        List<Dimension> resolutions = new ArrayList<>();
        DEVMODE dm = new DEVMODE();
        int modeNum = 0;

        while (User32.INSTANCE.EnumDisplaySettingsA(null, modeNum, dm)) {
            Dimension d = new Dimension(dm.dmPelsWidth, dm.dmPelsHeight);
            if (!resolutions.contains(d)) {
                resolutions.add(d);
                System.out.println("支持的分辨率: " + dm.dmPelsWidth + "x" + dm.dmPelsHeight +
                        " @" + dm.dmDisplayFrequency + "Hz");
            }
            modeNum++;
        }

        return resolutions;
    }
    /**
     * 查找最接近的支持分辨率
     */
    public Dimension findNearestResolution(int targetWidth, int targetHeight) {
        java.util.List<Dimension> supported = getSupportedResolutions();
        Dimension nearest = null;
        double minDiff = Double.MAX_VALUE;

        for (Dimension d : supported) {
            double diff = Math.sqrt(
                    Math.pow(d.width - targetWidth, 2) +
                            Math.pow(d.height - targetHeight, 2)
            );
            if (diff < minDiff) {
                minDiff = diff;
                nearest = d;
            }
        }

        return nearest;
    }
    /**
     * 检查是否支持指定分辨率
     */
    public boolean isResolutionSupported(int width, int height) {
        List<Dimension> supported = getSupportedResolutions();
        return supported.stream()
                .anyMatch(d -> d.width == width && d.height == height);
    }

    /**
     * 更改分辨率（改进版）
     */
    public boolean changeResolution(int width, int height) {
        DEVMODE dm = new DEVMODE();

        // 获取当前显示设置
        if (!User32.INSTANCE.EnumDisplaySettingsA(null, User32.ENUM_CURRENT_SETTINGS, dm)) {
            System.out.println("无法获取当前显示设置");
            return false;
        }

        // 保存当前设置
        int currentFrequency = dm.dmDisplayFrequency;
        System.out.println("当前显示设置: " + dm.dmPelsWidth + "x" + dm.dmPelsHeight +
                " @" + currentFrequency + "Hz " +
                dm.dmBitsPerPel + "位色深");

        // 查找最佳显示模式
        DEVMODE bestMode = findBestDisplayMode(width, height);
        if (bestMode == null) {
            System.out.println("未找到合适的显示模式");
            return false;
        }

        // 使用找到的最佳模式
        dm.dmPelsWidth = bestMode.dmPelsWidth;
        dm.dmPelsHeight = bestMode.dmPelsHeight;
        dm.dmDisplayFrequency = bestMode.dmDisplayFrequency;
        dm.dmBitsPerPel = bestMode.dmBitsPerPel;
        dm.dmFields = DM_PELSWIDTH | DM_PELSHEIGHT | DM_DISPLAYFREQUENCY | DM_BITSPERPEL;

        System.out.println("尝试切换到: " + dm.dmPelsWidth + "x" + dm.dmPelsHeight +
                " @" + dm.dmDisplayFrequency + "Hz " +
                dm.dmBitsPerPel + "位色深");

        // 应用设置
        int result = User32.INSTANCE.ChangeDisplaySettingsA(dm,
                User32.CDS_UPDATEREGISTRY | User32.CDS_FULLSCREEN);

        if (result == User32.DISP_CHANGE_SUCCESSFUL) {
            isResolutionChanged = true;
            return true;
        }

        System.out.println("更改分辨率失败，错误代码: " + result);
        return false;
    }


    /**
     * 获取错误信息
     */
    private String getErrorMessage(int code) {

        switch (code) {
            case DISP_CHANGE_SUCCESSFUL:
                return "成功 (0)";
            case DISP_CHANGE_RESTART:
                return "需要重启 (1)";
            case DISP_CHANGE_BADMODE:
                return "不支持的显示模式 (-2)";
            case DISP_CHANGE_NOTUPDATED:
                return "无法写入注册表 (-3)";
            case DISP_CHANGE_BADFLAGS:
                return "参数标志错误 (-4)";
            case DISP_CHANGE_BADPARAM:
                return "参数无效 (-5)";
            case DISP_CHANGE_BADDUALVIEW:
                return "不支持双视图 (-6)";
            default:
                return "未知错误 (" + code + ")";
        }
    }

    private DEVMODE findBestDisplayMode(int targetWidth, int targetHeight) {
        DEVMODE bestMode = null;
        DEVMODE tempMode = new DEVMODE();
        int modeNum = 0;
        int bestFrequency = 0;

        while (User32.INSTANCE.EnumDisplaySettingsA(null, modeNum, tempMode)) {
            if (tempMode.dmPelsWidth == targetWidth &&
                    tempMode.dmPelsHeight == targetHeight) {

                // 优先选择高刷新率的模式
                if (tempMode.dmDisplayFrequency > bestFrequency) {
                    bestFrequency = tempMode.dmDisplayFrequency;
                    bestMode = new DEVMODE();
                    bestMode.dmPelsWidth = tempMode.dmPelsWidth;
                    bestMode.dmPelsHeight = tempMode.dmPelsHeight;
                    bestMode.dmDisplayFrequency = tempMode.dmDisplayFrequency;
                    bestMode.dmBitsPerPel = tempMode.dmBitsPerPel;
                    bestMode.dmFields = tempMode.dmFields;
                }
            }
            modeNum++;
        }

        return bestMode;
    }


    /**
     * 恢复原始分辨率
     */
    public void restoreResolution() {
        if (isResolutionChanged) {
            DEVMODE dm = new DEVMODE();

            // 使用原始设置
            dm.dmPelsWidth = originalWidth;
            dm.dmPelsHeight = originalHeight;
            dm.dmDisplayFrequency = originalFrequency;
            dm.dmBitsPerPel = originalBitsPerPel;
            dm.dmFields = DM_PELSWIDTH | DM_PELSHEIGHT | DM_DISPLAYFREQUENCY | DM_BITSPERPEL;

            System.out.println("正在恢复原始设置: " +
                    originalWidth + "x" + originalHeight +
                    " @" + originalFrequency + "Hz " +
                    originalBitsPerPel + "位色深");

            // 应用设置
            int result = User32.INSTANCE.ChangeDisplaySettingsA(dm,
                    User32.CDS_UPDATEREGISTRY | User32.CDS_FULLSCREEN);

            if (result == User32.DISP_CHANGE_SUCCESSFUL) {
                isResolutionChanged = false;
                System.out.println("已成功恢复原始显示设置");

                // 验证是否真的恢复了
                DEVMODE verifyMode = new DEVMODE();
                User32.INSTANCE.EnumDisplaySettingsA(null, User32.ENUM_CURRENT_SETTINGS, verifyMode);
                System.out.println("验证当前设置: " +
                        verifyMode.dmPelsWidth + "x" + verifyMode.dmPelsHeight +
                        " @" + verifyMode.dmDisplayFrequency + "Hz " +
                        verifyMode.dmBitsPerPel + "位色深");
            } else {
                System.out.println("恢复显示设置失败，错误代码: " + result);
            }
        }
    }

    /**
     * 获取当前分辨率
     */
    public Dimension getCurrentResolution() {
        DEVMODE dm = new DEVMODE();
        User32.INSTANCE.EnumDisplaySettingsA(null, User32.ENUM_CURRENT_SETTINGS, dm);
        return new Dimension(dm.dmPelsWidth, dm.dmPelsHeight);
    }
}