package come.tool.JDialog;

import jxy2.refine.RefNewBtn;
import jxy2.refine.RefineFrame;
import jxy2.refine.RefineJPanel;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;

public class ConfirmDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {
        RefineJPanel refineJPanel = RefineFrame.getRefineFrame().getRefineJPanel();
        if (tishi) {
            if (object==null){
                if (RefNewBtn.timer != null && RefNewBtn.timer.isRunning()) {
                    return; // 如果计时器已经在运行，则直接返回，不创建新的计时器
                }
                if (refineJPanel.getCardJpanel().getNewEquiJpanel().getEquipment()[2].getIcon() == null) {
                    RefNewBtn.currentProgress = 0;
                    if (RefNewBtn.timer != null) {
                        RefNewBtn.timer.stop();
                    }
                    ZhuFrame.getZhuJpanel().addPrompt("#G请放入需要精炼的装备");
                    return;
                }
                RefNewBtn.ChantingExample(refineJPanel.getCardJpanel().getNewEquiJpanel());
            }else {
                String sendmes = Agreement.getAgreement().confirmAgreement(GsonUtil.getGsonUtil().getgson().toJson(object));
                SendMessageUntil.toServer(sendmes);
            }

        }
    }
}