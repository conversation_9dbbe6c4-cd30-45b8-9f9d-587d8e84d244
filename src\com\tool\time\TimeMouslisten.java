package com.tool.time;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import org.come.Frame.MsgJframe;
import org.come.until.FormsManagement;
import org.come.until.ScrenceUntil;

public class TimeMouslisten implements MouseListener{

	private Limit limit;
	
	public TimeMouslisten(Limit limit) {
		super();
		this.limit = limit;
	}

	@Override
	public void mouseClicked(MouseEvent e) {
		// TODO Auto-generated method stub
	
	}

	@Override
	public void mousePressed(MouseEvent e) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void mouseReleased(MouseEvent e) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void mouseEntered(MouseEvent e) {
		// TODO Auto-generated method stub
		MsgJframe.getJframe().getJapnel().zssxk(limit,ScrenceUntil.Screen_x-10-TimeLimit.getLimits().getlimit(limit)*25,70);
	}

	@Override
	public void mouseExited(MouseEvent e) {
		// TODO Auto-generated method stub
		FormsManagement.HideForm(46);
	}

}
