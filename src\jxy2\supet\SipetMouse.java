package jxy2.supet;

import com.tool.Document.RichDocument;
import com.tool.tcpimg.UIUtils;
import jxy2.UniversalModel;
import org.come.Frame.AlchemyJframe;
import org.come.Frame.PetSkillsJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.AlchemyJpanel;
import org.come.Jpanel.AlchemyMainJpanel;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChosePetSkillsMouslisten;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import javax.swing.*;
import javax.swing.text.BadLocationException;
import java.awt.event.MouseEvent;

public class SipetMouse extends TemplateMouseListener {
    private JList<UniversalModel> listpet;//
    private int types;//0主列表,1炼妖

    public SipetMouse(JList<UniversalModel> listpet , int types) {
        this.listpet = listpet;
        this.types = types;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (e.getButton() == MouseEvent.BUTTON1&&types==0) {// 左键点击

            if (listpet.getSelectedIndex()!=-1&&SipetJPanel.idx!=-1){
                RoleSummoning roleSummoning =  UserMessUntil.getPetListTable().get(SipetJPanel.idx);
                    SipetJPanel.newPart = roleSummoning.getPart();
                    SipetFrame.getSipetFrame().getSipetJPanel().setChosePetMes(roleSummoning);
                    UserMessUntil.setChosePetMes(roleSummoning);
                    PetAddPointMouslisten.showPetValue();
                PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().setPetskillID("");
                if (FormsManagement.getframe(18).isVisible()) {
                    ChosePetSkillsMouslisten.refreshPetSkills();
                }
                SipetFrame.getSipetFrame().getSipetJPanel().BtnIsvisible();
                if (e.isShiftDown()) {
                    try {
                        JTextField SendMes = ZhuFrame.getZhuJpanel().getSendMes();
                        ((RichDocument) SendMes.getDocument()).insertRich(SendMes.getCaretPosition(), 3,
                                roleSummoning.getSid(), "["
                                        + roleSummoning.getSummoningname() + "]", "G", null);
                    } catch (BadLocationException e1) {
                        // TODO Auto-generated catch block
                        e1.printStackTrace();
                    }
                }

            }

        }else {
            //右键炼化列表
            if (e.getButton() == MouseEvent.BUTTON3&&types==1) {
                int index = listpet.locationToIndex(e.getPoint());
                RoleSummoning roleSummoning = UserMessUntil.getPetListTable().get(index);
                if (AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getInterface() == 1) {
                    listpet.setSelectedIndex(index);
                    AlchemyJpanel.newPart = roleSummoning.getPart();
                    listpet.getModel().getElementAt(index).setTextColor(UIUtils.COLOR_NAME);
                    listpet.getModel().getElementAt(index).getLabAnswer().setVisible(true);
                    // 只高亮当前项，其它全部取消
                    for (int i = 0; i < listpet.getModel().getSize(); i++) {
                        RoleSummoning pet = UserMessUntil.getPetListTable().get(i); // 每个i都取自己的宠物
                        if (SipetJPanel.getBtnRich(pet)) {
                            // 出战宠物，特殊色
                            listpet.getModel().getElementAt(i).setTextColor(UIUtils.COLOR_CL_BACK);
                        } else if (i == index) {
                            // 右键高亮色
                            listpet.getModel().getElementAt(i).setTextColor(UIUtils.COLOR_NAME);
                        } else {
                            // 普通色
                            listpet.getModel().getElementAt(i).setTextColor(UIUtils.COLOR_goods_quantity);
                        }
                        if (i != index) {
                            listpet.getModel().getElementAt(i).getLabAnswer().setVisible(false);
                        }
                    }
                    UserMessUntil.setChosePetMes(roleSummoning);
                    PetAddPointMouslisten.ShowPet(roleSummoning);
                } else if (AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getInterface() == 4) {
                    AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyExtractJpanel().showPetModel(roleSummoning,index);
                } else if (AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getInterface() == 5) {
                    AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemyReturnJpanel().SkillReturnToSpirit(roleSummoning,index);
                }else {
                    int oype = AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemySpellJpanel().getType();
                    AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel().getCardJPanel().getAlchemySpellJpanel().initPromptAreas(oype == 1 ? roleSummoning : null, index);
                }
            }
        }


    }


    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        SipetJPanel.idx = -1;
        AlchemyMainJpanel.idx = -1;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

        SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
        int y = sipetJPanel.openpipe?38:20;
        if (e.getY() / y < listpet.getModel().getSize()) {
                switch (types) {
                    case 0:
                        SipetJPanel.idx = listpet.locationToIndex(e.getPoint());
                        AlchemyMainJpanel.idx = -1; // 其它类型的变量重置
                        break;
                    case 1:
                        AlchemyMainJpanel.idx = listpet.locationToIndex(e.getPoint());
                        SipetJPanel.idx = -1; // 其它类型的变量重置
                        break;
                }
            }
    }
}
