package jxy2.xbao;

import org.come.mouslisten.TemplateMouseListener;

import java.awt.event.MouseEvent;

public class AcGoodsLabelMouse extends TemplateMouseListener {
    private int index;
    private XbaoJpanel xbaoJpanel;
    public AcGoodsLabelMouse(int index,XbaoJpanel xbaoJpanel) {
        this.index = index;
        this.xbaoJpanel = xbaoJpanel;

    }
    @Override
    protected void specificMousePressed(MouseEvent e) {

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
