package jxy2.xbao;

import org.come.mouslisten.TemplateMouseListener;

import java.awt.event.MouseEvent;

public class Xbao<PERSON>istMouse extends TemplateMouseListener {
    public  XbaoLibraryModel xbaoLibraryModel;
    public int index;
    public Xuanbao xuanbao;
    public XbaoListMouse(XbaoLibraryModel xbaoLibraryModel, int index, Xuanbao xuanbao) {
        this.xbaoLibraryModel = xbaoLibraryModel;
        this.index = index;
        this.xuanbao = xuanbao;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        XbaoLibraryJPanel xbaoLibraryJPanel = XbaoLibraryFrame.getXbaoLibraryFrame().getXbaoLibraryJPanel();
        xbaoLibraryJPanel.getXbaoLibraryModelMap().get(index).getXuanbaotx().setVisible(true);
        for (int i = 0; i < xbaoLibraryJPanel.getXbaoLibraryModelMap().size(); i++) {
            if (i!=index){
                xbaoLibraryJPanel.getXbaoLibraryModelMap().get(i).getXuanbaotx().setVisible(false);
            }
        }
        xbaoLibraryJPanel.getXbaoRightSideJpanel().LoadingData(xbaoLibraryJPanel.getXbaoLibraryModelMap().get(index).getXuanbao());
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
