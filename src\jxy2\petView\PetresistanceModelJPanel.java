package jxy2.petView;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.ImgZoom;
import org.come.entity.RoleSummoning;

import javax.swing.*;
import java.awt.*;
/**
* 装备展示基础属性属性 EdbapModelJPanel
* <AUTHOR>
* @date 2024/7/18 上午6:38
*/

public class PetresistanceModelJPanel extends JPanel {
    private PetresistanceBtn petresistanceBtn;//抗性
    private int index,Sex=0;
    private boolean isExpanded,allowRefresh;
    private RichLabel richLabel;
    private JList<String> listNo1;
    private JList<String> listNo2;
    private DefaultListModel<String> dlm= new DefaultListModel<String>();
    private DefaultListModel<String> dlm1= new DefaultListModel<String>();;
    public PetresistanceModelJPanel(int index) {
        this.index = index;
        this.isExpanded = false; // 默认不展开
        this.allowRefresh = false; // 默认不展开
        setPreferredSize(new Dimension(288, 180));
        setOpaque(false);
        setLayout(null);
        String[] resis = {"法术抗性","法术增强","物理属性","五行属性","其他"};
        petresistanceBtn = new PetresistanceBtn(ImgConstants.tz68, 1, index,resis[index], UIUtils.COLOR_BTNTEXT,this,index);
        petresistanceBtn.setBounds(3 ,0, 288, 34);
        this.add(petresistanceBtn);
        getRichLabel();
        getListNo1();
        getListNo2();
    }

    public void initPetResistance(int index, RoleSummoning pet) {
        petresistanceBtn.setBounds(3 ,0, 288, 34);
    }
    public ImgZoom tz22 = Juitil.WindowImgPng(ImgConstants.tz22, 14, 7);

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (isExpanded) {
            Juitil.ImngBack(g, tz22, 4, 26, 285, 134, 1);
        }
    }

    public PetresistanceBtn getPetresistanceBtn() {
        return petresistanceBtn;
    }

    public void setPetresistanceBtn(PetresistanceBtn petresistanceBtn) {
        this.petresistanceBtn = petresistanceBtn;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getSex() {
        return Sex;
    }

    public void setSex(int sex) {
        Sex = sex;
    }
    public void setExpanded(boolean isExpanded) {
        this.isExpanded = isExpanded;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public RichLabel getRichLabel() {
        if (richLabel == null){
            richLabel=new RichLabel("", UIUtils.MSYH_HY14, 255);
            richLabel.setBounds(15, 34, richLabel.getWidth(), 23);
            this.add(richLabel);
        }
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }

    public boolean isAllowRefresh() {
        return allowRefresh;
    }

    public void setAllowRefresh(boolean allowRefresh) {
        this.allowRefresh = allowRefresh;
    }

    public JList<String> getListNo1() {
        if (listNo1 == null){
            listNo1 = new JList<String>();
            listNo1.setForeground(UIUtils.Color_pet_reis);
            listNo1.setFont(UIUtils.MSYH_HY14);
            listNo1.setBorder(BorderFactory.createEmptyBorder());
            listNo1.setBackground(UIUtils.Color_BACK);
            listNo1.setCellRenderer(new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                    JLabel label = (JLabel) super.getListCellRendererComponent(list, value, index, false, cellHasFocus);
                    label.setBackground(list.getBackground());
                    label.setForeground(list.getForeground());
                    label.setBorder(BorderFactory.createEmptyBorder());
                    return label;
                }
            });
            listNo1.setModel(dlm);
            this.add(listNo1);
        }
        return listNo1;
    }

    public void setListNo1(JList<String> listNo1) {
        this.listNo1 = listNo1;
    }

    public JList<String> getListNo2() {
        if (listNo2 == null) {
            listNo2 = new JList<String>();
            listNo2.setForeground(UIUtils.Color_pet_reis);
            listNo2.setFont(UIUtils.MSYH_HY14);
            listNo2.setBorder(BorderFactory.createEmptyBorder());
            listNo2.setBackground(UIUtils.Color_BACK);
            // 设置自定义的 ListCellRenderer
            listNo2.setCellRenderer(new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                    JLabel label = (JLabel) super.getListCellRendererComponent(list, value, index, false, cellHasFocus);
                    label.setBackground(list.getBackground());
                    label.setForeground(list.getForeground());
                    label.setBorder(BorderFactory.createEmptyBorder());
                    return label;
                }
            });
            listNo2.setModel(dlm1);
            this.add(listNo2);
        }
        return listNo2;
    }

    public void setListNo2(JList<String> listNo2) {
        this.listNo2 = listNo2;
    }

    public DefaultListModel<String> getDlm() {
        return dlm;
    }

    public void setDlm(DefaultListModel<String> dlm) {
        this.dlm = dlm;
    }

    public DefaultListModel<String> getDlm1() {
        return dlm1;
    }

    public void setDlm1(DefaultListModel<String> dlm1) {
        this.dlm1 = dlm1;
    }


}
