package jxy2.backutil;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.refine.RefineFrame;
import org.come.entity.Goodstable;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.CutButtonImage;
import org.come.until.EquipTool;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Goodtype;

import javax.swing.*;
import java.awt.*;

/**
 * ClassName:除主包裹之外的包裹调用
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/4/18 22:25
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class AssGoodUntil {
    private static Goodstable[] Goodslist = new Goodstable[24];
    public static int Pagenumber = 0;// 页数 0表示第一页
    private static ImageIcon lockimg = CutButtonImage.getWdfPng(ImgConstants.tz42,"defaut.wdf");
    private static ImageIcon[] goodimg = new ImageIcon[24];// 存放当前背包页面的img
    /**绘制物品*/
    public static void draw(Graphics g, int x, int y) {
        g.setFont(UIUtils.TEXT_FONT);
        g.setColor(Color.WHITE);
        for (int i = 0; i < 24; i++) {
            int row = i % 6 * 49;
            int col = i / 6 * 49;
            if (Goodslist[i + Pagenumber * 24] != null) {
                if (goodimg[i] != null) {
                    boolean is = GoodsMouslisten.list.contains(i + "");
                    int gx =is?x + row+1: x + row;
                    int gy = is?y + col + 3:y + col + 2;

                    if (Goodslist[i + Pagenumber * 24].getIsSelected() == 1&& RefineFrame.getRefineFrame().isVisible()) {
                        g.drawImage(Juitil.convertToGray(goodimg[i].getImage()), gx , gy, 40, 40, null);
                    }else {
                        g.drawImage(goodimg[i].getImage(), gx , gy, 40, 40, null);
                        Goodslist[i + Pagenumber * 24].setIsSelected(0);
                    }
                }
                packTimeUi(g, x, y, i, row, col);
                // 判断如果是已加锁，则加上加锁标志
                if (Goodslist[i + Pagenumber * 24].getGoodlock() == 1) {
                    g.drawImage(lockimg.getImage(), x + row + 26, y + col + 28, 15, 15, null);
                }

            }
        }
    }

    private static void packTimeUi(Graphics g, int x, int y, int i, int row, int col) {
        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        int rx = x-1+row;
        int ry = y+13 + col;
        if (!EquipTool.isEquip(Goodslist[i + Pagenumber * 24].getType())) {//可叠加物品
            Juitil.TextBackground(g, "" + Goodslist[i + Pagenumber * 24].getUsetime(), 13, rx, ry-13, UIUtils.COLOR_value, UIUtils.FZCY_HY14);
            if (GoodsListFromServerUntil.NottrueKs&& Goodtype.Ore(Goodslist[i + Pagenumber * 24].getType())||Goodslist[i + Pagenumber * 24].getType()==8889){
                Goodstable good = Goodslist[i+Pagenumber *24];
                if (!good.getValue().isEmpty()) {
                    String lv = good.getValue().split("\\|")[0].split("=")[1];
                    Juitil.TextBackground(g, lv + "级", 13, rx + 18, ry + 14, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                }
            }
        }else if (GoodsListFromServerUntil.Nottrue&&Goodslist[i + Pagenumber * 24].getType()==188||Goodtype.baoshi(Goodslist[i + Pagenumber * 24].getType())){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            String lv = good.getValue().split("\\|")[0].split("=")[1];
            Juitil.TextBackground(g, lv+"级", 13, rx+18, ry+15, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
        }else if (GoodsListFromServerUntil.Nottrue&&Goodtype.ExerciseMonsterOre(Goodslist[i + Pagenumber * 24].getType())){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            String lv = good.getValue().split("\\|")[1].split("=")[1];
            Juitil.TextBackground(g, "+"+lv, 13, rx+20, ry-13, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
        }else if (Goodslist[i + Pagenumber * 24].getType()==7099&&GoodsListFromServerUntil.Nottrue){
            //绘制符石等级
            Goodstable good = Goodslist[i+Pagenumber *24];
            if (!good.getValue().isEmpty()) {
                String lv = good.getValue().split("\\|")[0].split("=")[1];
                Juitil.TextBackground(g, lv + "阶", 13, rx + 18, ry + 14, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
            }
        }
    }


    /**
     * 获取物品
     */
    public static void getgood(int number) {
        Pagenumber = number;
        for (int i = Pagenumber * 24; i < (Pagenumber + 1) * 24; i++) {
                if (Goodslist[i]!=null) {
                    goodimg[i - Pagenumber * 24] = GoodsListFromServerUntil.imgpathWdfile(Goodslist[i].getSkin(), 60, 60);
                }else {
                    goodimg[i - Pagenumber * 24] = null;
                }

        }
    }

    public static void addddgood(Goodstable[] goodstable) {
            Goodslist = goodstable;
            AssGoodUntil.getgood(Pagenumber);
    }

    public static Goodstable[] getGoodslist() {
        return Goodslist;
    }

    public static void setGoodslist(Goodstable[] goodslist) {
        Goodslist = goodslist;
    }
}
