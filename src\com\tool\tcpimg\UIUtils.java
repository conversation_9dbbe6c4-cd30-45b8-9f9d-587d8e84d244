package com.tool.tcpimg;

/*
 * JavaXYQ Source Code
 * by kylixs
 * at 2010-5-15
 * please visit http://javaxyq.googlecode.com
 * or <NAME_EMAIL>
 */

import org.come.until.AnalysisString;
import org.come.until.FontUntil;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2010-5-15 create
 */
/**
 * <AUTHOR> 2019年12月9日 下午3:57:12 类名:UIUtils
 */
public class UIUtils {
    // 人物名字间隔
    public static final int NAMESiZE = 18;
    public static final Font TEXT_FONT10 = new Font("宋体", Font.PLAIN, 10);
    /** 字段名:TEXT_FONT "宋体", Font.PLAIN, 12<br> */
    public static final Font TEXT_FONT = new Font("宋体", Font.PLAIN, 12);
    public static final Font TEXT_FONT2 = new Font("宋体", Font.PLAIN, 13);
    public static final Font TEXT_FONT1 = new Font("宋体", Font.PLAIN, 14);
    public static final Font TEXT_FONT15 = new Font("宋体", Font.PLAIN, 15);
    public static final Font TEXT_NAME_FONT = new Font("宋体", Font.PLAIN, 16);
    public static final Font TEXT_BOLD_FONT = new Font("宋体", Font.BOLD, 16);
    public static final Font TEXT_FONT22 = new Font("宋体", Font.PLAIN, 18);
    public static final Font TEXT_FONT_17 = new Font("宋体", Font.PLAIN, 17);
    public static final Font TEXT_FONT_17B = new Font("宋体", Font.BOLD, 17);
    public static final Font TEXT_FONT3 = new Font("宋体", Font.PLAIN, 20);
    public static final Font TEXT_FONT19 = new Font("宋体", Font.PLAIN, 19);

    public static final Font TEXT_FONT_0 = new Font("宋体", Font.PLAIN, 14);
    public static Font TEXT_FONT_1;
    public static Font TEXT_FONT_2;

    // 天演策技能名称
    public static final Font TEXT_FONT5 = new Font("宋体", 1, 19);
    public static Font TEXT_HYK16; /** 字段名:TEXT_HYK16 汉仪楷体简 <br> */
    public static Font TEXT_HYK20; /** 字段名:TEXT_HYK20 汉仪楷体简 <br> */
    public static Font TEXT_HYK28; /** 字段名:TEXT_HYK20 汉仪楷体简 <br> */
    public static Font TEXT_HY12; /** 汉仪小隶书简12号 */
    public static Font TEXT_HY14; /** 汉仪小隶书简14号 */
    public static Font TEXT_HY16B; /** 汉仪小隶书简16号 */
    public static Font TEXT_HY16; /** 汉仪小隶书简16号 */
    public static Font TEXT_HY16_F; /** 汉仪小隶书简16号 */
    public static Font TEXT_HY17; /** 汉仪小隶书简17号 */
    public static Font TEXT_HY19; /** 汉仪小隶书简19号 */
    public static Font TEXT_HY24; /** 汉仪小隶书简24号 */
    public static Font TEXT_HY34; /** 汉仪小隶书简34号 */
    public static Font HYXKJ_HY12; /**汉仪行楷简12号**/
    public static Font HYXKJ_HY13; /**汉仪行楷简13号**/
    public static Font HYXKJ_HY14; /**汉仪行楷简14号**/
    public static Font HYXKJ_HY15; /**汉仪行楷简15号**/
    public static Font HYXKJ_HY16; /**汉仪行楷简16号**/
    public static Font HYXKJ_HY17; /**汉仪行楷简17号**/
    public static Font HYXKJ_HY18; /**汉仪行楷简18号**/
    public static Font HYXKJ_HY19; /**汉仪行楷简19号**/
    public static Font HYXKJ_HY20; /**汉仪行楷简20号**/
    public static Font HYXKJ_HY21; /**汉仪行楷简21号**/
    public static Font HYXKJ_HY22; /**汉仪行楷简22号**/
    public static Font HYXKJ_HY28; /**汉仪行楷简22号**/
    public static Font HYXKJ_HY26; /**汉仪行楷简22号**/
    public static Font HYXKJ_HY27; /**汉仪行楷简22号**/
    public static Font HYXKJ_HY29; /**汉仪行楷简22号**/
    public static Font HYXKJ_HY30; /**汉仪行楷简22号**/


    public static Font NEWTX_HY12; /**汉仪楷体简12号**/
    public static Font NEWTX_HY13; /**汉仪楷体简13号**/
    public static Font NEWTX_HY14; /**汉仪楷体简14号**/
    public static Font NEWTX_HY15; /**汉仪楷体简15号**/
    public static Font NEWTX_HY16; /**汉仪楷体简16号**/
    public static Font NEWTX_HY17; /**汉仪楷体简17号**/
    public static Font NEWTX_HY18; /**汉仪楷体简18号**/
    public static Font NEWTX_HY19; /**汉仪楷体简19号**/
    public static Font NEWTX_HY20; /**汉仪楷体简20号**/
    public static Font NEWTX_HY21; /**汉仪楷体简21号**/
    public static Font NEWTX_HY22; /**汉仪楷体简22号**/
    public static Font NEWTX_HY28; /**汉仪楷体简22号**/
    public static Font NEWTX_HY26; /**汉仪楷体简22号**/
    public static Font NEWTX_HY27; /**汉仪楷体简22号**/

    public static Font NEWTX_HY12B; /**汉仪楷体简12号**/
    public static Font NEWTX_HY13B; /**汉仪楷体简13号**/
    public static Font NEWTX_HY14B; /**汉仪楷体简14号**/
    public static Font NEWTX_HY15B; /**汉仪楷体简15号**/
    public static Font NEWTX_HY16B; /**汉仪楷体简16号**/
    public static Font NEWTX_HY17B; /**汉仪楷体简17号**/
    public static Font NEWTX_HY18B; /**汉仪楷体简18号**/
    public static Font NEWTX_HY19B; /**汉仪楷体简19号**/
    public static Font NEWTX_HY20B; /**汉仪楷体简20号**/
    public static Font NEWTX_HY21B; /**汉仪楷体简21号**/
    public static Font NEWTX_HY22B; /**汉仪楷体简22号**/
    public static Font NEWTX_HY28B; /**汉仪楷体简22号**/
    public static Font NEWTX_HY26B; /**汉仪楷体简22号**/
    public static Font NEWTX_HY27B; /*汉仪楷体简22号**/

    public static Font MSYH_HY12; /**汉仪楷体简12号**/
    public static Font MSYH_HY13; /**汉仪楷体简13号**/
    public static Font MSYH_HY14; /**汉仪楷体简14号**/
    public static Font MSYH_HY15; /**汉仪楷体简15号**/
    public static Font MSYH_HY16; /**汉仪楷体简16号**/
    public static Font MSYH_HY17; /**汉仪楷体简17号**/
    public static Font MSYH_HY18; /**汉仪楷体简18号**/
    public static Font MSYH_HY19; /**汉仪楷体简19号**/
    public static Font MSYH_HY20; /**汉仪楷体简20号**/
    public static Font MSYH_HY21; /**汉仪楷体简21号**/
    public static Font MSYH_HY22; /**汉仪楷体简22号**/
    public static Font MSYH_HY28; /**汉仪楷体简22号**/
    public static Font MSYH_HY26; /**汉仪楷体简22号**/
    public static Font MSYH_HY27; /**汉仪楷体简22号**/

    public static Font MSYH_HY12B; /**汉仪楷体简12号**/
    public static Font MSYH_HY13B; /**汉仪楷体简13号**/
    public static Font MSYH_HY14B; /**汉仪楷体简14号**/
    public static Font MSYH_HY15B; /**汉仪楷体简15号**/
    public static Font MSYH_HY16B; /**汉仪楷体简16号**/
    public static Font MSYH_HY17B; /**汉仪楷体简17号**/
    public static Font MSYH_HY18B; /**汉仪楷体简18号**/
    public static Font MSYH_HY19B; /**汉仪楷体简19号**/
    public static Font MSYH_HY20B; /**汉仪楷体简20号**/
    public static Font MSYH_HY21B; /**汉仪楷体简21号**/
    public static Font MSYH_HY22B; /**汉仪楷体简22号**/
    public static Font MSYH_HY28B; /**汉仪楷体简22号**/
    public static Font MSYH_HY26B; /**汉仪楷体简22号**/
    public static Font MSYH_HY27B; /*汉仪楷体简22号**/

    public static Font FZCY_HY12; /**方正粗圆_GBK12号**/
    public static Font FZCY_HY13; /**方正粗圆_GBK13号**/
    public static Font FZCY_HY14; /**方正粗圆_GBK14号**/
    public static Font FZCY_HY15; /**方正粗圆_GBK15号**/
    public static Font FZCY_HY16; /**方正粗圆_GBK16号**/
    public static Font FZCY_HY17; /**方正粗圆_GBK17号**/
    public static Font FZCY_HY18; /**方正粗圆_GBK18号**/
    public static Font FZCY_HY19; /**方正粗圆_GBK19号**/
    public static Font FZCY_HY20; /**方正粗圆_GBK20号**/
    public static Font FZCY_HY21; /**方正粗圆_GBK21号**/
    public static Font FZCY_HY22; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY28; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY26; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY27; /**方正粗圆_GBK22号**/

    public static Font FZCY_HY12B; /**方正粗圆_GBK12号**/
    public static Font FZCY_HY13B; /**方正粗圆_GBK13号**/
    public static Font FZCY_HY14B; /**方正粗圆_GBK14号**/
    public static Font FZCY_HY15B; /**方正粗圆_GBK15号**/
    public static Font FZCY_HY16B; /**方正粗圆_GBK16号**/
    public static Font FZCY_HY17B; /**方正粗圆_GBK17号**/
    public static Font FZCY_HY18B; /**方正粗圆_GBK18号**/
    public static Font FZCY_HY19B; /**方正粗圆_GBK19号**/
    public static Font FZCY_HY20B; /**方正粗圆_GBK20号**/
    public static Font FZCY_HY21B; /**方正粗圆_GBK21号**/
    public static Font FZCY_HY22B; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY28B; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY26B; /**方正粗圆_GBK22号**/
    public static Font FZCY_HY27B; /**方正粗圆_GBK27号**/

    public static Font MSYHBD_HY12; /**汉仪楷体简12号**/
    public static Font MSYHBD_HY13; /**汉仪楷体简13号**/
    public static Font MSYHBD_HY14; /**汉仪楷体简14号**/
    public static Font MSYHBD_HY15; /**汉仪楷体简15号**/
    public static Font MSYHBD_HY16; /**汉仪楷体简16号**/
    public static Font MSYHBD_HY17; /**汉仪楷体简17号**/
    public static Font MSYHBD_HY18; /**汉仪楷体简18号**/
    public static Font MSYHBD_HY19; /**汉仪楷体简19号**/
    public static Font MSYHBD_HY20; /**汉仪楷体简20号**/
    public static Font MSYHBD_HY21; /**汉仪楷体简21号**/
    public static Font MSYHBD_HY22; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY28; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY26; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY27; /**汉仪楷体简22号**/

    public static Font MSYHBD_HY12B; /**汉仪楷体简12号**/
    public static Font MSYHBD_HY13B; /**汉仪楷体简13号**/
    public static Font MSYHBD_HY14B; /**汉仪楷体简14号**/
    public static Font MSYHBD_HY15B; /**汉仪楷体简15号**/
    public static Font MSYHBD_HY16B; /**汉仪楷体简16号**/
    public static Font MSYHBD_HY17B; /**汉仪楷体简17号**/
    public static Font MSYHBD_HY18B; /**汉仪楷体简18号**/
    public static Font MSYHBD_HY19B; /**汉仪楷体简19号**/
    public static Font MSYHBD_HY20B; /**汉仪楷体简20号**/
    public static Font MSYHBD_HY21B; /**汉仪楷体简21号**/
    public static Font MSYHBD_HY22B; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY28B; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY26B; /**汉仪楷体简22号**/
    public static Font MSYHBD_HY27B; /*汉仪楷体简22号**/

    public static Font TEXT_HYJ12; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ13; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ14; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ15; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ16; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ17; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ18; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ19; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ21; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ22; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ24; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ26; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */

    public static Font TEXT_HYJ12B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ13B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ14B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ15B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ16B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ17B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ18B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ19B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ21B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ22B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ24B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */
    public static Font TEXT_HYJ26B; /** 字段名:TEXT_HYK20 汉仪隶书简 <br> */

    public static Font LiSu_LS12; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS13; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS14; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS15; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS16; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS17; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS18; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS19; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS21; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS22; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS24; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS26; /** 字段名:LiSu_HYK20 LiSu <br> */

    public static Font LiSu_LS12B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS13B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS14B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS15B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS16B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS17B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS18B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS19B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS21B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS22B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS24B; /** 字段名:LiSu_HYK20 LiSu <br> */
    public static Font LiSu_LS26B; /* 字段名:TEXT_HYK20 LiSu <br> */
    static{
    	Font font=FontUntil.loadFont("font/HYF2GJM_0.TTF");
    	TEXT_HY12=font.deriveFont(Font.BOLD, 12);
    	TEXT_HY14=font.deriveFont(Font.BOLD, 14);
    	TEXT_HY16B=font.deriveFont(Font.BOLD, 16);
    	TEXT_HY16=font.deriveFont(Font.BOLD, 16);
    	TEXT_HY16_F=font.deriveFont(Font.PLAIN, 16);
    	TEXT_HY17=font.deriveFont(Font.BOLD, 17);
    	TEXT_HY19=font.deriveFont(Font.BOLD, 19);
    	TEXT_HY24=font.deriveFont(Font.BOLD, 24);
    	TEXT_HY34=font.deriveFont(Font.BOLD, 34);
    	font=FontUntil.loadFont("font/HYC1GJM.TTF");
    	TEXT_HYK16=font.deriveFont(Font.BOLD, 16);
    	TEXT_HYK20=font.deriveFont(Font.BOLD, 20);
        font=FontUntil.loadFont("font/hyi1gjm.ttf");
        HYXKJ_HY12 = font.deriveFont(Font.PLAIN, 12);
        HYXKJ_HY13 = font.deriveFont(Font.PLAIN, 13);
        HYXKJ_HY14 = font.deriveFont(Font.PLAIN, 14);
        HYXKJ_HY15 = font.deriveFont(Font.PLAIN, 15);
        HYXKJ_HY16 = font.deriveFont(Font.PLAIN, 16);
        HYXKJ_HY17 = font.deriveFont(Font.PLAIN, 17);
        HYXKJ_HY18 = font.deriveFont(Font.PLAIN, 18);
        HYXKJ_HY19 = font.deriveFont(Font.PLAIN, 19);
        HYXKJ_HY20 = font.deriveFont(Font.PLAIN, 20);
        HYXKJ_HY21 = font.deriveFont(Font.PLAIN, 21);
        HYXKJ_HY22 = font.deriveFont(Font.PLAIN, 22);
        HYXKJ_HY28 = font.deriveFont(Font.PLAIN, 28);
        HYXKJ_HY26 = font.deriveFont(Font.PLAIN, 26);
        HYXKJ_HY27 = font.deriveFont(Font.PLAIN, 27);
        HYXKJ_HY29 = font.deriveFont(Font.PLAIN, 29);
        HYXKJ_HY30 = font.deriveFont(Font.PLAIN, 30);
        font=FontUntil.loadFont("font/HYC1GJM.TTF");
        NEWTX_HY12 = font.deriveFont(Font.PLAIN, 12);
        NEWTX_HY13 = font.deriveFont(Font.PLAIN, 13);
        NEWTX_HY14 = font.deriveFont(Font.PLAIN, 14);
        NEWTX_HY15 = font.deriveFont(Font.PLAIN, 15);
        NEWTX_HY16 = font.deriveFont(Font.PLAIN, 16);
        NEWTX_HY17 = font.deriveFont(Font.PLAIN, 17);
        NEWTX_HY18 = font.deriveFont(Font.PLAIN, 18);
        NEWTX_HY19 = font.deriveFont(Font.PLAIN, 19);
        NEWTX_HY20 = font.deriveFont(Font.PLAIN, 20);
        NEWTX_HY21 = font.deriveFont(Font.PLAIN, 21);
        NEWTX_HY22 = font.deriveFont(Font.PLAIN, 22);
        NEWTX_HY28 = font.deriveFont(Font.PLAIN, 28);
        NEWTX_HY26 = font.deriveFont(Font.PLAIN, 26);
        NEWTX_HY27 = font.deriveFont(Font.PLAIN, 27);

        NEWTX_HY12B = font.deriveFont(Font.BOLD, 12);
        NEWTX_HY13B = font.deriveFont(Font.BOLD, 13);
        NEWTX_HY14B = font.deriveFont(Font.BOLD, 14);
        NEWTX_HY15B = font.deriveFont(Font.BOLD, 15);
        NEWTX_HY16B = font.deriveFont(Font.BOLD, 16);
        NEWTX_HY17B = font.deriveFont(Font.BOLD, 17);
        NEWTX_HY18B = font.deriveFont(Font.BOLD, 18);
        NEWTX_HY19B = font.deriveFont(Font.BOLD, 19);
        NEWTX_HY20B = font.deriveFont(Font.BOLD, 20);
        NEWTX_HY21B = font.deriveFont(Font.BOLD, 21);
        NEWTX_HY22B = font.deriveFont(Font.BOLD, 22);
        NEWTX_HY28B = font.deriveFont(Font.BOLD, 28);
        NEWTX_HY26B = font.deriveFont(Font.BOLD, 26);
        NEWTX_HY27B = font.deriveFont(Font.BOLD, 27);

        font=FontUntil.loadFont("font/MSYH.TTF");
        MSYH_HY12 = font.deriveFont(Font.PLAIN, 12);
        MSYH_HY13 = font.deriveFont(Font.PLAIN, 13);
        MSYH_HY14 = font.deriveFont(Font.PLAIN, 14);
        TEXT_FONT_2 = font.deriveFont(Font.PLAIN, 14);
        MSYH_HY15 = font.deriveFont(Font.PLAIN, 15);
        MSYH_HY16 = font.deriveFont(Font.PLAIN, 16);
        MSYH_HY17 = font.deriveFont(Font.PLAIN, 17);
        MSYH_HY18 = font.deriveFont(Font.PLAIN, 18);
        MSYH_HY19 = font.deriveFont(Font.PLAIN, 19);
        MSYH_HY20 = font.deriveFont(Font.PLAIN, 20);
        MSYH_HY21 = font.deriveFont(Font.PLAIN, 21);
        MSYH_HY22 = font.deriveFont(Font.PLAIN, 22);
        MSYH_HY28 = font.deriveFont(Font.PLAIN, 28);
        MSYH_HY26 = font.deriveFont(Font.PLAIN, 26);
        MSYH_HY27 = font.deriveFont(Font.PLAIN, 27);

        MSYH_HY12B = font.deriveFont(Font.BOLD, 12);
        MSYH_HY13B = font.deriveFont(Font.BOLD, 13);
        MSYH_HY14B = font.deriveFont(Font.BOLD, 14);
        MSYH_HY15B = font.deriveFont(Font.BOLD, 15);
        MSYH_HY16B = font.deriveFont(Font.BOLD, 16);
        MSYH_HY17B = font.deriveFont(Font.BOLD, 17);
        MSYH_HY18B = font.deriveFont(Font.BOLD, 18);
        MSYH_HY19B = font.deriveFont(Font.BOLD, 19);
        MSYH_HY20B = font.deriveFont(Font.BOLD, 20);
        MSYH_HY21B = font.deriveFont(Font.BOLD, 21);
        MSYH_HY22B = font.deriveFont(Font.BOLD, 22);
        MSYH_HY28B = font.deriveFont(Font.BOLD, 28);
        MSYH_HY26B = font.deriveFont(Font.BOLD, 26);
        MSYH_HY27B = font.deriveFont(Font.BOLD, 27);

        font=FontUntil.loadFont("font/fzxh1jw.TTF");
        FZCY_HY12 = font.deriveFont(Font.PLAIN, 12);
        FZCY_HY13 = font.deriveFont(Font.PLAIN, 13);
        FZCY_HY14 = font.deriveFont(Font.PLAIN, 14);
        TEXT_FONT_1 = font.deriveFont(Font.PLAIN, 14);
        FZCY_HY15 = font.deriveFont(Font.PLAIN, 15);
        FZCY_HY16 = font.deriveFont(Font.PLAIN, 16);
        FZCY_HY17 = font.deriveFont(Font.PLAIN, 17);
        FZCY_HY18 = font.deriveFont(Font.PLAIN, 18);
        FZCY_HY19 = font.deriveFont(Font.PLAIN, 19);
        FZCY_HY20 = font.deriveFont(Font.PLAIN, 20);
        FZCY_HY21 = font.deriveFont(Font.PLAIN, 21);
        FZCY_HY22 = font.deriveFont(Font.PLAIN, 22);
        FZCY_HY28 = font.deriveFont(Font.PLAIN, 28);
        FZCY_HY26 = font.deriveFont(Font.PLAIN, 26);
        FZCY_HY27 = font.deriveFont(Font.PLAIN, 27);

        FZCY_HY12B = font.deriveFont(Font.BOLD, 12);
        FZCY_HY13B = font.deriveFont(Font.BOLD, 13);
        FZCY_HY14B = font.deriveFont(Font.BOLD, 14);
        FZCY_HY15B = font.deriveFont(Font.BOLD, 15);
        FZCY_HY16B = font.deriveFont(Font.BOLD, 16);
        FZCY_HY17B = font.deriveFont(Font.BOLD, 17);
        FZCY_HY18B = font.deriveFont(Font.BOLD, 18);
        FZCY_HY19B = font.deriveFont(Font.BOLD, 19);
        FZCY_HY20B = font.deriveFont(Font.BOLD, 20);
        FZCY_HY21B = font.deriveFont(Font.BOLD, 21);
        FZCY_HY22B = font.deriveFont(Font.BOLD, 22);
        FZCY_HY28B = font.deriveFont(Font.BOLD, 28);
        FZCY_HY26B = font.deriveFont(Font.BOLD, 26);
        FZCY_HY27B = font.deriveFont(Font.BOLD, 27);


        font=FontUntil.loadFont("font/MSYHBD.TTF");
        MSYHBD_HY12 = font.deriveFont(Font.PLAIN, 12);
        MSYHBD_HY13 = font.deriveFont(Font.PLAIN, 13);
        MSYHBD_HY14 = font.deriveFont(Font.PLAIN, 14);
        MSYHBD_HY15 = font.deriveFont(Font.PLAIN, 15);
        MSYHBD_HY16 = font.deriveFont(Font.PLAIN, 16);
        MSYHBD_HY17 = font.deriveFont(Font.PLAIN, 17);
        MSYHBD_HY18 = font.deriveFont(Font.PLAIN, 18);
        MSYHBD_HY19 = font.deriveFont(Font.PLAIN, 19);
        MSYHBD_HY20 = font.deriveFont(Font.PLAIN, 20);
        MSYHBD_HY21 = font.deriveFont(Font.PLAIN, 21);
        MSYHBD_HY22 = font.deriveFont(Font.PLAIN, 22);
        MSYHBD_HY28 = font.deriveFont(Font.PLAIN, 28);
        MSYHBD_HY26 = font.deriveFont(Font.PLAIN, 26);
        MSYHBD_HY27 = font.deriveFont(Font.PLAIN, 27);

        MSYHBD_HY12B = font.deriveFont(Font.BOLD, 12);
        MSYHBD_HY13B = font.deriveFont(Font.BOLD, 13);
        MSYHBD_HY14B = font.deriveFont(Font.BOLD, 14);
        MSYHBD_HY15B = font.deriveFont(Font.BOLD, 15);
        MSYHBD_HY16B = font.deriveFont(Font.BOLD, 16);
        MSYHBD_HY17B = font.deriveFont(Font.BOLD, 17);
        MSYHBD_HY18B = font.deriveFont(Font.BOLD, 18);
        MSYHBD_HY19B = font.deriveFont(Font.BOLD, 19);
        MSYHBD_HY20B = font.deriveFont(Font.BOLD, 20);
        MSYHBD_HY21B = font.deriveFont(Font.BOLD, 21);
        MSYHBD_HY22B = font.deriveFont(Font.BOLD, 22);
        MSYHBD_HY28B = font.deriveFont(Font.BOLD, 28);
        MSYHBD_HY26B = font.deriveFont(Font.BOLD, 26);
        MSYHBD_HY27B = font.deriveFont(Font.BOLD, 27);




        //汉仪小隶书
        font=FontUntil.loadFont("font/HYF2GJM.TTF");
        TEXT_HYJ12=font.deriveFont(Font.PLAIN, 12);
        TEXT_HYJ13=font.deriveFont(Font.PLAIN, 13);
        TEXT_HYJ14=font.deriveFont(Font.PLAIN, 14);
        TEXT_HYJ15=font.deriveFont(Font.PLAIN, 15);
        TEXT_HYJ16=font.deriveFont(Font.PLAIN, 16);
        TEXT_HYJ17=font.deriveFont(Font.PLAIN, 17);
        TEXT_HYJ18=font.deriveFont(Font.PLAIN, 18);
        TEXT_HYJ19=font.deriveFont(Font.PLAIN, 19);
        TEXT_HYJ21=font.deriveFont(Font.PLAIN, 21);
        TEXT_HYJ22=font.deriveFont(Font.PLAIN, 22);
        TEXT_HYJ24=font.deriveFont(Font.PLAIN, 24);
        TEXT_HYJ26=font.deriveFont(Font.PLAIN, 26);

        TEXT_HYJ12B=font.deriveFont(Font.BOLD, 12);
        TEXT_HYJ13B=font.deriveFont(Font.BOLD, 13);
        TEXT_HYJ14B=font.deriveFont(Font.BOLD, 14);
        TEXT_HYJ15B=font.deriveFont(Font.BOLD, 15);
        TEXT_HYJ16B=font.deriveFont(Font.BOLD, 16);
        TEXT_HYJ17B=font.deriveFont(Font.BOLD, 17);
        TEXT_HYJ18B=font.deriveFont(Font.BOLD, 18);
        TEXT_HYJ19B=font.deriveFont(Font.BOLD, 19);
        TEXT_HYJ21B=font.deriveFont(Font.BOLD, 21);
        TEXT_HYJ22B=font.deriveFont(Font.BOLD, 22);
        TEXT_HYJ24B=font.deriveFont(Font.BOLD, 24);
        TEXT_HYJ26B=font.deriveFont(Font.BOLD, 26);
        //数字隶书
        font=FontUntil.loadFont("font/simli.TTF");
        LiSu_LS12=font.deriveFont(Font.PLAIN, 12);
        LiSu_LS13=font.deriveFont(Font.PLAIN, 13);
        LiSu_LS14=font.deriveFont(Font.PLAIN, 14);
        LiSu_LS15=font.deriveFont(Font.PLAIN, 15);
        LiSu_LS16=font.deriveFont(Font.PLAIN, 16);
        LiSu_LS17=font.deriveFont(Font.PLAIN, 17);
        LiSu_LS18=font.deriveFont(Font.PLAIN, 18);
        LiSu_LS19=font.deriveFont(Font.PLAIN, 19);
        LiSu_LS21=font.deriveFont(Font.PLAIN, 21);
        LiSu_LS22=font.deriveFont(Font.PLAIN, 22);
        LiSu_LS24=font.deriveFont(Font.PLAIN, 24);
        LiSu_LS26=font.deriveFont(Font.PLAIN, 26);

        LiSu_LS12B=font.deriveFont(Font.BOLD, 12);
        LiSu_LS13B=font.deriveFont(Font.BOLD, 13);
        LiSu_LS14B=font.deriveFont(Font.BOLD, 14);
        LiSu_LS15B=font.deriveFont(Font.BOLD, 15);
        LiSu_LS16B=font.deriveFont(Font.BOLD, 16);
        LiSu_LS17B=font.deriveFont(Font.BOLD, 17);
        LiSu_LS18B=font.deriveFont(Font.BOLD, 18);
        LiSu_LS19B=font.deriveFont(Font.BOLD, 19);
        LiSu_LS21B=font.deriveFont(Font.BOLD, 21);
        LiSu_LS22B=font.deriveFont(Font.BOLD, 22);
        LiSu_LS24B=font.deriveFont(Font.BOLD, 24);
        LiSu_LS26B=font.deriveFont(Font.BOLD, 26);
    }
    /** 字体:宋体-加粗:0-字体:15号 */
    public static final Font TEXT_COM_FONT = new Font("宋体", 0, 15);
    /** 字体:微软雅黑-加粗:0-字体:15号 */
    public static final Font TEXT_WRYH_12 = new Font("微软雅黑", 0, 12);
    public static final Font TEXT_WRYH_12B = new Font("微软雅黑", 1, 12);



    /** 7-16-HGC-end */

    // 翅膀品质对应颜色
    /** 把玩品质颜色 */
    public static final Color COLOR_PLAY = new Color(41, 191, 107);
    /** 贴身品质颜色 */
    public static final Color COLOR_PERSONAL = new Color(1, 251, 249);
    /** 珍藏品质颜色 */
    public static final Color COLOR_TREASURE = new Color(239, 238, 12);
    /** 无价品质颜色 */
    public static final Color COLOR_NONVALENT = new Color(219, 10, 205);
    /** 传世品质颜色 */
    public static final Color COLOR_CHENS = new Color(243, 94, 1);
    /** 神迹品质颜色 */
    public static final Color COLOR_SIGN = new Color(251, 0, 1);
    /** 装备精炼颜色 */
    public static final Color COLOR_REFINE = new Color(180,197,225);
    /** 装备服务大全类名 */
    public static final Color COLOR_CL_NAME= new Color(113,72,20);
    /** 装备服务大全类名1 */
    public static final Color COLOR_CL_BACK= new Color(197,169,118);
    /**游戏设置*/
    public static final Color COLOR_CL_Setting= new Color(170,204,153);
    //红木
    public static final Color COLOR_CL_RedMU= new Color(187,165,75);
    //法门标题
    public static final Color COLOR_FM_Title= new Color(251,216,51);
    //
    public static final Color COLOR_FM_gaormount= new Color(204,192,159);
    public static final Color COLOR_SHS= new Color(255,243,193);
    public static final Color COLOR_SHS_1= new Color(255,250,215);
    public static final Color COLOR_SHS_2= new Color(115,84,59);
    public static final Color COLOR_SHS_3= new Color(184,164,72);
    public static final Color COLOR_SHS_4= new Color(243,217,147);

    /** 按钮字体颜色1 */
    public static final Color COLOR_TEXT1 = new Color(114, 114, 114);
    /**
     * 字段名:COLOR_BTNPUTONG1 <br>
     * 普通按钮
     */
    public static Color[] COLOR_BTNPUTONG;
    public static Color[] COLOR_BTNPUTONG_Back;
    /**
     * 字段名:COLOR_BTNTEXT <br>
     * 文字按钮/选项卡按钮
     */
    public static Color[] COLOR_BTNTEXT;
    public static Color[] COLOR_SHE;
    public static Color[] COLOR_RED;

    //作废
    public static Color[] COLOR_ZHUJPANEL;
    public static Color[] COLOR_W;
    /**
     * 字段名:COLOR_BTNXUANXIANGKA <br>
     * 选项卡按钮
     */
    public static Color[] COLOR_BTNXUANXIANGKA;

    public static Color[] JIANG_JIANGHU;

    /** 首杀队伍颜色 */
    public static final Color COLOR_FIRSTTEAM = new Color(176, 180, 48);

    public static final Font TEXT_HURT2 = new Font("宋体", Font.BOLD, 14);// 战斗伤害字体
    public static Font nameFont = new Font("宋体", Font.BOLD, 16);// 名称字体

    public static final Font TEXT_FONT42 = new Font("楷体", Font.PLAIN, 18);

    public static final Font TEXT_FONT21 = new Font("楷体", Font.BOLD + Font.ITALIC, 17);

    public static final Font TEXT_FONT41 = new Font("微软雅体", Font.PLAIN, 20);
    public static final Font TEXT_MSG = new Font("微软雅体", Font.PLAIN, 14);

    public static final Font TEXT_FONT4 = new Font("微软雅体", Font.BOLD, 50);

    public static final Color COLOR_HIHT = new Color(8, 4, 8);
    public static final Color COLOR_NAME_BACKGROUND = new Color(27, 26, 18);
    public static final Color Color_BACK = new Color(0, 0, 0, 0);// 透明
    public static final Color Color_pet_reis = new Color(142, 222, 255);// 透明

    public static final Font TEXT_TIP = new Font("微软雅黑", 1, 15);
    public static final Font TEXT_RESULT = new Font("微软雅黑", Font.LAYOUT_LEFT_TO_RIGHT+Font.BOLD, 20);
    public static final Color COLOR_HURTR1 = new Color(217, 7, 37);
    public static final Color COLOR_HURTB1 = new Color(13, 116, 186);
    public static final Color COLOR_HURTB2 = new Color(0, 18, 31);
    public static final Color COLOR_HURTY1 = new Color(250, 245, 90);
    // NPC暗
    public static final Color COLOR_NPC0 = new Color(196, 199, 69);
    // NPC亮
    public static final Color COLOR_NPC1 = new Color(255, 255, 0);
    // NPC亮
    public static final Color COLOR_Y2 = Color.GREEN;
    // 0转
    public static final Color COLOR_NAME = new Color(102, 205, 0);
    // 1转
    public static final Color COLOR_NAME1 = new Color(255, 110, 0);
    // 玄宝 品质等级
    public static final Color COLOR_XB = new Color(245, 140, 15);
    // 2转
    public static final Color COLOR_NAME2 = new Color(62, 223, 236);
    // 3转
    public static final Color COLOR_NAME3 = new Color(255, 0, 0);
    // 飞升
    public static final Color COLOR_NAME4 = new Color(155, 48, 255);
    // 0转暗
    public static final Color COLOR_NAME5 = new Color(0, 111, 0);
    // 1转暗
    public static final Color COLOR_NAME6 = new Color(111, 67, 21);
    // 2转暗
    public static final Color COLOR_NAME7 = new Color(15, 70, 75);
    // 3转暗
    public static final Color COLOR_NAME8 = new Color(0, 0, 0);// 黑
    // 飞升暗
    public static final Color COLOR_NAME9 = new Color(85, 26, 139);
    // pet
    // 0转
    public static final Color COLOR_NAME_PET = new Color(238, 99, 99);
    // 1转
    public static final Color COLOR_NAME1_PET = new Color(255, 105, 180);
    // 2转
    public static final Color COLOR_NAME2_PET = new Color(153, 250, 204);
    // 3转
    public static final Color COLOR_NAME3_PET = Color.BLUE;
    public static final Color ComprehensiveTextColor = new Color(16,24,24);
    // 飞升
    public static final Color COLOR_NAME4_PET = new Color(155, 48, 255);
    // 称谓一
    public static final Color COLOR_title = new Color(92, 138, 183);
    // 称谓二
    public static final Color COLOR_title2 = new Color(135, 180, 235);
    // 称谓二
    public static final Color COLOR_White= new Color(255 ,255, 255);
    // 背包物品数量
    public static final Color COLOR_goods_quantity= new Color(188 ,188, 188);
    public static final Color COLOR_goods_two= new Color(185 ,185, 185);

    public static final Color COLOR_goods_blue= new Color(12,40,109);

    public static final Color COLOR_66BBAAFF= new Color(102,187,170);
    public static final Color COLOR_66BBAA_1F= new Color(43,100,90);

    public static final Color COLOR_CCDDDDFF= new Color(204,221,221);

    public static final Color COLOR_DDEE55FF= new Color(221,238,85);

    public static final Color COLOR_CCFFEE= new Color(204,255,238);

    public static final Color COLOR_808182= new Color(128,129,130);
    public static final Color COLOR_XZ= new Color(97,97,97);
    public static final Color COLOR_153= new Color(153,153,153);

    public static final Color COLOR_239= new Color(239,224,202);

    public static final Color COLOR_96= new Color(96,80,62);
    public static final Color COLOR_zhi= new Color(86,255,98);
    public static final Color tjcoor= new Color(162,152,201);


    
    /** 字段名:COLOR_FightingRound 战斗回合<br>
      *@time 2019年12月28日 上午10:08:16<br>
      */
    public static final Color COLOR_FightingRound = new Color(199, 252, 0);
    public static final Color COLOR_Fighting = new Color(102, 204, 187);
    public static final Color COLOR_orange = new Color(216,92,23);//橙色
    public static final Color COLOR_Fightingtext = new Color(55,83,131);
    public static final Color COLOR_value = new Color(208,250,243);
    public static final Color COLOR_FightingRound_Black = new Color(0, 0, 0,179);

    // 翅膀字体颜色
    public static final Color COLOR_Wing1 = new Color(187, 165, 75);
    public static final Color COLOR_Draw1 = new Color(210, 222, 122);
    public static final Color COLOR_TBule = new Color(87, 250, 255);
    public static final Color COLOR_Pack = new Color(36, 31, 0);//装备底图
    public static Color[] COLOR_BTNPUTONGS;//水墨
    static {
        COLOR_BTNPUTONG = new Color[3];
        COLOR_BTNPUTONG[0] = new Color(16, 24, 2);
        COLOR_BTNPUTONG[2] = COLOR_BTNPUTONG[1] = new Color(48, 88, 56);

        COLOR_BTNPUTONG_Back = new Color[3];
        COLOR_BTNPUTONG_Back[0] = new Color(0, 0, 0);
        COLOR_BTNPUTONG_Back[2] = COLOR_BTNPUTONG_Back[1] = new Color(0, 0, 0);

        COLOR_BTNTEXT = new Color[3];
        COLOR_BTNTEXT[0] = new Color(187, 221, 170);
        COLOR_BTNTEXT[1] = new Color(238, 255, 221);
        COLOR_BTNTEXT[2] = new Color(170, 187, 187);

        COLOR_SHE = new Color[3];
        COLOR_SHE[0] = new Color(16, 24, 24);
        COLOR_SHE[1] = new Color(48, 88, 56);
        COLOR_SHE[2] = new Color(16, 24, 24);

        COLOR_RED = new Color[3];
        COLOR_RED[0] = new Color(187, 165, 75);
        COLOR_RED[1] = new Color(217, 226, 106);
        COLOR_RED[2] = new Color(219, 229, 109);



        COLOR_ZHUJPANEL = new Color[3];
        COLOR_ZHUJPANEL[0] = new Color(238, 255, 221);
        COLOR_ZHUJPANEL[1] = new Color(255, 255, 255);
        COLOR_ZHUJPANEL[2] = new Color(204, 238, 255);

        COLOR_W = new Color[3];
        COLOR_W[0] = new Color(255, 255, 255);
        COLOR_W[1] = new Color(255, 255, 255);
        COLOR_W[2] = new Color(255, 255, 255);



        COLOR_BTNXUANXIANGKA = new Color[3];
        COLOR_BTNXUANXIANGKA[0] = new Color(23, 18, 22);
        COLOR_BTNXUANXIANGKA[2] = COLOR_BTNXUANXIANGKA[1] = new Color(40, 68, 40);
        //水墨
        COLOR_BTNPUTONGS = new Color[3];
        COLOR_BTNPUTONGS[0] =new Color(16, 24, 2);
        COLOR_BTNPUTONGS[2] = COLOR_BTNPUTONGS[1] =  new Color(48, 88, 56);

        JIANG_JIANGHU = new Color[3];
        JIANG_JIANGHU[0] =new Color(0,0,0);
        JIANG_JIANGHU[1] =new Color(71,47,71);
        JIANG_JIANGHU[2] =new Color(0, 0, 0);

    }

    /**
     * 根据转生系数获取颜色
     */
    public static Color getcolor(int zs) {
        switch (zs) {
        case -1:
            return Color.gray;
        case 0:
            return COLOR_NAME;
        case 1:
            return COLOR_NAME1;
        case 2:
            return COLOR_NAME2;
        case 3:
            return COLOR_NAME3;
        case 4:
            return COLOR_NAME4;
        }
        return COLOR_NAME;

    }

    /**
     * 根据转生系数获取暗颜色
     */
    public static Color getDarkColor(int zs) {
        switch (zs) {
        case 0:
            return COLOR_NAME5;
        case 1:
            return COLOR_NAME6;
        case 2:
            return COLOR_NAME7;
        case 3:
            return COLOR_NAME8;
        case 4:
            return COLOR_NAME9;
        }
        return COLOR_NAME;

    }

    /**
     * 根据转生系数获取召唤兽颜色
     */
    public static Color getPetcolor(int zs) {
        switch (zs) {
        case -1:
            return Color.gray;
        case 0:
            return COLOR_NAME_PET;
        case 1:
            return COLOR_NAME1_PET;
        case 2:
            return COLOR_NAME2_PET;
        case 3:
            return COLOR_NAME3_PET;
        case 4:
            return COLOR_NAME4_PET;
        }
        return COLOR_NAME;

    }

    public static final Color TEXT_NAME_NPC_COLOR = new Color(255, 255, 0);
    public static final Color COLOR_NAME_HIGHLIGHT = Color.RED;
    private static Map<String, Color> colors = new HashMap<String, Color>();
    /**
     * @param color
     * @return
     */
    public static Color getColor(String color) {
        Color c = colors.get(color);
        if (c == null) {
        	c = Color.decode("0x" + color.substring(2));
        	colors.put(color, c);
        }
        return c;
    }

    /**
     * 依据等级获取颜色
     */
    public static Color gradeForColor(int grade) {
        Color returnColor;
        // 依据等级获取转生系数
        String gradeMes = AnalysisString.lvl(grade);
        // 依据转进行切割
        String intMes[] = gradeMes.split("转");
        if (intMes.length > 1) {
            returnColor = getcolor(Integer.valueOf(intMes[0]));
        } else {
            returnColor = getcolor(4);
        }
        return returnColor;
    }
}
