package com.tool.pool;

import com.tool.image.ManimgAttribute;
import org.come.bean.NpcInfoBean;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * NPC对象池 - 用于复用ManimgAttribute对象，减少内存分配
 */
public class NpcObjectPool {
    private static final ConcurrentLinkedQueue<ManimgAttribute> pool = new ConcurrentLinkedQueue<>();
    private static final int MAX_POOL_SIZE = 100; // 最大池大小
    private static int poolSize = 0;
    
    // 统计信息
    private static long totalAcquired = 0;
    private static long totalReused = 0;
    private static long totalCreated = 0;
    
    /**
     * 从对象池获取NPC对象
     */
    public static ManimgAttribute acquire(NpcInfoBean npcInfo) {
        totalAcquired++;
        
        ManimgAttribute npc = pool.poll();
        if (npc != null) {
            poolSize--;
            totalReused++;
            // 重置对象状态
            npc.reset(npcInfo);
            return npc;
        } else {
            totalCreated++;
            // 创建新对象
            return new ManimgAttribute(npcInfo);
        }
    }
    
    /**
     * 将NPC对象归还到对象池
     */
    public static void release(ManimgAttribute npc) {
        if (npc == null) return;
        
        // 如果池未满，则归还对象
        if (poolSize < MAX_POOL_SIZE) {
            npc.cleanup(); // 清理资源
            pool.offer(npc);
            poolSize++;
        }
        // 如果池已满，让对象被GC回收
    }
    
    /**
     * 批量归还NPC对象
     */
    public static void releaseAll(java.util.List<ManimgAttribute> npcs) {
        if (npcs == null) return;
        
        for (ManimgAttribute npc : npcs) {
            release(npc);
        }
    }
    
    /**
     * 清空对象池
     */
    public static void clear() {
        ManimgAttribute npc;
        while ((npc = pool.poll()) != null) {
            npc.cleanup();
        }
        poolSize = 0;
    }
    
    /**
     * 获取对象池统计信息
     */
    public static String getPoolStats() {
        double reuseRate = totalAcquired > 0 ? (double) totalReused / totalAcquired * 100 : 0;
        return String.format("NPC对象池统计: 池大小=%d/%d, 总获取=%d, 复用=%d, 新建=%d, 复用率=%.1f%%", 
                           poolSize, MAX_POOL_SIZE, totalAcquired, totalReused, totalCreated, reuseRate);
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStats() {
        totalAcquired = 0;
        totalReused = 0;
        totalCreated = 0;
    }
}
