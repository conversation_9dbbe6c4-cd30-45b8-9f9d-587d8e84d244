package jxy2.soul;

import com.tool.btn.MoBanBtn;
import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;


public class ResetSoulSkillBtn extends MoBanBtn {
    public int typeBtn;
    public ResetSoulSkillJPanl resetSoulSkillJPanl;
    public ResetSoulSkillBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, ResetSoulSkillJPanl resetSoulSkillJPanl, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.resetSoulSkillJPanl = resetSoulSkillJPanl;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        //重置逻辑
        Goodstable goodstable = GoodsListFromServerUntil.getGoodsName(resetSoulSkillJPanl.goodstable.getGoodsname());
        if (goodstable==null){ ZhuFrame.getZhuJpanel().addPrompt2("缺少物品");return;}
        Goodstable zbgoods = GoodsListFromServerUntil.fushis.get(resetSoulSkillJPanl.goodstable.getRgid());
        if (zbgoods!=null){ZhuFrame.getZhuJpanel().addPrompt2("#R无法重置装备中的魔晶");return;}
        //在包裹找寻对应物品名字的重置丹
        String msg = Agreement.getAgreement().ResetSkillAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(
                        resetSoulSkillJPanl.goodstable)+"&"+GsonUtil.getGsonUtil().getgson().toJson(goodstable));
        SendMessageUntil.toServer(msg);

    }
}
