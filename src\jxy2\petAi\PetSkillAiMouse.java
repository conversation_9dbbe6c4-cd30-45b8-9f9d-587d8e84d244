package jxy2.petAi;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import org.come.until.CutButtonImage;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class Pet<PERSON>kill<PERSON><PERSON><PERSON>ouse implements MouseListener {
    public PetSkillAiJPanel petSkillAiJPanel;
    public int index,type;
    public PetSkillAiMouse(PetSkillAiJPanel petSkillAiJPanel, int index,int type) {
        this.petSkillAiJPanel = petSkillAiJPanel;
        this.index = index;
        this.type = type;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        if (type==0){
            petSkillAiJPanel.xzname = petSkillAiJPanel.Rebirth[index].getText();
            Selectwj(index);
        }else {
            Selectwjs(index);
            if (index==3){
                petSkillAiJPanel.stopmsg  = true;
            }else {
                petSkillAiJPanel.value = Double.parseDouble(petSkillAiJPanel.conditionL[index].getText());
            }
        }

    }

    private void Selectwj(int index) {
        petSkillAiJPanel.textimg[index].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz84,"defaut.wdf"));
        petSkillAiJPanel.Rebirth[index].setForeground(UIUtils.COLOR_Wing1);
        for (int i = 0; i < petSkillAiJPanel.textimg.length; i++) {
            if (i!=index){
                petSkillAiJPanel.textimg[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
                petSkillAiJPanel.Rebirth[i].setForeground(UIUtils.COLOR_White);
            }
        }
    }
    private void Selectwjs(int index) {
        petSkillAiJPanel.cindImg[index].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz84,"defaut.wdf"));
        for (int i = 0; i < petSkillAiJPanel.cindImg.length; i++) {
            if (index==3){

            }else {
                if (i!=index){
                    petSkillAiJPanel.cindImg[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
                    petSkillAiJPanel.stopmsg  = false;
                }
            }

        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }
}
