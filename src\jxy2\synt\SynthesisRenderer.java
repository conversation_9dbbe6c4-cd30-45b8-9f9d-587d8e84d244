package jxy2.synt;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;

public class SynthesisRenderer  extends JPanel implements ListCellRenderer{
    public SynthesisJPanl synthesisJPanl;
    public SynthesisRenderer() {

    }
    @Override
    public Dimension getPreferredSize() {
        return new Dimension(144, 30);
    }
    @Override
    public Component getListCellRendererComponent(JList list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
        SynthesisRenderer mr = new SynthesisRenderer();
        mr.removeAll();
        JLabel jLabel = new JLabel();
        jLabel.setForeground(new Color(187, 255, 170));
        jLabel.setText(value+"");
        jLabel.setFont(UIUtils.MSYH_HY13);
        jLabel.setBounds(50,50,144,27*index);
        jLabel.setVerticalTextPosition(SwingConstants.CENTER);
        jLabel.setHorizontalTextPosition(SwingConstants.CENTER);
        jLabel.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz118,144,27,"defaut.wdf"));
        if (SynthesisJPanl.idx == index&&!isSelected) {
            jLabel.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz119,144,27,"defaut.wdf"));
        }else if (isSelected) {
            jLabel.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz120,144,27,"defaut.wdf"));
        } else {
            jLabel.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz118,144,27,"defaut.wdf"));
        }
        mr.setPreferredSize(new Dimension(144,27*index));
        mr.setOpaque(false);
        mr.add(jLabel);
        return mr;
    }
}
