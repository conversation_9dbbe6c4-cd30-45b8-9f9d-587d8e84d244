package com.tool.ModerateTask;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AllTask {
	//任务
	private Map<Integer, TaskData> allTaskData;
	private Map<Integer, TaskSet>  allTaskSet;
	
	private List<Integer> setList;//会自动提示的任务id
	private List<String> typeList;//拥有的任务类型
	public void getSetId(List<Integer> list){
		getSetList();
		S:for (int i=setList.size()-1;i>=0;i--) {
			int setId=setList.get(i);
			if (list.contains(setId)) {continue;}
			for (int j = 0; j < list.size(); j++) {
				if (list.get(j)<setId) {
					list.add(j,setId);
					continue S;
				}
			}
			list.add(setId);
		}
	}
	private void init(){
		setList=new ArrayList<>();//大到小
		typeList=new ArrayList<>();
		S:for (Integer value:allTaskSet.keySet()) {
			for (int i = 0; i < setList.size(); i++) {
				if (value<setList.get(i)) {
					setList.add(i, value);
					continue S;
				}
			}
			setList.add(value);
		}
		for (int i = setList.size()-1;i>=0; i--) {
			TaskSet taskSet=allTaskSet.get(setList.get(i));
			if (taskSet.getTaskMsgID()==0) {
				setList.remove(i);	
			}
			if (typeList.contains(taskSet.getTaskType())) {
				continue;
			}
			typeList.add(taskSet.getTaskType());
			
		}		
	}
	public List<String> getTypeList() {
		if (setList==null) {init();}
		return typeList;
	}
	public List<Integer> getSetList() {
		if (setList==null) {init();}
		return setList;
	}
	
	public Map<Integer, TaskData> getAllTaskData() {
		return allTaskData;
	}
	public void setAllTaskData(Map<Integer, TaskData> allTaskData) {
		this.allTaskData = allTaskData;
	}
	public Map<Integer, TaskSet> getAllTaskSet() {
		return allTaskSet;
	}
	public void setAllTaskSet(Map<Integer, TaskSet> allTaskSet) {
		this.allTaskSet = allTaskSet;
	}
}
