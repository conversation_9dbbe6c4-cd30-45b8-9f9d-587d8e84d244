package jxy2.qqiubook;

import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.ImgZoom;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import javax.swing.event.TreeExpansionEvent;
import javax.swing.event.TreeExpansionListener;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.plaf.basic.BasicScrollBarUI;
import javax.swing.plaf.basic.BasicTreeUI;
import javax.swing.tree.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class QianqiuBookJPanel extends JPanel {
    private JTree jTree;
    private DefaultMutableTreeNode top;
    private ConcurrentHashMap<Integer, QiqiuTreeNode> guideBeanConcurrentHashMap = new ConcurrentHashMap<>();
    private JScrollPane paneLeft, paneRight;
    private QiqiuTreeNode qiqiuTreeNode;
    private QianqiuBookBtn btnBasis;
    private boolean isOverviewSelected = true; // 添加一个标记来跟踪总览是否被选中
    private int qid;
    private int total = 0;//总功绩
    private JList<QianqiuBookModel> qianqiuBookModelJList;
    private DefaultListModel<QianqiuBookModel> qianqiuBookModelDefaultListModel = new DefaultListModel<>();
    private Map<Integer, QianqiuBookModel> qianqiuBookModelHashMap = new HashMap<>();
    public static int idx;
    private JLabel[] DataText = new JLabel[10];
    public String[] text = {
            "总功绩", "人物成长", "人物数据", "日常任务", "其他玩法", "庭院作坊", "人物关系", "升级任务", "缤纷外观", "决斗挑战"
             };
    private int okac = 0,rwkac = 0; // 已完成的功绩值

    public QianqiuBookJPanel() {
        this.setPreferredSize(new Dimension(660, 476));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this, 151, Util.SwitchUI == 1 ? 660 : 642);
        
        // 初始化按钮
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1015" : "0x6FAB1021";
        btnBasis = new QianqiuBookBtn(iconResource, 1, UIUtils.COLOR_BTNTEXT, "千秋功绩", 0, this, "");
        btnBasis.btnchange(2);
        btnBasis.setBounds(55, 16, 95, 33);
        this.add(btnBasis);

        for (int i = 0; i < DataText.length; i++) {
            DataText[i] = TeststateJpanel.GJpanelText(Color.WHITE,UIUtils.TEXT_FONT);
            if (i<6) {
                DataText[i].setBounds(i == 0 ? 408:307, 325 + i * 23, i == 0 ? 300 : 100, 19);
            }else {
                DataText[i].setBounds(505, 348 + (i-6) * 23, 100, 19);
            }
//            DataText[i].setText("99999/99999");
            this.add(DataText[i]);
        }

        initPanels();
    }

    private void initPanels() {
        // 左侧树形面板
        paneLeft = new JScrollPane(getjTree());
        paneLeft.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        paneLeft.getVerticalScrollBar().setUI(Juitil.createUI(Util.SwitchUI));
        paneLeft.getVerticalScrollBar().setUnitIncrement(50);
        paneLeft.getViewport().setOpaque(false);
        paneLeft.setOpaque(false);
        paneLeft.setBounds(48, 90, 160, 362);
        paneLeft.setBorder(BorderFactory.createEmptyBorder());
        paneLeft.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(paneLeft);

        qianqiuBookModelJList = new JList<QianqiuBookModel>();
        qianqiuBookModelJList.setSelectionForeground(Color.white);
        qianqiuBookModelJList.setForeground(Color.white);
        qianqiuBookModelJList.setFont(UIUtils.TEXT_HY16);
        qianqiuBookModelJList.setCellRenderer(null);
        qianqiuBookModelJList.setModel(qianqiuBookModelDefaultListModel);
        qianqiuBookModelJList.addMouseListener(new QianqiuBookMouse(qianqiuBookModelJList));
        qianqiuBookModelJList.addMouseMotionListener(new QianqiuBookMouse(qianqiuBookModelJList));
        qianqiuBookModelJList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        qianqiuBookModelJList.removeAll();
        qianqiuBookModelJList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        qianqiuBookModelJList.setOpaque(false);

        // 右侧内容面板
        paneRight = new JScrollPane(qianqiuBookModelJList);
//        paneRight = new JScrollPane(contentPanel);
        paneRight.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        paneRight.getVerticalScrollBar().setUI(Juitil.createUI(Util.SwitchUI));
        paneRight.getVerticalScrollBar().setUnitIncrement(30);
        paneRight.getViewport().setOpaque(false);
        paneRight.setOpaque(false);
        paneRight.setBounds(220, 90, 390, 362);
        paneRight.setBorder(BorderFactory.createEmptyBorder());
        paneRight.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(paneRight);


    }

/**
 * 刷新列表组件高度，只展开当前点击项，其它全部收起
 * @param index 当前点击的列表项索引
 * @param newHeight 当前项的新高度（展开高度）
 */
public void refreshListHeight(int index, int newHeight) {
    // 定义收起时的默认高度
    int defaultHeight = 63;
    // 检查索引有效性
    if (index < 0 || index >= qianqiuBookModelDefaultListModel.size()) {
        qianqiuBookModelJList.setPreferredSize(new Dimension(402, 177));
        paneRight.getViewport().setViewSize(new Dimension(402, 177));
        return;
    }
    // 遍历所有条目
    for (int i = 0; i < qianqiuBookModelDefaultListModel.size(); i++) {
        QianqiuBookModel model = qianqiuBookModelDefaultListModel.get(i);
        // 只展开当前点击项，其它全部收起
        if (i == index) {
            model.setH(newHeight); // 设置为展开高度
            model.setPreferredSize(new Dimension(402, newHeight));
        } else {
            model.setH(defaultHeight); // 其它项收起
            model.setPreferredSize(new Dimension(402, defaultHeight));
        }
        model.refreshData(); // 刷新数据
        qianqiuBookModelDefaultListModel.setElementAt(model, i); // 通知模型已更改
    }
    // 重新计算总高度
    int totalHeight = 0;
    for (int i = 0; i < qianqiuBookModelDefaultListModel.size(); i++) {
        totalHeight += qianqiuBookModelDefaultListModel.get(i).getH();
    }
    totalHeight = Math.max(289, totalHeight); // 保证最小高度
    // 更新列表和滚动面板大小
    qianqiuBookModelJList.setPreferredSize(new Dimension(402, totalHeight));
    paneRight.getViewport().setViewSize(new Dimension(402, totalHeight));
}

    /**
     * 重新计算已完成的功绩值
     */
    private void recalculateCompletedMerit() {
        okac = 0;
        rwkac = 0;
        // 使用guideBeanConcurrentHashMap而不是qianqiuBookModelHashMap
        for (QiqiuTreeNode node : guideBeanConcurrentHashMap.values()) {
            if (node != null && node.getComplete() == 1
                && node.getMeritvalue() != null
                && !node.getMeritvalue().isEmpty()) {
                try {
                    okac += Integer.parseInt(node.getMeritvalue().trim());
                    rwkac++;
                } catch (NumberFormatException e) {
                    System.err.println("无效的功绩值: " + node.getMeritvalue());
                }
            }
        }
        // 更新显示
        if (DataText[0] != null) {
            QiqiuTreeNode overview = UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().get(1);
            DataText[0].setText(rwkac + "/" + overview.getTotal_achievement());

        }

        DataText[1].setText(rwkac+"/"+getTotalTaskCount());

    }

    public void UpdateData(QiqiuTreeNode newNode) {
        if (newNode == null) return;

        // 首先更新总数据Map
        QiqiuTreeNode oldNode = guideBeanConcurrentHashMap.get(newNode.getId());
        if (oldNode != null) {
            // 如果之前完成现在未完成，减去旧的功绩值
            if (oldNode.getComplete() == 1 && newNode.getComplete() == 0) {
                try {
                    okac -= Integer.parseInt(oldNode.getMeritvalue().trim());
                } catch (NumberFormatException e) {
                    System.err.println("无效的功绩值: " + oldNode.getMeritvalue());
                }
            }
            // 如果之前未完成现在完成，添加新的功绩值
            else if (oldNode.getComplete() == 0 && newNode.getComplete() == 1) {
                try {
                    okac += Integer.parseInt(newNode.getMeritvalue().trim());
                } catch (NumberFormatException e) {
                    System.err.println("无效的功绩值: " + newNode.getMeritvalue());
                }
            }
            oldNode.updateFrom(newNode);
        } else {
            guideBeanConcurrentHashMap.put(newNode.getId(), newNode);
            if (newNode.getComplete() == 1) {
                try {
                    okac += Integer.parseInt(newNode.getMeritvalue().trim());
                } catch (NumberFormatException e) {
                    System.err.println("无效的功绩值: " + newNode.getMeritvalue());
                }
            }
        }

        // 更新显示模型
        QianqiuBookModel modelToUpdate = qianqiuBookModelHashMap.get(newNode.getId());
        if (modelToUpdate != null) {
            modelToUpdate.initData(newNode);
//            qianqiuBookModelJList.repaint();
        } else {
            // 如果是新节点，且属于当前显示的分类，则添加到列表中
            QiqiuTreeNode currentCategory = qiqiuTreeNode;
            if (currentCategory != null && newNode.getParentId() == currentCategory.getId()) {
                QianqiuBookModel newModel = new QianqiuBookModel(402, 63);
                newModel.setBounds(0, 0, 402, 63);
                newModel.initData(newNode);
                qianqiuBookModelDefaultListModel.addElement(newModel);
                qianqiuBookModelHashMap.put(newNode.getId(), newModel);
                updateListSize();
            }
        }

        // 如果在总览页面，更新显示
        if (qid == 1) {
            recalculateCompletedMerit();
        }
    }

    // 自定义ListCellRenderer
    private class QianqiuBookCellRenderer implements ListCellRenderer<QianqiuBookModel> {
        @Override
        public Component getListCellRendererComponent(JList<? extends QianqiuBookModel> list,
                                                    QianqiuBookModel value,
                                                    int index,
                                                    boolean isSelected,
                                                    boolean cellHasFocus) {
            // 设置组件大小
            value.setPreferredSize(new Dimension(402, value.getH()));
            return value;
        }
    }

    /**
     * 初始化指定分类下的列表
     * @param categoryNode 分类节点
     */
    public void init(QiqiuTreeNode categoryNode) {
        // 保存当前已有的数据
        Map<Integer, QianqiuBookModel> existingData = new HashMap<>(qianqiuBookModelHashMap);

        qianqiuBookModelDefaultListModel.clear();
        qianqiuBookModelHashMap.clear();
        qianqiuBookModelJList.removeAll();
        qianqiuBookModelJList.setCellRenderer(new QianqiuBookCellRenderer());

        // 加载该分类下的所有节点
        for (QiqiuTreeNode node : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
            if (node.getParentId() == categoryNode.getId() && node.getId() >= 100) {
                QianqiuBookModel qianqiuBookModel;
                // 检查是否存在已更新的数据
                if (existingData.containsKey(node.getId())) {
                    // 使用已更新的数据
                    qianqiuBookModel = existingData.get(node.getId());
                } else {
                    // 创建新的数据
                    qianqiuBookModel = new QianqiuBookModel(402, 63);
                    qianqiuBookModel.setBounds(0, 0, 402, 63);
                    qianqiuBookModel.initData(node);
                }
                qianqiuBookModelDefaultListModel.addElement(qianqiuBookModel);
                qianqiuBookModelHashMap.put(node.getId(), qianqiuBookModel);
            }
        }

        // 重新计算已完成的功绩值
        recalculateCompletedMerit();
        updateListSize();
    }





    /** 打开初始化数据 */
    public void initData() {
        fillTreeFromGuideBeanMap();
        recalculateCompletedMerit();
    }


    public String[] mingw = {"获得官职","总功绩值"};

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "千秋册");
        } else {
            Juitil.SheNewShow(g, 660, 476, "千秋册", 337, 80);
        }
        // 420 183
        Juitil.MenuNavigationBar(g,515,600,596);
        UiBack.UIResource.BACK_1.draw(g,this);
        if (qid==1){
            int nx = Util.SwitchUI == 1?0:-30 ;
            int ny = Util.SwitchUI == 1?0:10 ;
            int nh = Util.SwitchUI == 1 ? 0 : 5;
            int nw = Util.SwitchUI == 1 ? 0 : 5;
            int lineWidth =  Util.SwitchUI == 1 ? 19 : 23;
            Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0004:Juitil.red_0018,
                    Util.SwitchUI==1?215:185,
                    Util.SwitchUI==1?110:121,
                    Util.SwitchUI==1?416:424,
                    Util.SwitchUI==1?183:188,
                    1);
            paneRight.setBounds(215+nx, 114+ny, 415+nw, 176+nh);

            Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0021:Juitil.red_0046,
                    Util.SwitchUI==1?613:585,
                    Util.SwitchUI==1?110:124,
                    Util.SwitchUI==1?17:22,
                    Util.SwitchUI==1?176:181,
                    1);

            g.drawImage(Util.SwitchUI==1?Juitil.tz347.getImage():Juitil.tz348.getImage(),
                    Util.SwitchUI==1?215:184,
                    Util.SwitchUI==1?297:312,
                    230,
                    Util.SwitchUI==1?23:25,
                    null);


            DrawTextCoordinates(g, lineWidth);
            for (int i = 0; i < 2; i++) {
                Juitil.Textdrawing(g, mingw[i], 219+i* 130, 77, UIUtils.ComprehensiveTextColor, UIUtils.NEWTX_HY16B);
            }

        }else if (qid > 10 && qid< 100){
            if (Util.SwitchUI==1){
                extracted(g,213,137, 420, 302);
            }else {
                extracted(g,187,128, 420, 310);
            }
        }


        Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0021:Juitil.red_0046,
                Util.SwitchUI==1?190:158,
                Util.SwitchUI==1?87:100,
                Util.SwitchUI==1?17:21,
                368,
                1);
    }
    //
    private static void extracted(Graphics g,int x,int y,int w,int h) {
        Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0009:Juitil.red_0009, x,y, w, h, 1);
    }

    private void DrawTextCoordinates(Graphics g, int lineWidth) {
        ImgZoom imgZoom = Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0005;
        ImgZoom One = Util.SwitchUI == 1 ? Juitil.she_0024 : Juitil.red_0045;
        Color color = Util.SwitchUI == 1 ? UIUtils.ComprehensiveTextColor : UIUtils.COLOR_Wing1;
        int h =Util.SwitchUI == 1 ? 15 : 12;
        Font font = Util.SwitchUI == 1 ? UIUtils.NEWTX_HY16B : UIUtils.TEXT_HYJ16B;
        for (int i = 0; i < 10; i++) {

            if (i<6) {
                Juitil.ImngBack(g, imgZoom ,
                        Util.SwitchUI==1? 290:264,
                        Util.SwitchUI==1? (326 + i * 23) :(339 + i * 23),
                        i == 0 ? 300 : 100,
                        lineWidth, 1);

                Juitil.Textdrawing(g, text[i],
                         Util.SwitchUI==1?(i == 0 ? 231:216):(i == 0 ? 209:194),
                        Util.SwitchUI==1?341 + i * 23:355 + i * 23,
                        color, font);



                TextLeng(g, One, i, h);

                DataText[i].setBounds(
                        Util.SwitchUI==1? i == 0 ? 408: 307:i == 0 ? 380:280,
                        Util.SwitchUI==1?325 + i * 23:338 + i * 23,
                        i == 0 ? 300 : 100,
                        19);

            }else {
                Juitil.ImngBack(g, imgZoom,
                        Util.SwitchUI==1?487:461,
                        Util.SwitchUI==1?349 + (i-6) * 23:362 + (i-6) * 23,
                        100,
                        lineWidth, 1);
                Juitil.Textdrawing(g, text[i],
                        Util.SwitchUI==1?412:391,
                        Util.SwitchUI==1?364 + (i-6) * 23:378 + (i-6) * 23,
                        color, font);
                Juitil.ImngBack(g, One,
                        Util.SwitchUI==1?488:465,
                        Util.SwitchUI==1?351 + (i-6) * 23:367 + (i-6) * 23,
                        Util.SwitchUI==1?98:90,
                        h, 1);
                DataText[i].setBounds(
                        Util.SwitchUI==1?505:477,
                        Util.SwitchUI==1?348 + (i-6) * 23:361 + (i-6) * 23,
                        100,
                       19);
            }
        }
    }

    private void TextLeng(Graphics g, ImgZoom One, int i, int h) {
        int maxWidth = Util.SwitchUI == 1 ? 298 : 290; // 进度条最大宽度
        int progressWidth;
        QiqiuTreeNode node = UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().get(1);
        if (i == 0) { // 功绩进度条
            // 计算进度比例 (已完成/总功绩)，确保不会除零
            double progressRatio = (node.getTotal_achievement() > 0) ?
                (double) rwkac / node.getTotal_achievement() : 0;
            // 根据比例计算实际宽度，但不超过最大宽度
            progressWidth = (int) (maxWidth * Math.min(progressRatio, 1.0));
        } 
        else if (i == 1) { // 人物进度条
            int totalTasks = getTotalTaskCount();
            // 计算已完成任务比例，确保不会除零
            double progressRatio = (totalTasks > 0) ? 
                (double) rwkac / totalTasks : 0;
            // 计算人物进度条最大宽度（比功绩条短）
            int personMaxWidth = Util.SwitchUI == 1 ? 98 : 90;
            progressWidth = (int) (personMaxWidth * progressRatio);
        } 
        else { // 其他情况使用固定宽度
            progressWidth = 98;
        }
    
        // 绘制进度条背景
        Juitil.ImngBack(g, One,
                Util.SwitchUI == 1 ? 291 : 268,
                Util.SwitchUI == 1 ? 328 + i * 23 : 344 + i * 23,
                progressWidth,
                h,
                1);
    }

    public void updateButtonImages(int uiType) {
        String panelName = this.getClass().getSimpleName();
        if (uiType != 1) {
            Juitil.adjustFrameSize(634, 497, 151);
        } else {
            Juitil.adjustFrameSize(660, 476, 151);
        }
        Juitil.addClosingButtonToPanel(this, 151, uiType == 1 ? 660 : 642);

        BasicScrollBarUI scrollBarUI2 = Juitil.createUI(uiType);
        JScrollBar oldVBar2 = paneLeft.getVerticalScrollBar();
        JScrollBar newVBar2 = new JScrollBar(JScrollBar.VERTICAL);
        newVBar2.setValues(
                oldVBar2.getValue(),
                oldVBar2.getVisibleAmount(),
                oldVBar2.getMinimum(),
                oldVBar2.getMaximum()
        );
        newVBar2.setUI(scrollBarUI2);
        paneLeft.setVerticalScrollBar(newVBar2);
        paneLeft.getVerticalScrollBar().setUnitIncrement(50);

        BasicScrollBarUI scrollBarUI3 = Juitil.createUI(uiType);
        JScrollBar oldVBar3 = paneRight.getVerticalScrollBar();
        JScrollBar newVBar3 = new JScrollBar(JScrollBar.VERTICAL);
        newVBar3.setValues(
                oldVBar3.getValue(),
                oldVBar3.getVisibleAmount(),
                oldVBar3.getMinimum(),
                oldVBar3.getMaximum()
        );
        newVBar3.setUI(scrollBarUI3);
        paneRight.setVerticalScrollBar(newVBar3);
        paneRight.getVerticalScrollBar().setUnitIncrement(50);
        UiBack.getComponentStyle(panelName, "paneLeft").applyToScrollBar(paneLeft);
    }

    /** 获取树结构 */
    public JTree getjTree() {
        if (jTree == null) {
            jTree = new JTree(getTop());
            jTree.setOpaque(false);
            jTree.putClientProperty("JTree.lineStyle", "None");
            BasicTreeUI treeUI = (BasicTreeUI) jTree.getUI();
            treeUI.setLeftChildIndent(0);
            treeUI.setRightChildIndent(0);

            // 自定义节点显示
            DefaultTreeCellRenderer cellRenderer = new DefaultTreeCellRenderer() {
                private QiqiuTreeNode currentNode = null;
                private boolean selected = false;

                @Override
                public Component getTreeCellRendererComponent(JTree tree, Object value,
                                                              boolean selected, boolean expanded, boolean leaf, int row, boolean hasFocus) {
                    super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);
                    this.selected = selected;

                    DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                    if (node.getUserObject() instanceof QiqiuTreeNode) {
                        currentNode = (QiqiuTreeNode) node.getUserObject();
                        setText("");
                        if (currentNode.getId() == 1) {
                            setIcon(null);
                        }
                        setPreferredSize(new Dimension(160, 25));
                    }
                    return this;
                }

                @Override
                protected void paintComponent(Graphics g) {
                    if (currentNode != null) {
                        Graphics2D g2d = (Graphics2D) g;
                        if (currentNode.getId() >= 1 && currentNode.getId() <= 10){
                            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
                        }else {
                            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                        }

                        // 绘制背景 - 铺满整个组件宽度
                        if (selected || (currentNode.getId() == 1 && isOverviewSelected)) {
                            g2d.setColor(new Color(99, 93, 90));
                            g2d.fillRect(-100, 0, getWidth() + 200, getHeight());
                        }

                        // 绘制图标
                        if (getIcon() != null) {
                            getIcon().paintIcon(this, g2d, 0, (getHeight() - getIcon().getIconHeight()) / 2);
                        }

                        g2d.setFont(UIUtils.TEXT_FONT15);
                        String text = currentNode.getId() == 1 ? "总览" : currentNode.getName();

                        FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                        int textX = getIcon() != null ? getIcon().getIconWidth() + getIconTextGap() : 15;
                        int textY = (getHeight() + fm.getAscent() - fm.getDescent()) / 2;

                        g2d.setColor(Color.WHITE);
                        g2d.drawString(text, textX, textY);
                        setBorder(BorderFactory.createEmptyBorder());
                    }
                }
            };
            cellRenderer.setLeafIcon(null);
            try {
                cellRenderer.setOpenIcon(CutButtonImage.cuts("inkImg/button/B108.png")[0]);
                cellRenderer.setClosedIcon(CutButtonImage.cuts("inkImg/button/B109.png")[0]);
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            // 设置渲染器的全局属性
            cellRenderer.setTextSelectionColor(Color.WHITE);
            cellRenderer.setTextNonSelectionColor(Color.WHITE);
            cellRenderer.setBackgroundSelectionColor(new Color(99, 93, 90));
            cellRenderer.setBackgroundNonSelectionColor(new Color(0, 0, 0, 0));
            cellRenderer.setBorderSelectionColor(null);
            jTree.setCellRenderer(cellRenderer);
            jTree.setRootVisible(false);

            // 设置树的行高
            jTree.setRowHeight(20);

            // 设置树的首选大小
            jTree.setPreferredSize(new Dimension(160, 363));

            // 单击展开/收起节点
            jTree.addMouseListener(new MouseAdapter() {
                @Override
                public void mousePressed(MouseEvent e) {
                    if (SwingUtilities.isLeftMouseButton(e)) {
                        TreePath path = jTree.getPathForLocation(e.getX(), e.getY());
                        if (path != null) {
                            DefaultMutableTreeNode node = (DefaultMutableTreeNode) path.getLastPathComponent();
                            if (node.getUserObject() instanceof QiqiuTreeNode) {
                                QiqiuTreeNode qNode = (QiqiuTreeNode) node.getUserObject();
                                // 更新总览选中状态
                                isOverviewSelected = (qNode.getId() == 1);
                                qid = qNode.getId();
                                qiqiuTreeNode = qNode;
                                // 如果是主节点（ID 2-10）
                                if (qNode.getId() >= 2 && qNode.getId() <= 10) {
                            if (jTree.isExpanded(path)) {
                                jTree.collapsePath(path);
                            } else {
                                jTree.expandPath(path);
                            }
                        }
                    }
                        }
                        jTree.repaint();
                    }
                }
            });

            // 添加树节点展开监听器
            jTree.addTreeExpansionListener(new TreeExpansionListener() {
                @Override
                public void treeExpanded(TreeExpansionEvent event) {
                    SwingUtilities.invokeLater(() -> {
                        int totalHeight = jTree.getRowCount() * jTree.getRowHeight();
                        jTree.setPreferredSize(new Dimension(160, Math.max(363, totalHeight)));
                        paneLeft.getViewport().setViewSize(new Dimension(160, Math.max(363, totalHeight)));
                        paneLeft.revalidate();
                        paneLeft.repaint();
                    });
                }

                @Override
                public void treeCollapsed(TreeExpansionEvent event) {
                    SwingUtilities.invokeLater(() -> {
                        int totalHeight = jTree.getRowCount() * jTree.getRowHeight();
                        jTree.setPreferredSize(new Dimension(160, Math.max(363, totalHeight)));
                        paneLeft.getViewport().setViewSize(new Dimension(160, Math.max(363, totalHeight)));
                        paneLeft.revalidate();
                        paneLeft.repaint();
                    });
                }
            });

            // 添加树节点选择监听器
            jTree.addTreeSelectionListener(new TreeSelectionListener() {
                @Override
                public void valueChanged(TreeSelectionEvent e) {
                    DefaultMutableTreeNode node = (DefaultMutableTreeNode)
                            jTree.getLastSelectedPathComponent();

                    // 只有当实际选中节点时才更新面板
                    if (node != null && node.getUserObject() instanceof QiqiuTreeNode) {
                        QiqiuTreeNode qNode = (QiqiuTreeNode) node.getUserObject();
                        updateContentPanel(qNode);
                    }
                }
            });
        }
        return jTree;
    }

    public void setjTree(JTree jTree) {
        this.jTree = jTree;
    }

    public DefaultMutableTreeNode getTop() {
        if (top == null) {
            top = new DefaultMutableTreeNode("");
        }
        return top;
    }

    public void setTop(DefaultMutableTreeNode top) {
        this.top = top;
    }

    public QiqiuTreeNode getQiqiuTreeNode() {
        return qiqiuTreeNode;
    }

    public void setQiqiuTreeNode(QiqiuTreeNode qiqiuTreeNode) {
        this.qiqiuTreeNode = qiqiuTreeNode;
    }

    public JList<QianqiuBookModel> getQianqiuBookModelJList() {
        return qianqiuBookModelJList;
    }

    public void setQianqiuBookModelJList(JList<QianqiuBookModel> qianqiuBookModelJList) {
        this.qianqiuBookModelJList = qianqiuBookModelJList;
    }

    public DefaultListModel<QianqiuBookModel> getQianqiuBookModelDefaultListModel() {
        return qianqiuBookModelDefaultListModel;
    }

    public void setQianqiuBookModelDefaultListModel(DefaultListModel<QianqiuBookModel> qianqiuBookModelDefaultListModel) {
        this.qianqiuBookModelDefaultListModel = qianqiuBookModelDefaultListModel;
    }

    public Map<Integer, QianqiuBookModel> getQianqiuBookModelHashMap() {
        return qianqiuBookModelHashMap;
    }

    public void setQianqiuBookModelHashMap(Map<Integer, QianqiuBookModel> qianqiuBookModelHashMap) {
        this.qianqiuBookModelHashMap = qianqiuBookModelHashMap;
    }

    public ConcurrentHashMap<Integer, QiqiuTreeNode> getGuideBeanConcurrentHashMap() {
        return guideBeanConcurrentHashMap;
    }
    /** 设置数据并刷新树 */
    public void setGuideBeanConcurrentHashMap(ConcurrentHashMap<Integer, QiqiuTreeNode> guideBeanConcurrentHashMap) {
        this.guideBeanConcurrentHashMap = guideBeanConcurrentHashMap;
        getjTree().setPreferredSize(new Dimension(160, 363));
//        fillTreeFromGuideBeanMap(); // 数据变化时刷新树
    }
    public void fillTreeFromGuideBeanMap() {
        // 1. 创建新的根节点
        DefaultMutableTreeNode newRoot = new DefaultMutableTreeNode("Root");
        // 使用数组来包装节点，使其在lambda中可修改
        final DefaultMutableTreeNode[] overviewNodeHolder = new DefaultMutableTreeNode[1];

        // 2. 批量构建节点树
        for (QiqiuTreeNode node : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
            if (node.getId() >= 1 && node.getId() <= 10) {
                DefaultMutableTreeNode mainNode = new DefaultMutableTreeNode(node);
                newRoot.add(mainNode);

                // 如果是"总览"节点，保存到数组中
                if (node.getId() == 1) {
                    overviewNodeHolder[0] = mainNode;
                }

                // 添加子节点
                for (QiqiuTreeNode subNode : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
                    if (subNode.getParentId() == node.getId() &&
                        subNode.getId() > 10 && subNode.getId() < 100) {
                        mainNode.add(new DefaultMutableTreeNode(subNode));
                    }
                }
            }
        }
        // 3. 获取当前模型
        DefaultTreeModel model = (DefaultTreeModel) getjTree().getModel();

        // 4. 保存当前展开状态
        TreePath[] expandedPaths = getExpandedPaths(getjTree());

        // 5. 在EDT中原子性地更新树
        SwingUtilities.invokeLater(() -> {
            // 更新模型
            model.setRoot(newRoot);
            model.reload();

            // 收起所有节点
            collapseAll(getjTree(), new TreePath(newRoot), true);

            // 默认选中"总览"节点
            if (overviewNodeHolder[0] != null) {
                TreePath overviewPath = new TreePath(overviewNodeHolder[0].getPath());
                getjTree().setSelectionPath(overviewPath);
                getjTree().scrollPathToVisible(overviewPath);
            }

            // 恢复之前的展开状态
            for (TreePath path : expandedPaths) {
                getjTree().expandPath(path);
            }
        });
    }

    // 收起所有节点
    private void collapseAll(JTree tree, TreePath parent, boolean ignoreRoot) {
        TreeNode node = (TreeNode) parent.getLastPathComponent();
        if (!ignoreRoot && !node.isLeaf()) {
            tree.collapsePath(parent);
            for (int i = 0; i < node.getChildCount(); i++) {
                collapseAll(tree, parent.pathByAddingChild(node.getChildAt(i)), false);
            }
        }
    }

    // 获取当前展开的路径
    private TreePath[] getExpandedPaths(JTree tree) {
        List<TreePath> expandedPaths = new ArrayList<>();
        if (tree == null || tree.getModel() == null || tree.getModel().getRoot() == null) {
            return new TreePath[0];
        }

        // 从根节点开始递归获取所有展开的路径
        TreeNode root = (TreeNode) tree.getModel().getRoot();
        TreePath rootPath = new TreePath(root);
        collectExpandedPaths(tree, rootPath, expandedPaths);

        return expandedPaths.toArray(new TreePath[0]);
    }

    // 递归收集展开的路径
    private void collectExpandedPaths(JTree tree, TreePath parent, List<TreePath> expandedPaths) {
        if (tree.isExpanded(parent)) {
            expandedPaths.add(parent);

            TreeNode node = (TreeNode) parent.getLastPathComponent();
            if (node == null) return;

            for (int i = 0; i < node.getChildCount(); i++) {
                TreeNode child = node.getChildAt(i);
                if (child != null) {
                    TreePath path = parent.pathByAddingChild(child);
                    collectExpandedPaths(tree, path, expandedPaths);
                }
            }
        }
    }

    // 添加一个成员变量来缓存结果
    private Integer totalTaskCount = null;
    /**
     * 获取符合条件的任务总数
     */
    private int getTotalTaskCount() {
        // 如果已经计算过，直接返回缓存结果
        if (totalTaskCount != null) {
            return totalTaskCount;
        }

        // 否则计算并缓存结果
        int count = 0;
        for (QiqiuTreeNode n : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
            // 只统计有效的任务节点
            if (n.getId() >= 100 && n.getMeritvalue() != null && !n.getMeritvalue().isEmpty()) {
                count++;
            }
        }
        totalTaskCount = count;
        return count;
    }

    // 添加一个成员变量
    private boolean isInitializing = false;
    private void updateContentPanel(QiqiuTreeNode node) {
        if (isInitializing) {
            return; // 如果正在初始化，直接返回
        }

        try {
            for (int i = 0; i < 10; i++) {
                DataText[i].setVisible(node.getId() == 1);
            }
            if (node.getId() == 1) {
                // 显示总览内容
                qid = node.getId();
                paneRight.setVisible(true);

                // 清空现有数据
                qianqiuBookModelDefaultListModel.clear();
                // 设置CellRenderer
                qianqiuBookModelJList.setCellRenderer(new QianqiuBookCellRenderer());
                qianqiuBookModelJList.setFixedCellHeight(-1); // 允许可变高度

                // 获取所有已完成节点并按时间降序排序
                List<QiqiuTreeNode> completedNodes = new ArrayList<>();
                for (QiqiuTreeNode n : guideBeanConcurrentHashMap.values()) {
                    if (n.getComplete() == 1 && n.getTime() != null && !n.getTime().isEmpty()) {
                        completedNodes.add(n);
                    }
                }
                // 按时间降序排序
                completedNodes.sort((n1, n2) -> {
                    try {
                        return Long.compare(
                            Long.parseLong(n2.getTime().replaceAll("[^0-9]", "")),
                            Long.parseLong(n1.getTime().replaceAll("[^0-9]", ""))
                        );
                    } catch (Exception e) {
                        return 0;
                    }
                });

                // 只取最新的5条
                int count = Math.min(5, completedNodes.size());
                for (int i = 0; i < count; i++) {
                    QiqiuTreeNode item = completedNodes.get(i);
                    QianqiuBookModel model = new QianqiuBookModel(400, 63);
                    model.initData(item);
                    qianqiuBookModelDefaultListModel.addElement(model);
                }
                // 更新列表高度
                refreshListHeight(node.getId(), 63);

            } else if (node.getId() > 10 && node.getId() < 100) {
                // 发送请求获取最新数据
                String meg = Agreement.getAgreement().QianqiuAgreement(node.getId() + "");
                SendMessageUntil.toServer(meg);
                init(node);
                paneRight.setVisible(true);
            } else {
                paneRight.setVisible(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            isInitializing = false; // 初始化完成
        }
    }

    /**
     * 更新列表大小和滚动面板
     */
    private void updateListSize() {
        // 计算总高度
        int totalHeight = 0;
        for (int i = 0; i < qianqiuBookModelDefaultListModel.size(); i++) {
            totalHeight += qianqiuBookModelDefaultListModel.get(i).getH();
        }
        totalHeight = Math.max(289, totalHeight);

        qianqiuBookModelJList.setPreferredSize(new Dimension(402, totalHeight));
        qianqiuBookModelJList.setFixedCellHeight(-1); // 允许可变高度
        paneRight.getViewport().setViewSize(new Dimension(402, totalHeight));
        int x = Util.SwitchUI == 1 ? 0 : -26;
        int y = Util.SwitchUI == 1 ? 0 : -10;
        int w = Util.SwitchUI == 1 ? 0 : -2;
        int h = Util.SwitchUI == 1 ? 0 : -7;
        paneRight.setBounds(213+x, 158+y, 420+w, 288+h);
    }
}

