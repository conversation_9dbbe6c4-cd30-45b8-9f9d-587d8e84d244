package com.tool.btn;

import java.awt.event.MouseEvent;

import org.come.Jpanel.DuelBoardJpanel;
import org.come.until.CutButtonImage;
import org.come.until.ScrenceUntil;

public class DuelBoardBtn extends MoBanBtn {

    private int caozuo;
    private DuelBoardJpanel duelBoardJpanel;

    public DuelBoardBtn(String iconpath, int type, String text, int caozuo, DuelBoardJpanel duelBoardJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.duelBoardJpanel = duelBoardJpanel;
    }

    @Override
    public void chooseyes() {
    }

    @Override
    public void chooseno() {
    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 1) {
            try {
                if (duelBoardJpanel.isShowType()) {
                    duelBoardJpanel.getDuelBoardJframe().setBounds(ScrenceUntil.Screen_x - 18, 215, 18, 18);
                    duelBoardJpanel.getShowBtn().setBounds(0, 0, 18, 18);
                    duelBoardJpanel.getShowBtn().setIcons(CutButtonImage.cuts("inkImg/button/10.png"));
                } else {
                    duelBoardJpanel.getDuelBoardJframe().setBounds(ScrenceUntil.Screen_x - 210, 215, 210, 188);
                    duelBoardJpanel.getShowBtn().setBounds(191, 0, 18, 18);
                    duelBoardJpanel.getShowBtn().setIcons(CutButtonImage.cuts("inkImg/button/9.png"));
                }
                duelBoardJpanel.setShowType(!duelBoardJpanel.isShowType());
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
    }
}
