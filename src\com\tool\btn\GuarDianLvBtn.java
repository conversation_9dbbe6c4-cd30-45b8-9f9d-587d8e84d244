package com.tool.btn;

import com.tool.role.GetExp;
import com.tool.role.RoleData;
import org.come.Frame.MountJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.GuarDianLvJPanel;
import org.come.Jpanel.MountJPanel;
import org.come.bean.LoginResult;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.AnalysisString;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class GuarDianLvBtn extends MoBanBtn{
    public GuarDianLvJPanel  guarDianLvJPanel;
    public BigDecimal bigZero = new BigDecimal(0);
    public int BtnId;
    public GuarDianLvBtn(String iconpath, int type, int BtnId, String labelName,
                         GuarDianLvJPanel guarDianLvJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.guarDianLvJPanel = guarDianLvJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch (BtnId) {
            case 2:
                promotePoint();
                break;
            case 1:
                onekeyPromote();
                break;
            default:
                break;
        }
    }

    /** 提升修炼点 */
    public void promotePoint() {
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        BigDecimal experience = loginResult.getExperience();
        BigDecimal gold = loginResult.getGold();
        if (gold.compareTo(guarDianLvJPanel.money) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("当前游戏币不足");
            return;
        }
        // 获取已兑换的总修炼点
        int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type));
        int tsp = GetExp.getTSP(parseInt);;
        // 当前已兑换的修炼点数
        int tsx;
        // 获取当前修炼点数总进度
        int tsExp;
        // 获取当前人物等级
        String lvl = AnalysisString.lvl(loginResult.getGrade());
        if (tsp >= 24) {
            ZhuFrame.getZhuJpanel().addPrompt2("已兑换至当前境界上限");
            return;
        }
        // 获取当前人物境界名称
        gold = gold.subtract(guarDianLvJPanel.money);
        loginResult.setGold(gold);
        loginResult.setExtraPoint(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type), 1);
        String mes = Agreement.getAgreement().rolechangeAgreement(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type)+"1");
        SendMessageUntil.toServer(mes);
        // 获取已兑换的总修炼点
        parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type));
        // 当前天枢点
        tsp = GetExp.getTSP(parseInt);
        // 当前已兑换的修炼点数
        tsx = GetExp.getTSX(parseInt);
        // 获取当前修炼点数总进度
        tsExp = GetExp.getTSExp(tsp + 1);
        guarDianLvJPanel.ObtainVariousParameters(experience, tsx, tsExp, lvl, guarDianLvJPanel.type, tsp, 24);
        MountPanelBtn.register();
        MountJPanel mountJPanel= MountJframe.getMountjframe().getMountjpanel();
        mountJPanel.ComponentDisplay(mountJPanel.getGuardType());
    }

    /** 一键兑换 */
    public void onekeyPromote() {
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        BigDecimal experience = loginResult.getExperience();
        BigDecimal gold = loginResult.getGold();
        // 获取已兑换的总修炼点
        int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type));
        // 当前天枢点
        int tsp = GetExp.getTSP(parseInt);
        // 当前已兑换的修炼点数
        int tsx = GetExp.getTSX(parseInt);
        // 获取当前修炼点数总进度
        int tsExp = GetExp.getTSExp(tsp + 1);

        int tsD = tsExp - tsx;
        // 获取当前人物境界
        // 获取当前人物可以兑换的最大天枢点
        int realmMaxTSP = 24;

        if (tsp >= realmMaxTSP) {
            ZhuFrame.getZhuJpanel().addPrompt2("已兑换至当前境界上限");
            return;
        }
        if (tsD <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("已兑换至当前境界上限");
            return;
        }
        BigDecimal goldDiv = gold.divide(guarDianLvJPanel.money, 0, BigDecimal.ROUND_DOWN);

                if (goldDiv.compareTo(bigZero) <= 0) {
                    ZhuFrame.getZhuJpanel().addPrompt2("当前游戏币不足");
                    return;
                }
                onekeyDiff(loginResult, gold, experience, goldDiv, tsD);
        }



    /** 一键兑换判断 */
    public void onekeyDiff(LoginResult loginResult, BigDecimal gold, BigDecimal experience, BigDecimal value, int tsD) {
        int num = Math.min(tsD, value.intValue());
        gold = gold.subtract(new BigDecimal(num).multiply(guarDianLvJPanel.money));

        loginResult.setExperience(experience);
        loginResult.setGold(gold);
        loginResult.setExtraPoint(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type), num);
        // SendRoleAndRolesummingUntil.sendRole(loginResult);
        String mes = Agreement.getAgreement().rolechangeAgreement(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type) + num);
        SendMessageUntil.toServer(mes);
        ZhuFrame.getZhuJpanel().addPrompt2("当前修炼点加" + num);
        // 获取已兑换的总修炼点
        int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(guarDianLvJPanel.type));
        // 当前天枢点
        int tsp = GetExp.getTSP(parseInt);
        // 当前已兑换的修炼点数
        int tsx = GetExp.getTSX(parseInt);
        // 获取当前修炼点数总进度
        int tsExp = GetExp.getTSExp(tsp + 1);
        // 获取当前人物等级
        String lvl = AnalysisString.lvl(loginResult.getGrade());
        guarDianLvJPanel.ObtainVariousParameters(experience, tsx, tsExp, lvl, guarDianLvJPanel.type, tsp, 24);
        MountPanelBtn.register();
        MountJPanel mountJPanel= MountJframe.getMountjframe().getMountjpanel();
        mountJPanel.ComponentDisplay(mountJPanel.getGuardType());
    }
}
