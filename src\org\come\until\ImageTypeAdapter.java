package org.come.until;

import com.google.gson.*;
import java.awt.Image;
import java.lang.reflect.Type;

/**
 * 自定义的Image类型适配器，解决Gson访问java.awt.Image私有字段的问题
 * 这个适配器防止Gson尝试序列化/反序列化Image对象，避免反射访问限制
 */
public class ImageTypeAdapter implements JsonSerializer<Image>, JsonDeserializer<Image> {

    @Override
    public JsonElement serialize(Image src, Type typeOfSrc, JsonSerializationContext context) {
        // 对于Image对象，我们不进行序列化，返回null
        // 因为Image对象通常不应该被序列化到JSON
        return JsonNull.INSTANCE;
    }

    @Override
    public Image deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) 
            throws JsonParseException {
        // 对于Image对象，我们不进行反序列化，返回null
        // 因为Image对象通常不应该从JSON反序列化
        return null;
    }
}
