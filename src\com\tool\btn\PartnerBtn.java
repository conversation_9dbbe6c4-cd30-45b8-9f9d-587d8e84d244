package com.tool.btn;

import com.tool.pet.PetProperty;
import com.tool.role.RoleData;
import come.tool.JDialog.TiShiUtil;
import jxy2.jutnil.Juitil;
import jxy2.petAi.PetBattleAiFrame;
import org.come.Frame.OptionsJframe;
import org.come.Frame.PartnerJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.PartnerMainJpanel;
import org.come.Jpanel.PartnerPetJpanel;
import org.come.Jpanel.PartnerTeamJpanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.Pal;
import org.come.entity.RoleSummoning;
import org.come.model.PalData;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PartnerBtn extends MoBanBtn {

    private int caozuo;
    private PartnerMainJpanel partnerMainJpanel;

    public PartnerBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,PartnerMainJpanel partnerMainJpanel) {
        super(iconpath, type,0, colors,"");
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.partnerMainJpanel = partnerMainJpanel;
    }

    public PartnerBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo) {
        super(iconpath, type,0, colors,"");
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {

        try {
            switch (caozuo) {
                case 11:
                case 12:
                case 13:
                case 14:
                case 15:
                    partnerMainJpanel.setMaxtype(caozuo);
                    PartnerPetJpanel.showListModel(UserMessUntil.getPetListTable());
                    Juitil.handleButtonState(partnerMainJpanel.getBtnTeam(),(caozuo-11));
                    partnerMainJpanel.getPartnerCardJpanel().getCardLayout().show(partnerMainJpanel.getPartnerCardJpanel(), "l"+(caozuo-11));
                    break;
                case 4: // 参战
                    if ("参战".equals(getText())) {
                        if (PartnerJframe.getPartnerJframe().getPartnerMainJpanel().getPalDataChooseId() < 0) {
                            return;
                        }
                        PartnerTeamJpanel partnerTeamJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel()
                                .getPartnerCardJpanel().getPartnerTeamJpanel();
                        if (partnerTeamJpanel.hideBtnArr()) {
                            return;
                        }
                        PartnerBtn[] btnArrAck = partnerTeamJpanel.getBtnArrAck();
                        String[] btnArrStr = partnerTeamJpanel.getBtnArrStr();
                        for (int i = 0; i < btnArrAck.length; i++) {
                            btnArrAck[3 - i].setText(btnArrStr[i]);
                        }
                        partnerTeamJpanel.showBtnArr(true);
                    } else if ("激活".equals(getText())) {
                        activatePal();
                    } else if ("调整".equals(getText())) {
                        if (PartnerJframe.getPartnerJframe().getPartnerMainJpanel().getPalDataChooseId() < 0) {
                            return;
                        }
                        PartnerMainJpanel partnerMainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
                        PartnerTeamJpanel partnerTeamJpanel = partnerMainJpanel.getPartnerCardJpanel()
                                .getPartnerTeamJpanel();
                        if (partnerTeamJpanel.hideBtnArr()) {
                            return;
                        }
                        PartnerBtn[] btnArrAck = partnerTeamJpanel.getBtnArrAck();
                        String[] btnArrStr = partnerTeamJpanel.getBtnArrStr();
                        String pals = RoleData.getRoleData().getLoginResult().getPals();
                        if (pals != null) {
                            int num = 0;
                            String[] palsArr = pals.split("\\|");
                            for (int i = 0; i < palsArr.length; i++) {
                                if (palsArr[i].equals(partnerMainJpanel.pidGetPal(partnerMainJpanel.getPalDataChooseId())
                                        .getId() + "")) {
                                    num = i;
                                }
                            }
                            int sx = 0;
                            for (int i = 0; i < btnArrAck.length; i++) {
                                if (num == i) {
                                    continue;
                                }
                                btnArrAck[3 - sx].setText(btnArrStr[i]);
                                sx++;
                            }
                            btnArrAck[0].setText(btnArrStr[4]);
                        }
                        partnerTeamJpanel.showBtnArr(true);
                    }

                    break;
                case 5: {// 抗性
                    if ("参战".equals(getText())||"助战".equals(getText())){
                        changePalSummoning();
                    }else {
                        deletePal();
                    }

                    break;
            }
                case 8: // 突破
                    ZhuFrame.getZhuJpanel().addPrompt2("该道具已经失效");
                    return;
//                PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
//                int chooseId = mainJpanel.getPalDataChooseId();
//                PartnerEquipJpanel equipJpanel = mainJpanel.getPartnerCardJpanel().getPartnerEquipJpanel();
//                if (chooseId < 0) {
//                    return;
//                }
//                Pal pidGetPal = mainJpanel.pidGetPal(chooseId);
//                if (pidGetPal != null) {
//                    int breakLevel = equipJpanel.isBreakLevel(pidGetPal.getLvl());
//                    long palExp = PartnerJframe.getPartnerJframe().getPartnerMainJpanel().getPartnerCardJpanel()
//                            .getPartnerEquipJpanel().palExp(pidGetPal.getLvl());
//                    if (palExp > pidGetPal.getExp()) {
//                        ZhuFrame.getZhuJpanel().addPrompt2("经验没有升满");
//                        return;
//                    }
//                    if (breakLevel < 0) {
//                        ZhuFrame.getZhuJpanel().addPrompt2("等级没有抵达突破要求");
//                        return;
//                    }
//                    StringBuffer buffer = new StringBuffer();
//                    buffer.append("#W突破#G");
//                    buffer.append(pidGetPal.getLvl());
//                    buffer.append("#W级需要#R");
//                    buffer.append(breakLevel);
//                    buffer.append("#W个等级突破丹。是否突破?");
//                    OptionsJframe.getOptionsJframe().getOptionsJpanel()
//                            .showBox(TiShiUtil.PalKey, pidGetPal, buffer.toString());
//                } else {
//                    // 还没有激活
//                    ZhuFrame.getZhuJpanel().addPrompt2("你还没有激活当前伙伴");
//                }

                case 9:
                    ZhuJpanel.setUseGoodsType(0);
                    FormsManagement.showForm(2);
                    break;
                case 10: {
                    PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
                    String[] btnArrStr = mainJpanel.getPartnerCardJpanel().getPartnerTeamJpanel().getBtnArrStr();
                    List<Pal> pals = null;
                    if (btnArrStr[0].equals(getText())) {
                        pals = changeLogignResultPals(1, mainJpanel.getPalDataChooseId(), mainJpanel);
                    } else if (btnArrStr[1].equals(getText())) {
                        pals = changeLogignResultPals(2, mainJpanel.getPalDataChooseId(), mainJpanel);
                    } else if (btnArrStr[2].equals(getText())) {
                        pals = changeLogignResultPals(3, mainJpanel.getPalDataChooseId(), mainJpanel);
                    } else if (btnArrStr[3].equals(getText())) {
                        pals = changeLogignResultPals(4, mainJpanel.getPalDataChooseId(), mainJpanel);
                    } else if (btnArrStr[4].equals(getText())) {
                        pals = changeLogignResultPals(-1, mainJpanel.getPalDataChooseId(), mainJpanel);
                    }
                    mainJpanel.getPartnerCardJpanel().getPartnerTeamJpanel().hideBtnArr();
                    mainJpanel.refreshPals(pals);
                    break;
                }
                case 16:
                    deletePal();
                    break;
                case 17:
                    RoleData roleData  = RoleData.getRoleData();
                    if (roleData.getLoginResult().getSummoning_id().compareTo(UserMessUntil.getChosePetMes().getSid())==0)
                    {
                        ZhuFrame.getZhuJpanel().addPrompt2("选择的召唤兽于主角参战宠物冲突或无需选中宠物");
                        return;
                    }

                    if (!FormsManagement.getInternalForm(121).getFrame().isVisible()) {
                        FormsManagement.showForm(121);
                        PetBattleAiFrame.getPetBattleAiFrame().getPetBattleAiJPanel().initPetInfo(UserMessUntil.getChosePetMes());
                    } else {
                        FormsManagement.HideForm(121);
                    }
                    break;
                case 18:
                    break;
                case 19:
                    break;
                case 20:
                    PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
                    int chooseId = mainJpanel.getPalDataChooseId();
                    if (chooseId > 0) {
                        Pal pidGetPal = mainJpanel.pidGetPal(chooseId);
                        if (pidGetPal != null) {
                            PetProperty.ShowQl(pidGetPal);
                            if (!FormsManagement.getframe(58).isVisible()) {
                                PetProperty.ShowQl(UserMessUntil.getChosePetMes());
                                FormsManagement.showForm(58);
                            } else {
                                FormsManagement.HideForm(58);
                            }
                        } else {
                            // 还没有激活
                            ZhuFrame.getZhuJpanel().addPrompt2("你还没有激活当前伙伴");
                        }
                    } else {
                        // 没有选中
                        ZhuFrame.getZhuJpanel().addPrompt2("请先选中一个伙伴");
                    }
                    break;
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    private void deletePal() {
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        StringBuffer mesBuf = new StringBuffer();
        mesBuf.append("S");
        PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
        int pid = mainJpanel.getPalDataChooseId();
        Pal pidGetPal = mainJpanel.pidGetPal(pid);
        if (pidGetPal.getSummoning_id()!=null) {
            BigDecimal id = pidGetPal.getId();
            mesBuf.append(id);
            mesBuf.append("|");
            mesBuf.append(pet.getSid());
            String sendmes = Agreement.getAgreement().userpalAgreement(mesBuf.toString());
            SendMessageUntil.toServer(sendmes);
            mainJpanel.getPartnerCardJpanel().getPartnerPetJpanel().getBtnResistance().setText("参战");
            pet.setBattleAid(0);
            PartnerTeamJpanel.PetPart = null;

        }else {
            ZhuFrame.getZhuJpanel().addPrompt2("#R请选择对应的伙伴");
        }
    }

    /**参战召唤兽*/
    public void changePalSummoning() {
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        StringBuffer mesBuf = new StringBuffer();
        mesBuf.append("X");
        PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
        int pid = mainJpanel.getPalDataChooseId();
        Pal pidGetPal = mainJpanel.pidGetPal(pid);
        if (pidGetPal==null){
            ZhuFrame.getZhuJpanel().addPrompt2("你还没有选择伙伴");
            return;
        }
        if (RoleData.getRoleData().getLoginResult().getSummoning_id()!=null) {
            if (RoleData.getRoleData().getLoginResult().getSummoning_id().compareTo(UserMessUntil.getChosePetMes().getSid()) == 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("#W主角参战中无法继续");
                return;
            }
        }
        BigDecimal id = pidGetPal.getId();
        mesBuf.append(id);
        mesBuf.append("|");
        mesBuf.append(UserMessUntil.getChosePetMes().getSid());
        String sendmes = Agreement.getAgreement().userpalAgreement(mesBuf.toString());
        SendMessageUntil.toServer(sendmes);
        mainJpanel.getPartnerCardJpanel().getPartnerPetJpanel().getBtnResistance().setText("休息");
        pet.setBattleAid(1);
        PartnerTeamJpanel.PetPart = UserMessUntil.getChosePetMes().getPart();
    }


    /**
     * 调整参战位置
     * 
     * @param type
     *            1-4调整位置 -1休息
     * @param pid
     *            修改的Pid
     */
    public List<Pal> changeLogignResultPals(int type, int pid, PartnerMainJpanel mainJpanel) {
        List<Pal> palsList = new ArrayList<Pal>();
        String pals = RoleData.getRoleData().getLoginResult().getPals();
        StringBuffer mesBuf = new StringBuffer();
        mesBuf.append("P");
        Pal pidGetPal = mainJpanel.pidGetPal(pid);
        BigDecimal id = pidGetPal.getId();
        mesBuf.append(id);
        palsList.add(mainJpanel.idGetPal(id));
        if (pals != null && !"".equals(pals)) {
            StringBuffer buffer = new StringBuffer();
            String[] palsArr = pals.split("\\|");
            if (palsArr.length < type) {
                for (int i = 0; i < palsArr.length; i++) {
                    if (palsArr[i].equals(id + "")) {
                        continue;
                    }
                    if (buffer.length() != 0) {
                        buffer.append("|");
                    }
                    buffer.append(palsArr[i]);
                    palsList.add(mainJpanel.idGetPal(new BigDecimal(palsArr[i])));
                }
                buffer.append("|");
                buffer.append(id);
            } else {
                String lastPid;
                lastPid = type > 0 ? palsArr[type - 1] : null;
                if (lastPid != null) {
                    palsList.add(mainJpanel.idGetPal(new BigDecimal(lastPid)));
                }
                mesBuf.append("|");
                mesBuf.append(type > 0 ? type - 1 : -1);
                for (int i = 0; i < palsArr.length; i++) {
                    if (palsArr[i].equals(id + "")) {
                        palsArr[i] = lastPid;
                    } else if ((type - 1) == i) {
                        palsArr[i] = id + "";
                    }
                }
                for (int i = 0; i < palsArr.length; i++) {
                    if (palsArr[i] == null) {
                        continue;
                    }
                    if (buffer.length() != 0) {
                        buffer.append("|");
                    }
                    buffer.append(palsArr[i]);
                    palsList.add(mainJpanel.idGetPal(new BigDecimal(palsArr[i])));
                }
            }
            RoleData.getRoleData().getLoginResult().setPals(buffer.toString());
        } else {
            RoleData.getRoleData().getLoginResult().setPals(id + "");
        }

        String sendmes = Agreement.getAgreement().userpalAgreement(mesBuf.toString());
        SendMessageUntil.toServer(sendmes);
        return palsList;
    }

    /** 激活伙伴 */
    public void activatePal() {
        PartnerMainJpanel mainJpanel = PartnerJframe.getPartnerJframe().getPartnerMainJpanel();
        int chooseId = mainJpanel.getPalDataChooseId();
        if (chooseId < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("请先选中一个伙伴");
            return;
        }
        PalData palData = UserMessUntil.getPalData(chooseId);
        if (palData == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("请先选中一个伙伴");
            return;
        }
        String xh = palData.getXh();
        StringBuffer buffer = new StringBuffer();
        buffer.append("#W确定要激活#G");
        buffer.append(palData.getName());
        if (xh != null && !"".equals(xh)) {

            buffer.append("#W吗?消耗:#R");
            buffer.append(xh.substring(1, xh.length()));
            if (xh.startsWith("D")) {
                buffer.append("游戏币#W。");
            } else if (xh.startsWith("X")) {
                buffer.append("仙玉#W。");
            }
        } else {
            buffer.append("吗?消耗:#R无消耗#W。");
        }
        OptionsJframe.getOptionsJframe().getOptionsJpanel().showBox(TiShiUtil.PalKey, palData, buffer.toString());

    }
}
