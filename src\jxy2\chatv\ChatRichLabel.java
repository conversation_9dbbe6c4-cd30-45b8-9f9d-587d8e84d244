package jxy2.chatv;

import com.tool.tcp.Animation;
import com.tool.tcp.Frame;
import com.tool.tcp.SpriteFactory;
import com.tool.tcpimg.InputBean;
import com.tool.tcpimg.UIUtils;
import org.come.login.GameView;
import org.come.until.CutButtonImage;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


//TODO add Link
/**
 * 文字格式：聊天时可以为消息内容设置文字格式，指令为“#字母”，区分大小写。 <br>
 * #R 表示后面的字体为红色(red)<br>
 * #G 表示后面的字体为绿色(green)<br>
 * #B 表示后面的字体为蓝色(blue)<br>
 * #K 表示后面的字体为黑色(black)<br>
 * #Y 表示后面的字体为黄色(yellow)<br>
 * #W 表示后面的字体为白色(white)<br>
 * #b 表示后面的字体为闪烁(blink)<br>
 * #c + 六个数字或者A-F字母 自定义颜色，例如：c008000=暗绿色<br>
 * #u + 文字 + #u 文字有下划线。<br>
 * #n 所有文字状态恢复正常。<br>
 * #r 文字换行。<br>
 * ## 输出一个#号。<br>
 * #0-99 动画 //系统400 信息401 世界402 当前403 队伍404 帮派405 #T系统 #X 信息 #J 世界 #Q 当前 #D 队伍 #P 帮派
 * #V 点击监听
 * <AUTHOR>
 * @history 2008-6-9 龚德伟 新建
 */
public class ChatRichLabel extends JComponent {

    private static final long serialVersionUID = 4898130145332371300L;
    public ArrayList<Object> sectionList;
    //可交互的
    private boolean isInteractive;
    private long startTime;
    private long currTime;
    private Font font;
    private String text;
    private static HashMap<String, Animation> faceAnimations;
    private static Pattern pattern;
    static{
        faceAnimations = new HashMap<String,Animation>(100);
        // 聊天正则表达式
        String REGULAR = "#([RGBKYWMTXJQDPVLnbur#]|[1][0-5][0-9]|[1][6][0-8]|[8][8][0-1]|[1-9]\\d|[0-9]|"
                + "c[0-9A-Fa-f]?[0-9A-Fa-f]?[0-9A-Fa-f]?[0-9A-Fa-f]?[0-9A-Fa-f]?[0-9A-Fa-f]?)|"
                + "[^#]+";
        pattern = Pattern.compile(REGULAR);
    }
    private static String PATH="resource/emoticons/";

    public ChatRichLabel(String text, Font font) {
        sectionList = new ArrayList<Object>();
        setBackground(Color.RED);
        setForeground(Color.BLUE);
        setIgnoreRepaint(true);
        setBorder(null);
        setOpaque(false);
        this.text = text;
        setSize(14 * 7, 16);
        parseText(text);
        this.font = font;
    }
    public ChatRichLabel(String text, Font font,int size) {
        sectionList = new ArrayList<Object>();
        setBackground(Color.RED);
        setForeground(Color.BLUE);
        setIgnoreRepaint(true);
        setBorder(null);
        setOpaque(false);
        this.text = text;
        parseText(text);
        this.font = font;
        setSize(computeSize(size));
    }
    private void parseText(String text) {
        if (text == null)
            return;
        String section;
        Matcher m = pattern.matcher(text);
        Animation anim;
        // * //系统400 信息401 世界402 当前403 队伍404 帮派405
        // * #T 系统* #X 信息* #J 世界* #Q 当前* #D 队伍* #P 帮派
        while (m.find()) {
            section = m.group();
            if (section.startsWith("#")) {
                anim = faceAnimations.get(section);
                if (anim == null && section.charAt(1) >= '0' && section.charAt(1) <= '9') {
                    String value=section.substring(1);
                    if (value.equals("880")) {
                        anim=new Animation();
                        Frame frame=new Frame(CutButtonImage.getImage("inkImg/background/S190.png", -1, -1).getImage(), 0, 0);
                        anim.addFrame(frame);
                    }else if (value.equals("881")) {
                        anim=new Animation();
                        Frame frame=new Frame(CutButtonImage.getImage("inkImg/background/S189.png", -1, -1).getImage(), 0, 0);
                        anim.addFrame(frame);
                    }else {
                        anim = SpriteFactory.loadAnimation(PATH+value + ".was");
                    }
                    if (anim != null)
                        faceAnimations.put(section, anim);
                }else if (section.equals("#T")||section.equals("#X")||section.equals("#J")||section.equals("#Q")||section.equals("#D")||section.equals("#P")) {
                    anim = faceAnimations.get(section);
                    if (anim == null) {
                        anim = SpriteFactory.loadAnimation(PATH+gethead(section.substring(1)) + ".was");
                        if (anim != null)
                            faceAnimations.put(section, anim);
                    }
                }
                if (anim != null) {
                    sectionList.add(anim);
                } else if (section.startsWith("#c")) {
                    if (section.length()==8) {
                        Color color= UIUtils.getColor(section);
                        sectionList.add(color);
                    }
                } else if (section.equals("#R")) {
                    sectionList.add(Color.RED);
                } else if (section.equals("#G")) {
                    sectionList.add(Color.GREEN);
                } else if (section.equals("#B")) {
                    sectionList.add(Color.BLUE);
                }else if (section.equals("#W")) {
                    sectionList.add(Color.WHITE);
                } else if (section.equals("#K")) {
                    sectionList.add(Color.BLACK);
                } else if (section.equals("#Y")) {
                    sectionList.add(Color.YELLOW);
                } else if (section.equals("#r")) {
                    // line swap
                    sectionList.add(Integer.valueOf(-1));
                } else if (section.equals("#n")) {
                    // FIXME reset
                    sectionList.add(Color.WHITE);
                }else if (section.equals("#M")) {
                    if (m.find()) {
                        String cr = m.group();
                        int money = Integer.parseInt(cr);
                        if (money<9999){
                            sectionList.add(Color.white);
                        }else if (money < 100000){
                            sectionList.add(new Color(36, 219, 118));
                        }else if (money < 1000000){
                            sectionList.add(new Color(253, 68, 221));
                        }else if (money < 10000000){
                            sectionList.add(new Color(251, 217, 50));
                        }else if (money < 100000000){
                            sectionList.add(new Color(0, 239, 239));
                        }else if (money < 1000000000){
                            sectionList.add(Color.GREEN);
                        } else {
                            sectionList.add(Color.red);
                        }

                        if (cr.compareTo(String.valueOf(new BigDecimal(1000))) >= 0) {
                            StringBuffer gold = new StringBuffer(cr);
                            int index;
                            for (index = gold.length() - 3; index > 0; index -= 3) {
                                gold.insert(index, ',');
                            }
                            sectionList.add(gold.toString());
                        }
                    }
                }else if (section.equals("#V")) {
                    try {
                        if (m.find()) {
                            String cr = m.group();
                            if (cr.startsWith("c4f0f0f")) {sectionList.add(new InputBean(-1));continue;}
                            InputBean bean=GsonUtil.getGsonUtil().getgson().fromJson(cr, InputBean.class);
                            if (bean.getColor()!=null&&!bean.getColor().equals("")) {
                                Color color = null;
                                if (bean.getColor().startsWith("c")) {
                                    String color2=section.substring(2);
                                    if (!color2.equals("")) {
                                        color = Color.decode("0x" + color2);
                                    }
                                } else if (bean.getColor().equals("R")) {
                                    color=Color.RED;
                                } else if (bean.getColor().equals("G")) {
                                    color=Color.GREEN;
                                } else if (bean.getColor().equals("B")) {
                                    color=Color.BLUE;
                                } else if (bean.getColor().equals("W")) {
                                    color=Color.WHITE;
                                } else if (bean.getColor().equals("K")) {
                                    color=Color.BLACK;
                                } else if (bean.getColor().equals("Y")) {
                                    color=Color.YELLOW;
                                }
                                bean.setColor2(color);
                            }
                            bean.setColor(null);
                            sectionList.add(bean);
                            isInteractive=true;
                        }
                    } catch (Exception e) {
                        // TODO: handle exception
                        e.printStackTrace();
                    }
                }
            } else {
                sectionList.add(section);
            }
        }
    }

    public int gethead(String head){
        if (head.equals("T"))return 400;
        else if (head.equals("X"))return 401;
        else if (head.equals("J"))return 402;
        else if (head.equals("Q"))return 403;
        else if (head.equals("D"))return 404;
        else return 405;
    }
    @Override
    public void paint(Graphics g) {
        int index = 0;
        if (font != null) {
            g.setFont(ChatFrame.getChatJPanel().is?UIUtils.TEXT_FONT1:UIUtils.TEXT_FONT15);
        } else {
            g.setFont(UIUtils.TEXT_FONT2);
        }
        g.setColor(Color.WHITE);

        if (startTime == 0){
            startTime = System.currentTimeMillis();
        }
        currTime = System.currentTimeMillis();
        int maxwidth = GameView.getFrameDialogSync().getDialog().isVisible()?
                GameView.getFrameDialogSync().getDialog().getWidth()-21:
                ChatFrame.getChatFrame().getWidth() -36,
                rowWidth = 0,
                y = 0,
                rowHeight = 0;

        int count = sectionList.size(), start = 0, x = 0;
        for (int i = 0; i < count; i++) {
            Object obj = sectionList.get(i);
            if (obj instanceof String) {
                String str = (String) obj;
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                rowHeight = Math.max(rowHeight, fm.getHeight());
                int dx = fm.stringWidth(str);
                if (rowWidth + dx <= maxwidth) {
                    rowWidth += dx;
                } else {
                    Point p = paintRichText(g, x, y, maxwidth, rowHeight,start, i + 1);
                    start = i + 1;
                    rowWidth = p.x;
                    rowHeight = fm.getHeight();
                    x = p.x;
                    y = p.y;
                }
            } else if (obj instanceof Animation) {
                Animation anim = (Animation) obj;
                if (anim.getWidth() + rowWidth > maxwidth) {
                    paintRichText(g, x, y, maxwidth, rowHeight, start, i);
                    start = i;
                    x = 0;
                    y += rowHeight;
                    rowWidth = anim.getWidth();
                    rowHeight = anim.getHeight();
                } else {
                    rowHeight = Math.max(rowHeight, anim.getHeight());
                    rowWidth += anim.getWidth();
                    index++;
                }
            } else if (obj instanceof Integer) {// line swap
                paintRichText(g, x, y, maxwidth, rowHeight, start, i + 1);
                start = i;
                x = 0;
                y += rowHeight;
                rowWidth = 0;
                rowHeight = 0;
            }else if (obj instanceof InputBean) {
                InputBean bean=(InputBean) obj;
                if (bean.getType()==-1) {continue;}
                String str = bean.getName();
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                rowHeight = Math.max(rowHeight, fm.getHeight());
                int dx = fm.stringWidth(str);
                if (rowWidth + dx <= maxwidth) {
                    rowWidth += dx;
                }else {
                    Point p = paintRichText(g,x,y,maxwidth,rowHeight,start,i+1);
                    start = i + 1;
                    rowWidth = p.x;
                    rowHeight = fm.getHeight();
                    x = p.x;
                    y = p.y;
                }
            }
        }

        setSize(maxwidth, y+(maxwidth>=329&&index>2?36:rowHeight+3));
        paintRichText(g, x, y, maxwidth, rowHeight, start, count);
    }
    private String lastStr;
    private InputBean lastBean;
    private Point paintRichText(Graphics g, int x, int y, int width, int rowh,int start, int end) {
        if (lastStr != null) {
            if (lastBean!=null) {
                Color color=g.getColor();
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                if (lastBean.getColor2()!=null) {
                    g.setColor(lastBean.getColor2());
                }
                g.drawString(lastStr, 0, y + rowh);
                if (lastBean.getType()!=1) {
                    g.drawLine((lastBean.isM()?1:0), y+3+rowh+(lastBean.isM()?1:0),fm.stringWidth(lastStr)+(lastBean.isM()?1:0), y+3+rowh+(lastBean.isM()?1:0));
                }
                g.setColor(color);
            }else {
                if (font==null){
                    g.drawString(lastStr, 0, y + rowh);
                }else {//文本换行文字抗锯齿
                    if (font.getFontName().equals("YaHei Consolas Hybrid")) {
                        Graphics2D g2d = (Graphics2D) g.create();
//                        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                        g2d.drawString(lastStr, 0, y + rowh);
                        g2d.drawString(lastStr, 0, y + rowh);
                        g2d.dispose();
                    }else if (font.getFontName().equals("方正粗圆_GBK")) {
                        Graphics2D g2d = (Graphics2D) g.create();
                        g2d.drawString(lastStr, 0, y + rowh);
                        g2d.dispose();
                    }else {
                        g.drawString(lastStr, 0, y + rowh);
                    }
                }
            }
            lastStr = null;
            lastBean=null;
        }

        for (int i = start; i < end; i++) {
            Object obj = sectionList.get(i);
            if (obj instanceof String) {
                String str = (String) obj;
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                int len = str.length();
                int begin = 0, index, dx = 0;
                for (index = 0; index < len;) {
                    dx = fm.charWidth(str.charAt(index));
                    while (x + dx <= width) {
                        if (++index >= len){
                            break;
                        }
                        dx += fm.charWidth(str.charAt(index));
                    }
                    String s = str.substring(begin, index);
                    if (i == end - 1 && index >= len && x + dx < width && sectionList.size() > end) {
                        lastStr = s;
                        Object nextObj = sectionList.get(end);
                        if (nextObj instanceof Animation) {
                            Animation anim = (Animation) nextObj;
                            if (anim.getWidth() + x + dx > width) {
                                g.drawString(s, x, y + rowh);
                                lastStr = null;
                            }
                        }
                    } else {
                        if (font==null){
                            g.drawString(s, x, y + rowh);
                        }else {

                            if (font.getFontName().equals("YaHei Consolas Hybrid")||font.getFontName().equals("方正粗圆_GBK")) {
                                Graphics2D g2d = (Graphics2D) g.create();
//                                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                                g2d.setColor(Color.black);
                                g2d.drawString(s, x+ 2  + 1, y + rowh);
                                g2d.drawString(s, x+2, y + rowh+1);
                                g2d.setColor(g.getColor());
                                g2d.drawString(s, x + 2 ,  y + rowh);
                                g2d.dispose();

                            }else {
                                g.drawString(s, x, y + rowh);
                            }
                        }

                    }
                    if (index < len || x + dx == width) {
                        begin = index;
                        x = 0;
                        y += rowh;
                        rowh = fm.getHeight();
                    } else {
                        x += fm.stringWidth(s);
                    }
                }
            } else if (obj instanceof Color) {
                g.setColor((Color) obj);
            } else if (obj instanceof Animation) {
                Animation anim = (Animation) obj;
                anim.updateToTime(currTime - startTime);
                g.drawImage(anim.getImage(), x, y+3+rowh-anim.getHeight(),null);
                x += anim.getWidth();
            }else if (obj instanceof InputBean) {
                InputBean bean=(InputBean) obj;
                if (bean.getType()==-1) {continue;}
                Color color=g.getColor();
                if (bean.getColor2()!=null) {
                    g.setColor(bean.getColor2());
                }
                String str = bean.getName();
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                if (bean.getHeight()==null) {
                    bean.setS_x(x);
                    bean.setS_y(y-fm.getHeight()+rowh);
                }
                int len = str.length();
                int begin = 0, index, dx = 0;
                for (index = 0; index < len;) {
                    dx = fm.charWidth(str.charAt(index));
                    while (x + dx <= width) {
                        if (++index >= len){
                            break;
                        }
                        dx += fm.charWidth(str.charAt(index));
                    }
                    String s = str.substring(begin, index);
                    if (i == end - 1 && index >= len && x + dx < width && sectionList.size() > end) {
                        lastStr = s;
                        lastBean=bean;
                        Object nextObj = sectionList.get(end);
                        if (nextObj instanceof Animation) {
                            Animation anim = (Animation) nextObj;
                            if (anim.getWidth() + x + dx > width) {
                                g.drawString(s, x+(bean.isM()?1:0), y + rowh+(bean.isM()?1:0));
                                if (bean.getType()!=1) {
                                    g.drawLine(x+(bean.isM()?1:0), y+3+rowh+(bean.isM()?1:0), x+fm.stringWidth(s)+(bean.isM()?1:0), y+3+rowh+(bean.isM()?1:0));
                                }
                                lastStr = null;
                                lastBean= null;
                            }
                        }
                    } else {
                        if (font.getFontName().equals("YaHei Consolas Hybrid")||font.getFontName().equals("方正粗圆_GBK")) {
                            Graphics2D g2d = (Graphics2D) g.create();
//                            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                            g2d.setColor(Color.black);
                            g2d.drawString(s, x+(bean.isM()?1:0)+ 2  + 1, y + rowh+(bean.isM()?1:0));
                            g2d.drawString(s, x+(bean.isM()?1:0)+ 2 , y + rowh+(bean.isM()?1:0) +1 );
                            g2d.setColor(g.getColor());
                            g2d.drawString(s, x+(bean.isM()?1:0)+ 2 , y + rowh+(bean.isM()?1:0));
                            g2d.dispose();
                        }else {
                            g.drawString(s, x+(bean.isM()?1:0), y + rowh+(bean.isM()?1:0));
                        }


                        if (bean.getType()!=1) {
                            g.drawLine(x+(bean.isM()?1:0),y+3+rowh+(bean.isM()?1:0), x+fm.stringWidth(s)+(bean.isM()?1:0),y+3+rowh+(bean.isM()?1:0));
                        }
                    }
                    if (index < len || x + dx == width) {
                        begin = index;
                        x = 0;
                        y += rowh;
                        rowh = fm.getHeight();
                    } else {
                        x += fm.stringWidth(s);
                    }
                }
                if (bean.getHeight()==null) {
                    bean.setHeight(fm.getHeight());
                    bean.setE_x(x+20);
                    bean.setE_y(y-fm.getHeight()+rowh);
                }
                g.setColor(color);
            }
        }
        point.move(x, y);
        return point;
    }
    private Point point=new Point(0, 0);
    public void setText(String text) {
        this.text = text;
        sectionList.clear();
        this.isInteractive=false;
        parseText(text);
    }


    public void addText(String text) {
        this.text += text;
        parseText(text);
    }

    public String getText() {
        return text;
    }
    public void remove(){
        sectionList.clear();
        this.isInteractive=false;

        disable();
    }

    public void setTextSize(String text,int size) {
        this.text = text;
        sectionList.clear();
        this.isInteractive=false;
        parseText(text);
        setSize(computeSize(size));
    }

    static BufferedImage temp = new BufferedImage(1, 1,BufferedImage.TYPE_USHORT_565_RGB);
    public static int fontWidth(Font font,String text) {
        synchronized (temp) {
            try {
                Graphics g = temp.getGraphics();
                g.setFont(font);
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                return fm.stringWidth(text);
            } catch (Exception e) {
                return org.come.until.SafeFontMetrics.stringWidth(font, text);
            }
        }
    }
    public static int w = 0;
    public Dimension computeSize(int maxwidth) {
        if (sectionList.size() == 0)
            return new Dimension(1, 1);
        synchronized (temp) {
            int rowWidth = 0, y = 0, rowHeight = 0;
            int count = sectionList.size(), start = 0, x = 0;
            Graphics g = temp.getGraphics();
            if (font != null) {
                g.setFont(font);
            } else {
                g.setFont(UIUtils.TEXT_FONT);
            }
            boolean is = false;
            for (int i = 0; i < count; i++) {
                Object obj = sectionList.get(i);
                if (obj instanceof String) {
                    String str = (String) obj;
                    FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                    rowHeight = Math.max(rowHeight, fm.getHeight());
                    int dx = fm.stringWidth(str);
                    if (rowWidth + dx <= maxwidth) {
                        rowWidth += dx;
                    } else {
                        Point p = paintRichText(g, x, y, maxwidth, rowHeight,
                                start, i + 1);
                        start = i + 1;
                        rowWidth = p.x;
                        rowHeight = fm.getHeight();
                        x = p.x;
                        y = p.y;
                    }
                } else if (obj instanceof Animation) {
                    Animation anim = (Animation) obj;
                    if (anim.getWidth() + rowWidth > maxwidth) {
                        paintRichText(g, x, y, maxwidth, rowHeight, start, i);
                        start = i;
                        x = 0;
                        y += rowHeight;
                        rowWidth = anim.getWidth();
                        rowHeight = anim.getHeight();
                    } else {
                        rowHeight = Math.max(rowHeight, anim.getHeight());
                        rowWidth += anim.getWidth();
                    }

                } else if (obj instanceof Integer) {// line swap
                    paintRichText(g, x, y, maxwidth, rowHeight, start, i + 1);
                    start = i;
                    x = 0;
                    y += rowHeight;
                    rowWidth = 0;
                    rowHeight = 0;
                }else if (obj instanceof InputBean) {
                    InputBean bean=(InputBean) obj;
                    if (bean.getType()==-1) {is=true;continue;}
                    String str = bean.getName();
                    FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g);
                    rowHeight = Math.max(rowHeight, fm.getHeight());
                    int dx = fm.stringWidth(str);
                    if (rowWidth + dx <= maxwidth) {
                        rowWidth += dx;
                    } else {
                        Point p = paintRichText(g, x, y, maxwidth, rowHeight,start, i + 1);
                        start = i + 1;
                        rowWidth = p.x;
                        rowHeight = fm.getHeight();
                        x = p.x;
                        y = p.y;
                    }
                }
            }

            Point p = paintRichText(g, x, y, maxwidth, rowHeight, start, count);
            if (is) {maxwidth=0;}
            else if (y == 0&&p.x!=0){maxwidth = p.x;}
            return new Dimension(maxwidth, y + rowHeight + 4 );// FIXME height!!1
        }
    }

    @Override
    public void paintImmediately(int x, int y, int w, int h) {
        // super.paintImmediately(x, y, w, h);
    }
    /**判断是否点击到展示*/
    public InputBean isMonitor(int x, int y, MouseEvent e){
        if (isInteractive) {
            int count = sectionList.size();
            for (int i = 0; i < count; i++) {
                Object obj = sectionList.get(i);
                if (obj instanceof InputBean) {
                    InputBean bean=(InputBean) obj;
                    if (bean.getType()==-1) {continue;}
                    if (bean.isMonitor(x, y,e)) {
                        return bean;
                    }
                }
            }
        }
        return null;
    }
}
