package com.tool.btn;

import jxy2.chatv.ChatFrame;
import jxy2.chatv.ChatRichLabel;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.login.GameView;
import org.come.until.MessagrFlagUntil;

import java.awt.*;
import java.awt.event.MouseEvent;

public class SmallIconBtn extends MoBanBtn {

    private ZhuJpanel zhuJpanel;
    private String text;
    private int caozuo;

    public SmallIconBtn(String iconpath, int type, int caozuo, String text, ZhuJpanel zhuJpanel) {
        super(iconpath, type);
        this.zhuJpanel = zhuJpanel;
        this.text = text;
        this.caozuo = caozuo;
    }

    public SmallIconBtn(String iconpath, int type, int caozuo, String text, int num, ZhuJpanel zhuJpanel) {
        super(iconpath, type);
        this.zhuJpanel = zhuJpanel;
        this.text = text;
        this.caozuo = caozuo;
    }

    public SmallIconBtn(String iconpath, int type, int caozuo, String names) {
        super(iconpath,0 ,type,0,0);
        this.caozuo = caozuo;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }
    @Override
    public void nochoose(MouseEvent e) {
//        if (text==null)return;
//        if (text.equals("添加") && caozuo == 100 && FightingMixDeal.State == HandleState.USUAL) {
//            addFriend();
//        } else if (text.equals("删除") && caozuo == 100 && FightingMixDeal.State == HandleState.USUAL) {
//
//        } else if (text.equals("查找") && caozuo == 101 && FightingMixDeal.State == HandleState.USUAL) {
//            FormsManagement.showForm(75);
//        }
        if (GameView.getFrameDialogSync().dialog.isVisible()) {
            Component view;
            switch (caozuo) {
                case 0:
                case 1:
                    GameView.getFrameDialogSync().getDialog().setVisible(false);
                    ChatFrame.getChatJPanel().setVisible(true);
                    view = GameView.getFrameDialogSync().getChangeJpanel().getScrollPane().getViewport().getView();
                    ChatFrame.getChatJPanel().getjScrollPane().setViewportView(view);
                    FrameMessageChangeJpanel.refreshHeight(GameView.getFrameDialogSync().getChangeJpanel().getScrollPane());
                    // 清空目标容器，防止残留
                    FrameMessageChangeJpanel.chatbox.getChatlabels().clear();
                    // 只遍历一次，严格分配
                    for (ChatRichLabel label : FrameMessageChangeJpanel.chatbox1.getChatlabels()) {
                        FrameMessageChangeJpanel.chatbox.getChatlabels().add(label);
                    }
                    for (ChatRichLabel label : FrameMessageChangeJpanel.chatbox2.getChatlabels()) {
                        FrameMessageChangeJpanel.chatbox.getChatlabels().add(label);
                    }
                    ChatFrame.getChatJPanel().RunYexcp(0,0,1);
                    break;
            }
        }
    }

    /**
     * 添加好友
     */
    public void addFriend() {

        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE4);
    }

}
