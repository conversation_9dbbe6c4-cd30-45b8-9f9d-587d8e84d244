package jxy2.wsyl;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class LXiulianMainFrame extends JInternalFrame implements MouseListener {

    private static final long serialVersionUID = 6487538265898556693L;
    private int first_x, first_y;// x、y坐标
    private LXiulianMainPanel skillPromoteMainPanel;

    /**
     * 实例化界面
     *
     * @return
     */
    public static LXiulianMainFrame getLXiulianMainFrame() {
        return (LXiulianMainFrame) FormsManagement.getInternalForm(124).getFrame();
    }

    public LXiulianMainFrame() {
        super();
        skillPromoteMainPanel = new LXiulianMainPanel();
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        ((BasicInternalFrameUI) this.getUI()).setNorthPane(null);// 去除顶部的边框
        this.setBounds(344, 118, 370,354);
        this.setBackground(UIUtils.Color_BACK);
        this.add(skillPromoteMainPanel);
        this.pack();

        this.addMouseMotionListener(new MouseMotionListener() {// 判断窗口移动的位置

            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(), getWidth(), getHeight());
                }
            }
        });
        this.addMouseListener(this);

    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // 开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
         boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击
            FormsManagement.HideForm(124);

        } else {
            FormsManagement.Switchinglevel(124);
        }
        this.first_x = e.getX();
        this.first_y = e.getY();

    }

    @Override
    public void mouseReleased(MouseEvent e) {
    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {
        MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
    }

    public LXiulianMainPanel getLXiulianMainPanel() {
        return skillPromoteMainPanel;
    }

    public void setLXiulianMainPanel(LXiulianMainPanel skillPromoteMainPanel) {
        this.skillPromoteMainPanel = skillPromoteMainPanel;
    }
}
