package com.tool.btn;

import com.tool.PanelDisplay.RolePanelShow;
import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import jxy2.flight.FlyFrame;
import jxy2.qqiubook.QianqiuBookFrame;
import jxy2.wormap.WorldMapBtn;
import org.cbg.bean.SearchGoodsBean;
import org.cbg.frame.TrslationMainJframe;
import org.come.Frame.*;
import org.come.Jpanel.ZhuJpanel;
import org.come.daily.JframeDailyMain;
import org.come.entity.Baby;
import org.come.socket.Agreement;
import org.come.socket.GameClient;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

public class FormsOnOffBtn extends MoBanBtn {

    private int formsid;
    public FormsOnOffBtn(String iconpath, int type, int id) {
        super(iconpath,0, type);
        // TODO Auto-generated constructor stub
        this.formsid = id;
    }

    public FormsOnOffBtn(ImageIcon[] imageIcons ,int type, int id) {
        super(imageIcons,0, type);
        // TODO Auto-generated constructor stub
        this.formsid = id;
    }

    public FormsOnOffBtn(String iconpath, int type, int id,int ty,int num) {
        super(iconpath,0, type,0,0);
        // TODO Auto-generated constructor stub
        this.formsid = id;
    }

    public FormsOnOffBtn(ImageIcon[] imageIcons, int type, int id,String tecx) {
        super(imageIcons, id,type);
        // TODO Auto-generated constructor stub
        setNtext(tecx);
        this.formsid = id;
    }

    public FormsOnOffBtn(String iconpath, int type, int id,int index) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.formsid = id;
    }

    public FormsOnOffBtn(String iconpath, int type, String text, int id) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0,"",text);
        this.formsid = id;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }
    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        if (FormsManagement.getInternalForm3(formsid) != null) {
            if (formsid == 2)
                ZhuJpanel.setUseGoodsType(0);
            if (formsid == 14) {
                String send = Agreement.getAgreement().TransStateAgreement("2");
                SendMessageUntil.toServer(send);
                return;
            }
            if (formsid == 16 || formsid == 15) {
                FormsManagement.HideForm(formsid == 16 ? 15 : 16);
            } else if (formsid == 47) {
                ZhuJpanel.setNedangoods(null);// 清空选中的内丹
            }
            FormsManagement.HideForm(formsid);
            // 关闭音效
            Music.addyinxiao("关闭窗口.mp3");
            return;
        }

        switch (formsid) {
        case 0:
            RolePanelShow.Show();
            break;
        case 1:
            Baby baby = UserMessUntil.getbaby(TestChildJframe.getTestChildJframe().getTestChildJpanel().getBabyid());
            TestChildJframe.getTestChildJframe().getTestChildJpanel().ShowBaby(baby);
            FormsManagement.showForm(1);
            break;
        case 2:
            ZhuJpanel.setUseGoodsType(0);
            FormsManagement.showForm(2);
//            TestRankJPanel testRankJPanel = TestpackJframe.getTestpackJframe().getPackMainJPanel().getCardJPanel().getTestRankJPanel();
//            if (testRankJPanel.getState()==null){
//                String mes = Agreement.getAgreement().initJingjieAgreement("J");
//                SendMessageUntil.toServer(mes);
//            }
            break;
        case 3:
            FormsManagement.showForm(3);
            TesttaskJframe.getTesttaskJframe().getJtask().showTaskMethod();
            break;
        case 4:
            MessagrFlagUntil.ReceiveFriend();
            break;
        case 5:
            String oneArenaSendmes = Agreement.getAgreement().oneArenaAgreement("1");
            SendMessageUntil.toServer(oneArenaSendmes);
            break;
        case 48:
            Show();
            break;
        case 7:
            ZhuFrame.getZhuJpanel().HzShowBtn(!ZhuJpanel.getMountBtn().isVisible());
            break;
        case 22:
            TestsmallmapJframe.getTestsmallmapJframe(0).setLocation(70, 80);
            FormsManagement.showForm(formsid);
            break;
        case 60:
            // // 展示超级富豪榜
            // 向服务器发送请求排行榜数据
            String mes = Agreement.getAgreement().pankinglistAgreement("1");
            SendMessageUntil.toServer(mes);
            break;
        case 39:
            break;
        case 40:
            if (FormsManagement.getInternalForm2(40) != null) {
                    ActivityJframe.getActivityJframe().getActivityJpanel().refreshView();

            }
            FormsManagement.showForm(formsid);
            break;
        case 78:
            /** 展示藏宝阁窗口 */
//            if(FormsManagement.getInternalForm2(78) == null){
                FormsManagement.showForm(78);
//            }else{
//                if(!FormsManagement.getframe(78).isVisible()){
//                    FormsManagement.showForm(78);
//                }
//            }
            /**
             * 向服务器获取最新上架的物品信息 1、初始化协议 2、修改搜索类型(0、首页最新上架页面 1、首页 2、游戏币 3、道具 4、召唤兽
             * 5、装备 6、灵宝 7、公示期 8、收藏 9、我要卖之已寄售品) 3、当前页码(默认为1) 4、转换为json字符串
             * 5、上传给服务器
             */
            SearchGoodsBean searchGoodsBean = new SearchGoodsBean();
            searchGoodsBean.setSaletype(1);
            searchGoodsBean.setPageNum(1);
            String msg = Agreement.getAgreement().searchGoodsAgreement(
                    GsonUtil.getGsonUtil().getgson().toJson(searchGoodsBean));
            SendMessageUntil.toServer(msg);
            TrslationMainJframe.getTrslationMainJframe().setPanelOpen(0);
            
            break;
        case 90:
            JframeDailyMain.getJframeDailyMain().getJpanelDailyMain().getEventMap(-1);
            FormsManagement.showForm(90);
            break;
        case 91:
            complainOpenWeb();
            break;
        case 99:
            System.gc();
            FormsManagement.showForm(99);
            IphoneVerifyFrame.getIphoneVerifyFrame().getIphoneVerifyPanel().changeMenu(-1);
            break;
        case 105:
            PartnerJframe.getPartnerJframe().getPartnerMainJpanel().addPartnerUnit();
            break;
        case 106://坐骑
            // 向服务器发送消息请求坐骑列表
            FormsManagement.showForm(7);
            String sendmes = Agreement.getAgreement().MountAgreement();
            SendMessageUntil.toServer(sendmes);
            ZhuFrame.getZhuJpanel().HzShowBtn(false);
        break;
        case 107://飞行器
            Util.StopFrame(148);
            ZhuFrame.getZhuJpanel().HzShowBtn(false);
            FlyFrame.getFlightFrame().getFlightJPanel().InitializeAircraftData();
         break;
        case 108://
            Util.StopFrame(129);
         break;
        case 109://
            int MapID = Math.toIntExact(RoleData.getRoleData().getLoginResult().getMapid());
            if (!TestsmallmapJframe.getTestsmallmapJframe(0).isVisible()) {
                if (Util.mapmodel.getMin_x() > 0) {
                    WorldMapBtn.NewWorldMapImgShow(MapID,0,false);
                    TestsmallmapJframe.getTestsmallmapJframe(0).setLocation(70, 80);
                    TestsmallmapJframe.getTestsmallmapJframe(0).getTestsmallmapJpanel().OpenMapFXQ();
                    Music.addyinxiao("开关窗口.mp3");

                }
            } else {
                FormsManagement.HideForm(22);
                Music.addyinxiao("关闭窗口.mp3");
            }
         break;
        case 110:
            Util.StopFrame(151);
            QianqiuBookFrame.getQianqiuBookFrame().getQianqiuBookJPanel().initData();
            break;
        default:
            FormsManagement.showForm(formsid);
            break;
        }
        // 打开面板
        Music.addyinxiao("开关窗口.mp3");
    }

    /** 传递参数发送给问题反馈页面 */
    public void complainOpenWeb() {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("roleName", ImageMixDeal.userimg.getRoleShow().getRolename());
        params.put("roleAccount", RoleData.getRoleData().getLoginResult().getUserName());
        /** zrikka 2020 0414 **/
//        params.put("roleQuid", GameClient.potAndIpStrings[5]);
        params.put("roleQuid", GameClient.potAndIpStrings[5]);
        /***/
        String url = "http://www.dongmengzhongchou.com:8080/question";
        StringBuffer sb = new StringBuffer();
        sb.append(url);
        sb.append("?");
        if (params != null) {
            // 将哈希表参数转化为字符串
            for (Entry<String, Object> e : params.entrySet()) {
                sb.append(e.getKey());
                sb.append("=");
                sb.append(e.getValue());
                sb.append("&");
            }
            sb = sb.deleteCharAt(sb.length() - 1);
        }
        String stringParams = sb.toString();
        Desktop desktop = Desktop.getDesktop();
        try {
            desktop.browse(new URI(stringParams));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (URISyntaxException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static void Show() {
        // TODO Auto-generated method stub
        if (ImageMixDeal.userimg.getRoleShow().getGang_id() == null
                || ImageMixDeal.userimg.getRoleShow().getGang_id().intValue() == 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("你没有帮派!");
            return;
        }
        if (FormsManagement.getframe(48).isVisible()) {
            FormsManagement.HideForm(48);
            return;
        }
        String senmes = Agreement.getAgreement().IntogangAgreement(
                ImageMixDeal.userimg.getRoleShow().getGang_id().toString());
        // 向服务器发送信息
        SendMessageUntil.toServer(senmes);
    }
}