package jxy2.wormap;

import com.tool.btn.MoBanBtn;
import jxy2.jutnil.Juitil;
import org.come.Frame.TestsmallmapJframe;
import org.come.Jpanel.TestsmallmapJpanel;
import org.come.bean.Mapmodel;
import org.come.model.Gamemap;
import org.come.until.*;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.Calendar;
import java.util.GregorianCalendar;

public class WorldMapBtn extends MoBanBtn {
    //菜单
    public int mapid;
    public boolean is = false;
    public Mapmodel mapmodel = new Mapmodel();
    public WorldMapJPanel worldMapJPanel;
    public WorldMapBtn(String iconpath, int type, int mapid,WorldMapJPanel worldMapJPanel) {
        super(iconpath,0, type);
        this.mapid= mapid;
        this.worldMapJPanel= worldMapJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        int MapID =  Juitil.getMapButton(mapid);
        if (mapid==50||mapid==22||mapid==17||mapid==18||mapid==46){
            if (!FormsManagement.getframe(130).isVisible()) {
                FormsManagement.showForm(130);
                WordlMapMenuFrame.getWordlMapMenuFrame().getWordlMapMenuJPanel().initMapID(mapid);
                Music.addyinxiao("开关窗口.mp3");
            } else {
                FormsManagement.HideForm(130);
                Music.addyinxiao("关闭窗口.mp3");
            }
        }else if (mapid==55){
            if (!FormsManagement.getframe(131).isVisible()) {
                NpcQueryFrame.getNpcQueryFrame().getNpcQueryJPanel().LoadNPC(Util.mapmodel.getGamemap().getMapname());
                FormsManagement.showForm(131);
                Music.addyinxiao("开关窗口.mp3");
            } else {
                FormsManagement.HideForm(131);
                Music.addyinxiao("关闭窗口.mp3");
            }
        }else if (mapid==56){

        }else {
            NewWorldMapImgShow(MapID,1,false);
        }
    }


    public static void NewWorldMapImgShow(int MapID,int type,boolean is){
        Gamemap gamemap = UserMessUntil.getAllmapbean().getAllMap().get(MapID + "");
        int right = Integer.parseInt(gamemap.getWidth());
        int bottom = Integer.parseInt(gamemap.getHeight());
        Util.mapmodel.MapsizeWorld(right,bottom);
        GregorianCalendar calendar = new GregorianCalendar();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (gamemap.getSmallmap().isEmpty()){return;}//没有小地图结束
        Image image;
        if ((MapID == 1193 || MapID == 1207) && (hour >= 19 || hour <= 7)) {
            image = CutButtonImage.getImage("resource/smap/s" + MapID + "1.png",-1,-1).getImage();
        } else {
            image = CutButtonImage.getImage("resource/smap/s" + MapID + ".png",-1,-1).getImage();
        }

        if (image.getWidth(null) <= 0) {
            TestsmallmapJframe.getTestsmallmapJframe(type).setBounds(70, 80, 50, 50);
        } else {
            TestsmallmapJframe.getTestsmallmapJframe(type).setBounds(120, 118, image.getWidth(null) + 83,
                    image.getHeight(null) + 64);
        }
        TestsmallmapJpanel testjpanel = TestsmallmapJframe.getTestsmallmapJframe(type).getTestsmallmapJpanel();
        TestsmallmapJframe.getTestsmallmapJframe(type).setStartimg(is);
        testjpanel.LoadAllNPC(true,MapID+"");
        testjpanel.select.setVisible(!testjpanel.isVisible());
        testjpanel.isopen = false;
        testjpanel.getNpcNameCode().setText("查询NPC名字");
        TestsmallmapJframe.getTestsmallmapJframe(type).setMapID(MapID);
        Util.mapmodel.MiniMap(image,gamemap.getMapname(),type);
        testjpanel.OpenMapFXQ();
        FormsManagement.showForm(22);
    }



    public int getMapid() {
        return mapid;
    }

    public void setMapid(int mapid) {
        this.mapid = mapid;
    }

}
