package com.tool.tcpimg;

import jxy2.jutnil.ImgConstants;
import org.come.bean.ImgZoom;
import org.come.until.CutButtonImage;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import java.awt.*;

public class TipBox{
	  private RichLabel label;
	  private int time;
	  private ImgZoom imgZoom;
		private static int lastHeight = 0; // 上一个提示框的高度
		private int y; // 当前提示框的 Y 坐标
		public static boolean is = false;
      //x偏移量
      private int px;
      private int width;
      private int height;
      public TipBox(String text) {
          label = new RichLabel(text,UIUtils.MSYH_HY15,320);
		  label.setLocation(15, 10);
          Dimension d = label.computeSize(320);
          label.setSize(d);
		  width=d.width-50;
		  height=d.height+24;
          this.imgZoom= CutButtonImage.newcutsPng(ImgConstants.TxtImg, 6,6, true,"defaut.wdf");
		  if (imgZoom==null) {return;}
          imgZoom.setMiddlew(d.width);
          imgZoom.setMiddleh(d.height);
          this.time = Util.TIME_CHAT2;
		  y = lastHeight + 10; // 计算当前提示框的 Y 坐标
		  lastHeight = y + height; //       更新上一个提示框的高度
		  imgZoom.setMiddlew(320);
		  imgZoom.setMiddleh(d.height+10);
		  if (width>=320){
			  px=ScrenceUntil.Screen_x/3-d.width/2;
		  }else {
			  px=ScrenceUntil.Screen_x/4 +100 -d.width/500;
		  }

      }
      public void paint(Graphics g) {
    	  g.translate(px,0);
    	  imgZoom.draw(g);
    	  g.translate(label.getX(),label.getY());
          label.paint(g);
          g.translate(-px-label.getX(),-label.getY());
      }
	public RichLabel getLabel() {
		return label;
	}
	public void setLabel(RichLabel label) {
		this.label = label;
	}
	public boolean IsTime() {
		return time-->0;
	}
	public ImgZoom getImgZoom() {
		return imgZoom;
	}
	public void setImgZoom(ImgZoom imgZoom) {
		this.imgZoom = imgZoom;
	}
	public int getPx() {
		return px;
	}
	public void setPx(int px) {
		this.px = px;
	}
	public int getWidth() {
		return width;
	}
	public void setWidth(int width) {
		this.width = width;
	}
	public int getHeight() {
		return height;
	}
	public void setHeight(int height) {
		this.height = height;
	}
      
}
