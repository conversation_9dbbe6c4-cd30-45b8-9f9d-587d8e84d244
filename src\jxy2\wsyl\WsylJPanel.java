package jxy2.wsyl;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import jxy2.LocalTextPathUtil;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
* 万兽有灵(灵犀)
* <AUTHOR>
* @date 2024/8/30 下午10:11
*/

public class WsylJPanel extends JPanel {
    private WsylBtn exbtn,washUpBtn,suppBtn,allbtn,queding;
    private JLabel[] labImg = new JLabel[3];
    private RichLabel[] richLabels = new RichLabel[3];
    private XSkillLx[] wuli,fashu,fuzhu;
    private static JLabel lingxidian;
    private int dianshu;
    // 当前打开面板类型
    private int panelType;

    public boolean stopjpanel =true;
    public WsylJPanel() {
        this.setPreferredSize(new Dimension(644, 405));
        this.setOpaque(false);
        this.setLayout(null);
        Juitil.addClosingButtonToPanel(this,123,644);
        exbtn = new WsylBtn(ImgConstants.tz34,1,0,"","兑换",this);
        exbtn.setBounds(173, 367, 59, 26);
        this.add(exbtn);
        washUpBtn = new WsylBtn(ImgConstants.tz34,1,1,"","洗练",this);
        washUpBtn.setBounds(173+65, 367, 59, 26);
        this.add(washUpBtn);
        suppBtn = new WsylBtn(ImgConstants.tz34,1,2,"","补充",this);
        suppBtn.setBounds(80+185*2, 367, 59, 26);
        this.add(suppBtn);
        allbtn = new WsylBtn(ImgConstants.tz227,1,3,"","",this);
        allbtn.setVisible(false);
        allbtn.setBounds(6, 300, 26, 43);
        this.add(allbtn);
        queding = new WsylBtn(ImgConstants.tz85, -1, 6, "","确定加点",this);
        queding.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz239,"defaut.wdf"));
        queding.setVisible(false);
        queding.setBounds(150+185*2, 367, 82, 26);
        this.add(queding);
        panelType = -1;
        for (int i = 0; i < labImg.length; i++) {
            labImg[i] = new JLabel();
            labImg[i].setBounds(50+185*i,15,171,346);
            labImg[i].addMouseListener(new WsylJMouse(this,i));
            this.add(labImg[i]);
        }


        for (int i = 0; i < richLabels.length; i++) {
            richLabels[i] = new RichLabel(LocalTextPathUtil.Srext("1"), UIUtils.FZCY_HY13, 15);
            richLabels[i].setBounds(53+i*185,305,165,346-30);
            this.add(richLabels[i]);
        }


        lingxidian = new JLabel();
        lingxidian.setBounds(145, 370, 45, 14);
        lingxidian.setForeground(Color.WHITE);
        lingxidian.setFont(new Font("微软雅黑", 1, 12));
        lingxidian.setVerticalTextPosition(SwingConstants.CENTER);
        lingxidian.setHorizontalTextPosition(SwingConstants.CENTER);
        lingxidian.setText("0");
        lingxidian.setVisible(false);
        this.add(lingxidian);

    }
    public void changePanel(int type) {
        if (type == panelType) {
            showType(type);

            return;
        }
        switch (type) {
            case 0:
                queding.setVisible(false);
                allbtn.setVisible(false);
                for (int i = 0; i < 3; i++) {
                    richLabels[i].setVisible(true);
                    labImg[i].setVisible(true);
                }
                panelType = 0;
                stopjpanel = true;
                break;
            case 1:
                queding.setVisible(true);
                panelType = 1;
                allbtn.setVisible(true);
                for (int i = 0; i < 3; i++) {
                    richLabels[i].setVisible(false);
                    labImg[i].setVisible(false);
                }
                stopjpanel = false;
                break;
            case 2:
                queding.setVisible(true);
                allbtn.setVisible(true);
                panelType = 2;
                for (int i = 0; i < 3; i++) {
                    richLabels[i].setVisible(false);
                    labImg[i].setVisible(false);
                }
                stopjpanel = false;
                break;
            case 3:
                queding.setVisible(true);
                allbtn.setVisible(true);
                panelType = 3;
                for (int i = 0; i < 3; i++) {
                    richLabels[i].setVisible(false);
                    labImg[i].setVisible(false);
                }
                stopjpanel = false;
                break;
            default:
                queding.setVisible(false);
                allbtn.setVisible(false);
                panelType = 0;
                for (int i = 0; i < 3; i++) {
                    richLabels[i].setVisible(true);
                    labImg[i].setVisible(true);
                }
                stopjpanel = true;
                break;
        }
        showType(type);
    }

    public void showType(int type) {
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        int ssn = Integer.parseInt(pet.getSsn());

        switch (type) {
            case 0:
                if (wuli != null) {
                    for (XSkillLx lx : wuli) {
                        lx.setVisible(false);
                    }
                }
                if (fashu != null) {
                    for (XSkillLx lx : fashu) {
                        lx.setVisible(false);
                    }
                }
                if (fuzhu != null) {
                    for (XSkillLx lx : fuzhu) {
                        lx.setVisible(false);
                    }
                }
                break;
            case 1:
                if (fashu != null) {
                    for (XSkillLx lx : fashu) {
                        lx.setVisible(false);
                    }
                }
                if (fuzhu != null) {
                    for (XSkillLx lx : fuzhu) {
                        lx.setVisible(false);
                    }
                }
                if (wuli == null) {
                    wuli = getWuli();
                    for (XSkillLx lx : wuli) {
                        this.add(lx);
                    }
                } else {
                    for (int i = 0; i < wuli.length; i++) {
                        XSkillLx lx = wuli[i];
                        lx.index = i;
                        boolean shouldBeVisible = (ssn == 0 && i < 18) || (ssn == 1 && i < 21) || (ssn == 4||ssn == 2 && i < 24);
                        lx.setVisible(shouldBeVisible);
                    }
                }
                break;
            case 2:
                if (fuzhu != null) {
                    for (XSkillLx lx : fuzhu) {
                        lx.setVisible(false);
                    }
                }
                if (wuli != null) {
                    for (XSkillLx lx : wuli) {
                        lx.setVisible(false);
                    }
                }

                if (fashu == null) {
                    fashu = getFashu();
                    for (XSkillLx lx : fashu) {
                        this.add(lx);
                    }
                } else {
                    for (int i = 0; i < fashu.length; i++) {
                        XSkillLx lx = fashu[i];
                        boolean shouldBeVisible = (ssn == 0 && i < 18) || (ssn == 1 && i < 21) || (ssn == 4||ssn == 2 && i < 24);
                        lx.setVisible(shouldBeVisible);
                    }
                }


                break;
            case 3:
                if (fashu != null) {
                    for (XSkillLx lx : fashu) {
                        lx.setVisible(false);
                    }
                }
                if (wuli != null) {
                    for (XSkillLx lx : wuli) {
                        lx.setVisible(false);
                    }
                }
                if (fuzhu == null) {
                    fuzhu = getFuzhu();
                    for (XSkillLx lx : fuzhu) {
                        this.add(lx);
                    }
                } else {
                    for (int i = 0; i < fuzhu.length; i++) {
                        XSkillLx lx = fuzhu[i];
                        boolean shouldBeVisible = (ssn == 0 && i < 18) || (ssn == 1 && i < 21) || (ssn == 4||ssn == 2 && i < 24);
                        lx.setVisible(shouldBeVisible);
                    }
                }


                break;

            case 4:
                if (fashu != null) {
                    for (XSkillLx lx : fashu) {
                        lx.setVisible(false);
                        lx.reset();
                    }
                }
                if (wuli != null) {
                    for (XSkillLx lx : wuli) {
                        lx.setVisible(false);
                        lx.reset();
                    }
                }
                if (fuzhu != null) {
                    for (XSkillLx lx : fuzhu) {
                        lx.setVisible(false);
                        lx.reset();
                    }
                }
                break;

            default:
                break;
        }
    }
    /**
     * 物理灵犀
     * @return
     */
    public XSkillLx[] getWuli() {

        XSkillLx[] lx = new XSkillLx[24];
        LxPointBean[] points = new LxPointBean[24];

            points[0] = new LxPointBean(1, 11003, 5, 0);
            lx[0] = new XSkillLx(points[0], 6, 1, 1);

            points[1] = new LxPointBean(1, 11001, 5, 0);
            lx[1] = new XSkillLx(points[1], 6, 2, 1);

            points[2] = new LxPointBean(1, 11004, 5, 0);
            lx[2] = new XSkillLx(points[2], 6, 3, 1);

            points[3] = new LxPointBean(1, 11005, 5, 0);
            lx[3] = new XSkillLx(points[3], 6, 4, 1);

            points[4] = new LxPointBean(1, 11006, 5, 0);
            lx[4] = new XSkillLx(points[4], 6, 5, 1);

            points[5] = new LxPointBean(1, 11007, 5, 0);
            lx[5] = new XSkillLx(points[5], 6, 6, 1);


            // 222222222222222222

            points[6] = new LxPointBean(2, 11008, 3, 0);
            lx[6] = new XSkillLx(points[6], 4, 1, 1);

            points[7] = new LxPointBean(2, 11009, 3, 0);
            lx[7] = new XSkillLx(points[7], 4, 2, 1);

            points[8] = new LxPointBean(2, 11010, 3, 0);
            lx[8] = new XSkillLx(points[8], 4, 3, 1);

            points[9] = new LxPointBean(2, 11011, 3, 0);
            lx[9] = new XSkillLx(points[9], 4, 4, 1);

            // 33333333333
            points[10] = new LxPointBean(3, 11012, 3, 0);
            lx[10] = new XSkillLx(points[10], 2, 1, 1);

            points[11] = new LxPointBean(3, 11013, 3, 0);
            lx[11] = new XSkillLx(points[11], 2, 2, 1);

            // 4444444444
            points[12] = new LxPointBean(4, 11014, 3, 0);
            lx[12] = new XSkillLx(points[12], 2, 1, 1);

            points[13] = new LxPointBean(4, 11015, 3, 0);
            lx[13] = new XSkillLx(points[13], 2, 2, 1);

            // 555555555555
            points[14] = new LxPointBean(5, 11016, 3, 0);
            lx[14] = new XSkillLx(points[14], 2, 1, 1);

            points[15] = new LxPointBean(5, 11017, 3, 0);
            lx[15] = new XSkillLx(points[15], 2, 2, 1);

            // 666666666666666
            points[16] = new LxPointBean(6, 11018, 4, 0);
            lx[16] = new XSkillLx(points[16], 2, 1, 1);

            points[17] = new LxPointBean(6, 11019, 4, 0);
            lx[17] = new XSkillLx(points[17], 2, 2, 1);


            points[18] = new LxPointBean(7, 11020,30,0);
            lx[18] = new XSkillLx(points[18],6,1,1);

            points[19] = new LxPointBean(7, 11021,30,0);
            lx[19] = new XSkillLx(points[19],6,2,1);

            points[20] = new LxPointBean(7, 11022,30,0);
            lx[20] = new XSkillLx(points[20],6,3,1);

            points[21] = new LxPointBean(7, 11023,30,0);
            lx[21] = new XSkillLx(points[21],6,4,1);

            points[22] = new LxPointBean(7, 11024,30,0);
            lx[22] = new XSkillLx(points[22],6,5,1);

            points[23] = new LxPointBean(7, 11025,30,0);
            lx[23] = new XSkillLx(points[23],6,6,1);
            return lx;



    }

    /**
     * 法术灵犀
     * @return
     */
    public XSkillLx[] getFashu() {
        XSkillLx[] lx = new XSkillLx[23];

        LxPointBean[] points = new LxPointBean[23];

        points[0] = new LxPointBean(1, 11001,5,0);
        lx[0] = new XSkillLx(points[0],6,1,2);

        points[1] = new LxPointBean(1, 11004,5,0);
        lx[1] = new XSkillLx(points[1],6,2,2);

        points[2] = new LxPointBean(1, 11002,5,0);
        lx[2] = new XSkillLx(points[2],6,3,2);

        points[3] = new LxPointBean(1, 11005,5,0);
        lx[3] = new XSkillLx(points[3],6,4,2);

        points[4] = new LxPointBean(1, 11007,5,0);
        lx[4] = new XSkillLx(points[4],6,5,2);

        points[5] = new LxPointBean(1, 11026,5,0);
        lx[5] = new XSkillLx(points[5],6,6,2);


        // 222222222222222222

        points[6] = new LxPointBean(2, 11027,5,0);
        lx[6] = new XSkillLx(points[6],3,1,2);

        points[7] = new LxPointBean(2, 11028,4,0);
        lx[7] = new XSkillLx(points[7],3,2,2);

        points[8] = new LxPointBean(2, 11029,4,0);
        lx[8] = new XSkillLx(points[8],3,3,2);


        // 33333333333
        points[9] = new LxPointBean(3, 11031,3,0);
        lx[9] = new XSkillLx(points[9],2,1,2);
        points[10] = new LxPointBean(3, 11032,3,0);
        lx[10] = new XSkillLx(points[10],2,2,2);


        // 4444444444
        points[11] = new LxPointBean(4, 11033,3,0);
        lx[11] = new XSkillLx(points[11],2,1,2);
        points[12] = new LxPointBean(4, 11034,3,0);
        lx[12] = new XSkillLx(points[12],2,2,2);

        // 555555555555
        points[13] = new LxPointBean(5, 11035,3,0);
        lx[13] = new XSkillLx(points[13],2,1,2);
        points[14] = new LxPointBean(5, 11030,3,0);
        lx[14] = new XSkillLx(points[14],2,2,2);

        // 666666666666666
        points[15] = new LxPointBean(6, 11036,4,0);
        lx[15] = new XSkillLx(points[15],2,1,2);
        points[16] = new LxPointBean(6, 11037,4,0);
        lx[16] = new XSkillLx(points[16],2,2,2);

        // 7777777777777777
        points[17] = new LxPointBean(7, 11039,30,0);
        lx[17] = new XSkillLx(points[17],6,1,2);

        points[18] = new LxPointBean(7, 11040,30,0);
        lx[18] = new XSkillLx(points[18],6,2,2);

        points[19] = new LxPointBean(7, 11041,30,0);
        lx[19] = new XSkillLx(points[19],6,3,2);

        points[20] = new LxPointBean(7, 11042,30,0);
        lx[20] = new XSkillLx(points[20],6,4,2);

        points[21] = new LxPointBean(7, 11043,30,0);
        lx[21] = new XSkillLx(points[21],6,5,2);

        points[22] = new LxPointBean(7, 11044,30,0);
        lx[22] = new XSkillLx(points[22],6,6,2);

        return lx;
    }

    /**
     * 辅助灵犀
     * @return
     */
    public XSkillLx[] getFuzhu() {
        XSkillLx[] lx = new XSkillLx[23];

        LxPointBean[] points = new LxPointBean[23];

        points[0] = new LxPointBean(1, 11001,5,0);
        lx[0] = new XSkillLx(points[0],6,1,3);

        points[1] = new LxPointBean(1, 11004,5,0);
        lx[1] = new XSkillLx(points[1],6,2,3);

        points[2] = new LxPointBean(1, 11002,5,0);
        lx[2] = new XSkillLx(points[2],6,3,3);

        points[3] = new LxPointBean(1, 11005,5,0);
        lx[3] = new XSkillLx(points[3],6,4,3);

        points[4] = new LxPointBean(1, 11045,5,0);
        lx[4] = new XSkillLx(points[4],6,5,3);

        points[5] = new LxPointBean(1, 11046,5,0);
        lx[5] = new XSkillLx(points[5],6,6,3);


        // 222222222222222222
        points[6] = new LxPointBean(2, 11047,3,0);
        lx[6] = new XSkillLx(points[6],3,1,3);

        points[7] = new LxPointBean(2, 11048,5,0);
        lx[7] = new XSkillLx(points[7],3,2,3);

        points[8] = new LxPointBean(2, 11049,5,0);
        lx[8] = new XSkillLx(points[8],3,3,3);


        // 33333333333
        points[9] = new LxPointBean(3, 11050,5,0);
        lx[9] = new XSkillLx(points[9],2,1,3);
        points[10] = new LxPointBean(3, 11051,5,0);
        lx[10] = new XSkillLx(points[10],2,2,3);


        // 4444444444
        points[11] = new LxPointBean(4, 11052,3,0);
        lx[11] = new XSkillLx(points[11],2,1,3);
        points[12] = new LxPointBean(4, 11053,3,0);
        lx[12] = new XSkillLx(points[12],2,2,3);

        // 555555555555
        points[13] = new LxPointBean(5, 11054,5,0);
        lx[13] = new XSkillLx(points[13],2,1,3);
        points[14] = new LxPointBean(5, 11055,5,0);
        lx[14] = new XSkillLx(points[14],2,2,3);

        // 666666666666666
        points[15] = new LxPointBean(6, 11056,4,0);
        lx[15] = new XSkillLx(points[15],2,1,3);
        points[16] = new LxPointBean(6, 11057,4,0);
        lx[16] = new XSkillLx(points[16],2,2,3);

        // 7777777777777777
        points[17] = new LxPointBean(7, 11058,30,0);
        lx[17] = new XSkillLx(points[17],6,1,3);

        points[18] = new LxPointBean(7, 11059,30,0);
        lx[18] = new XSkillLx(points[18],6,2,3);

        points[19] = new LxPointBean(7, 11060,30,0);
        lx[19] = new XSkillLx(points[19],6,3,3);

        points[20] = new LxPointBean(7, 11061,30,0);
        lx[20] = new XSkillLx(points[20],6,4,3);

        points[21] = new LxPointBean(7, 11062,30,0);
        lx[21] = new XSkillLx(points[21],6,5,3);

        points[22] = new LxPointBean(7, 11063,30,0);
        lx[22] = new XSkillLx(points[22],6,6,3);

        return lx;
    }


    /** 灵犀技能 */
    class XSkillLx extends JComponent implements MouseListener {

        // 技能类型 1物理，2法术，3辅助
        private int type;

        private LxPointBean lxPointBean;
        // 技能边框
        private ImageIcon icon1;
        // 技能图标
        private ImageIcon icon2;
        // 数值底框
        private ImageIcon icon3;
        // 数值
        private String value;

        // 灰色遮挡
        private ImageIcon icon4;

        // 加锁覆盖
        private ImageIcon icon5;

        public int px,py,maxPoint,col,index;

        /**
         * @param lxPointBean
         * @param
         * @param idx 当前排序（从上到下）
         */
        public XSkillLx(LxPointBean lxPointBean, int col, int idx,int type) {
            this.type = type;
            this.lxPointBean = lxPointBean;
            this.addMouseListener(this);
            this.px = 40 + lxPointBean.getLv() * 70;
            this.py = 40 + (150 - col * 25) + (idx - 1) * 50;
            this.col = lxPointBean.getLv();
            this.maxPoint = lxPointBean.getMaxPoint();
            reset();

        }

        /** 重置 */ // 重置为锁定状态，点数清空
        public void reset() {
            RoleSummoning pet = UserMessUntil.getChosePetMes();
            int ssn = Integer.parseInt(pet.getSsn());
            this.icon1 = CutButtonImage.getWdfPng(ImgConstants.tz234, "defaut.wdf");
            this.icon2 = CutButtonImage.LxSkill(this.lxPointBean.getTableId());
            setIcon4();
            lock();
            setPoint(0);
            nvalue();
            values();
            int y = col==7&&ssn==1?py+70:py;
            int x = ssn==0?40 + lxPointBean.getLv() * 83: 40 + lxPointBean.getLv() * 70;
            this.setBounds(x, y , 50, 50);

        }

        public void lock() {
            this.icon5 = CutButtonImage.getWdfPng(ImgConstants.tz236, "defaut.wdf");
        }

        public void unlock() {
            this.icon5 = null;
        }


        public void values() {
            value="";
        }

        public void nvalue() {
            this.icon3 = null;
        }


        public int getCol() {
            return col;
        }




        /** 学习进度更改 */
        public void setPoint(int point) {
            value = lxPointBean.setPoint(point);
            if (maxPoint < 10) {
                icon3 = CutButtonImage.getWdfPng(ImgConstants.tz238, "defaut.wdf");
            } else {
                icon3 = CutButtonImage.getWdfPng(ImgConstants.tz238, "defaut.wdf");
            }
        }

        public int getLv() {
            return lxPointBean.getNowPoint();
        }

        public int getMaxLv() {
            return lxPointBean.getMaxPoint();
        }

        public Skill getSkill() {
            return UserMessUntil.getSkillId(lxPointBean.getTableId() + "");
        }

        public boolean isLock() {
            return icon5 != null;
        }

        /** 根据条件刷新 */
        public void setIcon4() {
            this.icon4 = CutButtonImage.getWdfPng(ImgConstants.tz235, 49,49,"defaut.wdf");
        }

        public int getType() {
            return type;
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);

            if (icon1 != null) {
                g.drawImage(icon1.getImage(), 0, 0, 49, 49, this);
            }

            if (icon2 != null) {
                g.drawImage(icon2.getImage(), 4, 4, 41, 41, this);
            }
            if (icon3 != null) {
                g.drawImage(icon3.getImage(), 3, 30, this);
            }
            if (value != null) {
                g.setColor(Color.yellow);
                g.setFont(UIUtils.LiSu_LS12);
                g.drawString(value, maxPoint > 10 ? value.length() > 4 ? 6 : 10 : 7, 41);
            }
            if (getLv() == 0 && icon4 != null) {
                g.drawImage(icon4.getImage(), 4, 4, 42, 42, this);
            }

            if (icon5 != null) {
                g.drawImage(icon5.getImage(), 15, 15, 16, 18, this);
            }
        }

        @Override
        public void mouseClicked(MouseEvent e) {

        }

        @Override
        public void mousePressed(MouseEvent e) {
            // 开启窗口音效
            Music.addyinxiao("关闭窗口.mp3");
            if (e.getButton() == MouseEvent.BUTTON1) {
                if (icon5 != null) {
                    String lingxi = UserMessUntil.getChosePetMes().getLingxi();
                    if (lingxi.isEmpty()) {
                        lingxi = "Lx=0&Lv=0&Point=0&Open=11001_0|11002_0|11003_0|11004_0|11005_0|11006_0|11007_0|11008_0|11009_0|11010_0|11026_0|11027_0|11028_0|11029_0|11045_0|11046_0|11047_0|11048_0|11049_0";
                    }
                    String[] param = lingxi.split("&");
                    if (param.length != 4) {
                        return;
                    }
                    String jn = param[3].split("=")[1];
                    String[] jineng = jn.split("\\|");
                    if (index>(jineng.length-10)){
                    ZhuFrame.getZhuJpanel().addPrompt2("#R前面的技能还没有开启呢！请依次开通灵犀技能格");
                        return;
                    }
                    OptionsJframe.getOptionsJframe().getOptionsJpanel().showBox(TiShiUtil.openLxLock,
                            UserMessUntil.getChosePetMes().getSid()+"&"+ lxPointBean.getTableId(),
                                    "#Y此技能格处于锁定状态，确定消耗#R"+(jineng.length-10+1)+"#Y个#G灵犀丹#Y为#G"+UserMessUntil.getChosePetMes().getSummoningname()+"#Y开启第#R"+(index+1)+"#Y个技能格？#R（每次使用后有一定几率可开启灵犀技能格，已开启技能格越多消耗的灵犀丹越多）", (index+1));

                    return;
                }
                changePoint(this, true);
            } else if (e.getButton() == MouseEvent.BUTTON3) {
                changePoint(this, false);
            }
            if (icon4 != null) {
                return;
            }
            py++;
        }

        @Override
        public void mouseReleased(MouseEvent e) {
            if (icon4 != null) {
                return;
            }
            py--;
        }

        @Override
        public void mouseEntered(MouseEvent e) {
            Skill skill = UserMessUntil.getSkillId(lxPointBean.getTableId() + "");
            MsgJframe.getJframe().getJapnel().LX(skill != null ? skill.getSkillname() : "", getSkillMsg(skill, this));

        }

        @Override
        public void mouseExited(MouseEvent e) {
            FormsManagement.HideForm(46);// 隐藏窗体
        }

    }
    /** 生成技能描述 */
    public String getSkillMsg(Skill skill, XSkillLx xSkillLx) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("#cffffff【等级】	" + xSkillLx.getLv() + "/" + xSkillLx.getMaxLv());
        buffer.append("#r#cffffff【技能类别】	被动");
        buffer.append("#r#G" + calLxNumber(skill.getRemark(),xSkillLx.getLv()));
        buffer.append("#r #r#cffffff学习条件:");
        //TODO 这里等级打标或点数打标则标记绿色
        buffer.append("#r#Y召唤兽要达到#R点化"+skill.getSkilllevel()+"#Y级");
        if (skill.getDielectric().isEmpty() && !"0.0".equals(skill.getDielectric())) {
            buffer.append("#r#Y需已分配点数#R"+skill.getDielectric().split("\\.")[0]+"#Y点");
        }
        if (skill.getSkillralation().isEmpty()) {
            buffer.append("#r #r#Y与#R"+UserMessUntil.getSkillId(skill.getSkillralation()).getSkillname()+"#Y互斥");
        }
        if (Integer.parseInt(skill.getSkillid()) >= 11020 && Integer.parseInt(skill.getSkillid()) <= 11022) {
            buffer.append("#r #r#Y可同时修炼#R一往无前、有备无患、将功补过#Y中的两种技能");
        } else if (Integer.parseInt(skill.getSkillid()) >= 11023 && Integer.parseInt(skill.getSkillid()) <= 11025) {
            buffer.append("#r #r#Y可同时修炼#R奋不顾身、卷土重来、惊涛骇浪#Y中的两种技能");
        } else  if (Integer.parseInt(skill.getSkillid()) >= 11039 && Integer.parseInt(skill.getSkillid()) <= 11041) {
            buffer.append("#r #r#Y可同时修炼#R一飞冲天、展翅欲飞、青云直上#Y中的两种技能");
        } else if (Integer.parseInt(skill.getSkillid()) >= 11042 && Integer.parseInt(skill.getSkillid()) <= 11044) {
            buffer.append("#r #r#Y可同时修炼#R大开杀戒、锥心刺骨、哀兵必败#Y中的两种技能");
        } else  if (Integer.parseInt(skill.getSkillid()) >= 11058 && Integer.parseInt(skill.getSkillid()) <= 11060) {
            buffer.append("#r #r#Y可同时修炼#R有仇必报、春色满园、步步相逼#Y中的两种技能");
        } else if (Integer.parseInt(skill.getSkillid()) >= 11061 && Integer.parseInt(skill.getSkillid()) <= 11063) {
            buffer.append("#r #r#Y可同时修炼#R飘然出尘、碧荷凝露、焕然新生#Y中的两种技能");
        }

        if ( xSkillLx.getLv() !=0 && xSkillLx.getLv() < xSkillLx.getMaxLv()) {
            buffer.append("#r #r#cffffff下一等级:");
            buffer.append("#r#G" + calLxNumber(skill.getRemark(),xSkillLx.getLv()+1));
        }
        buffer.append("");

        return buffer.toString();
    }

    /**
     * 计算灵犀描述数值
     * @param msg
     * @param lvl
     * @return
     */
    public static String calLxNumber(String msg,int lvl) {
        Matcher mat = Pattern.compile("<([^>]*)>").matcher(msg);
        lvl = lvl > 0 ? lvl - 1 : lvl;
        StringBuilder sb = new StringBuilder();
        int idx = 0;
        while(mat.find()){
            String str = mat.group();
            str = str.replaceAll("<", "").replaceAll(">", "");
            if (str.indexOf("+") > -1) {
                String[] num = str.split("\\+");
                if (num.length == 2) {
                    double s = Double.parseDouble(num[0]);
                    double e = Double.parseDouble(num[1]);
                    String txt = "#R" + ((s + e*lvl)+"").split("\\.")[0] + "#G";

                    sb.append(msg.substring(idx,mat.start()));
                    sb.append(txt);
                    idx = mat.end();
                }
            } else if (str.indexOf("-") > -1) {
                String[] num = str.split("-");
                if (num.length == 2) {
                    double s = Double.parseDouble(num[0]);
                    double e = Double.parseDouble(num[1]);
                    String txt = "#R" + ((s - e*lvl)+"").split("\\.")[0] + "#G";

                    sb.append(msg.substring(idx,mat.start()));
                    sb.append(txt);
                    idx = mat.end();
                }
            }
        }
        sb.append(msg.substring(idx));
        return sb.toString();
    }

    /*
     * 	Lx=1&Point=60&Open=11003_3|11004_0|11006_0|11007_2|11005_3|11001_0
		Lx      灵犀选择的的是 1物理  2法术  3辅助
		Point   拥有修炼点书
		Open    已开启的技能格子以及所加点数（前两排免费赠送）
     */
    /** 点数更改 true+ false- */
    public void changePoint(XSkillLx xSkilllx, boolean is) {
        if (is) {

            if (xSkilllx.lxPointBean.getNowPoint() >= xSkilllx.lxPointBean.getMaxPoint()) {
                ZhuFrame.getZhuJpanel().addPrompt2("此技能已达到修炼上限");
                return;
            }
            //TODO 增加点数，判断是否有修炼点、判断格子是否开启、判断等级、判断互斥、判断前置点数
            // 修炼点判断
            if (dianshu < 1) {
                ZhuFrame.getZhuJpanel().addPrompt2("灵犀点数不足");
                return;
            }

            // 召唤兽等级是否满足
            RoleSummoning pet = UserMessUntil.getChosePetMes();
            int lv = pet.getGrade() - 544;
            if (!xSkilllx.getSkill().getSkilllevel().isEmpty() && !xSkilllx.getSkill().getSkilllevel().equals("0") && lv < Integer.parseInt(xSkilllx.getSkill().getSkilllevel())) {
                ZhuFrame.getZhuJpanel().addPrompt2("召唤兽等级不满足条件");
                return;
            }

            //TODO 判断格子是否开启
            if (xSkilllx.isLock()) {
                ZhuFrame.getZhuJpanel().addPrompt2("#R技能栏已锁");
                return;
            }

            // 判断是否有修炼点
            if (Integer.parseInt(lingxidian.getText()) == 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("召唤兽灵犀点数不足");
                return;
            }
            int tA = (xSkilllx.getType()+1) % 3;
            int tB = (xSkilllx.getType()+2) % 3;
            // 是否点击了其他的灵犀
            if (getDianshu(tA) > 0 || getDianshu(tB) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你已经选择了其他类型的灵犀技能，如果要更新请先清空其他类型灵犀技能的点数");
                return;
            }
            Skill skill = xSkilllx.getSkill();
            //判断前置点数
            if (skill.getDielectric().isEmpty()&& !"0.0".equals(skill.getDielectric())) {
                int point = Integer.parseInt(skill.getDielectric().split("\\.")[0]);
                if (point > getQianzhi(xSkilllx)) {
                    ZhuFrame.getZhuJpanel().addPrompt2("修炼此技能需要前置点数达到#R"+point+"#Y点（前置点数不计算同级）");
                    return;
                }
            }
            //判断技能互斥
            if (skill.getSkillralation().isEmpty() && getCountTemp(Integer.parseInt(skill.getSkillralation()),xSkilllx.getType()) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("此技能与#R"+UserMessUntil.getSkillId(skill.getSkillralation()).getSkillname()+"#Y为互斥关系，如果要选择此技能需要先清空互斥技能的点数");
                return;
            }
            //判断6选2
            if (!selectMax(xSkilllx.lxPointBean.getTableId(),xSkilllx.getType())) {
                ZhuFrame.getZhuJpanel().addPrompt2("超出可同时修炼限制，此召唤兽不可修炼此技能");
                return;
            }

            queding.setBtn(1);
            queding.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz85,"defaut.wdf"));
            dianshu--;
            xSkilllx.setPoint(xSkilllx.lxPointBean.getNowPoint() + 1);
            lingxidian.setText(dianshu + "");

        } else {
            if (xSkilllx.lxPointBean.getNowPoint()<=1){
                queding.setBtn(-1);
                queding.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz239,"defaut.wdf"));
            }
            if (xSkilllx.lxPointBean.getNowPoint() <= 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("'没有可以减少的点数了'");
                return;
            }
            // 判断此点数是否已经被保存过，保存过不允许减少
            if (getCount(xSkilllx.lxPointBean.getTableId()) >= xSkilllx.lxPointBean.getNowPoint()) {
                ZhuFrame.getZhuJpanel().addPrompt2("此点数已被保存，如要减少清使用洗点功能重新分配灵犀点");
                return;
            }

            // 减少点数，判断减少此技能点数后是否还满足其他技能的前置需求
            if (!jian(xSkilllx.getCol(), xSkilllx.getType())) {
                ZhuFrame.getZhuJpanel().addPrompt2("此技能被其他技能依赖，请先清空后面有前置点数需求的技能点数");
                return;
            }

            dianshu++;
            xSkilllx.setPoint(xSkilllx.lxPointBean.getNowPoint() - 1);
            lingxidian.setText(dianshu + "");
        }
    }
    /**
     * 获取当前灵犀，提供保存按钮使用
     * @return
     */
    public String getLingxi() {
        if (panelType == 0) {
            return "";
        }
        StringBuilder str = new StringBuilder();
        switch (panelType) {
            case 1:
                for (XSkillLx xSkilllx : wuli) {
                    if (xSkilllx.getLv() > 0) {
                        if (str.toString().length() == 0) {
                            str.append("Lx=" + panelType + "&");

                        }else {
                            str.append("|");
                        }
                        str.append(xSkilllx.lxPointBean.getTableId() + "_" + xSkilllx.getLv());

                    }
                }
                break;
            case 2:
                for (XSkillLx xSkilllx : fashu) {
                    if (xSkilllx.getLv() > 0) {
                        if (str.toString().length() == 0) {
                            str.append("Lx=" + panelType + "&");
                        }else {
                            str.append("|");
                        }
                        str.append(xSkilllx.lxPointBean.getTableId() + "_" + xSkilllx.getLv());
                    }
                }
                break;
            case 3:
                for (XSkillLx xSkilllx : fuzhu) {
                    if (xSkilllx.getLv() > 0) {
                        if (str.toString().length() == 0) {
                            str.append("Lx=" + panelType + "&");
                        }else {
                            str.append("|");
                        }
                        str.append(xSkilllx.lxPointBean.getTableId() + "_" + xSkilllx.getLv());
                    }
                }
                break;
            default:
                break;
        }
        return str.toString();
    }

    /**
     * 减少点数，判断减少此技能点数后是否还满足其他技能的前置需求
     * @param col
     * @param type
     * @return
     */
    public boolean jian(int col, int type) {
        if (col == 7) {
            return true;
        }

        switch (type) {
            case 1:
                for (XSkillLx xSkilllx : wuli) {
                    if (col < xSkilllx.getCol() && xSkilllx.getLv() > 0) {
                        Skill skill = xSkilllx.getSkill();
                        if (skill.getDielectric().isEmpty()&& !"0.0".equals(skill.getDielectric())) {
                            int point = Integer.parseInt(skill.getDielectric().split("\\.")[0]);
                            if (point > getQianzhi(xSkilllx) - 1) {
                                return false;
                            }
                        }
                    }
                }
                break;
            case 2:
                for (XSkillLx xSkilllx : fashu) {
                    if (col < xSkilllx.getCol() && xSkilllx.getLv() > 0) {
                        Skill skill = xSkilllx.getSkill();
                        if (skill.getDielectric().isEmpty() && !"0.0".equals(skill.getDielectric())) {
                            int point = Integer.parseInt(skill.getDielectric().split("\\.")[0]);
                            if (point > getQianzhi(xSkilllx) - 1) {
                                return false;
                            }
                        }
                    }
                }
                break;
            case 3:
                for (XSkillLx xSkilllx : fuzhu) {
                    if (col < xSkilllx.getCol() && xSkilllx.getLv() > 0) {
                        Skill skill = xSkilllx.getSkill();
                        if (skill.getDielectric().isEmpty() && !"0.0".equals(skill.getDielectric())) {
                            int point = Integer.parseInt(skill.getDielectric().split("\\.")[0]);
                            if (point > getQianzhi(xSkilllx) - 1) {
                                return false;
                            }
                        }
                    }
                }
                break;
            default:
                break;
        }
        return true;
    }


    /**
     * 返回比此技能级别低的技能总加点数
     * @param
     * @return
     */
    public int getQianzhi(XSkillLx lx) {
        int col = lx.getCol();

        int sum = 0;

        switch (lx.getType()) {
            case 1:
                for (XSkillLx xSkilllx : wuli) {
                    if (col > xSkilllx.getCol()) {
                        sum += xSkilllx.getLv();
                    }
                }
                break;
            case 2:
                for (XSkillLx xSkilllx : fashu) {
                    if (col > xSkilllx.getCol()) {
                        sum += xSkilllx.getLv();
                    }
                }
                break;
            case 3:
                for (XSkillLx xSkilllx : fuzhu) {
                    if (col > xSkilllx.getCol()) {
                        sum += xSkilllx.getLv();
                    }
                }
                break;
            default:
                break;
        }
        return sum;
    }


    private static final int[] W = {11020,11021,11022,11023,11024,11025};
    private static final int[] F = {11039,11040,11041,11042,11043,11044};
    private static final int[] Z = {11058,11059,11060,11061,11062,11063};
    /**
     * 判断6选2系列技能是否可选
     * @return
     */
    public boolean selectMax(int skillId,int type) {
        // 判断当前技能是否属于6选2系列
        int idx = -1;
        int upCount = 0;	//上半部分加点项目数量
        int downCount = 0;  //下半部分加点项目数量
        switch (type) {
            case 1:
                for (int i = 0 ; i < W.length ; i++) {
                    if (W[i] == skillId) {
                        idx = i;
                        break;
                    }
                }
                if (idx == -1) {
                    // 不属于6选2直接跳过
                    return true;
                }
                for (XSkillLx xSkilllx : wuli) {
                    if (xSkilllx.lxPointBean.getTableId() == W[0] && idx != 0 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == W[1] && idx != 1 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == W[2] && idx != 2 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == W[3] && idx != 3 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == W[4] && idx != 4 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == W[5] && idx != 5 && xSkilllx.getLv() > 0) {
                        downCount++;
                    }
                }
                break;
            case 2:
                for (int i = 0 ; i < F.length ; i++) {
                    if (F[i] == skillId) {
                        idx = i;
                        break;
                    }
                }
                if (idx == -1) {
                    // 不属于6选2直接跳过
                    return true;
                }
                for (XSkillLx xSkilllx : fashu) {
                    if (xSkilllx.lxPointBean.getTableId() == F[0] && idx != 0 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == F[1] && idx != 1 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == F[2] && idx != 2 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == F[3] && idx != 3 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == F[4] && idx != 4 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == F[5] && idx != 5 && xSkilllx.getLv() > 0) {
                        downCount++;
                    }
                }
                break;
            case 3:
                for (int i = 0 ; i < Z.length ; i++) {
                    if (Z[i] == skillId) {
                        idx = i;
                        break;
                    }
                }
                if (idx == -1) {
                    // 不属于6选2直接跳过
                    return true;
                }
                for (XSkillLx xSkilllx : fuzhu) {
                    if (xSkilllx.lxPointBean.getTableId() == Z[0] && idx != 0 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == Z[1] && idx != 1 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == Z[2] && idx != 2 && xSkilllx.getLv() > 0) {
                        upCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == Z[3] && idx != 3 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == Z[4] && idx != 4 && xSkilllx.getLv() > 0) {
                        downCount++;
                    } else
                    if (xSkilllx.lxPointBean.getTableId() == Z[5] && idx != 5 && xSkilllx.getLv() > 0) {
                        downCount++;
                    }
                }
                break;
            default:
                return false;
        }

        if (idx < 3) {
            // 上半部分
            return upCount <= 1 && downCount == 0;
        } else {
            // 下半部分
            return downCount <= 1 && upCount == 0;
        }
    }
    /**
     * 获取指定类型的已点灵犀点数
     * @param type
     * @return
     */
    public int getDianshu(int type) {
        int dianshu = 0;
        switch (type) {
            case 1:
                for (XSkillLx xSkilllx : wuli) {
                    dianshu += xSkilllx.getLv();
                }
                break;
            case 2:
                for (XSkillLx xSkilllx : fashu) {
                    dianshu += xSkilllx.getLv();
                }
                break;
            case 3:
                for (XSkillLx xSkilllx : fuzhu) {
                    dianshu += xSkilllx.getLv();
                }
                break;
            default:
                break;
        }
        return dianshu;
    }
    /**
     * 获取某个技能的已点点数（有可能未保存）
     * @param skillId
     * @return
     */
    public int getCountTemp(int skillId,int type) {
        switch (type) {
            case 1:
                for (XSkillLx xSkilllx : wuli) {
                    if (xSkilllx.lxPointBean.getTableId() == skillId) {
                        return xSkilllx.getLv();
                    }
                }
                break;
            case 2:
                for (XSkillLx xSkilllx : fashu) {
                    if (xSkilllx.lxPointBean.getTableId() == skillId) {
                        return xSkilllx.getLv();
                    }
                }
                break;
            case 3:
                for (XSkillLx xSkilllx : fuzhu) {
                    if (xSkilllx.lxPointBean.getTableId() == skillId) {
                        return xSkilllx.getLv();
                    }
                }
                break;
            default:
                break;
        }
        return 0;
    }


    /**
     * 获取某个技能的已保存点数
     * @param skillId
     * @return
     */
    public int getCount(int skillId) {
        String lingxi = UserMessUntil.getChosePetMes().getLingxi();
        if (lingxi.isEmpty()) {
            return 0;
        }
        String[] param = lingxi.split("&");
        if (param.length != 4) {
            return 0;
        }
        // 灵犀技能
        String jn = param[3].split("=")[1];
        String[] jineng = jn.split("\\|");
        for (String idStr : jineng) {
            String[] ids_count = idStr.split("_");
            int id = Integer.parseInt(ids_count[0]);
            int count = Integer.parseInt(ids_count[1]);
            if (id == skillId) {
                return count;
            }
        }
        return 0;
    }


    public void init() {
        String lingxi = UserMessUntil.getChosePetMes().getLingxi();
        if (lingxi==null||lingxi.isEmpty()) {
            lingxi = "Lx=0&Lv=0&Point=0&Open=11001_0|11002_0|11003_0|11004_0|11005_0|11006_0|11007_0|11008_0|11009_0|11010_0|11026_0|11027_0|11028_0|11029_0|11045_0|11046_0|11047_0|11048_0|11049_0";
        }
        String[] param = lingxi.split("&");
        if (param.length != 4) {
            return;
        }
        // 清空技能面板
        if (wuli != null) {
            for (XSkillLx lx : wuli) {
                lx.reset();
            }
        }
        if (fashu != null) {
            for (XSkillLx lx : fashu) {
                lx.reset();
            }
        }
        if (fuzhu != null) {
            for (XSkillLx lx : fuzhu) {
                lx.reset();
            }
        }
        // 面板类型
        int type = Integer.parseInt(param[0].split("=")[1]);
        // 修炼等级
        String xl = param[1].split("=")[1];
        // 灵犀点数
        dianshu = Integer.parseInt(param[2].split("=")[1]);
        // 灵犀技能
        String jn = param[3].split("=")[1];
        // 已消耗的灵犀点数
        int ds = 0;
        String[] jineng = jn.split("\\|");
        for (String idStr : jineng) {
            String[] ids_count = idStr.split("_");
            int id = Integer.parseInt(ids_count[0]);
            int count = Integer.parseInt(ids_count[1]);
            ds += count;
            if (wuli == null) {
                wuli = getWuli();
                for (XSkillLx lx : wuli) {
                    this.add(lx);
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 1 ? count : 0);
                        lx.unlock();
                    }
                }
            } else {
                for (XSkillLx lx : wuli) {
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 1 ? count : 0);
                        lx.unlock();
                    }
                }
            }
            if (fashu == null) {
                fashu = getFashu();
                for (XSkillLx lx : fashu) {
                    this.add(lx);
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 2 ? count : 0);
                        lx.unlock();
                    }
                }
            } else {
                for (XSkillLx lx : fashu) {
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 2 ? count : 0);
                        lx.unlock();
                    }
                }
            }
            if (fuzhu == null) {
                fuzhu = getFuzhu();
                for (XSkillLx lx : fuzhu) {
                    this.add(lx);
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 3 ? count : 0);
                        lx.unlock();
                    }
                }
            } else {
                for (XSkillLx lx : fuzhu) {
                    if (lx.lxPointBean.getTableId() == id) {
                        lx.setPoint(type == 3 ? count : 0);
                        lx.unlock();
                    }
                }
            }

        }
        dianshu -= ds;
        lingxidian.setText(dianshu+"");
        changePanel(type);

    }




    private int xs = 50,ys =15;
    private int xs1 = 50+185,ys1 =15;
    private int xs2 = 50+185*2,ys2 =15;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        RoleSummoning pet  = UserMessUntil.getChosePetMes();
        Juitil.ImngBack(g,Juitil.tz67,0,0,getWidth(),getHeight(),1);
        Juitil.ImngBack(g, Juitil.tz237, 6, 8, getWidth()-13, getHeight()-15, 1);
        Juitil.ImngBack(g,Juitil.tz128,105,371,55,20,1);
        Juitil.ImngBack(g,Juitil.tz226,5,357,636,43,1);
        g.drawImage(Juitil.icon.getImage(), 55, 356, 580, 3, null);
        Juitil.TextBackground(g, "灵犀点  "+lingxidian.getText(), 15, 53, 372, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);

        if (stopjpanel) {
            Juitil.ImngBack(g,Juitil.tz22,50-15,10,540+35,369,1);
            Juitil.ImngBack(g,Juitil.tz224,xs,ys,171,346,1);
            Juitil.ImngBack(g,Juitil.tz218,xs1,ys1,171,346,1);
            Juitil.ImngBack(g,Juitil.tz221,xs2,ys2,171,346,1);
        }else {//展示学习技能界面tz19
            switch (panelType){
                case 1:
                    Juitil.ImngBack(g,Juitil.tz222,50,8,590,347,1);
                    Juitil.ImngBack(g,Juitil.tz219,5,-4,87,381,1);
                    break;
                case 2:
                    Juitil.ImngBack(g,Juitil.tz225,50,8,590,347,1);
                    Juitil.ImngBack(g,Juitil.tz228,5,-4,87,381,1);
                    break;
                case 3:
                    Juitil.ImngBack(g,Juitil.tz216,50,8,590,347,1);
                    Juitil.ImngBack(g,Juitil.tz223,5,-4,87,381,1);
                    break;
            }


            for (int i = 0; i < 4; i++) {
                int spx = Integer.parseInt(pet.getSsn())==0?274:233;
                int spxx = Integer.parseInt(pet.getSsn())==0?83:70;
                g.drawImage(Juitil.tz232.getImage(),(spx+i*spxx),152,23,75,null);
            }
            if (pet.getSsn().equals("1")){
                g.drawImage(Juitil.tz233.getImage(),512,120,23,130,null);
            }else if (pet.getSsn().equals("4")||pet.getSsn().equals("2")){
                g.drawImage(Juitil.tz233.getImage(),512,48,23,130,null);
                g.drawImage(Juitil.tz233.getImage(),512,198,23,130,null);
            }
        }

    }


    public RichLabel[] getRichLabels() {
        return richLabels;
    }

    public void setRichLabels(RichLabel[] richLabels) {
        this.richLabels = richLabels;
    }

    public WsylBtn getExbtn() {
        return exbtn;
    }

    public void setExbtn(WsylBtn exbtn) {
        this.exbtn = exbtn;
    }

    public WsylBtn getWashUpBtn() {
        return washUpBtn;
    }

    public void setWashUpBtn(WsylBtn washUpBtn) {
        this.washUpBtn = washUpBtn;
    }

    public WsylBtn getSuppBtn() {
        return suppBtn;
    }

    public void setSuppBtn(WsylBtn suppBtn) {
        this.suppBtn = suppBtn;
    }

    public WsylBtn getAllbtn() {
        return allbtn;
    }

    public void setAllbtn(WsylBtn allbtn) {
        this.allbtn = allbtn;
    }

    public WsylBtn getQueding() {
        return queding;
    }

    public void setQueding(WsylBtn queding) {
        this.queding = queding;
    }

    public JLabel[] getLabImg() {
        return labImg;
    }

    public void setLabImg(JLabel[] labImg) {
        this.labImg = labImg;
    }

    public void setWuli(XSkillLx[] wuli) {
        this.wuli = wuli;
    }

    public void setFashu(XSkillLx[] fashu) {
        this.fashu = fashu;
    }

    public void setFuzhu(XSkillLx[] fuzhu) {
        this.fuzhu = fuzhu;
    }

    public static JLabel getLingxidian() {
        return lingxidian;
    }

    public static void setLingxidian(JLabel lingxidian) {
        WsylJPanel.lingxidian = lingxidian;
    }

    public int getDianshu() {
        return dianshu;
    }

    public void setDianshu(int dianshu) {
        this.dianshu = dianshu;
    }

    public int getXs() {
        return xs;
    }

    public void setXs(int xs) {
        this.xs = xs;
    }

    public int getYs() {
        return ys;
    }

    public void setYs(int ys) {
        this.ys = ys;
    }

    public int getXs1() {
        return xs1;
    }

    public void setXs1(int xs1) {
        this.xs1 = xs1;
    }

    public int getYs1() {
        return ys1;
    }

    public void setYs1(int ys1) {
        this.ys1 = ys1;
    }

    public int getXs2() {
        return xs2;
    }

    public void setXs2(int xs2) {
        this.xs2 = xs2;
    }

    public int getYs2() {
        return ys2;
    }

    public void setYs2(int ys2) {
        this.ys2 = ys2;
    }

    public int getPanelType() {
        return panelType;
    }

    public void setPanelType(int panelType) {
        this.panelType = panelType;
    }
}

