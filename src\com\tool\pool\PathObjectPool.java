package com.tool.pool;

import org.come.bean.PathPoint;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 寻路对象池 - 用于复用PathPoint对象，减少寻路时的内存分配
 */
public class PathObjectPool {

    // PathPoint对象池
    private static final ConcurrentLinkedQueue<PathPoint> pathPointPool = new ConcurrentLinkedQueue<>();
    private static final int MAX_PATHPOINT_POOL_SIZE = 200;
    private static int pathPointPoolSize = 0;

    // List对象池
    private static final ConcurrentLinkedQueue<List<PathPoint>> pathListPool = new ConcurrentLinkedQueue<>();
    private static final int MAX_LIST_POOL_SIZE = 50;
    private static int pathListPoolSize = 0;
    
    // 统计信息
    private static long totalPathPointsAcquired = 0;
    private static long totalPathPointsReused = 0;
    private static long totalListsAcquired = 0;
    private static long totalListsReused = 0;
    
    /**
     * 获取PathPoint对象
     */
    public static PathPoint acquirePathPoint(int x, int y) {
        totalPathPointsAcquired++;
        
        PathPoint point = pathPointPool.poll();
        if (point != null) {
            pathPointPoolSize--;
            totalPathPointsReused++;
            point.setX(x);
            point.setY(y);
            return point;
        } else {
            return new PathPoint(x, y);
        }
    }
    
    /**
     * 归还PathPoint对象
     */
    public static void releasePathPoint(PathPoint point) {
        if (point == null) return;
        
        if (pathPointPoolSize < MAX_PATHPOINT_POOL_SIZE) {
            // 清理对象状态
            point.setX(0);
            point.setY(0);
            pathPointPool.offer(point);
            pathPointPoolSize++;
        }
    }
    

    
    /**
     * 获取PathPoint列表
     */
    public static List<PathPoint> acquirePathPointList() {
        totalListsAcquired++;
        
        List<PathPoint> list = pathListPool.poll();
        if (list != null) {
            pathListPoolSize--;
            totalListsReused++;
            list.clear();
            return list;
        } else {
            return new ArrayList<>();
        }
    }
    
    /**
     * 归还PathPoint列表
     */
    public static void releasePathPointList(List<PathPoint> list) {
        if (list == null) return;
        
        if (pathListPoolSize < MAX_LIST_POOL_SIZE) {
            // 归还列表中的PathPoint对象
            for (PathPoint point : list) {
                releasePathPoint(point);
            }
            list.clear();
            pathListPool.offer(list);
            pathListPoolSize++;
        } else {
            // 如果池已满，至少归还PathPoint对象
            for (PathPoint point : list) {
                releasePathPoint(point);
            }
        }
    }
    

    
    /**
     * 清空所有对象池
     */
    public static void clearAll() {
        pathPointPool.clear();
        pathListPool.clear();
        pathPointPoolSize = 0;
        pathListPoolSize = 0;
    }
    
    /**
     * 获取对象池统计信息
     */
    public static String getPoolStats() {
        double pathPointReuseRate = totalPathPointsAcquired > 0 ?
            (double) totalPathPointsReused / totalPathPointsAcquired * 100 : 0;
        double listReuseRate = totalListsAcquired > 0 ?
            (double) totalListsReused / totalListsAcquired * 100 : 0;

        return String.format("寻路对象池统计: PathPoint池=%d/%d(复用率%.1f%%), List池=%d/%d(复用率%.1f%%)",
                           pathPointPoolSize, MAX_PATHPOINT_POOL_SIZE, pathPointReuseRate,
                           pathListPoolSize, MAX_LIST_POOL_SIZE, listReuseRate);
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStats() {
        totalPathPointsAcquired = 0;
        totalPathPointsReused = 0;
        totalListsAcquired = 0;
        totalListsReused = 0;
    }
}
