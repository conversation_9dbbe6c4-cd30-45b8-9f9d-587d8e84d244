package org.come.until;

import java.awt.*;
import java.awt.image.BufferedImage;

/**
 * 安全的字体度量工具类，避免使用可能不存在的 Toolkit.getFontMetrics() 方法
 */
public class SafeFontMetrics {
    
    // 创建一个临时的 BufferedImage 用于获取 Graphics 对象
    private static final BufferedImage TEMP_IMAGE = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
    
    /**
     * 安全地获取字体度量
     * @param font 字体
     * @return FontMetrics 对象
     */
    public static FontMetrics getFontMetrics(Font font) {
        try {
            Graphics g = TEMP_IMAGE.getGraphics();
            g.setFont(font);
            return g.getFontMetrics();
        } catch (Exception e) {
            // 如果出现异常，返回默认字体的度量
            try {
                Graphics g = TEMP_IMAGE.getGraphics();
                g.setFont(new Font("Dialog", Font.PLAIN, 12));
                return g.getFontMetrics();
            } catch (Exception e2) {
                // 如果还是失败，创建一个简单的 FontMetrics 实现
                return createFallbackFontMetrics(font);
            }
        }
    }
    
    /**
     * 安全地获取字体度量（从 Graphics 对象）
     * @param g Graphics 对象
     * @return FontMetrics 对象
     */
    public static FontMetrics getFontMetrics(Graphics g) {
        try {
            return g.getFontMetrics();
        } catch (Exception e) {
            // 如果出现异常，使用当前字体创建安全的度量
            Font font = g.getFont();
            if (font == null) {
                font = new Font("Dialog", Font.PLAIN, 12);
            }
            return getFontMetrics(font);
        }
    }
    
    /**
     * 安全地获取字体度量（从 Graphics 对象和指定字体）
     * @param g Graphics 对象
     * @param font 字体
     * @return FontMetrics 对象
     */
    public static FontMetrics getFontMetrics(Graphics g, Font font) {
        try {
            return g.getFontMetrics(font);
        } catch (Exception e) {
            // 如果出现异常，使用安全方法
            return getFontMetrics(font);
        }
    }
    
    /**
     * 创建一个简单的 FontMetrics 实现作为后备方案
     */
    private static FontMetrics createFallbackFontMetrics(Font font) {
        // 这是一个简化的实现，提供基本的度量信息
        return new FontMetrics(font != null ? font : new Font("Dialog", Font.PLAIN, 12)) {
            @Override
            public int stringWidth(String str) {
                if (str == null || str.isEmpty()) return 0;
                // 简单估算：每个字符大约是字体大小的 0.6 倍宽度
                return (int) (str.length() * getFont().getSize() * 0.6);
            }
            
            @Override
            public int getHeight() {
                return getFont().getSize() + 4; // 字体大小 + 一些间距
            }
            
            @Override
            public int getAscent() {
                return (int) (getFont().getSize() * 0.8);
            }
            
            @Override
            public int getDescent() {
                return (int) (getFont().getSize() * 0.2);
            }
            
            @Override
            public int charWidth(char ch) {
                return (int) (getFont().getSize() * 0.6);
            }
        };
    }
    
    /**
     * 安全地计算字符串宽度
     * @param font 字体
     * @param text 文本
     * @return 字符串宽度
     */
    public static int stringWidth(Font font, String text) {
        if (text == null || text.isEmpty()) return 0;
        try {
            FontMetrics fm = getFontMetrics(font);
            return fm.stringWidth(text);
        } catch (Exception e) {
            // 简单估算
            return (int) (text.length() * font.getSize() * 0.6);
        }
    }
}
