package jxy2.soul;

import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.Skill;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;

public class ResetSoulSkillMouse implements MouseListener {
    public int index ;
    public ResetModeSkills resetModeSkills;
    public ResetSoulSkillMouse(int index,ResetModeSkills resetModeSkills) {
        this.index=index;
        this.resetModeSkills=resetModeSkills;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        ResetSoulSkillJPanl  resetSoulSkillJPanl = ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl();
        String[] skillID = resetModeSkills.builder.toString().split("\\|");
        for (int i = 0; i < skillID.length; i++) {
            Skill skill = UserMessUntil.getSkillId(skillID[i]);
            ImageIcon icon = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(), 44, 44, "skill.wdf");
            Image img = Juitil.toRoundedCornerImage(icon.getImage(), 15);
                if (i == 0) {
                    resetSoulSkillJPanl.goods_1.setIcon(new ImageIcon(img));
                    resetSoulSkillJPanl.goods_1.setVisible(true); // 确保 goods_1 可见
                    resetSoulSkillJPanl.type = i;
                    resetSoulSkillJPanl. goods_1.setBounds(305,61,44,44);
                }
                if (i == 1) {
                    resetSoulSkillJPanl.goods_2.setIcon(new ImageIcon(img));
                    resetSoulSkillJPanl.goods_2.setVisible(true); // 确保 goods_2 可见
                    resetSoulSkillJPanl.type = i;
                    resetSoulSkillJPanl.goods_2.setBounds(375,61,44,44);
                }
            // 如果 golang 只有一个值，隐藏 goods_2
            if (skillID.length == 1) {
                resetSoulSkillJPanl.goods_2.setVisible(false);
                resetSoulSkillJPanl.type = -1;
                resetSoulSkillJPanl.goods_1.setBounds(345,61,44,44);
            }
        }

        for (Integer key : resetSoulSkillJPanl.getMapResistanceModelPanel().keySet()) {
            ResetModeSkills resetModeSkills1 = resetSoulSkillJPanl.getMapResistanceModelPanel().get(key);
            resetModeSkills1.frames.setBorder(BorderFactory.createEmptyBorder());
            JLabel selectedSkiimg = resetSoulSkillJPanl.getMapResistanceModelPanel().get(index).frames;
            if (selectedSkiimg != null) {
                selectedSkiimg.setBorder(BorderFactory.createLineBorder(Color.orange));
            }
        }

        resetSoulSkillJPanl.goodstable = resetModeSkills.goodstable;//获取物品参数
        resetSoulSkillJPanl.builder = resetModeSkills.builder;//拼接技能ID
        resetSoulSkillJPanl.xz = index;//选中索引
        //判断物品对应的消耗
        resetSoulSkillJPanl.consumables.setIcon(GoodsName(resetModeSkills.goodstable.getGoodsname()));
        resetSoulSkillJPanl.goodsSum.setText(GoodsSum(resetModeSkills.goodstable.getGoodsname())+"/1");

    }

    /**根据物品名字找到包裹中对应的数量*/
    public static String GoodsSum(String Name){
        switch (Name){
            case "普通魔晶":
              return  GoodsListFromServerUntil.getGoodNum(new BigDecimal(81268))+"";
            case "百年魔晶":
                return  GoodsListFromServerUntil.getGoodNum(new BigDecimal(81269))+"";
            case "千年魔晶":
                return  GoodsListFromServerUntil.getGoodNum(new BigDecimal(81270))+"";
            case "仙灵魔晶":
                return  GoodsListFromServerUntil.getGoodNum(new BigDecimal(81271))+"";
            default:
                return "0";
        }
    }


    public static ImageIcon GoodsName(String Name){
        switch (Name){
            case "普通魔晶":
              return GoodsListFromServerUntil.imgpathWdfile("2024-6B6B3054",50,43);
            case "百年魔晶":
                return GoodsListFromServerUntil.imgpathWdfile("2024-6B6B3053",50,43);
            case "千年魔晶":
                return GoodsListFromServerUntil.imgpathWdfile("2024-6B6B3052",50,43);
            case "仙灵魔晶":
                return GoodsListFromServerUntil.imgpathWdfile("2024-6B6B3051",50,50);
            default:
                return null;
        }
    }


    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        ResetSoulSkillJPanl  resetSoulSkillJPanl = ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl();
            JLabel selectedSkiimg = resetSoulSkillJPanl.getMapResistanceModelPanel().get(index).skiimg;
            if (selectedSkiimg != null) {
                selectedSkiimg.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz70,240,52,"defaut.wdf"));
            }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        ResetSoulSkillJPanl resetSoulSkillJPanl = ResetSoulSkillFrame.getResetSoulSkillFrame().getResetSoulSkillJPanl();
        JLabel selectedSkiimg = resetSoulSkillJPanl.getMapResistanceModelPanel().get(index).skiimg;
        if (selectedSkiimg != null) {
            selectedSkiimg.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz69, 240, 52, "defaut.wdf"));
        }
    }
}
