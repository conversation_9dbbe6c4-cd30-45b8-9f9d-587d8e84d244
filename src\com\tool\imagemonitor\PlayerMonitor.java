package com.tool.imagemonitor;

import java.math.BigDecimal;

import org.come.Frame.GiveJframe;
import org.come.Frame.TestfriendlistJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.GiveJpanel;
import org.come.bean.RoleShow;
import org.come.entity.Friend;
import org.come.entity.Friendtable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.AccessTeamInfoUntil;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;
import org.come.until.JTreeData;
import org.come.until.MessagrFlagUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import com.tool.role.RoleData;
import come.tool.BangBattle.BangFight;
import come.tool.FightingData.FightingForesee;

/**
 *玩家监听
 * <AUTHOR>
 */
public class PlayerMonitor {
	
    /**
     * type=0表示玩家自己
     * @param type
     * @param loginResult
     */
	public static void Player(ManimgAttribute attribute){
		RoleShow roleShow=attribute.getRoleShow();
		int type=attribute.getLeixing();
		String Id=roleShow.getRolename();
		// TODO Auto-generated method stub
		switch (MessagrFlagUntil.ImgFlagImg) {
		 case "组队":  
			 if (type==0) {
				createTeam();
			}else {
				teamApply(roleShow.getRole_id());
			}	
			break;
		case "交易"://请求交易//判断点击的是自己还是别人
			if(type==0){//是自己// 把鼠标变回原样
				MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
			}else if(type == 1){//是别人
				transApply(Id);
			}
			break;	
		case "好友"://添加好友
			addFriend(roleShow.getRole_id(), roleShow.getRolename());
			break;
		case "给予"://给予方法
			//给与信息
			give(attribute);
			break;
		case "切磋":			
			//改变鼠标的样式状态为原本状态
			MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
			if (roleShow.getFighting()!=0) {
		    	String a=ImageMixDeal.userimg.getRoleShow().getTeamInfo();
		    	if (a==null||a.equals("")) {
		    		String fightMes = Agreement.getAgreement().battleConnectionAgreement(roleShow.getFighting()+"");
					SendMessageUntil.toServer(fightMes);			
				}else {
					ZhuFrame.getZhuJpanel().addPrompt2("只有单人才可以进行观战");
				}
        	    return;
			}
		  //进行玩家切磋挑战
		    String[] v=ImageMixDeal.userimg.getTeams();
            if (v==null) {
            	ZhuFrame.getZhuJpanel().addPrompt2("叫队长帮你点");
				return;
			}
            if (Util.ditubianma==3315) {
		    	if (BangFight.getBangFight().manstate==1){
    				ZhuFrame.getZhuJpanel().addPrompt2("你需要等待一段时间才能投入战斗");
    				return;
    			}
		    	if (!BangFight.getBangFight().isChao()){
    				ZhuFrame.getZhuJpanel().addPrompt2("你当前状态被限制");
    				return;
    			}  	
            	if (ImageMixDeal.userimg.getRoleShow().getTeam().split("\\|").length<BangFight.MINSUM) {
            		ZhuFrame.getZhuJpanel().addPrompt2("最少人数"+BangFight.MINSUM);
					return;
				}
            	pk(roleShow,11);
            	return;
			}
            if(type==0){
				ZhuFrame.getZhuJpanel().addPrompt2("您不能和自己切磋！！");	
				return;
			}
            if (ImageMixDeal.userimg.getRoleShow().getGrade()<30) {
		    	ZhuFrame.getZhuJpanel().addPrompt2("30级才开放PK");	
				return;
			}
            if (roleShow.getGrade()<30) {
				ZhuFrame.getZhuJpanel().addPrompt2("玩家处于新手保护期");	
				return;
			}
            if (ImageMixDeal.userimg.getRoleShow().getBooth_id()!=null) {
		    	ZhuFrame.getZhuJpanel().addPrompt2("你还在摆摊啊");	
				return;
			}
            if (roleShow.getBooth_id()!=null) {
				ZhuFrame.getZhuJpanel().addPrompt2("玩家在摆摊");	
				return;
			}
            for (int i = 0; i < v.length; i++) {
            	if (v[i].equals(roleShow.getRolename())) {
            		ZhuFrame.getZhuJpanel().addPrompt2("他是你的队员");
					return;
				}
			} 
            pk(roleShow, 5);	
			break;
			default:
				if (type==1) {
					ZhuFrame.getZhuJpanel().creatroletext(roleShow.getRole_id(), roleShow.getRolename());	
				}			
				break;
		}	
	}
	/** pk发送 */
	public static void pk(RoleShow roleShow, int type) {
		FightingForesee fightingForesee = new FightingForesee();// 进行切磋的函数
		if (ImageMixDeal.userimg.getRoleShow().getTeamInfo() != null && !ImageMixDeal.userimg.getRoleShow().getTeamInfo().equals("")) {
			fightingForesee.setYidui(ImageMixDeal.userimg.getRoleShow().getTeamInfo());
		} else {
			fightingForesee.setYidui(ImageMixDeal.userimg.getRoleShow().getRolename());
		}
		if (roleShow.getTeamInfo() != null && !roleShow.getTeamInfo().equals("")) {
			fightingForesee.setErdui(roleShow.getTeamInfo());
		} else {
			fightingForesee.setErdui(roleShow.getRolename());
		}
		// 进入战斗，发送消息给服务器
		fightingForesee.setType(type);
		String fightMes = Agreement.FightingForeseeAgreement(GsonUtil.getGsonUtil().getgson().toJson(fightingForesee));
		SendMessageUntil.toServer(fightMes);
	}
	/**交易*/
	public static void transApply(String Id){
		if(ImageMixDeal.Playerimgmap.get(Id) != null){
			// 把鼠标变回原样
			MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
			String send=Agreement.getAgreement().TransStateAgreement("0|"+Id);
			SendMessageUntil.toServer(send);
		}
	}
	/**添加好友*/
	public static void addFriend(BigDecimal id, String name) {
		MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		if (id.compareTo(ImageMixDeal.userimg.getRoleShow().getRole_id()) == 0) {
			FrameMessageChangeJpanel.addtext(5, "自己加自己?", null, null);
			return;
		}
		if (UserMessUntil.getFriendtables() != null) {
			for (int i = 0; i < UserMessUntil.getFriendtables().size(); i++) {
				if (UserMessUntil.getFriendtables().get(i).getRole_id().compareTo(id) == 0) {
					ImageMixDeal.userimg.Dialogue("对方已经是你好友");
					FrameMessageChangeJpanel.addtext(5, "对方已经是你好友", null, null);
					return;
				}
			}
		}
		Friend fiend = new Friend();// 添加好友返回给服务端
		fiend.setFriendid(id);// 设置好友id
		fiend.setRoleid(ImageMixDeal.userimg.getRoleShow().getRole_id());// 设置自己的id
		String sendmes = Agreement.getAgreement().addFrientAgreement(GsonUtil.getGsonUtil().getgson().toJson(fiend));
		// 向服务器发送信息
		SendMessageUntil.toServer(sendmes);
		ZhuFrame.getZhuJpanel().addPrompt2("你添加#G" + name + "#Y为好友");
	}
	/**删除好友*/
	public static void deleteFriden(String role) {
		Friendtable friendtable = null;
		for (int i = 0; i < UserMessUntil.getFriendtables().size(); i++) {
			if (role.equals(UserMessUntil.getFriendtables().get(i).getRolename())) {
				friendtable = UserMessUntil.getFriendtables().remove(i);
				break;
			}
		}
		if (friendtable == null) {
			return;
		}
		if (FormsManagement.getframe(4).isVisible()) {
			JTreeData.ShowFriendMsg(TestfriendlistJframe.getTestfriendlistJframe().getJflist().getTop(), TestfriendlistJframe.getTestfriendlistJframe().getJflist().getjTree());
		}
		// 发送消息给服务端
		Friend fiendFriend = new Friend();
		fiendFriend.setRoleid(ImageMixDeal.userimg.getRoleShow().getRole_id());
		fiendFriend.setFriendid(friendtable.getRole_id());
		// 删除消息
		String mes = Agreement.getAgreement().delectFriend(GsonUtil.getGsonUtil().getgson().toJson(fiendFriend));
		SendMessageUntil.toServer(mes);
	}
	/**给与方法*/ 
	public static void give(ManimgAttribute attribute) {
		if (attribute.getLeixing() == 0) {// 0 玩家自己 1其他玩家 2npc 3召唤兽 4 宝宝 5 野怪 6任务怪
			ZhuFrame.getZhuJpanel().addPrompt2("不能将物品给与自己！！");
			return;
		}
		GiveJpanel giveJpanel = GiveJframe.getGivejframe().getGivejpanel();
		if (FormsManagement.getframe(12).isVisible()) {
			ZhuFrame.getZhuJpanel().addPrompt("您当前状态不可给予,请稍后再试！");
			return;
		}
		MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		giveJpanel.giveShow(attribute);
	}
	/** 创建组队 */
	public static void createTeam() {
		// 在万寿园和圣兽园不允许组队
		MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		if (ImageMixDeal.userimg.getRoleShow().getMapid() == 3321||ImageMixDeal.userimg.getRoleShow().getMapid() == 3322) {
			ZhuFrame.getZhuJpanel().addPrompt2("该地图无法组队");
			return;
		}
		if (AccessTeamInfoUntil.isJail(RoleData.getRoleData().getLoginResult().getTaskDaily())) {
			ZhuFrame.getZhuJpanel().addPrompt2("坐牢状态无法组队");
			return;
		}
		RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
		if (roleShow.getTroop_id()!=null) {
			ZhuFrame.getZhuJpanel().addPrompt2("你已经有队伍了");
			return;
		}
		String mes = Agreement.getAgreement().team1Agreement("");
		SendMessageUntil.toServer(mes);
	
	}
	/**申请加入队伍*/
	public static void teamApply(BigDecimal Id) {
		MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
		// 在万寿园和圣兽园不允许组队
		if (ImageMixDeal.userimg.getRoleShow().getMapid() == 3321 || ImageMixDeal.userimg.getRoleShow().getMapid() == 3322) {
			ZhuFrame.getZhuJpanel().addPrompt2("该地图无法组队");
			return;
		}
		if (AccessTeamInfoUntil.isJail(RoleData.getRoleData().getLoginResult().getTaskDaily())) {
			ZhuFrame.getZhuJpanel().addPrompt2("坐牢状态无法组队");
			return;
		}
		RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
		if (roleShow.getTroop_id()!=null) {
			ZhuFrame.getZhuJpanel().addPrompt2("你已经有队伍了");
			return;
		}
		String mes = Agreement.getAgreement().team2Agreement(Id.toString());
		SendMessageUntil.toServer(mes);
	}
}
