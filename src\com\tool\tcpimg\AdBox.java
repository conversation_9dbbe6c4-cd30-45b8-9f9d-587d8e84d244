package com.tool.tcpimg;

import java.awt.Graphics;
import java.awt.Image;
import java.util.ArrayList;
import java.util.List;

import javax.swing.ImageIcon;

import org.come.until.ScrenceUntil;

public class AdBox {

	private RichLabel text;
	private int length;
	private int value;
	private List<String> line;
	private Image image;
//	Sprite tcp
	public AdBox(String text) {
		// TODO Auto-generated constructor stub
		this.image=new ImageIcon("inkImg/button/B31.png").getImage();
		CZ(text);
	}
	public void draw(Graphics g) {
		// TODO Auto-generated method stub
		if (text==null) {
			return;
		}
		value++;
		if (value<length) {
			Graphics g2 = g.create(0,ScrenceUntil.Screen_y-75,ScrenceUntil.Screen_x,24);
			g2.drawImage(image,0,0,ScrenceUntil.Screen_x,24,null);
			g2.translate(ScrenceUntil.Screen_x-value,0);
			text.paint(g2);
			g2.dispose();
		}else {
			CZ(null);			
		}
	}
	public void addText(String text){
	    if (this.text==null) {
	    	CZ(text);
		}else {
			if (line==null) {
				line =new ArrayList<>();
			}
			line.add(text);
		}
	}
	public void CZ(String text){
		if (text==null) {
			if (line!=null&&line.size()!=0) {
				text=line.remove(0);
			}
		}else if (line!=null) {
			line.remove(text);
		}
		if (text==null) {
			this.text=null;
			return;
		}
		if (this.text==null) {
			this.text=new RichLabel(text, UIUtils.TEXT_NAME_FONT,9999);
		}else {
			this.text.setText(text);
		}
		this.length=ScrenceUntil.Screen_x+this.text.getWidth();
		this.value=0;
	}
	public RichLabel getText() {
		return text;
	}
}
