package jxy2.classicpet;

import com.tool.tcp.NewPart;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

/**
* 宠物经典J面板
* <AUTHOR>
* @date 2024/9/12 下午8:51
*/

public class PetClassicJPanel extends JPanel {
    public PetClassicJPanel() {
        this.setPreferredSize(new Dimension(396, 517));
        this.setOpaque(false);
        this.setLayout(null);
        Juitil.addClosingButtonToPanel(this,125,396);
    }

    public  NewPart newPart;
    public String titie = "召唤兽";
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, getWidth()+4, 32, 1);
        Juitil.ImngBack(g, Juitil.JK_1, 0, 32, getWidth()+7, getHeight()-55, 1);
        Juitil.Subtitledrawing(g, getWidth()/2-20, 24, titie, Color.WHITE, UIUtils.HYXKJ_HY20,1);
    }


}
