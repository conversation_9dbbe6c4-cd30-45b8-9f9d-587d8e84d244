package jxy2.petAi;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.Skill;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;

public class PetSkillAiJPanel extends JPanel {
    public JLabel[] Rebirth,textimg; // 自动重悟
    public JLabel[] conditionL,cindImg;
    private PetSkillAiBtn btnAr;//自动
    public String xzname;
    public double value;
    public boolean stopmsg = false;
    public PetSkillAiJPanel() {
        this.setPreferredSize(new Dimension(284, 480));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        getRebirth();
        getBtnAr();
        getTextimg();
        getConditionL();
        getCindImg();

    }
    public ImageIcon img;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz67, 0, 0, 284, 480, 1);
    }

    public JLabel[] getRebirth() {
        if (Rebirth==null){
            Rebirth = new JLabel[37];
            String[] skid = new String[37]; // 初始化长度为37的字符串数组
            for (int i = 0; i < Rebirth.length; i++) {
                Rebirth[i] = new JLabel();
                int startx = i % 2;
                int starty = i / 2;
                skid[i] = String.valueOf(1300 + i); // 将整数转换为字符串并存入数组
//                // 假设你想在获得每个ID时立即尝试获取对应的Skill
                try {
                    Skill skills = UserMessUntil.getSkillBean().getSkillMap().get(skid[i]);
                    if (skills != null) {
                        // 处理获取到的Skill对象，例如打印信息或进一步操作
                        Rebirth[i].setText(skills.getSkillname());
                    }
                } catch (Exception e) {
                    // 这里捕获并处理可能的异常，比如Map中没有找到相应key的情况
                    System.err.println("Error getting skill for ID " + skid[i] + ": " + e.getMessage());
                }
                Rebirth[i].setBounds(15+startx * 120 , 15+starty*19,107,15);
                Rebirth[i].setFont(UIUtils.MSYH_HY15);
                Rebirth[i].setForeground(UIUtils.COLOR_White);
                add(Rebirth[i]);
            }
        }
        return Rebirth;
    }




    public void setRebirth(JLabel[] rebirth) {
        Rebirth = rebirth;
    }

    public PetSkillAiBtn getBtnAr() {
        if (btnAr==null){
            btnAr = new PetSkillAiBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "自动", 5, this,"");
            btnAr.setBounds(115, 422, 59, 24);
            add(btnAr);
        }
        return btnAr;
    }

    public void setBtnAr(PetSkillAiBtn btnAr) {
        this.btnAr = btnAr;
    }

    public JLabel[] getTextimg() {
        if (textimg==null) {
            textimg = new JLabel[37];
            for (int i = 0; i < textimg.length; i++) {
                int startx = i % 2;
                int starty = i / 2;
                textimg[i] = new JLabel();
                textimg[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
                textimg[i].setBounds(110+startx * 125,16+starty * 19,17,16);
                textimg[i].addMouseListener(new PetSkillAiMouse(this,i,0));
                add(textimg[i]);
            }
        }
        return textimg;
    }

    public void setTextimg(JLabel[] textimg) {
        this.textimg = textimg;
    }

    public JLabel[] getConditionL() {
        if (conditionL==null){
            conditionL = new JLabel[4];
            String[] skid = {"3.0","4.0","5.0","提示"};// 初始化长度为37的字符串数组
            for (int i = 0; i < conditionL.length; i++) {
                conditionL[i] = Juitil.GJpanel();
                conditionL[i].setText(skid[i]);
                conditionL[i].setFont(UIUtils.MSYH_HY15);
                conditionL[i].setForeground(UIUtils.COLOR_White);
                conditionL[i].setBounds(10+i * 56,396,30,16);
                add(conditionL[i]);
            }
        }
        return conditionL;
    }

    public void setConditionL(JLabel[] conditionL) {
        this.conditionL = conditionL;
    }


    public JLabel[] getCindImg() {
        if (cindImg==null) {
            cindImg = new JLabel[4];
            for (int i = 0; i < cindImg.length; i++) {
                cindImg[i] = new JLabel();
                cindImg[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
                cindImg[i].setBounds(46+i * 56,396,17,16);
                cindImg[i].addMouseListener(new PetSkillAiMouse(this,i,1));
                add(cindImg[i]);
            }
        }
        return cindImg;
    }

    public void setCindImg(JLabel[] cindImg) {
        this.cindImg = cindImg;
    }
}
