package com.tool.tcpimg;

import jxy2.jutnil.Juitil;
import org.come.bean.ImgZoom;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import java.awt.*;

public class NoticeBox{
	  private RichLabel label;
      private int time;
      private ImgZoom imgZoom;
      //x偏移量
      private int px;
      private int width;
      private int height;
      public NoticeBox(String text) {
          label = new RichLabel(text,UIUtils.TEXT_FONT22);
          label.setLocation(6, 6);
          Dimension d = label.computeSize(16*40);
          d.setSize(642, 75);
          label.setSize(d);
          width=d.width+12;
          height=d.height+12;
          this.imgZoom= Juitil.notice;
          imgZoom.setMiddlew(d.width);
          imgZoom.setMiddleh(d.height);
          this.time = Util.TIME_CHAT2*7;
          px=ScrenceUntil.Screen_x/2-d.width/2;
      }
      public void paint(Graphics g) {
    	  g.translate(px,0);
    	  imgZoom.draw(g);
    	  g.translate(label.getX(),label.getY());
          label.paint(g);
          g.translate(-px-label.getX(),-label.getY());
      }
	public RichLabel getLabel() {
		return label;
	}
	public void setLabel(RichLabel label) {
		this.label = label;
	}
	public boolean IsTime() {
		return time-->0;
	}
	public ImgZoom getImgZoom() {
		return imgZoom;
	}
	public void setImgZoom(ImgZoom imgZoom) {
		this.imgZoom = imgZoom;
	}
	public int getPx() {
		return px;
	}
	public void setPx(int px) {
		this.px = px;
	}
	public int getWidth() {
		return width;
	}
	public void setWidth(int width) {
		this.width = width;
	}
	public int getHeight() {
		return height;
	}
	public void setHeight(int height) {
		this.height = height;
	}
}
