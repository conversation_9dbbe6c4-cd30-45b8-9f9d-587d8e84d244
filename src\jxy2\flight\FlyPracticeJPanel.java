package jxy2.flight;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.jutnil.Juitil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

public class FlyPracticeJPanel extends JPanel {
    private static final long serialVersionUID = 1L;
    private JLabel[] labicon = new JLabel[8];
    public FlyBtn sortGoods;
    public FlyPracticeJPanel() {
        this.setPreferredSize(new Dimension(320, 332));
        this.setLayout(null);  // 使用绝对布局
        this.setBackground(UIUtils.Color_BACK);
        for (int i = 0; i < labicon.length; i++) {
            labicon[i] = new JLabel();
            labicon[i].setBounds(15, 49 + i * 26, 134, 19);
            labicon[i].setVisible(false);
            this.add(labicon[i]);
        }
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        sortGoods = new FlyBtn(iconResource, 1, "修炼", 0, this);
        sortGoods.setBounds(55, 297, 60, 26);
        this.add(sortGoods);
    }

    public void initialization(Fly fly){
        labicon[0].setText("飞行");
        labicon[1].setText(fly.getFlylv()+"");
        labicon[2].setText(fly.getFlylv()+"/"+FlyJPanel.getTSExp(fly.getFlysteps()));
        labicon[3].setText("----");
        labicon[4].setText("40926");
        labicon[5].setText(RoleData.getRoleData().getLoginResult().getExperience()+"");
        labicon[6].setText("9066");
        labicon[7].setText(RoleData.getRoleData().getLoginResult().getGold()+"");
    }


    public String[] text = {
            "技能",
            "等级",
            "修为",
            "修炼所需",
            "所需经验",
            "当前经验",
            "需要金钱",
            "金    钱"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "技能修炼");
        } else {
            Juitil.SheNewShow(g, 320, getHeight(), "技能修炼", 310, 55);
        }


// 1. 先绘制所有背景
        for (int i = 0; i < 8; i++) {
            if (i == 3) continue;
            Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0043,
                    120, 45 + i * 26, 134,
                    Util.SwitchUI == 1 ? 19 : 23, 1);

            // 特殊处理i==2的情况，在这里绘制she_0024/red_0045背景
            if (i == 2) {
                FlyJPanel flyJPanel = FlyFrame.getFlightFrame().getFlightJPanel();
                Fly fly = FlyJPanel.FindAircraftById(flyJPanel.getAssemble());
                if (fly != null) {
                    int x = Util.SwitchUI == 1 ? 122 : 124;
                    int y = Util.SwitchUI == 1 ? 99 : 101;
                    int maxWidth = Util.SwitchUI == 1 ? 130 : 124; // 最大长度
                    // 计算进度条长度 (当前经验/最大经验 * 最大长度)
                    int progressWidth = (int)((double)fly.getFlylv() / FlyJPanel.getTSExp(fly.getFlysteps()) * maxWidth);
                    progressWidth = Math.min(progressWidth, maxWidth); // 确保不超过最大长度
                    Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.she_0024 : Juitil.red_0045,
                            x, y, progressWidth,
                            Util.SwitchUI == 1 ? 15 : 12, 1);
                }
            }
        }

            // 2. 绘制所有文本
        for (int i = 0; i < 8; i++) {
            // 绘制左侧文本
            Juitil.TextBackground(g, text[i], 15, 42, 49 + i * 26,
                    Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1,
                    Util.SwitchUI == 1 ? UIUtils.NEWTX_HY16B : UIUtils.TEXT_HYJ16B,
                    UIUtils.Color_BACK);

            // 绘制右侧文本
            if (i == 2) {  // 特殊处理i==2的文本位置
                Juitil.TextBackground(g, labicon[i].getText(), 15,
                        120 + 40,  // 向右移动50像素
                        45 + i * 26,
                        UIUtils.COLOR_White, UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            } else if (i == 0) {
                Juitil.TextBackground(g, labicon[0].getText(), 15, 123, 46,
                        UIUtils.COLOR_White, UIUtils.TEXT_FONT15, UIUtils.Color_BACK);
            } else if (i == 4) {
                Util.drawPrice(g, BigDecimal.valueOf(Long.parseLong(labicon[4].getText())), 123, 164, UIUtils.LiSu_LS15);
            } else if (i == 6) {
                Util.drawPrice(g, BigDecimal.valueOf(Long.parseLong(labicon[6].getText())), 123, 216, UIUtils.LiSu_LS15);
            } else if (i == 7) {
                Util.drawPrice(g, BigDecimal.valueOf(Long.parseLong(labicon[7].getText())), 123, 242, UIUtils.LiSu_LS15);
            } else if (i != 3) {  // 跳过i==3
                Juitil.TextBackground(g, labicon[i].getText(), 15, 120, (Util.SwitchUI == 1 ?44:46) + i * 26,
                        UIUtils.COLOR_White, UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            }
        }

    }

    public void updateButtonImages(int uiType) {
        String panelName = this.getClass().getSimpleName();
        if (uiType != 1) {
            Juitil.adjustFrameSize(293, 357, 150);
        } else {
            Juitil.adjustFrameSize(320, 332, 150);
        }
        Juitil.addClosingButtonToPanel(this,150,uiType==1?325:305);
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        sortGoods.setIcons(Juitil.getImgs(iconResource));
        UiBack.getComponentStyle(panelName, "sortGoods").applyToButton(sortGoods);
    }
}
