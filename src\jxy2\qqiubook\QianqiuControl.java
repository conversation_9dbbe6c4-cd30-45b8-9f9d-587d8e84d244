package jxy2.qqiubook;

import org.come.action.FromServerAction;
import org.come.until.GsonUtil;

import java.util.concurrent.ConcurrentHashMap;

public class QianqiuControl implements FromServerAction {
    // 使用Map存储QiqiuTreeNode，以ID作为键
    private static final ConcurrentHashMap<Integer, QiqiuTreeNode> nodeMap = new ConcurrentHashMap<>();
    public int index = 0;
    @Override
    public void controlMessFromServer(String mes, String type) {
        // 1. 反序列化出QiqiuTreeNode
//        index = 0;
        QiqiuTreeNode newNode = GsonUtil.getGsonUtil().getgson().fromJson(mes, QiqiuTreeNode.class);
        QianqiuBookJPanel qianqiuBookJPanel = QianqiuBookFrame.getQianqiuBookFrame().getQianqiuBookJPanel();

        // 2. 检查Map中是否已存在该节点
        QiqiuTreeNode existingNode = nodeMap.get(newNode.getId());

        if (existingNode == null) {
            // 如果节点不存在，直接添加
            nodeMap.put(newNode.getId(), newNode);
            qianqiuBookJPanel.UpdateData(existingNode);
        } else {
            // 如果节点已存在，更新其属性
            existingNode.updateFrom(newNode);  // 更新现有节点的属性
            // 使用更新后的节点
            qianqiuBookJPanel.UpdateData(existingNode);
        }

        qianqiuBookJPanel.setGuideBeanConcurrentHashMap(nodeMap);

    }
}
