package jxy2.guaed.fl;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserData;

import java.awt.event.MouseEvent;

public class LoadGuardianBtn extends MoBanBtn {
    private final int BtnId;
    private final OpenGuardianJPanel openGuardianJPanel;

    public LoadGuardianBtn(String iconpath, int type, int BtnId, String labelName,
                           OpenGuardianJPanel openGuardianJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.openGuardianJPanel = openGuardianJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        Goodstable goodstable = openGuardianJPanel.getGoodstable();
        if (goodstable==null){
            ZhuFrame.getZhuJpanel().addPrompt2("#R物品数据不对，请联系管理员");
            return;
        }
        if (goodstable.getGoodlock()==1){
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        goodstable.goodxh(1);
        if (goodstable.getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goodstable.getRgid());
        }
        goodstable.setStatus(1);
        openGuardianJPanel.getGoodslabel().setIcon(null);
        RoleData.getRoleData().getLoginResult()
                .setScore(UserData.Splice(RoleData.getRoleData().getLoginResult().getScore(), "守护之尘=" + openGuardianJPanel.getQuantityObtainable(), 2));
        String mes = Agreement.getAgreement().rolechangeAgreement("N"+goodstable.getRgid()+"|"+openGuardianJPanel.getQuantityObtainable());
        SendMessageUntil.toServer(mes);
        openGuardianJPanel.setQuantityObtainable(0);
    }
}
