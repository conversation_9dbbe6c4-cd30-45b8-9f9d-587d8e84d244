package jxy2.refine;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 精炼装备界面
 *  装备精炼等级对应颜色
 * "^ffffff"	//  白 1-3
 * * "^6cfb4b"	//  绿4-6
 * "^0c286d"	//  蓝7-9
 * "^5e1eff"	//  紫10-12
 * "^ff0000"	//  红13-15
 * "^ffdc50"	//  黄16
* <AUTHOR>
* @date 2024/7/19 下午8:45
*/

public class RefineJPanel extends JPanel {
    private RefiningCardJpanel cardJpanel;
    public RefineBtn[] forging =new RefineBtn[10];
    public BigDecimal money;
    public String[] prompt;
    private RichLabel richLabel;
    private int type;
    public RefineJPanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        cardJpanel = new RefiningCardJpanel();
        cardJpanel.setBounds(0, 0, 640, 460);
        this.add(cardJpanel);
        Juitil.addClosingButtonToPanel(this, 119, 640);
        prompt = new String[]{"精 炼","装备炼化","装备打造","炼 器","套装合成","套装收录","符石重铸","神 兵","宝 石","衍神兵"};
        for (int i = 0; i < prompt.length; i++) {
            forging[i] = new RefineBtn(ImgConstants.tz162, 1, UIUtils.COLOR_ZHUJPANEL, UIUtils.MSYH_HY14, prompt[i], i, this,"");
            forging[0].btnchange(2);
            forging[i].setBounds(8,35+i*40,122,40);
            this.add(forging[i]);
        }
        money = new BigDecimal(100000);
        getRichLabel();
    }
    public String titie = "服务大全";

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),titie);
        Juitil.ImngBack(g, Juitil.tz159, 128, 37, 500, 395, 1);
        g.drawImage(type==4||type==5?Juitil.tz165.getImage():Juitil.tz161.getImage(), 159, 82, 299, 335, null);
        g.drawImage(Juitil.icon.getImage(), 142, 70, 318, 2, null);

    }


    public RichLabel getRichLabel() {
        if (richLabel==null){
            richLabel = new RichLabel("",UIUtils.FZCY_HY14,160);
            richLabel.setBounds(464,102,160,380);
            add(richLabel);
        }
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public RefiningCardJpanel getCardJpanel() {
        return cardJpanel;
    }

    public void setCardJpanel(RefiningCardJpanel cardJpanel) {
        this.cardJpanel = cardJpanel;
    }

    public RefineBtn[] getForging() {
        return forging;
    }

    public void setForging(RefineBtn[] forging) {
        this.forging = forging;
    }
}

