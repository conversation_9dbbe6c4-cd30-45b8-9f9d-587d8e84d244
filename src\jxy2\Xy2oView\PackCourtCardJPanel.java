package jxy2.Xy2oView;

import org.come.Jpanel.TestpackJapnel;

import javax.swing.*;
import java.awt.*;

public class PackCourtCardJPanel extends JPanel {
    private CardLayout cardLayout;
    private TestpackJapnel jpac;//背包
    private TestRankJPanel testRankJPanel;//境界
    private TestXianyiJPanel testXianyiJPanel;//仙翼
    public PackCourtCardJPanel() {
        setBounds(0, 0, 403, 496);
        setOpaque(false);
        cardLayout = new CardLayout();
        setLayout(cardLayout);

        jpac = new TestpackJapnel();
        add(jpac,"knapsack");

        testRankJPanel = new TestRankJPanel();
        add(testRankJPanel,"rank");

        testXianyiJPanel = new TestXianyiJPanel();
        add(testXianyiJPanel,"xianyi");

    }

    public TestpackJapnel getJpac() {
        return jpac;
    }

    public void setJpac(TestpackJapnel jpac) {
        this.jpac = jpac;
    }

    public CardLayout getCardLayout() {
        return cardLayout;
    }

    public void setCardLayout(CardLayout cardLayout) {
        this.cardLayout = cardLayout;
    }

    public TestRankJPanel getTestRankJPanel() {
        return testRankJPanel;
    }

    public void setTestRankJPanel(TestRankJPanel testRankJPanel) {
        this.testRankJPanel = testRankJPanel;
    }

    public TestXianyiJPanel getTestXianyiJPanel() {
        return testXianyiJPanel;
    }

    public void setTestXianyiJPanel(TestXianyiJPanel testXianyiJPanel) {
        this.testXianyiJPanel = testXianyiJPanel;
    }
}


