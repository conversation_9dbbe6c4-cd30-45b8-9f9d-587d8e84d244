package com.tool.btn;

import com.tool.tcpimg.UIUtils;
import jxy2.chatv.ChatFrame;
import jxy2.jutnil.ImgConstants;
import org.come.Frame.*;
import org.come.Jpanel.*;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.List;

public class VipShopBtn extends MoBanBtn {

	private int caozuo;

	/** VIP反馈面板 */
	private VipGoodsJpanel vipGoodsJpanel;
    private String vicon;

	public VipShopBtn(String iconpath, int type, int caozuo) {
		super(iconpath, type);
		this.caozuo = caozuo;
	}
	public VipShopBtn(String iconpath, int type,int caozuo, String vicon) {
        super(iconpath, type);
        this.vicon = vicon;
        this.caozuo = caozuo;
    }
	boolean is;

	public VipShopBtn(String iconpath, int type, int caozuo, boolean is) {
		super(iconpath,0, type);
		this.caozuo = caozuo;
		this.is = is;
	}

	/** VIP反馈面板1 */
	public VipShopBtn(String iconpath, int type, int caozuo, VipGoodsJpanel vipGoodsJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.vipGoodsJpanel = vipGoodsJpanel;
	}

	/** 连续充值面板 */
	private ContinuousRechargeGoodsJpanel continuousRechargeGoodsJpanel;

	/** 连续充值面板2 */
	public VipShopBtn(String iconpath, int type, int caozuo, ContinuousRechargeGoodsJpanel continuousRechargeGoodsJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.continuousRechargeGoodsJpanel = continuousRechargeGoodsJpanel;
	}

	/** 每日充值面板 */
	private EverydayRechargeGoodsJpanel everydayRechargeGoodsJpanel;

	public VipShopBtn(String iconpath, int type, int caozuo, EverydayRechargeGoodsJpanel everydayRechargeGoodsJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.everydayRechargeGoodsJpanel = everydayRechargeGoodsJpanel;
	}

	/** 每日特惠面板 */
	private EveryDayOddsJpanel everyDayOddsJpanel;

	public VipShopBtn(String iconpath, int type, int caozuo, String text, EveryDayOddsJpanel everyDayOddsJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		setText(text);
		setForeground(Color.white);
		setFont(UIUtils.TEXT_FONT);
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
		this.everyDayOddsJpanel = everyDayOddsJpanel;
	}

	/** 冲级主面板5-7 */
	private ImpactGradeJpanel impactGradeJpanel;

	public VipShopBtn(String iconpath, int type, int caozuo, ImpactGradeJpanel impactGradeJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.impactGradeJpanel = impactGradeJpanel;
	}

	/** 冲级领取面板8 */
	private ImpactGradeGoodsJpanel impactGradeGoodsJpanel;

	/** 冲级领取面板8 */
	public VipShopBtn(String iconpath, int type, int caozuo, ImpactGradeGoodsJpanel impactGradeGoodsJpanel) {
		super(iconpath, type);
		this.caozuo = caozuo;
		this.impactGradeGoodsJpanel = impactGradeGoodsJpanel;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		if (caozuo == 1) {
			vipGoodsJpanel.exchangeGoods();
		} else if (caozuo == 2) {

			continuousRechargeGoodsJpanel.exchangeGoods();
		} else if (caozuo == 3) {
			everydayRechargeGoodsJpanel.exchangeGoods();
		} else if (caozuo == 4) {
			if (GoodsListFromServerUntil.Surplussum("-1", "-1", 1) < 1) {
				ZhuFrame.getZhuJpanel().addPrompt2("你的背包不够");
				return;
			}
			everyDayOddsJpanel.exchangeGoods();
		} else if (caozuo >= 5 && caozuo <= 7) {
			impactGradeJpanel.changeMenuBtn(caozuo - 4);
		} else if (caozuo == 8) {
			impactGradeGoodsJpanel.exchangeGoods();
		} else if (caozuo == 50) {
			ImpactGradeJframe.getImpactGradeJframe().getImpactGradeJpanel().changeMenuBtn(-1);
		} else if (caozuo == 51) {
			ContinuousRechargeJframe.getContinuousRechargeJframe().getContinuousRechargeJpanel().getGoods();
		} else if (caozuo == 52) {
			EverydayRechargeJframe.getEverydayRechargeJframe().getEverydayRechargeJpanel().getGoods();
		} else if (caozuo == 53) {
			EveryDayOddsJframe.getEveryDayOddsJframe().getEveryDayOddsJpanel().getGoods();
		} else if (caozuo == 54) {
			changeVie();
		}else if(caozuo == 55){
            if(vicon==null)return;
            String sendmes = Agreement.getAgreement().viconAgreement(vicon);
            SendMessageUntil.toServer(sendmes);
        }
	}

	public void changeVie() {
		for (int i = 0; i < ZhuFrame.getZhuJpanel().getBtnShop().length; i++) {
			ZhuFrame.getZhuJpanel().getBtnShop()[i].setVisible(is);
		}


        List<VipShopBtn> btnListVicon = ZhuFrame.getZhuJpanel().getBtnListVicon();
        if(btnListVicon!=null){
            for (int i = 0; i < btnListVicon.size(); i++) {
                btnListVicon.get(i).setVisible(is);
            }
        }

			if (is) {
				ZhuFrame.getZhuJpanel().getShowVipBtn().setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz208,"defaut.wdf"));
			} else {
				ZhuFrame.getZhuJpanel().getShowVipBtn().setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz207,"defaut.wdf"));
			}

		is = !is;
	}
	
    public boolean isIs() {
        return is;
    }
    public void setIs(boolean is) {
        this.is = is;
    }
    public String getVicon() {
        return vicon;
    }
    public void setVicon(String vicon) {
        this.vicon = vicon;
    }
	
}
