package jxy2.wdfDome.bean;

import java.awt.image.BufferedImage;

public class WasHead {
    private int flag;
    private int headSize;
    private int directionNum;
    private int spriteFrame;
    private int spriteWidth;
    private int spriteHeight;
    private int spriteCenterX;
    private int spriteCenterY;
    private int[] colorBoard;
    private FrameData[] frameDataList;
    private BufferedImage bufImage;

    public BufferedImage getBufImage() {
        return bufImage;
    }

    public void setBufImage(BufferedImage bufImage) {
        this.bufImage = bufImage;
    }

    public int getFlag() {
        return this.flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public int getHeadSize() {
        return this.headSize;
    }

    public void setHeadSize(int headSize) {
        this.headSize = headSize;
    }

    public int getDirectionNum() {
        return this.directionNum;
    }

    public void setDirectionNum(int directionNum) {
        this.directionNum = directionNum;
    }

    public int getSpriteFrame() {
        return this.spriteFrame;
    }

    public void setSpriteFrame(int spriteFrame) {
        this.spriteFrame = spriteFrame;
    }

    public int getSpriteWidth() {
        return this.spriteWidth;
    }

    public void setSpriteWidth(int spriteWidth) {
        this.spriteWidth = spriteWidth;
    }

    public int getSpriteHeight() {
        return this.spriteHeight;
    }

    public void setSpriteHeight(int spriteHeight) {
        this.spriteHeight = spriteHeight;
    }

    public int getSpriteCenterX() {
        return this.spriteCenterX;
    }

    public void setSpriteCenterX(int spriteCenterX) {
        this.spriteCenterX = spriteCenterX;
    }

    public int getSpriteCenterY() {
        return this.spriteCenterY;
    }

    public void setSpriteCenterY(int spriteCenterY) {
        this.spriteCenterY = spriteCenterY;
    }

    public int[] getColorBoard() {
        return this.colorBoard;
    }

    public void setColorBoard(int[] colorBoard) {
        this.colorBoard = colorBoard;
    }

    public FrameData[] getFrameDataList() {
        return this.frameDataList;
    }

    public void setFrameDataList(FrameData[] frameDataList) {
        this.frameDataList = frameDataList;
    }

}
