package jxy2.zodiac;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TStarSoulsJPanel extends JPanel {
    private JLabel[] goodLabels = new JLabel[36];
    private JLabel[] lastLabels =  new JLabel[6];
    private StarChartBtn conversion;
    public List<Goodstable> integers = new ArrayList<>();
    public Map<BigDecimal, Integer> goodstableMap = new HashMap<>();
    public Goodstable goodstable;
    public int goodPosition;
    public TStarSoulsJPanel() {
        this.setPreferredSize(new Dimension(365, 480+32));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,136,365);
          for (int i = 0; i < goodLabels.length; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            goodLabels[i] = new JLabel();
            goodLabels[i].setBounds(31+shop_x*51,70+shop_y*51, 51, 51);
            goodLabels[i].addMouseListener(new TStarSoulsMouse(i, this,0));
            this.add(goodLabels[i]);
        }

          for (int i = 0; i < lastLabels.length; i++) {
            lastLabels[i] = new JLabel();
            lastLabels[i].setBounds(31+i*51,400, 51, 51);
            lastLabels[i].addMouseListener(new TStarSoulsMouse(i, this,1));
            this.add(lastLabels[i]);
        }


        conversion = new StarChartBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "转化", 3, this,"");
        conversion.setBounds(260, 459, 59, 24);
        this.add(conversion);
    }
    /**初始化物品信息*/
    public void initGoodsData(Goodstable goods) {

    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),"转 化");
        Juitil.TextBackground(g, "请选择要转化的星魂：", 14, 31, 48, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        for (int i = 0; i < 36; i++) {
            int shop_x = i % 6;
            int shop_y = i / 6;
            Juitil.ImngBack(g,Juitil.tz266,31+shop_x*51,70+shop_y*51,51,51,1);

            int x = TStarSoulsMouse.list.contains(i+"")?1:0;
            Goodstable goodstable = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[i]);
            g.drawImage(goodstable!=null?Juitil.tz264.getImage():Juitil.tz265.getImage(),x+33+shop_x*51,x+71+shop_y*51,50,50,null);
            Juitil.Txtpet(g, x+50+shop_x*51, x+92+shop_y*51, Juitil.XpName()[i], goodstable!=null?UIUtils.COLOR_NAME8:UIUtils.COLOR_White, UIUtils.TEXT_HY16B);
            if (goodstable!=null) {
                Juitil.Sum(g,goodstable.getUsetime(),31+shop_x*51,83+shop_y*51);
            }
        }


        Juitil.ImngBack(g,Juitil.tz267,31,70,308,308,1);
        Juitil.TextBackground(g, "提示：左键点击选中1个，右键点击选中1组。", 13, 31, 380, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        Juitil.ImngBack(g, Juitil.tz26, 100+30, 456, 98, 23, 1);
        Juitil.TextBackground(g, "可获得魂值", 15, 15+30, 458, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);
            for (int j = 0; j < 6; j++) {
            Juitil.ImngBack(g,Juitil.tz266,31+j*51,400,51,51,1);
            }
        int i = 0;
        int totalValue = 0; // 用于累积 num * zhi 的值
        for (BigDecimal bigDecimal : goodstableMap.keySet()) {
            int num = goodstableMap.get(bigDecimal);
            Goodstable goodstable = GoodsListFromServerUntil.getRgid(bigDecimal);
            if (goodstable != null) {
                int zhi = Integer.parseInt(goodstable.getValue().split("=")[2]);
                g.drawImage(Juitil.tz264.getImage(), 35 + i * 51, 402, 50, 50, null);
                Juitil.Sum(g, num, 33 + i * 51, 415);
                // 累积 num 和 num * zhi
                totalValue += num * zhi;
                Juitil.Txtpet(g, 52 + i * 51, 423, goodstable.getGoodsname().substring(0, 2), UIUtils.COLOR_NAME8, UIUtils.TEXT_HY16B);
            }
            i++;
        }
            // 在循环结束后显示累积的总价值
        Juitil.TextBackground(g, totalValue + "", 15, 15 + 30 + 90, 458, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY15);
        Juitil.ImngBack(g,Juitil.tz267,31,400,308,51,1);
    }




    public JLabel[] getGoodLabels() {
        return goodLabels;
    }

    public void setGoodLabels(JLabel[] goodLabels) {
        this.goodLabels = goodLabels;
    }

    public StarChartBtn getConversion() {
        return conversion;
    }

    public void setConversion(StarChartBtn conversion) {
        this.conversion = conversion;
    }

    public JLabel[] getLastLabels() {
        return lastLabels;
    }

    public void setLastLabels(JLabel[] lastLabels) {
        this.lastLabels = lastLabels;
    }

    public List<Goodstable> getIntegers() {
        return integers;
    }

    public void setIntegers(List<Goodstable> integers) {
        this.integers = integers;
    }

    public Map<BigDecimal, Integer> getGoodstableMap() {
        return goodstableMap;
    }

    public void setGoodstableMap(Map<BigDecimal, Integer> goodstableMap) {
        this.goodstableMap = goodstableMap;
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    public int getGoodPosition() {
        return goodPosition;
    }

    public void setGoodPosition(int goodPosition) {
        this.goodPosition = goodPosition;
    }
}
