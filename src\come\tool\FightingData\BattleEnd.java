package come.tool.FightingData;

import org.come.bean.OneArenaBean;

import com.tool.Stall.AssetUpdate;

/**
 * 战斗结束
 * <AUTHOR>
 */
public class BattleEnd {
	/**战斗编号*/
	private int FightingNumber;
	/**胜利阵营*/
	private int camp;
	private AssetUpdate assetUpdate;
	private OneArenaBean arenaBean;
	/**任务数据*/
	private String taskn;
	/**坐牢数据 PK点数=身份标志=做天牢次数=每周坐牢次数*/       
	private String taskDaily;
	private String msg;
	public BattleEnd() {
		// TODO Auto-generated constructor stub
	}
	public int getFightingNumber() {
		return FightingNumber;
	}
	public void setFightingNumber(int fightingNumber) {
		FightingNumber = fightingNumber;
	}
	public int getCamp() {
		return camp;
	}
	public void setCamp(int camp) {
		this.camp = camp;
	}
	public AssetUpdate getAssetUpdate() {
		return assetUpdate;
	}
	public void setAssetUpdate(AssetUpdate assetUpdate) {
		this.assetUpdate = assetUpdate;
	}
	public String getTaskn() {
		return taskn;
	}
	public void setTaskn(String taskn) {
		this.taskn = taskn;
	}
	public String getTaskDaily() {
		return taskDaily;
	}
	public void setTaskDaily(String taskDaily) {
		this.taskDaily = taskDaily;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
    public OneArenaBean getArenaBean() {
        return arenaBean;
    }
    public void setArenaBean(OneArenaBean arenaBean) {
        this.arenaBean = arenaBean;
    }
	
}
