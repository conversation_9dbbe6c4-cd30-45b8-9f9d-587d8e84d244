package jxy2.wdfDome.main;

import jxy2.wdfDome.util.WdfTool;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
/**
* WDF包加载
* <AUTHOR>
* @date 2024/4/29 上午7:47
*/

public class WdfMianUtil {
    public static List<File> wdfFiles = new ArrayList<>();
    public static File file;

 // 静态初始化块，在类加载时执行
 static {
    loadWdfFiles();
}

 // 加载WDF文件的静态方法
 private static void loadWdfFiles() {
    Properties pro = new Properties();
    String path = "";
    try {
        pro.load(new InputStreamReader(
            Files.newInputStream(Paths.get("config.properties")),
            StandardCharsets.UTF_8));
        path = pro.getProperty("wdfs");
        if (path != null) {
            File directory = new File(path);
            addWdfFiles(directory);
            if (!wdfFiles.isEmpty()) {
                file = wdfFiles.get(wdfFiles.size() - 1);
            }
        }
    } catch (IOException e) {
        e.printStackTrace();
    }
}

    public static void addWdfFiles(File file) {
        File[] files = WdfTool.getWdfFiles(file);
        if (files != null) {
            wdfFiles.addAll(Arrays.asList(files));
        }
    }
}
