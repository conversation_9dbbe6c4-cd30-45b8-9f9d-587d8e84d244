package jxy2.synt;

import org.come.until.FormsManagement;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class SynthesisMouse implements MouseListener{
    public SynthesisJPanl synthesisJPanl;
    public int typeBtn;
    public SynthesisMouse(int typeBtn,SynthesisJPanl synthesisJPanl) {
        this.typeBtn = typeBtn;
        this.synthesisJPanl = synthesisJPanl;
    }


    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //打开物品列表根据物品类型展示，获取背包中对应类型的物品，如没有则但会null
        if (synthesisJPanl.getGoodstables()[typeBtn]!=null){
            synthesisJPanl.getLabPlus()[typeBtn].setBounds(321+typeBtn*100, 259, 50, 50);
        }else {
            synthesisJPanl.getLabPlus()[typeBtn].setBounds(331+typeBtn*100,269,28,28);
        }

    }

    @Override
    public void mouseReleased(MouseEvent e) {
        if (synthesisJPanl.getGoodstables()[typeBtn]!=null){
            synthesisJPanl.getLabPlus()[typeBtn].setBounds(320+typeBtn*100, 258, 50, 50);
        }else {
            synthesisJPanl.getLabPlus()[typeBtn].setBounds(330+typeBtn*100,268,28,28);
        }


        FormsManagement.showForm(117);
        ExchangSoulFrame.getExchangSoulFrame().getExchangSoulJPanel().InitializeSkillList(synthesisJPanl.getLabname().getText());
    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

}
