package jxy2.flight;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Frame.NoTitleInternalFrameUI;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Music;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class FlyFrame extends JInternalFrame implements MouseListener {
    private FlyJPanel flightJPanel;
    private int first_x;
    private int first_y;
    public static FlyFrame getFlightFrame() {
        return (FlyFrame) FormsManagement.getInternalForm(148).getFrame();
    }
    public FlyFrame() {
        flightJPanel = new FlyJPanel();
        this.getContentPane().add(flightJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        setUI(new NoTitleInternalFrameUI(this));
        this.setBounds(380, 146, 488, 452);
        Juitil.originalSizes.putIfAbsent(148, new Dimension(488,452));
        this.setBackground(UIUtils.Color_BACK);
        this.pack();
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setVisible(false);
        this.addMouseListener(this);
        this.addMouseMotionListener(new MouseMotionListener() {
            public void mouseMoved(MouseEvent e) {
                if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
                    MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
                }
            }

            public void mouseDragged(MouseEvent e) {
                if (isVisible()) {
                    int x = e.getX() - first_x;
                    int y = e.getY() - first_y;
                    setBounds(x + getX(), y + getY(), getWidth(), getHeight());
                }

            }
        });
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        Music.addyinxiao("关闭窗口.mp3");
         boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {
            FormsManagement.HideForm(148);
        } else {
            FormsManagement.Switchinglevel(148);
        }

        this.first_x = e.getX();
        this.first_y = e.getY();
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public FlyJPanel getFlightJPanel() {
        return flightJPanel;
    }

    public void setFlightJPanel(FlyJPanel flightJPanel) {
        this.flightJPanel = flightJPanel;
    }

    public int getFirst_x() {
        return first_x;
    }

    public void setFirst_x(int first_x) {
        this.first_x = first_x;
    }

    public int getFirst_y() {
        return first_y;
    }

    public void setFirst_y(int first_y) {
        this.first_y = first_y;
    }
}
