//TODO 多线程加载,paint不等待

package come.tool.map;

import org.come.login.GameView;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.util.LinkedList;

/**
 * 显示游戏地图的控件
 *
 * <AUTHOR>
 *
 */
public class JMap extends JComponent {

	private static final long serialVersionUID = 1L;

	private MapDecoder decoder;

	private Rectangle maxVisibleRect = new Rectangle();

	private LinkedList<MapImage> loadedImages = new LinkedList<MapImage>();

	private MapImage[][] visibleImages;

	//地图规则  0障碍  1行走   2遮罩 3传送点
	private byte[][] maprules;

	public JMap() {

	}

	@Override
	public void paint(Graphics g) {
		paintComponent(g);
	}

	/**
	 *在g上画出当前可见区域对应的地图图像
	 */
	private int xOff,yOff,xOff2,yOff2;
	public void draw(Graphics g, int map_x, int map_y) {
		xOff = -map_x % 320;
		yOff = -map_y % 240;
		g.translate(xOff, yOff);
		map_x = map_x / 320;
		map_y = map_y / 240;
		for (int h = 0; h < 6; h++) {
			xOff2 = h * 320;
			yOff2 = h + map_x;
			for (int v = 0; v < 6; v++) {
				Image image = getimage(yOff2, v + map_y);
				if (image != null) {
					g.drawImage(image, xOff2, v * 240, this);
				}
			}
		}
		g.translate(-xOff, -yOff);
		// 绘制完成后清除之前加载的 Image 对象的引用

		loadedImages.clear();
	}

	/**
	 * 获取面板
	 */
	public Image getimage(int x, int y) {
		if (x < 0 || x >= visibleImages.length || y < 0 || y >= visibleImages[0].length) {
			return null;
		}
		MapImage mapImage = visibleImages[x][y];
		if (mapImage == null) {
			mapImage = new MapImage(null, x, y);
			visibleImages[x][y] = mapImage;
			byte[] imageData = decoder.getJpegData(x, y);
			mapImage.image = Toolkit.getDefaultToolkit().createImage(imageData);
		}
		return mapImage.image;
	}



	/**
	 * 用于描述地图的一片解码后的数据
	 * <AUTHOR>
	 * @date
	 */
	class MapImage {
		public Image image;
		public int h;
		public int v;
		public MapImage(Image image, int h, int v) {
			this.image = image;
			this.h = h;
			this.v = v;
		}
	}
	/**
	 * 加载一个文件
	 * @param file
	 * @return
	 */
	public boolean loadMap(File file) {
		if (file == null)
			return false;
		try {
			GameView.alpha =0.1F;
			decoder = new MapDecoder(file);
			maprules=decoder.loadHeader();
			setSize(decoder.getWidth(), decoder.getHeight());
			setPreferredSize(new Dimension(decoder.getWidth(), decoder.getHeight()));
			GameView.alpha = 0.35f;
			maxVisibleRect.height = 0;
			maxVisibleRect.width = 0;
			visibleImages = new MapImage[(int) Math.ceil(decoder.getWidth() / 320.0)][(int)(Math.ceil(decoder.getHeight() / 240.0))];
			decoder.removedata();
		} catch (Exception e) {
			System.err.println("打开地图文件出错:" + file.getName());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	public boolean loadMap2(File file) {
		if (file == null)
			return false;
		try {
			GameView.alpha = 0.1f;
			decoder = new MapDecoder(file);
			maprules=decoder.loadHeader();
			setSize(decoder.getWidth(), decoder.getHeight());
			setPreferredSize(new Dimension(decoder.getWidth(), decoder.getHeight()));
			GameView.alpha =1.0f;
			loadedImages = new LinkedList<MapImage>();
			maxVisibleRect.height = 0;
			maxVisibleRect.width = 0;
			visibleImages = new MapImage[(int) Math.ceil(decoder.getWidth() / 320.0)][(int)(Math.ceil(decoder.getHeight() / 240.0))];
			decoder.removedata();
		} catch (Exception e) {
			System.err.println("打开地图文件出错:" + file.getName());
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public int getMapWidth() {
		return decoder.getWidth();
	}
	public int getMapHeight() {
		return decoder.getHeight();
	}
	public Dimension getMapSize() {
		return new Dimension(decoder.getWidth(), decoder.getHeight());
	}
	public Dimension getSegments() {
		return new Dimension(decoder.getHorSegmentCount(), decoder.getVerSegmentCount());
	}

	public MapDecoder getDecoder() {
		return decoder;
	}
	public byte[][] getMaprules() {
		return maprules;
	}

	public void setMaprules(byte[][] maprules) {
		this.maprules = maprules;
	}

}
