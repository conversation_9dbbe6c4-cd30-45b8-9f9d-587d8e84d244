package jxy2.npk;

import jxy2.jutnil.CheckTimer;
import jxy2.jutnil.StreamTimer;
import org.come.bean.ImgZoom;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.CropImageFilter;
import java.awt.image.FilteredImageSource;
import java.awt.image.ImageFilter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.lang.ref.SoftReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NPK file ImageIcon reader utility class
 * Provides functionality to read images from NPK files and convert to ImageIcon
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkImageReader {

    // NPK文件缓存
    private static final Map<String, NpkFile> npkFileCache = new HashMap<>();

    // 使用SoftReference缓存 - 参考CutButtonImage.getWdfPng的成功实现
    private static final Map<String, SoftReference<BufferedImage>> imageDataCache = new HashMap<>();
    private static final Map<String, SoftReference<ImageIcon>> imageIconCache = new HashMap<>();

    // 失败缓存 - 避免重复加载不存在的图像
    private static final Map<String, Boolean> failureCache = new HashMap<>();
    private static final int MAX_FAILURE_CACHE_SIZE = 1000;

    // 预加载状态
    private static boolean isPreloaded = false;

    // 性能监控
    private static long totalLoadTime = 0;
    private static int loadCount = 0;
    private static int failureCount = 0;
    public static ImageIcon JT;
    public static ImageIcon getJT() {
        if (JT == null) {
            JT = new ImageIcon("res/failure.png");
        }
        return JT;
    }
    /**
     * 从NPK文件中读取指定皮肤的ImageIcon
     *
     * @param skin 皮肤名称（十六进制偏移，如"0x1a5de2bd"）
     * @param w 目标宽度
     * @param h 目标高度
     * @param npkFileName NPK文件名（如"items.npk"）
     * @return ImageIcon对象，如果失败返回null
     */
    /**
     * NPK图像加载 - 使用SoftReference缓存机制（参考CutButtonImage.getWdfPng）
     */

    public static ImageIcon getNpkPng(String path,String file) {
        return getNpkPng(path,-1,-1,file);
    }

    public static ImageIcon getNpkPng(String skin, int w, int h, String npkFileName) {
        long startTime = System.currentTimeMillis();
        try {
            // 确保NPK文件已预加载
            if (!isPreloaded) {
                preloadNpkFiles();
            }
            // 检查失败缓存，避免重复加载不存在的图像
            String failureKey = npkFileName + "_" + skin;
            if (failureCache.containsKey(failureKey)) {
                failureCount++;
                if (failureCount % 100 == 0) {
                    System.out.println("[NPK] 警告: 重复请求不存在的图像 " + skin + "，已失败 " + failureCount + " 次");
                }
                return getJT(); // 返回默认图标而不是null
            }
            if (w == -1) {
                // 原始尺寸图像处理
                SoftReference<BufferedImage> imageRef = imageDataCache.get(skin);
                BufferedImage bufferedImage = null;
                if (imageRef != null) {
                    bufferedImage = imageRef.get();
                }
                if (bufferedImage == null) {
                    // 如果缓存中不存在图像数据，则加载图像数据并缓存
                    bufferedImage = loadBufferedImageFromNpk(skin, npkFileName);
                    if (bufferedImage != null) {
                        imageDataCache.put(skin, new SoftReference<>(bufferedImage));
                    } else {
                        // 加载失败，记录到失败缓存
                        addToFailureCache(failureKey);
                        return getJT();
                    }
                }

                SoftReference<ImageIcon> iconRef = imageIconCache.get(skin);
                if (iconRef == null || iconRef.get() == null) {
                    ImageIcon imageIcon = null;
                    if (bufferedImage != null) {
                        imageIcon = new ImageIcon(bufferedImage);
                        bufferedImage.flush(); // 立即释放BufferedImage资源
                    }
                    // 参考CutButtonImage.getWdfPng的逻辑：检查图像是否有效
                    if (imageIcon != null && imageIcon.getIconHeight() == -1) {
                        imageIcon = getJT(); // 无效图像使用默认图标
                    }
                    if (imageIcon == null) {
                        imageIcon = getJT(); // null图像使用默认图标
                    }
                    iconRef = new SoftReference<>(imageIcon);
                    imageIconCache.put(skin, iconRef);
                }

                // 记录性能统计
                recordPerformance(startTime);
                return iconRef != null ? iconRef.get() : null;

            } else {
                // 缩放尺寸图像处理
                String cacheKey = w + "x" + h + "_" + skin;
                SoftReference<ImageIcon> iconRef = imageIconCache.get(cacheKey);
                if (iconRef == null || iconRef.get() == null) {
                    ImageIcon originalIcon = getNpkPng(skin, -1, -1, npkFileName);
                    if (originalIcon == JT) {
                        return JT; // 参考CutButtonImage.getWdfPng的逻辑
                    }
                    if (originalIcon == null) {
                        return JT; // 参考CutButtonImage.getWdfPng的逻辑
                    }
                    if (originalIcon.getIconHeight() != h || originalIcon.getIconWidth() != w) {
                        ImageIcon scaledIcon = new ImageIcon(originalIcon.getImage().getScaledInstance(w, h, Image.SCALE_FAST));
                        iconRef = new SoftReference<>(scaledIcon);
                        imageIconCache.put(cacheKey, iconRef);
                    } else {
                        return originalIcon;
                    }
                }

                // 记录性能统计
                recordPerformance(startTime);
                return iconRef != null ? iconRef.get() : null;
            }

        } catch (Exception e) {
            System.err.println("[NPK] 读取NPK图像失败: " + skin + " - " + e.getMessage());
            return null;
        }
    }




    /**
     * 根据指定的宽度、高度和裁剪模式对PNG图像进行裁剪并返回裁剪后的图像对象。
     * NPK加载PNG图像-拆分加载。
     * @param srcImageFile 源图像文件的路径。
     * @param w 裁剪后图像的宽度。
     * @param h 裁剪后图像的高度。
     * @param l 是否使用长边匹配模式。如果为true，则按照长边匹配进行裁剪；否则，按照短边匹配进行裁剪。
     * @return ImgZoom 图像裁剪后的对象。
     */
    static Map<String, ImgZoom> img8 = new HashMap<>();
    public static ImgZoom NpkcutsPng(String srcImageFile, int w, int h, boolean l, String fileName) {
        try {
            if (img8.get(srcImageFile) != null) {
                return new ImgZoom(img8.get(srcImageFile));
            }
            ImgZoom zoom = new ImgZoom();
            Image[] icons = new Image[9];
            BufferedImage src = loadBufferedImageFromNpk(srcImageFile, fileName);
            if (src != null) {
                zoom.setMiddlew(src.getWidth() - 2 * w);
                zoom.setMiddleh(src.getHeight() - 2 * h);
                zoom.setEdgew(w);
                zoom.setEdgeh(h);
                zoom.setImgs(icons);
                BufferedImage image1 = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image1, src, 0, 0);
                icons[0] = image1;
                BufferedImage image2 = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image2, src, w + zoom.getMiddlew(), 0);
                icons[1] = image2;
                BufferedImage image3 = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image3, src, 0, h + zoom.getMiddleh());
                icons[2] = image3;
                BufferedImage image4 = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image4, src, w + zoom.getMiddlew(), h + zoom.getMiddleh());
                icons[3] = image4;
                BufferedImage image5 = new BufferedImage(w, zoom.getMiddleh(), BufferedImage.TYPE_INT_ARGB);
                drawImg(image5, src, 0, h);
                icons[4] = image5;
                BufferedImage image6 = new BufferedImage(w, zoom.getMiddleh(), BufferedImage.TYPE_INT_ARGB);
                drawImg(image6, src, w + zoom.getMiddlew(), h);
                icons[5] = image6;
                BufferedImage image7 = new BufferedImage(zoom.getMiddlew(), h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image7, src, w, 0);
                icons[6] = image7;
                BufferedImage image8 = new BufferedImage(zoom.getMiddlew(), h, BufferedImage.TYPE_INT_ARGB);
                drawImg(image8, src, w, h + zoom.getMiddleh());
                icons[7] = image8;
                if (l) {
                    BufferedImage image9 = new BufferedImage(zoom.getMiddlew(), zoom.getMiddleh(),
                            BufferedImage.TYPE_INT_ARGB);
                    drawImg(image9, src, w, h);
                    icons[8] = image9;
                }
                img8.put(srcImageFile, zoom);
                return zoom;
            }
            System.out.println("找不到图像：" + srcImageFile);
            return null;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将src按要求画在img
     * @param w
     * 起始位置
     * @param h
     */
    public static void drawImg(BufferedImage img, BufferedImage src, int w, int h) {
        int maxw = w, maxh = h;
        maxw += img.getWidth();
        maxh += img.getHeight();
        if (maxw > src.getWidth()) {
            maxw = src.getWidth();
        }
        if (maxh > src.getHeight()) {
            maxh = src.getHeight();
        }
        for (int i = w; i < maxw; i++) {
            for (int j = h; j < maxh; j++) {
                img.setRGB(i - w, j - h, src.getRGB(i, j));
            }
        }
    }

    /**
     * NPK加载PNG按钮。
     * @param srcImageFile 源图像文件的路径。
     * @return 返回一个图像图标数组，表示切割后的按钮图标。
     * @throws Exception 如果过程中出现错误，则抛出异常。
     */
    static Map<String, ImageIcon[]> img6 = new HashMap<>();
    public static  ImageIcon[] NpkcutsPngBtn(String srcImageFile,String fileName) {
        try {
            if (img6.get(srcImageFile) != null){
                return img6.get(srcImageFile);
            }

            ImageIcon[] icons = new ImageIcon[3];
            ImageFilter cropFilter;
            BufferedImage src = loadBufferedImageFromNpk(srcImageFile, fileName);
            if (src != null) {
                int destWidth = src.getWidth();
                int destHeight = src.getHeight() / 3;
                for (int i = 0; i < 3; i++) {
                    cropFilter = new CropImageFilter(0, i * destHeight, destWidth, destHeight);
                    icons[i] = new ImageIcon(Toolkit.getDefaultToolkit().createImage(new FilteredImageSource(src.getSource(), cropFilter)));
                }
                img6.put(srcImageFile, icons);
                return icons;
            }else {
                icons[0] = getJT();
                icons[1] = getJT();
                icons[2] = getJT();
                return icons;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 从NPK文件加载BufferedImage
     */
    private static BufferedImage loadBufferedImageFromNpk(String skin, String npkFileName) {
        try {
            // 加载NPK文件
            NpkFile npkFile = loadNpkFile(npkFileName);
            if (npkFile == null) {
                return null;
            }

            // 查找指定的皮肤文件
            NpkEntry targetEntry = findEntryBySkin(npkFile, skin);
            if (targetEntry == null) {
                return null;
            }

            // 提取图像数据
            byte[] imageData = NpkTool.extractFile(npkFile, targetEntry, null);
            if (imageData == null || imageData.length == 0) {
                return null;
            }
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageData));
            CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, bufferedImage));
            // 创建BufferedImage
            return bufferedImage;

        } catch (Exception e) {
            System.err.println("[NPK] 加载BufferedImage失败: " + skin + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 添加到失败缓存
     */
    private static void addToFailureCache(String key) {
        if (failureCache.size() >= MAX_FAILURE_CACHE_SIZE) {
            // 清理一半的失败缓存
            int toRemove = failureCache.size() / 2;
            int removed = 0;
            for (String failKey : new ArrayList<>(failureCache.keySet())) {
                if (removed >= toRemove) break;
                failureCache.remove(failKey);
                removed++;
            }
        }
        failureCache.put(key, true);
    }

    /**
     * 记录性能统计
     */
    private static void recordPerformance(long startTime) {
        long endTime = System.currentTimeMillis();
        long loadTime = endTime - startTime;
        totalLoadTime += loadTime;
        loadCount++;

        // 简化日志输出
        if (loadCount % 100 == 0) { // 每100个图像输出一次统计
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
            long maxMemory = runtime.maxMemory() / 1024 / 1024;
//            System.out.println("[NPK] 已加载 " + loadCount + " 个图像, 平均耗时: " + (totalLoadTime / loadCount) + "ms, 内存: " + usedMemory + "MB/" + maxMemory + "MB, 失败: " + failureCount + " 次");
        }
    }

    /**
     * 从NPK文件中读取指定皮肤的原始ImageIcon（不缩放）
     */
    public static ImageIcon getNpkPngOriginal(String skin, String npkFileName) {
        return getNpkPng(skin, -1, -1, npkFileName);
    }


    /**
     * 加载NPK文件
     */
    private static NpkFile loadNpkFile(String npkFileName) {
        try {
            // 检查缓存
            if (npkFileCache.containsKey(npkFileName)) {
                return npkFileCache.get(npkFileName);
            }

            // 查找NPK文件
            File npkFile = findNpkFile(npkFileName);
            if (npkFile == null || !npkFile.exists()) {
                System.err.println("NPK文件不存在: " + npkFileName);
                return null;
            }

            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);

            // 缓存结果
            npkFileCache.put(npkFileName, parsedFile);

            return parsedFile;

        } catch (Exception e) {
            System.err.println("加载NPK文件失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 查找NPK文件
     */
    private static File findNpkFile(String npkFileName) {
        // 首先在当前目录查找
        File currentDir = new File(npkFileName);
        if (currentDir.exists()) {
            return currentDir;
        }

        // 在常见路径中查找
        String[] searchPaths = {
                ".",
                "./",
                "../",
                "G:/JXy2o/GameClient3.0/res/",
                "res/",
                "data/"
        };

        for (String path : searchPaths) {
            File file = new File(path, npkFileName);
            if (file.exists()) {
                return file;
            }
        }

        return null;
    }

    /**
     * 根据皮肤名称查找NPK条目
     */
    private static NpkEntry findEntryBySkin(NpkFile npkFile, String skin) {
        List<NpkEntry> entries = npkFile.getEntries();
        // 确保皮肤名称格式正确
        String targetSkin = skin;
        if (!targetSkin.startsWith("0x") && !targetSkin.startsWith("0X")) {
            targetSkin = "0x" + targetSkin;
        }
        if (!targetSkin.endsWith(".png")) {
            targetSkin = targetSkin + ".png";
        }
        // 查找匹配的条目 - 精确匹配
        for (NpkEntry entry : entries) {
            String fileName = entry.getFileName();
            if (fileName != null && fileName.equalsIgnoreCase(targetSkin)) {
                return entry;
            }
        }
        // 如果没找到，尝试不区分大小写的匹配
        String hexPart = targetSkin.replace("0x", "").replace("0X", "").replace(".png", "");
        for (NpkEntry entry : entries) {
            String fileName = entry.getFileName();
            if (fileName != null) {
                // 提取文件名中的十六进制部分
                String fileHexPart = fileName.replace("0x", "").replace("0X", "").replace(".png", "");
                if (fileHexPart.equalsIgnoreCase(hexPart)) {
                    return entry;
                }
            }
        }
        return null;
    }



    /**
     * 预加载常用NPK文件
     */
    public static void preloadNpkFiles() {
        if (isPreloaded) {
            return;
        }

        long totalStart = System.currentTimeMillis();
        System.out.println("[NPK] 开始预加载NPK文件...");

        // 预加载常用的NPK文件
        String[] npkFiles = {
                 "item.npk",
                 "gires4.npk",
                 "redmu.npk",
                 "she.npk",
             };

        for (String npkFileName : npkFiles) {
            try {
                long fileStart = System.currentTimeMillis();
                File npkFile = findNpkFile(npkFileName);
                if (npkFile != null && npkFile.exists()) {
                    System.out.println("[NPK] 预加载NPK文件: " + npkFileName + " (大小: " + (npkFile.length() / 1024 / 1024) + "MB)");
                    NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
                    npkFileCache.put(npkFileName, parsedFile);
                    long fileEnd = System.currentTimeMillis();
                    System.out.println("[NPK] 成功预加载: " + npkFileName + " (条目数: " + parsedFile.getEntries().size() + ") 耗时: " + (fileEnd - fileStart) + "ms");

                    // 内存监控
                    Runtime runtime = Runtime.getRuntime();
                    long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
                    long maxMemory = runtime.maxMemory() / 1024 / 1024;
                    System.out.println("[NPK] 预加载后内存使用: " + usedMemory + "MB / " + maxMemory + "MB");
                }
            } catch (Exception e) {
                System.err.println("[NPK] 预加载NPK文件失败: " + npkFileName + " - " + e.getMessage());
                e.printStackTrace();
            }
        }

        isPreloaded = true;
        long totalEnd = System.currentTimeMillis();
        System.out.println("[NPK] NPK文件预加载完成，总耗时: " + (totalEnd - totalStart) + "ms");
//        clearAllCache();
    }

    /**
     * 清空所有缓存
     */
    public static void clearAllCache() {
        System.out.println("[NPK] 清空所有缓存...");
        imageDataCache.clear();
        imageIconCache.clear();
        failureCache.clear();
        npkFileCache.clear();
        isPreloaded = false;
        totalLoadTime = 0;
        loadCount = 0;
        failureCount = 0;
        System.gc();

        // 内存监控
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        System.out.println("[NPK] 清空缓存后内存使用: " + usedMemory + "MB / " + maxMemory + "MB");
    }

    /**
     * 获取缓存状态信息
     */
    public static String getCacheInfo() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
        long maxMemory = runtime.maxMemory() / 1024 / 1024;

        // 统计有效缓存数量
        int validDataCache = 0;
        for (SoftReference<BufferedImage> ref : imageDataCache.values()) {
            if (ref.get() != null) validDataCache++;
        }

        int validIconCache = 0;
        for (SoftReference<ImageIcon> ref : imageIconCache.values()) {
            if (ref.get() != null) validIconCache++;
        }

        return String.format("NPK缓存: %d个文件, 数据缓存: %d/%d个有效, 图标缓存: %d/%d个有效, 失败缓存: %d个, 内存: %dMB/%dMB, 平均加载耗时: %dms, 失败次数: %d",
                           npkFileCache.size(),
                           validDataCache, imageDataCache.size(),
                           validIconCache, imageIconCache.size(),
                           failureCache.size(),
                           usedMemory, maxMemory,
                           loadCount > 0 ? totalLoadTime / loadCount : 0,
                           failureCount);
    }

    /**
     * 强制垃圾回收
     */
    public static void forceGarbageCollection() {
        System.gc();
        // runFinalization() 已过时，移除此调用
        System.gc();

        Runtime runtime = Runtime.getRuntime();
        long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        System.out.println("[NPK] 强制GC后内存使用: " + usedMemory + "MB / " + maxMemory + "MB");
    }
}
