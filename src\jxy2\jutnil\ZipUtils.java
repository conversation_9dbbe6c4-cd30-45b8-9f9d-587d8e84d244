package jxy2.jutnil;

import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.FileHeader;
import org.come.until.CutButtonImage;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

/**
*
* <AUTHOR>
* @date 2024/4/5 17:51
*/
public class ZipUtils {

    /**
     * @param zipFileFullName  zip文件所在的路径名
     * @param targetFile      要提取的文件
     * @param password        需要解压的密码
     * @return InputStream
     */
    public static InputStream unZipFile(String zipFileFullName, String targetFile, String password) {
        try {

            ZipFile zipFile = new ZipFile(zipFileFullName);
            zipFile.setFileNameCharset("GBK");
            // 如果解压需要密码
            if (zipFile.isEncrypted()) {
                zipFile.setPassword(password);
            }
            FileHeader fileHeader = zipFile.getFileHeader(targetFile);
            if (fileHeader == null) {
                System.out.println("找不到文件" + targetFile);
                return null;
            }
            return zipFile.getInputStream(fileHeader);
        } catch (Exception e) {
            //TODO ...
        }
        return null;
    }

    /**
     * 获取压缩文件中指定文件的输入流
     * @param str 压缩文件中指定文件的路径
     * @return 指定文件的输入流
     */

    public static InputStream getInputStream(String str) {
        String[] split = str.split("/");
        String fileName = split[0];
        String fileStr = fileName + ".pak";
        if (new File(str).exists()) {
            File file = new File(str);
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, fileInputStream));

                return fileInputStream;
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
        } else if (new File("res/"+fileStr).exists()) {
            InputStream inputStream = ZipUtils.unZipFile("res/"+fileName + ".pak", str, "java.nio.file.attribute.BasicFileAttributes");
            CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, inputStream));

            return inputStream;
        }
        return null;
    }


//    static {
//        new Thread(new CheckTimer()).start();
//    }

    /**
     * 从输入流中读取图像
     * @param inputStream 输入流
     * @return 读取到的图像
     */
    public static Image read(InputStream inputStream) {
        if (inputStream == null) {
            return CutButtonImage.getJT().getImage();
        }
        try {
            BufferedImage read = ImageIO.read(inputStream);
            return read;
        } catch (Exception e) {
            e.printStackTrace();
            return CutButtonImage.getJT().getImage();
        }
    }

}
