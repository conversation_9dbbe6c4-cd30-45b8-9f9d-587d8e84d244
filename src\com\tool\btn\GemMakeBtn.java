package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.GemMakeJpanel;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.test.Main;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Goodtype;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class GemMakeBtn extends MoBanBtn {

    private GemMakeJpanel makeJpanel;
    private int p;
    private Integer b;
    public GemMakeBtn(String iconpath, int type, Color[] colors, Font font, String text, int p, GemMakeJpanel makeJpanel, String prompt){
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.p = p;
        this.makeJpanel = makeJpanel;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        if (p == 2) {
            b = -1;
        }
        if (this.p == 8) {
            this.setText("打造");
            setFont(UIUtils.TEXT_FONT1);
            setForeground(Color.yellow);
            setVerticalTextPosition(SwingConstants.CENTER);
            setHorizontalTextPosition(SwingConstants.CENTER);
        }
        if (this.p >= 9 && this.p <= 11) {
            setText("可打造");
            setFont(UIUtils.TEXT_FONT);
            setVerticalTextPosition(SwingConstants.CENTER);
            setHorizontalTextPosition(SwingConstants.CENTER);
            setForeground(Color.GRAY);
        }
    }


    public GemMakeBtn(String iconpath, int type, Color[] colors, String text, Font font, int p, GemMakeJpanel makeJpanel) {
        super(iconpath, type,0, colors,"");
        // TODO Auto-generated constructor stub
        this.p = p;
        this.makeJpanel = makeJpanel;
        if (p == 2) {
            b = -1;
        }
        setText(text);
        setFont(font);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        if (p == 0 || p == 1) {
            makeJpanel.qh(p);
        } else if (p >= 2 && p <= 7) {
            int v = makeJpanel.option(p);
            if (v != -2) {
                makeJpanel.chang(v);
            }
        } else if (p == 8) {
            Goodstable good1 = makeJpanel.getGood(0);
            Goodstable good2 = makeJpanel.getGood(1);
            if (good1 == null) {
                return;
            }
            if (good2 == null) {
                return;
            }
            GemXXX(true, good1, good2);
        } else if (p >= 9 && p <= 11) {
            Goodstable good1 = makeJpanel.getGood(0);
            Goodstable good2 = makeJpanel.getGood(p - 7);
            if (good1 == null) {
                return;
            }
            if (good2 == null) {
                return;
            }
            StringBuffer buffer = new StringBuffer();
            buffer.append("#Y你确定花费#R ");
            buffer.append(Integer.parseInt(good2.getValue().split("\\|")[0].split("=")[1]) * 3200000);
            buffer.append(" #Y金钱拆卸该宝石吗?");
            OptionsJframe.getOptionsJframe().getOptionsJpanel().
			showBox(TiShiUtil.GemOff, new Goodstable[] { good1, good2 },buffer.toString());
        }
    }

    /**
     * 类型 true装备 false 脱下 第一个物品装备 第二个物品宝石
     */
    public static void GemXXX(boolean l, Goodstable... goods) {
        if (goods.length != 2) {
            return;
        }
        int lvl = 0;
        if (Goodtype.baoshi(goods[1].getType())) {
            String[] vs = goods[1].getValue().split("\\|");
            lvl = Integer.parseInt(vs[0].split("=")[1]);
        } else {
            return;
        }
        List<BigDecimal> rgids = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i].getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (i != 0 && ((goods[i].getStatus() != 0 && l) || (goods[i].getStatus() == 0 && !l))) {
                return;
            }
            if (goods[i].getStatus() == 0) {
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return;
                }
            } else {
                if (GoodsListFromServerUntil.ischoseExist(goods[i])) {
                    return;
                }
            }
            rgids.add(goods[i].getRgid());
        }
        BigDecimal money = null;
        if (l) {// 装备
            money = new BigDecimal(5000000 + lvl * 1000000);
            if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y金钱不足");
                return;
            }
            List<Integer> integers = null;
            int[] goodids = new int[lvl <= 4 ? 1 : (lvl - 3)];
            for (int i = 0; i < goodids.length; i++) {
                goodids[i] = 81095;
            }
            integers = GoodsListFromServerUntil.chaxuns(goodids);
            if (integers == null) {
                ZhuFrame.getZhuJpanel().addPrompt("宝石精华不足");
                return;
            }
            
            for (int i = 0; i < integers.size(); i++) {
                Goodstable good = GoodsListFromServerUntil.getGoodslist()[integers.get(i)];
                if (good != null) {
                    good.goodxh(1);
                    if (good.getUsetime() <= 0) {
                        GoodsListFromServerUntil.Deletebiaoid(good.getRgid());
                    }
                    rgids.add(good.getRgid());
                }
            }
            goods[1].setStatus(1);
            GoodsListFromServerUntil.fushis.put(goods[1].getRgid(), goods[1]);
            GoodsListFromServerUntil.Deletebiaoid(goods[1].getRgid());
        } else {// 脱下
            money = new BigDecimal(3200000 * lvl);
            if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y金钱不足");
                return;
            }
            if (!GoodsListFromServerUntil.newgood(goods[1])) {
                return;
            }
            GoodsListFromServerUntil.fushis.remove(goods[1].getRgid());
        }
        RoleData.getRoleData().getLoginResult()
                .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));
        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(20);
        operBean.setGoods(rgids);
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
    }

    public Integer getB() {
        return b;
    }

    public void setB(Integer b) {
        this.b = b;
    }
}
