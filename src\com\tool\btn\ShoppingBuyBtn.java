package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.role.RoleLingFa;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.ShoppingBuyJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ShoppingBuyJpanel;
import org.come.bean.BuyShopBean;
import org.come.entity.Goodstable;
import org.come.model.Shop;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;
import org.lottery.frame.LotteryIntegralMainJframe;
import org.lottery.panel.LotteryIntegralGoodsJpanel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.List;

public class ShoppingBuyBtn extends MoBanBtn {
    private ShoppingBuyJpanel buyJpanel;
    private int caozuo;
    private LotteryIntegralGoodsJpanel lotteryIntegralGoodsJpanel;

    public ShoppingBuyBtn(String iconpath, int type, int caozuo, ShoppingBuyJpanel buyJpanel) {
        super(iconpath, type);
        this.buyJpanel = buyJpanel;
        this.caozuo = caozuo;
    }

    public ShoppingBuyBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            ShoppingBuyJpanel buyJpanel) {
        super(iconpath, type,0, colors,"");
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.buyJpanel = buyJpanel;
        this.caozuo = caozuo;
    }

    public ShoppingBuyBtn(String iconpath, int type, String text, int caozuo,
            LotteryIntegralGoodsJpanel lotteryIntegralGoodsJpanel) {
        super(iconpath, type, UIUtils.COLOR_BTNPUTONG);
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(UIUtils.TEXT_HY16);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.lotteryIntegralGoodsJpanel = lotteryIntegralGoodsJpanel;
        this.caozuo = caozuo;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch (caozuo) {
        case 1:
            /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
            if (FormsManagement.getframe(14).isVisible()) {
                ZhuFrame.getZhuJpanel().addPrompt2("交易时不能购买物品");
                return;
            }
            int sum = Integer.parseInt(buyJpanel.getTextNumber().getText());
            if (sum <= 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("你购买数量为零");
                return;
            }
            // 根据格子判断剩余的物品
            // 根据商品id找到shop
            Shop shop = buyJpanel.getShop();
            if (shop != null) {
                // 根据格子判断剩余的物品
                Goodstable goodstable = UserMessUntil.getgoodstable(shop.getShopiid());
                sum = GoodsListFromServerUntil.Surplussum(goodstable != null ? goodstable.getType() + "" : "6500",
                        shop.getShopid(), sum);
                if (sum <= 0) {
                    ZhuFrame.getZhuJpanel().addPrompt2("你背包已满");
                    return;
                }
                try {
                    // 已发送数量
                    long fa = 0;
                    long gjg = shop.getShopprice();
                    long yy = RoleData.getRoleData().getLoginResult().getGold().longValue();
                    if (buyJpanel.getCount() == 0) {
                        if (shop.getShoptype()==1) {
                            yy = RoleData.getRoleData().getLoginResult().getCodecard().longValue();
                        }
                    } else {
                    	if (buyJpanel.Balance!=null) {
                    		yy = buyJpanel.Balance.longValue();
						}else if (buyJpanel.Currency.equals("绑玉")){
							yy = RoleData.getRoleData().getLoginResult().getSavegold().longValue();
						}
                    }
                    fa = yy / gjg;
                    if (fa > sum)
                        fa = sum;
                    yy = gjg * fa;
                    if (fa > 0) {
                        if (RoleLingFa.isFB2(shop.getShopname())) {
                            sum = 1;
                            fa = 1;
                            yy = gjg * fa;
                            if (!RoleLingFa.getRoleLingFa().addfb(shop.getShopname())) {
                                return;
                            }
                        }
                        BuyShopBean bean = new BuyShopBean();
                        if (buyJpanel.getCount() == 0) {
                            bean.setAte(3);
                            bean.setCd(shop.getShopid());
                            bean.setSum((int) fa);
                            bean.setnId(buyJpanel.getnId());
                        } else {
                            bean.setAte(1);
                            bean.setCd(shop.getShopid());
                            bean.setSum((int) fa);
                            if (buyJpanel.Balance!=null) {
                                buyJpanel.Balance = new BigDecimal(buyJpanel.Balance.longValue() - yy);
                            }
                        }
                        String senmes = Agreement.getAgreement().nbuyAgreement(GsonUtil.getGsonUtil().getgson().toJson(bean));
                        SendMessageUntil.toServer(senmes);
                    } else {
                        ZhuFrame.getZhuJpanel().addPrompt2("你没有足够的" + buyJpanel.Currency);
                    }
                } catch (NumberFormatException e1) {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                }
            }

            break;
        case 2:
            buyJpanel.setGoldType(1);
//            buyJpanel.getShopGoodsType(buyJpanel.getShopList(), buyJpanel.getGoldType() + "");
            buyJpanel.changeGoodsView();
            break;
        case 3:
            buyJpanel.setGoldType(0);
//            buyJpanel.getShopGoodsType(buyJpanel.getShopList(), buyJpanel.getGoldType() + "");
            buyJpanel.changeGoodsView();
            break;
        case 4:
            if (FormsManagement.getframe(14).isVisible()) {
                ZhuFrame.getZhuJpanel().addPrompt2("交易时不能购买物品");
                return;
            }
            // 根据格子判断剩余的物品
            // 根据商品id找到shop
            Shop shop2 = lotteryIntegralGoodsJpanel.getShop();
            if (shop2 != null) {
                // 根据格子判断剩余的物品
                Goodstable goodstable = UserMessUntil.getgoodstable(shop2.getShopiid());
                sum = GoodsListFromServerUntil.Surplussum(goodstable != null ? goodstable.getType() + "" : "6500",
                        shop2.getShopid(), 1);
                if (sum <= 0) {
                    ZhuFrame.getZhuJpanel().addPrompt2("你背包已满");
                    return;
                }
                try {
                    // 已发送数量
                    long fa = 0;
                    // 物品价格
                    long gjg = shop2.getShopprice();
                    // 获取自身携带的金额
                    long yy = RoleData.getRoleData().getLoginResult().getScoretype("幸运奖池积分").longValue();
                    fa = yy / gjg;
                    if (fa > sum)
                        fa = sum;
                    yy = gjg * fa;
                    if (fa > 0) {
                        if (RoleLingFa.isFB2(shop2.getShopname())) {
                            sum = 1;
                            fa = 1;
                            yy = gjg * fa;
                            if (!RoleLingFa.getRoleLingFa().addfb(shop2.getShopname())) {
                                return;
                            }
                        }
                        BuyShopBean bean = new BuyShopBean();
                        bean.setAte(1);
                        bean.setCd(shop2.getShopid());
                        bean.setSum((int) fa);
                        LotteryIntegralMainJframe
                                .getLotteryIntegralMainJframe()
                                .getLotteryIntegralMainPanel()
                                .getIntegralTypeLab()
                                .setText(
                                        "幸运奖池积分:"
                                                + (RoleData.getRoleData().getLoginResult().getScoretype("幸运奖池积分")
                                                        .longValue() - yy));
                        String senmes = Agreement.getAgreement().nbuyAgreement(
                                GsonUtil.getGsonUtil().getgson().toJson(bean));
                        SendMessageUntil.toServer(senmes);
                    } else {
                        ZhuFrame.getZhuJpanel().addPrompt2("你没有足够的幸运奖池积分");
                    }
                } catch (NumberFormatException e1) {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                }
            }
            break;
            case 5:
                break;
        default:
            break;
        }
        if (caozuo>=5) {
            BtnMoni(caozuo - 5);
            int js = caozuo-5;
            String npctype = js==0?"5":js==2?"15":js==3?"14":js==4?"18":js==5?"13":"9";
            List<Shop> npcshop = UserMessUntil.getNpcshop().getNpcShopMap().get(npctype);
            ShoppingBuyJframe.getShoppingBuyJframe().getShoppingBuyJpanel().showshop(npcshop, npctype, null);
        }
    }
    public void BtnMoni(int typeBtn){
        buyJpanel.getBtnGems()[typeBtn].btnchange(2);
        for (int i = 0; i < buyJpanel.getBtnGems().length; i++) {
            if (i!=typeBtn){
                buyJpanel.getBtnGems()[i].btnchange(0);
            }
        }
    }
}
