package jxy2.xbao;

import org.come.Frame.ZhuFrame;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

public class Xu<PERSON>baoMouse extends TemplateMouseListener {
    // true 装备页的 false属性页的
    public static boolean shijian = true;
    // 表示格子位置 -1表示灵宝装备 或者法宝1装备
    private int path;
    public XbaoJpanel xbaoJpanel;
    public static List<String> list = new ArrayList<>();
    public XuanbaoMouse(int path,XbaoJpanel xbaoJpanel) {
        this.path = path;
        this.xbaoJpanel = xbaoJpanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        list.clear();
        Xuanbao xuanbao = getXuanbao();
        if (xuanbao != null){
            if (e.getButton() == MouseEvent.BUTTON3) {// 右键点击脱下玄宝
                if (shijian) {
                    RoleXuanbao.getRoleXuanbao().choseuse(xuanbao, path >= 0);
                }else {
                    RoleXuanbao.getRoleXuanbao().choseBao = xuanbao;
                }
            }else {
                if (path>=0) {
                    xbaoJpanel.getXuanbaoframe()[path].setBorder(BorderFactory.createLineBorder(Color.red,2));
                    for (int i = 0; i < xbaoJpanel.getXuanbaoframe().length; i++) {
                        if (i != path)
                            xbaoJpanel.getXuanbaoframe()[i].setBorder(BorderFactory.createEmptyBorder());
                    }
                }else {
                    //调整已穿玄宝的坐标
                    RoleXuanbao.getRoleXuanbao().choseBao = xuanbao;
                }
                xbaoJpanel.LoadingOfSelected(xuanbao);
                RoleXuanbao.getRoleXuanbao().choseBao = xuanbao;
            }
            list.add(path+"");
            FormsManagement.Switchinglevel(149);
            if (!shijian){
                xbaoJpanel.setAttributeVisible(true);
            }
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        list.remove(path+"");
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        Xuanbao xuanbao = getXuanbao();
        if (xuanbao != null) {
            ZhuFrame.getZhuJpanel().creatXuantext(xuanbao);
            if (path>=0)
                xbaoJpanel.EqPain(path,100);
        }
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().clearxuantext();
        if (path>=0)
            xbaoJpanel.EqClearText(path);

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
    public Xuanbao getXuanbao() {
        return RoleXuanbao.getRoleXuanbao().getxuanbao(path);
    }
}
