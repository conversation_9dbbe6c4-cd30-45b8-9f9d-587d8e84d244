package jxy2.synt;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import org.come.model.Exchange;

import javax.swing.*;
import java.awt.*;

public class SynthesisModel extends JPanel {
    public JLabel jLabel;
    private String message;
    private String[] msg =new String[30];
    public Exchange exchange;
    private boolean isExpanded,allowRefresh;
    public SynthesisBtn[] rest = new SynthesisBtn[30];
    public SynthesisBtn petresistanceBtn;//抗性
    public SynthesisModel(int index) {
        this.setPreferredSize(new Dimension(159, 30));
        this.setOpaque(false);
        this.setLayout(null);
        this.isExpanded = false; // 默认不展开
        this.allowRefresh = false; // 默认不展开
        getPetresistanceBtn(index);
        getRest();
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
//        petresistanceBtn.setVisible(true);
//        Juitil.TextBackground(g,  petresistanceBtn.getText(), 13, 10, 2, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
    }

    public void Ixi(Exchange exchange){
        this.exchange = exchange;
        String[] vs = exchange.getMtype().split("\\|");
        for (String v : vs) {
            message = v;
        }
    }

    public SynthesisBtn getPetresistanceBtn(int index) {
        if (petresistanceBtn==null){
            petresistanceBtn = new SynthesisBtn(ImgConstants.tz122, 1,"", UIUtils.COLOR_BTNTEXT,this,index,-1);
            petresistanceBtn.setBounds(10 ,2, 144, 27);
            this.add(petresistanceBtn);
        }
        return petresistanceBtn;
    }

    public void setPetresistanceBtn(SynthesisBtn petresistanceBtn) {
        this.petresistanceBtn = petresistanceBtn;
    }

    public SynthesisBtn[] getRest() {
            for (int i = 0; i < rest.length; i++) {
              if (rest[i]==null){
                rest[i] = new SynthesisBtn(ImgConstants.tz121, 1, "", UIUtils.COLOR_BTNTEXT, this, -1,i);
                add(rest[i]);
            }
        }
        return rest;
    }

    public void setRest(SynthesisBtn[] rest) {
        this.rest = rest;
    }

    public Exchange getExchange() {
        return exchange;
    }

    public void setExchange(Exchange exchange) {
        this.exchange = exchange;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public boolean isAllowRefresh() {
        return allowRefresh;
    }

    public void setAllowRefresh(boolean allowRefresh) {
        this.allowRefresh = allowRefresh;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String[] getMsg() {
        return msg;
    }

    public void setMsg(String[] msg) {
        this.msg = msg;
    }
}
