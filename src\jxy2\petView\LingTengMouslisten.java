package jxy2.petView;

import org.come.Jpanel.WuLingJPanel;
import org.come.entity.Goodstable;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class LingTengMouslisten extends TemplateMouseListener {

    public JList<String> listpet;
    public WuLingJPanel wuLingJPanel;


    public LingTengMouslisten(JList<String> listpet, WuLingJPanel wuLingJPanel) {
        this.listpet = listpet;
        this.wuLingJPanel = wuLingJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (listpet.getSelectedIndex()==-1)return;
        String petrd = listpet.getModel().getElementAt(listpet.getSelectedIndex()).split("\\|")[1];
        Goodstable goodstable = GoodsListFromServerUntil.getRgid(new BigDecimal(petrd));
        if (goodstable!=null){
            wuLingJPanel.getLabgoodimg().setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 50 ,50));
            wuLingJPanel.setGoodrid(petrd);
            char name = goodstable.getGoodsname().charAt(0);
            wuLingJPanel.name = name+"级";
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {
            WuLingJPanel.idx = -1;
    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        if (listpet!=null){
            if (e.getY() / 20 < wuLingJPanel.listModel.getSize()){
                WuLingJPanel.idx = wuLingJPanel.listpet.locationToIndex(e.getPoint());
            }
        }
    }
}
