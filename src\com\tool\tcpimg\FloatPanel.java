package com.tool.tcpimg;

import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

public class FloatPanel extends JPanel {

    private static final long serialVersionUID = 1L;

    private RichLabel label;

    private long createTime;

    public FloatPanel(String text) {
        setBorder(null);
        setLayout(null);
        setOpaque(false);
        setIgnoreRepaint(true);
        setFocusable(false);
        label = new RichLabel(text,null);
        label.setLocation(4, 3);
        Dimension d = label.computeSize(14 * 7);
        label.setSize(d);
        setSize(8 + d.width, 6 + d.height);
        this.createTime = Util.getTime();
    }

    public FloatPanel(String text,Font font) {
        setBorder(null);
        setLayout(null);
        setOpaque(false);
        setIgnoreRepaint(true);
        setFocusable(false);
        label = new RichLabel(text,font);
        label.setLocation(4, 3);
        Dimension d = label.computeSize(14 * 7);
        label.setSize(d);
        setSize(8 + d.width, 6 + d.height);
        this.createTime = Util.getTime();
    }
    public FloatPanel() {
        this(null);
    }

    @Override
    public void paint(Graphics g) {
        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));
        g2d.setColor(Color.BLACK);
        g2d.fillRoundRect(0, 0, getWidth(), getHeight(), 10, 10);
        g2d.dispose();
      	g.translate( label.getX(), label.getY());
        label.paint(g);
        g.translate(-label.getX(),-label.getY());
    }

    public void setText(String chatText) {
        label.setText(chatText);
        Dimension d = label.computeSize(14 * 7);
        label.setSize(d);
        setSize(8 + d.width, 6 + d.height);
    }
    public void remove(){
    	label.remove();
    	this.disable();
    }
    public long getCreateTime() {
        return createTime;
    }
	@Override
	public void paintImmediately(int x, int y, int w, int h) {
//		super.paintImmediately(x, y, w, h);
	}
}
