package jxy2.refine;

import com.tool.role.RoleData;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SynthesisJpanel;
import org.come.bean.JadeorGoodstableBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.mouslisten.StorageJadeMouslisten;
import org.come.until.*;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.plaf.basic.BasicTreeUI;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeCellRenderer;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import java.awt.*;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
* 装备套装面板
* <AUTHOR>
* @date 2024/7/28 下午8:14
*/

public class EqsuitJpanel extends JPanel {
    public SuitBtn[] suitBtn =new SuitBtn[7];//套装合成小菜单按钮
    public JLabel[] fiveJade = new JLabel[5];// 放置5个玉符
    public JLabel[] twoEquipment = new JLabel[2];//套装合成装备+玉符
    public String[] suit;
    public PartJade partJade;
    public JLabel selection;
    private int minType = 0;
    //套装合成
    public JScrollPane jScrollPane, // 套装列表滚动条
            jScrollPane2; // 套装介绍|玉符数量 滚动条
    public JTree jTree; // 套装列表
    public DefaultMutableTreeNode top; // 总节点
    public boolean showAll = true;// 判断是否只显示已有玉符的套装（默认：全部显示）
    public String current= "",xz="";
    //套装洗炼
    public JLabel labSet;// 套装
    public JLabel[] labsx = new JLabel[4]; // 套装属性
    //套装升级
    private JLabel labtz1,// 未升级的套装
            labtz2,// 已升级的套装
            labyf, // 玉符
            labpz,// 品质
            labtz,// 套装
            labzb,// 未合成玉符装备
            labgs;// 个数/

    //兑换灵修值
    public static BigDecimal khdlxz;// 可获得灵修值
    private JLabel labTzyf,labTzcoll;// 放置套装玉符
    private JTextField textNum;// 可输入数量
    private static int number = -1;// 还需要玉符的数量
    public Goodstable[]  goods = new Goodstable[6];
    private SuitBtn workshopBtn;
    private static Goodstable goodstable = new Goodstable();
    private static JadeorGoodstableBean goodstableBean = new JadeorGoodstableBean();
    private static BigDecimal money,value,sxlxz;// 消耗灵修值;// 消耗金钱
    public EqsuitJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        suit = new String[]{"合成","洗炼","套装升级","玉符升级","拆/转移","兑换灵修","收录玉符"};
        for (int i = 0; i < suitBtn.length; i++) {
            suitBtn[i] = new SuitBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, suit[i], i, this,"");
            suitBtn[i].btnchange(i==0?2:0);
            suitBtn[i].setBounds(128+i*62, 48, 66, 24);
            add(suitBtn[i]);
        }

        //放置5个玉符JLabel
        for (int i = 0; i < fiveJade.length; i++) {
            fiveJade[i] = new JLabel();
            fiveJade[i].setBounds(180+i*52, 86, 50, 50);
            fiveJade[i].addMouseListener(new FiveJadeMouse(this,i));
            add(fiveJade[i]);
        }

        for (int t = 0; t < twoEquipment.length; t++) {
            twoEquipment[t] = new JLabel();
            twoEquipment[t].setBounds(215 + t * 134, 202, 50, 50);
            twoEquipment[t].addMouseListener(new RefineMouse(this, 24 + t));
            add(twoEquipment[t]);
        }

        selection = new JLabel();
        selection.setBounds(140,410,17,16);
        selection.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                super.mousePressed(e);
                showAll = !showAll;
                selection.setIcon(CutButtonImage.getWdfPng(!showAll? ImgConstants.tz84:ImgConstants.tz83,"defaut.wdf"));
                AccessSuitMsgUntil.showSuitMethod(top, showAll);
                DefaultTreeModel treeModel = (DefaultTreeModel) jTree.getModel();
                treeModel.reload();
            }
        });
        selection.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
        add(selection);

        // 炼化装备按钮
        workshopBtn = new SuitBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 99, this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);

        top = new DefaultMutableTreeNode("");
        // 循环放上套装面板的方法
        AccessSuitMsgUntil.showSuitMethod(top, showAll);

        // 套装列表
        jTree = new JTree(top);
        jTree.setOpaque(false);
        jTree.putClientProperty("JTree.lineStyle", "None"); // 去除jtree的线条
        ((BasicTreeUI) jTree.getUI()).setLeftChildIndent(0); // 设置父节点与子节点左对齐，并去除缩进
        DefaultTreeCellRenderer cellRenderer = new DefaultTreeCellRenderer() {
            @Override
            public Component getTreeCellRendererComponent(JTree tree, Object value, boolean selected, boolean expanded,
                                                          boolean leaf, int row, boolean hasFocus) {
                DefaultMutableTreeNode node = (DefaultMutableTreeNode) value;
                super.getTreeCellRendererComponent(tree, value, selected, expanded, leaf, row, hasFocus);

                // 判断是几级的叶子
                if (node.getLevel() == 2) {
                    int suitid = AccessSuitMsgUntil.returnSuitID(node.getParent().toString());// 套装id
                    int partId = AccessSuitMsgUntil.returnPartsID(node.getUserObject().toString());// 部件id
                    // 根据套装id和部件id获得玉符情况
                    PartJade jade = RoleData.getRoleData().getPackRecord().getPartJade(suitid, partId);
                    if (jade.isJade()) {
                        setForeground(new Color(102, 102, 102));
                    } else {
                        setForeground(new Color(213, 210, 209));
                    }
                }
                return this;
            }
        };
        cellRenderer.setLeafIcon(null); // 设置叶子图标
        try {
            cellRenderer.setOpenIcon(CutButtonImage.cuts("inkImg/button/B108.png")[0]);
            cellRenderer.setClosedIcon(CutButtonImage.cuts("inkImg/button/B109.png")[0]); // 设置关闭子节点图标
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        } // 设置打开子节点图标

        cellRenderer.setFont(new Font("宋体", Font.PLAIN, 14));// 设置字体.
        cellRenderer.setBackgroundNonSelectionColor(new Color(0, 0, 0, 0));// 设置非选定节点的背景色
        cellRenderer.setBackgroundSelectionColor(new Color(99, 93, 90));// 设置节点在选中状态下的背景色
        cellRenderer.setBorderSelectionColor(new Color(0, 0, 0, 0));// 设置选中状态下节点边框的颜色

        /*
         * 设置选时或不选时，文字的变化颜色
         */
        cellRenderer.setTextNonSelectionColor(Color.white);// 设置绘制未选中状态下节点文本的颜色
        cellRenderer.setTextSelectionColor(Color.white);// 设置绘制选中状态下节点文本的颜色
        jTree.setCellRenderer(cellRenderer);
        jTree.setRootVisible(false); // 隐藏根节点
        jTree.setRowHeight(20); // 设置节点行高
        // 单击展开子节点
        jTree.addMouseListener(new MouseAdapter() { // 添加鼠标事件处理
            @Override
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isLeftMouseButton(e)) { // 点击了鼠标左键
                    TreePath path = jTree.getSelectionPath();
                    if (path != null) {
                        if (jTree.isExpanded(path)) {
                            jTree.collapsePath(path);// 关闭节点
                        } else {
                            jTree.expandPath(path);// 展开节点
                            DefaultMutableTreeNode selectionNode =   (DefaultMutableTreeNode) jTree.getLastSelectedPathComponent();
                            if (selectionNode.toString().compareTo(current)!=0&&!selectionNode.isLeaf()){
                                current = selectionNode+">>";
                            }
                            twoEquipment[1].setIcon(null);
                        }
                    }

                }
            }
        });
        jTree.addTreeSelectionListener(new TreeSelectionListener() {
            @Override
            public void valueChanged(TreeSelectionEvent e) {
                JTree tree = (JTree) e.getSource();
                // 利用JTree的getLastSelectedPathComponent()方法取得目前选取的节点.
                DefaultMutableTreeNode selectionNode = (DefaultMutableTreeNode) tree.getLastSelectedPathComponent();

                if (selectionNode == null) {
                    jScrollPane2.setViewportView(null);
                    current = "" ;
                    xz = "" ;
                    return;
                }
                String nodeName = selectionNode.toString();
                if (selectionNode.isLeaf()) {// 判断是否是叶子节点
                    int suitid = AccessSuitMsgUntil.returnSuitID(selectionNode.getParent().toString());// 套装id
                    int partId = AccessSuitMsgUntil.returnPartsID(selectionNode.getUserObject().toString());// 部件id
                    // 判断是否有玉符
                    PartJade jade = RoleData.getRoleData().getPackRecord().getPartJade(suitid, partId);
                    partJade = jade;
                    xz = selectionNode.getUserObject().toString();
                } else {
                    xz = "" ;
                    int key = AccessSuitMsgUntil.returnSuitID(nodeName);
                    RichLabel label = new RichLabel(UserMessUntil.getAllSuit().getRolesuit().get(key).getIntroduce(),
                            UIUtils.TEXT_FONT);
                    Dimension d = label.computeSize(140);
                    label.setSize(d);
                    label.setPreferredSize(d);
                    jScrollPane2.setViewportView(label);
                    current = nodeName+">>";
                }
            }
        });
        // 套装列表滚动条
        jScrollPane = new JScrollPane(jTree);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setOpaque(false);
        jScrollPane.setBounds(465, 103, 155, 140);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        add(jScrollPane);

        // 套装列表滚动条
        jScrollPane2 = new JScrollPane();
        jScrollPane2.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane2.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane2.getVerticalScrollBar().setUnitIncrement(30);
        jScrollPane2.getViewport().setOpaque(false);
        jScrollPane2.setOpaque(false);
        jScrollPane2.setBounds(465, 277, 155, 140);
        jScrollPane2.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane2.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        add(jScrollPane2);

        //套装洗炼
        labSet = new JLabel("", SwingConstants.CENTER);
        labSet.setBounds(282, 146, 50, 50);
        labSet.setVisible(false);
        labSet.addMouseListener(new RefineMouse(this, 24));
        add(labSet);
        // 套装属性
        for (int i = 0; i < 4; i++) {
            labsx[i] = new JLabel();
            labsx[i].setVisible(false);
            add(labsx[i]);
        }

        // 未升级的套装
        labtz1 = new JLabel();
        labtz1.setBounds(225, 170, 50, 50);
        labtz1.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }
            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                Goodstable goodstable = goodstableBean.getGoodstable();
                if (goodstable == null)
                    return;
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
            }
        });
        labtz1.setVisible(false);
        add(labtz1);
        // 玉符
        labyf = new JLabel();
        labyf.setBounds(340, 170, 50, 50);
        labyf.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                int index = goodstableBean.getType() - 1;
                if (goodstableBean.getPartJade() != null && index >= 0 && index < 5) {
                    StorageJadeMouslisten.showMsg(index);
                }
            }
        });
        add(labyf);
        // 已升级的套装
        labtz2 = new JLabel();
        labtz2.setBounds(282, 268, 50, 50);
        labtz2.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }
            @Override
            public void mouseEntered(MouseEvent e) {
                Goodstable goodstable = EqsuitJpanel.getGoodstable();
                if (goodstable != null&&labtz2.getIcon()==null) {
                    return;
                }
                ZhuFrame.getZhuJpanel().creatgoodtext(EqsuitJpanel.getGoodstable());
            }
        });
        labtz2.setVisible(false);
        add(labtz2);
        // 套装
        labtz = new JLabel();
        labtz.setBounds(228, 192-30, 50, 50);
        labtz.addMouseListener(new MouseAdapter() {

            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                Goodstable goodstable = goodstableBean.getGoodstable();
                if (goodstable == null)
                    return;
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
            }
        });
        this.add(labtz);
        // 未合成玉符装备
        labzb = new JLabel();
        labzb.setBounds(336, 192-30, 50, 50);
        labzb.addMouseListener(new MouseAdapter() {

            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                Goodstable goodstable = getGoodstable();
                if (goodstable == null)
                    return;
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
            }
        });
        this.add(labzb);

        // 品质
        labpz = new JLabel("", SwingConstants.CENTER);
        labpz.setBounds(233,202-28,66,15);
        labpz.setFont(UIUtils.TEXT_FONT2);
        labpz.setForeground(Color.white);
        this.add(labpz);

        // 个数
        labgs = new JLabel("", SwingConstants.CENTER);
        labgs.setBounds(233,200,66,15);
        labgs.setVisible(false);
        labgs.setFont(UIUtils.TEXT_FONT1);
        labgs.setForeground(Color.white);
        this.add(labgs);

        // 放置套装玉符
        labTzyf = new JLabel("", SwingConstants.CENTER);
        labTzyf.setBounds(282, 171-20, 50, 50);
        labTzyf.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                int index = goodstableBean.getType() - 1;
                if (goodstableBean.getPartJade() != null && index >= 0 && index < 5) {
                    StorageJadeMouslisten.showMsg(index);
                }
            }
        });
        this.add(labTzyf);

        // 放置套装玉符
        labTzcoll = new JLabel("", SwingConstants.CENTER);
        labTzcoll.setBounds(282, 171-20, 50, 50);
        labTzcoll.addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                clear();
            }

            @Override
            public void mouseExited(MouseEvent e) {
                ZhuFrame.getZhuJpanel().cleargoodtext();
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                int index = goodstableBean.getType() - 1;
                if (goodstableBean.getPartJade() != null && index >= 0 && index < 5) {
                    StorageJadeMouslisten.showMsg(index);
                }
            }
        });
        this.add(labTzcoll);
// 可输入数量
        textNum = new JTextField();
        textNum.setBounds(253,256,121,23);
        textNum.setFont(UIUtils.TEXT_FONT1);
        textNum.setOpaque(false);
        textNum.setBorder(BorderFactory.createEmptyBorder());
        textNum.setCaretColor(Color.white);
        textNum.setForeground(Color.white);
        textNum.addKeyListener(new KeyListener() {
            @Override
            public void keyTyped(KeyEvent e) {
                // TODO Auto-generated method stub
                int charstr = e.getKeyChar();
                // control the value between 0 and 9
                if (charstr < KeyEvent.VK_0 || charstr > KeyEvent.VK_9) {
                    e.consume();
                    return;
                }
                String str = textNum.getText();
                if (str.length() == 0)
                    textNum.setText("");
                if (str.length() == 1)
                    if (Long.parseLong(str) == 0)
                        textNum.setText("");
                if (str.length() >= 3) {
                    e.consume();
                    return;
                } // 不超过100个兑换数量
            }

            @Override
            public void keyPressed(KeyEvent e) {
            }

            @Override
            public void keyReleased(KeyEvent e) {

            }
        });
        textNum.getDocument().addDocumentListener(new DocumentListener() {

            @Override
            public void removeUpdate(DocumentEvent e) {
                String str = textNum.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        if (goodstableBean.getType() == 0 && goodstableBean.getPartJade() == null
                                && goodstableBean.getGoodstable() == null)
                            return;
                        int price = 3;
                        if (goodstableBean.getType() != 0 && goodstableBean.getPartJade() != null) {
                            price = AccessSuitMsgUntil.returnExcNum(goodstableBean.getType());
                        }
                        khdlxz = new BigDecimal(Long.parseLong(str) * price);
                    }
                } else {
                    khdlxz = null;
                }
            }

            @Override
            public void insertUpdate(DocumentEvent e) {
                String str = textNum.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        if (goodstableBean.getType() == 0 && goodstableBean.getPartJade() == null
                                && goodstableBean.getGoodstable() == null)
                            return;
                        int price = 3;
                        if (goodstableBean.getType() != 0 && goodstableBean.getPartJade() != null) {
                            price = AccessSuitMsgUntil.returnExcNum(goodstableBean.getType());
                        }
                        khdlxz = new BigDecimal(Long.parseLong(str) * price);
                    }
                } else {
                    khdlxz = null;
                }
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                String str = textNum.getText();
                if (str != null && !str.equals("")) {
                    if (Long.parseLong(str) > 0) {
                        if (goodstableBean.getType() == 0 && goodstableBean.getPartJade() == null
                                && goodstableBean.getGoodstable() == null)
                            return;
                        int price = 3;
                        if (goodstableBean.getType() != 0 && goodstableBean.getPartJade() != null) {
                            price = AccessSuitMsgUntil.returnExcNum(goodstableBean.getType());
                        }
                        khdlxz = new BigDecimal(Long.parseLong(str) * price);
                    }
                } else {
                    khdlxz = null;
                }
            }
        });
        this.add(textNum);
    }

    /**清除*/
    public void clear() {
        Arrays.fill(goods, null);
        labpz.setVisible(minType==3);
        labzb.setVisible(minType==4);
        labtz.setVisible(minType==4);

        for (JLabel jLabel:fiveJade){
            jLabel.setVisible(minType==0||minType==3||minType==5||minType==6);
        }
        for (JLabel jLabel:labsx){
            jLabel.setVisible(minType==1);
            jLabel.setText("");
        }
        for (JLabel jLabel:twoEquipment){
            jLabel.setVisible(minType==0);
            jLabel.setIcon(null);
        }
        jScrollPane.setVisible(minType==0||minType==3||minType==5||minType==6);
        jScrollPane2.setVisible(minType==0||minType==3||minType==5||minType==6);
        selection.setVisible(minType==0||minType==3||minType==5||minType==6);
        labyf.setVisible(minType==2||minType==3||minType==5||minType==6);
        labSet.setVisible(minType==1);
        labSet.setIcon(null);

        labtz1.setVisible(minType==2);
        labtz2.setVisible(minType==2);

        labyf.setBounds(minType==2?338:320, minType==2?170:168, 50, 50);

        workshopBtn.setText("?");
        labpz.setText("");
        labgs.setText("");
        money = null;
        labtz1.setIcon(null);
        labyf.setIcon(null);
        labtz.setIcon(null);
        labzb.setIcon(null);
        value=null;
        goodstable=null;
        goodstableBean.setGoodstable(null);
        goodstableBean.setPartJade(null);
        goodstableBean.setType(0);

        labTzyf.setVisible(minType==5);
        textNum.setVisible(minType==5);
        khdlxz = null;
        labTzyf.setIcon(null);
        textNum.setText("");

        labTzcoll.setIcon(null);
        labTzcoll.setVisible(minType==6);
        sxlxz =null;
    }


    /** 套装点击good */
    public void ClickSuit(Goodstable good, int path) {
        if (path < 24) {
            if (good!=null&& Goodtype.Equipment(good.getType())&&minType==0) {
                changesui(good, 0);
            }else if (minType==1){
                changesui(good, 0);
            }
        }else {
            changesui(null, path - 24);
        }
        String v = detectionSuit();
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }

    /** 套装合成检测公式 */
    public String detectionSuit() {
        if (minType==0&&goods[0]!=null&&twoEquipment[1].getIcon()!=null){
            return "合成";
        }else if (minType==1&&goods[0]!=null){
            return "洗炼";
        }else if (minType==2&&goodstableBean.getGoodstable()!=null){
            return "升级";
        }else if (minType==3&&labyf.getIcon()!=null){
            return "升级";
        }else if (minType==4||minType==5||minType==6){
            return workshopBtn.getText();
        }
        return "?";
    }

    public void clickGood(Goodstable good, int path) {
        if (path < 24) {
            if (good!=null&& Goodtype.Equipment(good.getType())) {
                changesxl(good, 0);
            }
        }else {
            changesxl(null, path - 24);
            for (int k = 0; k < 4; k++) {
                labsx[k].setText("");
            }
        }
        String v = detectionSuit();
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }
    private void changesxl(Goodstable good, int i) {
        goods[i] = good;
        SynthesisJpanel.getGoodstableBean().setGoodstable(good);
        if (good != null) {
            // 将原属性放上去
            labSet.setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
            List<String> attr = AccessSuitMsgUntil.getSuitAttr(AccessSuitMsgUntil.getExtra(good.getValue(),
                    "套装属性"));
            if (attr == null) return;
            int index = Math.min(attr.size(), 4);
            for (int j = 0; j < index; j++) {
                labsx[j].setText(attr.get(j));
            }
        }else {
            labSet.setIcon(null);
        }
    }

    private void changesui(Goodstable good, int i) {
        goods[i] = good;
        SynthesisJpanel.getGoodstableBean().setGoodstable(good);
        if (good != null) {
            twoEquipment[i].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        }else {
            twoEquipment[i].setIcon(null);
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);

        if (minType==0||minType==3||minType==5||minType==6){
            Juitil.ImngBack(g, Juitil.tz21, 458, 76, 166, 175, 1);
            Juitil.TextBackground(g, "套装列表", 13, 462, 82, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            Juitil.ImngBack(g, Juitil.tz21, 458, 251, 166, 175, 1);
            Juitil.TextBackground(g, "套装说明", 13, 462, 257, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            for (int i = 0; i < 5; i++) {
                Juitil.ImngBack(g, Juitil.good_2, 180 + i * 52, 86, 50, 50, 1);
                ImageIcon icon = CutButtonImage.getWdfPng("0x6FEB8" + (666 + i), 49, 49, "defaut.wdf");
                g.drawImage(icon.getImage(), 180 + i * 52, 86, 50, 50, null);
            }
            Juitil.ImngBack(g, Juitil.tz76, 601, 102, 19, 140, 1);
            Juitil.ImngBack(g, Juitil.tz76, 601, 277, 19, 140,1);
            Juitil.TextBackground(g, "只显示已有玉符", 13, 160, 410, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            if (partJade != null) {
                Juitil.TextBackground(g, partJade.getJade1() + "", 13, 232 - 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                Juitil.TextBackground(g, partJade.getJade2() + "", 13, 284 - 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                Juitil.TextBackground(g, partJade.getJade3() + "", 13, 336 - 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                Juitil.TextBackground(g, partJade.getJade4() + "", 13, 388 - 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                Juitil.TextBackground(g, partJade.getJade5() + "", 13, 440 - 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
            } else {
                for (int i = 0; i < 5; i++) {
                    Juitil.TextBackground(g, "0", 13, 180 + i * 52, 86, UIUtils.COLOR_value, UIUtils.FZCY_HY13);
                }
            }
            if (current != null) {
                Juitil.TextBackground(g, current + xz, 13, 180, 140, UIUtils.COLOR_White, UIUtils.FZCY_HY13);
            }
        }

        switch (minType) {
            case 0:
                for (int i = 0; i < 2; i++) {
                    Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
                    if (twoEquipment[i].getIcon() == null) {
                        Juitil.TextBackground(g, getPrompt(getMinType() + 10)[i], 13, 226 + i * 134, 220, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    }
                }
                break;
            case 1:
                Juitil.TextBackground(g, "现有套装属性", 14, 263, 198, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
                Juitil.ImngBack(g, Juitil.good_2, 282, 146, 50, 50, 1);
                Juitil.ImngBack(g, Juitil.tz26, 248 - 28, 220, 121, 23, 1);
                Juitil.ImngBack(g, Juitil.tz26, 248 - 28, 220 + 25, 121, 23, 1);
                Juitil.ImngBack(g, Juitil.tz26, 340, 220, 46, 23, 1);
                Juitil.ImngBack(g, Juitil.tz26, 340, 220 + 25, 46, 23, 1);
                if (labSet.getIcon() == null) {
                    Juitil.TextBackground(g, "装备", 13, 292, 220 - 56, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                }
                //绘制套装属性
                for (int i = 0; i < 4; i++) {
                    if (labsx[i].getText() == null) return;
                    int x = i % 2 == 0 ? 222 : 342;
                    int y = 225 + (i / 2) * 27;
                    Juitil.TextBackground(g, labsx[i].getText(), 13, x, y, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                }
                break;
            case 2://套装升级
                int centerX = 282; // 圆心的X坐标
                int centerY = 203; // 圆心的Y坐标
                int radius = 65;  // 圆的半径
                int numElements = 3; // 元素数量
                double angleIncrement = Math.PI / numElements * 2 ; // 角度增量，注意这里是π/3，因为我们只需要两次增量
                double startAngle = Math.PI / 2; // 起始角度，指向正上方
                for (int i = 0; i < numElements; i++) {
                    double angle = startAngle + i * angleIncrement; // 当前元素的角度
                    int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                    int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                    Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1); // 假设Juitil.ImngBack是用来绘制图像的方法
                }
                drawTextBackgroundIfNoIcon(g, labtz2, 0, 286, 285);
                drawTextBackgroundIfNoIcon(g, labtz1, 1, 236, 188);
                drawTextBackgroundIfNoIcon(g, labyf, 2, 348, 188);
            break;
            case 3://玉符升级
                for (int i = 0; i < 4; i++) {
                    if (i<=1){
                        Juitil.ImngBack(g, Juitil.tz26, 249, 170+i*25, 66, 23, 1);
                        Juitil.TextBackground(g, "品质", 13, 232-15, 202-30, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                        Juitil.TextBackground(g, "个数", 13, 232-15, 228-30, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                    }else {
                        Juitil.ImngBack(g, Juitil.tz26, 249, 190+i*25, 121, 23, 1);
                        Juitil.TextBackground(g, "消耗", 13, 232-15, 213+28, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                        Juitil.TextBackground(g, "拥有", 13, 232-15, 213+28*2, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                        Util.drawMoney(g, 255, 226+28*2);
                        if (money != null)
                            Util.drawPrice(g, money, 255, 226+30);
                    }
                }
                Juitil.ImngBack(g, Juitil.good_2, 320, 168, 50, 50, 1);
                if (labyf.getIcon()==null){
                    Juitil.TextBackground(g, "玉符", 13, 331, 183, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                }
                if (!labgs.getText().isEmpty()){
                    Util.drawPrice(g, BigDecimal.valueOf(Long.parseLong(labgs.getText())),255,211);
                }
                if (number!=-1) {
                    Juitil.TextBackground(g, "还需要" + number + "个玉符", 13, 251, 222, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                }
                break;
                case 4://拆解转移
                    for (int i = 0; i < 4; i++) {
                        Juitil.ImngBack(g, Juitil.tz26, 280, 230+i*25, 121, 23, 1);
                        Juitil.TextBackground(g, getPrompt(13)[i], 13, 210, 233+i*25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                    }
                    Juitil.ImngBack(g, Juitil.good_2, 228, 192-30, 50, 50, 1);
                    Juitil.ImngBack(g, Juitil.good_2, 336, 192-30, 50, 50, 1);
                    g.drawImage(Juitil.tz171.getImage(),293,186-30,30,56,null);


                    if (labtz.getIcon()==null) {
                        for (int i = 0; i < 2; i++) {
                            Juitil.TextBackground(g, getPrompt(14)[i], 13, 238 + i * 108, 192 - 12, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                        }
                    }
                    // 画拥有的灵修值
                    if (RoleData.getRoleData().getLoginResult().getScoretype("灵修值") != null) {
                        Util.drawPrice(g, RoleData.getRoleData().getLoginResult().getScoretype("灵修值"), 286, 322);
                    }
                    // 画拥有金钱
                    Util.drawMoney(g, 286, 271);
                    // 画消耗金钱
                    if (money != null)
                        Util.drawPrice(g, money, 286, 248);
                    // 画所需灵修值
                    if (value != null) {
                        Util.drawPrice(g, value, 286, 297);
                    }
                    break;
            case 5://兑换灵修值
                Juitil.ImngBack(g, Juitil.good_2, 282, 171-20, 50, 50, 1);
                if (labTzyf.getIcon()==null){
                    Juitil.TextBackground(g, "物品", 13, 292, 188-20, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                }
                Juitil.TextBackground(g, "玉符或九玄仙玉", 13, 260, 226-20, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                for (int i = 0; i < 2; i++) {
                    Juitil.ImngBack(g, Juitil.tz26, 249, 255+i*40, 121, 23, 1);
                    Juitil.TextBackground(g, "兑换数量：", 13, 262-15, 213+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                    Juitil.TextBackground(g, "可获得灵修值：", 13, 262-15, 213+33*2, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                }
                // 画可获得灵修值
                if (khdlxz != null)
                    Util.drawPrice(g, khdlxz, 253, 310);
                break;

                case 6://收录玉符
                    Juitil.ImngBack(g, Juitil.good_2, 282, 171-20, 50, 50, 1);
                    if (labTzcoll.getIcon()==null){
                        Juitil.TextBackground(g, "物品", 13, 292, 188-20, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    }
                    for (int i = 0; i < 3; i++) {
                        Juitil.ImngBack(g, Juitil.tz26, 249, 224+i*40, 121, 23, 1);
                        Juitil.TextBackground(g, "消耗银两：", 13, 262-15, 206, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                        Juitil.TextBackground(g, "消耗灵修值：", 13, 262-15, 213+35, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                        Juitil.TextBackground(g, "已有灵修值：", 13, 262-15, 213+38*2, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
                    }
                    // 画所需金钱
                    if (money != null)
                        Util.drawPrice(g, money, 262-10, 240);
                    // 画所需灵修值
                    if (sxlxz != null)
                        Util.drawPrice(g, sxlxz, 262-10, 280);
                    // 画拥有的灵修值
                    if (RoleData.getRoleData().getLoginResult().getScoretype("灵修值") != null) {
                        Util.drawPrice(g, RoleData.getRoleData().getLoginResult().getScoretype("灵修值"), 262-10, 320);
                    }
                    break;

        }
    }

    private void drawTextBackgroundIfNoIcon(Graphics g, JLabel label, int index, int x, int y) {
        if (label.getIcon() == null) {
            Juitil.TextBackground(g, getPrompt(getMinType() + 10)[index], 13, x, y, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        }
    }
    /**根究类型返回数组文本*/
    public String[] getPrompt(int type) {
        String[] result = EqartificeJapanel.PROMPT_MAP.get(type);
        return result != null ? result.clone() : null;
    }

    public SuitBtn[] getSuitBtn() {
        return suitBtn;
    }

    public void setSuitBtn(SuitBtn[] suitBtn) {
        this.suitBtn = suitBtn;
    }

    public PartJade getPartJade() {
        return partJade;
    }

    public void setPartJade(PartJade partJade) {
        this.partJade = partJade;
    }

    public JLabel[] getTwoEquipment() {
        return twoEquipment;
    }

    public void setTwoEquipment(JLabel[] twoEquipment) {
        this.twoEquipment = twoEquipment;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public SuitBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(SuitBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }

    public JLabel getLabtz1() {
        return labtz1;
    }

    public void setLabtz1(JLabel labtz1) {
        this.labtz1 = labtz1;
    }

    public JLabel getLabtz2() {
        return labtz2;
    }

    public void setLabtz2(JLabel labtz2) {
        this.labtz2 = labtz2;
    }

    public JLabel getLabyf() {
        return labyf;
    }

    public void setLabyf(JLabel labyf) {
        this.labyf = labyf;
    }

    public static Goodstable getGoodstable() {
        return goodstable;
    }

    public static void setGoodstable(Goodstable goodstable) {
        EqsuitJpanel.goodstable = goodstable;
    }

    public static JadeorGoodstableBean getGoodstableBean() {
        return goodstableBean;
    }

    public static void setGoodstableBean(JadeorGoodstableBean goodstableBean) {
        EqsuitJpanel.goodstableBean = goodstableBean;
    }

    public static BigDecimal getMoney() {
        return money;
    }

    public static void setMoney(BigDecimal money) {
        EqsuitJpanel.money = money;
    }

    public static int getNumber() {
        return number;
    }

    public static void setNumber(int number) {
        EqsuitJpanel.number = number;
    }

    public JLabel getLabpz() {
        return labpz;
    }

    public void setLabpz(JLabel labpz) {
        this.labpz = labpz;
    }

    public JLabel getLabgs() {
        return labgs;
    }

    public void setLabgs(JLabel labgs) {
        this.labgs = labgs;
    }

    public static BigDecimal getValue() {
        return value;
    }

    public static void setValue(BigDecimal value) {
        EqsuitJpanel.value = value;
    }

    public JLabel getLabtz() {
        return labtz;
    }

    public void setLabtz(JLabel labtz) {
        this.labtz = labtz;
    }

    public JLabel getLabzb() {
        return labzb;
    }

    public void setLabzb(JLabel labzb) {
        this.labzb = labzb;
    }

    public JLabel getLabTzyf() {
        return labTzyf;
    }

    public void setLabTzyf(JLabel labTzyf) {
        this.labTzyf = labTzyf;
    }

    public JTextField getTextNum() {
        return textNum;
    }

    public void setTextNum(JTextField textNum) {
        this.textNum = textNum;
    }

    public static BigDecimal getSxlxz() {
        return sxlxz;
    }

    public static void setSxlxz(BigDecimal sxlxz) {
        EqsuitJpanel.sxlxz = sxlxz;
    }

    public JLabel getLabTzcoll() {
        return labTzcoll;
    }

    public void setLabTzcoll(JLabel labTzcoll) {
        this.labTzcoll = labTzcoll;
    }

    public JTree getjTree() {
        return jTree;
    }

    public void setjTree(JTree jTree) {
        this.jTree = jTree;
    }
}

