package com.tool.tcp;


import java.awt.image.BufferedImage;
import java.awt.image.WritableRaster;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.SoftReference;
import java.util.*;

/**
 * Sprite 工厂类<br>
 * <AUTHOR>
 * @date
 */
public class SpriteFactory {
	/**动画播放每帧的间隔(ms)*/
	public static final int ANIMATION_INTERVAL = 100;
	/** 文件头标记 */
	public static final String WAS_FILE_TAG = "SP";
	public static final String WAS_FILE_TAGTWO = "SH";
	
	static final int TCP_HEADER_SIZE = 12;
	static final int TYPE_ALPHA = 0x00;// 前2位
	static final int TYPE_ALPHA_PIXEL = 0x20;// 前3位 0010 0000
	static final int TYPE_ALPHA_REPEAT = 0x00;// 前3位
	static final int TYPE_FLAG = 0xC0;// 2进制前2位 1100 0000
	static final int TYPE_PIXELS = 0x40;// 以下前2位 0100 0000
	static final int TYPE_REPEAT = 0x80;// 1000 0000
	static final int TYPE_SKIP = 0xC0; // 1100 0000
	/**动作类型*/
	public static String[] ActionType=new String[14];
	/**锁*/
	public static Object UPDATE_LOCK = new Object();
	/**预加载的tcp*/
	public static List<Object> loads=new ArrayList<>();
	public static TCPLoadThread thread;
	/**获取新方式素材索引*/
	public static Map<Long,HHOne[]> indexMap;
	/**索映射文件地址*/
	public static Map<Integer,FileRandom> indexFileMap;
	/**素材缺失代替*/
    public static Sprite[] fillIns=new Sprite[6];
	/**记录已加载的Sprite*/
	public static Map<String,SoftReference<Sprite>> TcpMap=new HashMap<>();
	/**记录新加载方式的Sprite*/
	public static Map<Long  ,SoftReference<Sprite>> TcpTwoMap=new HashMap<>();
	/**影子素材*/
	public static Sprite shadow;
	public static final BufferedImage image=new BufferedImage(1,1,BufferedImage.TYPE_INT_ARGB);
    static String[] vvv=new String[]{"hit.tcp","magic.tcp","defend.tcp","guard.tcp","die.tcp","attack.tcp","t2.tcp","4001.tcp","4002.tcp","4003.tcp","4004.tcp","400509","400535","400314"}; 
	static{
		//初始化动作
		ActionType[0]="walk";//走
		ActionType[1]="run";//跑
		ActionType[2]="stand";//站
		ActionType[3]="t1";//特效1
		ActionType[4]="hit";//被攻击
		ActionType[5]="magic";//法术
		ActionType[6]="defend";//防御
		ActionType[7]="guard";//进入战斗
		ActionType[8]="die";//死
		ActionType[9]="attack";//攻击
		ActionType[10]="attackrun";//攻击跑
		ActionType[11]="t2";//特效2
		ActionType[12]="t3";//特效3
		ActionType[13]="walk";//特效3

		indexFileMap=new HashMap<>();
		indexMap=new HashMap<>();//初始化

		try {
			File file=new File("skin/hh.init");
			if(file.exists()) {
				FileInputStream fis=new FileInputStream(file);
				byte[] data = new byte[fis.available()];
				fis.read(data);
				SpriteHead randomIn=new SpriteHead(data);
				randomIn.seek(2);
				while (randomIn.available()>0) {
					long skin=randomIn.readLong();
					indexMap.put(skin,initHHOne(skin,randomIn));
				}
				fis.close();
				randomIn.close();
				// 反向获取 hh 内容
//				for (Map.Entry<Long, HHOne[]> entry : indexMap.entrySet()) {
//					long skin = entry.getKey();
//					HHOne[] hhObject = entry.getValue();
//					System.out.println("Skin: " + skin + ", HH Object: " + Arrays.toString(hhObject));
//				}
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}

		thread = new TCPLoadThread();
		thread.start();

		shadow=VloadSprite("skin/YZ.tcp", null);
		shadow.updateToTime(0, 0);
	}
	/**初始化*/
	public static HHOne[] initHHOne(long skin,SpriteHead randomIn) throws IOException{
		HHOne[] ones=new HHOne[randomIn.read()];
		for (int i = 0; i < ones.length; i++) {
			HHOne one=new HHOne();
			one.setAct((byte)randomIn.read());
			long[] ls=new long[randomIn.read()];
			for (int j = 0; j < ls.length; j++) {
				ls[j]=randomIn.readLong();
			}
//			if (skin>>40>0) {//调整坐骑图层顺序
//				long a=ls[0];long b=ls[1];
//				ls[1]=a;     ls[0]=b;
//			}
			one.setLs(ls);
			ones[i]=one;
		}
		return ones;
	}
    /**清除未使用的Sprite 重置使用次数*/
    public static void ResetAndRemove(){
        try {
        	String key; 
			Iterator<String> it = TcpMap.keySet().iterator();
			while(it.hasNext()) {
				key=it.next();
			    SoftReference<Sprite> a=TcpMap.get(key);
			    if (a==null||a.get()==null||a.get().getUse()==0) it.remove();
			    else a.get().setUse(0);
		    }	
			long keyLong;
			Iterator<Long> itLong = TcpTwoMap.keySet().iterator();
			while(itLong.hasNext()) {
				keyLong=itLong.next();
				SoftReference<Sprite> a=TcpTwoMap.get(keyLong);
				if (a==null||a.get()==null||a.get().getUse()==0) itLong.remove();
			    else a.get().setUse(0);
			}
			int keyInt;
			Iterator<Integer> itInt=indexFileMap.keySet().iterator();
			while(itInt.hasNext()) {
				keyInt=itInt.next();
				FileRandom fileRandom=indexFileMap.get(keyInt);
				if (fileRandom==null||fileRandom.isEnd()) itInt.remove();
			}


        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
    }


	/**获取对应的索引加载文件*/
	public static synchronized FileRandom getFileRandom(int i){
		FileRandom fileRandom=indexFileMap.get(i);
		if (fileRandom==null) {
			try {
				fileRandom=new FileRandom("skin/"+i+".hh");
				indexFileMap.put(i, fileRandom);
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
		}else {
			fileRandom.setUse(0);
		}
		return fileRandom;
	}
	/**预加载*/
	public static Sprite Prepare(long skin,int act) {
		SoftReference<Sprite> s = TcpTwoMap.get(skin);
		if (s == null || s.get() == null) {
			addLoad(((act==0||act==1||act==2||act==10)?TCPLoadThread.N0:TCPLoadThread.N1)+skin);
			return null;
		} else {
			return s.get();
		}
	}
	/**预加载*/
	public static Sprite Prepare(String path) {
		SoftReference<Sprite> s = TcpMap.get(path);
		if (s == null || s.get() == null) {
			addLoad(path);
			return null;
		} else {
			return s.get();
		}
	}


    /**添加预加载的tcp*/
	public static void addLoad(Object object) {
		if (object==null) {return;}
		synchronized (UPDATE_LOCK) {
			if (!loads.contains(object)) {
				loads.add(object);
				thread.setLoadTcp();
			}		
		}
	}
	/**获取一个加载的路径*/
	public static Object getLoad(){
		synchronized (UPDATE_LOCK) {
			return loads.size()!=0?loads.get(0):null;
		}
	}
	/**移除一个加载路径*/
	public static void clearLoad(Object load){
		synchronized (UPDATE_LOCK) {
			loads.remove(load);
		}
	}
	/**表情包加载*/
	public static Animation loadAnimation(String filename) {
		Animation animation=null;
    	try {
        	File file=new File(filename);
    		if(file!=null&&file.exists()) {
    			InputStream in=new FileInputStream(file);
    			byte[] buf = new byte[2];
    			in.read(buf);
    			String fag=new String(buf);
    			int version=fag.equals(WAS_FILE_TAG)?0:fag.equals(WAS_FILE_TAGTWO)?1:-1;
    			if (version>=0) {
    				buf = new byte[in.available()];
    				int a = 0, count = 0;
    				while (in.available() > 0) {
    					a = in.read(buf,count,in.available());
    					count += a;
    				}
    				in.close();	
    				SpriteHead randomIn = new SpriteHead(buf);
    				animation=randomIn.initTwo();
        		}else {
        			in.close();	
				}
    	    }
    		//else { System.err.println("Warning: 找不到精灵的资源文件!"+filename); }
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		if (animation==null) {
			animation=new Animation();
			Frame frame=new Frame(image, 0, 0);
			animation.addFrame(frame);
		}
		return animation;
	}
    /**直接获取 新加载方式获取*/
    public static Sprite VloadSprite(long skin,boolean is,String value) {
    	Sprite sprite=null;
        try {
        	FileRandom fileRandom=getFileRandom((int)(skin>>32));
			if (fileRandom!=null) {
				byte[] bs=new byte[4];
				fileRandom.read(skin&0x7fffffff, bs);
				int size=0;
				for (int i = 0; i < bs.length; i++) {
					int v=(bs[i] & 0xff);
					v=v<<(8*i);
					size+=v;
				}
				bs=new byte[size];
				fileRandom.read((skin+4)&0x7fffffff, bs);
				SpriteHead randomIn = new SpriteHead(bs);
				sprite=randomIn.init(value,is,1);
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
        if (sprite==null){//获取固定素材
            sprite=getFillIn(1);
        }
        return sprite;
    }

	/**直接获取 按路径去获取*/
	public static Sprite VloadSprite(String filename, String value) {
		if (filename == null || filename.length() == 0) {
			return null;
		}
		Sprite sprite = null;
		try {
			File file = new File(filename);
			if (file != null && file.exists()) {
				InputStream in = new FileInputStream(file);
				byte[] buf = new byte[2];
				in.read(buf);
				String fag = new String(buf);
				int version = fag.equals(WAS_FILE_TAG) ? 0 : fag.equals(WAS_FILE_TAGTWO) ? 1 : -1;
				if (Objects.equals(fag, "SW"))version=1;//幻方
				if (version >= 0) {
					buf = new byte[in.available()];
					int a, count = 0;
					while (in.available() > 0) {
						a = in.read(buf, count, in.available());
						count += a;
					}
					in.close();
					SpriteHead randomIn = new SpriteHead(buf);
					sprite = randomIn.init(value, isV(filename), version);
				} else {
					in.close();
				}
			} else {
				//              System.err.println("Warning: 找不到精灵的资源文件!" + filename);
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		if (sprite == null && (value == null || value.length() > 0)) {//获取固定素材  不是固定素材加载
			sprite = getFillIn(getAct(filename));
		}
		return sprite;
	}




    /**指定着色方案*/
    public static void assignColor(short[] palette,String value){
        try {
            String[] v=value.split("\\|");
            int sum=Integer.parseInt(v[0]);
            int start=Integer.parseInt(v[1]);
            int end=0;
            for (int i = 1; i <=sum; i++) {
                start=end;
                end=Integer.parseInt(v[i+1]);
                ColorationScheme scheme=new ColorationScheme(v,sum+2+(i-1)*9);
                for (int j = start; j < end; j++) {
                    palette[j] = scheme.mix(palette[j]);
                }
            }
        } catch (Exception e) {
            // TODO: handle exception
            System.err.println("错误格式的着色方案");
        }
    }
    /** 素材缺失代替*/
    public static Sprite getFillIn(int act){
        int value=0;
        if (act==1||act==0||act==10){
            value=1;
        }else if (act==2||act==7){
            value=2;
        }else if (act==4||act==6){
            value=3;
        }else if (act==5||act==3||act==9||act==11||act==12){
            value=4;
        }else if (act==8){
            value=5;
        }
        Sprite sprite=fillIns[value];
        if (sprite==null){
            String path=null;
            if (value==1){//站立 火柴人
            	path="skin/火柴人/run.tcp";
            }else if (value==2){//跑 火柴人
            	path="skin/火柴人/stand.tcp";
            }else if (value==3){//被攻击 火柴人
            	path="skin/火柴人/hit.tcp";
            }else if (value==4){//法术 火柴人
            	path="skin/火柴人/magic.tcp";
            }else if (value==5){//墓碑
            	path="resource/FightingSkill/持续状态/墓碑.tcp";
            }else{//特效素材
            	path="resource/FightingSkill/1025.tcp";
            }
            sprite=VloadSprite(path,"");
            fillIns[value]=sprite;
        }
        return  sprite;
    }
    /**二次加载 （图片加载）*/
    public static void VloadImg(Sprite sprite) {
    	synchronized (sprite) {
    		
    		int loadDir=sprite.getLoad();
        	int count  =sprite.getFrameCount();
    		if (loadDir==-1) {return;}
    		loadDir%=sprite.getAnimationCount();
    		
    		//判断是否需要共用的方向
    		boolean is=false;
    		if (sprite.getAnimationCount()==4||sprite.getAnimationCount()==8) {
    			is=true;
				if (loadDir==1) { loadDir=0; }
				else if(loadDir==3) { loadDir=2; }
				else if(loadDir==7) { loadDir=5; }
			}
    		
        	//避免重复加载图片
        	if (sprite.getLoadFrame(loadDir).getImage()!=null) {return;}
        	SpriteHead in=sprite.getHead();
        	if (in!=null) {
        		for (int i = 0; i < count; i++) {
        			Frame frame=sprite.getFrames()[loadDir*count+i];
					BufferedImage bufferedImage=createDraw(in, frame, sprite);
					frame.setImage(bufferedImage);
				}
        	}else {//使用默认的1*1图片
        		for (int i = 0; i < count; i++) {
					sprite.getFrames()[loadDir*count+i].setImage(image);
				}
			}
        	if (is) {//对象转换
    			if (loadDir==0) {     sprite.translate(0, 1); } 
				else if(loadDir==2) { sprite.translate(2, 3); }
				else if(loadDir==5) { sprite.translate(5, 7); }
			}
        	sprite.removeHead();//加载完毕
        }
    }
    public static BufferedImage createDraw(SpriteHead in, Frame frame,Sprite sprite){
    	try {
    		if (frame.getPos()==0) { return image; }
    		in.seek(frame.getPos());    		
    		int frameX = in.readInt();
    		int frameY = in.readInt();
    		if (sprite.getFrameCount()==1) {
    			frameX=sprite.getRefPixelX();
    			frameY=sprite.getRefPixelY();
    		}
    		int frameWidth = in.readInt();
    		int frameHeight = in.readInt();
    		if (frameHeight>1000) { 
    			System.err.println("报警:"+frameHeight);	 
    			if (frameHeight>=5000) { return image; }//严重报警
    		}
			int[] lineOffsets = new int[frameHeight];
			for (int l = 0; l < frameHeight; l++) {
				lineOffsets[l] = in.readInt();
			}
			frame.setRefPixelX(frameX);frame.setRefPixelY(frameY);
			if (frameWidth==0||frameHeight==0) { return image; }
			short[] palette=in.getPalette();
			BufferedImage image = new BufferedImage(frameWidth,frameHeight,BufferedImage.TYPE_INT_ARGB);
			WritableRaster raster=image.getRaster();
			int[] iArray=new int[4];
			int b, x, c;
			int index;
			int count;
			for (int y = 0; y < frameHeight; y++) {
				x = 0;
				in.seek(lineOffsets[y]+frame.getPos());
				ss:while (x < frameWidth) {
					b = in.read();
					switch ((b & TYPE_FLAG)) {
					case TYPE_ALPHA:
						if ((b & TYPE_ALPHA_PIXEL) > 0) {
							index = in.read();
							c = palette[index];
							iArray[0]=(c >>> 11) & 0x1F;
							iArray[1]=(c >>> 5 ) & 0x3f;
							iArray[2]=c & 0x1F;
							iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
							iArray[1]=(iArray[1]<<2)|(iArray[1]&3);
							iArray[2]=(iArray[2]<<3)|(iArray[2]&7);
							iArray[3]=(b & 0x1F)<<3|(b&7);
							raster.setPixel(x++, y, iArray);
						} else if (b != 0) {// ???
							count = b & 0x1F;// count
							b = in.read();// alpha
							index = in.read();
							c = palette[index];
							iArray[0]=((c >>> 11) & 0x1F);
							iArray[1]=((c >>> 5 ) & 0x3f);
							iArray[2]=( c & 0x1F);
							iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
							iArray[1]=(iArray[1]<<2)|(iArray[1]&3);
							iArray[2]=(iArray[2]<<3)|(iArray[2]&7);
							iArray[3]=(b & 0x1F)<<3|(b&7);
							for (int i = 0; i < count; i++) {			
								raster.setPixel(x++, y, iArray);
							}
						} else {// block end
							if (x==0 &&y > 0) {//x还是0的时候 复制上一行的数据
								int yTwo = y - 1;
								for (int i = x; i < frameWidth; i++) { 
									raster.setPixel(i, y, raster.getPixel(i, yTwo, iArray)); 
								}
							}
							break ss;
						}
						break;
					case TYPE_PIXELS:
						count = b & 0x3F;
						for (int i = 0; i < count; i++) {
							index = in.read();
							if (index!=-1) {
								iArray[0] = ((palette[index] >>> 11) & 0x1F);
								iArray[1] = ((palette[index] >>> 5) & 0x3f);
								iArray[2] = (palette[index] & 0x1F);
								iArray[0] = (iArray[0] << 3) | (iArray[0] & 7);
								iArray[1] = (iArray[1] << 2) | (iArray[1] & 3);
								iArray[2] = (iArray[2] << 3) | (iArray[2] & 7);
								iArray[3] = 0xFF;
								try {
									raster.setPixel(x++, y, iArray);
								} catch (Exception e) {
//                                    throw new RuntimeException(e);
                                }

							}
						}
						break;
					case TYPE_REPEAT:
						count = b & 0x3F;
						index = in.read();
						c = palette[index];
						iArray[0]=((c >>> 11) & 0x1F);
						iArray[1]=((c >>> 5 ) & 0x3f);
						iArray[2]=( c & 0x1F);
						iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
						iArray[1]=(iArray[1]<<2)|(iArray[1]&3);
						iArray[2]=(iArray[2]<<3)|(iArray[2]&7);
						iArray[3]= 0xFF;
						for (int i = 0; i < count; i++) {
							raster.setPixel(x++, y, iArray);
						}
						break;
					case TYPE_SKIP:
						count = b & 0x3F;
						x += count;
						break;	
					default:
						break ss;
					}
				}
				if (x > frameWidth)
					System.err.println("block end error: [" + y + "][" + x + "/" + frameWidth + "]");
			}
			return image;
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
    	return image;
    }
    public static BufferedImage createDrawTwo(SpriteHead in, Frame frame,int frameCount,int refPixelX,int refPixelY,int width,int height){
    	try {
    		if (frame.getPos()==0) { return image; }
    		in.seek(frame.getPos());
    		int dx = in.readInt();
    		int dy = in.readInt();
    		frame.setRefPixelX(dx);frame.setRefPixelY(dy);
    		if (frameCount==1) {
    			dx=refPixelX;
    			dy=refPixelY;
    		}
    		int frameWidth = in.readInt();
    		int frameHeight = in.readInt();
    		int[] lineOffsets = new int[frameHeight];
    		for (int l = 0; l < frameHeight; l++) {
    			lineOffsets[l] = in.readInt();
    		}
    		if (frameWidth==0||frameHeight==0) { return image; }
    		short[] palette=in.getPalette();
    		BufferedImage image = new BufferedImage(width, height,BufferedImage.TYPE_INT_ARGB);
			WritableRaster raster=image.getRaster();
			dx = refPixelX - dx;
			dy = refPixelY - dy;
			int[] iArray=new int[4];
			int b, x, c;
			int index;
			int count;
			for (int y = 0; y + dy<height && y < frameHeight; y++) {
				x = 0;
				in.seek(lineOffsets[y]+frame.getPos());
				ss:while (x < frameWidth) {
					b = in.read();
					switch ((b & TYPE_FLAG)) {
					case TYPE_ALPHA:
						if ((b & TYPE_ALPHA_PIXEL) > 0) {
							index = in.read();
							c = palette[index];
							iArray[0]=((c >>> 11) & 0x1F);
							iArray[1]=((c >>> 5 ) & 0x3f);
							iArray[2]=( c & 0x1F);
							iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
							iArray[1]=(iArray[1]<<2)|(iArray[0]&3);
							iArray[2]=(iArray[2]<<3)|(iArray[0]&7);
							iArray[3]=(b & 0x1F)<<3;
							try {
								raster.setPixel(x +dx, y +dy, iArray);
                           }catch (Exception e) {
								
							}finally{
								x++;
							}
						} else if (b != 0) {// ???
							count = b & 0x1F;// count
							b = in.read();// alpha
							index = in.read();
							c = palette[index];
							iArray[0]=((c >>> 11) & 0x1F);
							iArray[1]=((c >>> 5 ) & 0x3f);
							iArray[2]=( c & 0x1F);
							iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
							iArray[1]=(iArray[1]<<2)|(iArray[0]&3);
							iArray[2]=(iArray[2]<<3)|(iArray[0]&7);
							iArray[3]=(b & 0x1F)<<3;
							for (int i = 0; i < count; i++) {			
								try {
									raster.setPixel(x +dx, y +dy, iArray);
								}catch (Exception e) {
									
								}finally{
									x++;
								}
							}
						} else {// block end
							if (x==0 &&y > 0) {//x还是0的时候 复制上一行的数据
								int yTwo = y - 1;
								for (int i = x; i < frameWidth; i++) { 
									raster.setPixel(i, y, raster.getPixel(i, yTwo, iArray)); 
								}
							}
							break ss;
						}
						break;
					case TYPE_PIXELS:
						count = b & 0x3F;
						for (int i = 0; i < count; i++) {
							index = in.read();		
							iArray[0]=((palette[index] >>> 11) & 0x1F);
							iArray[1]=((palette[index] >>> 5 ) & 0x3f);
							iArray[2]=( palette[index] & 0x1F);
							iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
							iArray[1]=(iArray[1]<<2)|(iArray[0]&3);
							iArray[2]=(iArray[2]<<3)|(iArray[0]&7);
							iArray[3]=0x1F<<3;
							try {
								raster.setPixel(x +dx, y +dy, iArray);
							}catch (Exception e) {
								
							}finally{
								x++;
							}
						}
						break;
					case TYPE_REPEAT:
						count = b & 0x3F;
						index = in.read();
						c = palette[index];
						iArray[0]=((c >>> 11) & 0x1F);
						iArray[1]=((c >>> 5 ) & 0x3f);
						iArray[2]=( c & 0x1F);
						iArray[0]=(iArray[0]<<3)|(iArray[0]&7);
						iArray[1]=(iArray[1]<<2)|(iArray[0]&3);
						iArray[2]=(iArray[2]<<3)|(iArray[0]&7);
						iArray[3]= 0x1F<<3;
						for (int i = 0; i < count; i++) {				
							try {
								raster.setPixel(x +dx, y +dy, iArray);
							}catch (Exception e) {
								
							}finally{
								x++;
							}
						}
						break;
					case TYPE_SKIP:
						count = b & 0x3F;
						x += count;
						break;	
					default:
						break ss;
					}
				}
				if (x > frameWidth)
					System.err.println("block end error: [" + y + "][" + x + "/" + frameWidth + "]");
			}
			return image;
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
    	return image;
    }
    /**是否要偷工减料*/
    public static boolean isV(String filename) {
        s:for (int i = 0; i < 11; i++) {
            if (filename.endsWith(vvv[i])) {
                for (int j = 11; j < vvv.length; j++) {
                    if (filename.indexOf(vvv[j])!=-1) {break s;}
                }
                return true;
            }
        }
        return false;
    }
    /**路径判断动作类型*/
    public static int getAct(String filename) {
    	if (filename.length()<=4) { return -1; }
    	filename=filename.substring(0, filename.length()-4);
        for (int i = 0; i < ActionType.length; i++) {
            if (filename.endsWith(ActionType[i])) {
                return i;
            }
        }
        return -1;
    }
    /**方向调整*/
    public static int changdir(int dir, int size) {
        if (size == 2) {
            return dir==3?0:1;
        }else {
            switch (dir) {
                case 0:dir = 6;break;
                case 1:dir = 3;break;
                case 2:dir = 7;break;
                case 3:dir = 0;break;
                case 4:dir = 4;break;
                case 5:dir = 1;break;
                case 6:dir = 5;break;
                case 7:dir = 2;break;
                default:break;
            }
        }
        return dir;
    }
    /**获取动作*/
	public static String getActionType(int type) {
		return ActionType[type];
	}
	public static HHOne[] getHHOne(long skin){
		HHOne[] ones=indexMap.get(skin);
		if (ones==null&&skin>0x7FFFFFFF&&(skin>>32)>18) {
			ones=indexMap.get((((skin>>32)-18)<<32|(int)skin));
		}
		return ones;
	}
	/**创建NewPart对象*/
	public static NewPart createPart(String skin, int act, int lvl,String color){
		if (color!=null) { color=TCPLoadThread.addMap(color); }
		NewPart part=null;
		if (act!=-2&&act!=-1&&act!=-3) {
			try {
				long skinInt=Long.parseLong(skin);
				HHOne[] ones=SpriteFactory.getHHOne(skinInt);
				if (ones!=null) {
					part = new PartTwo(skinInt, ones, act, lvl,color);
				}
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
		}
		if (part==null) {part=new PartOne(skin, act, lvl, color);}
		return part;
	}
	public static NewPart createPart(long skin, int act, int lvl,String color){
		if (color!=null) { color=TCPLoadThread.addMap(color); }
		NewPart part=null;
		if (act!=-2&&act!=-1) {
			try {
					HHOne[] ones = SpriteFactory.getHHOne(skin);
					if (ones != null) {
						//获取皮肤
						part = new PartTwo(skin, ones, act, lvl, color);
				}
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
		}
		if (part==null) {part=new PartOne(skin+"", act, lvl, color);}
		return part;
	}
	public static NewPart setPart(NewPart newPart,int lvl,String skin){
		try {
			long skinLong=Long.parseLong(skin);
			HHOne[] ones=SpriteFactory.getHHOne(skinLong);
			if (ones!=null) {
				return newPart.setPart(lvl,skinLong,ones);
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
		return newPart.setPart(lvl, skin);
	}
}

