package jxy2.refine;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;
import org.gemstone.btn.GemstoneOperationBtn;
import org.gemstone.panel.GemstoneOperationRecastTypePanel;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GemsTJpanel extends JPanel {
    public String[] textSymbol;
    private GemsBtn[] btnGems = new GemsBtn[3];
    private int minType = 0;
    private BigDecimal money = new BigDecimal(0);// 消耗金钱
    public Goodstable[] goods = new Goodstable[2];
    public JLabel[] towEquipment = new JLabel[2];//五
    private GemsBtn workshopBtn;
    private GemstoneOperationBtn reelectBtn; // 重铸按钮
    public GemstoneOperationRecastTypePanel gemstoneOperationRecastTypePanel;
    private Goodstable reelectGoods = new Goodstable(); // 重选类型type
    private JLabel recastGemstone; // 重铸宝石
    public GemsTJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        gemstoneOperationRecastTypePanel = new GemstoneOperationRecastTypePanel();
        gemstoneOperationRecastTypePanel.setVisible(false);
        this.add(gemstoneOperationRecastTypePanel);
        textSymbol = new String[]{"合成","重铸","鉴定"};
        for (int i = 0; i < btnGems.length; i++) {
            btnGems[i] = new GemsBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, textSymbol[i], i, this,"");
            btnGems[i].btnchange(i==0?2:0);
            btnGems[i].setBounds(138+i*66, 48, 66, 24);
            add(btnGems[i]);
        }
        for (int i = 0; i < towEquipment.length; i++) {
            towEquipment[i] = new JLabel();
            towEquipment[i].setBounds(215 + i * 134,202,50,50);
            towEquipment[i].addMouseListener(new RefineMouse(this, 24 + i));
            this.add(towEquipment[i]);
        }
        workshopBtn = new GemsBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "合成", 99,this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);

        // 切换属性
        reelectBtn = new GemstoneOperationBtn(ImgConstants.tz127, 1, UIUtils.COLOR_BTNTEXT, "重选类型", UIUtils.MSYH_HY13, 9);
        reelectBtn.setVisible(false);
        reelectBtn.setBounds(274, 321, 68, 17);
        this.add(reelectBtn);

        recastGemstone = new JLabel();
        recastGemstone.setOpaque(false);
        recastGemstone.setVisible(false);
        recastGemstone.setBounds(282, 266, 50, 50);
        this.add(recastGemstone);
    }

    public void ClickSuit(Goodstable good, int path) {
        if (path<24){
                boolean a = true;
                int i =1;
                if (good != null&&good.getType()==744){
                    if (goods[i] == null) {
                        change(good, i);
                        a = false;
                    }
                }
            if (a) {
                if (goods[0] == null) {
                    change(good, 0);
                }else {
                    change(good, 1);
                }
            }
        }else {
            good.setIsSelected(0);
            change(null, path - 24);
        }

        String v = minType==1?"重铸":minType==2?"鉴定":"合成";
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }
    private void change(Goodstable good, int i) {
        goods[i] = good;
        if (good != null) {
            towEquipment[i].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        }else {
            towEquipment[i].setIcon(null);

        }
    }
    public void clear() {
        for (int i = 0; i < 2; i++) {
            if (goods[i]!=null) {
                goods[i].setIsSelected(0);
            }
            goods[i] = null;
            towEquipment[i].setIcon(null);

        }
        money  = new BigDecimal(0);
        workshopBtn.setText("?");
        reelectBtn.setVisible(minType==1);
        recastGemstone.setVisible(minType==1);
//        gemstoneOperationRecastTypePanel.setVisible(minType==1);
    }

    public static Map<Integer,String[]> integerMap =new ConcurrentHashMap<>();
    static {
        integerMap.put(0,new String[]{"宝石","宝石"});
        integerMap.put(1,new String[]{"宝石","材料"});
        integerMap.put(2,new String[]{"奇异","材料"});
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        for (int i = 0; i < 2; i++) {
            Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
            if (towEquipment[i].getIcon()==null){
                Juitil.TextBackground(g, integerMap.get(minType)[i], 13, 226 + i * 134, 202 + 18, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }
            Juitil.ImngBack(g, Juitil.tz26, 500, 334+i*25, 121, 23, 1);
        }
        if (minType==1){
            Juitil.ImngBack(g, Juitil.good_2, 282, 266, 50, 50, 1);
            Juitil.TextBackground(g, "类型", 13, 292, 282, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        }


        Juitil.TextBackground(g, "消耗金钱", 13, 440, 310+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        Juitil.TextBackground(g, "拥有金钱", 13, 440, 335+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        // 画现金
        Util.drawMoney(g, 504, 374);
        // 画消耗金钱
        if (money != null)
            Util.drawPrice(g, money, 504, 374-25);
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public GemsBtn[] getBtnGems() {
        return btnGems;
    }

    public void setBtnGems(GemsBtn[] btnGems) {
        this.btnGems = btnGems;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public GemsBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(GemsBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }

    public GemstoneOperationRecastTypePanel getGemstoneOperationRecastTypePanel() {
        return gemstoneOperationRecastTypePanel;
    }

    public void setGemstoneOperationRecastTypePanel(GemstoneOperationRecastTypePanel gemstoneOperationRecastTypePanel) {
        this.gemstoneOperationRecastTypePanel = gemstoneOperationRecastTypePanel;
    }

    public Goodstable getReelectGoods() {
        return reelectGoods;
    }

    public void setReelectGoods(Goodstable reelectGoods) {
        this.reelectGoods = reelectGoods;
    }

    public JLabel getRecastGemstone() {
        return recastGemstone;
    }

    public void setRecastGemstone(JLabel recastGemstone) {
        this.recastGemstone = recastGemstone;
    }

    public GemstoneOperationBtn getReelectBtn() {
        return reelectBtn;
    }

    public void setReelectBtn(GemstoneOperationBtn reelectBtn) {
        this.reelectBtn = reelectBtn;
    }
}
