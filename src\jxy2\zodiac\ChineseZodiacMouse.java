package jxy2.zodiac;

import org.come.Frame.MsgJframe;
import org.come.login.SpriteBtn;
import org.come.until.FormsManagement;
import org.come.until.Music;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class ChineseZodiac<PERSON>ouse  implements MouseListener {
    public ChineseZodiacPanel zodiacPanel;
    public SpriteBtn btn;
    public int i;
    public ChineseZodiacMouse(int i, SpriteBtn btn, ChineseZodiacPanel zodiacPanel) {
		super();
		this.i = i;
		this.btn = btn;
		this.zodiacPanel=zodiacPanel;
	}
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // 点击
        if (i==10){
            if (btn.isChoose()) {
			if (btn.getZhen() != 2) {//选择类 选中
				btn.btn(2);
			}
		} else {
			btn.btn(2);
		}
            return;
        }
            if (btn.isChoose()) {
                  if (btn.getZhen() != 2) {// 选择类 选中
                      btn.btn(2);
                      btn.setXz(2);
                      Btn(i);
                  } else {
                      // 选择类 取消选中
                      btn.btn(0);
                  }
              } else {
                btn.btn(2);
            }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        if (i==10){
            if (btn.isChoose()) {btn.btn(0);}
               if (!FormsManagement.getframe(135).isVisible()) {
                // 设置存款
                FormsManagement.showForm(135);
                Music.addyinxiao("开关窗口.mp3");
            } else {
                FormsManagement.HideForm(135);
                Music.addyinxiao("关闭窗口.mp3");
            }
            return;
        }
	if (btn.isChoose()) {// 非选择类生效
			btn.btn(1);
            zodiacPanel.PlaySprite();
            zodiacPanel.setDynamicBtn(i);
		}

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (i==10){
             if (btn.getZhen() != 2) {btn.btn(1);}
            return;
        }
          for (int j = 0; j < zodiacPanel.getDynamicBtn().length; j++) {
                    if (zodiacPanel.getDynamicBtn()[j].getXz()==2){
                        btn.btn(1);
                        break;
                    }
                }
        if (btn.getZhen() != 2) {
			btn.btn(1);
		}
        MsgJframe.getJframe().getJapnel().StarChart(i);
    }

    @Override
    public void mouseExited(MouseEvent e) {
         if (i==10){
           if (btn.getZhen() != 2) {btn.btn(0);}
            return;
        }
    FormsManagement.HideForm(46);
        if (btn.getXz()!=2) {
            btn.btn(0);
        }
    }
    public void	Btn(int i) {
         for (int j = 0; j < zodiacPanel.getDynamicBtn().length; j++) {
                    if (i!=j){
                        zodiacPanel.getDynamicBtn()[j].btn(0);
                        zodiacPanel.getDynamicBtn()[j].setXz(0);
                    }
                }
    }
}
