package jxy2.petView;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.PetSkillsJframe;
import org.come.Frame.WuLingFrame;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.bean.PetOperationDTO;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class PetSkillQiLingBtn extends MoBanBtn {
    // 技能ID
    private int  index;
    private PetSkillQiLingJPanel  qiLingJPanel;
    // 父面板引用
    public PetSkillQiLingBtn(String iconpath, int type, String text, int index, String prowpt,PetSkillQiLingJPanel qiLingJPanel) {
        // 初始化按钮
        super(iconpath, type, 0, UIUtils.COLOR_BTNTEXT, prowpt);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.index = index;
        this.qiLingJPanel = qiLingJPanel;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    
    @Override
    public void chooseyes() {

    }
    @Override
    public void chooseno() {

    }
@Override
public void nochoose(MouseEvent e) {
    //删除技能自动设置关闭
    PetSkillsJpanel petSkillsJpanel = PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel();
    RoleSummoning pet = UserMessUntil.getChosePetMes();
    if (pet.getQiling()==0){
        ZhuFrame.getZhuJpanel().addPrompt("#R召唤兽还未启灵，无法开启悟灵");
        return;
    }
    //对比启灵次数，几次就能开启几个技能
    int sum = getOpenSkill(pet);

    PetOperationDTO dto = new PetOperationDTO();
    dto.setPetId(pet.getSid());
    dto.setOperationType("PET_OPEN_WL");
    String skid = PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillID();
    if (skid == null) return;
    dto.setEventType(skid);
    if (getText().equals("悟灵")){
        WuLingFrame.getWuLingFrame().getWuLingJPanel().JDataPetSkill(pet);
        Util.StopFrame(147);
        return;
    }
    // 切换按钮状态
    boolean isOpening = getText().equals("开启");
    if (sum>=pet.getQiling()&&isOpening){
        ZhuFrame.getZhuJpanel().addPrompt("#R召唤兽启灵次数不够开启悟灵。");
        return;
    }
    setText(isOpening ? "关闭" : "开启");
    petSkillsJpanel.getLabStar()[petSkillsJpanel.getPetskillNO()].setVisible(isOpening);
    dto.setOpenClose(isOpening ? "1" : "0");
    // 发送更新
    SendRoleAndRolesummingUntil.sendRoleSumming(dto);

    // 刷新技能信息
    Skill skill = UserMessUntil.getSkillId(skid);
    if (skill != null) {
        qiLingJPanel.skillmsg(skill, pet, PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel());
    }
}
    // 额外的辅助方法：检查技能是否开启
    public static boolean isSkillLingjieEnabled(RoleSummoning pet, String skid) {
        if (pet == null || pet.getLingjie() == null) return false;
        String[] vs = pet.getLingjie().split("&");
        for (String skillInfo : vs) {
            String[] parts = skillInfo.split("=");
            if (parts[0].equals(skid)) {
                // 返回技能的开启状态
                return parts.length > 2 && "1".equals(parts[2]);
            }
        }
        return false;
    }

    // 获取技能当前灵阶等级
    public static int getSkillLingjieLevel(RoleSummoning pet, String skid) {
        if (pet == null || pet.getLingjie() == null) return 0;
        String[] vs = pet.getLingjie().split("&");
        for (String skillInfo : vs) {
            String[] parts = skillInfo.split("=");
            if (parts[0].equals(skid)) {
                // 返回技能的当前等级
                return parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            }
        }
        return 0;
    }
    // 获取当前开启的技能数量
    public static int getOpenSkill(RoleSummoning pet) {
        if (pet == null || pet.getLingjie() == null) return 0;
        String[] vs = pet.getLingjie().split("&");
        int openSkillCount = 0;
        for (String skillInfo : vs) {
            String[] parts = skillInfo.split("=");
            if (parts.length >= 3 && parts[2].equals("1")) {
                openSkillCount++;
            }
        }
        return openSkillCount;
    }


}
