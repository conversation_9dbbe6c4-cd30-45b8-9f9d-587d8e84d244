package com.tool.role;

import java.math.BigDecimal;

public class RoleUpGrade {
	//升级抗性表  魔族天生"命中率=5","狂暴率=5","致命率=5"
	public String[] kx={
			"抗混乱","抗封印","抗昏睡","抗中毒",
			"抗风","抗火","抗雷","抗水",
			"命中率","狂暴率","致命率","物理吸收",
			"抗遗忘","抗三尸","抗鬼火","抗浩然正气","躲闪率"};  
	public String[] ren={"0=4=1","1=4=1","2=4=1","3=4=1"};
	public String[] mo={"0=8=1","1=8=1","2=8=1","3=8=1",
			            "4=12=1","5=12=1","6=12=1","7=12=1",
			            "8=20=1","11=8=1"};
	public String[] xian={"4=4=1","5=4=1","6=4=1","7=4=1"};
	public String[] gui={"15=1=-100","16=4=1",
			"0=6=1","1=6=1","2=6=1","3=6=1",
			"14=6=1","12=6=1","13=6=120",
			"4=8=-1","5=8=-1","6=8=-1","7=8=-1","8=12=1"};
	public String[] LONG={"11=6=1","0=6=1","1=6=1","2=6=1","3=6=1","12=6=1","8=20=1"};
   private static  RoleUpGrade gradekx;
   public static RoleUpGrade getGradeKX(){
	   if (gradekx==null) gradekx=new RoleUpGrade();
	      return gradekx;  
   }
	
	public  String upGrade(int lvl,BigDecimal raceid){
		if (lvl>162) lvl=162;
		if (raceid.intValue()==10001) 
			return getren(lvl);
		else if (raceid.intValue()==10002) 
			return getmo(lvl);
		else if (raceid.intValue()==10003) 
			return getxian(lvl);
		else if (raceid.intValue()==10004) 
			return getgui(lvl);
		else if (raceid.intValue()==10005) 
			return getlong(lvl);
		return getren(lvl);
	}
	/**人族等级抗性**/
	public String getren(double lvl){
		StringBuilder builder=new StringBuilder();
		for (int i = 0; i < ren.length; i++) {
			if (i!=0) builder.append("|");
			String[] v=ren[i].split("=");
			builder.append(kx[Integer.parseInt(v[0])]);
			builder.append("=");
			builder.append(lvl/Integer.parseInt(v[1])*Integer.parseInt(v[2]));}
		return builder.toString();
	}
	/**魔族等级抗性**/
	public String getmo(double lvl){
		StringBuilder builder=new StringBuilder();
		for (int i = 0; i < mo.length; i++) {
			if (i!=0) builder.append("|");
			String[] v=mo[i].split("=");
			builder.append(kx[Integer.parseInt(v[0])]);
			builder.append("=");
			builder.append(lvl/Integer.parseInt(v[1])*Integer.parseInt(v[2]));}
		builder.append("|命中率=5|狂暴率=5|致命率=5");
		return builder.toString();
	}
	/**仙族等级抗性**/
	public String getxian(double lvl){
		StringBuilder builder=new StringBuilder();
		for (int i = 0; i < xian.length; i++) {
			if (i!=0) builder.append("|");
			String[] v=xian[i].split("=");
			builder.append(kx[Integer.parseInt(v[0])]);
			builder.append("=");
			builder.append(lvl/Integer.parseInt(v[1])*Integer.parseInt(v[2]));}
		return builder.toString();
	}
	/**鬼族等级抗性**/	
	public String getgui(double lvl){
		StringBuilder builder=new StringBuilder();
		for (int i = 0; i < gui.length; i++) {
			if (i!=0) builder.append("|");
			String[] v=gui[i].split("=");
			builder.append(kx[Integer.parseInt(v[0])]);
			builder.append("=");
			builder.append(lvl/Integer.parseInt(v[1])*Integer.parseInt(v[2]));}
		return builder.toString();
	}	
	/**龙族等级抗性**/	
	public String getlong(double lvl){
		StringBuilder builder=new StringBuilder();
		for (int i = 0; i < LONG.length; i++) {
			if (i!=0) builder.append("|");
			String[] v=LONG[i].split("=");
			builder.append(kx[Integer.parseInt(v[0])]);
			builder.append("=");
			builder.append(lvl/Integer.parseInt(v[1])*Integer.parseInt(v[2]));}
		builder.append("|命中率=5|狂暴率=5|致命率=5");
		return builder.toString();
	}	

}
