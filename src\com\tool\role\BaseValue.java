package com.tool.role;

import java.math.BigDecimal;

/**
 * 基础值
 * <AUTHOR>
 *
 */
public class BaseValue {
		//获取人物的属性值                            种族id  点数   等级是（1转15 等级就是15） 类型 0hp 1mp 2ap 3sp 4定力
		public static int getRoleValue(BigDecimal raceid,int P,int lvl,int type){
			double G=getValue(raceid,1,type);
			if (type>=4) {
				return (int) (G*P);
			}
			double base=getValue(raceid,0,type);
			if (G>2) {G=0.01;}
			if (base>400) {G=10;}		
			int E=(100-lvl)/5;
			int LEPG=(int) ((lvl+E)*P*G);
			if (type==0||type==1) {
				return (int) (LEPG+base);
			}else if (type==2) {
				return (int)(LEPG/5+base);
			}else {
				return (int)((10+P)*G);
			}	
		}	
		//人魔仙鬼
		public static final double bases[]={360,300,70,8,
	                                  330,210,80,10,
	                                  300,390,60,10,
	                                  270,350,80,9,
	                                  300,240,80,10};
	    public static final double basevs[]={
	    1.2 ,1   ,0.95,0.8 ,1.05,
	    1.1 ,0.6 ,1.3 ,1   ,1   ,
	    1   ,1.4 ,0.7 ,1   ,0.9 ,
	    1.25,1.05,0.95,0.75,0.9 ,
	    0.9 ,0.7 ,1.3 ,1   ,1   };
	//获取值 根据种族  类型0表示值1表示成长 zhi=0血1蓝2ap3sp
	public static double getValue(BigDecimal raceid,int type,int zhi){
		if (type == 0) {
			return bases[zhi + getratio(raceid) * 4];
		} else {
			return basevs[zhi + getratio(raceid) * 5];
		}   
	}
	/**获取种族系数**/
	public static int getratio(BigDecimal raceid){
		if (raceid.intValue() == 10001) {return 0;}
		else if (raceid.intValue() == 10002) {return 1;}
		else if (raceid.intValue() == 10003) {return 2;}
		else if (raceid.intValue() == 10004) {return 3;}
		else if (raceid.intValue() == 10005) {return 4;}
		return 0;
	}
	
	/**人法 遗忘上限*/
	public static double Upper(String key,BigDecimal raceid){
		if (raceid.intValue()==10003||raceid.intValue()==10002) {
			return 110;
		}else if (raceid.intValue()==10001) {
			return 140;
		}else if (raceid.intValue()==10004) {
			if (key.equals("抗遗忘")) {return 140;}
			else {return 120;}
		}else if (raceid.intValue()==10005) {
			return 120;
		}
		return 110;
	}
	
	/**true gx贡献值 zftrue表示主  false表示副*/
	public static long getBangQuality(BigDecimal gx,boolean zf){
		if (gx==null)gx=new BigDecimal(0);
		long x=(long) getCubeRoot(gx.longValue()/500);
		if (x<30) {
			return zf?x:x/2;	
		}else {
			return zf?30:15;	
		}
	}
	public  static double getCubeRoot(long input){  
	    if(input==0)  
	        return 0;  
	    double x0,x1;  
	    x0=input;  
	    x1=(2*x0/3)+(input/(x0*x0*3));//利用迭代法求解  
	    while(Math.abs(x1-x0)>0.000001){  
	        x0=x1;  
	        x1=(2*x0/3)+(input/(x0*x0*3));  
	    }  
	    return  x1;  
	}
	/**获取经脉当前等级所需经验*/
    public static int getMeridiansExp(int lvl) { return lvl*100; }
    /**获取经脉等级总需经验*/
    public static int getMeridiansTotalExp(int lvl) { return (lvl+1)*lvl*50; }
    /**获取经脉等级*/
    public static int getMeridiansLvl(int value) {
        int lvl=0;
        do { lvl++;value-=getMeridiansExp(lvl); } while (value>=0);
        return lvl;
    }
}
