package com.tool.tcpimg;

import java.awt.Graphics;

import org.come.bean.PathPoint;
import org.come.until.Util;

import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
import come.tool.Fighting.sidian;

/**足迹特效显示*/
public class TxZJ {

	private String skin;
	private int jg;//添加的间隔
	private sidian[] sidians;//点1
//	private sidian sidian2;//点2 x时间  y方向  z 等价于位置x  w等价于位置y
//	private sidian sidian3;//点2 x时间  y方向  z 等价于位置x  w等价于位置y
	public TxZJ(String skin) {
		super();
		this.skin = skin;
		sidians=new sidian[5];
		for (int i = 0; i < sidians.length; i++) {
			sidians[i]=new sidian(-1, 0, 0, 0);
		}
		jg=30;
	}
	public void draw(Graphics g,long time){
		time*=1.5;
		Sprite sprite = null;
		for (int i = 0; i < sidians.length; i++) {
			if (i==0) {sprite = SpriteFactory.Prepare(GetTcpPath.getMouseTcp(skin));}
			if (sidians[i].getX()!=-1) {
				sidians[i].setX((int)(sidians[i].getX()+time));
				if (sprite!=null) {
					if (sidians[i].getX()<=sprite.getTime()) {
						PathPoint pathPoint = Util.mapmodel.path(sidians[i].getZ(), sidians[i].getW());
						if (pathPoint!=null) {
							sprite.updateToTime(sidians[i].getX(),sidians[i].getY());
							sprite.draw(g, pathPoint.getX(), pathPoint.getY());	
						}
					}else {
						sidians[i].setX(-1);
					}	
				}	
			}		
		}
	}
	//添加足迹
	public void addZJ(int x,int y,int dir){
		if (Util.mapmodel.path(x,y)==null) {
			return;
		}
		for (int i = 0; i < sidians.length; i++) {
			if (sidians[i].getX()==-1) {
				jg--;
				if (jg<0) {
					jg=30;
					sidians[i].setsidian(0, dir, x, y);
				}
				return;
			}
		}
	}
	public void setSkin(String skin) {
		this.skin = skin;
	}
	
}
