package jxy2.npk;

import java.io.File;
import java.util.List;

/**
 * NPK文件对象
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkFile {
    private File file;
    private NpkHeader header;
    private List<NpkEntry> entries;

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public NpkHeader getHeader() {
        return header;
    }

    public void setHeader(NpkHeader header) {
        this.header = header;
    }

    public List<NpkEntry> getEntries() {
        return entries;
    }

    public void setEntries(List<NpkEntry> entries) {
        this.entries = entries;
    }
}
