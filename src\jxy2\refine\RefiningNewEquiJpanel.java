package jxy2.refine;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.RefiningUtil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

/**
* 装备精炼面板
* <AUTHOR>
* @date 2024/7/28 下午8:08
*/
public class RefiningNewEquiJpanel extends JPanel {
    private RefNewBtn refNewBtn;
    public RefNewBtn left,//左移
            right,
            ody;//右移
    private int per = 1;
    private JLabel[] equipment = new JLabel[3];
    public BigDecimal money;
    public Goodstable[] goods = new Goodstable[3];
    private JLabel preview;
    public RefiningNewEquiJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        money = new BigDecimal(100000);
        refNewBtn = new RefNewBtn(ImgConstants.tz45, -1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "精炼", 0, this,"");
        refNewBtn.btnchange(2);
        refNewBtn.setBounds(138, 48, 66, 24);
        add(refNewBtn);
        for (int i = 0; i < equipment.length; i++) {
            equipment[i] = new JLabel();
            equipment[i].addMouseListener(new RefineMouse(this,24 + i));
            if (i==0){
                equipment[i].setBounds(280, 139, 50, 50);
            }else {
                equipment[i].setBounds(i==1?216:349, 95+120, 50, 50);
            }

            this.add(equipment[i]);
        }


        ody = new RefNewBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 1, this,"");
        ody.setBounds(280,374,59,26);
        this.add(ody);
        left = new RefNewBtn(ImgConstants.tz89, -1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "", 2, this,"");
        left.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz91,17,21,"defaut.wdf"));
        this.add(left);
        right =new RefNewBtn(ImgConstants.tz90, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "", 3, this,"");
        add(right);
        left.setBounds(259, 293, 17, 21);
        right.setBounds(259+75, 293, 17, 21);
        preview = new JLabel();
        preview.setBounds(280, 274, 50, 50);
        preview.addMouseListener(new RefineMouse(this,4));
        this.add(preview);

    }
    /**点击物品*/
    public void ClickGood(Goodstable good, int path) {
        if (path < 24) {
            boolean a = true;
            if (good != null && good.getType() >= 8900&&good.getType() <= 8901) {
                int i = 1;
                if (good.getType() == 8901||good.getType() == 7511) {
                    i = 1;
                } else if (good.getType() == 8900) {
                    i = 2;
                }
                if (goods[i] == null) {
                    change(good, i);
                    a = false;
                }
            }
            if (a) {
                    if (goods[0] == null) {
                        change(good, 0);
                        preview.setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
                    }

            }
        }else {
            change(null, path - 24);
        }
        String v = RefiningUtil.detection(goods, 4);
        if (!ody.getText().equals(v)) {
            ody.setText(v);
        }
    }

    /** 切换指定位置 */
    public void change(Goodstable good, int path) {
        goods[path] = good;
        if (good != null) {
            if (good.getRefinelv()!=null){
                if (good.getRefinelv()>0){
                    setPer(good.getRefinelv());
                }else {
                    setPer(1);
                }
            }
            equipment[path].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        } else {
            equipment[path].setIcon(null);
            if (path==0){
                preview.setIcon(null);
                setPer(1);
            }


        }
    }

    /**清除组件显示*/
    public void clear() {
        for (int i = 0; i < goods.length; i++) {
            goods[i] = null;
            equipment[i].setIcon(null);
        }
        preview.setIcon(null);
        ody.setText("?");
    }

    public String[] name = new String[]{"装备","石头","祝福符"};
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.good_2, 280 , 139, 50, 50, 1);
        Juitil.ImngBack(g, Juitil.good_2, 216, 95+120, 50, 50, 1);
        Juitil.ImngBack(g, Juitil.good_2, 349, 95+120, 50, 50, 1);
        for (int i = 0; i < name.length; i++) {
            int x,y;
            if (i==0){
                x = 290;
                y = 118+40;
            }else if (i==1){
                x = 226;
                y = 199+35;
            }else {
                x = 353;
                y = 199+35;
            }
            if (equipment[i].getIcon()==null) {
                Juitil.TextBackground(g, name[i], 13, x, y, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }
        }

        Juitil.TextBackground(g, "花费金钱",13, 280,329,UIUtils.COLOR_value,UIUtils.FZCY_HY13);
        Juitil.ImngBack(g, Juitil.tz26, 249, 344, 121, 23, 1);
        Util.drawPrice(g,money, 280, 359);
        Juitil.ImngBack(g, Juitil.good_2, 280, 274, 50, 50, 1);
    }

    public RefNewBtn getRefNewBtn() {
        return refNewBtn;
    }

    public void setRefNewBtn(RefNewBtn refNewBtn) {
        this.refNewBtn = refNewBtn;
    }

    public RefNewBtn getLeft() {
        return left;
    }

    public void setLeft(RefNewBtn left) {
        this.left = left;
    }

    public RefNewBtn getRight() {
        return right;
    }

    public void setRight(RefNewBtn right) {
        this.right = right;
    }

    public RefNewBtn getOdy() {
        return ody;
    }

    public void setOdy(RefNewBtn ody) {
        this.ody = ody;
    }
    public int getPer() {
        return per;
    }

    public void setPer(int per) {
        this.per = per;
    }

    public JLabel getPreview() {
        return preview;
    }

    public void setPreview(JLabel preview) {
        this.preview = preview;
    }

    public JLabel[] getEquipment() {
        return equipment;
    }

    public void setEquipment(JLabel[] equipment) {
        this.equipment = equipment;
    }
}
