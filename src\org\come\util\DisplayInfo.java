package org.come.util;

import java.awt.*;

/**
 * 显示器信息检测工具
 * 自动检测显示器刷新率并设置合适的游戏帧率
 */
public class DisplayInfo {
    
    private static DisplayInfo instance;
    private int detectedRefreshRate = 60; // 默认60Hz
    private int targetFPS = 60; // 目标FPS
    private boolean vsyncEnabled = true; // 是否启用垂直同步
    
    private DisplayInfo() {
        detectDisplayInfo();
    }
    
    public static DisplayInfo getInstance() {
        if (instance == null) {
            instance = new DisplayInfo();
        }
        return instance;
    }
    
    /**
     * 检测显示器信息
     */
    private void detectDisplayInfo() {
        try {
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
            GraphicsDevice[] devices = ge.getScreenDevices();
            
            System.out.println("[显示器检测] 检测到 " + devices.length + " 个显示器");
            
            // 获取主显示器信息
            GraphicsDevice primaryDevice = ge.getDefaultScreenDevice();
            DisplayMode currentMode = primaryDevice.getDisplayMode();
            
            detectedRefreshRate = currentMode.getRefreshRate();
            
            System.out.println("[显示器检测] 主显示器信息:");
            System.out.println("  分辨率: " + currentMode.getWidth() + "x" + currentMode.getHeight());
            System.out.println("  刷新率: " + detectedRefreshRate + "Hz");
            System.out.println("  色彩深度: " + currentMode.getBitDepth() + "位");
            
            // 设置目标FPS
            calculateTargetFPS();
            
            // 检测所有显示器
            for (int i = 0; i < devices.length; i++) {
                DisplayMode mode = devices[i].getDisplayMode();
                System.out.println("  显示器" + (i+1) + ": " + 
                    mode.getWidth() + "x" + mode.getHeight() + 
                    " @" + mode.getRefreshRate() + "Hz");
            }
            
        } catch (Exception e) {
            System.err.println("[显示器检测] 检测失败，使用默认60Hz: " + e.getMessage());
            detectedRefreshRate = 60;
            targetFPS = 60;
        }
    }
    
    /**
     * 计算目标FPS
     */
    private void calculateTargetFPS() {
        if (detectedRefreshRate == DisplayMode.REFRESH_RATE_UNKNOWN) {
            System.out.println("[显示器检测] 无法检测刷新率，使用默认60FPS");
            targetFPS = 60;
            return;
        }
        
        // 根据刷新率设置FPS策略
        if (vsyncEnabled) {
            // 垂直同步模式：FPS = 刷新率
            targetFPS = detectedRefreshRate;
        } else {
            // 非垂直同步模式：FPS稍低于刷新率，避免撕裂
            if (detectedRefreshRate >= 144) {
                targetFPS = 120; // 144Hz显示器用120FPS
            } else if (detectedRefreshRate >= 120) {
                targetFPS = 100; // 120Hz显示器用100FPS
            } else if (detectedRefreshRate >= 75) {
                targetFPS = 60;  // 75Hz显示器用60FPS
            } else {
                targetFPS = detectedRefreshRate; // 60Hz及以下直接匹配
            }
        }
        
        System.out.println("[显示器检测] 设置目标FPS: " + targetFPS + 
            " (刷新率: " + detectedRefreshRate + "Hz, 垂直同步: " + vsyncEnabled + ")");
    }
    
    /**
     * 获取检测到的刷新率
     */
    public int getDetectedRefreshRate() {
        return detectedRefreshRate;
    }
    
    /**
     * 获取目标FPS
     */
    public int getTargetFPS() {
        return targetFPS;
    }
    
    /**
     * 设置垂直同步
     */
    public void setVsyncEnabled(boolean enabled) {
        this.vsyncEnabled = enabled;
        calculateTargetFPS();
        System.out.println("[显示器检测] 垂直同步" + (enabled ? "启用" : "禁用") + 
            "，目标FPS调整为: " + targetFPS);
    }
    
    /**
     * 是否启用垂直同步
     */
    public boolean isVsyncEnabled() {
        return vsyncEnabled;
    }
    
    /**
     * 手动设置目标FPS
     */
    public void setTargetFPS(int fps) {
        if (fps > 0 && fps <= 300) {
            this.targetFPS = fps;
            System.out.println("[显示器检测] 手动设置目标FPS: " + fps);
        } else {
            System.err.println("[显示器检测] 无效的FPS值: " + fps);
        }
    }
    
    /**
     * 获取帧时间（纳秒）
     */
    public long getFrameTimeNanos() {
        return (long) ((1000.0 / targetFPS) * 1000000);
    }
    
    /**
     * 获取显示器信息摘要
     */
    public String getDisplaySummary() {
        return String.format("显示器: %dHz, 目标FPS: %d, 垂直同步: %s", 
            detectedRefreshRate, targetFPS, vsyncEnabled ? "开" : "关");
    }
    
    /**
     * 重新检测显示器（用于显示器设置改变后）
     */
    public void refresh() {
        System.out.println("[显示器检测] 重新检测显示器信息...");
        detectDisplayInfo();
    }
}
