package jxy2.zodiac;

import com.tool.btn.MoBanBtn;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;
import org.come.until.Music;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class StarChartBtn extends MoBanBtn {
      public StarChartJPanel starChartJPanel;
      public TStarSoulsJPanel tStarSoulsJPanel;
      public RStarSoulsJPanel rStarSoulsJPanel;
      public int index;
      public StarChartBtn(String iconpath, int type, Color[] colors, Font font, String text, int index, StarChartJPanel starChartJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.index = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.starChartJPanel = starChartJPanel;
    }

      public StarChartBtn(String iconpath, int type, Color[] colors, Font font, String text, int index, TStarSoulsJPanel tStarSoulsJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.index = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.tStarSoulsJPanel = tStarSoulsJPanel;
    }
      public StarChartBtn(String iconpath, int type, Color[] colors, Font font, String text, int index, RStarSoulsJPanel rStarSoulsJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.index = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.rStarSoulsJPanel = rStarSoulsJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {//365_480
        switch (index) {
            case 1:
                if (!FormsManagement.getframe(136).isVisible()) {
                    // 设置存款
                    FormsManagement.showForm(136);
                    Music.addyinxiao("开关窗口.mp3");
                } else {
                    FormsManagement.HideForm(136);
                    Music.addyinxiao("关闭窗口.mp3");
                }
                break;
            case 2:
                if (!FormsManagement.getframe(137).isVisible()) {
                    // 设置存款
                    FormsManagement.showForm(137);
                    Music.addyinxiao("开关窗口.mp3");
                } else {
                    FormsManagement.HideForm(137);
                    Music.addyinxiao("关闭窗口.mp3");
                }
                break;
            case 3:
                if (!tStarSoulsJPanel.getGoodstableMap().isEmpty()) {
                    ConversionBean conversionBean =  new ConversionBean();
                    conversionBean.setGoodstableMap(tStarSoulsJPanel.getGoodstableMap());
                    // 发送给服务器
                    String msg = Agreement.getAgreement().ActivateSSAgreement("Z"+GsonUtil.getGsonUtil().getgson().toJson(conversionBean));
                    // 向服务器发送信息
                    SendMessageUntil.toServer(msg);
                    //转换结束直接清空MAP
                    tStarSoulsJPanel.getGoodstableMap().clear();
                }
                break;
            case 4:
                if (!rStarSoulsJPanel.getGoodsrgid().isEmpty()) {
                    if (rStarSoulsJPanel.getGoodsrgid().size()<2){
                        ZhuFrame.getZhuJpanel().addPrompt2("请集齐星魂再来操作！！");
                        return;
                    }
                    BigDecimal moenty = rStarSoulsJPanel.money.multiply(rStarSoulsJPanel.firstLv);
                    OptionsJframe.getOptionsJframe().getOptionsJpanel().
                            showBox(TiShiUtil.reLingHun, rStarSoulsJPanel, "#W重炼星魂将消耗#M" + moenty + "#W两金钱，会重新获得一颗星魂，是否继续？");
                }
                break;
            default:
//                System.out.println("?");
                break;
        }
    }
}
