package jxy2.eqcon;

import com.tool.btn.BaptizeBtn;
import com.tool.image.Creepsskin;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.tcpimg.ChatBox;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.GoodsMsgJpanel;
import org.come.bean.ImgZoom;
import org.come.bean.LoginResult;
import org.come.bean.RoleSuitBean;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.starcard.JpanelStarCardMain;
import org.come.summonequip.JpanelSummonEquipMain;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;

public class EqComparisonJPanel extends JPanel {
    private int gwidth;// 面板的宽度
    private int gheight;// 面板的高度
    private ChatBox box,box2;
    private JLabel labgoodsimg,labgoobackomg;// 物品的图像
    public static String vs;
    private boolean jy = false,fjsx = false,score = false;
    public int durabilityHeight = -1,qelvl=0; // 用来记录耐久所在高度
    public static ImgZoom imgZoom = CutButtonImage.newcutsPng(ImgConstants.TxtImg, 14, 7, true,"defaut.wdf");
    public EqComparisonJPanel(int gwidth, int gheight) {
        // 键盘监听
        this.gwidth = gwidth;
        this.gheight = gheight;
        this.setPreferredSize(new Dimension(gwidth, gheight));
        this.setLayout(null);
        this.setBackground(new Color(0, 0, 0, 0));
        labgoodsimg = new JLabel();
        labgoodsimg.setBounds(18, 37, 60, 60);
        this.add(labgoodsimg);

        labgoobackomg = new JLabel();
        labgoobackomg.setIcon(CutButtonImage.getWdfPng(ImgConstants.goodsbackimg,-1,-1,"defaut.wdf"));
        labgoobackomg.setBounds(13, 32, 70, 70);
        this.add(labgoobackomg);

        box = new ChatBox();
        box2 = new ChatBox();
    }

    private int ips = 0;
    @Override
    public void paint(Graphics g) {
        imgZoom.draw(g);
        Graphics g22 = g.create(15, 110, 310, gheight - 10);
        box.paint(g22);
        Graphics g222 = g.create(95, 30, 310, gheight - 10);
        box2.paint(g222);

        g22.dispose();
        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setFont(UIUtils.FZCY_HY14);
        g2d.setColor(Color.red);
        g2d.drawString("当前装备", 15, gheight - 25);
        g2d.drawString("当前装备", 15, gheight - 25);
        g.drawImage(Juitil.tz19.getImage(), 5, gheight - 40, 265, 3, null);
        g.drawImage(Juitil.tz19.getImage(), 5, 110, 265, 3, null);
        if (fjsx) {
            g.drawImage(Juitil.tz19.getImage(), 5, durabilityHeight + 130, 265, 3, null);
        }
        if (score) {
            g.drawImage(Juitil.tz19.getImage(), 5, 140, 265, 3, null);
        }


        if (vs != null) {
            Juitil.TextBackground(g, vs,16, 15,8,Juitil.EquiColour(qelvl),UIUtils.FZCY_HY16,UIUtils.COLOR_NAME7);
        }
        super.paint(g);
    }
    /**颜色替换*/
    public String upColor(StringBuffer buffer,String color1,String color2){
        if (color1==null||!color2.equals(color1)) {
            buffer.append(color2);
            return color2;
        }else {
            return color1;
        }
    }

    /**展示人类装备*/
    public void showEquipment(String value, long type, Integer qhv,Goodstable goodstable){
//        力量要求 根骨要求 敏捷要求 灵性要求  等级要求 等级 阶数 性别要求 装备角色  不是基础属性
        LoginResult result = RoleData.getRoleData().getLoginResult();
        if (value==null||value.equals("")) {return;}
        String color=null;
        StringBuffer buffer=new StringBuffer();
        String[] v = value.split("\\|");
        String xz = null;
        buffer.append("#c66ccbb装备评分："+(goodstable.getRefinelv()!=null?1000+goodstable.getRefinelv()*888:1000));
        buffer.append("#r #r");
        score = true;
        int totalHeight = 0; // 用来记录总高度
        for (int i = 0; i < v.length; i++) {
            if(v[i].startsWith(BaptizeBtn.Extras[0])){//炼化属性
                color = upColor(buffer, color, "#c66ccbb");
                buffer.append("#r耐久 4000/4000");
                xz = showEw0(buffer, v[i], type);//炼化属性-==
                buffer.append("#r #r");
            }else if(v[i].startsWith(BaptizeBtn.Extras[1])){//"炼器属性"
                showEw1(buffer, v[i]);
            }else if(v[i].startsWith(BaptizeBtn.Extras[2])){//"神兵属性"
                showEw2(buffer, v[i]);
            }else if(v[i].startsWith(BaptizeBtn.Extras[3])){//"套装属性"
                showEw3(buffer, v[i]);
            }else if(v[i].startsWith(BaptizeBtn.Extras[4])){//"宝石属性"
                showEw4(buffer, v[i]);
            }else if(v[i].startsWith(BaptizeBtn.Extras[5])){//"觉醒技"
                showEw5(buffer, v[i]);
            }else if(v[i].startsWith(BaptizeBtn.Extras[6])){//"五行属性"
                showEw6(buffer, v[i], xz);
            }else{
                fjsx =false;
                String[] zhi = v[i].split("=");
                if(buffer.length()!=0){buffer.append("#r");}
                if (zhi[0].equals("装备角色") || zhi[0].equals("等级") || zhi[0].equals("最高携带等级") || zhi[0].equals("装备等级") || zhi[0].equals("阶数") || zhi[0].equals("等级要求")  || zhi[0].equals("套装品质")) {
                    color = upColor(buffer, color, "#cFFFFFF");
                    buffer.append("");
                } else if (zhi[0].equals("力量要求") || zhi[0].equals("灵性要求") || zhi[0].equals("根骨要求") || zhi[0].equals("敏捷要求")) {
                    int dex = Integer.parseInt(zhi[1]);
                    color = upColor(buffer, color, Bone(zhi[0])<dex?"#cff0000":"#cddee55");
                    buffer.append(Bone(zhi[0])>=dex?"":zhi[0]+"："+zhi[1]);
                } else if (zhi[0].equals("装备部位")) {
                    String part = AccessSuitMsgUntil.returnPartsName(zhi[1]);
                    if (part != null) {zhi[1] = part;}
                    color = upColor(buffer, color, "#cFFFFFF");
                    buffer.append("【装备部位】");
                    buffer.append(zhi[1]);
                } else if (zhi[0].equals("性别要求") || zhi[0].equals("性别")) {
                    color = upColor(buffer, color, result.getSex().equals("男")&&zhi[1].equals("1")?"#cddee55":"#cff0000");
                    buffer.append(result.getSex().equals("男")&&zhi[1].equals("1")||result.getSex().equals("女")&&zhi[1].equals("0")?"":"性别要求：");
                    buffer.append(result.getSex().equals("男")&&zhi[1].equals("1")||result.getSex().equals("女")&&zhi[1].equals("0")?"":zhi[1].equals("1")?"男":zhi[1].equals("0")?"女":"全部性别");
//                    totalHeight+=20;
                } else if (zhi[0].equals("耐久")) {
                    color = upColor(buffer, color, "#c66ccbb");
                    buffer.append("耐久:");
                    buffer.append(zhi[1].split(",")[0]);
                    durabilityHeight = totalHeight; // 记录耐久所在的高度
                } else {
                    if (zhi.length > 1) {
                        color = upColor(buffer, color, "#c66ccbb");
                        buffer.append(zhi[0]);
                        buffer.append(" ");
                        buffer.append(GoodsMsgJpanel.zffh(zhi[1]));
                        buffer.append(GoodsMsgJpanel.tianjia(zhi[0]));
                        if(qhv != null && qhv>0){
                            color = upColor(buffer, color, "#G");
                            buffer.append(" ");
                            buffer.append("(");
                            buffer.append("+");
                            buffer.append(String.format("%.1f", RoleProperty.getQHGemXS(qhv)/100*Double.parseDouble(zhi[1])));
                            buffer.append(")");
                        }
                    }
                }
            }
            // 更新总高度，根据实际情况调整
            totalHeight += 16; // 假设每一行文字高度为20，可根据实际情况调整
        }
        box.addText(buffer.toString(), 235, UIUtils.FZCY_HY14);
    }
    private int Bone(String zhi ) {
        LoginResult result = RoleData.getRoleData().getLoginResult();
        switch (zhi){
            case "力量要求":
                return result.getPower();
            case "灵性要求":
                return result.getSpir();
            case "根骨要求":
                return result.getBone();
            case "敏捷要求":
                return result.getSpeed();
            default:
                return 0;
        }
    }
    /**炼器属性*/
    public void showEw1(StringBuffer buffer, String value){
        String[] v = value.split("&");
        if(buffer.length()!=0){buffer.append("#r");}
        String color = upColor(buffer, null, "#W");
        buffer.append("【炼器】");
        color = upColor(buffer, color, "#c00EAFF");
        buffer.append("开光次数 ");
        buffer.append(v[1]);
        for (int i = 2; i < v.length; i++) {
            String[] vs = v[i].split("=");
            if(buffer.length()!=0){buffer.append("#r");}
            color = upColor(buffer, color, "#c00CD00");
            buffer.append(vs[0]);
            buffer.append(" ");
            buffer.append(GoodsMsgJpanel.zffh(vs[1]));
            buffer.append(GoodsMsgJpanel.tianjia(vs[0]));
        }
    }
    /**神兵属性*/
    public void showEw2(StringBuffer buffer, String value){
        String color = null;
        String[] v = value.split("&");
        for (int i = 1; i < v.length; i++) {
            String[] vs = v[i].split("=");
            if(buffer.length()!=0){buffer.append("#r");}
            color = upColor(buffer, color, "#cBE9786");
            buffer.append(vs[0]);
            buffer.append(" ");
            buffer.append(GoodsMsgJpanel.zffh(vs[1]));
            buffer.append(GoodsMsgJpanel.tianjia(vs[0]));
        }
    }
    /**套装属性*/
    public void showEw3(StringBuffer buffer, String value){
        if(buffer.length()!=0){buffer.append("#r");}
        String color = upColor(buffer, null, "#c00ffff");
        buffer.append("【套装属性】");
        String[] v = value.split("&");
        for (int i = 4; i < v.length; i++) {
            String[] vs = v[i].split("=");
            if(buffer.length()!=0){buffer.append("#r");}
            color = upColor(buffer, color, "#c00CD00");
            buffer.append(vs[0]);
            buffer.append(" ");
            buffer.append(GoodsMsgJpanel.zffh(vs[1]));
            buffer.append(GoodsMsgJpanel.tianjia(vs[0]));
        }
        if(buffer.length()!=0){buffer.append("#r");}
        color = upColor(buffer, null, "#c00ffff");
        buffer.append("【套装品质:");
        buffer.append(v[3]);
        buffer.append("】");
        int suitId = Integer.parseInt(v[1]);
        RoleSuitBean suit = UserMessUntil.getSuit(suitId);
        if (suit != null) {
            int sum = RoleProperty.getRoleProperty().getSuitSum(v[1]);
            String[] vs = suit.getHaveSkill().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                String[] vss = vs[i].split("-");
                int maxsum = Integer.parseInt(vss[0]);
                Skill skill = UserMessUntil.getSkillId(vss[1]);
                if (skill != null) {
                    if(buffer.length()!=0){buffer.append("#r");}
                    if (sum >= maxsum) {
                        color = upColor(buffer, null, "#c00CD00");
                        buffer.append("[");
                        buffer.append(maxsum);
                    } else {
                        color = upColor(buffer, null, "#c807876");
                        buffer.append("[");
                        buffer.append(sum);
                    }
                    buffer.append("/");
                    buffer.append(maxsum);
                    buffer.append("]");
                    buffer.append(skill.getSkillname());
                }
            }
        }
    }

    /**宝石属性*/
    public void showEw4(StringBuffer buffer, String value){
        if(buffer.length()!=0){buffer.append("#r");}
        String color = upColor(buffer, null, "#c00ffff");
        buffer.append("【宝石镶嵌】");
        String[] v = value.split("&");
        for (int i = 1; i < v.length; i++) {
            Goodstable good = GoodsListFromServerUntil.fushis.get(new BigDecimal(v[i]));
            if (good != null) {
                if(buffer.length()!=0){buffer.append("#r");}
                color = upColor(buffer, null, "#cEA5700");
                buffer.append(good.getGoodsname());
                String[] bs = good.getValue().split("\\|");
                buffer.append(" ");
                buffer.append(bs[0].split("=")[1]);
                buffer.append("级 ");
                bs = bs[1].split("=");
                buffer.append(bs[0]);
                buffer.append(" ");
                buffer.append(GoodsMsgJpanel.zffh(bs[1]) + GoodsMsgJpanel.tianjia(bs[1]));
            }
        }
    }

    /**觉醒技展示处理*/
    public void showEw5(StringBuffer buffer,String value) {
        String[] split = value.split("&");
        Skill skill = UserMessUntil.getSkillId(split[1]);
        if (buffer.length()!=0) {buffer.append("#r");}//觉醒技
        long lvl = JpanelSummonEquipMain.expChangeLevel(Long.parseLong(split[3]));
        String color=upColor(buffer, null, "#cFFFF00");
        buffer.append("【觉醒技】 ");
        buffer.append(skill.getSkillname());
        buffer.append("(");
        buffer.append(split[2]);
        buffer.append(")");
        if (buffer.length()!=0) {buffer.append("#r");}//觉醒技等级
        color = upColor(buffer, color, "#cFFFFFF");
        buffer.append("【觉醒技等级】 ");
        buffer.append(lvl);
        if (buffer.length()!=0) {buffer.append("#r");}//类型
        color = upColor(buffer, color, "#cFFFFFF");
        buffer.append("【类型】 通用");
        if (buffer.length()!=0) {buffer.append("#r");}//技能介绍
        color = upColor(buffer, color, "#c00FF00");
        buffer.append(SummonSkillRemark(skill.getRemark(), skill, split[2], lvl + ""));
        if (buffer.length()!=0) {buffer.append("#r");}//简介
        color = upColor(buffer, color, "#cC5C583");
        buffer.append("铃、环、甲觉醒三合一，觉醒技方可生效");

    }

    public static String SummonSkillRemark(String remark, Skill skill, String sld, String lvl) {
        remark = remark.replace("{公式一}", "#R" + (int) Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))) + "#G");
        remark = remark.replace("{公式二}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.8")) + "#G");
        remark = remark.replace("{公式三}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.7")) + "#G");
        remark = remark.replace("{公式四}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.3")) + "#G");
        remark = remark.replace("{公式五}", "#R" + (int) Arith.sub(Double.parseDouble("100"), Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl)))) + "#G");
        remark = remark.replace("{公式六}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.25")) + "#G");
        remark = remark.replace("{公式七}", "#R" + (int) Arith.add(Double.parseDouble("100"), Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl)))) + "#G");
        remark = remark.replace("{公式八}", "#R" + (int) Arith.add(Double.parseDouble("1"), Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.07"))) + "#G");
        remark = remark.replace("{公式九}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.5")) + "#G");
        remark = remark.replace("{公式十}", "#R" + (int) Arith.mul(Arith.mul(Arith.div(Arith.mul(Double.parseDouble(skill.getValue()), Double.parseDouble(sld)), 5, 0), Math.sqrt(Double.parseDouble(lvl))), Double.parseDouble("0.05")) + "#G");
        return remark;
    }
    /**五行属性展示处理*/
    public void showEw6(StringBuffer buffer,String value,String xz) {
        if (buffer.length()!=0) {buffer.append("#r");}
        String color=upColor(buffer, null, "#cFFFFFF");
        buffer.append("【五行】");
        String[] v = value.split("&");
        String[] starArray = xz!=null?xz.split("="):null;
        double num = 0;
        for (int i = 1; i < v.length; i++) {
            String[] vs = v[i].split("=");
            if (buffer.length()!=0) {buffer.append("#r");}
            color=upColor(buffer, color, "#cFFFF00");
            buffer.append(vs[0]);
            buffer.append(" ");
            buffer.append(vs[1]);
            buffer.append("/100");
            if (starArray!=null) {
                num += JpanelStarCardMain.fiveElementRestrainCreate(starArray[2], vs[0], vs[1]);
            }
        }
        if (starArray!=null) {
            if (buffer.length()!=0) {buffer.append("#r");}
            color=upColor(buffer, color, "#c4ADEDD");
            buffer.append("五行加成星阵之力 ");
            buffer.append(String.format("%.1f", num));
            buffer.append("%");
            if (buffer.length()!=0) {buffer.append("#r");}
            buffer.append("【星阵】");
            if (buffer.length()!=0) {buffer.append("#r");}
            buffer.append(starArray[1]);
            buffer.append("(");
            buffer.append(starArray[2]);
            buffer.append(")");
            if (isStarArrayName(starArray[1])) {
                buffer.append("#r#cFFFFFF赤帝宫 #c00FF00"+starArray[3]);
                buffer.append("#r#cFFFFFF青帝宫 #c00FF00"+starArray[4]);
                buffer.append("#r#cFFFFFF黄帝宫 #c00FF00"+starArray[5]);
                buffer.append("#r#cFFFFFF白帝宫 #c00FF00"+starArray[6]);
                buffer.append("#r#cFFFFFF黑帝宫 #c00FF00"+starArray[7]);
                color=null;
            }
            buffer.append("#r#c00FF00"+getStarArrayAttribute(starArray[1]));
        }else {
            buffer.append("#r#c4ADEDD无星阵，五行暂不生效");
        }
    }

    /** 判断是否是四神兽 */
    public boolean isStarArrayName(String name) {
        if (name.equals("朱雀") || name.equals("青龙") || name.equals("白虎") || name.equals("玄武"))
            return true;
        return false;
    }
    /** 根据星阵名称判断星阵的属性 */
    public String getStarArrayAttribute(String name) {
        if (name.equals("朱雀"))
            return "瑕疵:略微减少全队冰混睡忘抗性";
        else if (name.equals("青龙"))
            return "瑕疵:略微减少全队仙法抗性";
        else if (name.equals("白虎"))
            return "瑕疵:略微减少全队鬼火、三尸虫抗性";
        else if (name.equals("玄武"))
            return "瑕疵:略微减少全队震慑抗性";
        else if (name.equals("金牛"))
            return "本方所有人物、召唤兽对敌方造成的物理伤害有一定的加成";
        else if (name.equals("火猿"))
            return "若对方灵宝对本方任意单位造成伤害，抵抗一定程度伤害；每2回合可生效一次";
        else if (name.equals("赤马"))
            return "本方所有人物、召唤兽获得一定经验加成";
        else if (name.equals("黄鹤"))
            return "本方所有人物增加冰混睡忘抗性";
        else
            return "本方所有人物、召唤兽的仙法、鬼火、震慑有一定加成";
    }

    /**炼化属性展示处理*/
    public String showEw0(StringBuffer buffer,String value,long type) {
        String xz=null;
        String color=null;
        boolean is=Goodtype.GodEquipment_God(type)||Goodtype.GodEquipment_xian(type);
        String[] v = value.split("&");
        for (int i = 1; i < v.length; i++) {
            if (v[i].startsWith("星阵属性")) {xz=v[i];continue;}
            String[] vs = v[i].split("=");
            if (buffer.length()!=0) {buffer.append("#r");}
            if (vs[0].equals("特技")) {
                color=upColor(buffer, color, "#c868090");
                buffer.append("特技");
                for (int j = 1; j < vs.length; j++) {
                    Skill skill = UserMessUntil.getSkillId(vs[j]);
                    if (skill != null) {
                        if (buffer.length()!=0) {buffer.append("#r");}
                        buffer.append(skill.getSkillname());
                        buffer.append(":");
                        buffer.append(skill.getRemark());
                    }
                }
            } else if (vs[0].endsWith("等级")) {
                color=upColor(buffer, color, "#c00CD00");
                buffer.append(vs[0]);
                buffer.append(" ");
                buffer.append(vs[1]);
                buffer.append("级");
            } else if (vs[0].equals("资质")) {
                color=upColor(buffer, color, "#cFFFFFF");
                buffer.append("【神通】");
                buffer.append(vs[0]);
                buffer.append(" ");
                buffer.append(vs[1]);
                buffer.append("/100");
            } else {
                color=upColor(buffer, color, is?"#c00EAFF":"#c00ff00");
                buffer.append(vs[0]);
                buffer.append(" ");
                buffer.append(GoodsMsgJpanel.zffh(vs[1]));
                buffer.append(GoodsMsgJpanel.tianjia(vs[0]));
            }
        }
        return xz;
    }

    /**展示物品*/
    public int showGood(Goodstable good){
        labgoodsimg.setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),60,60));
        if (good.getQht()!=null&&good.getQht()>0) {
            StringBuffer buffer=new StringBuffer();
            buffer.append("#Y");
            buffer.append(good.getGoodsname());
            buffer.append("#r");
            for (int i = 1; i < good.getQht(); i++) {
                if(good.getQhv()==null||i>good.getQhv()) buffer.append("#880");
                else buffer.append("#881");
            }
            box.removeAddText(buffer.toString(), 235,UIUtils.TEXT_COM_FONT);
        }else {
            box.removeAddText("", 235,UIUtils.TEXT_COM_FONT);
            qelvl=good.getRefinelv()==null?0:good.getRefinelv();
            vs =good.getRefinelv()==null?good.getGoodsname(): good.getGoodsname() + "(+" + good.getRefinelv() + ")";

        }
        GoddmesNew(good);
        if (good.getInstruction()!=null&&!good.getInstruction().equals("")&&Goodtype.CancelMsg(good.getType())) {
            box.addText(good.getInstruction(), 245, UIUtils.MSYH_HY13);//物品描述
        }
        if (!Goodtype.CancelMsg(good.getType())) {//展示属性
            if (Goodtype.EquipmentType(good.getType())!=-1) {//人物装备类
                showEquipment(good.getValue(), good.getType(),good.getRefinelv(),good);
            }
            ips = 30;
        }else {
            score = false;
            ips = 0;
        }


        if (box.getHeight()+ box2.getHeight() < 140) gheight = 160;
        else gheight = box.getHeight() + 30 + box2.getHeight()+ips;
        this.setPreferredSize(new Dimension(gwidth, gheight));
        imgZoom.setMiddlew(gwidth - 2 * imgZoom.getEdgew());
        imgZoom.setMiddleh(gheight - 2 * imgZoom.getEdgeh());
        return gheight;
    }

    /**新装备物品数据展示*/
    private void GoddmesNew(Goodstable good) {
        StringBuilder tx = new StringBuilder(Goodtype.isEquipment(good.getType()));
        StringBuffer buffer = new StringBuffer();
        String js = "无限制";
        String lve = "无限制";
        String yq = "无限制";
        String ys = "#c66ccbb";
        if (good.getValue()==null){
            buffer.append("#c66ccbb类型："+tx+"#r");
            buffer.append("#c66ccbb境界："+yq+"#r");
            buffer.append("#c66ccbb级别："+lve+"#r");
            buffer.append(ys+"种族："+js+"#r #r");
            box2.removeAddText(buffer.toString(),170,UIUtils.MSYH_HY13);
            return;
        }

        String[] v = good.getValue().split("\\|");
        for (int i = 0; i < v.length; i++) {
            String[] zhi = v[i].split("=");
            if (buffer.length() != 0) {buffer.append("#r");}
            if (zhi[0].equals("装备角色")){
                if (zhi[1].contains(Creepsskin.getLocalName(RoleData.getRoleData().getLoginResult().getSpecies_id().intValue()))){
                    js =Creepsskin.getLocalName(RoleData.getRoleData().getLoginResult().getSpecies_id().intValue());
                }else {
                    int endIndex = Math.min(zhi[1].length(), 11);
                    js =  zhi[1].substring(0, endIndex);
                    ys = "#cff0000";
                }
            }else if (zhi[0].equals("等级要求")){
                lve =zhi[1]+"级";
            }else if (zhi[0].equals("等级")&&Goodtype.OrdinaryEquipment(good.getType())){
                tx.insert(0, Util.number2Chinese(Integer.parseInt(zhi[1]))+"级");
            }else if (zhi[0].equals("阶数")&&Goodtype.GodEquipment_xian(good.getType())){
                tx.insert(0, Util.number2Chinese(Integer.parseInt(zhi[1]))+"阶·仙器");
            }
        }
        buffer.append("#c66ccbb类型："+tx+"#r");
        buffer.append("#c66ccbb境界："+yq+"#r");
        buffer.append("#c66ccbb级别："+lve+"#r");
        buffer.append(ys+"种族："+js+"#r #r");
        box2.removeAddText(buffer.toString(),170,UIUtils.FZCY_HY13);
    }
}
