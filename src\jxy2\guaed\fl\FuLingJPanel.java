package jxy2.guaed.fl;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class FuLingJPanel extends JPanel {
    private  FuLingCourdJPanel cardJPanel;
    public FuLingBtn[] vnavi = new FuLingBtn[2];//右侧导航
    private  String[] name = {"附灵","融炼"};
    public int type;
    public FuLingJPanel() {
        setPreferredSize(new Dimension(633, 507));
        setOpaque(false);
        setLayout(null);
        Juitil.addClosingButtonToPanel(this,141,633);
        cardJPanel = new FuLingCourdJPanel();
        cardJPanel.setBounds(0, 0, 633, 507);
        add(cardJPanel);
        for (int i = 0; i < vnavi.length; i++) {
            vnavi[i] = new FuLingBtn(ImgConstants.tz45, 1, UIUtils.COLOR_ZHUJPANEL, UIUtils.MSYH_HY14, name[i], i, this,"");
            vnavi[i].btnchange(i==0?2:0);
            vnavi[i].setBounds(56 + 66 * i, 35, 66, 24);
            this.add(vnavi[i]);
        }
    }
    public String titie = "附 灵";
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.JPanelNewShow(g,getWidth(),getHeight(),titie);
        g.drawImage(Juitil.tz19.getImage(), 50, 57, getWidth()-88, 2, null);
        g.drawImage(Juitil.tz319.getImage(), 19, 72, 592, 344, null);

    }

    public FuLingCourdJPanel getCardJPanel() {
        return cardJPanel;
    }

    public void setCardJPanel(FuLingCourdJPanel cardJPanel) {
        this.cardJPanel = cardJPanel;
    }

    public FuLingBtn[] getVnavi() {
        return vnavi;
    }

    public void setVnavi(FuLingBtn[] vnavi) {
        this.vnavi = vnavi;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
