package jxy2.refine;

import com.tool.btn.BaptizeBtn;
import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.SuitBaptizeJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SuitBaptizeJpanel;
import org.come.Jpanel.SynthesisJpanel;
import org.come.Jpanel.UpgradeJpanel;
import org.come.bean.JadeorGoodstableBean;
import org.come.bean.LoginResult;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* 套装合成
* <AUTHOR>
* @date 2024/7/27 上午6:57
*/
public class SuitBtn extends MoBanBtn {
    public EqsuitJpanel eqsuitJpanel;
    public int typeBtn;
    public SuitBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, EqsuitJpanel eqsuitJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.eqsuitJpanel = eqsuitJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        String s = eqsuitJpanel.detectionSuit();

        if (typeBtn==99){
            if (s.equals("合成")) {
                heCheng();
            }else if (s.equals("洗炼")) {
                xiLian();
            }else if (s.equals("升级")&&eqsuitJpanel.getMinType()==2) {
                tzShengJi();
            }else if (s.equals("升级")&&eqsuitJpanel.getMinType()==3) {
                yfShengJi();
            }else if (s.equals("拆解")||s.equals("转移")) {
                zhuanYi();
            }else if (s.equals("兑换")){
                duiHuan();
            }else if (s.equals("收录")){
                shouLu();
            }
        }else {
            eqsuitJpanel.setMinType(typeBtn);//接装装备炼化，0-4   5开始
            EquiButtonClick(typeBtn);
            eqsuitJpanel.clear();
        }

//        String text  = Juitil.TextInedx(refineJPanel.getMinType());
//        refineJPanel.getRichLabel().setTextSize(text,160);
    }


    public void EquiButtonClick(int clickedIndex) {
        if (clickedIndex==-1)return;
        eqsuitJpanel.getSuitBtn()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <eqsuitJpanel.getSuitBtn().length; i++) {
            if (i != clickedIndex) {
                eqsuitJpanel.getSuitBtn()[i].btnchange(0);
            }
        }
    }
    /** 合成 */
    public void heCheng() {
        // 获得合成需要消耗的金币
        BigDecimal big = AccessSuitMsgUntil.returnMoney(SynthesisJpanel.getGoodstableBean(), 1);
        if (big == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请准备要合成的装备和玉符。");
            return;
        }
        PartJade jade = SynthesisJpanel.getGoodstableBean().getPartJade();
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (big.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        Goodstable good = SynthesisJpanel.getGoodstableBean().getGoodstable();
        if (good.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(good)) {
            return;
        }
        // 判断 玉符和对应装备类型
        int type = Goodtype.EquipmentType(SynthesisJpanel.getGoodstableBean().getGoodstable().getType());
        if (jade.getPartId() == 11) {
            if (type != 10) {
                ZhuFrame.getZhuJpanel().addPrompt("装备类型和玉符不一致..");
                return;
            }
        } else if (type != jade.getPartId()) {
            ZhuFrame.getZhuJpanel().addPrompt("装备类型和玉符不一致..");
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        List<BigDecimal> goods = new ArrayList<>();
        goods.add(SynthesisJpanel.getGoodstableBean().getGoodstable().getRgid());
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(SynthesisJpanel.getGoodstableBean().getType(), 1);
        operBean.setType(0);
        operBean.setGoods(goods);
        operBean.setJade(jade2);

        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 删除玉符
        jade.deleteJade(SynthesisJpanel.getGoodstableBean().getType(), 1);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        eqsuitJpanel.twoEquipment[0].setIcon(null);
        eqsuitJpanel.goods[0] = null ;
        ZhuFrame.getZhuJpanel().addPrompt(
                "消耗了一个" + AccessSuitMsgUntil.returnJadeName(SynthesisJpanel.getGoodstableBean().getType()) + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了100W金币..");
    }


    /** 洗炼套装 */
    private void xiLian() {
        if (eqsuitJpanel.goods[0] == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要洗炼的套装..");
            return;
        }
        Goodstable good = eqsuitJpanel.goods[0];
        if (good.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(good)) {
            return;
        }
        SuitBaptizeJpanel suitBaptizeJpanel = SuitBaptizeJframe.getSuitBaptizeJframe().getBaptizeJpanel();
        suitBaptizeJpanel.getLabtz().setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        // 还原界面
        for (int i = 0; i < 4; i++) {
            suitBaptizeJpanel.getOldAttr()[i].setText("");
            suitBaptizeJpanel.getNewAttr()[i].setText("");
        }

        // 将原有的属性放上去
        List<String> attr = AccessSuitMsgUntil.getSuitAttr(AccessSuitMsgUntil.getExtra(good.getValue(), "套装属性"));
        if (attr != null) {
            int index = attr.size() >= 4 ? 4 : attr.size();
            for (int i = 0; i < index; i++) {
                suitBaptizeJpanel.getOldAttr()[i].setText(attr.get(i));
            }
        }
        // 还原界面
        suitBaptizeJpanel.getBaptizeBtn2().setBtn(-1);
        suitBaptizeJpanel.getBaptizeBtn3().setBtn(-1);
        suitBaptizeJpanel.getBaptizeBtn1().setText("开始洗炼");
        // 打开洗炼的小面板
        FormsManagement.upgradForm(74);
    }
    /** 套装升级 */
    public void tzShengJi() {
        // 获得套装升级需要消耗的金币
        BigDecimal big = AccessSuitMsgUntil.returnMoney(EqsuitJpanel.getGoodstableBean(), 2);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (big == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请准备要升级的装备和玉符。");
            return;
        }
        if (big.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        PartJade jade = EqsuitJpanel.getGoodstableBean().getPartJade();
        Goodstable goodstable = EqsuitJpanel.getGoodstableBean().getGoodstable();
        if (goodstable.getGoodlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
            return;
        }
        if (GoodsListFromServerUntil.isExist(goodstable)) {
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        List<BigDecimal> goods = new ArrayList<>();
        goods.add(goodstable.getRgid());
        operBean.setType(3);
        operBean.setGoods(goods);
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(EqsuitJpanel.getGoodstableBean().getType(), 1);
        operBean.setJade(jade2);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 改变该物品的品质
        // 替换属性
        String Extras = AccessSuitMsgUntil.getExtra(goodstable.getValue(), "套装属性");
        String[] ss = goodstable.getValue().split("\\|");
        String newEx = AccessSuitMsgUntil.returnnewEx(1, Extras);
        ss[0] = "套装品质=" + AccessSuitMsgUntil.returnnewEx(3, Extras);
        String value = BaptizeBtn.newExtra(ss, 3, newEx);
        goodstable.setValue(value);
        EqsuitJpanel.setGoodstable(goodstable);
        eqsuitJpanel.getLabtz2().setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),49,49));
        // 删除玉符
        jade.deleteJade(EqsuitJpanel.getGoodstableBean().getType(), 1);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        eqsuitJpanel.clear();
        ZhuFrame.getZhuJpanel().addPrompt(
                "消耗了一个" + AccessSuitMsgUntil.returnJadeName(UpgradeJpanel.getGoodstableBean().getType()) + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了1000W金币..");
    }

    /**
     * 玉符升级
     */
    public void yfShengJi() {
        if (EqsuitJpanel.getGoodstableBean().getPartJade() == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要升级的玉符..");
            return;
        }
        BigDecimal big = AccessSuitMsgUntil.returnJadeMoney(EqsuitJpanel.getGoodstableBean().getType());
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (loginResult.getGold().compareTo(big) < 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        PartJade jade = EqsuitJpanel.getGoodstableBean().getPartJade();
        int num = AccessSuitMsgUntil.returnJadeNum(EqsuitJpanel.getGoodstableBean().getType());// 升级所消耗的玉符数量
        // 判断所需的玉符是否足够
        if (num > jade.getJade(EqsuitJpanel.getGoodstableBean().getType())) {
            ZhuFrame.getZhuJpanel().addPrompt("你所需的玉符需要"+num+"个..");
            return;
        }

        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(4);
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(EqsuitJpanel.getGoodstableBean().getType(), num);
        operBean.setJade(jade2);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 删除玉符
        jade.deleteJade(EqsuitJpanel.getGoodstableBean().getType(), num);
        // 消耗金钱
        loginResult.setGold(loginResult.getGold().subtract(big));
        // 清空界面
        eqsuitJpanel.clear();
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + num + "个" + AccessSuitMsgUntil.returnJadeName(EqsuitJpanel.getGoodstableBean().getType())  + "玉符..");
        ZhuFrame.getZhuJpanel().addPrompt("消耗了500W金币..");
    }
    /**
     * 转移
     */
    public void zhuanYi() {
        if (EqsuitJpanel.getGoodstableBean().getGoodstable() == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要拆解的套装..");
            return;
        }
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (eqsuitJpanel.getWorkshopBtn().getText().equals("拆解")) {
            if (loginResult.getGold().compareTo(new BigDecimal(100000)) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                return;
            }
            Goodstable goodstable = EqsuitJpanel.getGoodstableBean().getGoodstable();
            if (goodstable.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (GoodsListFromServerUntil.isExist(goodstable)) {
                return;
            }
            // 扣金币
            loginResult.setGold(loginResult.getGold().subtract(new BigDecimal(100000)));
            List<BigDecimal> goods = new ArrayList<>();
            goods.add(goodstable.getRgid());
            SuitOperBean operBean = new SuitOperBean();
            operBean.setGoods(goods);
            operBean.setType(5);
            // 发送消息给服务器
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            // 清空界面
            eqsuitJpanel.clear();
            ZhuFrame.getZhuJpanel().addPrompt("消耗了10W金币..");

        } else if (eqsuitJpanel.getWorkshopBtn().getText().equals("转移")) {
            if (EqsuitJpanel.getGoodstable() == null) {
                ZhuFrame.getZhuJpanel().addPrompt("请选择你要转移属性的装备..");
                return;
            }
            if (loginResult.getGold().compareTo(new BigDecimal(10000000)) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                return;
            }
            long value = AccessSuitMsgUntil.getSxlxz(EqsuitJpanel.getGoodstableBean().getGoodstable().getValue());
            if (loginResult.getScoretype("灵修值").longValue() < value) {
                ZhuFrame.getZhuJpanel().addPrompt("灵修值不足..");
                return;
            }
            Goodstable good1 = EqsuitJpanel.getGoodstableBean().getGoodstable();
            Goodstable good2 = EqsuitJpanel.getGoodstable();

            if (good1.getGoodlock() == 1 || good2.getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (GoodsListFromServerUntil.isExist(good1) || GoodsListFromServerUntil.isExist(good2)) {
                return;
            }
            // 判断装备类型是否对应
            if (Goodtype.EquipmentType(good1.getType()) != Goodtype.EquipmentType(good2.getType())) {
                ZhuFrame.getZhuJpanel().addPrompt("装备类型不一致..");
                return;
            }
            // 扣金币
            loginResult.setGold(loginResult.getGold().subtract(new BigDecimal(10000000)));
            // 扣灵修值
            loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + value, 3));
            List<BigDecimal> goods = new ArrayList<>();
            goods.add(good1.getRgid());
            goods.add(good2.getRgid());
            SuitOperBean operBean = new SuitOperBean();
            operBean.setGoods(goods);
            operBean.setType(6);
            // 发送消息给服务器
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            // 清空界面
            eqsuitJpanel.clear();
            ZhuFrame.getZhuJpanel().addPrompt("消耗了1000W金币..");
            ZhuFrame.getZhuJpanel().addPrompt("消耗了" + value + "点灵修值..");
        }
    }

    /**
     * 兑换灵修值
     */
    public void duiHuan() {
        JadeorGoodstableBean bean = EqsuitJpanel.getGoodstableBean();
        String v = eqsuitJpanel.getTextNum().getText();
        int num = (v != null && !v.equals("")) ? Integer.parseInt(v) : 0;
        if (bean == null || (bean != null && bean.getType() == 0)) {
            ZhuFrame.getZhuJpanel().addPrompt2("请选择你要兑换的玉符或玄玉。");
            return;
        }
        if (num <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("请输入你要兑换的玉符或玄玉的数量。");
            return;
        }
        int val = 0;// 可以得到的灵修值
        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(7);
        if (bean.getType() == 1) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade1() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade1(num);
                operBean.setJade(jade);
                val = num;
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 2) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade2() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade2(num);
                operBean.setJade(jade);
                val = num;
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 3) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade3() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade3(num);
                operBean.setJade(jade);
                val = num * 2;
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 4) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade4() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade4(num);
                operBean.setJade(jade);
                val = num * 2;
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 5) {
            if (bean.getPartJade() != null && bean.getPartJade().getJade5() >= num) {
                PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
                jade.setJade5(num);
                operBean.setJade(jade);
                val = num * 3;
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的玉符数量不足。");
                return;
            }
        } else if (bean.getType() == 6) {
            if (bean.getGoodstable() != null && bean.getGoodstable().getUsetime() >= num) {
                Goodstable goodstable = bean.getGoodstable();
                if (goodstable.getGoodlock() == 1) {
                    ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                    return;
                }
                if (GoodsListFromServerUntil.isExist(goodstable)) {
                    return;
                }
                PartJade jade = new PartJade(-1, -1);
                jade.setJade1(num);
                List<BigDecimal> goods = new ArrayList<>();
                goods.add(bean.getGoodstable().getRgid());
                operBean.setGoods(goods);
                operBean.setJade(jade);
                val = num * 3;
                bean.getGoodstable().setUsetime(bean.getGoodstable().getUsetime() - num);
                if (bean.getGoodstable().getUsetime() <= 0) {
                    // 将这个物品删除
                    GoodsListFromServerUntil.Deletebiaoid(bean.getGoodstable().getRgid());
                }
            } else {// 数量不足
                ZhuFrame.getZhuJpanel().addPrompt2("你的九天玄玉数量不足。");
                return;
            }
        }
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        ZhuFrame.getZhuJpanel().addPrompt("获得了 " + val + " 点灵修值..");
        // 清空界面
        eqsuitJpanel.clear();
    }

    /**
     * 收录
     */
    public void shouLu() {
        JadeorGoodstableBean bean = EqsuitJpanel.getGoodstableBean();
        if (bean == null || (bean != null && bean.getPartJade() == null)) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要收录的玉符..");
            return;
        }
        // //这个套装所收录的部件数量
        int num = AccessSuitMsgUntil.getCollNum(eqsuitJpanel.partJade.getSuitid());
        // 所需灵修值
        BigDecimal sxlxz = new BigDecimal(50);
        // 所需金钱
        BigDecimal money = new BigDecimal((num + 1) * 1000000);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (sxlxz.compareTo(loginResult.getScoretype("灵修值")) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("灵修值点数不足，快去获取灵修值吧..");
            return;
        }
        if (money.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        // 判断这个部件是否可收录
        if (RoleData.getRoleData().getPackRecord()
                .isCollect(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId()) != null) {
            ZhuFrame.getZhuJpanel().addPrompt("已到达收录上限..");
            return;
        }
        SuitOperBean operBean = new SuitOperBean();
        PartJade jade = new PartJade(bean.getPartJade().getSuitid(), bean.getPartJade().getPartId());
        jade.setJade(bean.getType(), 1);
        operBean.setJade(jade);
        operBean.setType(8);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);

        // 扣除灵修值
        loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + sxlxz, 3));
        // 扣除金钱
        loginResult.setGold(loginResult.getGold().subtract(money));
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + sxlxz + "点灵修值       扣除了" + money + "金币..");
        // 清空界面
        eqsuitJpanel.clear();
    }
}
