package come.tool.FightingEffect;

import com.tool.PanelDisplay.PetPanelShow;
import com.tool.role.RoleData;
import com.tool.tcp.GetTcpPath;
import come.tool.Fighting.*;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.RoleSummoning;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.Article;
import org.come.until.UserMessUntil;

public class Pet implements Effect{

	@Override
	public StateProgress analysisAction(FightingState State, int path) {
		// TODO Auto-generated method stub
		if (State.getStartState().equals("召回")) {
			FightingMixDeal.depath(State.getCamp(), State.getMan());
			FightingMixDeal.buffUtil.CS(State.getBuff(), FightingMixDeal.camp);
			return null;
		}
		FightingManData bb=State.getFightingManData();
		int myman=FightingMixDeal.myman()+5;
		if (bb.getCamp()==FightingMixDeal.camp&&bb.getMan()==myman) {
			RoleSummoning pet=Article.bb(bb.getId());
			if (pet!=null) {
				UserMessUntil.setChosePetMes(pet);
				//将宠物头像框设置为有头像
				ZhuJpanel.setLabpetimg("0x6B" + pet.getSummoningskin());
				//绘制召唤兽 3条
				Article.souxie(pet);
				PetPanelShow.ShowMesForJpanel();
				if (!State.getStartState().equals("闪现")) {
					String mes = Agreement.getAgreement().rolechangeAgreement("P"+pet.getSid().toString());
					SendMessageUntil.toServer(mes);
					// 设置自身携带的宠物id
					RoleData.getRoleData().getLoginResult().setSummoning_id(pet.getSid());
//					TestPetJpanel.showStar();
				}
			}
		}
		
		FightingMixDeal.depath(bb.getCamp(), bb.getMan());
		Fightingimage fightingimage=new Fightingimage(bb,FightingMixDeal.camp);
		FightingMixDeal.CurrentData.add(fightingimage);
		path=FightingMixDeal.CurrentData.size()-1;
		StateProgress progress=EffectType.getProgress(State, path);
		if (State.getStartState().equals("闪现")) {
			SkillSpell skillSpell=new SkillSpell();
			skillSpell=new SkillSpell(GetTcpPath.getSkillTcp("1806"),fightingimage.getX(),fightingimage.getY());
			progress.setSpell(skillSpell);
		}
		
		return progress;
	}

}
