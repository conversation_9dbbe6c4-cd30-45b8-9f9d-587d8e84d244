package org.come.util;

import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * DDS (DirectDraw Surface) 图片读取器
 * 支持 DXT1、DXT3、DXT5 格式的解压缩
 * 
 * DDS 是 DirectX 纹理压缩格式，可以显著减少纹理内存消耗
 */
public class DDSReader {
    
    // DDS 文件头标识
    private static final int DDS_MAGIC = 0x20534444; // "DDS "
    
    // DDS 头部大小
    private static final int DDS_HEADER_SIZE = 124;
    
    // DXT 格式标识
    private static final int FOURCC_DXT1 = 0x31545844; // "DXT1"
    private static final int FOURCC_DXT3 = 0x33545844; // "DXT3"
    private static final int FOURCC_DXT5 = 0x35545844; // "DXT5"
    
    // DDS 头部标志
    private static final int DDSD_CAPS = 0x1;
    private static final int DDSD_HEIGHT = 0x2;
    private static final int DDSD_WIDTH = 0x4;
    private static final int DDSD_PITCH = 0x8;
    private static final int DDSD_PIXELFORMAT = 0x1000;
    private static final int DDSD_MIPMAPCOUNT = 0x20000;
    private static final int DDSD_LINEARSIZE = 0x80000;
    private static final int DDSD_DEPTH = 0x800000;
    
    /**
     * DDS 文件头结构
     */
    public static class DDSHeader {
        public int size;
        public int flags;
        public int height;
        public int width;
        public int pitchOrLinearSize;
        public int depth;
        public int mipMapCount;
        public int[] reserved1 = new int[11];
        public DDSPixelFormat pixelFormat;
        public int caps;
        public int caps2;
        public int caps3;
        public int caps4;
        public int reserved2;
    }
    
    /**
     * DDS 像素格式结构
     */
    public static class DDSPixelFormat {
        public int size;
        public int flags;
        public int fourCC;
        public int rgbBitCount;
        public int rBitMask;
        public int gBitMask;
        public int bBitMask;
        public int aBitMask;
    }
    
    /**
     * 从文件读取 DDS 图像
     */
    public static BufferedImage readDDS(String filePath) throws IOException {
        return readDDS(new File(filePath));
    }
    
    /**
     * 从文件读取 DDS 图像
     */
    public static BufferedImage readDDS(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return readDDS(fis);
        }
    }
    
    /**
     * 从输入流读取 DDS 图像
     */
    public static BufferedImage readDDS(InputStream inputStream) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        
        byte[] data = baos.toByteArray();
        ByteBuffer bb = ByteBuffer.wrap(data);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        
        return readDDS(bb);
    }
    
    /**
     * 从字节缓冲区读取 DDS 图像
     */
    public static BufferedImage readDDS(ByteBuffer buffer) throws IOException {
        // 验证 DDS 魔数
        int magic = buffer.getInt();
        if (magic != DDS_MAGIC) {
            throw new IOException("不是有效的 DDS 文件格式");
        }
        
        // 读取 DDS 头部
        DDSHeader header = readHeader(buffer);
        
        // 验证头部
        if (header.size != DDS_HEADER_SIZE) {
            throw new IOException("无效的 DDS 头部大小");
        }
        
        // 检查支持的格式
        int fourCC = header.pixelFormat.fourCC;
        if (fourCC != FOURCC_DXT1 && fourCC != FOURCC_DXT3 && fourCC != FOURCC_DXT5) {
            throw new IOException("不支持的 DDS 格式: " + fourCCToString(fourCC));
        }
        
        // 读取图像数据
        int width = header.width;
        int height = header.height;
        
        // 计算数据大小
        int blockSize = (fourCC == FOURCC_DXT1) ? 8 : 16;
        int blocksWide = Math.max(1, (width + 3) / 4);
        int blocksHigh = Math.max(1, (height + 3) / 4);
        int dataSize = blocksWide * blocksHigh * blockSize;
        
        // 读取压缩数据
        byte[] compressedData = new byte[dataSize];
        buffer.get(compressedData);
        
        // 解压缩并创建 BufferedImage
        return decompressDXT(compressedData, width, height, fourCC);
    }
    
    /**
     * 读取 DDS 头部
     */
    private static DDSHeader readHeader(ByteBuffer buffer) {
        DDSHeader header = new DDSHeader();
        
        header.size = buffer.getInt();
        header.flags = buffer.getInt();
        header.height = buffer.getInt();
        header.width = buffer.getInt();
        header.pitchOrLinearSize = buffer.getInt();
        header.depth = buffer.getInt();
        header.mipMapCount = buffer.getInt();
        
        // 跳过保留字段
        for (int i = 0; i < 11; i++) {
            header.reserved1[i] = buffer.getInt();
        }
        
        // 读取像素格式
        header.pixelFormat = new DDSPixelFormat();
        header.pixelFormat.size = buffer.getInt();
        header.pixelFormat.flags = buffer.getInt();
        header.pixelFormat.fourCC = buffer.getInt();
        header.pixelFormat.rgbBitCount = buffer.getInt();
        header.pixelFormat.rBitMask = buffer.getInt();
        header.pixelFormat.gBitMask = buffer.getInt();
        header.pixelFormat.bBitMask = buffer.getInt();
        header.pixelFormat.aBitMask = buffer.getInt();
        
        header.caps = buffer.getInt();
        header.caps2 = buffer.getInt();
        header.caps3 = buffer.getInt();
        header.caps4 = buffer.getInt();
        header.reserved2 = buffer.getInt();
        
        return header;
    }
    
    /**
     * 解压缩 DXT 格式数据
     */
    private static BufferedImage decompressDXT(byte[] data, int width, int height, int format) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        
        int blockSize = (format == FOURCC_DXT1) ? 8 : 16;
        int blocksWide = (width + 3) / 4;
        int blocksHigh = (height + 3) / 4;
        
        ByteBuffer buffer = ByteBuffer.wrap(data);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        
        for (int blockY = 0; blockY < blocksHigh; blockY++) {
            for (int blockX = 0; blockX < blocksWide; blockX++) {
                int[] blockColors = new int[16];
                
                if (format == FOURCC_DXT1) {
                    decompressDXT1Block(buffer, blockColors);
                } else if (format == FOURCC_DXT3) {
                    decompressDXT3Block(buffer, blockColors);
                } else if (format == FOURCC_DXT5) {
                    decompressDXT5Block(buffer, blockColors);
                }
                
                // 将块数据写入图像
                for (int y = 0; y < 4; y++) {
                    for (int x = 0; x < 4; x++) {
                        int pixelX = blockX * 4 + x;
                        int pixelY = blockY * 4 + y;
                        
                        if (pixelX < width && pixelY < height) {
                            image.setRGB(pixelX, pixelY, blockColors[y * 4 + x]);
                        }
                    }
                }
            }
        }
        
        return image;
    }
    
    /**
     * 将 FourCC 转换为字符串
     */
    private static String fourCCToString(int fourCC) {
        return new String(new char[] {
            (char)(fourCC & 0xFF),
            (char)((fourCC >> 8) & 0xFF),
            (char)((fourCC >> 16) & 0xFF),
            (char)((fourCC >> 24) & 0xFF)
        });
    }
    
    /**
     * 解压缩 DXT1 块
     */
    private static void decompressDXT1Block(ByteBuffer buffer, int[] blockColors) {
        int color0 = buffer.getShort() & 0xFFFF;
        int color1 = buffer.getShort() & 0xFFFF;
        int colorIndices = buffer.getInt();

        // 转换 RGB565 到 RGB888
        int[] colors = new int[4];
        colors[0] = rgb565ToArgb(color0, 255);
        colors[1] = rgb565ToArgb(color1, 255);

        if (color0 > color1) {
            // 4 色模式
            colors[2] = interpolateColor(colors[0], colors[1], 1, 2);
            colors[3] = interpolateColor(colors[0], colors[1], 2, 1);
        } else {
            // 3 色模式 + 透明
            colors[2] = interpolateColor(colors[0], colors[1], 1, 1);
            colors[3] = 0x00000000; // 透明
        }

        // 解码像素
        for (int i = 0; i < 16; i++) {
            int colorIndex = (colorIndices >> (i * 2)) & 0x3;
            blockColors[i] = colors[colorIndex];
        }
    }

    /**
     * 解压缩 DXT3 块
     */
    private static void decompressDXT3Block(ByteBuffer buffer, int[] blockColors) {
        // 读取 alpha 数据 (64 位)
        long alphaData = buffer.getLong();

        // 读取颜色数据
        int color0 = buffer.getShort() & 0xFFFF;
        int color1 = buffer.getShort() & 0xFFFF;
        int colorIndices = buffer.getInt();

        // 转换颜色
        int[] colors = new int[4];
        colors[0] = rgb565ToRgb(color0);
        colors[1] = rgb565ToRgb(color1);
        colors[2] = interpolateColor(colors[0], colors[1], 1, 2);
        colors[3] = interpolateColor(colors[0], colors[1], 2, 1);

        // 解码像素
        for (int i = 0; i < 16; i++) {
            int colorIndex = (colorIndices >> (i * 2)) & 0x3;
            int alpha = (int)((alphaData >> (i * 4)) & 0xF) * 17; // 4位扩展到8位
            blockColors[i] = (alpha << 24) | colors[colorIndex];
        }
    }

    /**
     * 解压缩 DXT5 块
     */
    private static void decompressDXT5Block(ByteBuffer buffer, int[] blockColors) {
        // 读取 alpha 值
        int alpha0 = buffer.get() & 0xFF;
        int alpha1 = buffer.get() & 0xFF;

        // 读取 alpha 索引 (48 位)
        long alphaIndices = 0;
        for (int i = 0; i < 6; i++) {
            alphaIndices |= ((long)(buffer.get() & 0xFF)) << (i * 8);
        }

        // 计算 alpha 调色板
        int[] alphas = new int[8];
        alphas[0] = alpha0;
        alphas[1] = alpha1;

        if (alpha0 > alpha1) {
            // 8 alpha 模式
            for (int i = 2; i < 8; i++) {
                alphas[i] = ((8 - i) * alpha0 + (i - 1) * alpha1) / 7;
            }
        } else {
            // 6 alpha 模式 + 0 和 255
            for (int i = 2; i < 6; i++) {
                alphas[i] = ((6 - i) * alpha0 + (i - 1) * alpha1) / 5;
            }
            alphas[6] = 0;
            alphas[7] = 255;
        }

        // 读取颜色数据
        int color0 = buffer.getShort() & 0xFFFF;
        int color1 = buffer.getShort() & 0xFFFF;
        int colorIndices = buffer.getInt();

        // 转换颜色
        int[] colors = new int[4];
        colors[0] = rgb565ToRgb(color0);
        colors[1] = rgb565ToRgb(color1);
        colors[2] = interpolateColor(colors[0], colors[1], 1, 2);
        colors[3] = interpolateColor(colors[0], colors[1], 2, 1);

        // 解码像素
        for (int i = 0; i < 16; i++) {
            int colorIndex = (colorIndices >> (i * 2)) & 0x3;
            int alphaIndex = (int)((alphaIndices >> (i * 3)) & 0x7);
            blockColors[i] = (alphas[alphaIndex] << 24) | colors[colorIndex];
        }
    }

    /**
     * 将 RGB565 转换为 ARGB
     */
    private static int rgb565ToArgb(int rgb565, int alpha) {
        int r = ((rgb565 >> 11) & 0x1F) * 255 / 31;
        int g = ((rgb565 >> 5) & 0x3F) * 255 / 63;
        int b = (rgb565 & 0x1F) * 255 / 31;
        return (alpha << 24) | (r << 16) | (g << 8) | b;
    }

    /**
     * 将 RGB565 转换为 RGB
     */
    private static int rgb565ToRgb(int rgb565) {
        int r = ((rgb565 >> 11) & 0x1F) * 255 / 31;
        int g = ((rgb565 >> 5) & 0x3F) * 255 / 63;
        int b = (rgb565 & 0x1F) * 255 / 31;
        return (r << 16) | (g << 8) | b;
    }

    /**
     * 颜色插值
     */
    private static int interpolateColor(int color1, int color2, int weight1, int weight2) {
        int totalWeight = weight1 + weight2;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;

        int r = (r1 * weight1 + r2 * weight2) / totalWeight;
        int g = (g1 * weight1 + g2 * weight2) / totalWeight;
        int b = (b1 * weight1 + b2 * weight2) / totalWeight;

        return (r << 16) | (g << 8) | b;
    }

    /**
     * 检查 DDS 文件是否有效
     */
    public static boolean isDDSFile(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            byte[] magic = new byte[4];
            if (fis.read(magic) == 4) {
                ByteBuffer bb = ByteBuffer.wrap(magic);
                bb.order(ByteOrder.LITTLE_ENDIAN);
                return bb.getInt() == DDS_MAGIC;
            }
        } catch (IOException e) {
            // 忽略异常
        }
        return false;
    }
}
