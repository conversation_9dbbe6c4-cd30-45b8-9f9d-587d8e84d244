package come.tool.JDialog;

import com.tool.role.RoleData;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserData;

import java.math.BigDecimal;

public class PetxdsumDialog implements TiShiChuLi  {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            int num = RoleData.getRoleData().getPackRecord().getTxNum()+1;
            RoleData.getRoleData().getPackRecord().setTxNum(num);
            //发送消息给服务器
            GoodsListFromServerUntil.sendPackRecord(7, num+"");
            //扣除金币
            UserData.uptael(new BigDecimal(1000000 * num).longValue());

        }
    }
}
