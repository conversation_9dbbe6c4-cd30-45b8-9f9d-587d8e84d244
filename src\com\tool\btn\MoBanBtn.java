package com.tool.btn;

import com.tool.tcpimg.UIUtils;
import jxy2.chatv.ChatFrame;
import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.Jpanel.ZhuJpanel;
import org.come.test.Main;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.MessagrFlagUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public abstract class MoBanBtn extends JLabel implements MouseListener, BtnInterface {
    // 按钮图片
    protected ImageIcon[] icons;
    // 字体颜色
    protected Color[] colors;
    protected Color color;
    // 按钮是否可用 -1不可用按钮 0不为可用按钮且无图片 1表示非选择按钮 2为绑定选择按钮 3非绑定选择按钮
    protected int btn;
    // 当前图片按钮状态 0移入 1点击 2移出
    protected int zhen;
    protected int imgzhen,index;
    protected Integer V;// 左上角的小红点
    // 当前按钮状态0移入 1点击 2移出
    // 0移出 1移入 2点击
    protected int type;
    protected int num = -1;
    protected int isup = 0;
    protected int menu = 0;//区分导航与按钮

    protected String prompt;
    protected String ntext,sttext;
    public MoBanBtn(String iconpath, int type) {
        super();
        try {
            btn = type;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsWasBtn(iconpath,"sprite.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    public MoBanBtn(String iconpath, int type,String login) {
        super();
        try {
            btn = type;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,"login.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    public MoBanBtn(String iconpath,int index, int type) {
        super();
        try {
            btn = type;
            this.index = index;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,"defaut.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public MoBanBtn(String iconpath, int index, int type, ZhuJpanel zhuJpanel) {
        super();
        try {
            btn = type;
            this.index = index;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,Util.SwitchUI==1?"she.wdf":"redmu.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public MoBanBtn(ImageIcon[] iconpath, int index, int type) {
        super();
        try {
            btn = type;
            this.index = index;
            if (iconpath != null) {
                this.icons = iconpath;
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }




    public MoBanBtn(String iconpath, int index, int type,int ty,int num) {
        super();
        try {
            btn = type;
            this.index = index;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,Util.SwitchUI==1?"she.wdf":"redmu.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    public MoBanBtn(String iconpath, int type,int index,Color[] colors,String prompt) {
        super();
        try {
            btn = type;
            this.prompt = prompt;
            this.index = index;
            this.colors = colors;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,"defaut.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public MoBanBtn(String iconpath, int type,int index,Color[] colors,String prompt,String text) {
        super();
        try {
            btn = type;
            this.prompt = prompt;
            this.index = index;
            this.colors = colors;
            this.ntext = text;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsPngBtn(iconpath,Util.SwitchUI==1?"she.wdf":"redmu.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 按钮
     * @param iconpath 图像链接
     * @param type 标号
     * @param index 绑定的id
     * @param prompt 文本说明
     * @param text 标题
     */
    public MoBanBtn(String iconpath, int type,int index,String prompt,String text) {
        super();
            btn = type;
            this.prompt = prompt;
            this.index = index;
            this.ntext = text;
            if (iconpath != null) {
            this.icons = CutButtonImage.cutsPngBtn(iconpath,"defaut.wdf");
            btnchange(0);
            }
            this.addMouseListener(this);
    }

    public MoBanBtn(String iconpath, int type, Color[] colors) {
        super();
        try {
            btn = type;
            this.colors = colors;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsWasBtn(iconpath,"sprite.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    public MoBanBtn(String iconpath, int type, Color[] colors,String text) {
        super();
        try {
            btn = type;
            this.ntext = text;
            this.colors = colors;
            if (iconpath != null) {
                this.icons = CutButtonImage.cutsWasBtn(iconpath,"sprite.wdf");
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public MoBanBtn(String iconpath, int type, Color[] colors, int num, int isup) {
        super();
        try {
            btn = type;
            this.colors = colors;
            this.num = num;
            this.isup = isup;
            if (iconpath != null) {
                this.icons = CutButtonImage.cuts(iconpath);
                btnchange(0);
            }
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        // 绘制背景
        if (type == 2) {
            g.translate(1, 1);
            super.paintComponent(g);
            g.translate(-1, -1);
        } else {
            super.paintComponent(g);
        }

        // 绘制小红点
        if (V != null) {
            g.drawImage(CutButtonImage.getJT().getImage(), this.getWidth() - 10, 0, null);
        }
        if (sttext!=null){
            Color[] uicolors =  Util.SwitchUI == 0  ? UIUtils.COLOR_BTNTEXT :  Util.SwitchUI == 1?UIUtils.COLOR_ZHUJPANEL: UIUtils.COLOR_RED;
            g.setColor(uicolors[type]);
            if (type==2){
                g.drawString(sttext, 9, 14);
            }else {
                g.drawString(sttext, 8, 13);
            }
        }
        // 绘制文本
        if (ntext != null) {
            if (ntext.startsWith("L")) {
                Color color1 = Util.SwitchUI == 0 || Util.SwitchUI == 1 ? UIUtils.COLOR_White : UIUtils.COLOR_CL_RedMU;
                Juitil.CenterTextdrawing(g, ntext.split("\\|")[1], type == 2 ? 20 : 18, type == 2 ? 19 : 17, color1, UIUtils.TEXT_FONT);
            }else {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                // 设置字体
                Font font = Util.SwitchUI == 1 ? UIUtils.TEXT_HYJ17B : UIUtils.TEXT_FONT_17;
                g2d.setFont(menu==1||menu==2||menu==3?getFont():font);

                // 计算文本的实际宽度和高度
                FontMetrics fm = org.come.until.SafeFontMetrics.getFontMetrics(g2d);
                int textWidth = fm.stringWidth(ntext);
                int textHeight = fm.getAscent() - (textWidth > 50 ? 6 : 3);
                // 计算文本的居中坐标
                int centerX = ((getWidth() - textWidth) / 2) - (textWidth > 50 ? 1 : 0);
                int centerY = (getHeight() + textHeight) / 2+(menu==1?2:0);
                // 绘制黑色描边
                if (Util.SwitchUI==0) {
                    g2d.setColor(Color.black);
                }
//                if (this.type == 2) {
//                    g2d.drawString(ntext, centerX + 2, centerY + 2);
//                } else {
//                    int s = Util.SwitchUI == 0 ? 1 : 0;
//                    g2d.drawString(ntext, centerX + s, centerY + s);
//                }
                // 绘制白色文本
                if (color != null) {
                    g2d.setColor(color);
                } else {
                    Color uicolors = Util.SwitchUI == 0 ? UIUtils.COLOR_BTNTEXT[type] : Util.SwitchUI == 1 ? UIUtils.COLOR_SHE[type] : UIUtils.COLOR_RED[type];
                    if (colors!=null&&menu!=0&&menu!=1){
                        g2d.setColor(colors[type]);
                    }else {
                        g2d.setColor(btn == -1 ? Color.gray : uicolors);
                    }

                }
                if (this.type == 2) {
                    if (Util.SwitchUI == 0) {
                        g2d.drawString(ntext, centerX + 1, centerY + 1);
                    } else if (Util.SwitchUI == 2) {
                        if (ntext.equals("当前物品栏整理")||ntext.equals("全部物品栏整理")
                                ||ntext.equals("飞行驭器")||ntext.equals("坐骑")||
                                ntext.equals("组队操作")||ntext.equals("组队平台")){
                            g2d.setFont(UIUtils.TEXT_HYJ17B);
                            g2d.drawString(ntext, centerX+1, centerY+2);
                        }else {
                            g2d.drawString(ntext, centerX + 2, centerY + 1);
                            g2d.drawString(ntext, centerX + 1, centerY + 1);
                            g2d.drawString(ntext, centerX + 1, centerY + 1);
                        }

                    } else {
                        if (ntext.equals("当前物品栏整理")||ntext.equals("全部物品栏整理")
                                ||ntext.equals("飞行驭器")||ntext.equals("坐骑")||
                                ntext.equals("组队操作")||ntext.equals("组队平台")){
                            g2d.setColor(UIUtils.COLOR_CCDDDDFF);
                            g2d.drawString(ntext, centerX+1, centerY+2);
                        }else {
                            g2d.drawString(ntext, centerX + 1, centerY + 2);
//                            g2d.drawString(ntext, centerX + 1, centerY + 2);
                        }

                    }
                } else {
                    if (Util.SwitchUI == 0) {
                        g2d.drawString(ntext, centerX, centerY);
                    } else if (Util.SwitchUI == 2) {
                        if (menu==1||menu==2||menu==3){
                            g2d.drawString(ntext, centerX, centerY);
                        }else {
                            if (ntext.equals("当前物品栏整理")||ntext.equals("全部物品栏整理")
                                    ||ntext.equals("飞行驭器")||ntext.equals("坐骑")||
                                    ntext.equals("组队操作")||ntext.equals("组队平台")){
                                g2d.setFont(UIUtils.TEXT_HYJ17);
                                g2d.drawString(ntext, centerX+2, centerY);
                            }else {
                                g2d.drawString(ntext, centerX + 1, centerY);
                                g2d.drawString(ntext, centerX, centerY);
                                g2d.drawString(ntext, centerX, centerY);
                            }
                        }
                    } else {
                        if (menu==1||menu==2||menu==3){
                            g2d.drawString(ntext, centerX, centerY);
                        }else {
                            if (ntext.equals("当前物品栏整理")||ntext.equals("全部物品栏整理")
                                    ||ntext.equals("飞行驭器")||ntext.equals("坐骑")||
                                    ntext.equals("组队操作")||ntext.equals("组队平台")){
                                g2d.setColor(UIUtils.COLOR_White);
                                g2d.setFont(UIUtils.TEXT_HYJ17);
                                g2d.drawString(ntext, centerX+5, centerY);
                            }else {
                                g2d.drawString(ntext, centerX, centerY + 1);
//                                g2d.drawString(ntext, centerX, centerY + 1);
                            }

                        }

                    }
                }
                g2d.dispose();
            }
        }
    }
    public MoBanBtn(ImageIcon[] icon, int type) {
        super();
        try {
            btn = type;
            this.icons = icon;
            btnchange(0);
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public MoBanBtn(ImageIcon[] icon, int type, Color[] colors) {
        super();
        try {
            btn = type;
            this.icons = icon;
            this.colors = colors;
            btnchange(0);
            this.addMouseListener(this);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 按钮状态切换
     */
    public void btnchange(int change) {
        zhen = change;
        imgchange(zhen);
    }

    /**
     * 按钮图片切换
     */
    public void imgchange(int change) {
        imgzhen = change;
        if (btn == 0) {
            this.setIcon(null);
        } else {
            if (icons != null) {
                this.setIcon(icons[change]);
            }
            if (colors != null) {
                this.setForeground(colors[change]);
            }
        }
    }

    /**
     * 切换按钮类型
     */
    public void btntypechange(int change) {
        btn = change;
        type = 0;
        btnchange(1);
    }

    @Override
    public void mouseClicked(MouseEvent e) {
        // TODO Auto-generated method stub

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // TODO Auto-generated method stub
        dianji();
        Util.TIME_CHAT2 = 50;
    }

    /**
     * 模拟点击
     */
    public void dianji() {
        if (btn == -1 || btn == 0) {
            return;
        }
        if (btn == 2 || btn == 3) {
            if (zhen != 2) {
                // 选择类生效
                type = 2;
                btnchange(2);
                chooseyes();
            } else if (btn == 3) {
                // 选择类取消
                type = 1;
                btnchange(1);
                chooseno();
            }
        } else {
            type = 2;
            btnchange(2);
        }
    }


    @Override
    public void mouseReleased(MouseEvent e) {
        // TODO Auto-generated method stub
        shifang(e);
        ChatFrame.getChatFrame().setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
    }

    /**
     * 模拟释放
     */
    public void shifang(MouseEvent e) {
        if (btn == 1) {
            // 非选择类生效
            if (type != 0) {
            	type = 0;
                btnchange(type);
                if (Util.isM()) {return;}
            	nochoose(e);
            } else {
                btnchange(type);
            }
        }
        if (FormsManagement.getframe(46).isVisible()) {
            FormsManagement.HideForm(46);
        }
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        // TODO Auto-generated method stub
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (btn == -1) {
            return;
        }
        if (btn != 0 && zhen != 2) {
            btnchange(1);
        }
        type = 1;
        if (prompt!=null) {
            MsgJframe.getJframe().getJapnel().BtnPrompts(prompt);
        }


    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        // TODO Auto-generated method stub
        if (btn == -1) {
            return;
        }
        if ((btn != 0 || btn != -1) && zhen != 2) {btnchange(0);}
        type = 0;

        if (Main.frame.getLoginJpanel().getViewId()==5) {
            if (FormsManagement.getframe(46).isVisible()) {
                FormsManagement.HideForm(46);
            }
            if (FormsManagement.getframe(24).isVisible()) {
                FormsManagement.HideForm(24);
            }
            if (FormsManagement.getframe(628).isVisible()) {
                FormsManagement.HideForm(628);
            }
        }
    }

    @Override
    public abstract void chooseyes();

    @Override
    public abstract void chooseno();

    @Override
    public abstract void nochoose(MouseEvent e);

    public int getZhen() {
        return zhen;
    }

    public void setZhen(int zhen) {
        this.zhen = zhen;
    }

    public int getImgzhen() {
        return imgzhen;
    }

    public void setImgzhen(int imgzhen) {
        this.imgzhen = imgzhen;
    }

    public ImageIcon[] getIcons() {
        return icons;
    }

    public void setIcons(ImageIcon[] icons) {
        this.icons = icons;
        btnchange(0);
    }

    public int getBtn() {
        return btn;
    }

    public void setBtn(int btn) {
        this.btn = btn;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getIsup() {
        return isup;
    }

    public void setIsup(int isup) {
        this.isup = isup;
    }

    /** 开启小红点 */
    public void onOffRed(int v) {
        if (v == 0) {
            V = null;
        } else {
            V = v;
        }
    }

    public Color[] getColors() {
        return colors;
    }

    public void setColors(Color[] colors) {
        setForeground(colors[0]);
        this.colors = colors;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getNtext() {
        return ntext;
    }

    public void setNtext(String ntext) {
        this.ntext = ntext;
    }

    public String getSttext() {
        return sttext;
    }

    public void setSttext(String sttext) {
        this.sttext = sttext;
    }

    public Color getColor() {
        return color;
    }

    public void setColor(Color color) {
        this.color = color;
    }

    public int getMenu() {
        return menu;
    }

    public void setMenu(int menu) {
        this.menu = menu;
    }
}
