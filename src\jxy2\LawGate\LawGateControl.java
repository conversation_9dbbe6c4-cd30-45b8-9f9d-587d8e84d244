package jxy2.LawGate;

import com.tool.role.RoleData;
import org.come.action.FromServerAction;
import org.come.bean.LoginResult;
import org.come.until.GsonUtil;
import org.skill.frame.SkillMainFrame;

import java.util.*;

public class LawGateControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        LoginResult result = GsonUtil.getGsonUtil().getgson().fromJson(mes, LoginResult.class);
        RoleData.getRoleData().getLoginResult().setFmsld(result.getFmsld());
        // 更新 LawGateXLJPanel 的熟练度
        updateSkillLevel(getSkillLevel(result));
        SkillMainFrame.getSkillMainFrame().getSkillMainPanel().changeBtnPanel(2);
    }

    private void updateSkillLevel( String level) {
        if (!level.isEmpty()) {
            // 假设 LawGateXLJPanel 是一个全局变量或可以通过某种方式获取
            LawGateXLJPanel panel = LawGateXLFrame.getLawGateXLFrame().getLawGateXLJPanel();
            if (panel != null) {
                panel.updateSkillLevel(Integer.parseInt(level));
            }
        }
    }

    /**获取熟练度*/
    public static String getSkillLevel(LoginResult loginResult) {
        String fmsld = loginResult.getFmsld();
        // 解析 fmsld，提取技能ID和熟练度
        Map<String, String> skillMap = new HashMap<>();
        String[] skillPairs = fmsld.split("#");
        for (String skillPair : skillPairs) {
            String id = skillPair.split("_")[0];
            String sld = skillPair.split("_")[1];
            skillMap.put(id, sld);
        }

        // 获取选中的技能ID
        String selectedSkillId = LawGateXLFrame.getLawGateXLFrame().getLawGateXLJPanel().getLawGateSum();
        // 获取选中的技能ID对应的熟练度
        String selectedSkillLevel = skillMap.get(selectedSkillId);
        if (selectedSkillLevel == null) {
            System.out.println("未找到选中的技能ID: " + selectedSkillId);
            return "";
        }
        return  selectedSkillLevel;
    }


    /**更新多个技能熟练度*/

    public static List<String> updateSkillLevels(LoginResult loginResult) {
        String[] fmid = RoleData.getRoleData().getPrivateData().getSkill("F");
        String fmsld = loginResult.getFmsld();
        // 解析 fmsld，提取技能ID和熟练度
        Map<String, String> skillMap = new HashMap<>();
        String[] skillPairs = fmsld.split("#");
        for (String skillPair : skillPairs) {
            String id = skillPair.split("_")[0];
            String sld = skillPair.split("_")[1];
            skillMap.put(id, sld);
        }
        // 对比 fmid 并获取熟练度
        List<String> skillLevels = new ArrayList<>();
        for (String idWithLevel : fmid) {
            if (idWithLevel.equals("0")) continue; // 跳过无效的ID
            String[] idParts = idWithLevel.split("_");
            if (idParts.length == 2) {
                String id = idParts[0];
                String level = skillMap.get(id);
                if (level != null) {
                    skillLevels.add(level);
                } else {
                    System.out.println("未找到技能ID: " + id);
                }
            }
        }

        // 更新 LawGateXLJPanel 的熟练度
        return skillLevels;
    }
}
