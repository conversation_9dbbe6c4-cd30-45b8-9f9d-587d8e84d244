package com.tool.role;

import org.come.bean.PrivateData;

import java.math.BigDecimal;

public class SkillUtil {
	static String[] skillmsg;
	static String[] fmskillfmsg;
	static String[][] skills=new String[20][5];
	static String[][] fmskills = new String[30][2];
	static{
		skillmsg=new String[]{"雷","火","风","水","冰","睡","毒","混","慑","牛","盘","速","冥","蛊","忘","魅","霹","沧","霖","摇"};
		skills[0][0]="雷霆霹雳";
		skills[0][1]="日照光华";
		skills[0][2]="雷神怒击";
		skills[0][3]="电闪雷鸣";
		skills[0][4]="天诛地灭";
		
		skills[1][0]="地狱烈火";
		skills[1][1]="天雷怒火";
		skills[1][2]="三味真火";
		skills[1][3]="烈火骄阳";
		skills[1][4]="九阴纯火";
		
		skills[2][0]="飞砂走石";
		skills[2][1]="乘风破浪";
		skills[2][2]="太乙生风";
		skills[2][3]="风雷涌动";
		skills[2][4]="袖里乾坤";
		
		skills[3][0]="龙卷雨击";
		skills[3][1]="龙腾水溅";
		skills[3][2]="龙啸九天";
		skills[3][3]="蛟龙出海";
		skills[3][4]="九龙冰封";
		
		skills[4][0]="作茧自缚";
		skills[4][1]="金蛇缠丝";
		skills[4][2]="天罗地网";
		skills[4][3]="作壁上观";
		skills[4][4]="四面楚歌";
		
		skills[5][0]="催眠咒";
		skills[5][1]="瞌睡咒";
		skills[5][2]="离魂咒";
		skills[5][3]="迷魂醉";
		skills[5][4]="百日眠";
		
		skills[6][0]="蛇蝎美人";
		skills[6][1]="追魂迷香";
		skills[6][2]="断肠烈散";
		skills[6][3]="鹤顶红粉";
		skills[6][4]="万毒攻心";
		
		skills[7][0]="反间之计";
		skills[7][1]="情真意切";
		skills[7][2]="谗言相加";
		skills[7][3]="借刀杀人";
		skills[7][4]="失心狂乱";
		
		skills[8][0]="夺命勾魂";
		skills[8][1]="追神摄魄";
		skills[8][2]="魔音摄心";
		skills[8][3]="销魂蚀骨";
		skills[8][4]="阎罗追命";
		
		skills[9][0]="妖之魔力";
		skills[9][1]="力神复苏";
		skills[9][2]="狮王之怒";
		skills[9][3]="兽王神力";
		skills[9][4]="魔神附身";
		
		skills[10][0]="红袖添香";
		skills[10][1]="莲步轻舞";
		skills[10][2]="楚楚可怜";
		skills[10][3]="魔神护体";
		skills[10][4]="含情脉脉";
		
		skills[11][0]="魔之飞步";
		skills[11][1]="急速之魔";
		skills[11][2]="魔神飞舞";
		skills[11][3]="天外飞魔";
		skills[11][4]="乾坤借速";
		
		skills[12][0]="幽冥鬼火";
		skills[12][1]="火影迷踪";
		skills[12][2]="冥烟销骨";
		skills[12][3]="落日熔金";
		skills[12][4]="血海深仇";
		
		skills[13][0]="吸血水蛭";
		skills[13][1]="六翅毒蝉";
		skills[13][2]="啮骨抽髓";
		skills[13][3]="血煞之蛊";
		skills[13][4]="吸星大法";
		
		skills[14][0]="麻沸散";
		skills[14][1]="鬼失惊";
		skills[14][2]="乱魂钉";
		skills[14][3]="失心疯";
		skills[14][4]="孟婆汤";
		
		skills[15][0]="幽怜魅影";
		skills[15][1]="醉生梦死";
		skills[15][2]="一曲销魂";
		skills[15][3]="秦丝冰雾";
		skills[15][4]="倩女幽魂";
		
		skills[16][0]="平地生雷";
		skills[16][1]="惊霆贯顶";
		skills[16][2]="列缺霹雳";
		skills[16][3]="风雷万钧";
		skills[16][4]="震天动地";
		
		skills[17][0]="碧海潮生";
		skills[17][1]="怒涛拍岸";
		skills[17][2]="洪波涌起";
		skills[17][3]="白浪滔天";
		skills[17][4]="沧海横流";
		
		skills[18][0]="久旱初雨";
		skills[18][1]="兴云致雨";
		skills[18][2]="润物无声";
		skills[18][3]="沛然莫御";
		skills[18][4]="泽被万物";
		
		skills[19][0]="激浊扬清";
		skills[19][1]="狂飙怒号";
		skills[19][2]="扶摇而上";
		skills[19][3]="凌虚御风";
		skills[19][4]="飞举九天";

		fmskillfmsg=new String[]{
				//人
				"10001|男|守中","10001|男|血刃","10001|男|乱神",
				"10001|女|守中","10001|女|乱神","10001|女|神力",
				//魔
				"10002|男|机变","10002|男|神力","10002|男|御兽",
				"10002|女|戒定","10002|女|神力","10002|女|御兽",
				//仙
				"10003|男|灵光","10003|男|攻坚","10003|男|破壁",
				"10003|女|灵光","10003|女|攻坚","10003|女|破壁",
				//鬼
				"10004|男|攻坚","10004|男|护持","10004|男|正心",
				"10004|女|戒定","10004|女|攻坚","10004|女|禁咒",
				//龙
				"10005|男|潜龙","10005|男|亢龙","10005|男|惊龙",
				"10005|女|潜龙","10005|女|亢龙","10005|女|惊龙"
		};

		//男人---------------------------------------------------------------------
		fmskills[0][0]="清心静气|12001";
		fmskills[0][1]="以静制动|12501";

		fmskills[1][0]="利刃加身|12002";
		fmskills[1][1]="积羽沉舟|12502";

		fmskills[2][0]="神不守舍|12003";
		fmskills[2][1]="扑朔迷离|12503";

		//女人---------------------------------------------------------------------


		fmskills[3][0]="清心静气|12001";
		fmskills[3][1]="以静制动|12501";

		fmskills[4][0]="神不守舍|12003";
		fmskills[4][1]="扑朔迷离|12503";

		fmskills[5][0]="神力加身|12004";
		fmskills[5][1]="云飞烟灭|12504";

		//男魔---------------------------------------------------------------------

		fmskills[6][0]="幻影迷踪|12005";
		fmskills[6][1]="心无旁骛|12505";

		fmskills[7][0]="力挽狂澜|12006";
		fmskills[7][1]="披荆斩棘|12506";

		fmskills[8][0]="兽魂俯首|12007";
		fmskills[8][1]="困兽之斗|12507";

		//女魔---------------------------------------------------------------------

		fmskills[9][0]="刚柔兼备|12008";
		fmskills[9][1]="妙法莲华|12508";

		fmskills[10][0]="勢如破竹|12009";
		fmskills[10][1]="暴虎冯河|12509";

		fmskills[11][0]="兽魂俯首|12007";
		fmskills[11][1]="困兽之斗|12507";

		//男仙---------------------------------------------------------------------

		fmskills[12][0]="凝神一击|12010";
		fmskills[12][1]="一气呵成|12510";

		fmskills[13][0]="无坚不摧|12011";
		fmskills[13][1]="韬光养晦|12511";

		fmskills[14][0]="气吞山河|12012";
		fmskills[14][1]="摧经断骨|12512";
		//女仙---------------------------------------------------------------------
		fmskills[15][0]="凝神一击|12010";
		fmskills[15][1]="一气呵成|12510";

		fmskills[16][0]="无坚不摧|12011";
		fmskills[16][1]="韬光养晦|12511";

		fmskills[17][0]="气吞山河|12012";
		fmskills[17][1]="摧经断骨|12512";

		//男鬼---------------------------------------------------------------------

		fmskills[18][0]="无坚不摧|12011";
		fmskills[18][1]="韬光养晦|12511";

		fmskills[19][0]="法魂护体|12013";
		fmskills[19][1]="血蛊佑身|12513";

		fmskills[20][0]="气聚神凝|12014";
		fmskills[20][1]="明镜止水|12514";

		//女鬼---------------------------------------------------------------------

		fmskills[21][0]="刚柔兼备|12008";
		fmskills[21][1]="妙法莲华|12508";

		fmskills[22][0]="无坚不摧|12011";
		fmskills[22][1]="韬光养晦|12511";

		fmskills[23][0]="失魂落魄|12015";
		fmskills[23][1]="人鬼殊途|12515";

		//男龙---------------------------------------------------------------------

		fmskills[24][0]="鱼龙潜跃|12016";
		fmskills[24][1]="虎踞龙盘|12516";

		fmskills[25][0]="行气如虹|12017";
		fmskills[25][1]="浩气凌云|12517";

		fmskills[26][0]="神龙摆尾|12018";
		fmskills[26][1]="积健为雄|12518";

	}

	/**解析法门技能显示*/
	public static String[] getFMSkill(String str) {
		for(int i = 0; i < fmskillfmsg.length; ++i) {
			if (fmskillfmsg[i].equals(str)) {
				return fmskills[i];
			}
		}
		return null;
	}

   //判断他属于那门法术
	public static String getSkillDoor(String skillname){
		for (int i = 0; i < skills.length; i++) {
			for (int j = 0; j < skills[i].length; j++) {
				if (skills[i][j].equals(skillname)) {
					return skillmsg[i];
				}
			}
		}
		return null;
	}
	/**根据id获取对应的属性*/
    public static String getsuitSkill(int id){
    	if (id==6001) {return "加强封印";}
    	else if (id==6002) {return "加强昏睡";}
    	else if (id==6003) {return "加强遗忘";}
    	else if (id==6004) {return "加强混乱";}
    	else if (id==6005) {return "加强风法";}
    	else if (id==6006||id==6007) {return "加强法术";}
    	else if (id==6008) {return "加强火法";}
    	else if (id==6009) {return "加强鬼火";}
    	else if (id==6010) {return "忽视抗火";}
    	else if (id==6011) {return "忽视抗混";}
    	else if (id==6012) {return "忽视抗风";}
    	else if (id==6013) {return "忽视抗封";}
    	else if (id==6014) {return "忽视抗睡";}
    	else if (id==6015) {return "忽视遗忘";}
    	else if (id==6016) {return "加强震慑";}
    	else if (id==6017) {return "提抗上限";}
    	else if (id==6030) {return "加强速度法术效果";}
    	else if (id==6031) {return "水魔附身";}
    	else if (id==6032) {return "加强三尸虫";}
    	else if (id==6035) {return "加强防御法术效果";}
    	else if (id==6036) {return "加强防御法术效果";}
    	else if (id==6039) {return "加强攻击法术效果";}
    	else if (id==8001||id==8006) {return "忽视抗混";}
    	else if (id==8002||id==8007) {return "忽视抗封";}
    	else if (id==8003||id==8008) {return "忽视抗睡";}
    	else if (id==8004||id==8009) {return "忽视抗遗忘";}
    	else if (id==8005||id==8010) {return "加强毒";}
    	else if (id==8011) {return "忽视抗雷";}
    	else if (id==8012) {return "忽视抗火";}
    	else if (id==8013) {return "忽视抗风";}
    	else if (id==8014) {return "忽视抗水";}
    	else if (id==8015) {return "忽视抗鬼火";}
		return null;	
    }
    /**根据id获取对应的属性值*/
    public static double getSuitValue(int id,int lvl){
    	if (id==6016) {
			return 1.5+lvl*0.5;
		}if (id==6017) {
			return 5+lvl;
		}else if ((id>=6001&&id<=6009)||id==6030||id==6035||id==6036||id==6039) {
			return 3+lvl;
		}else if (id==3032) {
			return 100+lvl*100;
		}else if (id>=8001&&id<=8004) {//每50点根骨提供0.1
			int v=RoleProperty.getBone(RoleData.getRoleData().getLoginResult())/50;
			return v*0.1;
		}else if (id>=8006&&id<=8009) {//每30点灵性提供0.1
			int v=RoleProperty.getPower(RoleData.getRoleData().getLoginResult())/30;
			return v*0.1;
		}else if ((id>=8011&&id<=8014)||id==8005) {//每10点根骨提供0.1
			int v=RoleProperty.getBone(RoleData.getRoleData().getLoginResult())/10;
			return v*0.1;
		}else if (id>=8010) {//每10点灵性提供0.1
			int v=RoleProperty.getPower(RoleData.getRoleData().getLoginResult())/10;
			return v*0.1;
		}else if (id>=8015) {//每20点根骨提供0.1
			int v=RoleProperty.getPower(RoleData.getRoleData().getLoginResult())/20;
			return v*0.1;
		}
    	return 0.5+lvl*0.5;
    }
    
    /** 根据法术类型获取对应的法术 */
    public static String[] getSkillsAll(String skillDoor){
    	for (int i = 0; i < skillmsg.length; i++) {
			if(skillmsg[i].equals(skillDoor)){
				return skills[i];
			}
		}
		return null;
    }
    /**获取种族*/
    public static String getSepciesN(BigDecimal se){
    	int id=se.intValue();
		if (id==23001||id==23002||id==23003) {return "男鬼";}
		else if (id==23004||id==23005||id==23006) {return "女鬼";}
		else if (id==24001||id==24002||id==24003) {return "男龙";}
		else if (id==24004||id==24005||id==24006) {return "女龙";}
		else if (id==22001||id==22002||id==22003||id==22007||id==22009) {return "男仙";}
		else if (id==22004||id==22005||id==22006||id==22008||id==22010) {return "女仙";}
		else if (id==21001||id==21002||id==21003||id==21007||id==21009) {return "男魔";}
		else if (id==21004||id==21005||id==21006||id==21008||id==21010) {return "女魔";}
		else if (id==20001||id==20002||id==20003||id==20007||id==20009) {return "男人";}
		else if (id==20004||id==20005||id==20006||id==20008||id==20010) {return "女人";}
    	return "";
    }
    /**获取种族*/
    public static String[] getSepciesS(String se){
		if (se.equals("男鬼")) {return new String[]{"遗忘","鬼火","三尸虫"};}
		else if (se.equals("女鬼")) {return new String[]{"遗忘","鬼火","魅惑"};}
		else if (se.equals("男仙")) {return new String[]{"雷","水","风"};}
		else if (se.equals("女仙")) {return new String[]{"雷","水","火"};}
		else if (se.equals("男魔")) {return new String[]{"震慑","加攻","加速"};}
		else if (se.equals("女魔")) {return new String[]{"震慑","加攻","盘丝"};}
		else if (se.equals("男人")) {return new String[]{"封印","昏睡","混乱"};}
		else if (se.equals("女人")) {return new String[]{"封印","昏睡","毒"};}
		else if (se.equals("男龙")) {return new String[]{"霹雳","甘霖","扶摇"};}
		else if (se.equals("女龙")) {return new String[]{"霹雳","甘霖","沧波"};}
    	return null; 	
    }

	public static String[] getSepcieswas(String se){
        switch (se) {
            case "男鬼":
                return new String[]{"忘", "冥", "蛊"};
            case "女鬼":
                return new String[]{"忘", "惑", "冥"};
            case "男仙":
                return new String[]{"雷", "水", "风"};
            case "女仙":
                return new String[]{"雷", "水", "火"};
            case "男魔":
                return new String[]{"震", "攻", "速"};
            case "女魔":
                return new String[]{"震", "攻", "盘"};
            case "男人":
                return new String[]{"冰", "睡", "混"};
            case "女人":
                return new String[]{"冰", "睡", "毒"};
            case "男龙":
                return new String[]{"霹", "雨", "摇"};
            case "女龙":
                return new String[]{"霹", "雨", "涌"};
			default:
				return new String[0]; // 返回空数组
        }
	}
	public static String[] getSepciesZhiye(String se){
        switch (se) {
            case "男鬼":
                return new String[]{"忘冥", "冥蛊", "蛊忘"};
            case "女鬼":
                return new String[]{"忘惑", "惑冥", "冥忘"};
            case "男仙":
                return new String[]{"雷水", "水风", "风雷"};
            case "女仙":
                return new String[]{"雷水", "水火", "火雷"};
            case "男魔":
                return new String[]{"震攻", "攻速", "速震"};
            case "女魔":
                return new String[]{"震攻", "攻盘", "盘震"};
            case "男人":
                return new String[]{"混冰", "冰睡", "睡混"};
            case "女人":
                return new String[]{"毒冰", "睡毒", "冰睡"};
            case "男龙":
                return new String[]{"霹雨", "雨摇", "摇霹"};
            case "女龙":
                return new String[]{"霹雨", "雨涌", "涌霹"};
        }
		return null;
	}


	/**更换技能集合*/
	public static void changeSkill(PrivateData data,BigDecimal se){
		String[] vs=data.getSkill("S");
		String[] ses=getSepciesS(getSepciesN(se));
		if (vs!=null) {
			StringBuffer buffer=new StringBuffer();
			for (int i = 0; i < vs.length; i++) {
				String[] vss = vs[i].split("_");
                if (buffer.length()!=0) {buffer.append("#");}
                buffer.append(changeSkillId(Integer.parseInt(vss[0]),ses));
                buffer.append("_");
                buffer.append(vss[1]); 
			}
			data.setSkills("S",buffer.toString());
		}
	}
	/**获取将转换成的技能id*/
	public static int changeSkillId(int id,String[] ses){
		int p=0;
		if ((id>=1006&&id<=1010)||(id>=1046&&id<=1050)||(id>=1021&&id<=1025)||(id>=1071&&id<=1075)||(id>=1081&&id<=1085)) {
			p=0;
		}else if ((id>=1011&&id<=1015)||(id>=1051&&id<=1055)||(id>=1026&&id<=1030)||(id>=1061&&id<=1065)||(id>=1071&&id<=1075)||(id>=1091&&id<=1095)) {
			p=1;
		}else {
			p=2;
		}
		String leixing=ses[p];
		int lvl=id%5;
		if (lvl==0) {lvl=5;}
		if (leixing.equals("封印")) {id=1005+lvl;}
		else if (leixing.equals("昏睡")) {id=1010+lvl;}
		else if (leixing.equals("混乱")) {id=1000+lvl;}
		else if (leixing.equals("毒")) {id=1015+lvl;}
		else if (leixing.equals("雷")) {id=1045+lvl;}
		else if (leixing.equals("水")) {id=1050+lvl;}
		else if (leixing.equals("风")) {id=1040+lvl;}
		else if (leixing.equals("火")) {id=1055+lvl;}
		else if (leixing.equals("震慑")) {id=1020+lvl;}
		else if (leixing.equals("加攻")) {id=1025+lvl;}
		else if (leixing.equals("加速")) {id=1035+lvl;}
		else if (leixing.equals("盘丝")) {id=1030+lvl;}
		else if (leixing.equals("遗忘")) {id=1070+lvl;}
		else if (leixing.equals("鬼火")) {id=1060+lvl;}
		else if (leixing.equals("三尸虫")) {id=1065+lvl;}
		else if (leixing.equals("魅惑")) {id=1075+lvl;}
		else if (leixing.equals("霹雳")) {id=1080+lvl;}
		else if (leixing.equals("沧波")) {id=1085+lvl;}
		else if (leixing.equals("甘霖")) {id=1090+lvl;}
		else if (leixing.equals("扶摇")) {id=1095+lvl;}
		return id;
	}
}
