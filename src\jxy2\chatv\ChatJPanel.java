package jxy2.chatv;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.until.*;

import javax.swing.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;
/**
* 聊天窗口
* <AUTHOR>
* @date 2024/7/4 上午6:18
*/

public class ChatJPanel extends JPanel {
    public ArrayList<ChatBtn> btnBasis = new ArrayList<>(7); // 初始化时设置初始容量
    public ChatBtn[] ranksBtn;
    public ChatBtn las,addBtn,fontBtn,btnLeftKey,btnRoll,btnDrag,btnAlpha,btnRecord;
    private String[] basis = {"全","帮","系","队","世","战","信"};
    private JScrollPane jScrollPane;
    public boolean is = false;
    public List<String> list = new ArrayList<>();
    public ChatJPanel() {
        // 为面板添加键盘监听
        this.setPreferredSize(new Dimension(556, 190));
        this.setLayout(null);
        this.setFocusable(true);
        this.setOpaque(true);
        this.setBackground(UIUtils.Color_BACK);

        getFontBtn();
        getjScrollPane();
        getLas();
        getBtnLeftKey();
        getBtnRoll();
        getBtnDrag();
        getBtnAlpha();
        getBtnRecord();
        getAddBtn();
        getRanksBtn();

    }
    /**
     * 更新按钮状态的方法
     * @param newList 按钮状态的新列表。这个列表决定了按钮的可见性或启用状态。
     * 列表中的每个字符串代表了一个按钮的状态。
     * 注意：此方法的实现细节未提供，因此注释仅针对方法的总体目的和参数。
     */
    public void updateButtons(List<String> newList) {
//        // 先确保btnBasis至少和newList一样长
//        while (btnBasis.size() < newList.size()) {
//            // 创建并添加新按钮的逻辑保持不变
//            ChatBtn newBtn = new ChatBtn(ImgConstants.tz104, 1, this, btnBasis.size());
//            btnBasis.add(newBtn);
//            this.add(newBtn); // 确保新按钮被添加到容器
//        }
//
//        // 遍历newList更新按钮
//        for (int i = 0; i < newList.size(); i++) {
//            ChatBtn btn = btnBasis.get(i);
//            btn.setText(newList.get(i));
//            btn.btnchange(i == 0 ? 2 : 0);
//            btn.setBounds(6 + i * 62, 1, 62, 24);
//        }
//        // 处理btnBasis中多余的按钮
//        while (btnBasis.size() > newList.size()) {
//            // 移除最后一个不再需要的按钮
//            ChatBtn removedBtn = btnBasis.remove(btnBasis.size() - 1);
//            this.remove(removedBtn); // 从容器中移除该按钮
//            removedBtn.setVisible(false); // 可选：确保按钮不可见
//            removedBtn = null; // 可选：帮助垃圾回收
//
//            int newFrameWidth = Math.min(369, 690);
//            int newFrameHeight = Math.min(168, 346);
//            ChatFrame.getChatFrame().setBounds(0, ScrenceUntil.Screen_y + 58 - newFrameHeight, newFrameWidth, newFrameHeight);
//        }
//
//        // 调整窗口大小和按钮位置的逻辑保持不变
//        int btnsize = newList.size() * 62;
//        if (getWidth() < btnsize + 20) {
//            int newFrameHeight =getHeight();
//            newFrameHeight = Math.max(Math.min(newFrameHeight, 346), 168);
//            ChatFrame.getChatFrame().setBounds(0, ScrenceUntil.Screen_y + 58 - newFrameHeight, btnsize + 80, getHeight());
//        }
//        addBtn.setBounds(6 + btnsize, 6, 18, 18);
//        las.setBounds(getWidth() - 16, 15, 16, 15);
//        fontBtn.setBounds(getWidth()-16 ,getHeight()-30,18,18);
//        FrameMessageChangeJpanel.Out();

    }

    public void updateButtonImages(int uiType) {
        SwingUtilities.invokeLater(() -> {
            try {
                // 移除并重新添加滚动条
                JScrollBar oldVBar1 = jScrollPane.getVerticalScrollBar();
                JScrollBar newVBar1 = new JScrollBar(JScrollBar.VERTICAL);
                // 复制旧滚动条的属性
                newVBar1.setValues(oldVBar1.getValue(), oldVBar1.getVisibleAmount(),
                        oldVBar1.getMinimum(), oldVBar1.getMaximum());
                // 设置新的UI
                BasicScrollBarUI basicScrollBarUI = (uiType == 1) ? new ChatPanelUI() : new ChatPanelRedUI();
                newVBar1.setUI(basicScrollBarUI);
                // 替换滚动条
                jScrollPane.setVerticalScrollBar(newVBar1);
                // 更新图标资源
                BOTTOM_ICON(true);
                RunYexcp(0,0,1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }


    public static float alps = 1.0f;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        this.setBorder(BorderFactory.createEmptyBorder());
        // 将Graphics转换为Graphics2D，以便使用AlphaComposite
        Graphics2D g2d = (Graphics2D) g.create();
        AlphaComposite alphaComposites = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alps);
        g2d.setComposite(alphaComposites);
        Juitil.ImngBack(g2d, Juitil.tz128, 0, 20, getWidth()-2, getHeight(), 1);
        String ts = Util.SwitchUI==1?"0x6FAC1039":"0x6FAB1069";
        String lj = Util.SwitchUI==1?"she.wdf":"redmu.wdf";
        g.drawImage(CutButtonImage.getWdfPng(ts,lj).getImage(), getWidth()-13, 35, 6, getHeight()-65, this);
        // 设置透明度为0.5（50%透明）
        float alpha = 0.5f; // 范围从0.0（完全透明）到1.0（完全不透明）
        AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
        g2d.setComposite(alphaComposite);
        Juitil.ImngBack(g2d, Juitil.tz128, 0, getHeight()-20, getWidth()-2, 20, 1);
        Juitil.ImngBack(g2d, Juitil.tz128, 0, 17, getWidth(), getHeight(), 1);

        // 释放Graphics2D资源
        g2d.dispose();
        // 以下代码不变
        if (FrameMessageChangeJpanel.chatbox.getHeight() < (getHeight()-43)) {
            jScrollPane.getViewport().setViewSize(new Dimension(getWidth(), (getHeight() - 42)));
        }
        jScrollPane.setBounds(10,24,getWidth()-14,getHeight()-43);
        jScrollPane.getVerticalScrollBar().setUnitIncrement(80);

    }



    public JScrollPane getjScrollPane() {
        //宠物技能展示
        if (jScrollPane == null) {
            jScrollPane = new JScrollPane();
            jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            BasicScrollBarUI basicScrollBarUI= Util.SwitchUI==1?new ChatPanelUI():new ChatPanelRedUI();
            jScrollPane.getVerticalScrollBar().setUI(basicScrollBarUI);
            jScrollPane.getVerticalScrollBar().setUnitIncrement(80);
            //滚动条在左边
//            jScrollPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
            jScrollPane.getViewport().setOpaque(false);
            jScrollPane.setOpaque(false);
            jScrollPane.setBounds(3,24,getWidth()-5,getHeight()-28);
            jScrollPane.setBorder(BorderFactory.createEmptyBorder());
            jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            JPanel jPanel = new JPanel();
            jPanel.setBackground(null);
            jPanel.setOpaque(false);
            jPanel.setBorder(null);
            jPanel.setLayout(null);
            jScrollPane.setViewportView(jPanel);
            this.add(jScrollPane);
        }
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public ChatBtn getLas() {
        if (las==null){
            las= new ChatBtn(ImgConstants.tz28, 1, this,17,"");
            las.addMouseMotionListener(new MouseAdapter() {
                @Override
                public void mouseDragged(MouseEvent e) {
                    super.mouseDragged(e);
                   RunYexcp(e.getX(),e.getY(),1);
                }
            });
            las.setBounds(369-16 ,15,11,12);
            add(las);
        }
        return las;
    }

    public void RunYexcp(int dx, int dy, int type) {
        if (type == 2) {
            // 获取当前窗口
            ChatFrame chatFrame = ChatFrame.getChatFrame();
            // 获取当前窗口位置和大小
            java.awt.Rectangle bounds = chatFrame.getBounds();
            // 计算新位置
            int newX = bounds.x + dx;
            int newY = bounds.y + dy;
            // 获取屏幕尺寸

            // 确保窗口不会移出屏幕
            newX = Math.max(0, Math.min(newX, ScrenceUntil.Screen_x - bounds.width));
            newY = Math.max(0, Math.min(newY, (ScrenceUntil.Screen_y-30) - bounds.height));
            // 设置新位置
            chatFrame.setBounds(newX, newY, bounds.width, bounds.height);
        } else {
            int newFrameWidth = getWidth() + dx ;
            int newFrameHeight =getHeight() - dy;
            newFrameWidth = Math.max(Math.min(newFrameWidth, 479), 466);
            newFrameHeight = Math.max(Math.min(newFrameHeight, 500), 168);
            int nys = Util.SwitchUI==1?30:28;
            ChatFrame.getChatFrame().setBounds(0, ScrenceUntil.Screen_y-nys-newFrameHeight,newFrameWidth,newFrameHeight);
        }
        //底部菜单按钮坐标调整
        BOTTOM_MENU();
        //底部图标调整
        BOTTOM_ICON(true);
        ChatBtn.EquiButtonClick(this,0);
        if (type==1){
            ChatFrame.getChatFrame().setBorder(BorderFactory.createEmptyBorder());
            FrameMessageChangeJpanel.Out(true);
        }

    }

    public void BOTTOM_ICON(boolean is) {
        String img = Util.SwitchUI ==1?"0x6FAC1035":"0x6FAB1065";
        String btn = Util.SwitchUI ==1?"0x6FAC1053":"0x6FAB1028";
        String roll = Util.SwitchUI ==1?"0x6FAC1061":"0x6FAB1077";
        String rolltrue = Util.SwitchUI ==1?"0x6FAC1062":"0x6FAB1078";
        String font = Util.SwitchUI ==1?"0x6FAC1063":"0x6FAB1079";
        String deag = Util.SwitchUI ==1?"0x6FAC1060":"0x6FAB1076";
        String alpha = Util.SwitchUI ==1?"0x6FAC1064":"0x6FAB1080";
        String record = Util.SwitchUI ==1?"0x6FAC1065":"0x6FAB1081";
        String add = Util.SwitchUI ==1?"0x6FAC1070":"0x6FAB1030";
        String addbtns = Util.SwitchUI ==1?"0x6FAC1071":"0x6FAB1082";
        las.setIcons(Juitil.getImgs(img));
        btnLeftKey.setIcons(Juitil.getImgs(btn));
        btnRoll.setIcons(Juitil.getImgs(is?roll:rolltrue));
        fontBtn.setIcons(Juitil.getImgs(font));
        btnDrag.setIcons(Juitil.getImgs(deag));
        btnAlpha.setIcons(Juitil.getImgs(alpha));
        btnRecord.setIcons(Juitil.getImgs(record));
        addBtn.setIcons(Juitil.getImgs(add));
        for (int i = 0; i < 7; i++) {
            ranksBtn[i].setIcons(Juitil.getImgs(addbtns));
            ranksBtn[i].setColors(Util.SwitchUI==1?UIUtils.COLOR_BTNTEXT:UIUtils.COLOR_RED);
            ranksBtn[i].setFont(UIUtils.TEXT_HYJ16B);
        }
    }

    public void BOTTOM_MENU() {
        int w =  Util.SwitchUI ==1?18:19;
        int h =  Util.SwitchUI ==1?18:20;
        btnLeftKey.setBounds(3 ,getHeight()-19,w,h);
        btnRoll.setBounds(23 ,getHeight()-19,w,h);
        btnDrag.setBounds(43 ,getHeight()-19,w,h);
        fontBtn.setBounds(63 ,getHeight()-19,w,h);
        btnAlpha.setBounds(83 ,getHeight()-19,w,h);
        btnRecord.setBounds(103 ,getHeight()-19,w,h);
        addBtn.setBounds(123 ,getHeight()-19,w,h);
        las.setBounds(getWidth()-12 ,15,11,12);
        for (int i = 0; i < 7; i++) {
            ranksBtn[i].setBounds(143+i *20 ,getHeight()-19,w,h);
            ranksBtn[i].setMenu(3);
            ranksBtn[i].btnchange(0);
            ranksBtn[i].setBtn(1);
        }
    }

    public void setLas(ChatBtn las) {
        this.las = las;
    }

    public ChatBtn getFontBtn() {
        if (fontBtn==null) {
            String font = Util.SwitchUI ==1?"0x6FAC1063":"0x6FAB1079";
            fontBtn = new ChatBtn(font, 1, this, 14,"");
            fontBtn.setBounds(369-16 ,136,18,18);
            add(fontBtn);
        }

        return fontBtn;
    }

    public void setFontBtn(ChatBtn fontBtn) {
        this.fontBtn = fontBtn;
    }
    public ChatBtn getBtnLeftKey() {
        if (btnLeftKey==null) {
            String btn = Util.SwitchUI ==1?"0x6FAC1053":"0x6FAB1028";
            btnLeftKey = new ChatBtn(btn, 1, this, 15,"");
            btnLeftKey.setBounds(353 ,136,19,20);
            add(btnLeftKey);
        }
        return btnLeftKey;
    }

    public void setBtnLeftKey(ChatBtn btnLeftKey) {
        this.btnLeftKey = btnLeftKey;
    }

    public ChatBtn getBtnRoll() {
        if (btnRoll==null) {
            String btn = Util.SwitchUI ==1?"0x6FAC1061":"0x6FAB1077";
            btnRoll = new ChatBtn(btn, 1, this, 16,"");
            btnRoll.setBounds(353+20 ,136,19,20);
            add(btnRoll);
        }
        return btnRoll;
    }

    public void setBtnRoll(ChatBtn btnRoll) {
        this.btnRoll = btnRoll;
    }

    public ChatBtn getBtnDrag() {
        if (btnDrag==null) {
            String deag = Util.SwitchUI ==1?"0x6FAC1060":"0x6FAB1076";
            btnDrag = new ChatBtn(deag, 1, this, 17,"");
            btnDrag.setBounds(353+40 ,136,19,20);
            add(btnDrag);
        }
        return btnDrag;
    }

    public void setBtnDrag(ChatBtn btnDrag) {
        this.btnDrag = btnDrag;
    }

    public ChatBtn getBtnAlpha() {
        if (btnAlpha==null) {
            String alpha = Util.SwitchUI ==1?"0x6FAC1064":"0x6FAB1080";
            btnAlpha = new ChatBtn(alpha, 1, this, 18,"");
            btnAlpha.setBounds(353+60 ,136,19,20);
            add(btnAlpha);
        }
        return btnAlpha;
    }

    public void setBtnAlpha(ChatBtn btnAlpha) {
        this.btnAlpha = btnAlpha;
    }

    public ChatBtn getBtnRecord() {
        if (btnRecord==null) {
            String record = Util.SwitchUI ==1?"0x6FAC1065":"0x6FAB1081";
            btnRecord = new ChatBtn(record, 1, this, 19,"");
            btnRecord.setBounds(353+80 ,136,19,20);
            add(btnRecord);
        }
        return btnRecord;
    }

    public void setBtnRecord(ChatBtn btnRecord) {
        this.btnRecord = btnRecord;
    }

    public ChatBtn getAddBtn() {
        if (addBtn==null) {
            String add = Util.SwitchUI ==1?"0x6FAC1070":"0x6FAB1030";
            addBtn = new ChatBtn(add, 1, this, 20,"");
            addBtn.setBounds(353+100 ,136,19,20);
            add(addBtn);
        }
        return addBtn;
    }

    public void setAddBtn(ChatBtn addBtn) {
        this.addBtn = addBtn;
    }

    public ChatBtn[] getRanksBtn() {
        if (ranksBtn==null) {
            ranksBtn = new ChatBtn[7];
            for (int i = 0; i < 7; i++) {
                String img = Util.SwitchUI ==1?"0x6FAC1071":"0x6FAB1082";
                ranksBtn[i] = new ChatBtn(img, 1, this, i,basis[i]);
                ranksBtn[i].setBounds(353+120+i *20 ,136,19,20);
                btnBasis.add(ranksBtn[i]);
                add(ranksBtn[i]);
            }
        }
        return ranksBtn;
    }

    public void setRanksBtn(ChatBtn[] ranksBtn) {
        this.ranksBtn = ranksBtn;
    }

    public String[] getBasis() {
        return basis;
    }

    public void setBasis(String[] basis) {
        this.basis = basis;
    }

    public ArrayList<ChatBtn> getBtnBasis() {
        return btnBasis;
    }

    public void setBtnBasis(ArrayList<ChatBtn> btnBasis) {
        this.btnBasis = btnBasis;
    }
}
