package come.tool.FightingEffect;

import come.tool.Fighting.*;

public class AttackEffect implements Effect{
//	攻击9 防御 6 被攻击4 法术5 
	@Override
	public StateProgress analysisAction(FightingState State, int path) {
		// TODO Auto-generated method stub
		 StateProgress  progress=EffectType.getProgress(State, path);
		 progress.setMan(path);
		 Fightingimage fightingimage=FightingMixDeal.CurrentData.get(path);
		 if (State.getStartState().equals("普通攻击")) {
			 int dir=Integer.parseInt(State.getEndState());
			 dir=EffectType.getdir(dir,State.getCamp()==FightingMixDeal.camp?FightingMixDeal.camp:State.getCamp());
			 progress.setDir(dir);
			 progress.setDirend(dir);
			 progress.setType(9);
		 }else  if (State.getStartState().equals("法术攻击")){
			 progress.setType(5);
		 }else  if (State.getStartState().equals("防御")){
			 progress.setType(6);
		 }else  if (State.getStartState().equals("被攻击")){
			progress.setType(4);
		 }else  if (State.getStartState().equals("技能")){
			 if (progress.getHp_Change()<0) {
				 progress.setType(4);
			}else {
				 progress.setType(7);	
			}	
		 }else  if (State.getStartState().equals("药")){
			 if (progress.getHp_Change()>0||progress.getMp_Change()>0) {
				 progress.setMusic("药品");
				 SkillSpell skillSpell;
				 skillSpell=new SkillSpell(progress.getHp_Change()>0?"加血":"加蓝",fightingimage.getX(),fightingimage.getY());
				 progress.setSpell(skillSpell);				 
			 } 
			 progress.setType(7);		
		 }else  if (State.getStartState().equals("代价")){
			 progress.setType(0);		
		 }else  if (State.getStartState().equals("限制刷新")){
			progress.setType(0);
			fightingimage.getFightingManData().setState_1(State.getEndState());
			//刷新技能栏
			if (fightingimage.getFightingManData().getCamp() == FightingMixDeal.camp && fightingimage.getFightingManData().getMan() == FightingMixDeal.myman()) {
				FightingMixDeal.buffUtil.SXSkill(fightingimage.getFightingManData().cxxx("技能"));	
			}			
		 }else if (State.getStartState().equals("特效1")) {
			 progress.setType(3);
		 }else if (State.getStartState().equals("特效2")) {
			 progress.setType(11);
		 }else if (State.getStartState().equals("特效3")) {
			 progress.setType(12);
		 }
		 return progress;
	}

}
