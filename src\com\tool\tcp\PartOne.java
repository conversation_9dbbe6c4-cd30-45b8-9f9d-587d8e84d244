package com.tool.tcp;

import jxy2.WdfLoaDing;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.SoftReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PartOne implements NewPart{
	/**旧人物动作加载 特效类的 act==-1技能特效  act==-2其他特效*/
	private String skin;//路径
	private String color;//颜色
	private int act;//动作
	private int lvl;//层级
	private Sprite tcp;//画步
	private NewPart   flyPart;
	private NewPart   part;//下一层 层数低先画  默认1层  召唤兽饰品2层
	private static final Map<String, Sprite> stateSpriteCache = new HashMap<>();
	private float shadowOffsetY = 0; // 影子Y轴偏移量
	private boolean isFlying = false; // 是否在飞行中
	private static final float MAX_SHADOW_OFFSET = 100f; // 最大影子偏移量
	private static final float SHADOW_STEP = 5f; // 每次更新增加/减少的偏移量
	private double bodyAlpha = 1.0f; // 主体透明度
	private boolean isLanding = false; // 是否正在降落
	private float currentShadowOffset = 100f; // 当前影子偏移量

	public PartOne(String skin, int act, int lvl,String color) {
		this.skin = skin;
		this.act = act;
		this.lvl = lvl;
		this.color=color;
	}
	@Override
	public void draw(Graphics g, int x, int y, int dir, long time) {
		// TODO Auto-generated method stub
		if (flyPart != null) {
			flyPart.drawFly(g, x, y, dir, time, 1F);
		}

		if (tcp == null) {
			loadTcp();
		}//加载素材
		synchronized (this) {
			if (tcp != null) {//绘制
				tcp.updateToTime(time, dir);
				tcp.draw(g, x, y);
			}
		}

		if (part != null) {
			part.draw(g, x, y, dir, time);
		}
	}
	@Override
	public void draw(Graphics g, int x, int y, int dir, long time,int type) {
		// TODO Auto-generated method stub
		if (flyPart!=null) { flyPart.drawFly(g, x, y, dir, time , 1F); }

		if (tcp==null) {loadTcp();}//加载素材
		synchronized(this){
		if (tcp!=null) {//绘制
			tcp.updateToTime(time,dir);
			tcp.draw(g, x, y,200,200);
			}
		}
		if (part!=null) {
			part.draw(g, x, y, dir, time);
		}
	}
	@Override
	public void draw(Graphics g, int x, int y, int dir, long time, float alpha) {
		// TODO Auto-generated method stub

		if (flyPart!=null) {
			flyPart.drawFly(g, x, y, dir, time,alpha);
		}

		if (tcp==null) {loadTcp();}//加载素材
		synchronized(this){
		if (tcp!=null) {//绘制
			tcp.updateToTime(time,dir);
			tcp.draw(g, x, y, alpha);
		}
		}
		if (part!=null) {
			part.draw(g, x, y, dir, time,alpha);
		}
	}
	@Override
	public void drawEnd(Graphics g, int x, int y, int dir, float alpha) {
		// TODO Auto-generated method stub
		if (lvl!=1) {
			if (part!=null) {
				part.drawEnd(g, x, y, dir, alpha);
			}
			return;
		}
		if (tcp==null) {loadTcp();}//加载素材
		synchronized(this){
		   if (tcp!=null) {//绘制
			   tcp.updateToTime(tcp.getTime()-1,dir);
			   tcp.draw(g, x, y, alpha);
			}
		}
	}
	@Override
	public void drawBattle(Graphics g, int x, int y, int dir, long time,float alpha) {
		// TODO Auto-generated method stub
		if (tcp==null) {loadTcp();}//加载素材
		synchronized(this){
		if (tcp!=null) {//绘制
			tcp.updateToTime(time,SpriteFactory.changdir(dir,tcp.getAnimationCount()));
			tcp.draw(g, x, y , alpha);
		}
		}
		if (part!=null) {
			part.drawBattle(g,x,y,dir,time,alpha);
		}
	}
	@Override
	public void drawFly(Graphics g, int x, int y, int dir, long time, float alpha) {

		if (tcp == null) {
			loadTcp();
		}

		synchronized (this) {
			if (tcp != null) {
				tcp.updateToTime(time, dir);
				if (isLanding) {
					tcp.draw(g, x, y, (float)bodyAlpha);
				} else {
					tcp.draw(g, x, y, alpha);
				}
			}
		}

	}
	@Override
	public NewPart addPart(NewPart newPart) {
		// TODO Auto-generated method stub
		if (newPart==null) {
			return this;
		}
		if (newPart.getLvl()<lvl) {
			newPart.setPart(this);
			return newPart;
		}
		if (part==null) {
			part=newPart;
			return this;
		}else {
			part=part.addPart(newPart);
		}
		return this;
	}
	@Override
	public NewPart removePart(String rSkin) {
		// TODO Auto-generated method stub
		if (isD(rSkin)) {
			return part;
		}
		if (part!=null) {
			part=part.removePart(rSkin);
		}
		return this;
	}
	/**对比*/
	private boolean isD(String rSkin) {
		return rSkin.equals(skin);
	}
	@Override
	public boolean contains(int x, int y) {
		// TODO Auto-generated method stub
		if (lvl == 1) {
			synchronized (this) {
				if (tcp != null) {
					return tcp.contains(x, y);
				}
			}
		} else if (part != null) {
			return part.contains(x, y);
		}
		return false;
	}
	@Override
	public void recycle() {
		// TODO Auto-generated method stub
		synchronized(this){
			tcp=null;
		}
		if (part!=null) {
			part.recycle();
		}
	}
	@Override
	public int getTime() {
		// TODO Auto-generated method stub
		if (lvl!=1&&part!=null) {
			return part.getTime();
		}
		synchronized(this){
			if (tcp!=null) {
	        	return tcp.getTime();
			}
		}
		loadTcp();
		return 1200;
	}
	@Override
	public void loadTcp() {
		// TODO Auto-generated method stub
		synchronized(this){
			if (act==-2) {
				tcp=SpriteFactory.Prepare(GetTcpPath.getMouseTcp(skin));
			}else if (act==-1) {
//				tcp=SpriteFactory.Prepare(GetTcpPath.getSkillTcp(skin));
				tcp = sprite(act,skin);
			}else if (act==-3) {//fly
				tcp=SpriteFactory.Prepare(skin);
			}else {
				if (flyPart!=null) {
					tcp = sprite(2,skin);
				}else {
					//TODO 实现皮肤分配处理大小不得超过90MB
					if (getSkinValue(act, skin) != null) {
						Sprite stateSprite = stateSpriteCache.get(getSkinValue(act, skin));
						if (stateSprite == null) {
							stateSprite = sprite(act,skin);
							if (stateSprite != null) {
								stateSpriteCache.put(getSkinValue(act, skin), stateSprite);
							}
						}
						if (stateSprite != null) {
							tcp = sprite(act,skin);
						}
					}else {
						tcp = null;
						tcp=SpriteFactory.Prepare(GetTcpPath.getRoleTcp(skin,act,color));
					}
				}
			}
		}
	}
	@Override
	public int getAct() {
		// TODO Auto-generated method stub
		if (lvl!=1&&part!=null) {
			return part.getAct();
		}
		return act;
	}
	@Override
	public void setAct(int Act) {
		// TODO Auto-generated method stub
		if (act>0) {
			if (act!=Act) {
				act=Act;
				synchronized(this){
					tcp=null;
				}
			}
		}
		if (part!=null) {
			part.setAct(Act);
		}
	}
	@Override
	public int getLvl() {
		// TODO Auto-generated method stub
		return lvl;
	}
	@Override
	public NewPart getPart() {
		// TODO Auto-generated method stub
		return part;
	}
	@Override
	public void setPart(NewPart part) {
		// TODO Auto-generated method stub
		this.part=part;
	}

	@Override
	public NewPart setPart(int lvl, String skin) {
		// TODO Auto-generated method stub
		if (this.lvl==lvl) {
			this.skin=skin;
            synchronized(this){
            	this.tcp=null;
        	}
            return this;
		}else if (part!=null) {
			return part.setPart(lvl, skin);
		}
		return this;
	}
	@Override
	public NewPart setPart(int lvl, long skin, HHOne[] ones) {
		// TODO Auto-generated method stub
        if (this.lvl==lvl) {
        	NewPart newPart=new PartTwo(skin, ones, act, lvl,color);
        	newPart.setPart(part);
        	return newPart;
		}else if (part!=null) {
			return part.setPart(lvl, skin, ones);
		}
		return this;
	}
	@Override
	public int getPy() {
		// TODO Auto-generated method stub
		if (lvl==1) {
			synchronized(this){
				if (tcp!=null) {
					return tcp.getHightMax();//暂时未整理
				}
			}
		}else if (part!=null) {
			return part.getPy();
		}
		return -1;
	}
	@Override
	public NewPart clonePart() {
		// TODO Auto-generated method stub
		NewPart newPart=new PartOne(skin, act, lvl, color);
		if (part!=null) {newPart.setPart(part.clonePart());}
		return newPart;
	}
	@Override
	public int getAnimationCount() {
		// TODO Auto-generated method stub
		if (lvl==1) {
			synchronized(this){
			if (tcp!=null) {
				return tcp.getAnimationCount();
			}
			}
		}else if (part!=null) {
			return part.getAnimationCount();
		}
		return 2;
	}
	@Override
	public void setFly(String skin) {
		if (lvl==1) {
			if (skin==null) {
				isLanding = true;
				bodyAlpha = 1.0f;
			} else {
				isLanding = false;
				bodyAlpha = 1.0f;
				flyPart=SpriteFactory.createPart(skin, -3, 1, null);
			}
			return;
		}
		if (part!=null) {
			part.setFly(skin);
		}
	}
	/**
	 * 根据传入的 act 和 petskin 参数获取对应的 skin 值。
	 * @param act      动作编号
	 * @param petskin  宠物皮肤编号
	 * @return 对应的 skin 值，如果未找到则返回 null
	 */
	public static String getSkinValue(int act, String petskin) {
		try (InputStream inputStream = PartOne.class.getResourceAsStream("/PetActionType.xml")) {
			if (inputStream == null) {
				throw new IOException("Resource not found: PetActionType.xml");
			}
			// 创建DocumentBuilderFactory对象
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			// 创建DocumentBuilder对象
			DocumentBuilder builder = factory.newDocumentBuilder();
			// 直接从输入流解析XML文件
			Document doc = builder.parse(inputStream);
			// 获取根元素
			Element root = doc.getDocumentElement();
			// 查找匹配的Dialog元素
			NodeList dialogList = root.getElementsByTagName("Dialog");
			for (int i = 0; i < dialogList.getLength(); i++) {
				Node dialogNode = dialogList.item(i);
				if (dialogNode.getNodeType() == Node.ELEMENT_NODE) {
					Element dialogElement = (Element) dialogNode;
					String dialogPetSkin = dialogElement.getAttribute("petskin");
					// 检查是否匹配指定的petskin
					if (dialogPetSkin.equals(petskin)) {
						// 查找匹配的menu元素
						NodeList menuList = dialogElement.getElementsByTagName("menu");
						for (int j = 0; j < menuList.getLength(); j++) {
							Node menuNode = menuList.item(j);
							if (menuNode.getNodeType() == Node.ELEMENT_NODE) {
								Element menuElement = (Element) menuNode;
								int menuAct = Integer.parseInt(menuElement.getAttribute("act"));
								// 检查是否匹配指定的act
								if (menuAct == act) {
									// 返回skin属性的值
									return menuElement.getAttribute("skin");
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null; // 如果未找到匹配项，返回null
	}

	/**记录已加载的Sprite*/
	public static final Map<String, SoftReference<Sprite>> TcpMap = new ConcurrentHashMap<>();

	private static final Pattern HEX_PATTERN = Pattern.compile("0x([A-F0-9]+)");


	// 添加一个强引用缓存，用于存储常用的状态
	private static final Map<String, Sprite> strongCache = new ConcurrentHashMap<>();

	public static Sprite sprite(int act, String skin) {
		String skinValue = Objects.requireNonNull(getSkinValue(act, skin));
		// 第一次检查（无锁）
		Sprite sprite = strongCache.get(skinValue);
		if (sprite != null) {
			return sprite;
		}
		// 第二次检查（带锁）
		synchronized (TcpMap) {
			sprite = strongCache.get(skinValue);
			if (sprite == null) {
				SoftReference<Sprite> ref = TcpMap.get(skinValue);
				sprite = ref != null ? ref.get() : null;

				if (sprite == null) {
					Matcher matcher = HEX_PATTERN.matcher(skinValue);
					if (!matcher.find()) {
						return null;
					}
					String prefix = matcher.group(1);
					sprite = Prepare(prefix, skinValue);
					if (sprite != null) {
						TcpMap.put(skinValue, new SoftReference<>(sprite));
					}
				}
			}
		}
		return sprite;
	}
	public static Sprite Prepare(String path,String skinValue) {
		switch (path.charAt(0)) {
			case 'A':
				return WdfLoaDing.newdynamic(skinValue, "shape2.wdf");
			case 'B':
				return WdfLoaDing.newdynamic(skinValue, "shape1.wdf");
			case 'C':
				return WdfLoaDing.newdynamic(skinValue, "shape3.wdf");
			case 'F':
				return WdfLoaDing.newdynamic(skinValue, "shape4.wdf");
			case 'D':
				return WdfLoaDing.newdynamic(skinValue, "magic.wdf");
			case 'E':
				return WdfLoaDing.newdynamic(skinValue, "effect.wdf");
			default:
				return null;
		}
	}


	public static void main(String[] args) {
		// 起始值和结束值
		long start =198;
		long end = 300;
		// 定义 act 值数组
		int[] acts = {0,1, 2, 4, 5, 6, 7, 8,9, 10};
		String[] actss = {"walk 走","run 跑", "stand 站立", "hit 被攻击", "magic 法术", "defend 防御", "guard 进入战斗","die 死", "attack 攻击", "attackrun 跑动攻击"};
		// 计算总共需要多少组 Dialog
		int totalGroups = (int) ((end - start + 1) / 10);
		// 生成 XML
		for (int group = 1; group < totalGroups; group++) {
			// 生成 XML 开始部分
			System.out.println("<Dialog petskin=\"400066\"><!-- 名字 -->");
			// 循环生成并打印每个 <menu> 元素
			for (int i = 0; i < acts.length; i++) {
				long skinValue = start + (group * 10) + i;
				String pureHexValueWithPrefix = "0xA273F" + skinValue;
				// 输出去掉前缀的纯数字部分
				System.out.println("\t<menu act=\"" + acts[i] + "\" skin=\"" + pureHexValueWithPrefix + "\" ></menu><!-- " + actss[i] + "-->");
			}
			// 生成 XML 结束部分
			System.out.println("</Dialog>");
		}
	}

	public double getBodyAlpha() {
		return bodyAlpha;
	}

	// 获取当前影子偏移量
	public float getCurrentShadowOffset() {
		if (isLanding) {
			currentShadowOffset = Math.max(0, currentShadowOffset - SHADOW_STEP);
		}
		return currentShadowOffset;
	}

	// 新增：设置透明度的方法
	public void setBodyAlpha(double alpha) {
		this.bodyAlpha = alpha;
	}

}
