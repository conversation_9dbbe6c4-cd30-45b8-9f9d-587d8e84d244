package jxy2.wdfDome.view;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class ItemFrame extends JDialog implements MouseListener, Runnable {
    private AnimationPane apane;

    public ItemFrame(J<PERSON>rame frame, AnimationPane apane) {
        super(frame, false);
        this.setTitle("详细");
        int width = apane.getItemBufImages()[0].getWidth();
        int height = apane.getItemBufImages()[0].getHeight();
        this.setSize(width + 50, height + 100);
        this.setLocationRelativeTo(frame);
        this.addMouseListener(this);
        this.apane = apane;
        this.add(apane);
        this.setVisible(true);
        (new Thread(this)).start();
    }

    @Override
    public void mouseClicked(MouseEvent e) {
        this.setVisible(false);
    }

    @Override
    public void mousePressed(MouseEvent e) {

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    @Override
    public void run() {
        while(true) {
            try {
                if (this.isVisible()) {
                    this.repaint();
                    Thread.sleep(100L);
                    continue;
                }
            } catch (InterruptedException var2) {
                InterruptedException e = var2;
                e.printStackTrace();
            }

            return;
        }
    }
}
