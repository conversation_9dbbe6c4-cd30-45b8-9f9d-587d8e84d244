package jxy2.wdfDome.util;

import jxy2.wdfDome.bean.WasData;

import java.awt.image.BufferedImage;

public class ImageWithWasData {
    private final BufferedImage image;
    private final WasData wasData;

    public ImageWithWasData(BufferedImage image, WasData wasData) {
        this.image = image;
        this.wasData = wasData;
    }

    public BufferedImage getImage() {
        return image;
    }

    public WasData getWasData() {
        return wasData;
    }
}