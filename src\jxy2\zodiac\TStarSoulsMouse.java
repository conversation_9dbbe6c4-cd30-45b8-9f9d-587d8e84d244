package jxy2.zodiac;

import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.entity.Goodstable;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.MessagrFlagUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class TStarSoulsMouse implements MouseListener {
    public TStarSoulsJPanel tStarSoulsJPanel;
    public int caozuo ,type ;
    public static List<String> list = new ArrayList<>();
    public TStarSoulsMouse(int caozuo, TStarSoulsJPanel tStarSoulsJPanel,int type) {
        this.caozuo = caozuo;
        this.type = type;
        this.tStarSoulsJPanel = tStarSoulsJPanel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }


    @Override
    public void mousePressed(MouseEvent e) {
        if (type == 0) {
            Goodstable goods = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[caozuo]);
            if (goods==null)return;
            // 共36个每组6个，计算点击的是哪一组的哪一个
            if (e.getButton() == MouseEvent.BUTTON3) { // 右键点击选中一组
            //TODO 调整加入物品的数量
            int sum = GoodsListFromServerUntil.Surplussum(goods.getType() + "", goods.getGoodsid() + "",
				goods.getUsetime());
             if ((tStarSoulsJPanel.getGoodstableMap().size()) <7) {
                 tStarSoulsJPanel.getGoodstableMap().put(goods.getRgid(), sum);
             }
            } else if (e.getButton() == MouseEvent.BUTTON1) { // 左键选中一个
                if ((tStarSoulsJPanel.getGoodstableMap().size()) <7 &&!tStarSoulsJPanel.getGoodstableMap().containsKey(goods.getRgid())) {
                    tStarSoulsJPanel.getGoodstableMap().put(goods.getRgid(), 1);
                }else {
                    for (BigDecimal key : tStarSoulsJPanel.getGoodstableMap().keySet()) {
                        Goodstable goodstable = GoodsListFromServerUntil.getRgid(key);
                        if (goodstable!=null&&goodstable.getGoodsid().intValue()==goods.getGoodsid().intValue()) {
                            int sum = tStarSoulsJPanel.getGoodstableMap().get(key);
                            if (goods.getUsetime() > sum) {
                                sum += 1;
                            } else {
                                sum = goods.getUsetime();
                            }
                            tStarSoulsJPanel.getGoodstableMap().put(goods.getRgid(), sum);
                        }
                    }
                }
            }
            tStarSoulsJPanel.getGoodLabels()[caozuo].setBorder(BorderFactory.createLineBorder(Color. red, 2));
            for (int i = 0; i < tStarSoulsJPanel.getGoodLabels().length; i++) {
                if (i != caozuo) {
                    tStarSoulsJPanel.getGoodLabels()[i].setBorder(BorderFactory.createEmptyBorder());
                }
            }
            list.add(caozuo + "");
        } else {
            int i = 0;
              for (BigDecimal key :tStarSoulsJPanel.getGoodstableMap().keySet()){
                    if (caozuo==i) {
                        tStarSoulsJPanel.getGoodstableMap().remove(key);
                        tStarSoulsJPanel.getLastLabels()[i].setIcon(null);
                        break;
                    }
                    i++;
            }
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        list.remove(caozuo + "");
    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (type==0){
            MsgJframe.getJframe().getJapnel().StarChartGoods(caozuo,0);
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }
}
