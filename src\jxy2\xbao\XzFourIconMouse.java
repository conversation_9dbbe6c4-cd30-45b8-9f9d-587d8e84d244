package jxy2.xbao;

import come.tool.JDialog.TiShiUtil;
import org.come.Frame.MsgJframe;
import org.come.Frame.OptionsJframe;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import java.awt.event.MouseEvent;

public class XzFourIconMouse extends TemplateMouseListener {
    private int index;
    private XbaoJpanel xbaoJpanel;
    public XzFourIconMouse(int index,XbaoJpanel xbaoJpanel) {
        this.index = index;
        this.xbaoJpanel = xbaoJpanel;
    }


   // 添加成员变量保存原始位置
    private int originalX;
    private int originalY;

    @Override
    protected void specificMousePressed(MouseEvent e) {


        // 保存原始位置
        originalX = xbaoJpanel.getXzfouricon()[index].getX();
        originalY = xbaoJpanel.getXzfouricon()[index].getY();
        // 设置按下时的偏移效果（1像素）
        xbaoJpanel.getXzfouricon()[index].setBounds(originalX + 1, originalY + 1, 64, 64);


//
//
//        RoleXuanbao.getRoleXuanbao().xuanyinchange(RoleXuanbao.getRoleXuanbao().choseBao, GoodsListFromServerUntil.fushis.get(new BigDecimal(539000)), false);
    }

    /**根据等级品质来获取玄钰的数量*/
    public String getXuanYuNum(int lv) {
        if (lv>=40&&lv<=59){
            return "#R2#W";
        }else if (lv>=60&&lv<=79){
            return "#R4#W";
        }else if (lv>=80&&lv<=99){
            return "#R8#W";
        }else if (lv>=100&&lv<=149){
            return "#R12#W";
        }else if (lv>=150&&lv<=199){
            return "#R20#W";
        }
        return "";
    }


    @Override
    protected void specificMouseReleased(MouseEvent e) {
        // 恢复原始位置
        xbaoJpanel.getXzfouricon()[index].setBounds(originalX, originalY, 64, 64);


        //判断等级是否达标
        String[] split = RoleXuanbao.getRoleXuanbao().choseBao.getBaomarkactive().split("\\|");
        if (split[index].equals("1")){
            Util.StopFrame(155);
            XuanyinInlayFrame.getXuanyinInlayFrame().getXuanyinInlayJPanel().TransmittingColors(xbaoJpanel.getStorecolors()[index].getText());
            System.out.println("开始镶嵌打开玄印背包");
            return;
        }


        if (RoleXuanbao.getRoleXuanbao().choseBao.getBaolvl().intValue()>=xbaoJpanel.maxlv[index]){
            OptionsJframe.getOptionsJframe().getOptionsJpanel()
                    .showBox(TiShiUtil.openXuanYin, RoleXuanbao.getRoleXuanbao().choseBao.getId()+"|"+index,
                            "#W消耗#G"+getXuanYuNum(xbaoJpanel.maxlv[index])+
                                    "#W个#G玄钰#W有机率解锁此玄印槽。是否确认解锁？#r#R   解锁机率为"+(100-(xbaoJpanel.maxlv[index]-20))+"%#r#Y   当前剩余：999");
        }

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        if (RoleXuanbao.getRoleXuanbao().choseBao==null)return;
        //找颜色
        Xuanbao xuanbao = UserMessUntil.getXuanabao(RoleXuanbao.getRoleXuanbao().choseBao.getBaoname());
        if (xuanbao!=null){
            boolean flag = RoleXuanbao.getRoleXuanbao().choseBao.getBaolvl().intValue()>=xbaoJpanel.maxlv[index];
            MsgJframe.getJframe().getJapnel().XuanbaoMs(
                    xbaoJpanel.getStorecolors()[index].getText(),//颜色
                    index,//标识
                    flag,//是否达到对应等
                    RoleXuanbao.getRoleXuanbao().choseBao);//品质
        }



    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        FormsManagement.HideForm(46);
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}