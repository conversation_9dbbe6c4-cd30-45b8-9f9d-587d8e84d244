package jxy2.wdfDome.util;

import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteHead;

import java.io.File;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.channels.Channels;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Callable;

public class SpriteLoadTask  implements Callable<Sprite>
{
    private final File file;
    private final long offset;

    public SpriteLoadTask(File file, long offset) {
        this.file = file;
        this.offset = offset;
    }
    @Override
    public Sprite call() throws Exception {
        String cacheKey = file.getAbsolutePath() + ":" + offset;
        if (WasTool.spriteCache.containsKey(cacheKey)) {
            return WasTool.spriteCache.get(cacheKey);
        }

        Sprite sprite = null;
        try (RandomAccessFile in = new RandomAccessFile(file, "r")) {
            in.seek(offset);
            InputStream inputStream = Channels.newInputStream(in.getChannel());
            byte[] buf = new byte[2];
            inputStream.read(buf);
            String fag = new String(buf, StandardCharsets.UTF_8);
            int version = fag.equals("SP") ? 0 : fag.equals("SH") ? 1 : -1;
            if (version >= 0) {
                buf = new byte[inputStream.available()];
                int count = 0;
                while (inputStream.available() > 0) {
                    count += inputStream.read(buf, count, inputStream.available());
                }
                SpriteHead randomIn = new SpriteHead(buf);
                sprite = randomIn.init(null, false, version);
                WasTool.spriteCache.put(cacheKey, sprite);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sprite;
    }
}
