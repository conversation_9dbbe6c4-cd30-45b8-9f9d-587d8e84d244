package jxy2.refine;

import com.tool.btn.DazaoBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.Arrays;

/**
* 符石操作
* <AUTHOR>
* @date 2024/8/1 下午8:11
*/
public class SymbolJpanel extends JPanel {
    public String[] textSymbol;
    private SymbolBtn[] btnSymbol = new SymbolBtn[2];
    private DazaoBtn workshopBtn;
    private int minType = 0;
    private BigDecimal money = new BigDecimal(0);// 消耗金钱
    public JLabel[] fiveEquipment = new JLabel[5];//五
    public Goodstable[] goods = new Goodstable[2];
    public Goodstable[] goods1 = new Goodstable[5];
    public SymbolJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        textSymbol = new String[]{"重铸","升级"};
        for (int i = 0; i < btnSymbol.length; i++) {
            btnSymbol[i] = new SymbolBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, textSymbol[i], i, this,"");
            btnSymbol[i].btnchange(i==0?2:0);
            btnSymbol[i].setBounds(138+i*66, 48, 66, 24);
            add(btnSymbol[i]);
        }



        workshopBtn = new DazaoBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "洗练", this);
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);

        for (int i = 0; i < fiveEquipment.length; i++) {
            fiveEquipment[i] = new JLabel();
            fiveEquipment[i].addMouseListener(new RefineMouse(this, 24 + i));
            this.add(fiveEquipment[i]);
        }
    }

    public void ClickSuit(Goodstable good, int path) {
        if (path < 24) {
            if (good.getType() != 188) {return;}
                if (minType==0) {
                    for (int i = 0; i < 2; i++) {
                        if (goods[i] == null) {
                            changesui(good, i);
                            good.setIsSelected(1);
                            break;
                        }
                    }
                }else {
                    for (int i = 0; i < 5; i++) {
                        if (goods1[i] == null) {
                            changesui(good, i);
                            good.setIsSelected(1);
                            break;
                        }
                    }
                }


        }else {
            if (minType==0){
                goods[path - 24].setIsSelected(0);
            }else {
                goods1[path - 24].setIsSelected(0);
            }

            changesui(null, path - 24);
        }
        String v = detectionSuit();
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }

    private String detectionSuit() {
        return workshopBtn.getText();
    }

    private void changesui(Goodstable good, int i) {
        if (minType==0){
            goods[i] = good;
        }else {
            goods1[i] = good;
        }
        if (good != null) {
            fiveEquipment[i].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        }else {
            fiveEquipment[i].setIcon(null);
            money = null;
        }
    }

    public void clear() {
        Arrays.fill(goods, null);
        Arrays.fill(goods1, null);
        int min = minType==0?2:5;
        for (int i = 0; i < min; i++) {
            fiveEquipment[i].setIcon(null);
            fiveEquipment[i].setVisible(true);
            fiveEquipment[i].setBorder(BorderFactory.createEmptyBorder());
        }
        //隐藏多余的
        for (int i = min; i < 5; i++) {
            fiveEquipment[i].setVisible(false);
            fiveEquipment[i].setText("");
        }
        money = null;

    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        switch (minType){
            case 0:
                for (int i = 0; i < 2; i++) {
                    Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
                    if (fiveEquipment[i].getIcon()==null) {
                        Juitil.TextBackground(g, "符石", 13, 226 + i * 134, 202 + 18, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    }else {
                        //绘制物品等级
                        String v = goods[i].getValue().split("\\|")[0].split("=")[1];
                        if (!v.isEmpty()){
                            Juitil.Textdrawing(g,v+"级", 245 + i * 134, 252 , UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                        }
                    }
                    fiveEquipment[i].setBounds(215 + i * 134,202,50,50);
                }
                break;
            case 1:
                int centerX = 282; // 圆心的X坐标
                int centerY = 203; // 圆心的Y坐标
                int radius = 95;  // 圆的半径
                int  numElements = 5; // 元素数量
                double  angleIncrement = Math.PI / numElements * 2 ; // 角度增量，注意这里是π/3，因为我们只需要两次增量
                double  startAngle = Math.PI / 2; // 起始角度，指向正上方
                for (int i = 0; i < numElements; i++) {
                    double angle = startAngle + i * angleIncrement; // 当前元素的角度
                    int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                    int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                    Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1); // 假设Juitil.ImngBack是用来绘制图像的方法
                    if (fiveEquipment[i].getIcon()==null) {
                        Juitil.TextBackground(g, "符石",13, x+11,y+18,UIUtils.COLOR_goods_quantity,UIUtils.FZCY_HY13);
                    }else {
                        //绘制物品等级
                        String v = goods1[i].getValue().split("\\|")[0].split("=")[1];
                        if (!v.isEmpty()){
                            Juitil.Textdrawing(g,v+"级", x+15, y+62 , UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                        }
                    }
                    fiveEquipment[i].setBounds(x,y,50,50);

                }
                Juitil.TextBackground(g, "5个除5级符石外任意同级别可升级高一级的符石", 13, 140, 410, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                break;



        }
        for (int i = 0; i < 2; i++) {
            Juitil.ImngBack(g, Juitil.tz26, 500, 334+i*25, 121, 23, 1);
        }
        Juitil.TextBackground(g, "消耗金钱", 13, 440, 310+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        Juitil.TextBackground(g, "拥有金钱", 13, 440, 335+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        // 画现金
        Util.drawMoney(g, 504, 374);
        // 画消耗金钱
        if (money != null)
            Util.drawPrice(g, money, 504, 374-25);
    }

    public SymbolBtn[] getBtnSymbol() {
        return btnSymbol;
    }

    public void setBtnSymbol(SymbolBtn[] btnSymbol) {
        this.btnSymbol = btnSymbol;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public DazaoBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(DazaoBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }
    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String[] getTextSymbol() {
        return textSymbol;
    }

    public void setTextSymbol(String[] textSymbol) {
        this.textSymbol = textSymbol;
    }

    public JLabel[] getFiveEquipment() {
        return fiveEquipment;
    }

    public void setFiveEquipment(JLabel[] fiveEquipment) {
        this.fiveEquipment = fiveEquipment;
    }

    public Goodstable[] getGoods() {
        return goods;
    }

    public void setGoods(Goodstable[] goods) {
        this.goods = goods;
    }


}
