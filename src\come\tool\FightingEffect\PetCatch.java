package come.tool.FightingEffect;

import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.FightingState;
import come.tool.Fighting.SkillSpell;
import come.tool.Fighting.StateProgress;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.bean.SummonPetBean;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;
import org.come.until.ScrenceUntil;

import java.math.BigDecimal;

public class PetCatch implements Effect {

	@Override
	public StateProgress analysisAction(FightingState State, int path) {
		// TODO Auto-generated method stub
		StateProgress Active = EffectType.getProgress(State, path);
		SkillSpell skillSpell = new SkillSpell("捕捉", ScrenceUntil.Screen_x / 2,ScrenceUntil.Screen_y / 2);
		skillSpell.setSkinpath(1);
		Active.setSpell(skillSpell);
		Active.setType(7);
		if (State.getStartState().equals("捕捉失败")) {
			// //捕捉失败
			
		} else {
			Active.setEscape(1);
			String[] v = State.getEndState().split("\\|");
			int camp = Integer.parseInt(v[0]);
			int man = Integer.parseInt(v[1]);
			int myman = FightingMixDeal.myman();
			if (camp == FightingMixDeal.camp && myman == man) {
//				int x =  TestPetJframe.getTestPetJframe().getPetMainJPanel().getPetsum();
//				if (UserMessUntil.getPetListTable() != null && UserMessUntil.getPetListTable().size() >= x) {
//					ZhuFrame.getZhuJpanel().addPrompt2("您的召唤兽可携带的数量已满！！！");
//					return Active;
//				}
				// 获取召唤兽
				SummonPetBean summonPetBean = new SummonPetBean();
				summonPetBean.setPetid(new BigDecimal(v[2]));
				String mes = Agreement.getAgreement().summonpetAgreement(GsonUtil.getGsonUtil().getgson().toJson(summonPetBean));
				// 向服务器发送信息
				SendMessageUntil.toServer(mes);
				FrameMessageChangeJpanel.addtext(5,"获得您成功捕获了召唤兽",null,null);
			}
		}
		return Active;
	}

}
