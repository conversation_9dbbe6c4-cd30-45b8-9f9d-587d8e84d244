package com.tool.ModerateTask;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.come.bean.Coordinates;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import com.tool.tcpimg.InputBean;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;

public class Task {
	/**任务id*/
	private int taskId;
	/**任务状态*/
	private int taskState;
	/**任务进度   */
	private List<TaskProgress> progress;
	/**任务超时*/
	private long time;
	
	/**任务配置表*/
	private transient TaskData taskData;
	/**任务追踪*/
	private transient RichLabel richLabel;
	public Task(int taskId, int taskState) {
		super();
		this.taskId = taskId;
		this.taskState = taskState;
	}
    public void reset(){
    	if (progress!=null) {progress.clear();}
    	this.time=0;
    	this.taskData=null;
	}
	/**将任务进度拆分*/
	public void addProgress(String[] values){
		if (progress!=null) {progress.clear();}
		else {progress=new ArrayList<>();}
        if (values!=null) {
        	//任务id=任务状态=T过期时间=PTYPE_S1-1_MMAP-X-Y_DID-NAME_GID-NAME;
    		for (int i = 2; i < values.length; i++) {
    			if (values[i].startsWith("P")) {
    				String[] vs=values[i].split("_");
    				TaskProgress taskProgress=new TaskProgress(this);
    				taskProgress.setType(Integer.parseInt(vs[0].substring(1)));
    				taskProgress.setSum(0);
    				taskProgress.setMax(1);
    				for (int j = 1; j < vs.length; j++) {
    					if (vs[j].startsWith("S")) {
    						String[] ts=vs[j].split("-");
    						taskProgress.setSum(Integer.parseInt(ts[0].substring(1)));
    						taskProgress.setMax(Integer.parseInt(ts[1]));
    					}else if (vs[j].startsWith("M")) {
    						String[] ts=vs[j].split("-");
    						taskProgress.setMap(Integer.parseInt(ts[0].substring(1)));
    						taskProgress.setX(Integer.parseInt(ts[1]));
    						taskProgress.setY(Integer.parseInt(ts[2]));
    					}else if (vs[j].startsWith("D")) {
    						String[] ts=vs[j].split("-");
    						taskProgress.setDId(Integer.parseInt(ts[0].substring(1)));
    						taskProgress.setDName(ts[1]);
    					}else if (vs[j].startsWith("G")) {
    						String[] ts=vs[j].split("-");
    						taskProgress.setGId(Integer.parseInt(ts[0].substring(1)));
    						taskProgress.setGName(ts[1]);
    					}
    				}
    				progress.add(taskProgress);
    			}else if (values[i].startsWith("T")) {
    				this.time=Long.parseLong(values[i].substring(1))*1000;
    			}	
    		}
        }
		if (progress.size()==0) {
			richLabel=null;
		}else {
			StringBuffer buffer=new StringBuffer();
			buffer.append("#Y");
			buffer.append(getTaskData().getTaskName());
			buffer.append("#W[");
			buffer.append(UserMessUntil.getTaskSet(getTaskData().getTaskSetID()).getTaskType());
			buffer.append("]");
			for (int i = 0; i < progress.size(); i++) {
				TaskProgress ps=progress.get(i);
				buffer.append("#r");
				if (ps.getType()==0||ps.getType()==1||ps.getType()==2){
					buffer.append("击杀");	
				}else if (ps.getType()==3) {
					buffer.append("问候");
				}else if (ps.getType()==4) {
					buffer.append("将");
					buffer.append(ps.getDName());
					buffer.append("送给");
				}else if (ps.getType()==5) {
					buffer.append("护送");
					buffer.append(ps.getDName());
					buffer.append("到");
				}
				if (ps.getMap()==0) {
				    buffer.append((ps.getType()==4||ps.getType()==5)?ps.getGName():ps.getDName());
			    }else {
				    Coordinates coordinates=new Coordinates(ps.getMap(), ps.getX(), ps.getY());
				    InputBean inputBean=new InputBean(null,ps.getType()+20,new BigDecimal((ps.getType()==4||ps.getType()==5)?ps.getGId():ps.getDId()),(ps.getType()==4||ps.getType()==5)?ps.getGName():ps.getDName(),"G",coordinates);
				    buffer.append("#V");
				    buffer.append(GsonUtil.getGsonUtil().getgson().toJson(inputBean));
				    buffer.append("#L");
			    }
				if (ps.getType()==0||ps.getType()==1||ps.getType()==2||ps.getType()==4) {
				    buffer.append(" ");
				    buffer.append(ps.getSum());
				    buffer.append("/");
				    buffer.append(ps.getMax());	
			    }
			}
			if (richLabel==null) {
				richLabel=new RichLabel(buffer.toString(), UIUtils.TEXT_FONT, 165);
			}else {
				richLabel.setTextSize(buffer.toString(), 165);
			}
		}
	}
	/**
	 * 完成部分
	 * false无关 true有关
	 * @return
	 */
	public int PartFinish(String type,String name){
		for (int i = 0; i < progress.size(); i++) {
			TaskProgress Progre=progress.get(i);
			if (Progre.getSum()>=Progre.getMax()) {
				continue;
			}
			if (name.equals((Progre.getType()!=5?Progre.getDName():Progre.getGName()))) {
				if ((Progre.getType()==0||Progre.getType()==1||Progre.getType()==2)&&type.equals("击杀")) {
					return 3;	
				}else if ((Progre.getType()==3||Progre.getType()==5)&&type.equals("问候")) {
					return 1;
				}else if (Progre.getType()==4&&type.equals("给予物品")) {
					return 2;
				}
			}
		}		
		return 0;
	}
	/**任务是否完成*/
	public boolean isFinish(){
		if (taskState==TaskState.doTasking) {
			if (progress==null) {
				taskState=TaskState.completeTask;
				return true;
			}
			for (int i = 0; i < progress.size(); i++) {
				if (progress.get(i).getSum()<progress.get(i).getMax()) {
					return false;
				}
			}
			taskState=TaskState.completeTask;
			return true;	
		}
		return false;
	}
	/**
	 * 任务超时判断
	 * @return
	 */
	public boolean isOverTime(){
		if (time==0)return false;
		if (time<=Util.getTime())return true;
		return false;
	}
	/**返回还未完成的任务 自定义野怪  击杀NPC  问候NPC*/
	public TaskProgress getTaskProgress(){
		for (int i = 0; i < progress.size(); i++) {
			TaskProgress taskProgress=progress.get(i);
			if (taskProgress.getType()==1||taskProgress.getType()==2||taskProgress.getType()==3) {
				if (taskProgress.getSum()<taskProgress.getMax()) {
					return taskProgress;
				}	
			}
		}
		return null;
	}
	public int getTaskId() {
		return taskId;
	}
	public void setTaskId(int taskId) {
		this.taskId = taskId;
	}
	public int getTaskState() {
		return taskState;
	}
	public void setTaskState(int taskState) {
		this.taskState = taskState;
	}
	public List<TaskProgress> getProgress() {
		return progress;
	}
	public void setProgress(List<TaskProgress> progress) {
		this.progress = progress;
	}
	public long getTime() {
		return time;
	}
	public void setTime(long time) {
		this.time = time;
	}
	public TaskData getTaskData() {
		if (taskData==null) {
			taskData=UserMessUntil.getTaskData(taskId);
		}
		return taskData;
	}
	public void setTaskData(TaskData taskData) {
		this.taskData = taskData;
	}
	public RichLabel getRichLabel() {
		return richLabel;
	}
}
