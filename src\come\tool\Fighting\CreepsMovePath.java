package come.tool.Fighting;

import java.util.List;

import org.come.bean.PathPoint;

import com.tool.image.ManimgAttribute;

public class CreepsMovePath {
	private int x;//原本x位置
	private int y;//原本y位置
	private int Z;//移动进度
	private int time;
	private List<PathPoint> points;
	public CreepsMovePath(int x, int y, int time, List<PathPoint> points) {
		super();
		this.x = x;
		this.y = y;
		this.time = time;
		this.points = points;
	}
	public boolean isMove(ManimgAttribute attribute,long addTime,int dir){
		time+=addTime;
		if (time<=0) {return false;}
		if (Z>=points.size()) {return true;}
		PathPoint point = points.get(Z);
		int dx = point.getX()-x;
		int dy = point.getY()-y;
		long endtime = FightingMove2.gettime(dx, dy, 0.07);
		if (time>=endtime) {
			time-=endtime;
			Z++;
			x=point.getX();
			y=point.getY();
			attribute.setX(point.getX());
			attribute.setY(point.getY());
			attribute.getMapMonsterBean().setX(point.getX());
			attribute.getMapMonsterBean().setY(point.getY());
			return isMove(attribute, 0, dir);
		}
		dir = FightingMove2.getdir(dx, dy, dir);
		attribute.setX(FightingMove2.getmove2(x,point.getX(),time,endtime));
		attribute.setY(FightingMove2.getmove2(y,point.getY(),time,endtime));
		attribute.getMapMonsterBean().setX(attribute.getX());
		attribute.getMapMonsterBean().setY(attribute.getY());
		attribute.setDir(dir);
		attribute.setType(1);
		return false;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public int getTime() {
		return time;
	}
	public void setTime(int time) {
		this.time = time;
	}
	public List<PathPoint> getPoints() {
		return points;
	}
	public void setPoints(List<PathPoint> points) {
		this.points = points;
	}
}
