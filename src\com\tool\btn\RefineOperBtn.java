package com.tool.btn;

import com.tool.role.RoleData;
import jxy2.refine.EqartificeJapanel;
import jxy2.refine.RefineFrame;
import jxy2.refine.ReimpJpanel;
import org.come.Frame.GoodDetailedJframe;
import org.come.Frame.NewRefiningJframe;
import org.come.Frame.TaobaoCourtMainJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.NewRefiningJpanel;
import org.come.Jpanel.RefinersJpanel;
import org.come.Jpanel.RefiningEquiJpanel;
import org.come.bean.NpcComposeBean;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.starcard.JframeStarCardMain;
import org.come.test.Main;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RefineOperBtn extends MoBanBtn {

    private NewRefiningJpanel NrJpanel;
    private RefinersJpanel rJpanel;
    private RefiningEquiJpanel eJpanel;
    private int cao;
    private EqartificeJapanel refineJPanel;
    /** 0保留 1替换 2再次炼化 */
    public RefineOperBtn(String iconpath, int type, Color[] colors, Font font, NewRefiningJpanel nrJpanel, int cao,
            String text) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        NrJpanel = nrJpanel;
        this.cao = cao;
    }

    /** 0保留 1替换 2再次炼化 */
    public RefineOperBtn(String iconpath, int type, Color[] colors, Font font, String text, int cao, EqartificeJapanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.cao = cao;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }

    /** 3下拉框展示与隐藏 */
    public RefineOperBtn(String iconpath, int type, NewRefiningJpanel nrJpanel, int cao) {
        super(iconpath, type);
        NrJpanel = nrJpanel;
        this.cao = cao;
    }

    /** 0 ?按钮字在改变 */
    public RefineOperBtn(String iconpath, int type, Color[] colors, Font font, RefinersJpanel rJpanel, int cao,
            String text) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.rJpanel = rJpanel;
        this.cao = cao;
    }

    /** 0 ?按钮字在改变 */
    public RefineOperBtn(String iconpath, int type, Color[] colors, Font font, RefiningEquiJpanel eJpanel, int cao,
            String text) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.eJpanel = eJpanel;
        this.cao = cao;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        if (NrJpanel != null) {
            /** 0保留 1替换 2再次炼化 */
            if (cao == 0) {
                FormsManagement.HideForm(11);
            } else if (cao == 1) {
                if (NrJpanel.clBean != null) {
                    String senmes = Agreement.extrAttrOperAgreement(GsonUtil.getGsonUtil().getgson()
                            .toJson(NrJpanel.clBean));
                    SendMessageUntil.toServer(senmes);
                    NrJpanel.TH(NrJpanel.clBean);
                } else {
                    FormsManagement.HideForm(11);
                }
            } else if (cao == 2) {
                if (NrJpanel.leixing == 1) {
                    ReimpJpanel rJpanel = RefineFrame.getRefineFrame().getRefineJPanel().getCardJpanel().getReimpJpanel();
                    String v = rJpanel.detection();
                    if (v.equals("炼器")) {
                        cao1(rJpanel.goods, rJpanel.money, 1);
                    }
                } else if (NrJpanel.leixing == 0) {
                    //新界面仙器炼化-装备炼化
                    EqartificeJapanel refine = RefineFrame.getRefineFrame().getRefineJPanel().getCardJpanel().getEqartifice();
                    String v = RefiningUtil.detection(refine.goods, 1);
                    if (v.equals("炼化装备") || v.equals("炼化仙器") || v.equals("炼化神兵")) {
                        cao1(refine.goods, refine.money, 4);
                    }
                } else if (NrJpanel.leixing == 2 || NrJpanel.leixing == 3) {
                    JframeStarCardMain.getJframeSummonEquipMain().getJpanelStarCardMain().caoZuoStarCard();
                } else if (NrJpanel.leixing == 4) {
                    NrJpanel.selectArenaIndex();
                }
            } else if (cao == 3) {
                NrJpanel.showArenaDownLab();
            }
        } else if (rJpanel != null) {
            if (cao == 0) {
                String v = rJpanel.detection();
                if (v.equals("炼器")) {
                    String value = AccessSuitMsgUntil.getExtra(rJpanel.goods[0].getValue(), "炼器属性");
                    NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel().show(value, 1, true);
                } else if (v.equals("?")) {
                    ZhuFrame.getZhuJpanel().addPrompt2("公式不对");
                } else if (v.equals("清除")) {
                    cao1(rJpanel.goods, rJpanel.money, 3);
                } else if (v.equals("开光")) {
                    cao1(rJpanel.goods, rJpanel.money, 0);
                }
            }
        } else if (refineJPanel != null) {
           int ty= refineJPanel.getMinType()>=2?2:1;
           if (refineJPanel.getMinType()==5||refineJPanel.getMinType()==6){ty=1;}
            String v = RefiningUtil.detection(refineJPanel.goods, ty);
            if (v.equals("炼化装备") || v.equals("炼化仙器") || v.equals("炼化神兵")) {
                String value = AccessSuitMsgUntil.getExtra(refineJPanel.goods[0].getValue(), "炼化属性");
                NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel().show(value, 0, true);
            } else if (v.equals("装备培养") || v.equals("装备升级") || v.equals("装备重铸")) {
                if (RoleData.getRoleData().getLoginResult().getGold().longValue() < refineJPanel.money.longValue()) {
                    ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                    return;
                }
                palEquipUpgradeOrCultivate(refineJPanel.goods, v);
            } else {
                if (RoleData.getRoleData().getLoginResult().getGold().longValue() < refineJPanel.money.longValue()) {
                    ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                    return;
                }
                List<BigDecimal> rgids = new ArrayList<>();
                int size = 0;
                int p = -1;
                for (int i = 0; i < refineJPanel.goods.length; i++) {
                    if (refineJPanel.goods[i] == null) {
                        continue;
                    }
                    size++;
                    p = i;
                    if (refineJPanel.goods[i].getGoodlock() == 1) {
                        ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                        return;
                    }
                    if (GoodsListFromServerUntil.isExist(refineJPanel.goods[i])) {
                        return;
                    }
                    int sum = 1;
                    for (int j = 0; j < rgids.size(); j++) {
                        if (refineJPanel.goods[i].getRgid().compareTo(rgids.get(j)) == 0) {
                            sum++;
                        }
                    }
                    if (sum > refineJPanel.goods[i].getUsetime()) {
                        ZhuFrame.getZhuJpanel().addPrompt2("请凑齐物品再来");
                        return;
                    }
                    rgids.add(refineJPanel.goods[i].getRgid());
                }
                if (p + 1 != size) {
                    ZhuFrame.getZhuJpanel().addPrompt2("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                    return;
                }

                if (v.equals("佩饰培养") || v.equals("一键培养")) {
                    cao3(refineJPanel.goods, refineJPanel.money, rgids);
                } else if (v.equals("秘石合成")) {
                    cao4(refineJPanel.goods, refineJPanel.money, v, rgids);
                } else {
                    cao2(refineJPanel.goods, refineJPanel.money, v, rgids);
                }
            }
            if (refineJPanel.getMinType()>=7&&refineJPanel.getMinType()<=9){
//                v = refineJPanel.detection();
                if (v.equals("炼器")) {
                    String value = AccessSuitMsgUntil.getExtra(refineJPanel.goods[0].getValue(), "炼器属性");
                    NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel().show(value, 1, true);
                } else if (v.equals("?")) {
                    ZhuFrame.getZhuJpanel().addPrompt2("公式不对");
                } else if (v.equals("清除")) {
                    cao1(refineJPanel.goods, refineJPanel.money, 3);
                } else if (v.equals("开光")) {
                    cao1(refineJPanel.goods, refineJPanel.money, 0);
                }
            }
        }
    }




    /** 0开光 1炼器 3清除炼器 4炼化 */
    public boolean cao1(Goodstable[] goods, BigDecimal money, int type) {
        NewRefiningJpanel NrJpanel = null;
        if (this.NrJpanel == null) {
            NrJpanel = NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel();
        } else {
            NrJpanel = this.NrJpanel;
        }
        int lock = 0;
        int num = 0;
        if (type == 4 || type == 1) {
            lock = NrJpanel.getlock();
            num = lock % 10;
            lock = lock / 10;
        }
        BigDecimal xy = null;
        if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y金钱不足");
            return false;
        }
        if (num > 3) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("最多只能锁定3个");
            return false;
        } else if (num > 0) {
            if (type == 4
                    && (Goodtype.GodEquipment_xian(goods[0].getType()) || Goodtype.GodEquipment_God(goods[0].getType()))) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("仙器和神兵无法锁定连化");
                return false;
            }
            if (num == 1) {
                if (type == 4) {
                    xy = new BigDecimal(100);
                } else {
                    xy = new BigDecimal(1000);
                }
            } else if (num == 2) {
                if (type == 4) {
                    xy = new BigDecimal(500);
                } else {
                    xy = new BigDecimal(3000);
                }
            } else if (num == 3) {
                if (type == 4) {
                    xy = new BigDecimal(2500);
                } else {
                    xy = new BigDecimal(6000);
                }
            }
            if (RoleData.getRoleData().getLoginResult().getCodecard().longValue() < xy.longValue()) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("仙玉不足");
                return false;
            }
        }
        List<BigDecimal> rgids = new ArrayList<>();
        int size = 0;
        int p = -1;
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] != null) {
                size++;
                p = i;
                if (goods[i].getGoodlock() == 1) {
                    ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                    return false;
                }
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return false;
                }
                int sum = 1;
                for (int j = 0; j < rgids.size(); j++) {
                    if (goods[i].getRgid().compareTo(rgids.get(j)) == 0) {
                        sum++;
                    }
                }
                if (sum > goods[i].getUsetime()) {
                    ZhuFrame.getZhuJpanel().addPrompt2("请凑齐物品再来");
                    return false;
                }
                rgids.add(goods[i].getRgid());
            }
        }
        if (p + 1 != size) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
            return false;
        }
        SuitOperBean operBean = new SuitOperBean();
        if (type == 0) {
            if (size != 2) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼器属性");
            if (extra != null) {
                String[] extras = extra.split("&");
                if (Integer.parseInt(extras[1]) >= 5) {
                    FrameMessageChangeJpanel.addtext(5, "最大开光次数5", null, null);
                    return false;
                }
            }
            operBean.setType(10);
        } else if (type == 1) {
            if (size != 4) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼器属性");
            if (extra == null) {
                FrameMessageChangeJpanel.addtext(5, "先去开光", null, null);
                return false;
            }
            operBean.setType(11);
        } else if (type == 3) {
            if (size != 1) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            operBean.setType(13);
        } else if (type == 4) {
            if (AccessSuitMsgUntil.getExtra(goods[0].getValue(), "套装属性") != null) {
                FrameMessageChangeJpanel.addtext(5, "套装无法炼化", null, null);
                return false;
            }
            if (Goodtype.GodEquipment_xian(goods[0].getType())) {
                if (goods[1].getType() == 7005) {// 仙器阶数
                    String god = Goodtype.StringParsing(goods[1].getValue())[1];
                    if (!god.equals("阶数=1")) {
                        FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                        return false;
                    }
                } else if (Goodtype.GodEquipment_xian(goods[1].getType())) {
                    String god = Goodtype.StringParsing(goods[1].getValue())[0];
                    if (!god.equals("阶数=1")) {
                        FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                        return false;
                    }
                }
            }
            operBean.setType(14);
        }
        for (int i = 1; i < goods.length; i++) {
            if (goods[i] != null) {
                goods[i].goodxh(1);
                if (goods[i].getUsetime() <= 0) {
                    GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                }
            }
        }
        RoleData.getRoleData().getLoginResult()
                .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));
        if (xy != null) {
            RoleData.getRoleData().getLoginResult()
                    .setCodecard(RoleData.getRoleData().getLoginResult().getCodecard().subtract(xy));
            operBean.setJade(new PartJade(lock, 0));
            GoodDetailedJframe.getGoodDetailedJframe().getGoodDetailedJpanel().getYonghuXianyu()
                    .setText(RoleData.getRoleData().getLoginResult().getCodecard() + "");
            TaobaoCourtMainJframe.getTaobaoCourtJframe().getTaobaoCourtMainJpanel().getJadeNum()
                    .setText(RoleData.getRoleData().getLoginResult().getCodecard() + "");
        }
        operBean.setGoods(rgids);// 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        return true;
    }

    public void cao2(Goodstable[] goods, BigDecimal money, String type, List<BigDecimal> rgids) {
        NpcComposeBean npcComposeBean = new NpcComposeBean();
        if (type.equals("佩饰重铸")) {
            // 饰品描述信息
            String Bottletext = goods[0].getValue();
            String[] gongneng = Goodtype.StringParsing(Bottletext);
            // 矿石等级
            int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
            if (DazaoBtn.Numerical(gongneng[0]) != (kslvl - 3)) {
                FrameMessageChangeJpanel.addtext(5, "请使用" + (DazaoBtn.Numerical(gongneng[0]) + 3) + "级矿石进行重铸!", null,
                        null);
                return;
            }
            npcComposeBean.setComposetype("我要重铸饰品");
        } else if (type.equals("护身符培养")) {
            String[] vs = goods[0].getValue().split("\\|");
            int pz = 0;
            for (int i = 0; i < vs.length; i++) {
                String[] vsz = vs[i].split("=");
                if (vsz[0].equals("品质")) {
                    pz = Integer.parseInt(vsz[1].split("/")[0]);
                    break;
                }
            }
            int up = 800;
            String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼化属性");
            if (extra != null) {
                String[] vvs = extra.split("&");
                s: for (int i = 0; i < vvs.length; i++) {
                    String[] vvvs = vvs[i].split("=");
                    if (vvvs[0].equals("特技")) {
                        for (int j = 1; j < vvvs.length; j++) {
                            if (vvvs[j].equals("8031")) {
                                up = 900;
                                break s;
                            }
                        }
                    }
                }
            }
            if (pz > up) {
                FrameMessageChangeJpanel.addtext(5, "该护身符品质大于" + up + "后无法培养", null, null);
                return;
            }
            npcComposeBean.setComposetype("我要培养护身符");
        } else if (type.equals("护身符重铸")) {
            String[] vs = goods[0].getValue().split("\\|");
            int pz = 0;
            for (int i = 0; i < vs.length; i++) {
                String[] vsz = vs[i].split("=");
                if (vsz[0].equals("品质")) {
                    pz = Integer.parseInt(vsz[1].split("/")[0]);
                    break;
                }
            }
            if (pz < 300) {
                FrameMessageChangeJpanel.addtext(5, "该护身符品质低于300的需要培养", null, null);
                return;
            }
            // 矿石等级
            int kslvl = Integer.parseInt(goods[1].getValue().split("=")[1]);
            if (kslvl != 9 && kslvl != 10) {
                FrameMessageChangeJpanel.addtext(5, "护身符重铸使用9级矿石,升级使用10级矿石", null, null);
                return;
            }
            npcComposeBean.setComposetype("我要重铸护身符");
            int lvl = Integer.parseInt(goods[0].getValue().split("\\|")[0].split("=")[1]);
            if (kslvl == 10) {
                lvl++;
                if (lvl > 7) {
                    FrameMessageChangeJpanel.addtext(5, "护身符等级最高为7级", null, null);
                    return;
                }
            }
        } else if (type.equals("彩晶石培养")) {
            npcComposeBean.setComposetype("培养彩晶石");
        } else {
            return;
        }
        UserData.uptael(money.intValue());
        List<BigDecimal> goodstables = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] != null) {
                goodstables.add(goods[i].getRgid());
                goods[i].goodxh(1);
                if (goods[i].getUsetime() <= 0) {
                    GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                    refineJPanel.ClickGood(null, i + 24);
                }
            }
        }
        npcComposeBean.setGoodstables(goodstables);
        String sendMes = Agreement.getAgreement().npccomposeAgreement(
                GsonUtil.getGsonUtil().getgson().toJson(npcComposeBean));
        SendMessageUntil.toServer(sendMes);// 向服务器发送信息
    }

    /** 一键佩饰培养 */
    public void cao3(Goodstable[] goods, BigDecimal money, List<BigDecimal> rgids) {
        int mxxh = goods[1].getUsetime();
        long sxm = RoleData.getRoleData().getLoginResult().getGold().longValue();
        if (sxm / money.longValue() < mxxh) {
            mxxh = (int) (sxm / money.longValue());
        }
        int xh = 0;
        String[] qs = goods[0].getValue().split("\\|");
        if (DazaoBtn.Numerical(qs[0]) >= 7) {
            ZhuFrame.getZhuJpanel().addPrompt2("还没开放8级佩饰培养");
            return;
        }
        if (Goodtype.Accessories(goods[1].getType())) {
            int flvl = 0;
            String[] vs = goods[1].getValue().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                String[] v = vs[i].split("=");
                if (v[0].equals("等级")) {
                    flvl = Integer.parseInt(v[1]);
                }
            }
            if (flvl > 2) {
                ZhuFrame.getZhuJpanel().addPrompt2("无法用2级以上的配饰培养");
                return;
            }
        }
        int max = 0;
        for (int i = 0; i < qs.length; i++) {
            if (qs[i].length() >= 2 && qs[i].substring(0, 2).equals("培养")) {
                String[] num = qs[i].split("=")[1].split("/");
                int value = Integer.parseInt(num[0]);
                max = Integer.parseInt(num[1]);
                xh = max - value + 1;
            }
        }
        if (max == 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("该配饰无法培养");
            return;
        }
        if (xh > mxxh) {
            xh = mxxh;
        }
        if (xh <= 0) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请凑齐物品再来");
            return;
        }
        money = new BigDecimal(money.longValue() * xh);
        if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y金钱不足");
            return;
        }
        ZhuFrame.getZhuJpanel().addPrompt2("一键培养了" + xh + "次");
        RoleData.getRoleData().getLoginResult()
                .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));

        SuitOperBean operBean = new SuitOperBean();
        PartJade jade = new PartJade(-1, -1);
        jade.setJade1(xh);
        operBean.setType(15);
        goods[0].setUsetime(0);
        GoodsListFromServerUntil.Deletebiaoid(goods[0].getRgid());
        eJpanel.ClickGood(null, 24);
        goods[1].goodxh(xh);
        if (goods[1].getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goods[1].getRgid());
            eJpanel.ClickGood(null, 25);
        }
        operBean.setJade(jade);
        operBean.setGoods(rgids);// 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
    }

    private void cao4(Goodstable[] goods, BigDecimal money, String v, List<BigDecimal> rgids) {
        // TODO Auto-generated method stub
        SuitOperBean operBean = new SuitOperBean();
        if (v.equals("秘石合成")) {
            operBean.setType(36);
            int value = -1;
            for (int i = 0; i < goods.length; i++) {
                if (goods[i] == null) {
                    continue;
                }
                if (value == -1) {
                    value = Integer.parseInt(goods[i].getValue().split("=")[1]);
                } else if (value != Integer.parseInt(goods[i].getValue().split("=")[1])) {
                    ZhuFrame.getZhuJpanel().addPrompt2("使用等级相同的秘石合成");
                    return;
                }
            }
            if (value == -1) {
                return;
            }
            if (value >= 5) {
                ZhuFrame.getZhuJpanel().addPrompt2("最高5级");
                return;
            }
        }
        RoleData.getRoleData().getLoginResult()
                .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] != null) {
                goods[i].goodxh(1);
                if (goods[i].getUsetime() <= 0) {
                    GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                    refineJPanel.ClickGood(null, i + 24);
                }
            }
        }
        operBean.setGoods(rgids);
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        for (int i = 0; i < 4; i++) {
            goods[i] = null;
            refineJPanel.sixEquipment[i].setIcon(null);
        }
    }

    /**
     * 伙伴装备操作
     * 
     * @param goods
     * @param type
     *            0伙伴装备升级 1伙伴装备培养
     */
    public void palEquipUpgradeOrCultivate(Goodstable[] goods, String type) {
        String palEquipAgree = AccessSuitMsgUntil.getPalEquipAgree(goods[0].getValue(), "契合度");
        String[] split = palEquipAgree.split("=");
        String[] agreeArr = split[1].split("/");
        if ("装备升级".equals(type)) {
            if (Integer.parseInt(agreeArr[0]) < Integer.parseInt(agreeArr[1])) {
                // 契合度不够
                ZhuFrame.getZhuJpanel().addPrompt2("该装备契合度不够");
                return;
            }
        } else if ("装备培养".equals(type)) {
            if (Integer.parseInt(agreeArr[0]) >= Integer.parseInt(agreeArr[1])) {
                // 契合度不够
                ZhuFrame.getZhuJpanel().addPrompt2("该装备契合度已满");
                return;
            }
            if (Goodtype.isPalEquip(goods[1].getType())) {
                String levelEquip = AccessSuitMsgUntil.getPalEquipAgree(goods[1].getValue(), "等级");
                String[] lvlArr = levelEquip.split("=");
                if (Integer.parseInt(lvlArr[1]) >= 4) {
                    ZhuFrame.getZhuJpanel().addPrompt2("装备4级以上包括4级,不能作为培养道具");
                    return;
                }
            } else if (goods[1].getType() != 7511) {
                ZhuFrame.getZhuJpanel().addPrompt2("不是伙伴装备培养道具");
                return;
            }
        }
        SuitOperBean operBean = new SuitOperBean();
        List<BigDecimal> arrayList = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] == null) {
                continue;
            }
            arrayList.add(goods[i].getRgid());
            refineJPanel.ClickGood(null, i + 24);

        }
        operBean.setQuantity(MixNum());
        for (int i = 0; i < refineJPanel.getLabnum().length; i++) {
            if (refineJPanel.getLabnum()[i].getIcon()!=null){
                refineJPanel.getLabnum()[i].setIcon(null);
                break;
            }
        }


        operBean.setGoods(arrayList);
        operBean.setType(62 - ("装备培养".equals(type) ? 1 : 0));
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
    }

    private int MixNum() {
        for (int i = 0; i < refineJPanel.getLabnum().length; i++) {
            if (refineJPanel.getLabnum()[i].getIcon()!=null){

                switch (i){
                    case 0:
                        return 5;
                    case 1:
                        return 10;
                    case 2:
                        return 15;
                    case 3:
                        return 20;
                    case 4:
                        return 30;
                    case 5:
                        return 99;
                }
            }
        }
        return 1;
    }

    /**
     * 判断伙伴装备升级需要的矿石等级
     * 
     * @param agreeMax
     * @return
     */
    public String isBooleanEquipLvl(String agreeMax) {
        if ("1000".equals(agreeMax)) {
            return "2";
        } else if ("2000".equals(agreeMax)) {
            return "3";
        } else if ("4000".equals(agreeMax)) {
            return "4";
        } else if ("6000".equals(agreeMax)) {
            return "5";
        } else if ("8000".equals(agreeMax)) {
            return "6";
        }
        return "-1";
    }

}
