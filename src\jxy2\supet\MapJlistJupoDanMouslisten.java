package jxy2.supet;

import org.come.entity.Goodstable;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class MapJlistJupoDanMouslisten extends TemplateMouseListener {

    public JList<String> listpet;
    public JupoDanJPanel jupoDanJPanel;

    public MapJlistJupoDanMouslisten(JList<String> listpet, JupoDanJPanel jupoDanJPanel) {
        this.listpet = listpet;
        this.jupoDanJPanel = jupoDanJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        jupoDanJPanel.getRichLabel().setText("");
        jupoDanJPanel.getGoodimg().setIcon(null);
        if (listpet.getModel().getSize()==0)return;
        String petrd = listpet.getModel().getElementAt(listpet.getSelectedIndex()).split("\\|")[1];
        Goodstable goodstable = GoodsListFromServerUntil.getRgid(new BigDecimal(petrd));
        StringBuilder buffer = new StringBuilder();
        if (goodstable!=null){
           String[] vs = goodstable.getValue().split("\\|");
            buffer.append("#Y").append(vs[0].replace("=", "：#G"));
            buffer.append("#r");
            buffer.append("#Y").append(vs[1].replace("=", "：#G"));
            jupoDanJPanel.getRichLabel().setText(buffer.toString());
            jupoDanJPanel.setGoodstable(goodstable);
            jupoDanJPanel.getGoodimg().setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(), 50 ,50));
        }

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        JupoDanJPanel.idx = -1;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        if (listpet!=null){
            if (e.getY() / 20 < jupoDanJPanel.listModel.getSize()){
                JupoDanJPanel.idx = jupoDanJPanel.listpet.locationToIndex(e.getPoint());
            }
        }
    }
}
