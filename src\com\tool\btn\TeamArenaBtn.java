package com.tool.btn;

import org.come.Jpanel.TeamArenaMainJpanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
//import com.tool.role.RoleData;

public class TeamArenaBtn extends MoBanBtn{
    
    private int caozuo;
    private TeamArenaMainJpanel teamArenaMainJpanel;
    
    public TeamArenaBtn(String iconpath, int type,Color[] colors, Font font, String text, int caozuo ,TeamArenaMainJpanel teamArenaMainJpanel) {
        super(iconpath, type,colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.teamArenaMainJpanel = teamArenaMainJpanel;
    }
    public TeamArenaBtn(String iconpath, int type,Color[] colors, Font font, String text) {
        super(iconpath, type,colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        
    }

    @Override
    public void chooseno() {
        
    }

    @Override
    public void nochoose(MouseEvent e) {
        if(caozuo == 1){
            String sendmes = Agreement.getAgreement().teamArenaAgreement("A");
            SendMessageUntil.toServer(sendmes);
        }else if(caozuo == 2){
            String sendmes = Agreement.getAgreement().teamArenaAgreement("D");
            SendMessageUntil.toServer(sendmes);
        }
    }

}
