package jxy2.wdfDome.util;

public class DataTool {

    /**
     * 将字节数组中的四个字节转换为一个32位的整数。
     * @param res 字节数组，包含至少4个字节的数据。
     * @param index 要转换的字节在数组中的起始索引。
     * @return 转换后的32位整数。
     */
    public static int byte2int32(byte[] res, int index) {
        // 将字节数组中的四个字节转换为一个32位整数
        int targets = res[index] & 255 | res[index + 1] << 8 & '\uff00' | res[index + 2] << 24 >>> 8 | res[index + 3] << 24;
        return targets;
    }

    /**
     * 将字节数组中的4个字节转换为无符号32位整数。
     * @param b 字节数组，需要转换的数据存放在该数组中。
     * @param index 在字节数组中开始读取数据的索引位置。
     * @return 从字节数组指定位置开始读取的4个字节转换成的无符号32位整数。
     */
    public static long byte2uint32(byte[] b, int index) {
        // 将字节数组中的每个字节转换为无符号长整型，并适当移动位数
        long s0 = b[index] & 255;
        long s1 = b[index + 1] & 255;
        long s2 = b[index + 2] & 255;
        long s3 = b[index + 3] & 255;

        long s4 = 0L;
        long s5 = 0L;
        long s6 = 0L;
        long s7 = 0L;

        // 左移操作，为每个字节分配合适的位数
        s1 <<= 8;
        s2 <<= 16;
        s3 <<= 24;
        s4 <<= 32;
        s5 <<= 40;
        s6 <<= 48;
        s7 <<= 56;
        // 将所有位合并成一个长整型数值
        return s0 | s1 | s2 | s3 | s4 | s5 | s6 | s7;
    }


    public static int byte2int16(byte[] res, int index) {
        return (short)byte2uint16(res, index);
    }

    public static int byte2uint16(byte[] res, int index) {
        int targets = res[index] & 255 | res[index + 1] << 8 & '\uff00';
        return targets;
    }

    public static String ten2six(String s) {
        String text = "";
        long i = Math.abs(Long.parseLong(s));
        if (i == 0L) {
            return "0";
        } else {
            while(i > 0L) {
                long a = i % 16L;
                i /= 16L;
                text = sixformat(a) + text;
            }

            return "0x" + text;
        }
    }

    private static String sixformat(long i) {
        switch ((int)i) {
            case 10:
                return "A";
            case 11:
                return "B";
            case 12:
                return "C";
            case 13:
                return "D";
            case 14:
                return "E";
            case 15:
                return "F";
            default:
                return String.valueOf(i);
        }
    }
}
