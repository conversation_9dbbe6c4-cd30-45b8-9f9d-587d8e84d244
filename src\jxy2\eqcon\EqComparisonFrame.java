package jxy2.eqcon;

import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class EqComparisonFrame extends J<PERSON><PERSON>nalFrame implements MouseListener {
    private EqComparisonJPanel eqComparisonJPanel = new EqComparisonJPanel(275, 150); ;
    public static EqComparisonFrame getEqComparisonFrame() {
        return (EqComparisonFrame) FormsManagement.getInternalForm(118).getFrame();
    }

    public EqComparisonFrame() {
        this.add(eqComparisonJPanel);
        this.addMouseListener(this);
        this.setBackground(new Color(0,0,0,0));
        this.addMouseMotionListener(new MouseMotionListener() {

            @Override
            public void mouseMoved(MouseEvent e) {

            }

            @Override
            public void mouseDragged(MouseEvent e) {
                // TODO Auto-generated method stub

            }
        });
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        ((BasicInternalFrameUI)this.getUI()).setNorthPane(null);//去除顶部的边框
        this.setBounds(0, 0,275, 150);//设置窗口出现的位置
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
// TODO Auto-generated method stub
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(118);
        }else {
            FormsManagement.Switchinglevel(118);
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public EqComparisonJPanel getEqComparisonJPanel() {
        return eqComparisonJPanel;
    }

    public void setEqComparisonJPanel(EqComparisonJPanel eqComparisonJPanel) {
        this.eqComparisonJPanel = eqComparisonJPanel;
    }
}
