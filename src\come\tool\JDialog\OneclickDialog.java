package come.tool.JDialog;

import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class OneclickDialog implements  TiShiChuLi{
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            // 发送给服务器
            String xl2 = Agreement.getAgreement().OpenLxLockAgreement("Z&"+ object);
            // 向服务器发送信息
            SendMessageUntil.toServer(xl2);
        }
    }
}
