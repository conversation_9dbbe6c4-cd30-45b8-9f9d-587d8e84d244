package jxy2.genius;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.LoginResult;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
* 天才战
* <AUTHOR>
* @date 2024/8/4 下午4:26
*/

public class GeniusBattleJPanel extends JPanel {
    private JLabel[] names = new JLabel[33];
    private JLabel time;
    private long startTime;
    private long endTime;
    private GeniusBattleBtn signBtn;
    public boolean iscx = true;
    private List<LoginResult> loginResults = new ArrayList<>();
    public GeniusBattleJPanel() {
        this.setPreferredSize(new Dimension(1022, 608));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        for (int i = 1; i < 33; i++) {
            names[i] = TeststateJpanel.GJpanelText(UIUtils.COLOR_REFINE,UIUtils.FZCY_HY14);
            if (i<=16){
                names[i].setBounds(38, 49+i*32, 83, 15);
            }else {
                names[i].setBounds(902, 80+(i-17)*32, 83, 15);
            }
            this.add(names[i]);
        }

        time = new JLabel();
        time.setVisible(false);
        add(time);
        startTime = System.currentTimeMillis();
        endTime = startTime + 2 * 60 * 60 * 1000; // 2 hours in milliseconds

        signBtn = new GeniusBattleBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "" +
                "我要报名", 1, this,"");
        signBtn.setBounds(150,150,82,29);
        add(signBtn);
    }

    /**展示玩家数据*/
    public void loadData(){
            String serverMes = Agreement.getAgreement().GeniusUpAgreement("C|");
            SendMessageUntil.toServer(serverMes);
    }
    /**加载已报名玩家数据*/
    public void loadSignData(GeniusBean geniusBean){
        //没有数据先清空
        for (int i = 1; i < 33; i++) {names[i].setText("空席位");}
        List<LoginResult> results = geniusBean.getRoleTables();
        for (int i = 0; i < results.size(); i++) {
            if (RoleData.getRoleData().getLoginResult().getRolename().compareTo(results.get(i).getRolename())==0){
                signBtn.setText("取消报名");
            }
            names[i+1].setText(results.get(i).getRolename());
        }
    }


    public String titie = "三千州天才战";
//    public Sprite  vnavi_1_sprite = WdfLoaDing.dynamic("0x4447BB79","sprite.wdf");
    public long times;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.tz176.getImage(),0,0,1022,608,this);
        Juitil.Subtitledrawing(g, getWidth()/2-60, 24, titie, UIUtils.COLOR_White, UIUtils.HYXKJ_HY20,1);
//        Juitil.TextBackground(g, "1v1大乱斗·初始5点积分，败方扣除5点积分，胜利方获得10点积分，败方将被传送出地图！积分可兑换相应物品！最终角逐出第一名！奖励礼包。（注：战斗只有一场）", 13, 21, 38, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
//        times+=12L;
//        long remainingTime = (endTime - System.currentTimeMillis()) / 1000;
//        int hours = (int) (remainingTime / 3600);
//        int minutes = (int) ((remainingTime % 3600) / 60);
//        int seconds = (int) (remainingTime % 60);
//        String timeStr = String.format("%02d:%02d:%02d", hours, minutes, seconds);
//        Juitil.TextBackground(g, "活动在 "+timeStr+" 后结束", 13, 430, 564, UIUtils.COLOR_NAME3, UIUtils.FZCY_HY16);
//        signBtn.setBounds(468,532,82,29);

//        vnavi_1_sprite.updateToTime(times, 0);
//        vnavi_1_sprite.draw(g, 410, 220);





    }


    public List<LoginResult> getLoginResults() {
        return loginResults;
    }

    public void setLoginResults(List<LoginResult> loginResults) {
        this.loginResults = loginResults;
    }

    public GeniusBattleBtn getSignBtn() {
        return signBtn;
    }

    public void setSignBtn(GeniusBattleBtn signBtn) {
        this.signBtn = signBtn;
    }
}
