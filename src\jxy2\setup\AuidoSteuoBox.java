package jxy2.setup;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.llandudno.AtlasListCell;
import org.come.llandudno.RuleMouse;
import org.come.until.SrcollPanelUI;

import javax.swing.*;
import java.awt.*;

public class Au<PERSON>S<PERSON>uoBox extends JPanel {
    private String backPath;
    private int width, heigth;
    public JList<String> jlist;
    public DefaultListModel<String> listModel;
    public static JScrollPane jScrollPane;
    public static int idx = -1;

    public JList<String> getJlist() {
        if (jlist == null) {
            jlist = new JList<String>();
            jlist.setSelectionBackground(Color.yellow);
            jlist.setSelectionForeground(UIUtils.COLOR_DDEE55FF);
            jlist.setForeground(UIUtils.COLOR_CCDDDDFF);
            jlist.setFont(UIUtils.FZCY_HY14);
            jlist.addMouseMotionListener(new RuleMouse(this,0));
            jlist.setCellRenderer(new AtlasListCell(idx,Color.GRAY,94,20,4));
            jlist.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
            jlist.setOpaque(false);
            jlist.setModel(getListModel());
        }
        return jlist;
    }

    public void setJlist(JList<String> jlist) {
        this.jlist = jlist;
    }

    public DefaultListModel<String> getListModel() {
        if (listModel == null) {
            listModel = new DefaultListModel<String>();
        }
        return listModel;
    }
    public AuidoSteuoBox(int width, int heigth,String[] rowData) {
        this.width = width;
        this.heigth = heigth;
        this.setPreferredSize(new Dimension(width, heigth));
        this.setLayout(null);
        this.setOpaque(false);
        // 滚动条
        jScrollPane = new JScrollPane(getJlist());
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setOpaque(false);
        jScrollPane.setBounds(0, 0, width, heigth - 1);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);
        if(rowData != null){
            for (int i = 0; i < rowData.length; i++) {
                getListModel().add(i, rowData[i]);
            }
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz26, 0, 0, 121, heigth, 1);
        jScrollPane.setBounds(3, 2, width, heigth);
        jScrollPane.getViewport().setViewSize(new Dimension(width,heigth+1));

    }


}
