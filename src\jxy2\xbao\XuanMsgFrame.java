package jxy2.xbao;

import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class XuanMsgFrame extends JInternalFrame implements MouseListener {
    private XuanMsgJPanel xuanMsgJPanel;
    public static XuanMsgFrame getXuanMsgFrame() {
        return (XuanMsgFrame) FormsManagement.getInternalForm(152).getFrame();
    }

    public XuanMsgFrame() {
        xuanMsgJPanel = new XuanMsgJPanel();
        this.add(xuanMsgJPanel);
        this.setBackground(new Color(0,0,0,0));
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        ((BasicInternalFrameUI)this.getUI()).setNorthPane(null);//去除顶部的边框
        this.setBounds(0,0,480,429);//设置窗口出现的位置
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.addMouseListener(this);
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        // 480 429
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(152);
        }else {
            FormsManagement.Switchinglevel(152);
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public XuanMsgJPanel getXuanMsgJPanel() {
        return xuanMsgJPanel;
    }

    public void setXuanMsgJPanel(XuanMsgJPanel xuanMsgJPanel) {
        this.xuanMsgJPanel = xuanMsgJPanel;
    }
}

