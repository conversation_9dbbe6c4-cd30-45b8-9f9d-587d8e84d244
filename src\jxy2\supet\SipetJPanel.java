package jxy2.supet;

import com.tool.btn.NeidanBtn;
import com.tool.role.RoleData;
import com.tool.tcp.NewPart;
import com.tool.tcpimg.UIUtils;
import com.tool.time.TimeLimit;
import jxy2.UiBack;
import jxy2.UniversalModel;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.ZhuJpanel;
import org.come.entity.RoleSummoning;
import org.come.llandudno.AtlasListCell;
import org.come.mouslisten.PointChangeMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;
import org.soaring.btn.CharacterBtn;

import javax.swing.*;
import javax.swing.plaf.basic.BasicScrollBarUI;
import javax.swing.text.AbstractDocument;
import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.DocumentFilter;
import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SipetJPanel extends JPanel {
    // 单例实例
    private static SipetJPanel instance;
    public JList<UniversalModel> sipetModelJPanelJList;
    private DefaultListModel<UniversalModel> defaultListModel = new DefaultListModel<>();
    private Map<Integer, UniversalModel> mapPetModelPanel = new HashMap<>();
    private JScrollPane jScrollPane;
    public SipetBtn btnanimal,btnpetnamech,btndomesticated,btnwar,btnCarrying,btnParrying,btnCuta,btnSort,btnCall,btnPipe,btnextc;//头像;
    private SipetBtn[] btngoods = new SipetBtn[5];
    private String[] title = {"物品","炼妖","灵犀","抗性","技能"};
    public static int idx = -1,p = -1;
    private JLabel[] jLabels = new JLabel[16];
    private JTextField labname;
    private CharacterBtn[] dians = new CharacterBtn[10];
    private NeidanBtn[] labNedan = new NeidanBtn[4];// 放置内丹的四个小框框
    public boolean openpipe = false;//显示头像
    public boolean extc = false;//扩展组件
    // 获取单例实例
    public static SipetJPanel getInstance() {
        if (instance == null) {
            instance = new SipetJPanel();
        }
            instance.RefreshPetViewport();
        return instance;
    }
    
    public SipetJPanel() {
        this.setPreferredSize(new Dimension(368+10, 517));
        this.setLayout(null);  // 使用绝对布局
        this.setBackground(UIUtils.Color_BACK);

        sipetModelJPanelJList = new JList<UniversalModel>();
        sipetModelJPanelJList.setSelectionForeground(Color.white);
        sipetModelJPanelJList.setForeground(Color.white);
        sipetModelJPanelJList.setFont(UIUtils.TEXT_HY16);
        sipetModelJPanelJList.setModel(defaultListModel);
        sipetModelJPanelJList.setCellRenderer(new AtlasListCell(idx, Color.gray, 143, 20, 9));
        sipetModelJPanelJList.addMouseListener(new SipetMouse(sipetModelJPanelJList ,0));
        sipetModelJPanelJList.addMouseMotionListener(new SipetMouse(sipetModelJPanelJList,0));
        sipetModelJPanelJList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        sipetModelJPanelJList.removeAll();
        sipetModelJPanelJList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        sipetModelJPanelJList.setOpaque(false);

        jScrollPane = new JScrollPane(sipetModelJPanelJList);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getVerticalScrollBar().setUnitIncrement(50);
        jScrollPane.setOpaque(false);
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setBounds(23, 63, 167, 128);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);


        // 修改名称按钮
        btnpetnamech = new SipetBtn(ImgConstants.tz80, 1, "改", 2,"");
        btnpetnamech.setBounds(171,212,18,18);
        this.add(btnpetnamech);

        btnPipe = new SipetBtn("0x6FAB1090", 1, "", 16,"");
        btnPipe.setBounds(30,36,18,18);
        this.add(btnPipe);

        btnextc = new SipetBtn("0x6FAB1087", 1, "", 17,"");
        btnextc.setBounds(0,514,23,23);
        this.add(btnextc);

        // 放生宠物按钮
        btnanimal = new SipetBtn(ImgConstants.tz114, 1, "放生",1,"");;
        btnanimal.setBounds(158,235,34,18);
        add(btnanimal);
        // 驯养按钮
        btndomesticated = new SipetBtn(ImgConstants.tz114, 1, "驯养",3,"");
        btndomesticated.setBounds(158,235+26,34,18);
        add(btndomesticated);
        // 参战按钮
        btnwar = new SipetBtn(ImgConstants.tz34, 1, 4, "出战",this,"");
        btnwar.setBounds(287,257,59,24);
        this.add(btnwar);
        // 召唤按钮
        btnCall = new SipetBtn(ImgConstants.tz34, 1, 15, "召唤",this,"");
        btnCall.setBounds(287-68,257,59,24);
        this.add(btnCall);
        init();
        // 确认加点
        btnParrying = new SipetBtn(ImgConstants.tz126, 1, "确认加点",6,"");
        btnParrying.setBounds(287,257+26,51,18);
        add(btnParrying);
        // 切属性
        btnCuta = new SipetBtn(ImgConstants.tz126, 1, "切属性",7,"");
        btnCuta.setBounds(287,257+26*2,51,18);
        add(btnCuta);
        // 载书
        btnCarrying = new SipetBtn(ImgConstants.tz114, 1, "载书",8,"");
        btnCarrying.setBounds(213,41,34,18);
        add(btnCarrying);

        for (int i = 0; i < btngoods.length; i++) {
            btngoods[i] = new SipetBtn(ImgConstants.tz34, 1,  9+i, title[i], this,"");
            btngoods[i].setBounds(33+i*60, 462, 59, 24);
            add(btngoods[i]);
        }
        // 排序按钮
        btnSort = new SipetBtn(ImgConstants.tz80, 1, "排", 14,"");
        btnSort.setBounds(168,43,18,18);
        this.add(btnSort);

        for (int i = 0; i < jLabels.length; i++) {
            jLabels[i] = new JLabel();
            jLabels[i].setVisible(false);
            add(jLabels[i]);
        }

        labname = Juitil.jTextField(UIUtils.COLOR_White, UIUtils.TEXT_NAME_FONT,1);
        labname.setBounds(50, 206, 120, 24);
        labname.setOpaque(false);
        labname.setForeground(Color.white);
        labname.setBorder(BorderFactory.createEmptyBorder());
        labname.setCaretColor(Color.white);
        // 设置 DocumentFilter 限制输入字符最大为 6 个
        ((AbstractDocument) labname.getDocument()).setDocumentFilter(new DocumentFilter() {
            @Override
            public void insertString(FilterBypass fb, int offset, String string, AttributeSet attr) throws BadLocationException {
                if ((fb.getDocument().getLength() + string.length()) <= 12) {
                    super.insertString(fb, offset, string, attr);
                }
            }

            @Override
            public void replace(FilterBypass fb, int offset, int length, String text, AttributeSet attrs) throws BadLocationException {
                if ((fb.getDocument().getLength() + text.length() - length) <= 12) {
                    super.replace(fb, offset, length, text, attrs);
                }
            }
        });
        this.add(labname);
        for (int i = 0; i < dians.length; i++) {
            if (i % 2 == 0) {
                dians[i] = new CharacterBtn("0x6FAB1039", 1, 10 + i);
            } else {
                dians[i] = new CharacterBtn("0x6FAB1041", 1, 10 + i);
            }
            int shop_x = i % 2 ;
            int shop_y = i / 2;
            dians[i].setBounds(315 + shop_x * 16, 314 + shop_y  * 25, 16, 15);
            dians[i].addMouseListener(new PointChangeMouslisten(i + 10));
            this.add(dians[i]);
        }
        for (int i = 0; i < 4; i++) {
            labNedan[i] = new NeidanBtn(ImgConstants.tz327, 1,i);
            labNedan[i].setBounds(192, 76 + i * 18, 18, 18);
            this.add(labNedan[i]);
        }

    }



/**动态切换按钮 - 召唤兽主界面*/
public void updateButtonImages(int uiType) {
    // 获取面板名称
    String panelName = this.getClass().getSimpleName();
    // 设置按钮图标资源
    String iconResource = uiType==0 ? "0x6FEB8534" : uiType==1 ? "0x6FAC1008" : "0x6FAB1002";
    String leng34_18 = uiType==0||uiType==1  ? "0x6FEB8614": "0x6FAB1014";
    String leng18_18 = uiType==0||uiType==1  ? "0x6FEB8580": "0x6FAB1017";
    String leng51_18= uiType==0||uiType==1  ? "0x6FEB8626": "0x6FAB1015";
    String tx= uiType==0||uiType==1  ? "0x6FAC1077": "0x6FAB1090";
    // 单独设置主要按钮的图标

    btnwar.setIcons(Juitil.getImgs(iconResource));
    btnCall.setIcons(Juitil.getImgs(iconResource));
    btnanimal.setIcons(Juitil.getImgs(leng34_18));
    btndomesticated.setIcons(Juitil.getImgs(leng34_18));
    btnCarrying.setIcons(Juitil.getImgs(leng34_18));
    btnpetnamech.setIcons(Juitil.getImgs(leng18_18));
    btnPipe.setIcons(Juitil.getImgs(tx));
    btnCuta.setIcons(Juitil.getImgs(leng51_18));
    btnParrying.setIcons(Juitil.getImgs(leng51_18));
    btnSort.setIcons(Juitil.getImgs(leng18_18));
    String exc= extc?(uiType==1 ? "0x6FAC1073": "0x6FAB1086"):(uiType==1 ? "0x6FAC1074": "0x6FAB1087");
    btnextc.setIcons(Juitil.getImgs(exc));
    int h = uiType==1?492:517;
    int g = extc?h-5:h-23;
    btnextc.setBounds(uiType==1?14:0,g,23,23);
    // 设置物品按钮的图标
    for (SipetBtn btn : btngoods) {
        btn.setIcons(Juitil.getImgs(iconResource));
    }
    // 单独应用样式到主要按钮

    UiBack.getComponentStyle(panelName, "btnwar").applyToButton(btnwar);
    UiBack.getComponentStyle(panelName, "jScrollPane").applyToScrollBar(jScrollPane);
    UiBack.getComponentStyle(panelName, "btnCall").applyToButton(btnCall);
    UiBack.getComponentStyle(panelName, "btnpetnamech").applyToButton(btnpetnamech);
    UiBack.getComponentStyle(panelName, "btnanimal").applyToButton(btnanimal);
    UiBack.getComponentStyle(panelName, "btndomesticated").applyToButton(btndomesticated);
    UiBack.getComponentStyle(panelName, "btnParrying").applyToButton(btnParrying);
    UiBack.getComponentStyle(panelName, "btnCuta").applyToButton(btnCuta);
    UiBack.getComponentStyle(panelName, "btnCarrying").applyToButton(btnCarrying);
    UiBack.getComponentStyle(panelName, "btnSort").applyToButton(btnSort);
    UiBack.getComponentStyle(panelName, "btnPipe").applyToButton(btnPipe);

   Juitil.changeButtonColor(this,uiType);
        //调整滚动条
    BasicScrollBarUI scrollBarUI2 = Juitil.createUI(uiType);
    JScrollBar oldVBar2 = jScrollPane.getVerticalScrollBar();
    JScrollBar newVBar2 = new JScrollBar(JScrollBar.VERTICAL);
    newVBar2.setValues(
            oldVBar2.getValue(),
            oldVBar2.getVisibleAmount(),
            oldVBar2.getMinimum(),
            oldVBar2.getMaximum()
    );
    newVBar2.setUI(scrollBarUI2);
    jScrollPane.setVerticalScrollBar(newVBar2);
    jScrollPane.getVerticalScrollBar().setUnitIncrement(50);
        //调整窗体大小
        if (uiType==1){//589 513
            Juitil.adjustFrameSize(extc?589:403, extc?513:492, 144);
        }else {
            Juitil.adjustFrameSize(extc?562:378, extc?538:517, 144);
        }
        int ofo = extc?uiType==1?590:572:uiType==1?403:388;
        Juitil.addClosingButtonToPanel(this,144,ofo);
        BtnIsvisible();
        RefreshPetViewport();
         if (chosePetMes!=null){
            BtnAddPoints(chosePetMes);
        }

    String dainsimg = uiType==0||uiType==1  ? "0x6FAC1027": "0x6FAB1039";
    String dainsimgtwo = uiType==0||uiType==1  ? "0x6FAC1029": "0x6FAB1041";
    for (int i = 0; i < 10; i++) {
        if (i % 2 == 0) {
            dians[i].setIcons(Juitil.getImgs(dainsimg));
        } else {
            dians[i].setIcons(Juitil.getImgs(dainsimgtwo));
        }
    }

}


    /**初始化召唤兽列表*/
    public void init() {
        // 清除之前的面板对象
        for (int i = 0; i < UserMessUntil.getPetListTable().size(); i++) {
            RoleSummoning pet = UserMessUntil.getPetListTable().get(i);
            if (pet != null) {
                UniversalModel universalModel = new UniversalModel(143,140);
                universalModel.PetData(i,pet,
                        0,2,
                        20,
                        false,
                        true,
                        20,0);
                universalModel.setBounds(0,i*20,143,140);
                sipetModelJPanelJList.add(universalModel);
                defaultListModel.add(i,universalModel);
                mapPetModelPanel.put(i, universalModel);
            }
        }
        sipetModelJPanelJList.setPreferredSize(new Dimension(143, UserMessUntil.getPetListTable().size() * 20));
        //初始化召唤兽按钮是否在战斗中
        btnCall.setVisible(ZhuJpanel.fighting);
        String meg = Agreement.getAgreement().WuLingLvAgreement("wulinglv");
        SendMessageUntil.toServer(meg);

    }



    /**数据刷新*/
    public void RefreshPetViewport() {

        List<RoleSummoning> pets = UserMessUntil.getPetListTable();
        if (mapPetModelPanel.size()!=pets.size()){
            sipetModelJPanelJList.removeAll();
            defaultListModel.removeAllElements();
            mapPetModelPanel.clear();
            init();
        }else {
            for (int i = 0; i < pets.size(); i++) {
                RoleSummoning pet = pets.get(i);
                //判断召唤兽的可分配点数
                UniversalModel universalModel = mapPetModelPanel.get(i);
                universalModel.PetData(i, pet,
                        openpipe?148:0,openpipe?12:2,
                        20,
                        openpipe,
                        true,
                        openpipe?40:20,0);
                universalModel.setBounds(0,i*(openpipe?38:20),openpipe?166:143,140);
                if (getBtnRich(pet)&&!FormsManagement.getframe(18).isVisible()){
                    sipetModelJPanelJList.setSelectedIndex(i);
                }
            }

        }
        //初始化召唤兽按钮是否在战斗中
        btnCall.setVisible(ZhuJpanel.fighting);
        if (chosePetMes!= null) {
            BtnIsvisible();
        }
        BtnAddPoints(chosePetMes);
        int iconResource = Util.SwitchUI==2 ? extc?453:136 : extc?446:147;
        int inde= Math.max((openpipe?38:20) * pets.size(), iconResource);
        sipetModelJPanelJList.setPreferredSize(new Dimension(openpipe?166:146, inde));
        jScrollPane.getViewport().setViewSize(new Dimension(openpipe?166:146, inde));
    }


    public void BUFFIN() {

    }

    /**
     * 根据BUFF名称找到对应的BUFF时效
     * @param name 名字
     */
    public static String QueryTimeBuff(String name){
        TimeLimit timeLimit = TimeLimit.getLimits();
        if (timeLimit==null)return "";
        for (int i = 0; i < timeLimit.limits.size(); i++) {
            if (timeLimit.limits.get(i).getName().equals(name)){
                return  timeLimit.limits.get(i).getValue();
            }
        }
        return "";
    }
    /**驱动加点按钮可见度*/
    public void BtnPoints(int canponint) {
        int lv = chosePetMes != null ? AnalysisString.petTurnRount(chosePetMes.getGrade()) : 0;
        for (int i = 0; i < dians.length; i++) {
            // 对索引8和9特殊处理
            if (i == 8 || i == 9) {
                // 当lv>3时用正常逻辑，否则强制隐藏
                dians[i].setVisible(lv > 3 && !(canponint <= 0));
            } else {
                // 其他元素保持原逻辑
                dians[i].setVisible(!(canponint <= 0));
            }
        }
    }


    /**调整按钮显示位置*/
    public void BtnIsvisible() {
        if (chosePetMes ==null)return;
        for (int i = 0; i < 5; i++) {
            boolean oj = chosePetMes.getTurnRount() < 4;
            // 动态计算坐标
            int x;
            if (oj) {
                // 如果oj为true，调整坐标
                int sum = i >= 2 ? (i * 80) - 80 : i * 80;
                x = Util.SwitchUI==0 ? 35 + sum :
                        Util.SwitchUI==1 ?55 + sum :
                                40 + sum; // 从第3个按钮开始，坐标向前移动
            } else {
                x = Util.SwitchUI==0 ?25 + i * 66: Util.SwitchUI==1 ?45 + i * 66:28 + i * 66;
            }
            int y =  Util.SwitchUI==0 ?462: Util.SwitchUI==1 ?442:462; // y 坐标
            // 设置按钮的边界
            btngoods[i].setBounds(extc?(Util.SwitchUI==1?x+200:x+190):x, extc?20+y:y, 60, 26);
            // 确保其他按钮可见
            btngoods[i].setVisible(!oj||i!=2); // 设置 btngoods[2] 不可见
        }
        String dainsimg =  Ii() ? "0x6FEB9009": "0x6FAB1025";
        if (chosePetMes.getInnerGoods()!=null&& !chosePetMes.getInnerGoods().isEmpty()) {
            for (int i = 0; i < 4; i++) {
                labNedan[i].setBounds(extc?(Util.SwitchUI==1 ?248:228):Util.SwitchUI==1 ?215:192, 76 + i * 18, 18, 18);
                labNedan[i].setIcons(Juitil.getImgs(dainsimg));
            }
        }

    }

    public RoleSummoning OBTAIN_THE_SELECTED_SUMMONED_BEAST(int index){
        List<RoleSummoning> pets = UserMessUntil.getPetListTable();
        for (int i = 0; i < pets.size(); i++) {
            if ( i == index){
                return pets.get(i);
            }
        }
        return pets.get(0);
    }

    /**调整加载按钮可见度*/
    public void BtnAddPoints(RoleSummoning chosePetMes) {
        int lv = chosePetMes!= null ? AnalysisString.petTurnRount(chosePetMes.getGrade()) : 0;
        for (int i = 0; i < 10; i++) {
            int shop_x = i % 2 ;
            int shop_y = i / 2;
            int disnsugao = Util.SwitchUI==2?extc?320:314:extc?304:294;
            int nx = Util.SwitchUI==2?extc?506:315:extc?535:334;
            dians[i].setBounds(nx + shop_x * 16, disnsugao + shop_y  * 25, 11, 15);
        }

        int gao = Util.SwitchUI==2?lv>3?436:410:lv>3?416:390;
        btnParrying.setBounds( extc?492:300,extc?gao+10:gao,51,17);
        int x = Util.SwitchUI==2?extc?440:248:extc?469:248;
        btnCuta.setBounds(x,extc?gao+10:gao,51,17);
        btnSort.setBounds(Ii()?188:148,Ii()?22:35,18,18);
        btnParrying.setVisible(chosePetMes!=null&&chosePetMes.getCanpoint()!=0);
        //根据召唤兽转生等级来设置是否显示，大于3级显示，小于3级不显示
        dians[8].setVisible(lv>3&&chosePetMes.getCanpoint()!=0);
        dians[9].setVisible(lv>3&&chosePetMes.getCanpoint()!=0);
        btnCall.setVisible(ZhuJpanel.fighting);
        btnCuta.setVisible(lv>3);
    }

    public boolean Ii(){
        return Util.SwitchUI == 1;
    }

    /**
     * 0-4 获取根骨灵性力量敏捷可分配点
     */
    public int getdian(int type) {
        try {
            if (type == 0) {
                type = Integer.parseInt(jLabels[11].getText());
//                    dians[0].setBtn(type<=1?-1:1);
            } else if (type == 1) {
                type = Integer.parseInt(jLabels[12].getText());
            } else if (type == 2) {
                type = Integer.parseInt(jLabels[13].getText());
            } else if (type == 3) {
                type = Integer.parseInt(jLabels[14].getText());
            } else if (type == 4) {
                type = Integer.parseInt(jLabels[15].getText());
            } else if (type == 5) {
                type = Integer.parseInt(jLabels[10].getText());
            }
        } catch (Exception e) {
            // TODO: handle exception
            type = 0;
        }
        return type;
    }

    /**增加点数*/
    public void adddian(int type, int point) {
        try {
            if (type == 0) {
                type = Integer.parseInt(jLabels[11].getText());
                jLabels[11].setText(type + point + "");
            } else if (type == 1) {
                type = Integer.parseInt(jLabels[12].getText());
                jLabels[12].setText(type + point + "");
            } else if (type == 2) {
                type = Integer.parseInt(jLabels[13].getText());
                jLabels[13].setText(type + point + "");
            } else if (type == 3) {
                type = Integer.parseInt(jLabels[14].getText());
                jLabels[14].setText(type + point + "");
            } else if (type == 4) {
                type = Integer.parseInt(jLabels[15].getText());
                jLabels[15].setText(type + point + "");
            }
            type = Integer.parseInt(jLabels[10].getText());
            jLabels[10].setText(type - point + "");
        } catch (Exception e) {
            // TODO: handle exception
        }
    }
    /**召唤兽是否已参战*/
    public static boolean getBtnRich(RoleSummoning pet) {
        if (RoleData.getRoleData().getLoginResult().getSummoning_id() != null) {
            //点击参战后
            return RoleData.getRoleData().getLoginResult().getSummoning_id().compareTo(
                    pet.getSid()) == 0;
        }
        return false;
    }

    /***
     * 获取已参战的召唤兽
     * @return roles
     */
    public static RoleSummoning getBtnRich() {
        if (RoleData.getRoleData().getLoginResult().getSummoning_id() != null) {
            for (int i = 0; i < UserMessUntil.getPetListTable().size(); i++) {
                RoleSummoning pet = UserMessUntil.getPetListTable().get(i);
                if (RoleData.getRoleData().getLoginResult().getSummoning_id().compareTo(
                        pet.getSid()) == 0) {
                    return pet;
                }
            }
        }
        return null;
    }



    public static NewPart newPart;
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
       if (Util.SwitchUI==0){
           Juitil.JPanelNewShow(g, getWidth(), getHeight(), "召唤兽");
       }else if (Util.SwitchUI==1){
           Juitil.SheNewShow(g,extc?589:403,getHeight(),"召唤兽",271,55);
       }else {
           Juitil.RedMuNewShow(g,getWidth(),getHeight(),"召唤兽");
       }
        if (extc) {
            DrawMaxCoordinates(g);
        }else {
            DrawCoordinates(g);
        }



    }




    public static void PJpanelText(Graphics g,String text,int x ,int y) {
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                g2d.setFont(UIUtils.TEXT_NAME_FONT);
                g2d.setColor(UIUtils.COLOR_White);
                g2d.drawString(text, x+1, y);
                g2d.drawString(text, x, y);
    }


    /**
     * 绘制坐标轴上的信息
     * 此方法负责在图形组件上绘制宠物的信息，包括名称、等级、忠诚度等
     * @param g Graphics对象，用于绘制
     */
    public String[] text = {"名称", "等级", "忠诚", "亲密", "气血", "法力", "攻击", "速度", "经验", "禅定",
            "可分配点数", "根骨", "灵性", "力量", "敏捷", "定力"
    };
    public long time = 0;
    private RoleSummoning chosePetMes;
    public void DrawCoordinates(Graphics g) {
        int a =Util.SwitchUI;
        Juitil.ImngBack(g,a==1?Juitil.she_0009:Juitil.red_0009, a==1?45:18, a==1?20:35, 170, 160, 1);
        Juitil.ImngBack(g,a==1?Juitil.she_0005:Juitil.red_0006, a==1?233:208, a==1?39:52, 143, 200, 1);
        jScrollPane.setBounds(a==1?49:21, a==1?42:54, a==1?167:165, a==1?145:133);
        btnwar.setBounds(a==1?310:287,a==1?240:257,59,24);
        time+=35L;
        if (chosePetMes != null) {
            int vx = Ii()?24:0;
            int vy = Ii()?-20:0;
            if (newPart != null) {
                newPart.draw(g, vx+270, 218, 0, time);
            }

            if (chosePetMes.getQiling()>0){
                g.drawImage(Juitil.tz335.getImage(),  vx+278, vy+68, 71, 20, null);
                Juitil.TextBackground(g, chosePetMes.getQiling()+"次", 14, vx+325, vy+69, UIUtils.COLOR_White, UIUtils.TEXT_FONT2, UIUtils.Color_BACK);
            }
        }
        int lv = chosePetMes != null ? AnalysisString.petTurnRount(chosePetMes.getGrade()) : 0;
        // 计算面板中心位置
        int centerX = (getWidth() / 2) -5;
        int startY =  a==2?210:190; // 起始Y坐标
        int lineHeight = 25; // 每行高度
        int lineWidth = a==1?21:23;
        labname.setBounds(centerX - 133, startY-4, 120, 24);
        btnpetnamech.setBounds(a==1?185:171,a==1?191:212,18,18);
        btnanimal.setBounds(a==1?168:158,a==1?215:237,34,18);
        btndomesticated.setBounds(a==1?168:158,a==1?240:261,34,18);
        // 绘制左边部分（名称到经验）
        for (int i = 0; i <(lv>3?8:9); i++) { // 只绘制前8项（名称到经验）
            Juitil.TextBackground(g, text[i], 16, centerX - 168, startY + i * lineHeight,
                    Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                    a==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);
            // 为"等级"和"忠诚"设置不同的图像宽度
            if (i == 1) { // "等级"
                Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX - 125, startY + i * lineHeight, 96, lineWidth, 1);
            } else if (i == 2) { // "忠诚"
                Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX - 125, startY + i * lineHeight, 96, lineWidth, 1);
            } else {
                Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX - 125, startY + i * lineHeight, 132, lineWidth, 1);
            }
            PJpanelText(g,jLabels[i].getText(),centerX - 122, 16+startY + i * lineHeight);

        }
        // 如果等级 > 3，显示左侧"禅定"和右侧"定力"
        if (lv > 3) {
            // 绘制左侧"禅定"
            Juitil.TextBackground(g, text[9], 16, centerX -168, startY + 8 * lineHeight,
                    Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                    a==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);

            Juitil.TextBackground(g, text[8], 16, centerX -168, startY + 9 * lineHeight,
                    Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                    a==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);


            Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX - 125, startY + 8 * lineHeight, 132, lineWidth, 1);
            Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX - 125, startY + 9 * lineHeight, 132, lineWidth, 1);
            // 绘制右边部分（可分配点数到定力）
            for (int i = 10; i < text.length; i++) {
                Juitil.TextBackground(g, text[i], 16, centerX + 23, 75+startY + (i - 10) * lineHeight,
                        Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                        a==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                        UIUtils.Color_BACK);
                // 为"可分配点数"设置不同的图像宽度
                if (i == 10) { // "可分配点数"
                    Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX + 117, 75 + startY, 50, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 120, 91 +startY);
                } else {
                    Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX + 65, 75 + startY + (i - 10) * lineHeight, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 68, 91 +startY + (i - 10) * lineHeight);

                }
            }
            PJpanelText(g,jLabels[8].getText(),centerX - 122, 16+startY + 9 * lineHeight);
            PJpanelText(g,jLabels[9].getText(),centerX - 122, 16+startY + 8 * lineHeight);

        } else {
            // 如果等级 <= 3，不显示"禅定"和"定力"
            // 只绘制右边部分（可分配点数到敏捷）
            for (int i = 10; i < text.length - 1; i++) { // 不绘制最后一项"定力"
                Juitil.TextBackground(g, text[i], 16, centerX + 23, 75 + startY + (i - 10) * lineHeight,
                        Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                        a==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                        UIUtils.Color_BACK);
                // 为"可分配点数"设置不同的图像宽度
                if (i == 10) { // "可分配点数"
                    Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX + 117, 75 + startY, 50, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 120, 91 +startY);
                } else {
                    Juitil.ImngBack(g,  a==1?Juitil.tz128:Juitil.red_0005, centerX + 65, 75 + startY + (i - 10) * lineHeight, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 68, 91 +startY + (i - 10) * lineHeight);

                }
            }
        }
    }


    /**
     *绘制最大化的组件坐标
     */
    public String[] texts = {"名称", "等级", "忠诚", "气血", "法力", "攻击", "速度", "经验", "禅定",
            "亲密","可分配点数", "根骨", "灵性", "力量", "敏捷", "定力"
    };
    private void DrawMaxCoordinates(Graphics g) {
        int a = Util.SwitchUI;
        Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0009:Juitil.red_0009, a==1?42:17, a==1?20:35, 193, a==1?460:476, 1);
        Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.she_0005:Juitil.red_0006, a==1?246:219, a==1?39:54, 320, 178, 1);
        jScrollPane.setBounds(a==1?42:20, a==1?42:54, a==1?194:188, a==1?445:452);
        btnwar.setBounds(a==1?506:478,a==1?222:238,59,24);
        int ex  = 186;
        time+=35L;
        if (chosePetMes != null) {
            int vx = Ii()?24:0;
            int vy = Ii()?-20:0;
            if (newPart != null) {
                newPart.draw(g, vx+376, 180, 0, time);
            }

            if (chosePetMes.getQiling()>0){
                g.drawImage(Juitil.tz335.getImage(),  vx+278+ex, vy+68, 71, 20, null);
                Juitil.TextBackground(g, chosePetMes.getQiling()+"次", 14, vx+325+ex, vy+69, UIUtils.COLOR_White, UIUtils.TEXT_FONT2, UIUtils.Color_BACK);
            }
        }
        int lv = chosePetMes != null ? AnalysisString.petTurnRount(chosePetMes.getGrade()) : 0;
        // 计算面板中心位置
        int centerX = (a==1?114:104)+(getWidth() / 2) -5;
        int startY =  Util.SwitchUI==2?241:226; // 起始Y坐标
        int lineHeight = 25; // 每行高度
        int lineWidth = Util.SwitchUI==1?21:23;
        labname.setBounds(centerX - 133, startY-4, 120, 24);
        btnpetnamech.setBounds(a==1?391:369,a==1?228:242,18,18);
        btnanimal.setBounds(a==1?375:352,a==1?252:267,34,18);
        btndomesticated.setBounds(a==1?375:352,a==1?277:292,34,18);
        // 绘制左边部分（名称到经验）
        for (int i = 0; i < (lv > 3 ? 7 : 8); i++) { // 只绘制前8项（名称到经验）
            // 绘制文本背景
            Juitil.TextBackground(g, texts[i], 16, centerX - 168, startY + i * lineHeight,
                    Util.SwitchUI == 0 ? UIUtils.COLOR_goods_quantity :
                            Util.SwitchUI == 1 ? UIUtils.COLOR_NAME_BACKGROUND : UIUtils.COLOR_CL_RedMU,
                    Util.SwitchUI == 1 ? UIUtils.NEWTX_HY17B : UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);

            // 绘制背景图像
            if (i == 1 || i == 2) { // "等级" 或 "忠诚"
                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0005,
                        centerX - 125, startY + i * lineHeight, 96, lineWidth, 1);
            } else {
                Juitil.ImngBack(g, Util.SwitchUI == 1 ? Juitil.tz128 : Juitil.red_0005,
                        centerX - 125, startY + i * lineHeight, 132, lineWidth, 1);
            }

            // 绘制JLabel文本
            if (i >= 1) { // 只处理索引1-7的JLabel
                int labelIndex = (i == 1 || i == 2) ? i : (i == 3 ? 4 : (i == 4 ? 5 : (i == 5 ? 6 : (i == 6 ? 7 : -1))));
                if (labelIndex != -1 && labelIndex < jLabels.length && jLabels[labelIndex] != null) {
                    PJpanelText(g, jLabels[labelIndex].getText(),
                            centerX - 122, (16 + startY + (i * lineHeight)));
                }
            }
        }


        // 如果等级 > 3，显示左侧"禅定"和右侧"定力"
        if (lv > 3) {
            // 绘制左侧"禅定"
            Juitil.TextBackground(g, texts[8], 16, centerX -168, (startY + 8 * lineHeight)-25,
                    Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                    Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);

            Juitil.TextBackground(g, texts[7], 16, centerX -168, (startY + 9 * lineHeight)-25,
                    Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                    Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                    UIUtils.Color_BACK);


            Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX - 125, (startY + 8 * lineHeight)-25, 132, lineWidth, 1);
            Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX - 125, (startY + 9 * lineHeight)-25, 132, lineWidth, 1);


            // 绘制右边部分（可分配点数到定力）
            for (int i = 9; i < texts.length; i++) {
                Juitil.TextBackground(g, texts[i], 16, centerX + 15, 50+startY + (i - 10) * lineHeight,
                        Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                        Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                        UIUtils.Color_BACK);
                // 为"可分配点数"设置不同的图像宽度
                if (i == 9) { // "可分配点数"
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 110, 50 + startY, 50, lineWidth, 1);
                }else if (i == 10) { // "可分配点数"
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 58, 25 + startY, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 114, 66 +startY);
                } else {
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 58, 50 + startY + (i - 10) * lineHeight, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 61, 66 +startY + (i - 10) * lineHeight);

                }
            }
            PJpanelText(g,jLabels[8].getText(),centerX - 122, (16+startY + 9 * lineHeight)-25);
            PJpanelText(g,jLabels[9].getText(),centerX - 122, (16+startY + 8 * lineHeight)-25);

        } else {
            // 如果等级 <= 3，不显示"禅定"和"定力"
            // 只绘制右边部分（可分配点数到敏捷）
            for (int i = 9; i < texts.length - 1; i++) { // 不绘制最后一项"定力"
                Juitil.TextBackground(g, texts[i], 16, centerX + 15, 50 + startY + (i - 10) * lineHeight,
                        Util.SwitchUI==0?UIUtils.COLOR_goods_quantity:Util.SwitchUI==1?UIUtils.COLOR_NAME_BACKGROUND:UIUtils.COLOR_CL_RedMU,
                        Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B,
                        UIUtils.Color_BACK);
                // 为"可分配点数"设置不同的图像宽度
                if (i == 9) { // "可分配点数"
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 110, 50 + startY, 50, lineWidth, 1);
//                    PJpanelText(g,jLabels[i].getText(),centerX + 120, 66 +startY);
                }else if (i == 10) { // "可分配点数"
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 58, 25 + startY, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 114, 66 +startY);
                } else {
                    Juitil.ImngBack(g,  Util.SwitchUI==1?Juitil.tz128:Juitil.red_0005, centerX + 58, 50 + startY + (i - 10) * lineHeight, 102, lineWidth, 1);
                    PJpanelText(g,jLabels[i].getText(),centerX + 61, 66 +startY + (i - 10) * lineHeight);

                }
            }
            PJpanelText(g, jLabels[8].getText(), centerX - 122, (16 + startY + 175));//经验
        }
        PJpanelText(g, jLabels[3].getText(), centerX + 60, (16 + startY+lineHeight));
    }

















    public JList<UniversalModel> getSipetModelJPanelJList() {
        return sipetModelJPanelJList;
    }

    public void setSipetModelJPanelJList(JList<UniversalModel> sipetModelJPanelJList) {
        this.sipetModelJPanelJList = sipetModelJPanelJList;
    }

    public DefaultListModel<UniversalModel> getDefaultListModel() {
        return defaultListModel;
    }

    public void setDefaultListModel(DefaultListModel<UniversalModel> defaultListModel) {
        this.defaultListModel = defaultListModel;
    }

    public Map<Integer, UniversalModel> getMapPetModelPanel() {
        return mapPetModelPanel;
    }

    public void setMapPetModelPanel(Map<Integer, UniversalModel> mapPetModelPanel) {
        this.mapPetModelPanel = mapPetModelPanel;
    }

    public JScrollPane getjScrollPane() {
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public JLabel[] getjLabels() {
        return jLabels;
    }

    public void setjLabels(JLabel[] jLabels) {
        this.jLabels = jLabels;
    }

    public JTextField getLabname() {
        return labname;
    }

    public void setLabname(JTextField labname) {
        this.labname = labname;
    }

    public SipetBtn getBtnwar() {
        return btnwar;
    }

    public void setBtnwar(SipetBtn btnwar) {
        this.btnwar = btnwar;
    }

    public SipetBtn getBtnCall() {
        return btnCall;
    }

    public void setBtnCall(SipetBtn btnCall) {
        this.btnCall = btnCall;
    }

    public NeidanBtn[] getLabNedan() {
        return labNedan;
    }

    public void setLabNedan(NeidanBtn[] labNedan) {
        this.labNedan = labNedan;
    }

    public CharacterBtn[] getDians() {
        return dians;
    }

    public void setDians(CharacterBtn[] dians) {
        this.dians = dians;
    }

    public RoleSummoning getChosePetMes() {
        return chosePetMes;
    }

    public void setChosePetMes(RoleSummoning chosePetMes) {
        this.chosePetMes = chosePetMes;
    }
}
