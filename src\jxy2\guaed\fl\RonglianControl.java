package jxy2.guaed.fl;

import org.come.action.FromServerAction;

public class RonglianControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        boolean is = !"融合失败".equals(mes.split("\\|")[0]);
        RonglianJPanel  ronglianJPanel  = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel();
        // 分割消息并检查数组长度
        String[] parts = mes.split("\\|"); // 将消息按'|'分割
        int matchPositions = parts.length < 3 ? 0 : Integer.parseInt(parts[2]); // 检查数组长度
        String part1 = parts.length > 1 ? parts[1] : ""; // 检查数组长度以安全访问parts
        String part2 = parts.length > 1 ? parts[3] : ""; // 检查数组长度以安全访问parts
        RonglianBtn.CycleTimer(ronglianJPanel, is, part1, matchPositions,part2); // 使用分割后的数组
    }
}
