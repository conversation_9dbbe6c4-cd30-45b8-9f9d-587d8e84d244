package jxy2.petView;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;

public class ExtractedJPanel extends JPanel{
    private RichLabel richLabel;
    public ExtractedJPanel() {
        this.setPreferredSize(new Dimension(284, 182));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        getRichLabel();
    }

    public  ImageIcon tz67 = CutButtonImage.getWdfPng(ImgConstants.tz67,"defaut.wdf");
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(tz67.getImage(),0,0,200,182,this);
        Juitil.Textdrawing(g,"炼化属性预览",13,22,UIUtils.COLOR_White,UIUtils.MSYH_HY14);
    }

    public RichLabel getRichLabel() {
        if (richLabel==null){
            richLabel = new RichLabel("",UIUtils.MSYH_HY14,165);
            richLabel.setBounds(11,24,210,160);
            add(richLabel);
        }
        return richLabel;
    }
}
