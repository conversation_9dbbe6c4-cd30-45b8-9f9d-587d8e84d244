package com.tool.tcp;

import java.lang.ref.SoftReference;
import java.util.HashMap;
import java.util.Map;

public class TCPLoadThread extends Thread {
	/**颜色格式转换*/
	private static long colorID=0;
	private static Map<String,String> colorMap;//双向map
	static{ colorMap=new HashMap<>(); }
	public static synchronized String addMap(String value){
		String color=colorMap.get(value);
		if (color!=null) { return color; }
		color=(colorID++)+"";
		colorMap.put(color, value);
		colorMap.put(value, color);
		return color;
	}
	private static String getMap(String id){
		return colorMap.get(id);
	}
	public static String N="N";
	public static String N0="N0";
	public static String N1="N1";
	private static String HX="_";
	boolean is = true;
	private int size;
	public TCPLoadThread() {
		// TODO Auto-generated constructor stub
		setDaemon(true);
	}
	public void setLoadTcp() {
		synchronized (this) {
			if (is) { return; }
			is = true;
			this.notifyAll();
		}
	}
	@Override
	public void run() {
		while (true) {
			// load image
			try {
				Object object=SpriteFactory.getLoad();
				if (object==null) {
					synchronized (this) {
						is = false;
						this.wait();
						continue;
					}
				}
				if (object instanceof Sprite) {
					if ((size++%4)==0) {Thread.sleep(1);}
					SpriteFactory.VloadImg((Sprite)object);
				}else if (object instanceof String) {
					String TCPPath=(String)object;
					if (TCPPath.startsWith(N)) {//新加载方式
						long l = Long.parseLong(TCPPath.substring(2));
						SoftReference<Sprite> s = SpriteFactory.TcpTwoMap.get(l);
						if (s == null || s.get() == null) {
							long color=l>>40;
							Sprite sprite = SpriteFactory.VloadSprite(l&0x7fffffffffL,TCPPath.startsWith(N1),color!=0?getMap(color+""):null);
							if (sprite != null) {
								SpriteFactory.TcpTwoMap.put(l,new SoftReference<>(sprite));
								if (sprite.getAnimationCount()==1) {
									sprite.setLoad(0);
									SpriteFactory.VloadImg(sprite);
								}
							}
						}
					}else {
						SoftReference<Sprite> s = SpriteFactory.TcpMap.get(TCPPath);
						if (s == null || s.get() == null) {
							Sprite sprite;
							int index = TCPPath.indexOf(HX);
							if (index == -1) {
								sprite = SpriteFactory.VloadSprite(TCPPath,null);
							} else {
								sprite = SpriteFactory.VloadSprite(TCPPath.substring(0, index),getMap(TCPPath.substring(index + 1)));
							}
							if (sprite != null) {
								SpriteFactory.TcpMap.put(TCPPath,new SoftReference<>(sprite));
								if (sprite.getAnimationCount()==1) {
									sprite.setLoad(0);
									SpriteFactory.VloadImg(sprite);	
								}
							}
						}
					}
				}
				SpriteFactory.clearLoad(object);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				synchronized (this) {
					try {
						Thread.sleep(5000);
						is = true;
						this.notifyAll();
					} catch (Exception e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
			}
		}
	}
}