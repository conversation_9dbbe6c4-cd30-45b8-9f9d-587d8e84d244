package jxy2.jutnil;

import java.io.PrintStream;
import java.io.UnsupportedEncodingException;

/**
 * 控制台输出工具类 - 解决中文乱码问题
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ConsoleUtil {
    
    private static boolean isInitialized = false;
    
    /**
     * 初始化控制台编码设置
     */
    public static void initConsoleEncoding() {
        if (isInitialized) {
            return;
        }
        
        try {
            // 设置系统属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.stdout.encoding", "UTF-8");
            System.setProperty("sun.stderr.encoding", "UTF-8");
            
            // 在 Windows 系统上设置控制台编码
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                try {
                    // 设置控制台代码页为 UTF-8
                    ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "chcp 65001");
                    pb.start();
                } catch (Exception e) {
                    // 忽略异常
                }
                
                // 尝试重新设置 System.out 和 System.err 的编码
                try {
                    System.setOut(new PrintStream(System.out, true, "UTF-8"));
                    System.setErr(new PrintStream(System.err, true, "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    // 使用默认编码
                }
            }
            
            isInitialized = true;
            
        } catch (Exception e) {
            // 编码设置失败，使用默认设置
            System.err.println("控制台编码设置失败: " + e.getMessage());
        }
    }
    //java.net.MalformedURLException: Accessing a URL protocol that was not enabled. The URL protocol http is supported but not enabled by default. It must be enabled by adding the --enable-url-protocols=http option to the native-image command.
    /**
     * 安全的中文输出方法
     * @param message 要输出的消息
     */
    public static void println(String message) {
        try {
            // 确保编码已初始化
            if (!isInitialized) {
                initConsoleEncoding();
            }
            
            // 在 Windows 上，尝试转换编码if not exist "META-INF\native-image" mkdir META-INF\native-image
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                try {
                    // 将 UTF-8 字符串转换为 GBK 编码输出到 Windows 控制台
                    byte[] gbkBytes = message.getBytes("GBK");
                    String gbkString = new String(gbkBytes, "GBK");
                    System.out.println(gbkString);
                } catch (Exception e) {
                    // 如果 GBK 转换失败，直接输出
                    System.out.println(message);
                }
            } else {
                // 非 Windows 系统直接输出
                System.out.println(message);
            }
        } catch (Exception e) {
            // 最后的备选方案
            System.out.println(message);
        }
    }
    
    /**
     * 安全的中文错误输出方法
     * @param message 要输出的错误消息
     */
    public static void printlnErr(String message) {
        try {
            // 确保编码已初始化
            if (!isInitialized) {
                initConsoleEncoding();
            }
            
            // 在 Windows 上，尝试转换编码
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("windows")) {
                try {
                    // 将 UTF-8 字符串转换为 GBK 编码输出到 Windows 控制台
                    byte[] gbkBytes = message.getBytes("GBK");
                    String gbkString = new String(gbkBytes, "GBK");
                    System.err.println(gbkString);
                } catch (Exception e) {
                    // 如果 GBK 转换失败，直接输出
                    System.err.println(message);
                }
            } else {
                // 非 Windows 系统直接输出
                System.err.println(message);
            }
        } catch (Exception e) {
            // 最后的备选方案
            System.err.println(message);
        }
    }
    
    /**
     * 格式化输出方法
     * @param format 格式字符串
     * @param args 参数
     */
    public static void printf(String format, Object... args) {
        try {
            String message = String.format(format, args);
            println(message);
        } catch (Exception e) {
            System.out.printf(format, args);
        }
    }
}
