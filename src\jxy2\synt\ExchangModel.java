package jxy2.synt;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;

public class ExchangModel extends JPanel {
    public JLabel skname,skiimg,skIcon,skillnames,frames;
    public JLabel[] select =new JLabel[2];
    public StringBuilder builder = new StringBuilder();
    public Goodstable goodstable;
    public ExchangModel() {
        setPreferredSize(new Dimension(249, 55));
        setOpaque(false);
        setLayout(null);
        getSkname();
        getSelect();
        getSkIcon();
        getSkillnames();
        getFrames();
        getSkiimg();
    }

    /**初始化技能信息*/
    public void initSkillInfo(Goodstable goodstable){
        //先清空原有的字符串
        if (this.builder != null) {this.builder.setLength(0);}
        this.goodstable=goodstable;
        skname.setText(goodstable.getGoodsname());
        String[] vs = goodstable.getValue().split("\\|");
        StringBuilder skillNamesBuilder = new StringBuilder(); // 使用 StringBuilder 来构建最终的字符串

        for (String skillIdStr : vs) { // 使用增强 for 循环代替索引循环
            Skill skill = UserMessUntil.getSkillId(skillIdStr);
            if (skill != null) {
                if (builder.length()!=0) {builder.append("|");}
                skillNamesBuilder.append("[").append(skill.getSkillname()).append("]");
                builder.append(skill.getSkillid());
            }
        }
        skillnames.setText("技能："+skillNamesBuilder); // 设置最终的技能名字符串
        skIcon.setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),35,35));
    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
        for (int j = 0; j < synthesisJPanl.getGoodstables().length; j++) {
            Goodstable good = synthesisJPanl.getGoodstables()[j];
            select[j].setVisible(good != null && good.getRgid().compareTo(goodstable.getRgid()) == 0);
        }
    }

    public void getSkname() {
        if (skname == null){
            skname = new JLabel();
            skname.setBounds(73, 10, 100, 17);
            skname.setFont(UIUtils.MSYH_HY13);
            skname.setForeground(UIUtils.Color_pet_reis);
            add(skname);
        }
    }
    public void getSkillnames() {
        if (skillnames == null){
            skillnames = new JLabel("技能：");
            skillnames.setBounds(73, 30, 182, 17);
            skillnames.setFont(UIUtils.MSYH_HY13);
            skillnames.setForeground(UIUtils.COLOR_Wing1);
            add(skillnames);
        }
    }
    public void getSkiimg() {
        if (skiimg == null){
            skiimg = new JLabel();
            skiimg.setBounds(5, 5, 240, 52);
            skiimg.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz69,240,52,"defaut.wdf"));
            add(skiimg);
        }
    }
    public void getFrames() {
        if (frames == null){
            frames = new JLabel();
            frames.setBounds(5, 5, 238, 50);
            add(frames);
        }
    }
    public void getSkIcon() {
        if (skIcon == null){
            skIcon = new JLabel();
            skIcon.setBounds(20, 10, 35, 35);
            add(skIcon);
        }
    }

    public void getSelect() {
        for (int i = 0; i < 2; i++) {
            if (select[i]==null){
                select[i] = new JLabel("[已选中]");
                select[i].setBounds(150, 10, 100, 17);
                select[i].setFont(UIUtils.MSYH_HY13);
                select[i].setForeground(Color.GREEN);
                select[i].setVisible(false);
                add(select[i]);
            }
        }

    }

    public void setSelect(JLabel[] select) {
        this.select = select;
    }
}
