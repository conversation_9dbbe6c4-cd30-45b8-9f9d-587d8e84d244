package come.tool.JDialog;

import java.math.BigDecimal;
import java.util.List;

import org.come.Frame.LingbaoJframe;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.model.Lingbao;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserData;

import com.tool.role.ExpUtil;

public class LingTupoJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		Lingbao lingbao = (Lingbao) object;
		if (tishi) {
			int lvl=lingbao.getLingbaolvl().intValue();
			int sum=lvl/5;
			int[] goodids=new int[sum];
			for (int i = 0; i < goodids.length; i++) {
				goodids[i]=10079;
			}
			List<Integer> integers =GoodsListFromServerUntil.chaxuns(goodids);
			if (integers==null) {
				FrameMessageChangeJpanel.addtext(5,"物品不足",null,null);
				return;
			}
			GoodsListFromServerUntil.Delete(integers);
		    long exp=lingbao.getLingbaoexe().longValue();
		    long maxexp=ExpUtil.LFExp(lvl);
		    lvl+=1;
		    exp-=maxexp;
		    lingbao.setLingbaolvl(new BigDecimal(lvl));
		    lingbao.setLingbaoexe(new BigDecimal(exp));
			UserData.upling(lingbao);
			LingbaoJframe.getLingbaoJframe().getLingbaoJpanel().getLingbaoCardJpanel().getAttributeJpanel().sj(lingbao);
			
		}
	}

}
