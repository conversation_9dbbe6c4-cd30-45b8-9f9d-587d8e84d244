package jxy2.wdfDome.bean;

/**
 * WasData 类用于表示 WAS（Web Application Server）相关的数据。
 */
public class WasData {
    private long id; // 数据记录的唯一标识符
    private long fileSize; // 文件大小，以字节为单位
    private long fileOffset; // 文件在存储介质中的偏移量，以字节为单位
    private long fileSpace; // 文件占用的存储空间大小，以字节为单位

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getFileSize() {
        return this.fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public long getFileOffset() {
        return this.fileOffset;
    }

    public void setFileOffset(long fileOffset) {
        this.fileOffset = fileOffset;
    }

    public long getFileSpace() {
        return this.fileSpace;
    }

    public void setFileSpace(long fileSpace) {
        this.fileSpace = fileSpace;
    }
}
