package jxy2.flight;

import com.tool.btn.MoBanBtn;
import com.tool.image.ImageMixDeal;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class FlyBtn  extends MoBanBtn {
    public boolean open = false;
    public FlyJPanel flightJPanel;
    public FlyPracticeJPanel flyPracticeJPanel;
    public FlyBtn(String iconpath, int type, String text, int index, FlyJPanel flightJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, index, type, null);  // 修改为正确的参数顺序
        setNtext(text);
        this.index = index;
        this.flightJPanel = flightJPanel;
    }

    public FlyBtn(String iconpath, int type, String text,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, 0,type, null);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.index=index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public FlyBtn(String iconpath, int type, String text, int index, FlyPracticeJPanel flyPracticeJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, index, type, null);  // 修改为正确的参数顺序
        setNtext(text);
        this.index = index;
        this.flyPracticeJPanel = flyPracticeJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        String msg;
            if (flyPracticeJPanel!=null){
                if (index==0){
                    msg = Agreement.getAgreement().FlyAgreement("S"+ FlyFrame.getFlightFrame().getFlightJPanel().getAssemble());
                    SendMessageUntil.toServer(msg);
                }
            }else {
                switch (index)
                {
                    case 0://飞行
                        boolean hasEquippedFly = FlyJPanel.getFlyList().stream()
                                .anyMatch(fly -> fly.getFlyEquip() == 1);

                        if (!hasEquippedFly) {
                            ZhuFrame.getZhuJpanel().addPrompt("你还没有装备飞行器！");
                            return;
                        }


                        // 检查当前飞行状态并切换
                        boolean isFlying = ImageMixDeal.userimg.getRoleShow().getFlyID() > 0;
                        // 发送飞行状态变更消息到服务器，格式：A + roleId|flyState
                        // 不再立即设置飞行状态，等待服务器响应
                        msg = Agreement.getAgreement().FlyAgreement(
                                "A" + ImageMixDeal.userimg.getRoleShow().getRole_id() + "|" + (isFlying ? "0" : "1")
                        );
                        SendMessageUntil.toServer(msg);
                        // 移除立即更新显示的代码，等待服务器广播后自动更新
                        break;
                    case 1://装备
                        msg = Agreement.getAgreement().FlyAgreement("B" + flightJPanel.getAssemble());
                        SendMessageUntil.toServer(msg);
                        break;
                    case 2://修炼
                        FlyJPanel flyJPanel  = FlyFrame.getFlightFrame().getFlightJPanel();
                        Fly fly = FlyJPanel.FindAircraftById(flyJPanel.getAssemble());
                        if (fly!=null){
                            FlyPracticeFrame.getFlyPracticeFrame().getFlyPracticeJPanel().initialization(fly);
                        }
                        Util.StopFrame(150);
                        break;
                    case 3://补充
                        msg = Agreement.getAgreement().FlyAgreement("G");
                        SendMessageUntil.toServer(msg);
                        break;
                    case 4://升阶
                        if (flightJPanel.getAssemble()==null)return;
                        msg = Agreement.getAgreement().FlyAgreement("J"+ flightJPanel.getAssemble());
                        SendMessageUntil.toServer(msg);
                        break;
                    case 5://销毁
                        flyJPanel  = FlyFrame.getFlightFrame().getFlightJPanel();
                        fly = FlyJPanel.FindAircraftById(flyJPanel.getAssemble());
                        if (fly!=null) {
                            if (fly.getFlyEquip()==1){
                                ZhuFrame.getZhuJpanel().addPrompt("无法删除装备中的飞行器！");
                                return;
                            }
                            if (fly.getFlysteps()==5){
                                ZhuFrame.getZhuJpanel().addPrompt("无法删除满阶飞行器！");
                                return;
                            }

                            OptionsJframe.getOptionsJframe().getOptionsJpanel()
                                    .showBox(TiShiUtil.DestroyFly, this, "#W  您确定要销毁[#G" + fly.getFlyname()+"]#W吗? 销毁后所有数据将被删除！请谨慎操作");

                        }

                        break;
                }
            }

    }
}