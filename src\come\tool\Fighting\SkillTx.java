package come.tool.Fighting;

import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import org.come.bean.Skill;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;

public class SkillTx {

	private int id;
	private Skill skill;
	//位置
	private int x,y;
	//判断是否已经点击过了
	private boolean is;
	private ImageIcon icon;
	private static Image image;
	public static Image getImage(){
		if (image==null) {image=new ImageIcon("img/xy2uiimg/技能框_w36,h36.png").getImage();}
		return image;
	}
	public SkillTx() {
		// TODO Auto-generated constructor stub
		if (image==null) {image=new ImageIcon("img/xy2uiimg/技能框_w36,h36.png").getImage();}
	}
	public void draw(Graphics g){
		if (icon==null) {
			icon=CutButtonImage.TYCSkill(id);
		}
		g.drawImage(image, x-3,y-3,36,36, null);
		g.drawImage(icon.getImage(), x,y,30,30, null);
	}
	public void SReset(String skillName,int x,int y){
		this.is=false;
		this.x=x;
		this.y=y;
		if (skill==null||!skill.getSkillname().equals(skillName)) {
			skill=UserMessUntil.getskill1(skillName);
			id=Integer.parseInt(skill.getSkillid());
			this.icon=null;	
		}	
	}
	public boolean isMonitor(int Mx,int My){
		if (x<=Mx&&x+35>=Mx&&y<=My&&y+35>=My) {
			return true;
		}
		return false;
	}
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public Skill getSkill() {
		return skill;
	}
	public void setSkill(Skill skill) {
		this.skill = skill;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public boolean isIs() {
		return is;
	}
	public void setIs(boolean is) {
		this.is = is;
	}
	public ImageIcon getIcon() {
		return icon;
	}
	public void setIcon(ImageIcon icon) {
		this.icon = icon;
	}
}
