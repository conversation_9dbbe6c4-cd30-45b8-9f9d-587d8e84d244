package jxy2.chatv;

import com.tool.tcpimg.InputBean;
import com.tool.tcpimg.UIUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

public class ChatBoxs extends JPanel {
    private List<ChatRichLabel> chatlabels;
    public boolean is = false;
    public ChatBoxs() {
        chatlabels = new ArrayList<>();
        this.setBackground(UIUtils.Color_BACK);
    }
    // 屏幕透明度
    private float Alpha = 0.0f;
    // 判断显示位置(显示在左 true)
    private boolean display = false;
    // 聊天框的大小

    private int w = 0;
    private int h = 0;
    // 显示偏差
    private int deviation = 0;
    public String string="#Q";
    /** 监听 */
    public InputBean isMonitor(int x, int y, MouseEvent e) {
        int History;
        int hig = -deviation * 22;
        for (int i = 0; i < chatlabels.size(); i++) {
            ChatRichLabel c = chatlabels.get(i);
            History = c.getHeight();
            hig += History;
            if (hig >= 0 && hig >= y) {
                return c.isMonitor(x, y - (hig - History),e);
            }
        }
        return null;
    }

    @Override
    public void paint(Graphics g) {
        if (chatlabels.size() != 0) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, Alpha));
            g2d.setColor(Color.BLACK);
            if (h == 0) {
                g2d.fillRoundRect(1, 1, getWidth()+5, getHeight(), 0, 0);
                g2d.fillRoundRect(0, 0, getWidth()+5, getHeight(), 0, 0);
            } else {
                g2d.fillRoundRect(1, 1, w+5,h, 0, 0);
                g2d.fillRoundRect(0, 0, w+5,h, 0, 0);
            }


            g2d.dispose();
            // 记录上一个窗体所用的高度
            int History = 0;
            int hig = -deviation * 22;
            g.translate(0, hig);
            for (int i = 0; i < chatlabels.size(); i++) {
                Component c = chatlabels.get(i);
                if (chatlabels.get(i).getText().startsWith(string)&&is){
                    g.translate(c.getX(), History);
                    History = c.getHeight();
                    hig += History;
                    if (hig >= 0) {
                        c.paint(g);
                    }
                }else {
                    if (!is) {
                        g.translate(c.getX(), History);
                        History = c.getHeight();
                        hig += History;
                        if (hig >= 0) {
                            c.paint(g);
                        }
                    }
                }

          }
            g2d.dispose();
        }
    }

    public ChatRichLabel ChatAddText(String chatText, int size, Font font) {
        ChatRichLabel label = new ChatRichLabel(chatText, font);
        Dimension d = label.computeSize(size);
        label.setSize(d);
        this.chatlabels.add(label);
        ChatFormsize(size);
        return label;
    }

    public ChatRichLabel ChatromeText(String chatText, int size, Font font) {
        ChatRichLabel label = new ChatRichLabel(chatText, font);
        Dimension d = label.computeSize(size);
        label.setSize(d);
        ChatFormsize(size);
        return label;
    }

    /**TODO 移除式添加*/
    public ChatRichLabel removeAddText(String chatText, int size, Font font) {
        removeAll();
        ChatRichLabel label=chatlabels.size()!=0?chatlabels.get(0):null;
        chatlabels.clear();
        if (label==null) {
            label = new ChatRichLabel(chatText, font);
            Dimension d = label.computeSize(size);
            label.setSize(d);
        }else {
            label.setFont(font);
            label.setTextSize(chatText, size);
        }
        this.chatlabels.add(label);
        ChatFormsize(size);
        return label;
    }


    public void ChatFormsize(int size) {
        int h = 2;
        for (ChatRichLabel chatlabel : chatlabels) {
            if (chatlabel.getText().startsWith(string) && is) {
                h += (int) chatlabel.getSize().getHeight();
            } else {
                if (!is) {
                    h += (int) chatlabel.getSize().getHeight();
                }
            }
        }
        setSize(size, h);
    }

    public void RemoMsg(String msg){
        int w = ChatFrame.getChatFrame().getWidth()-38;
        int h = 1;
        for (int i = 0; i < chatlabels.size(); i++) {
            ChatRichLabel chatlabel = chatlabels.get(i);
            if (chatlabel.getText().startsWith(transformation(msg)) && is) {
                chatlabel.remove();
                h += (int) chatlabel.getSize().getHeight();
                setSize(w, h);
            }
        }
    }

    public String transformation(String msg){
        switch (msg){
            case "当前":
                return "#Q";
            case "系统":
                return "#T";
            case "队伍":
                return "#D";
            case "世界":
                return "#J";
            case "战斗":
                return "#Z";
            default:
                return "";
        }
    }

    /**清除对话记录*/
    public void removemsg() {
        removeAll();
        chatlabels.clear();
    }

    public int getDeviation() {
        return deviation;
    }

    public void setDeviation(int deviation) {
        this.deviation = deviation;
    }

    public int getW() {
        return w;
    }

    public void setW(int w) {
        this.w = w;
    }

    public int getH() {
        return h;
    }

    public void setH(int h) {
        this.h = h;
    }

    public List<ChatRichLabel> getChatlabels() {
        return chatlabels;
    }

    public void setChatlabels(List<ChatRichLabel> chatlabels) {
        this.chatlabels = chatlabels;
    }

    public boolean isDisplay() {
        return display;
    }

    public void setDisplay(boolean display) {
        this.display = display;
    }
}
