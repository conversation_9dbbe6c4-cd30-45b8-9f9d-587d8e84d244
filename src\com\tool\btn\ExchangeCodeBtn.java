package com.tool.btn;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ExchangeAwardJpanel;
import org.come.bean.ChangeRoleNameBean;
import org.come.bean.LoginResult;
import org.come.bean.PrivateData;
import org.come.entity.Goodstable;
import org.come.mouslisten.GoodsMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.SendRoleAndRolesummingUntil;
import org.come.until.Util;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.role.RoleReborn;
import com.tool.role.RoleSkill;
import com.tool.tcpimg.UIUtils;

public class ExchangeCodeBtn extends MoBanBtn {

    private ExchangeAwardJpanel awardJpanel;

    public ExchangeCodeBtn(String iconpath, int type, String text, ExchangeAwardJpanel awardJpanel) {
        super(iconpath, type, UIUtils.COLOR_BTNPUTONG);
        // TODO Auto-generated constructor stub
        setText(text);
        setFont(UIUtils.TEXT_HY16);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.awardJpanel = awardJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (getText().equals("取 消")) {
            FormsManagement.HideForm(73);
            return;
        }
        String text = awardJpanel.getTextCode().getText();
        if (text == null || text.equals("")) {
            ZhuFrame.getZhuJpanel().addPrompt("输入为空");
            return;
        }
        if (getText().equals("兑 换")) {
            // 发送消息给服务器
            String senmes = Agreement.exchangeGoodsAgreement(text);
            SendMessageUntil.toServer(senmes);
        } else if (getText().equals("修 改")) {
            if (awardJpanel.getRgid() != null) {
                ChangeNameupload(text, awardJpanel.getRgid());
            }
        } else if (getText().equals("修 正")) {
            if (awardJpanel.getRgid() != null) {
                ChangeBorn(text, awardJpanel.getRgid());
            }
        } else if (getText().equals("解 封")) {
            unJF(text, awardJpanel.getRgid());
        } else if (getText().equals("解 禁")) {
            unJJ(text, awardJpanel.getRgid());
        } else if (getText().equals("招 募")) {
            recruit(text);
        }
    }

    /** 解除封号 */
    public void unJF(String ChangeBorn, BigDecimal rgid) {
        String sendmes = Agreement.getAgreement().userAgreement(rgid + "|" + ChangeBorn);
        SendMessageUntil.toServer(sendmes);
        FormsManagement.HiddenDisplay(73);
    }

    /** 解除禁言 */
    public void unJJ(String ChangeBorn, BigDecimal rgid) {
        if (!ChangeBorn.matches("[0-9]+")) {
            ZhuFrame.getZhuJpanel().addPrompt2("请输入数字");
            return;
        }
        String sendmes = Agreement.getAgreement().userAgreement(rgid + "|" + ChangeBorn);
        SendMessageUntil.toServer(sendmes);
        FormsManagement.HiddenDisplay(73);
    }
    
    // 更改修正
    public static void ChangeBorn(String ChangeBorn, BigDecimal rgid) {
        String[] v = ChangeBorn.trim().split("-");
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (v.length > loginResult.getTurnAround() || v.length > 3) {
            ZhuFrame.getZhuJpanel().addPrompt2("只能输入于自身相同的转生次数");
            return;
        }
        int[] vv = getbz(v);
        if (vv == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("输入格式有误");
            return;
        }
        // 开始进入转生
        String yuben = null;
        for (int i = 0; i < vv.length; i++) {
            yuben = RoleReborn.reborn(RoleSkill.getRoleSkill().getAllSkill(vv[i], i * 5000 + 10000), yuben);
        }
        Goodstable goodstable = GoodsListFromServerUntil.Uerbiaoid(rgid);
        if (goodstable != null) {
            ZhuFrame.getZhuJpanel().addPrompt2("更改修正成功");
            GoodsMouslisten.gooduse(goodstable, 1);
            RoleProperty.Resetborn(null, yuben);
            PrivateData data = RoleData.getRoleData().getPrivateData();
            data.setBorn(yuben);
            SendRoleAndRolesummingUntil.sendRole(data);
            FormsManagement.HideForm(73);
        }
    }

    /** 检验输入格式 */
    public static int[] getbz(String[] v) {
        try {
            int[] a = new int[v.length];
            for (int i = 0; i < v.length; i++) {
                a[i] = Integer.parseInt(v[i]);
                if (a[i] < 1 || a[i] > 10)
                    return null;
            }
            return a;
        } catch (Exception e) {
            // TODO: handle exception
        }
        return null;
    }

    // 上传改名
    public static void ChangeNameupload(String ChangeName, BigDecimal rgid) {
        ChangeName = ChangeName.trim();
        int size = 0;
        char[] nz = ChangeName.toCharArray();
        for (int i = 0; i < nz.length; i++) {
            String a = nz[i] + "";
            if (a.getBytes().length == 1) {
                size++;
            } else {
                size += 2;
            }
        }
        if (size > 14) {
            ZhuFrame.getZhuJpanel().addPrompt2("名称太长");
            return;
        }
        if (ImageMixDeal.userimg.getRoleShow().getTeamInfo() == null
                || ImageMixDeal.userimg.getRoleShow().getTeamInfo().equals("")) {
            if (!ChangeName.equals(ImageMixDeal.userimg.getRoleShow().getRolename())) {
                if (special(ChangeName)) {
                    if (!Util.isIllegal(ChangeName)) {
                        ZhuFrame.getZhuJpanel().addPrompt("名称中包含非法字符！！");
                        return;
                    }
                    ChangeRoleNameBean bean = new ChangeRoleNameBean();
                    bean.setOldName(ImageMixDeal.userimg.getRoleShow().getRolename());
                    bean.setNewName(ChangeName);
                    bean.setRgid(rgid);
                    // 上传服务器
                    String sendmes = Agreement.getAgreement().ChangeRoleNameAgreement(
                            GsonUtil.getGsonUtil().getgson().toJson(bean));
                    SendMessageUntil.toServer(sendmes);
                    // 关闭改名窗体
                    FormsManagement.HideForm(73);
                } else {
                    ZhuFrame.getZhuJpanel().addPrompt2("修改后的名字不能有特殊符号");
                }
            } else {
                ZhuFrame.getZhuJpanel().addPrompt2("修改后的名字不能和自己重复");
            }
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("组队状态下不能使用改名卡");
        }
    }

    /** 判断字符串是否含有特殊符号 **/
    public static boolean special(String ChangeName) {
        String[] a = { "!", "|", "*", "&", "@", "#", "$", "%", "^", "/" };
        for (int i = 0; i < a.length; i++) {
            if (ChangeName.indexOf(a[i]) != -1) {
                return false;
            }
        }
        return true;
    }
    /**招募*/
    public void recruit(String text){
        if(!isNumeric(text)){
            ZhuFrame.getZhuJpanel().addPrompt2("请输入正确的数字");
            return;
        }
        SendMessageUntil.toServer(Agreement.getAgreement().hjslAgreement("Z"+text));
        FormsManagement.HideForm(73);
    }
    
    /**判断是否是数字*/
    public boolean isNumeric(String str) {  
        Pattern pattern = Pattern.compile("[0-9]*");  
        Matcher isNum = pattern.matcher(str);  
        return isNum.matches();
    }
}
