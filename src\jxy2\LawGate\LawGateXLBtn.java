package jxy2.LawGate;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class LawGateXLBtn extends MoBanBtn {
    public int BtnId;
    public LawGateXLJPanel lawGateXLJPanel;
    public LawGateMainJPanel lawGateMainJPanel;
    public LawGateXLBtn(String iconpath, int type, int BtnId, String labelName,
                        LawGateXLJPanel lawGateXLJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.lawGateXLJPanel = lawGateXLJPanel;
    }

    public LawGateXLBtn(String iconpath, int type, String text,int BtnId, LawGateMainJPanel lawGateMainJPanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        this.BtnId=BtnId;
        this.lawGateMainJPanel=lawGateMainJPanel;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (BtnId==1){
            String mes = Agreement.getAgreement().rolechangeAgreement("F"+lawGateXLJPanel.getLawGateSum());
            SendMessageUntil.toServer(mes);
        }else {//重置法门
            OptionsJframe.getOptionsJframe().getOptionsJpanel().
                    showBox(TiShiUtil.ResetLawGate, null, "#W重置法门技能需要消耗金钱#M200000#W两,你确定要重置吗？");


        }

    }
}
