package jxy2.zodiac;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.Skill;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
/**
 * 星图镶嵌面板
 * Star Map Inlay Panel
 * <AUTHOR>
 * @date 2024/12/1 下午9:09
*/
public class InlaidStarJPanel extends JPanel {
    public String xpname;
    public int type,index,sum;
    public Goodstable goodstable;
    public InlaidStarBtn setBs,setBs2 ;
    private RichLabel richLabel;
    private Skill skill;
    public InlaidStarJPanel() {
        this.setPreferredSize(new Dimension(250, 220));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        setBs = new InlaidStarBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "镶嵌", 1, this,"");
        setBs.setBounds(54, 170, 59, 24);
        this.add(setBs);
        setBs2 = new InlaidStarBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "关闭", 2, this,"");
        setBs2.setBounds(54+80, 170, 59, 24);
        this.add(setBs2);
        richLabel=new RichLabel("", UIUtils.MSYH_HY14, 255);
        richLabel.setBounds(102,60, 226, 110);
        this.add(richLabel);
    }

        /**初始化组件内容*/
        public void init(String[][] name,int i,int type,int sum) {
            isVa(sum,i);
            this.sum = sum;
            if (sum==1&&name!=null) {
                for (int j = 0; j < name.length; j++) {
                    String[] vs = name[j];
                    if (j == i) {
                        this.xpname = vs[0];
                        this.type = type;
                        this.index = i;
                        break;
                    }
                }
                goodstable = GoodsListFromServerUntil.getXpGoods(xpname);
            }else {
                ChineseZodiacPanel zodiacPanel = ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel();
                Skill skill = UserMessUntil.getSkillId(zodiacPanel.initSkillData(i));
                if (skill != null) {
                    this.skill = skill;
                    String[] vs = skill.getRemark().split("_");
                    richLabel.setText("#Y"+vs[1]);
                    this.index = i;
                }
            }
        }

    private void isVa(int sum, int i) {
        ChineseZodiacPanel chineseZodiacPanel = ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel();
        boolean is = false;
        if (chineseZodiacPanel != null && chineseZodiacPanel.sumlist != null && !chineseZodiacPanel.sumlist.isEmpty()) {
            is = chineseZodiacPanel.sumlist.get(0).equals(i+"");
        }
        setBs.setText(sum==1 ? "镶嵌" : is ? "已激活" : "激活");
        setBs.setBtn(sum==1 ? 1 : is ? -1 : 1);
        setBs2.setVisible(sum==1);
        richLabel.setVisible(sum==0);
        setBs.setBounds(sum==1 ? 54 : 150, sum==1 ? 170 : 210, 59, 24);
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.TxtImg, 0, 0, getWidth(), getHeight() + 14, 1);

        if (sum==1) {//
            Juitil.ImngBack(g, Juitil.tz258, 16, 55, 59, 58, 1);
            g.drawImage(goodstable != null ? Juitil.tz264.getImage() : Juitil.tz265.getImage(), 20, 58, 50, 50, null);
            Juitil.ImngBack(g, Juitil.maskimg, 83, 57, 150, 70, 1);
            if (xpname != null) {
                Juitil.Txtpet(g, 37, 80, this.xpname, goodstable != null ? Color.BLACK : Color.WHITE, UIUtils.TEXT_HY16);
            }
            Juitil.TextBackground(g, "镶嵌星魂", 13, 90, 10, UIUtils.COLOR_TREASURE, UIUtils.FZCY_HY15);
            Juitil.TextBackground(g, "该处镶嵌1颗" + xpname + "星", 13, 58, 30, UIUtils.COLOR_CCDDDDFF, UIUtils.FZCY_HY15);
            Juitil.TextBackground(g, "温馨提示：已镶嵌的星魂不能主动卸下。", 13, 14, 147, UIUtils.COLOR_NAME3, UIUtils.FZCY_HY13);
            Juitil.TextBackground(g, "镶嵌需消耗", 13, 91, 63, UIUtils.COLOR_White, UIUtils.FZCY_HY15);
        }else {//激活技能
            Juitil.ImngBack(g, Juitil.maskimg, 20, 57, 300, 120, 1);
            Juitil.TextBackground(g, "秘技激活", 13, 150, 10, UIUtils.COLOR_TREASURE, UIUtils.FZCY_HY15);
            Juitil.TextBackground(g, "每切换一次秘技需消耗10点魂值。", 13, 88, 30, UIUtils.COLOR_CCDDDDFF, UIUtils.FZCY_HY15);
            g.drawImage(Juitil.tz263.getImage(), 38, 62, 52, 52, this);
            Juitil.ImngBack(g, Juitil.tz267, 36, 60, 56, 56, 1);
        }
    }

    public String getXpname() {
        return xpname;
    }

    public void setXpname(String xpname) {
        this.xpname = xpname;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public Skill getSkill() {
        return skill;
    }

    public void setSkill(Skill skill) {
        this.skill = skill;
    }
}
