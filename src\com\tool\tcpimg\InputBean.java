package com.tool.tcpimg;

import org.come.bean.Coordinates;
import org.come.mouslisten.Mouselistener;
import org.come.until.Util;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class InputBean {
//  指标
	private transient Integer index;
//	 类型 0:保留 1:人物列表 2:物品 3:召唤兽 4:灵宝   11申请入队    12拉人入队
//	10正常寻路
//	20 21 22  任务目标寻路 
	private int type;
//	 id
	private BigDecimal id;
//	 别名
	private String name;
//	 颜色
	private String color;
	//坐标
	private Coordinates zb;
//  起始
	private transient Color color2;
//  起始
	private transient Integer s_x;
	private transient Integer s_y;
//  结束	
	private transient Integer e_x;
	private transient Integer e_y;
//  字体高
	private transient Integer height;
	private transient boolean isM;
	public InputBean() {
		// TODO Auto-generated constructor stub
	}
	public InputBean(int type) {
		super();
		this.type = type;
	}
	public InputBean(int type, BigDecimal id, String name, String color) {
		this(null,type,id,name,color,null);
	}
	public InputBean(int index, int type, BigDecimal id, String name,String color) {
		this(index,type,id,name,color,null);
	}
	public InputBean(int type,String name,Coordinates zb) {
		this(null,type,null,name,null,zb);
	}
	public InputBean(int type,BigDecimal id,String name,Coordinates zb) {
		this(null,type,id,name,null,zb);
	}
	public InputBean(Integer index, int type, BigDecimal id, String name,String color, Coordinates zb) {
		this.index = index;
		this.type = type;
		this.id = id;
		this.name = name;
		this.color = color;
		this.zb = zb;
	}
	public InputBean( BigDecimal id,int type,Coordinates zb) {
		this.type = type;
		this.zb = zb;
		this.id = id;
	}
	public InputBean(int type,Coordinates zb) {
		this.zb = zb;
		this.type = type;
	}


	/**判断光标是否在指标内*/
	public boolean isIndex(int offset){
		if (offset>index&&(index+name.length())>offset) {
			return true;
		}
		return false;
	}
	int xd = 0;
	int yd = 0;
	/**判断点击*/
	public boolean isMonitor(int x,int y,MouseEvent e){
		if (e.getButton() == MouseEvent.BUTTON3) {
			int y1 = y+552;
			int xdd = (Util.mapmodel.getShot_x() + x) / 20;
			int ydd = (Util.mapmodel.getShot_y() + y1) / 20;
			if (xd != xdd || yd != ydd) {
				xd = xdd;
				yd = ydd;
				Mouselistener.Pathfinding(xd, yd);
			}

		}else {
			if (height != null) {
				if (y >= s_y && y <= (e_y + height)) {
					if (s_y.intValue() == e_y.intValue()) {
						return x >= s_x && x <= e_x;
					} else if (y <= (s_y + height)) {
						return x >= e_x;
					} else if (y < e_y) {
						return true;
					} else if (x <= e_x) {
						return true;
					}
				}
			}
		}
		return false;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getColor() {
		return color;
	}
	public void setColor(String color) {
		this.color = color;
	}
	public Color getColor2() {
		return color2;
	}
	public void setColor2(Color color2) {
		this.color2 = color2;
	}
	public Integer getS_x() {
		return s_x;
	}
	public void setS_x(Integer s_x) {
		this.s_x = s_x;
	}
	public Integer getS_y() {
		return s_y;
	}
	public void setS_y(Integer s_y) {
		this.s_y = s_y;
	}
	public Integer getE_x() {
		return e_x;
	}
	public void setE_x(Integer e_x) {
		this.e_x = e_x;
	}
	public Integer getE_y() {
		return e_y;
	}
	public void setE_y(Integer e_y) {
		this.e_y = e_y;
	}
	public Integer getHeight() {
		return height;
	}
	public void setHeight(Integer height) {
		this.height = height;
	}
	public Coordinates getZb() {
		return zb;
	}
	public void setZb(Coordinates zb) {
		this.zb = zb;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}
	public boolean isM() {
		return isM;
	}
	public void setM(boolean isM) {
		this.isM = isM;
	}
	
}
