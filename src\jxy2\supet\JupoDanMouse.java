package jxy2.supet;

import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Frame.PetSkillsJframe;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.util.Arrays;

public class JupoDanMouse {
    // 刷新展示召唤兽技能的方法
    public static void refreshPetSkills(RoleSummoning pet) {
        if (pet==null)return; 
        JupoDanJPanel jupoDanJPanel = JupoDanFrame.getJupoDanFrame().getJupoDanJPanel();
        if (pet.getSkilllock()==1){
            String leng68_18= Util.SwitchUI==0||Util.SwitchUI==1  ? "0x6FAC1023": "0x6FAB1026";
            jupoDanJPanel.getBtngoodlock().setIcons(Juitil.getImgs(leng68_18));
            jupoDanJPanel.getBtngoodlock().BtnId = 4;
        }else {
            String leng68_18= Util.SwitchUI==0||Util.SwitchUI==1  ? "0x6FAC1024": "0x6FAB1027";
            jupoDanJPanel.getBtngoodlock().setIcons(Juitil.getImgs(leng68_18));
            jupoDanJPanel.getBtngoodlock().BtnId = 3;
        }


        jupoDanJPanel.RewriteState(pet);
        //getOpenSeal已解封的格子
        int totalSkills = 9; // 总技能格子数量
        int openedSkills = pet.getOpenSeal(); // 已开启的技能格子数量,1
        int defaultUnsealed = pet.getSealstatus(); // 默认解封的技能格子数量,2
        int totalUnsealed =  pet.getNotobtained(); // 全部未获得,7
        // 判断该召唤兽是否有技能
        if (pet.getPetSkills() != null && !pet.getPetSkills().isEmpty()) {
            PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getMapPetModelPanel().get(0).getBox().setText(null);
            String[] petskills = pet.getPetSkills().split("\\|");
            // 先将界面清空
            for (int i = 0; i < totalSkills; i++) {
                jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                jupoDanJPanel.getLabPetskills()[i].setVisible(false);
                jupoDanJPanel.getLabPetskills()[i].setBorder(BorderFactory.createEmptyBorder());
                jupoDanJPanel.getLabSkilllock()[i].setIcon(null);
                jupoDanJPanel.getShowPetJuPoDanSkillsMouslistens()[i].setSkill(null);
            }
            // 将技能展示出来
            for (int i = 0; i < petskills.length; i++) {
                Skill skill = UserMessUntil.getSkillBean().getSkillMap().get(petskills[i]);
                ImageIcon img = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(),50,50,"skill.wdf");
                jupoDanJPanel.getLabPetskills()[i].setIcon(img);
                jupoDanJPanel.getLabPetskills()[i].setVisible(true);
                jupoDanJPanel.getShowPetJuPoDanSkillsMouslistens()[i].setSkill(skill);
            }

            // 设置没有解封的
            if (openedSkills <= totalSkills) {
                for(int i = 0; i < openedSkills; ++i) {
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(1);
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(false);
                }
                for (int i = openedSkills; i < totalUnsealed; i++) {
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(2);
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                }
                for(int i = openedSkills; i < defaultUnsealed; ++i) {
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(0);
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                }
                jupoDanJPanel.getLabSkilllock()[openedSkills].setIcon(null);
            }

            if (pet.getSkilllock()==1){
                for (int i = 0; i < totalSkills; i++) {
                        ImageIcon locking = CutButtonImage.getWdfPng(ImgConstants.tz236,15,17,"defaut.wdf");
                        jupoDanJPanel.getLabSkilllock()[i].setIcon(locking);
                }
            }

        } else {
            PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getMapPetModelPanel().get(0).getBox().setText(null);
            // 先将界面清空
            for (int i = 0; i < totalSkills; i++) {
                jupoDanJPanel.getPetSkillsBtns()[i].setVisible(false);
                jupoDanJPanel.getLabPetskills()[i].setBorder(BorderFactory.createEmptyBorder());
                jupoDanJPanel.getLabPetskills()[i].setIcon(null);
                jupoDanJPanel.getLabSkilllock()[i].setIcon(null);
            }
            // 设置没有解封的
            if (openedSkills <= totalSkills) {
                for(int i = 0; i < openedSkills; ++i) {
                    //第一步未获得状态设置2
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(1); // 设置为未获得状态
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                }
                for (int i = openedSkills; i < totalUnsealed; i++) {
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(2); // 设置为未获得状态
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                }
                //设置已解封的
                for(int i = openedSkills; i < defaultUnsealed; ++i) {
                    jupoDanJPanel.getPetSkillsBtns()[i].btnchange(0);
                    jupoDanJPanel.getPetSkillsBtns()[i].setVisible(true);
                }
                jupoDanJPanel.getLabSkilllock()[openedSkills].setIcon(null);
            }
        }


        // 判断召唤兽有没有神兽技能
        if (pet.getBeastSkills() != null && !pet.getBeastSkills().isEmpty()) {
            // 将技能展示出来
            ImageIcon img = CutButtonImage.getWdfPng("0x6B6B" + pet.getBeastSkills(),50,50,"skill.wdf");
            jupoDanJPanel.getLabPetskills()[8].setIcon(img);
            jupoDanJPanel.getPetSkillsBtns()[8].btnchange(1); // 设置为未获得状态
            jupoDanJPanel.getPetSkillsBtns()[8].setVisible(true);
            jupoDanJPanel.getLabPetskills()[8].setVisible(true);
            jupoDanJPanel.getLabSkilllock()[8].setVisible(true);

        } else {
            jupoDanJPanel.getLabPetskills()[8].setIcon(null);
            String[] ssn = {"2","3","4"};
            jupoDanJPanel.getPetSkillsBtns()[8].btnchange(0);
            jupoDanJPanel.getPetSkillsBtns()[8].setVisible(Arrays.asList(ssn).contains(pet.getSsn()));
            jupoDanJPanel.getLabPetskills()[8].setVisible(Arrays.asList(ssn).contains(pet.getSsn()));
            jupoDanJPanel.getLabSkilllock()[8].setVisible(Arrays.asList(ssn).contains(pet.getSsn()));
            if (pet.getSkilllock()==1) {
                ImageIcon locking = CutButtonImage.getWdfPng(ImgConstants.tz236, 15, 17, "defaut.wdf");
                jupoDanJPanel.getLabSkilllock()[8].setIcon(locking);
            }
        }
    }
}
