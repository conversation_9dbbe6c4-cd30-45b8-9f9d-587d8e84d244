package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.role.RoleTX;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TaobaoCourtSplendidJpanel;
import org.come.bean.BuyShopBean;
import org.come.model.Eshop;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.CutButtonImage;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.List;

public class SpecificBtn extends MoBanBtn {

    private int caozuo;
    private Eshop eshop;
    private TaobaoCourtSplendidJpanel taobaoCourtSplendidJpanel;

    public SpecificBtn(String iconpath, int type, String text, int caozuo) {
        super(iconpath, type,UIUtils.COLOR_BTNTEXT);
        this.caozuo = caozuo;
        this.setText(text);
        setFont(UIUtils.TEXT_FONT);
        if (caozuo >= 3 && caozuo <= 5) {
            setForeground(Color.yellow);
        } else {
            setForeground(Color.white);
        }
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        // TODO Auto-generated constructor stub
    }

    public SpecificBtn(String iconpath, int type, String text, int caozuo,String prowpt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0,UIUtils.COLOR_BTNTEXT,prowpt);
        this.caozuo = caozuo;
        setFont(UIUtils.TEXT_FONT);
        this.setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public SpecificBtn(String iconpath, int type, String text, int caozuo,
            TaobaoCourtSplendidJpanel taobaoCourtSplendidJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.taobaoCourtSplendidJpanel = taobaoCourtSplendidJpanel;
        this.setText(text);
        if (caozuo > 18 && caozuo < 22) {
            setFont(UIUtils.TEXT_HY16);
            setForeground(Color.white);
        } else {
            setFont(UIUtils.TEXT_FONT);
            setForeground(Color.white);
        }
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        // TODO Auto-generated constructor stub
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 1) {// 打开形象装扮面板
            FormsManagement.showForm(38);
        } else if (caozuo == 2) {// 一键卸除
            RoleTX.getRoleTX().EToggle(-1);
            RoleTX.getRoleTX().EToggle(-2);
            RoleTX.getRoleTX().EToggle(-3);
            RoleTX.getRoleTX().EToggle(-4);
        } else if (caozuo == 3) {// 保存形象
            RoleTX.getRoleTX().BCXX();
        } else if (caozuo == 4) {// 捐献

        } else if (caozuo == 5) {// 多宝阁购买
            taobaoCourtSplendidJpanel.showTaobao();
        } else if (caozuo == 6) {// 人物向左转向
            RoleTX.getRoleTX().upDir(0, true);
        } else if (caozuo == 7) {// 人物向右转向
            RoleTX.getRoleTX().upDir(0, false);
        } else if (caozuo == 8) {// 向上翻页
            RoleTX.getRoleTX().Toggle(-1, 0);
        } else if (caozuo == 9) {// 向下翻页
            RoleTX.getRoleTX().Toggle(-1, 1);
        } else if (caozuo == 10) {// 购买按钮
            if (eshop == null)
                return;
            BUY(eshop);
        } else if (caozuo == 11) {// 首页
            List<Eshop> lEshops = taobaoCourtSplendidJpanel.returnlEshops(taobaoCourtSplendidJpanel.getType());
            if (lEshops == null || (lEshops != null && lEshops.size() <= 0))
                return;
            taobaoCourtSplendidJpanel.setNowpage(1);
            // 刷新界面
            taobaoCourtSplendidJpanel.refreshDbgSplendid(lEshops, taobaoCourtSplendidJpanel.getNowpage());
        } else if (caozuo == 12) {// 上一页
            List<Eshop> lEshops = taobaoCourtSplendidJpanel.returnlEshops(taobaoCourtSplendidJpanel.getType());
            if (lEshops == null || (lEshops != null && lEshops.size() <= 0))
                return;
            taobaoCourtSplendidJpanel.setNowpage(taobaoCourtSplendidJpanel.getNowpage() - 1 <= 1 ? 1
                    : taobaoCourtSplendidJpanel.getNowpage() - 1);
            // 刷新界面
            taobaoCourtSplendidJpanel.refreshDbgSplendid(lEshops, taobaoCourtSplendidJpanel.getNowpage());
        } else if (caozuo == 13) {// 下一页
            List<Eshop> lEshops = taobaoCourtSplendidJpanel.returnlEshops(taobaoCourtSplendidJpanel.getType());
            if (lEshops == null || (lEshops != null && lEshops.size() <= 0))
                return;
            // 总页数
            int totalpage = lEshops.size() / 6 + (lEshops.size() % 6 == 0 ? 0 : 1);
            taobaoCourtSplendidJpanel.setNowpage(taobaoCourtSplendidJpanel.getNowpage() + 1 >= totalpage ? totalpage
                    : taobaoCourtSplendidJpanel.getNowpage() + 1);
            // 刷新界面
            taobaoCourtSplendidJpanel.refreshDbgSplendid(lEshops, taobaoCourtSplendidJpanel.getNowpage());
        } else if (caozuo == 14) {// 末页
            List<Eshop> lEshops = taobaoCourtSplendidJpanel.returnlEshops(taobaoCourtSplendidJpanel.getType());
            if (lEshops == null || (lEshops != null && lEshops.size() <= 0))
                return;
            // 总页数
            int totalpage = lEshops.size() / 6 + (lEshops.size() % 6 == 0 ? 0 : 1);
            taobaoCourtSplendidJpanel.setNowpage(totalpage);
            // 刷新界面
            taobaoCourtSplendidJpanel.refreshDbgSplendid(lEshops, taobaoCourtSplendidJpanel.getNowpage());
        } else if (caozuo == 15) {// 人物向左转向（多宝阁）
            RoleTX.getRoleTX().upDir(1, true);
        } else if (caozuo == 16) {// 人物向右转向（多宝阁）
            RoleTX.getRoleTX().upDir(1, false);
        } else if (caozuo == 17) {// 卸除
            // TaobaoCourtJpanel courtJpanel =
            // TaobaoCourtJframe.getTaobaoCourtJframe().getCourtJpanel();
            if (taobaoCourtSplendidJpanel.getEshops() == null)
                return;
            for (int i = 0; i < taobaoCourtSplendidJpanel.getEshops().length; i++) {
                // 将之前的特效移除
                if (taobaoCourtSplendidJpanel.getEshops()[i] != null) {
                    RoleTX.getRoleTX().removeTX(1,
                            -Integer.parseInt(taobaoCourtSplendidJpanel.getEshops()[i].getEshopiid()));
                    taobaoCourtSplendidJpanel.getEshops()[i] = null;
                }
            }
            // 装扮数清零
            taobaoCourtSplendidJpanel.setZbs(0);
            // 判断面板是否打开
            if (FormsManagement.getframe(51).isVisible()) {// 打开
                taobaoCourtSplendidJpanel.showTxMsg();
            }
        } else if (caozuo == 18) {// 试穿
            if (FormsManagement.getframe(51).isVisible()) {
                FormsManagement.HideForm(51);
            } else {
                // 展示面板信息
                taobaoCourtSplendidJpanel.showTxMsg();
                FormsManagement.showForm(51);
            }
        } else if ((caozuo >= 19) && (caozuo <= 21)) {
            if ((caozuo - 8) != taobaoCourtSplendidJpanel.getType()) {
                try {
                    if (taobaoCourtSplendidJpanel.getType() == 11) {
                        taobaoCourtSplendidJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B119.png"));
                    } else if (taobaoCourtSplendidJpanel.getType() == 12) {
                        taobaoCourtSplendidJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B121.png"));
                    } else if (taobaoCourtSplendidJpanel.getType() == 13) {
                        taobaoCourtSplendidJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B123.png"));
                    }
                    taobaoCourtSplendidJpanel.setType(caozuo - 8);
                    if (taobaoCourtSplendidJpanel.getType() == 11) {
                        taobaoCourtSplendidJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/B120.png"));
                    } else if (taobaoCourtSplendidJpanel.getType() == 12) {
                        taobaoCourtSplendidJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/B122.png"));
                    } else if (taobaoCourtSplendidJpanel.getType() == 13) {
                        taobaoCourtSplendidJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/B124.png"));
                    }

                } catch (Exception e1) {
                    e1.printStackTrace();
                }
            }
            taobaoCourtSplendidJpanel.refresGoodsSplendid();
        }
        // else if (caozuo == 20) {
        // taobaoCourtSplendidJpanel.setType(12);
        // try {
        // taobaoCourtSplendidJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
        // taobaoCourtSplendidJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/4.png"));
        // taobaoCourtSplendidJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
        // } catch (Exception e1) {
        // // TODO Auto-generated catch block
        // e1.printStackTrace();
        // }
        // taobaoCourtSplendidJpanel.refresGoodsSplendid();
        // } else if (caozuo == 21) {
        // taobaoCourtSplendidJpanel.setType(13);
        // try {
        // taobaoCourtSplendidJpanel.getBtnTx().setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
        // taobaoCourtSplendidJpanel.getBtnZsp().setIcons(CutButtonImage.cuts("inkImg/button/3.png"));
        // taobaoCourtSplendidJpanel.getBtnZj().setIcons(CutButtonImage.cuts("inkImg/button/4.png"));
        // } catch (Exception e1) {
        // // TODO Auto-generated catch block
        // e1.printStackTrace();
        // }
        // taobaoCourtSplendidJpanel.refresGoodsSplendid();
        // }
    }

    /** 购买 */
    public void BUY(Eshop eshop) {
        if (!Util.canBuyOrno) {
            ZhuFrame.getZhuJpanel().addPrompt2("背包没有解锁!");
            return;
        }
        if (RoleData.getRoleData().getPackRecord().isTX(-Integer.parseInt(eshop.getEshopiid()) + "")) {
            ZhuFrame.getZhuJpanel().addPrompt2("你已拥有该特效");
            return;
        }
        if ((RoleData.getRoleData().getLoginResult().getCodecard().compareTo(new BigDecimal(eshop.getEshopprice()))) >= 0) {
            BuyShopBean bean = new BuyShopBean();
            bean.setAte(0);
            bean.setCd(eshop.getEshopid());
            bean.setSum(1);
            String senmes = Agreement.getAgreement().nbuyAgreement(GsonUtil.getGsonUtil().getgson().toJson(bean));
            SendMessageUntil.toServer(senmes);
        } else {
            ZhuFrame.getZhuJpanel().addPrompt2("没有足够的仙玉!");
        }

    }

    public Eshop getEshop() {
        return eshop;
    }

    public void setEshop(Eshop eshop) {
        this.eshop = eshop;
    }
}
