package jxy2.jutnil;

import net.lingala.zip4j.io.ZipInputStream;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class CheckTimer implements Runnable {
    public static Object lock = new Object();
    public static List<StreamTimer> list = new CopyOnWriteArrayList<>();
    public static boolean running = true; // 添加一个标志位来控制线程的运行状态
    public static boolean off = true; // 添加一个标志位来控制线程的运行状态

    public static void stopTimer() {
        running = false;
        synchronized (lock) {
            lock.notifyAll(); // 唤醒等待中的线程
        }
    }



    @Override
    public void run() {
        while (running) { // 使用标志位来控制线程的运行
            try {
                synchronized (lock) {
                    long currentTime = System.currentTimeMillis();
                    for (StreamTimer timer : list) {
                        if (currentTime - timer.getTime() >= 0) {
                            try {
                                if (timer.getInputStream() instanceof ZipInputStream) {
                                    ((ZipInputStream) timer.getInputStream()).close(true);
                                }
                                if (timer.getFileInputStream() != null) {
                                    timer.getFileInputStream().close();
                                }
                                if (timer.getBufferedImage() != null) {
                                    timer.getBufferedImage().flush();
                                }
                                if (timer.getSprite() != null) {
                                    timer.getSprite().removeHead();
                                }
                                if (timer.getByteArrayOutputStream() != null) {
                                    timer.getByteArrayOutputStream().close();
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            } finally {
                                list.remove(timer);
                            }
                        }
                    }
                }
                if (off){
                    System.gc();
                    System.out.println("清理内存数据");
                }
                Thread.sleep(1000);
//                System.out.println("?123");
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public static void add(StreamTimer streamTimer) {
        list.add(streamTimer);
    }

    public static List<StreamTimer> getList() {
        return list;
    }

    public static void setList(List<StreamTimer> list) {
        CheckTimer.list = list;
    }
}
