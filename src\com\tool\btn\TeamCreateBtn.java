package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.imagemonitor.PlayerMonitor;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TeamCreateJpanel;
import org.come.Jpanel.TeamCreateModelJpanel;
import org.come.entity.TeamRole;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class TeamCreateBtn extends MoBanBtn {

    private TeamCreateJpanel teamCreateJpanel;
    private TeamCreateModelJpanel teamCreateModelJpanel;
    private int caozuo;

    public TeamCreateBtn(String iconpath, int type, int caozuo, TeamCreateJpanel teamCreateJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.teamCreateJpanel = teamCreateJpanel;
    }

    public TeamCreateBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,TeamCreateModelJpanel teamCreateModelJpanel) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.teamCreateModelJpanel = teamCreateModelJpanel;
    }
    public TeamCreateBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,TeamCreateJpanel teamCreateJpanel) {
    	super(iconpath, type, colors);
    	this.caozuo = caozuo;
    	setText(text);
    	setFont(font);
    	setVerticalTextPosition(SwingConstants.CENTER);
    	setHorizontalTextPosition(SwingConstants.CENTER);
    	this.teamCreateJpanel = teamCreateJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 0) {
        	if (ImageMixDeal.userimg.getRoleShow().getCaptian()!=1) {
        		ZhuFrame.getZhuJpanel().addPrompt2("只有队长才能发布队伍信息");
        		return;
			}
        	FormsManagement.HiddenDisplay(19);
        } else if (caozuo == 1) {
//            teamCreateJpanel.getRollTwo().setVisible(!teamCreateJpanel.getRollTwo().isVisible());
        } else if (caozuo == 2) {
        	TeamRole teamRole=teamCreateModelJpanel.getTeamBean().getTeams().get(0);
			if (getText().equals("加入")) {
				PlayerMonitor.teamApply(teamRole.getRoleId());
			}else if (getText().equals("交谈")) {
				if (ImageMixDeal.userimg.getRoleShow().getRole_id().compareTo(teamRole.getRoleId())==0) {
					ZhuFrame.getZhuJpanel().addPrompt2("不能和自己说话");
	        		return;
				}
//				FriendChatMessageJframe.getFriendChatMessageJframe().getJpanel().showFriend(teamRole, MessagrFlagUntil.getRichLabel(teamRole.getName()));
//	            FormsManagement.showForm(56);
			}
		}
    }

}
