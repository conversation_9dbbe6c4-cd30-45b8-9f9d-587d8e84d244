package come.tool.handle;


/**
 * 
 * <AUTHOR>
 *
 */
public enum  HandleType {
	/**0  none*/
	Null(null),
	/**1 玩家操  none*/
	Player(new HandlePlayer()),
	/**2 召唤兽操作  none*/
	pet(new HandlePet()),
	/**3 等待指令接收  none*/
	Wait(new HandleWait()),
	/**4 等待战斗处理  none*/
	Battle(new HandleBattle()),
	/**5 战斗播放  none*/
	Play(new HandlePlay()),
	/**6 战斗播放结束同步  none*/
	End(new HandleEnd()),
	;
    private Handle handle;
	
	private HandleType (Handle handle){
		this.handle = handle;
	}
	public Handle getTarget() {
		return handle;
	}
	public static Handle getHandleById(int handletId){
		HandleType[] values = HandleType.values();
		HandleType handleType = values[handletId];
		return handleType.getTarget();
	}
}
