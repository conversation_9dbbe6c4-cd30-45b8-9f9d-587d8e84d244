package jxy2.refine;

import com.tool.btn.MoBanBtn;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class GodBtn extends MoBanBtn {
    public int typeBtn;//提示
    public GodJpanel godJpanel;
    public GodBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, GodJpanel godJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.godJpanel = godJpanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        BtnMoni(typeBtn);
        godJpanel.setMinType(typeBtn);
        godJpanel.clear();
        RefineFrame.getRefineFrame().getRefineJPanel().getRichLabel().setTextSize(Juitil.getPrompt(7,typeBtn),160);
    }

    private void BtnMoni(int typeBtn){
        godJpanel.getBtnGod()[typeBtn].btnchange(2);
        for (int i = 0; i < godJpanel.getBtnGod().length; i++) {
            if (i!=typeBtn){
                godJpanel.getBtnGod()[i].btnchange(0);
            }
        }
    }
}
