package jxy2.eqcon;

import com.tool.btn.MoBanBtn;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.GGJpanel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class GGBtn extends MoBanBtn {
    public GGJpanel ggJpanel;
    public int typeBtn;
    public GGBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, GGJpanel ggJpanel, String prompt) {
        super(iconpath, type, 0, colors, prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.ggJpanel = ggJpanel;

    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn==1){
            if (ggJpanel.yOffset!=0) {
                ggJpanel.yOffset += 50;
            }
        }else {
            if (ggJpanel.yOffset==0){ggJpanel.yOffset-=50;}
            String[] hg = String.valueOf(ggJpanel.yOffset).split("-");
            int sum = Integer.parseInt(hg[1]);
            if (sum < (ggJpanel.richLabel.getHeight() / 2)){
                ggJpanel.yOffset -= 50;
            }else {
                ZhuFrame.getZhuJpanel().addPrompt2("#R已经到底了……");
            }
        }
    }
}
