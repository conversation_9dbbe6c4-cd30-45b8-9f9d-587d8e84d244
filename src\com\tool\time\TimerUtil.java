package com.tool.time;

import org.come.until.Util;

/**
 * 定时器
 * 
 * <AUTHOR>
 */
public class TimerUtil {
	// 判断时间是否到了 true 为时间到了
	public static boolean Timeto(long timing) {
		if (Util.getTime() >= timing) {
			return true;
		}
		return false;
	}
	// 重置定时器时间
	public static long TimeReset(long time) {
		return Util.getTime() + time;
	}
	/**判断剩余多少分钟*/
	public static int fenzhong(long time) {
		return (int) ((time - Util.getTime()) / 60000);
	}
	
	/**判断剩余多少天*/
	public static int residueDay(long time){
		return (int)((time-Util.getTime())/60000/60/24);
	}
	
}
