package jxy2.synt;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.npc.SummonPet;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class SynthesisBtn extends MoBanBtn {
    public SynthesisModel synthesisModel;
    public SynthesisJPanl synthesisJPanl;
    public int typeBtn;
    public boolean is =true;
    public String text,string;
    public SynthesisBtn(String iconpath, int type, String text, Color[] colors, SynthesisModel synthesisModel, int index,int typeBtn) {
        super(iconpath, type,0,colors,text);
        setFont(UIUtils.MSYH_HY14);
        this.synthesisModel = synthesisModel;
        this.index = index;
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        // TODO Auto-generated constructor stub
    }

    public SynthesisBtn(String iconpath, int type, Color[] colors, Font font, String text, SynthesisJPanl synthesisJPanl, int index,int typeBtn) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.synthesisJPanl = synthesisJPanl;
        this.index = index;
        this.typeBtn = typeBtn;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (synthesisModel!=null&&synthesisModel.getMessage()!=null&&typeBtn==-1){
            if (type==-1){type=0;}
            if (this.type == 2) {
                Juitil.CenterTextdrawing(g,synthesisModel.getMessage(), 135 ,25,UIUtils.COLOR_HURTR1,UIUtils.FZCY_HY15);
            } else {
                Juitil.CenterTextdrawing(g,synthesisModel.getMessage(), 134 ,24,UIUtils.COLOR_White,UIUtils.FZCY_HY15);
            }
        }else if (synthesisModel!=null&&synthesisModel.getMsg()!=null&&typeBtn!=-1){
            if (type==-1){type=0;}
            if (this.type == 2) {
                Juitil.CenterTextdrawing(g,synthesisModel.getMsg()[typeBtn], 120 ,25,this.colors[type],UIUtils.FZCY_HY14);
            } else{
                Juitil.CenterTextdrawing(g,synthesisModel.getMsg()[typeBtn], 119 ,24,this.colors[type],UIUtils.FZCY_HY14);
            }
        }
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
        for (int i = 0; i < 8; i++) {
            synthesisJPanl.getLaItmeImg()[i].setIcon(null);
            synthesisJPanl.getLaItmeName()[i].setText("");
            synthesisJPanl.getLaItmeNum()[i].setText("");
            synthesisJPanl.getGoodstable()[i] = null;
        }
        synthesisJPanl.getLabcbtimg().setIcon(null);
        synthesisJPanl.setNewPart(null);
        if (index!=-1){
            if (index==99){
                //TODO 合成按钮触发服务器协议；
                String msg = Agreement.getAgreement().ResetSkillAgreement("S&"+GsonUtil.getGsonUtil().getgson().toJson(synthesisJPanl.getGoodstables()[0])+"&"+GsonUtil.getGsonUtil().getgson().toJson(synthesisJPanl.getGoodstables()[1]));
                SendMessageUntil.toServer(msg);
            }else if (index==98){//兑换物品
                new SummonPet().menuControl(synthesisJPanl.getLabname().getText());
                PetAddPointMouslisten.showPetValue();
            }else {
                synthesisJPanl.PetResistanceRefresh(index);
                synthesisJPanl.Initialize = index;
            }
        }
        if (typeBtn!=-1){
            //TODO 缺少选中醒目效果
            //TODO 在SynthesisJPanl界面设置组件
            if (synthesisModel.getExchange().geteId()==1){
                String[] vs = synthesisModel.getExchange().getStype().split("\\|");
                synthesisJPanl.getLabname().setText(vs[typeBtn]);
                for (int i = 0; i < 2; i++) {
                    synthesisJPanl.getLabGoodsName()[i].setText(vs[typeBtn]);
                }
                //取物品ID
                String[] id = synthesisModel.getExchange().getGoodsid().split("\\|");
                Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(id[typeBtn]));
                synthesisJPanl.getLabgoods().setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),50,50));
                handleButtonClick(typeBtn);
            }else if (synthesisModel.getExchange().geteId()==3){
                GetSynthesisModel();
                //找获得物品的ID
                String[] petid = synthesisModel.getExchange().getGoodsid().split("\\|");
                //根据ID来获取表格内召唤兽信息
               RoleSummoning pet = UserMessUntil.getPet(new BigDecimal(petid[typeBtn]));
                synthesisJPanl.setNewPart(pet.getPart());
                handleButtonClick(typeBtn);
            }else if (synthesisModel.getExchange().geteId()==4){
                GetSynthesisModel();
                //找获得物品的ID
                String[] petid = synthesisModel.getExchange().getGoodsid().split("\\|");
                Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(petid[typeBtn]));
                synthesisJPanl.getLabcbtimg().setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),50,50));
            }

        }

        synthesisJPanl.A_isVisible();
    }

    public void GetSynthesisModel() {
        SynthesisJPanl synthesisJPanl = SynthesisFrame.getSynthesisFrame().getSynthesisJPanl();
        String[] vs = synthesisModel.getExchange().getStype().split("\\|");
        synthesisJPanl.getLabname().setText(vs[typeBtn]);
        //取消耗物品ID
        String[] xh = synthesisModel.getExchange().getConsume().split("&");
        String[] id = xh[typeBtn].split("\\|");
        for (int i = 0; i < id.length; i++) {
            String xhid = id[i].split("=")[1];
            int  xhnum  = Integer.parseInt(id[i].split("=")[2]);
            Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(xhid));
            if (goodstable!=null){
                synthesisJPanl.getLaItmeImg()[i].setIcon(GoodsListFromServerUntil.imgpathWdfile(goodstable.getSkin(),50,50));
                if (synthesisModel.getExchange().geteId()==3) {
                    synthesisJPanl.getLaItmeName()[i].setText("天书" + goodstable.getGoodsname().substring(4));
                }
                synthesisJPanl.getLaItmeNum()[i].setText(GoodsListFromServerUntil.getGoodNum(new BigDecimal(xhid))+"/"+xhnum);
                synthesisJPanl.getGoodstable()[i] = goodstable;
            }
        }

        handleButtonClick(typeBtn);
    }


    private void handleButtonClick(int clickedIndex) {
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        synthesisModel.getRest()[clickedIndex].btnchange(2);
        for (int i = 0; i <synthesisModel.getRest().length; i++) {
            if (i != clickedIndex) {
                synthesisModel.getRest()[i].btnchange(0);
            }
        }
    }
}
