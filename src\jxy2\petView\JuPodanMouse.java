package jxy2.petView;

import jxy2.UniversalModel;
import jxy2.supet.JupoDanJPanel;
import jxy2.supet.JupoDanMouse;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class Ju<PERSON>odanMouse extends TemplateMouseListener {
    public JList<UniversalModel> petList;
    public JuPodanMouse(JList<UniversalModel> petList) {
        this.petList = petList;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (petList.getSelectedIndex()!=-1) {
            RoleSummoning roleSummoning = UserMessUntil.getPetListTable().get(petList.getSelectedIndex());
            if (FormsManagement.getframe(145).isVisible()) {
                UserMessUntil.setChosePetMes(roleSummoning);
                JupoDanMouse.refreshPetSkills(roleSummoning);
            }
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        JupoDanJPanel.idxs = -1;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
            if (e.getY() / 20 < petList.getModel().getSize()){
                JupoDanJPanel.idxs = petList.locationToIndex(e.getPoint());
            }
    }
}
