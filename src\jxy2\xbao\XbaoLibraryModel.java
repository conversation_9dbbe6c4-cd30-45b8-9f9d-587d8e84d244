package jxy2.xbao;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;

public class XbaoLibraryModel extends JPanel {
    private Xuanbao xuanbao;
    private JLabel xuanbaoImg,xuanbaoicon,xuanbaoName,xuanbaotx;
    public XbaoLibraryModel() {
        this.setPreferredSize(new Dimension(92,108));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        getXuanbaoicon();
        getXuanbaoName();
        //底层背景
        getXuanbaotx();
        getXuanbaoImg();
    }

    public void LoadingModel(Xuanbao xuanbao,boolean Have) {
        ImageIcon icon,icons;
        if (Have){
            icon = GoodsListFromServerUntil.xbaoWdfile(UserMessUntil.getXuanabaoSikn(xuanbao.getBaoname()),51,51);
            icons = Juitil.tz374;
        }else {
            icon = new ImageIcon(Juitil.convertToGray(GoodsListFromServerUntil.xbaoWdfile(UserMessUntil.getXuanabaoSikn(xuanbao.getBaoname()),51,51).getImage()));
            icons = new ImageIcon(Juitil.convertToGray(Juitil.tz374.getImage()));

        }
        xuanbaoImg.setIcon(icons);
        xuanbaoicon.setIcon(icon);
        xuanbaoicon.setBounds(20, 20, 52, 52);
        xuanbaoName.setText(xuanbao.getBaoname());
        xuanbaoName.setBounds(13, 76, 60, 14);

    }

    public Xuanbao getXuanbao() {
        return xuanbao;
    }

    public void setXuanbao(Xuanbao xuanbao) {
        this.xuanbao = xuanbao;
    }

    public void getXuanbaoImg() {
        if (xuanbaoImg==null) {
            this.xuanbaoImg = new JLabel();
            xuanbaoImg.setBounds(0, 0, 92, 108);
            xuanbaoImg.setIcon(new ImageIcon(Juitil.convertToGray(Juitil.tz374.getImage())
            ));
            this.add(xuanbaoImg);
        }
    }

    public JLabel getXuanbaoicon() {
        if (xuanbaoicon==null) {
            xuanbaoicon = new JLabel();
            xuanbaoicon.setBounds(0, 0, 52, 52);
            add(xuanbaoicon);
        }
        return xuanbaoicon;
    }

    public JLabel getXuanbaoName() {
        if (xuanbaoName==null) {
            xuanbaoName =new JLabel();
            xuanbaoName.setBounds(0, 0, 92, 108);
            xuanbaoName.setFont(UIUtils.TEXT_FONT);
            xuanbaoName.setForeground(new Color(70,53,33));
            xuanbaoName.setHorizontalAlignment(JLabel.CENTER);
            xuanbaoName.setVerticalAlignment(JLabel.CENTER);
            add(xuanbaoName);
        }
        return xuanbaoName;
    }

    public void setXuanbaoName(JLabel xuanbaoName) {
        this.xuanbaoName = xuanbaoName;
    }

    public JLabel getXuanbaotx() {
        if (xuanbaotx==null) {
            xuanbaotx = new JLabel();
            xuanbaotx.setBounds(0, 0, 92, 108);
            xuanbaotx.setVisible(false);
            xuanbaotx.setIcon(Juitil.tz375);
            add(xuanbaotx);
        }
        return xuanbaotx;
    }

    public void setXuanbaotx(JLabel xuanbaotx) {
        this.xuanbaotx = xuanbaotx;
    }
}
