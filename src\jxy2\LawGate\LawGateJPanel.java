package jxy2.LawGate;

import com.tool.role.RoleData;
import com.tool.role.SkillUtil;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.bean.LoginResult;
import org.come.bean.Skill;
import org.come.until.Arith;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
* 法门
* LawGate
* <AUTHOR>
* @date 2024/12/4 下午10:56
*/
public class LawGateJPanel extends JPanel {
    public String title,hhtext,zhi;//说明
    public RichLabel richLabel;
    public JLabel testMes;// 蒙版
    public JLabel[] skillimg = new JLabel[2];
    public JLabel[] skillname = new JLabel[2];
    public JLabel[] skillArt = new JLabel[2];
    public JLabel[] types = new JLabel[2];
    public JLabel[] skillValue = new JLabel[2];
    public LawGateBtn[] practice = new LawGateBtn[2];
    public LawGateBtn study;
    public List<String> skillids =new ArrayList<>();
    public int K,G;
    public boolean isv = false;
    public LawGateJPanel(int py) {
        this.setBounds(176 * py, 0, 170, 340);
        this.setOpaque(false);
        this.setLayout(null);
        study = new LawGateBtn(ImgConstants.tz34, 1,  py, "选 定", this,"");
        study.setBounds(56, 264, 59, 24);
        add(study);

        testMes  = new JLabel();
        testMes.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz272, 160,295,"defaut.wdf"));
        testMes.setBounds(5,5,160,295);
        add(testMes);

        for (int i = 0; i < skillimg.length; i++) {
            skillimg[i] = new JLabel();
            skillimg[i].setBounds(12, 143 + i * 60, 40, 40);
            add(skillimg[i]);
        }
        for (int i = 0; i < skillname.length; i++) {
            skillname[i] = new JLabel();
            skillname[i].setBounds(62, 148 + i * 55, 143, 15);
            skillname[i].setVisible(false);
            add(skillname[i]);
        }
        for (int i = 0; i < types.length; i++) {
            types[i] = new JLabel();
            types[i].setBounds(62, 170 + i * 55, 159, 15);
            types[i].setVisible(false);
            add(types[i]);
        }
        for (int i = 0; i < skillArt.length; i++) {
            skillArt[i] = new JLabel();
            skillArt[i].setBounds(62, 170 + i * 55, 0, 15);
            skillArt[i].setVisible(false);
            add(skillArt[i]);
        }
        for (int i = 0; i < skillValue.length; i++) {
            skillValue[i] = new JLabel(0 + "/" + 10000);
            skillValue[i].setBounds(62, 170 + i * 55, 159, 15);
            skillValue[i].setVisible(false);
            add(skillValue[i]);
        }
        for (int i = 0; i < practice.length; i++) {
            practice[i] = new LawGateBtn(ImgConstants.tz112, 1, "修炼",4+i,this);
            practice[i].setBounds(126, 188 + i * 86, 34, 18);
            practice[i].setVisible(false);
            add(practice[i]);
        }

        for (int i = 0; i < 2; i++) {
            LawGateMouse lawGateMouse = new LawGateMouse(i,this);
            skillimg[i].addMouseListener(lawGateMouse);
            skillname[i].addMouseListener(lawGateMouse);
            skillArt[i].addMouseListener(lawGateMouse);
            skillValue[i].addMouseListener(lawGateMouse);
            types[i].addMouseListener(lawGateMouse);
        }

    }


    @Override
    protected void paintComponent(Graphics g) {
        // TODO Auto-generated method stub
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz22, 0, 0, 170, 320, 1);
        for (int i = 0; i < 2; i++) {//+26
            Juitil.ImngBack(g, Juitil.tz258, 5, 137 + i * (58+K), 160, 54+K, 1);
            Juitil.TextBackground(g, skillname[i].getText(), 14, 62, (148)+i*(55+K), UIUtils.COLOR_White, UIUtils.MSYH_HY16);
            Juitil.TextBackground(g, types[i].getText(), 14, 62, 166 + i * (55 + K), UIUtils.COLOR_808182, UIUtils.MSYH_HY14);
            skillimg[i].setBounds(12, 143 + i * (60+K), 40, 40);
            if (isv) {
                Juitil.ImngBack(g, Juitil.tz23, 13, 190 + i * (60 + K), 106, 15, 1);
                Juitil.ImngBack(g, Juitil.tz54, 15, 192 + i * 86, skillArt[i].getWidth(), 11, 1);
                Juitil.TextBackground(g, skillValue[i].getText(), 13, 35, 190 + i * 86, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }

        }
        Juitil.ImngBack(g, Juitil.tz271, 5, 40, 160, 7, 1);
        Juitil.TextBackground(g, title, 14, 52, 20, UIUtils.COLOR_FM_Title, UIUtils.HYXKJ_HY30);
        Juitil.getLineWrap(g, UIUtils.COLOR_White, UIUtils.MSYH_HY13, 180, 60,hhtext);

    }


    public void addSkillsName(String str, String[] vs) {
        skillids.clear();
        String[] skills = SkillUtil.getFMSkill(str);
        if (skills==null)return;
        for(int i = 0; i < skills.length; ++i) {
            String[] vss = skills[i].split("\\|");
            //解析技能名称
            skillname[i].setText(vss[0]);
            //解析技能ID
            int skillid = Integer.parseInt(vss[1]);
            types[i].setText(i == 0 ? "主动技能" : "被动技能");
            skillimg[i].setIcon(CutButtonImage.FmSkill(skillid,40,40));
            if (!skillids.contains(skillid+"")){
                skillids.add(skillid+"");
            }
        }
        //解析当前已学习的技能
        if (vs!=null){
            for (String v : vs) {
                String[] vss = v.split("_");
                Skill skill = UserMessUntil.getSkillId(vss[0]);
                if (skill != null) {
                    for (int i = 0; i < skillname.length; i++) {
                        if (skillname[i].getText().equals(skill.getSkillname())) {
                            int parseInt;
                            LoginResult result = RoleData.getRoleData().getLoginResult();
                            if (result.getFmsld()!=null&&!LawGateControl.updateSkillLevels(result).isEmpty()){
                                parseInt=  Integer.parseInt(LawGateControl.updateSkillLevels(result).get(i));
                            }else {
                                parseInt = Integer.parseInt(vss[1]);
                            }
                            LawGateBtn.sendRole(Integer.parseInt(vs[0].substring(2)));
                            skillArt[i].setVisible(true);
                            int witNum = (int) Math.round(Arith.mul(
                                    Arith.div(Double.valueOf(parseInt), Double.valueOf(10000), 1), 102));
                            witNum = witNum > 0 ? witNum : 1;
                            witNum = Math.min(witNum, 102);
                            isv = true;
                            skillArt[i].setSize(witNum, skillArt[i].getHeight());
                            skillValue[i].setText(parseInt>10000?"10000"+ "/" + 10000:parseInt + "/" + 10000);
                            zhi = str.split("\\|")[2];
                            break;
                        }
                    }
                }
            }
        }else {
            for (int i = 0; i < LawGateMainJPanel.lawGateJPanels.length; i++) {
                LawGateMainJPanel.lawGateJPanels[i].testMes.setVisible(true);
                LawGateMainJPanel.lawGateJPanels[i].K = 0;
                LawGateMainJPanel.lawGateJPanels[i].isv = false;
                for (int k = 0; k < 2; k++) {
                    LawGateMainJPanel.lawGateJPanels[i].practice[k].setVisible(false);
                }
                LawGateMainJPanel.lawGateJPanels[i].study.setVisible(true);
            }
        }
    }
}
