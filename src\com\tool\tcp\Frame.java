package com.tool.tcp;

import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;

public class Frame{
	private static final long serialVersionUID = 2191491798990378838L;
	private Image image;
	private int pos;//索引 加载完后 重置为 旋转标识
	private int refPixelX;//
	private int refPixelY;
	public int width = -1;
	public int height = -1;


	public Frame(int pos) {
		super();
		this.pos = pos;
	}
	public Frame(BufferedImage image, int centerX, int centerY) {
		this.image = image;
		this.refPixelX = centerX;
		this.refPixelY = centerY;
	}
	public Frame(Image image, int centerX, int centerY) {
		this.image = image;
		this.refPixelX = centerX;
		this.refPixelY = centerY;
	}
	public Image getImage() {
		return image;
	}
	public void dispose() {
		this.image = null;
	}
	protected void doDraw(Graphics2D g2, int x, int y, int width, int height) {
		int x1 = x - this.refPixelX;
		int y1 = y - this.refPixelY;
		g2.drawImage(this.image, x1, y1, x1 + width, y1 + height, 0, 0, width,height, null);
	}
	public int getRefPixelX() {
		return refPixelX;
	}
	public void setRefPixelX(int centerX) {
		this.refPixelX = centerX;
	}
	public int getRefPixelY() {
		return refPixelY;
	}
	public void setRefPixelY(int centerY) {
		this.refPixelY = centerY;
	}
	public void setImage(BufferedImage image) {
		this.pos=0;
		this.image = image;
	}
	public void setImage(Image image,int x,int y) {
		this.pos=1;
		this.refPixelX=x;
		this.refPixelY=y;
		this.image = image;
	}
	public boolean contains(int x, int y) {
		return false;
	}
	public int getWidth() {return width==-1?image.getWidth(null):width;}
	public int getHeight() {return height==-1?image.getHeight(null):height;}
	public int getPos() { return pos; }
	public void setPos(int pos) { this.pos=pos; }

	public void setWidth(int width) {
		this.width = width;
	}

	public void setHeight(int height) {
		this.height = height;
	}
}
