package com.tool.btn;

import com.tool.Document.RichDocument;
import com.tool.PanelDisplay.PetPanelShow;
import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import com.tool.imagemonitor.FightingMonitor;
import com.tool.role.RoleData;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import jxy2.chatv.ChatFrame;
import jxy2.flight.FlyFrame;
import jxy2.flight.FlyJPanel;
import jxy2.wormap.WorldMapBtn;
import org.come.Frame.TestChildJframe;
import org.come.Frame.TestsmallmapJframe;
import org.come.Frame.TesttaskJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.FightOperation;
import org.come.bean.NChatBean;
import org.come.entity.Baby;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.login.GameView;
import org.come.mouslisten.ChangeMouseSymbolMouslisten;
import org.come.mouslisten.GoodsMouslisten;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.test.Main;
import org.come.until.*;

import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.Document;
import java.awt.*;
import java.awt.event.AWTEventListener;
import java.awt.event.KeyEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MyAWTEventListener implements AWTEventListener {

	private static String mesageType = "当前";// 消息类型
	public boolean open = false;
	@Override
	public void eventDispatched(AWTEvent event) {
		try {
			if (event.getClass() == KeyEvent.class) {
				// 被处理的事件是键盘事件.
				KeyEvent keyEvent = (KeyEvent) event;
				if (keyEvent.getID() == KeyEvent.KEY_PRESSED) {
					keyReleased(keyEvent);
				}
			}
		} catch (Exception e) {
			// Native Image 环境中可能出现的异常，静默处理
			System.err.println("键盘事件处理异常: " + e.getMessage());
			// 不抛出异常，避免影响游戏运行
		}
	}
	public static int msgIndex;
	public static List<String> msgList;
	static {
		msgIndex = 0;
		msgList = new ArrayList<>();
	}
	private void keyReleased(KeyEvent event) {
		try {
			if (event.getKeyCode() == KeyEvent.VK_ENTER) {
				Mesage();
			}else if (event.getKeyCode() == KeyEvent.VK_UP){
			if (msgIndex > 0) {
				msgIndex--;
				//解析拼凑物品信息
			 if (msgList.get(msgIndex).contains("#V")){
					if (TypetID()==2){
						GOODXINXI();
					}else {
						PETXINXI();
					}
				}else {
					ZhuFrame.getZhuJpanel().getSendMes().setText(msgList.get(msgIndex));
				}
			}
		}else if (event.getKeyCode() == KeyEvent.VK_DOWN){
			if (msgIndex < msgList.size() - 1) {
				msgIndex ++;
				 if (msgList.get(msgIndex).contains("#V")){
					if (TypetID()==2){
						GOODXINXI();
					}else {
						PETXINXI();
					}
				}else {
					ZhuFrame.getZhuJpanel().getSendMes().setText(msgList.get(msgIndex));
				}

			}
		}else if (event.getKeyCode() == KeyEvent.VK_ESCAPE) {
			try {
				for (int i = 0; i < 999; i++) {
					FormsManagement.HideForm(i);
				}
			} catch (NoClassDefFoundError e) {
				// 处理类未找到的情况
				System.exit(0);
				e.printStackTrace();
			}

		} else if (event.isAltDown()) {
			try {
				if (FightingMixDeal.State != HandleState.USUAL) {
					// 普通攻击 alt+a
					if (event.getKeyCode() == KeyEvent.VK_A) {
						FightOperation operation = FightingMonitor.getOperation();
						operation.Record(-1, -1, 0, null);
						FightingMonitor.execution(operation);

					} else if (event.getKeyCode() == KeyEvent.VK_W) {// 弹出法术列表 alt+s
						if (FightingMixDeal.State == HandleState.HANDLE_PLAYER|| FightingMixDeal.State == HandleState.HANDLE_PET) {
							FightingBtn.Btnfashu();
						}
					} else if (event.getKeyCode() == KeyEvent.VK_D) {// 防御alt+d
						if (FightingMixDeal.State == HandleState.HANDLE_PLAYER|| FightingMixDeal.State == HandleState.HANDLE_PET) {
							FightingBtn.Btnfangyu();
						}
					} else if (event.getKeyCode() == KeyEvent.VK_E) {// 物品栏alt+e
						if (!FormsManagement.getframe(2).isVisible()) {
							FormsManagement.showForm(2);

						} else {
							FormsManagement.HideForm(2);

						}
					} else if (event.getKeyCode() == KeyEvent.VK_T) {// ALT+T 保护
						FightingBtn.Btnbaohu();
					} else if (event.getKeyCode() == KeyEvent.VK_B) {// ALT+B 捕捉
						FightingBtn.Btnbuzhua();
					} else if (event.getKeyCode() == KeyEvent.VK_S) {// ALT+s 自动法术
						if (FightingMixDeal.State == HandleState.HANDLE_PLAYER|| FightingMixDeal.State == HandleState.HANDLE_PET) {
							FightOperation operation = FightingMonitor.getOperation();
							if (operation.getSpell() == null) {
								ZhuFrame.getZhuJpanel().addPrompt2("没有默认的法术");
								return;
							}
							FightingMonitor.mousesname = operation.getSpell();
							FightingMonitor.mousestate = 1;
							MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE8);
							ZhuFrame.getZhuJpanel().HideBeastBtn();
						}
					}
				} else {
					// alt+1键打开小地图  alt+2键打开大地图
					if (event.getKeyCode() == KeyEvent.VK_1) {
						int MapID = Math.toIntExact(RoleData.getRoleData().getLoginResult().getMapid());
						if (!TestsmallmapJframe.getTestsmallmapJframe(0).isVisible()) {
							if (Util.mapmodel.getMin_x() > 0) {
								WorldMapBtn.NewWorldMapImgShow(MapID,0,false);
								TestsmallmapJframe.getTestsmallmapJframe(0).setLocation(70, 80);
								TestsmallmapJframe.getTestsmallmapJframe(0).getTestsmallmapJpanel().OpenMapFXQ();
								Music.addyinxiao("开关窗口.mp3");

							}
						} else {
							FormsManagement.HideForm(22);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_2) {
						if (!FormsManagement.getframe(129).isVisible()) {
							// 设置存款
							FormsManagement.showForm(129);
							Music.addyinxiao("开关窗口.mp3");
						} else {
							FormsManagement.HideForm(129);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_Q) {// alt+q 打开或者关闭任务列表
						if (!FormsManagement.getframe(3).isVisible()) {
							FormsManagement.showForm(3);
							Music.addyinxiao("开关窗口.mp3");
							TesttaskJframe.getTesttaskJframe().getJtask().showTaskMethod();
						} else {
							FormsManagement.HideForm(3);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_W) {// alt+w 人物属性面板
						Util.StopFrame(0);
						PetAddPointMouslisten.getplayerValue();// 设置面板里面的基本参数
					} else if (event.getKeyCode() == KeyEvent.VK_O) {// 召唤兽属性面板
                        if (UserMessUntil.getChosePetMes() != null) {// 如果已经携带了召唤兽,那么打开面板的时候默认就打开所有的选中的信息
							PetPanelShow.ShowMesForJpanel();
						}
						Util.StopFrame(144);
					} else if (event.getKeyCode() == KeyEvent.VK_I|| event.getKeyCode() == KeyEvent.VK_E) {// 道具栏
						if (!FormsManagement.getframe(2).isVisible()) {
							// 设置存款
							ZhuJpanel.setUseGoodsType(0);
							FormsManagement.showForm(2);
							Music.addyinxiao("开关窗口.mp3");


						} else {
							// 将使用物品的状态改为人物点击状态
							ZhuJpanel.setUseGoodsType(0);
							FormsManagement.HideForm(2);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_A) {// 组队alt+t
						MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE6);
						ZhuFrame.getZhuJpanel().setVisible(false);
						ChatFrame.getChatJPanel().setVisible(false);
					} else if (event.getKeyCode() == KeyEvent.VK_T) {// 组队alt+t
						ChangeMouseSymbolMouslisten.dianji("组队快捷键");
					} else if (event.getKeyCode() == KeyEvent.VK_G) {// 给予alt+g
						MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE3);
						ZhuFrame.getZhuJpanel().setVisible(false);
						ChatFrame.getChatJPanel().setVisible(false);
					} else if (event.getKeyCode() == KeyEvent.VK_X) {// 交易alt+x
						MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE5);
						ZhuFrame.getZhuJpanel().setVisible(false);
						ChatFrame.getChatJPanel().setVisible(false);
					} else if (event.getKeyCode() == KeyEvent.VK_R) {// 坐骑alt+r
						if (!FormsManagement.getframe(7).isVisible()) {
							// 向服务器发送消息请求坐骑列表
							FormsManagement.showForm(7);
							String sendmes = Agreement.getAgreement().MountAgreement();
							SendMessageUntil.toServer(sendmes);
							Music.addyinxiao("开关窗口.mp3");
						} else {
							FormsManagement.HideForm(7);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_F) {// 好友ctrl+f
						MessagrFlagUntil.ReceiveFriend();
					} else if (event.getKeyCode() == KeyEvent.VK_U) {// 好友ctrl+f
						Util.StopFrame(148);
						FlyFrame.getFlightFrame().getFlightJPanel().InitializeAircraftData();
					}else if (event.getKeyCode() == KeyEvent.VK_C) {// 好友ctrl+f
						// 检查当前飞行状态并切换
						if (Util.isMapIdValid(ImageMixDeal.userimg.getRoleShow().getMapid())) {
							ZhuFrame.getZhuJpanel().addPrompt2("当前地图无法飞行");
						}
						boolean hasEquippedFly = FlyJPanel.getFlyList().stream()
								.anyMatch(fly -> fly.getFlyEquip() == 1);

						if (!hasEquippedFly) {
							ZhuFrame.getZhuJpanel().addPrompt("你还没有装备飞行器！");
							return;
						}

						boolean isFlying = ImageMixDeal.userimg.getRoleShow().getFlyID() > 0;
						// 发送飞行状态变更消息到服务器，格式：A + roleId|flyState
						// 不再立即设置飞行状态，等待服务器响应
						String msg = Agreement.getAgreement().FlyAgreement(
								"A" + ImageMixDeal.userimg.getRoleShow().getRole_id() + "|" + (isFlying ? "0" : "1")
						);
						SendMessageUntil.toServer(msg);
					} else if (event.getKeyCode() == KeyEvent.VK_Y) {// 宝宝alt+y
						if (!FormsManagement.getframe(1).isVisible()) {
							Baby baby = UserMessUntil.getbaby(TestChildJframe.getTestChildJframe().getTestChildJpanel().getBabyid());
							TestChildJframe.getTestChildJframe().getTestChildJpanel().ShowBaby(baby);
							FormsManagement.showForm(1);
							Music.addyinxiao("开关窗口.mp3");
						} else {
							FormsManagement.HideForm(1);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_V) {// ALT+v// 法宝界面
						if (!FormsManagement.getframe(43).isVisible()) {
							// 设置存款
							FormsManagement.showForm(43);
							Music.addyinxiao("开关窗口.mp3");
						} else {
							FormsManagement.HideForm(43);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_B) {// ALT+b	// 帮派界面
						if (FormsManagement.getframe(48).isVisible()) {
							FormsManagement.HideForm(48);
							return;
						}
						if (ImageMixDeal.userimg.getRoleShow().getGang_id() == null|| ImageMixDeal.userimg.getRoleShow().getGang_id().intValue() == 0) {
							ZhuFrame.getZhuJpanel().addPrompt2("你没有帮派!");
							return;
						}
						// 向服务器发送信息
						String senmes = Agreement.getAgreement().IntogangAgreement(ImageMixDeal.userimg.getRoleShow().getGang_id().toString());
						SendMessageUntil.toServer(senmes);
					} else if (event.getKeyCode() == KeyEvent.VK_S) {// ALT+s 法宝界面
						if (!FormsManagement.getframe(50).isVisible()) {
							// 设置存款
							Music.addyinxiao("开关窗口.mp3");
							FormsManagement.showForm(50);
						} else {
							FormsManagement.HideForm(50);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_0) {// ALT+s 小表情界面
						if (!FormsManagement.getframe(52).isVisible()) {
							// 设置存款
							Music.addyinxiao("开关窗口.mp3");
							FormsManagement.showForm(52);
						} else {
							FormsManagement.HideForm(52);
							Music.addyinxiao("关闭窗口.mp3");
						}
					} else if (event.getKeyCode() == KeyEvent.VK_4) {
						ManimgAttribute.ISNAME = !ManimgAttribute.ISNAME;
					} else if (event.getKeyCode() == KeyEvent.VK_5) {
						ManimgAttribute.ISTCP = !ManimgAttribute.ISTCP;
					} else if (event.getKeyCode() == KeyEvent.VK_6) {
						ImageMixDeal.ISSTALL = !ImageMixDeal.ISSTALL;
					}
				}
			} catch (Exception e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}
	} catch (Exception e) {
		// Native Image 环境中键盘事件处理异常
		System.err.println("键盘事件处理异常: " + e.getMessage());
		// 不抛出异常，避免影响游戏运行
	}
}

	private void PETXINXI() {
		try {
			JTextField sendMes = ZhuFrame.getZhuJpanel().getSendMes();
			String currentText = msgList.get(msgIndex);
			// 清空文本框
			sendMes.setText("");
			// 获取所有召唤兽ID
			List<BigDecimal> goodIDs = getGoodIDs(currentText);
			// 使用正则表达式分割文本
			String[] parts = currentText.split("#V\\{.*?\\}#L", -1);
			// 处理每个部分
			for (int i = 0; i < parts.length; i++) {
				// 添加普通文本
				if (!parts[i].isEmpty()) {
					try {
						Document doc = sendMes.getDocument();
						doc.insertString(doc.getLength(), parts[i], null);
					} catch (BadLocationException e) {
						e.printStackTrace();
					}
				}

				// 添加召唤兽链接
				if (i < goodIDs.size()) {
					RoleSummoning roleSummoning = UserMessUntil.getPetRgid(goodIDs.get(i));
					if (roleSummoning != null) {
						((RichDocument) sendMes.getDocument()).insertRich(
								sendMes.getCaretPosition(),
								3,
								roleSummoning.getSid(),
								"[" + roleSummoning.getSummoningname() + "]",
								"G",
								null
						);
					}
				}
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private static void GOODXINXI() {
		try {
			JTextField sendMes = ZhuFrame.getZhuJpanel().getSendMes();
			String currentText = msgList.get(msgIndex);
			// 清空文本框
			sendMes.setText("");
			// 获取所有召唤兽ID
			List<BigDecimal> goodIDs = getGoodIDs(currentText);
			// 使用正则表达式分割文本
			String[] parts = currentText.split("#V\\{.*?\\}#L", -1);
			// 处理每个部分
			for (int i = 0; i < parts.length; i++) {
				// 添加普通文本
				if (!parts[i].isEmpty()) {
					try {
						Document doc = sendMes.getDocument();
						doc.insertString(doc.getLength(), parts[i], null);
					} catch (BadLocationException e) {
						e.printStackTrace();
					}
				}

				// 添加召唤兽链接
				if (i < goodIDs.size()) {
					Goodstable goodstable = GoodsListFromServerUntil.getRgid(goodIDs.get(i));
					if (goodstable != null) {
						String s = goodstable.getRefinelv() == null ? "[" + goodstable.getGoodsname() + "]" : "[" + goodstable.getGoodsname() + "(+" + goodstable.getRefinelv() + ")" + "]";
						JTextField SendMes = ZhuFrame.getZhuJpanel().getSendMes();
						((RichDocument) SendMes.getDocument()).insertRich(SendMes.getCaretPosition(), 2,
								goodstable.getRgid(), s, "G", null);
					}
				}
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	private static List<BigDecimal> getGoodIDs(String text) {
		List<BigDecimal> ids = new ArrayList<>();
		try {
			// 使用正则表达式匹配所有的id值
			Pattern pattern = Pattern.compile("\"id\":(\\d+)");
			Matcher matcher = pattern.matcher(text);
			while (matcher.find()) {
				try {
					int id = Integer.parseInt(matcher.group(1));
					ids.add(new BigDecimal(id));
				} catch (NumberFormatException e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ids;
	}
	private static int TypetID() {
		try {
			String jsonStr = msgList.get(msgIndex).substring(2); // 去掉开头的#V
			// 使用正则表达式提取id值
			Pattern pattern = Pattern.compile("\"type\":(\\d+)");
			Matcher matcher = pattern.matcher(jsonStr);
			if (matcher.find()) {
                return Integer.parseInt(matcher.group(1));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public static String getMesageType() {
		return mesageType;
	}

	public static void setMesageType(String mesageType) {
		MyAWTEventListener.mesageType = mesageType;
	}

	/** 聊天框处理 */
	private static long time;

	public static void Mesage() {
		int viewId = Main.frame.getLoginJpanel().getViewId();
		if (viewId==0){
			Main.frame.getLoginJpanel().getLoginView().login();
			return;
		}
		if (GameView.getFrameDialogSync()==null) {
			return;
		}
			JTextField field = ZhuFrame.getZhuJpanel().getSendMes();
			if (field.isFocusOwner()) {
				String sendmes = ((RichDocument) field.getDocument()).sendText();// 发送的消息
				if (sendmes.equals("")) {
					ZhuFrame.getZhuJpanel().addPrompt("请输入内容");
					return;
				}
				int max = RoleData.getRoleData().getLoginResult().getTurnAround() * 12;
				if (max < 999) {
					max = 999;
				}
				if (field.getText().length() > max) {
					ZhuFrame.getZhuJpanel().addPrompt("最大字符限制" + max + "个");
					return;
				}
				if (mgc(sendmes)) {
					ZhuFrame.getZhuJpanel().addPrompt("发送内容带有敏感词");
					return;
				}
				if (!msgList.contains(sendmes)) {
					msgList.add(sendmes);
				} else {
					msgList.remove(sendmes);
					msgList.add(sendmes);
				}
				msgIndex = msgList.size();
				if (msgList.size() > 15) {
					msgList.remove(0);
				}
				// 将聊天输入框的内容清空
				field.setText("");
				Mesage(sendmes, mesageType);
			} else {
				field.requestFocus();
			}
		}

	public static void Mesage(String sendmes, String type) {
		// 0当前 1队伍 2帮派 3世界 4传音
		NChatBean bean = new NChatBean();
		bean.setMessage(sendmes);
		if (type.equals("世界")) {
			bean.setId(3);
			long time2 = System.currentTimeMillis();
			if (time2 - time < 1000) {
				ZhuFrame.getZhuJpanel().addPrompt("世界喊话间隔30秒");
				return;
			}
			time = time2;
		} else if (type.equals("当前")) {
			bean.setId(0);
			long time2 = System.currentTimeMillis();
			if (time2 - time < 1000) {
				ZhuFrame.getZhuJpanel().addPrompt("当前喊话间隔2秒");
				return;
			}
			time = time2;
		} else if (type.equals("帮派")) {
			if (ImageMixDeal.userimg.getRoleShow().getGang_id().compareTo(new BigDecimal(0)) != 1) {
				ZhuFrame.getZhuJpanel().addPrompt2("您没有帮派，无法发送帮派信息！！");
				return;
			}
			long time2 = System.currentTimeMillis();
			if (time2 - time < 1000) {
				ZhuFrame.getZhuJpanel().addPrompt("当前喊话间隔2秒");
				return;
			}
			time = time2;
			bean.setId(2);
		} else if (type.equals("队伍")) {
			if (mesageType.equals("队伍")) {
				if (ImageMixDeal.userimg.getRoleShow().getTroop_id() == null) {
					ZhuFrame.getZhuJpanel().addPrompt2("您还没有加入队伍，无法发送队伍信息！！");
					return;
				}
			}
			bean.setId(1);
		} else if (type.equals("传音")) {
			// 判断是否有小喇叭
			int a = 0;
			for (int i = 0; i < GoodsListFromServerUntil.getGoodslist().length; i++) {
				Goodstable good = GoodsListFromServerUntil.getGoodslist()[i];
				if (good == null)
					continue;
				if (good.getType() == 2324) {
					// 消耗喇叭，发送物品使用情况
					good.setUsetime(good.getUsetime() - 1);
					GoodsMouslisten.gooduse(good, 1);
					if (good.getUsetime() <= 0)
						GoodsListFromServerUntil.Deleted(i);
					a = 1;
					break;
				}
			}
			if (a == 0) {
				ZhuFrame.getZhuJpanel().addPrompt2("您没有足够小喇叭了！！！");
				return;
			}
			bean.setId(4);
		}
		String value = Agreement.getAgreement().chatAgreement(
				GsonUtil.getGsonUtil().getgson().toJson(bean));
		// 向服务器发送信息
		SendMessageUntil.toServer(value);

	}

	static String[] v = { "#T", "#X", "#J", "#Q", "#D", "#P", "Q号", "q号", "QQ","qq", "Qq", "公益服", "免费送", "扣扣群" , "JB" , "J" };

	// 敏感词判断
	public static boolean mgc(String text) {
		for (int i = 0; i < v.length; i++) {
			if (text.indexOf(v[i]) != -1) {
				return true;
			}
		}
		return false;
	}
}