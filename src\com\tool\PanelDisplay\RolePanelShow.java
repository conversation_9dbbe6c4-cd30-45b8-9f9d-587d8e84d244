package com.tool.PanelDisplay;

import com.tool.image.ImageMixDeal;
import com.tool.role.GetExp;
import org.come.Frame.MsgJframe;
import org.come.Frame.Teststatejframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TeststateJpanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

/**人物状态窗口*/
public class RolePanelShow extends TemplateMouseListener {
	public static void Show() {
		PetAddPointMouslisten.getplayerValue();
		changeGrade(ImageMixDeal.userimg.getRoleShow().getGrade());
		if (!FormsManagement.getframe(0).isVisible()) {
			FormsManagement.showForm(0);
			Music.addyinxiao("开关窗口.mp3");// 打开面板
		} else {
			FormsManagement.HideForm(0);
			Music.addyinxiao("关闭窗口.mp3");
		}
	}
	/** 召唤兽等级的展示 */
	public static void changeGrade(int grade) {
		TeststateJpanel teststateJpanel = Teststatejframe.getTeststatejframe().getTeststateJpanel();
		// 判断人物的等级
		if (grade <= 102) {
			teststateJpanel.getLabrolelevel().setForeground(new Color(0, 255, 0));
			teststateJpanel.getLabrolelevel().setText("0转" + grade + "级");
		} else if (grade > 102 && grade <= 210) {
			teststateJpanel.getLabrolelevel().setForeground(new Color(255, 140, 0));
			teststateJpanel.getLabrolelevel().setText("1转" + (grade - 102 + 14) + "级");
		} else if (grade > 210 && grade <= 338) {
			teststateJpanel.getLabrolelevel().setForeground(new Color(0, 245, 255));
			teststateJpanel.getLabrolelevel().setText("2转" + (grade - 210 + 14) + "级");
		} else if (grade > 338 && grade <= 459) {
			teststateJpanel.getLabrolelevel().setForeground(new Color(238, 44, 44));
			teststateJpanel.getLabrolelevel().setText("3转" + (grade - 338 + 59) + "级");
		} else if (grade > 459) {
			teststateJpanel.getLabrolelevel().setForeground(new Color(238, 44, 44));
			teststateJpanel.getLabrolelevel().setText("飞升" + (grade - 459 + 139) + "级");
		}
	}
	/** 获取人物当前等级最大经验的方法 */
	public static BigDecimal accessMaxValue(int grade) {
		BigDecimal MaxExp = null;
		// 判断人物的等级
		if (grade <= 102) {
			MaxExp = new BigDecimal(GetExp.getRoleExp(0, grade));
		} else if (grade > 102 && grade <= 210) {
			MaxExp = new BigDecimal(GetExp.getRoleExp(1, grade - 102 + 15));
		} else if (grade > 210 && grade <= 338) {
			MaxExp = new BigDecimal(GetExp.getRoleExp(2, grade - 210 + 15));
		} else if (grade > 338 && grade <= 459) {
			MaxExp = new BigDecimal(GetExp.getRoleExp(3, grade - 338 + 59));
		} else if (grade > 459) {
			MaxExp = new BigDecimal(GetExp.getRoleExp(4, grade - 459 + 139));
		}
		return MaxExp;
	}
	@Override
	protected void specificMousePressed(MouseEvent e) {
		ZhuJpanel.getLabroleimg().setBounds(getButtonBounds(1));

	}

	@Override
	protected void specificMouseReleased(MouseEvent e) {
		ZhuJpanel.getLabroleimg().setBounds(getButtonBounds(0));
		Show();
	}

	@Override
	protected void specificMouseEntered(MouseEvent e) {
		MsgJframe.getJframe().getJapnel().HPMax_min(7);
	}

	@Override
	protected void specificMouseExited(MouseEvent e) {
		FormsManagement.HideForm(46);
	}

	@Override
	protected void specificMouseDragged(MouseEvent e) {

	}

	@Override
	protected void specificMouseMoved(MouseEvent e) {

	}

	/**
	 * 角色头像位置
	 * @return
	 */
	public static Rectangle getButtonBounds(int type) {
		int types = ZhuFrame.getZhuJpanel().getType();
		if (Util.SwitchUI==1){
			int xrt = types==0?ScrenceUntil.Screen_x - 173:ScrenceUntil.Screen_x - 152;
			return new Rectangle(type==0?xrt:xrt+1, type==0?8:9, types==0?57:51, types==0?58:50);
		}else {
			int xrt = types==0?ScrenceUntil.Screen_x - 174:ScrenceUntil.Screen_x - 152;
			int ny =type==0?9:10;
			return new Rectangle(type==0?xrt:xrt+1, types==0?(type==0?7:8):ny, types==0?58:51, types==0?58:51);
		}
	}

}
