package jxy2.wormap;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.come.bean.NpcInfoBean;
import org.come.llandudno.AtlasListCell;
import org.come.model.Gamemap;
import org.come.until.SplitStringTool;
import org.come.until.SrcollPanelUI;
import org.come.until.UserMessUntil;
import org.come.until.Util;
import org.skill.panel.SkillSMSelectOptionJpanel;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class NpcQueryJPanel extends JPanel {
    public TestsmallmapBtn btnDown;
    public JScrollPane jScrollPane;
    // 列表
    public JList<String> listgamemap;
    // 列表中的对象
    public DefaultListModel<String> listModel;// 放置召唤兽的对象
    public SkillSMSelectOptionJpanel select;
    public String[] mapname;
    public static int idx = -1;
    private String labmapname;
    private JTextField npcNameCode;// NPC名字输入框
    public NpcQueryJPanel() {
        this.setPreferredSize(new Dimension(246, 466));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,131,246);
        btnDown = new TestsmallmapBtn(ImgConstants.tz82,1,2,this);
        btnDown.setBounds(200,99,18,18);
        this.add(btnDown);


        listModel = new DefaultListModel<String>();
        listgamemap = new JList<String>(listModel) {
            {
                setOpaque(false);
            } // instance initializer
        };
        LoadNPC(Util.mapmodel.getGamemap().getMapname());
        String[] rowData = mapname;
        select = new SkillSMSelectOptionJpanel(158, 120, rowData,1);
        select.setVisible(false);
        select.setBounds(60, 120, 158, 120);
        this.add(select);
        // 添加监听

        select.getJlist().addMouseListener(new MouseAdapter() {
            @Override
            public void mousePressed(MouseEvent e) {
                labmapname = select.getJlist().getSelectedValue();
                LoadNPC(labmapname);
                select.setVisible(false);
            }
        });


        listgamemap.setSelectionBackground(Color.white);
        listgamemap.setSelectionForeground(Color.green);//选中颜色
        listgamemap.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        listgamemap.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        listgamemap.setCellRenderer(new AtlasListCell(idx,Color.GRAY,160,36,6));
        listgamemap.addMouseListener(new MapJlistChoseMouslisten(listgamemap, this));
        listgamemap.addMouseMotionListener(new MapJlistChoseMouslisten(listgamemap, this));

        jScrollPane = new JScrollPane(listgamemap);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getVerticalScrollBar().setUnitIncrement(50);
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setOpaque(false);
        jScrollPane.setBounds(27, 152, 192, 230);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);

        // NPC名字搜搜输入框
        npcNameCode = Juitil.jTextField(Color.white,UIUtils.FZCY_HY14);
        npcNameCode.setBounds(23, 68, 196, 24);
        npcNameCode.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                SearchNpcName(npcNameCode.getText());
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                if (npcNameCode.getText()==null||npcNameCode.getText().isEmpty()){
                    LoadNPC(Util.mapmodel.getGamemap().getMapname());
                }else {
                    SearchNpcName(npcNameCode.getText());
                }

            }

            @Override
            public void changedUpdate(DocumentEvent e) {

            }
        });
        npcNameCode.setBorder(BorderFactory.createEmptyBorder());
        this.add(npcNameCode);
    }

    /**搜索指定的NPC*/
    public void SearchNpcName(String npcName){
        listModel.clear();
        int index = 0;
        for (Map.Entry<String, Gamemap> mappet : UserMessUntil.getAllmapbean().getAllMap().entrySet()) {
            Gamemap gamemap = mappet.getValue();
            List<String> strings = SplitStringTool.splitString(gamemap.getMapnpc());
            for (String string : strings) {
                NpcInfoBean infoBean = UserMessUntil.getnpc(string);
                if (infoBean == null) {continue;}
                if (infoBean.getNpctable().getNpcname().contains(npcName)&&!infoBean.getNpctable().getNpcname().equals(" ")
                        && !infoBean.getNpctable().getNpcname().isEmpty()
                        && !infoBean.getNpctable().getNpctype().equals("2")) {
                    String vs = infoBean.getNpctable().getNpcname() + ","
                            + infoBean.getNpctable().getTy() + ","
                            + infoBean.getNpctable().getTx() + ","
                            + infoBean.getNpctable().getMname() + ","
                            + gamemap.getMapid();
                    listModel.add(index, vs);
                    index++;
                }
            }
        }

    }

    /**初始化所有地图NPC*/
    public void LoadNPC(String namme) {
        listModel.clear();
        int index = 0;
        // 创建 TreeMap 以自然排序拼音首字母
        TreeMap<String, String> sortedMap = new TreeMap<>();
        // 遍历所有 Gamemap 对象
        for (Map.Entry<String, Gamemap> mappet : UserMessUntil.getAllmapbean().getAllMap().entrySet()) {
            Gamemap gamemap = mappet.getValue();
            String firstPinyin = getFirstPinyinChar(gamemap.getMapname());
            sortedMap.put(firstPinyin, gamemap.getMapname());
        }
        // 统计有效元素的数量
        int validMaps = sortedMap.size();
        mapname = new String[validMaps+1]; // 初始化数组
        mapname[0] = "所有场景";
        // 填充 mapname 数组
        int sortedIndex = 1;
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            mapname[sortedIndex++] = entry.getValue();
        }
        if (getLabmapname()==null){
            Gamemap gamemaps = UserMessUntil.getAllmapbean().getAllMap().get(RoleData.getRoleData().getLoginResult().getMapid()+"");
            setLabmapname(gamemaps.getMapname());
            return;
        }
        Gamemap gamemap = null;
        if (getLabmapname().equals("所有场景")){
          for (Map.Entry<String, Gamemap> mappet : UserMessUntil.getAllmapbean().getAllMap().entrySet()) {
              gamemap = mappet.getValue();
          }
         }else {
            gamemap = UserMessUntil.getAllmapbean().getAllMap().get(getNpcID(namme));
            setLabmapname(gamemap.getMapname());
        }

        if (gamemap!=null) {
            List<String> strings = SplitStringTool.splitString(gamemap.getMapnpc());
            for (String string : strings) {
                NpcInfoBean infoBean = UserMessUntil.getnpc(string);
                if (infoBean == null) {
                    continue;
                }
                if (!infoBean.getNpctable().getNpcname().equals(" ")
                        && !infoBean.getNpctable().getNpcname().isEmpty()
                        && !infoBean.getNpctable().getNpctype().equals("2")) {
                    String vs = infoBean.getNpctable().getNpcname() + ","
                            + infoBean.getNpctable().getTy() + ","
                            + infoBean.getNpctable().getTx() + ","
                            + infoBean.getNpctable().getMname() + ","
                            + gamemap.getMapid();
                    listModel.add(index, vs);
                    index++;
                }
            }
        }
    }



    /**根据名称返回ID*/
    public static String getNpcID(String name) {
        for (Map.Entry<String, Gamemap> mappet : UserMessUntil.getAllmapbean().getAllMap().entrySet()) {
            Gamemap gamemap = mappet.getValue();
            if (gamemap.getMapname().equals(name)){
                return gamemap.getMapid();
            }
        }
        return "";
    }


    /**
     * 获取字符串的首字母拼音
     *
     * @param name 地图名称
     * @return 首字母拼音
     */
    private String getFirstPinyinChar(String name) {
        StringBuilder pinyinBuilder = new StringBuilder();
        for (char c : name.toCharArray()) {
            String[] pinyin = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyin != null && pinyin.length > 0) {
                pinyinBuilder.append(pinyin[0]);
            }
        }
        return pinyinBuilder.toString();
    }


    /** 展示与隐藏下拉框 */
    public void showArenaDownLab() {
        select.setVisible(!select.isVisible());
    }

    public String title = "查找NPC";
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, getWidth()+4, 32, 1);
        Juitil.ImngBack(g, Juitil.JK_1, 0, 32, getWidth()+7, getHeight()-55, 1);
        Juitil.Subtitledrawing(g, getWidth()/2-40, 24, title, Color.WHITE, UIUtils.HYXKJ_HY20,1);
        Juitil.TextBackground(g, "输入npc的名称", 14, 25, 45, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        Juitil.TextBackground(g, "场景", 14, 25, 100, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        Juitil.ImngBack(g, Juitil.tz26, 58, 96, 163, 24, 1);
        Juitil.ImngBack(g, Juitil.tz26, 25, 68, 196, 24, 1);
        Juitil.ImngBack(g, Juitil.tz255, 25, 125, 202, 265, 1);
        Juitil.ImngBack(g, Juitil.tz76, 201, 152, 16, 229, 1);//滑轨背景
        Juitil.TextBackground(g, "*点击搜索结果可以查看NPC& 在小地图的位置", 13, 25, 385, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        if (labmapname!=null&&!labmapname.isEmpty()){
            Juitil.TextBackground(g, labmapname, 13, 60, 100, UIUtils.COLOR_CL_BACK, UIUtils.FZCY_HY13);
        }

    }

    public String getLabmapname() {
        return labmapname;
    }

    public void setLabmapname(String labmapname) {
        this.labmapname = labmapname;
    }

    public JTextField getNpcNameCode() {
        return npcNameCode;
    }

    public void setNpcNameCode(JTextField npcNameCode) {
        this.npcNameCode = npcNameCode;
    }
}
