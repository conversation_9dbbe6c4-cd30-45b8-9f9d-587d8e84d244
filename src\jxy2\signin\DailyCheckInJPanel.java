package jxy2.signin;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.entity.Goodstable;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.*;

public class DailyCheckInJPanel extends JPanel {
    private JLabel leijiQiandao,labRepair;//累计签到 补
    private JLabel[] qiandaoday,qiandaoimg;//签到天数按钮
    public DailyCheckInaBtn leftButton,rightButton,qiandao,repairBtn;
    public String chooseqiandao;
    private JLabel month;
    private Color color=UIUtils.COLOR_TREASURE;
    private DailyCheckInaBtn[] canBe = new DailyCheckInaBtn[5];
    private DailyCheckInaBtn[] rightcanBe = new DailyCheckInaBtn[5];
    private JLabel[] prize = new JLabel[5];//签到奖品
    private JLabel[] ontinuous = new JLabel[5];//连续签到奖品
    private Map<Integer,List<Goodstable>> goodstables = new HashMap<>();//获取签到奖品列表
    private Map<Integer,List<Goodstable>> goodsontin = new HashMap<>();//获取连续签到奖品列表
    private RichLabel richLabel;
    public DailyCheckInJPanel() {
        this.setPreferredSize(new Dimension(685, 515));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,122,685);
        getRichLabel();
        this.qiandaoimg = new JLabel[getDayOfMonth()];
        this.qiandaoday = new JLabel[getDayOfMonth()];
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        int daysInMonth = getDayOfMonth();
        // 获取本月的第一天是星期几
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        int firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        // 计算月份的天数
        int c,b; // X坐标
        for (int i = 1; i <= daysInMonth; i++) {
            // 确定日期的列和行
            int column = (firstDayOfWeek - Calendar.SUNDAY + i - 1) % 7;
            int row = (firstDayOfWeek - Calendar.SUNDAY + i - 1) / 7;
            // 计算坐标
            c = column;
            b = row;
            qiandaoimg[i-1] =new JLabel();
            qiandaoimg[i-1].addMouseListener(new DailyCheckInMouse(this,i));
            qiandaoimg[i-1].setBounds(30+c * 51, 170 + b * 28, 48, 25);
            this.add(qiandaoimg[i-1]);
            qiandaoday[i-1] =new JLabel();
            qiandaoday[i-1].setText((i)+"");
            this.add(qiandaoday[i-1]);

        }

        leftButton = new DailyCheckInaBtn(ImgConstants.tz89, 1,  "", this,1,"");
        leftButton.setBounds(156, 120, 17, 21);
        this.add(leftButton);
        rightButton = new DailyCheckInaBtn(ImgConstants.tz90, 1, "", this,2,"");
        rightButton.setBounds(249, 120, 17, 21);
        this.add(rightButton);

        month =new JLabel();
        month.setText("2024年"+ LocalDateTime.now().getMonthValue()+"月");
        month.setVisible(false);
        this.add(month);

        leijiQiandao = TeststateJpanel.GJpanelText(UIUtils.COLOR_White,UIUtils.FZCY_HY14);
        leijiQiandao.setBounds(115, 346, 20, 20);
        this.add(leijiQiandao);

        labRepair = TeststateJpanel.GJpanelText(UIUtils.COLOR_White,UIUtils.FZCY_HY14);
        labRepair.setBounds(115, 346+23, 150, 20);
        this.add(labRepair);

        qiandao = new DailyCheckInaBtn(ImgConstants.tz210, -1,  "签到", this,3,"");
        qiandao.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz210,"defaut.wdf"));
        qiandao.setForeground(Color.GRAY);
        qiandao.setBounds(269, 355, 59, 26);
        this.add(qiandao);

        repairBtn = new DailyCheckInaBtn(ImgConstants.tz210, -1, "补签", this,4,"补签一次扣除1000仙玉");
        repairBtn.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz210,"defaut.wdf"));
        repairBtn.setForeground(Color.GRAY);
        repairBtn.setBounds(269+65, 355, 59, 26);
        this.add(repairBtn);

        for (int i = 0; i < canBe.length; i++) {
            canBe[i] = new DailyCheckInaBtn(ImgConstants.tz127, -1, "不可领取", this,i+5,"");
            canBe[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz212,"defaut.wdf"));
            canBe[i].setForeground(Color.GRAY);
            canBe[i].setBounds(18+i*76, 470, 68, 17);
            this.add(canBe[i]);
        }
        for (int i = 0; i < rightcanBe.length; i++) {
            rightcanBe[i] = new DailyCheckInaBtn(ImgConstants.tz127, -1, "不可领取", this,i+10,"");
            rightcanBe[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz212,"defaut.wdf"));
            rightcanBe[i].setForeground(Color.GRAY);
            rightcanBe[i].setBounds(585, 163+i*45, 68, 17);
            this.add(rightcanBe[i]);
        }
        for (int i = 0; i < prize.length; i++) {
            prize[i] =new JLabel();
            prize[i].setBounds(30+i*75,413,46,49);
            prize[i].addMouseListener(new DailyCheckInMouse(this,32+i));
            prize[i].setIcon(Juitil.tz213);
            this.add(prize[i]);
        }
        for (int i = 0; i < ontinuous.length; i++) {
            ontinuous[i] =new JLabel();
            ontinuous[i].setBounds(424,155+i*60,46,49);
            ontinuous[i].addMouseListener(new DailyCheckInMouse(this,37+i));
            ontinuous[i].setIcon(Juitil.tz213);
            this.add(ontinuous[i]);
        }
    }

    /**触发抽奖*/
    public void initChoujiang(Map<Integer, List<Goodstable>> choujianBean, String choujaing,int ConsecutiveDays,Map<Integer, List<Goodstable>> lxCjBean,String lxdata) {
        int a = 0;
        for(Iterator<Map.Entry<Integer, List<Goodstable>>> ii = choujianBean.entrySet().iterator(); ii.hasNext();a++) {
            Map.Entry<Integer, List<Goodstable>> iterable_element = ii.next();
            goodstables.put(a,iterable_element.getValue());
            boolean isAlreadyClaimed = false;
            String[] data = lxdata.split("\\|");
            // 首先检查是否已领取
            for (String datum : data) {
                if (datum.equals(iterable_element.getKey().toString())) {
                    canBe[a].setIcon(Juitil.tz212);
                    canBe[a].setForeground(Color.GRAY);
                    canBe[a].setBtn(-1);
                    canBe[a].setNtext("已领取");
                    isAlreadyClaimed = true;
                    break;
                }
            }
            if (!isAlreadyClaimed) {
                if (this.duihuaninit(iterable_element.getKey(), choujaing)) {
                    canBe[a].setIcon(Juitil.tz212);
                    canBe[a].setForeground(Color.GRAY);
                    canBe[a].setBtn(-1);
                    canBe[a].setNtext("不可领取");
                } else {
                    canBe[a].setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz127, "defaut.wdf"));
                    canBe[a].setBtn(1);
                    canBe[a].setNtext("可领取");
                }
            }
        }



        int b = 0;
        int[] rewardDays = {1, 2, 3, 5, 7}; // 连续签到的天数数组
        for (Map.Entry<Integer, List<Goodstable>> entry : lxCjBean.entrySet()) {
            goodsontin.put(b, entry.getValue());
            boolean isAlreadyClaimed = false;
            String[] data = lxdata.split("\\|");
            // 首先检查是否已领取
            for (String datum : data) {
                if (datum.equals(entry.getKey().toString())) {
                    rightcanBe[b].setIcon(Juitil.tz212);
                    rightcanBe[b].setForeground(Color.GRAY);
                    rightcanBe[b].setBtn(-1);
                    rightcanBe[b].setNtext("已领取");
                    isAlreadyClaimed = true;
                    break;
                }
            }

            // 如果没有领取，再根据连续签到天数判断
            if (!isAlreadyClaimed) {
                int canClaim = duihuanint(entry.getKey(), choujaing);
                if (canClaim == 2) { // 2 表示不可领取
                    rightcanBe[b].setIcon(Juitil.tz212);
                    rightcanBe[b].setForeground(Color.GRAY);
                    rightcanBe[b].setBtn(-1);
                    rightcanBe[b].setNtext("不可领取");
                } else if (ConsecutiveDays >= rewardDays[b]) { // 满足连续签到条件，可领取
                    rightcanBe[b].setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz127, "defaut.wdf"));
                    rightcanBe[b].setBtn(1);
                    rightcanBe[b].setNtext("可领取");
                }
            }
            b++;
        }



    }
    /**
     * 如果 duijiang 存在 dengji 返回false
     * @param dengji
     * @param duijiang
     * @return
     */
    public boolean duihuaninit(Integer dengji, String duijiang) {
        if (duijiang == null) {
            return true;
        } else {
            String[] days = duijiang.split("\\|");
            for (String day : days) {
                if (!day.isEmpty() && Integer.parseInt(day) == dengji - 31) {
                    return false;
                }
            }
            return true;
        }
    }
    public int duihuanint(Integer dengji, String duijiang) {

        if (duijiang == null||duijiang.isEmpty()) {
            return 0;
        } else {
            String[] days = duijiang.split("\\|");
            for (String day : days) {
                if (!day.isEmpty() && Integer.parseInt(day) == dengji - 31) {
                    //返回1可领取
                    return 1;
                }
            }
            return 2;
        }
    }


    /**更新每月日期显示*/
    public Calendar calendar  = Calendar.getInstance(Locale.CHINA);
    public int months = LocalDateTime.now().getMonthValue();
    public void updateDayOfMonth(int month) {
        months = month;
        // 获取本月的第一天是星期几
        calendar.set(2024, month-1, 1);
        int daysInMonth = calendar.getActualMaximum(Calendar.DATE);
        this.month.setText("2024年"+month+"月");
        int firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        this.qiandaoimg = new JLabel[daysInMonth];
        this.qiandaoday = new JLabel[daysInMonth];
        int c,b; // X坐标
        for (int i = 1; i <= daysInMonth; i++) {
            qiandaoimg[i-1] =new JLabel();
            qiandaoday[i-1] =new JLabel();
            // 确定日期的列和行
            int column = (firstDayOfWeek - Calendar.SUNDAY + i - 1) % 7;
            int row = (firstDayOfWeek - Calendar.SUNDAY + i - 1) / 7;
            // 计算坐标
            c = column;
            b = row;
            // 确保不会超出数组界限
            qiandaoimg[i-1].setBounds(30+c * 51, 170 + b * 28, 48, 25);
            getQiandaoday()[i-1].setText((i)+"");
            SetTheOperability(1,LocalDateTime.now().getMonthValue() == DailyCheckInaBtn.newMonth&&getNowDay()==i);
            add(qiandaoday[i-1]);
            add(qiandaoimg[i-1]);
        }
        SetTheOperability(0,false);
        chooseqiandao=null;
    }

    /**设置签到,补充签按钮可操作性*/
    public boolean Operanility;
    public void SetTheOperability(int type ,boolean Operability) {
        this.Operanility = Operability;
        MoBanBtn targetButton = (type == 1) ? qiandao : repairBtn;
        if (!Operability) {
            targetButton.setIcon(CutButtonImage.getWdfPng(ImgConstants.tz210, "defaut.wdf"));
            targetButton.setForeground(Color.GRAY);
            targetButton.setBtn(-1);
        } else {
            targetButton.setIcons(CutButtonImage.cutsPngBtn(ImgConstants.tz34, "defaut.wdf"));
            targetButton.setBtn(1);
        }
    }

    /**初始化签到天数*/
    public String[] days;
    public void initQiandao(String dar) {
        leijiQiandao.setText("");
        int dayOne;
        if (dar != null && !dar.isEmpty()) {
            days = dar.split("\\|");
            dayOne = days.length;
        } else {
            dayOne = 0;
        }

        leijiQiandao.setText(dayOne +"");
        int nowday = getNowDay();
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        int daysInMonth = getDayOfMonth();
        // 获取本月的第一天是星期几
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        for (int i = 1; i <= daysInMonth; i++) {
            //可补签日期
            if (nowday == i){//当日已签到
                SetTheOperability(1,false);
            }
            //单独判断当日可签到
            if (nowday == i&&!sureqiandaoOrno(days, i)){
                SetTheOperability(1,true);
            }
        }
        SetTheOperability(0,false);
        chooseqiandao=null;
    }



    public String[] num = new String[]{"累积签到2次", "累积签到5次", "累积签到10次", "累积签到17次", "累积签到26次"};
    public String[] week = new String[]{"周日", "周一", "周二", "周三", "周四","周五","周六"};
    public ImageIcon icons =CutButtonImage.getWdfPng(ImgConstants.tz211, "defaut.wdf");;
    public int yearNumber = LocalDate.now().getYear();
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        g.drawImage(Juitil.QiandaoImg.getImage(),0,0,getWidth(),getHeight(),null);
        Juitil.Subtitledrawing(g, getWidth()/2-42, 24, "签到领奖", Color.WHITE, UIUtils.HYXKJ_HY20,1);
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = now.format(dateFormatter);
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String formattedTime = now.format(timeFormatter);
        String timeText = "当前时间: " + formattedDate + " " + dayOfWeek.getDisplayName(TextStyle.FULL, java.util.Locale.CHINA) + " " + formattedTime;
        Juitil.TextBackground(g, timeText, 14, 405, 485, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        Juitil.TextBackground(g, "累计签到次数  ", 14, 22, 349, UIUtils.COLOR_66BBAAFF, UIUtils.FZCY_HY14);
        Juitil.TextBackground(g, "可补签的天数  ", 14, 22, 372, UIUtils.COLOR_66BBAAFF, UIUtils.FZCY_HY14);
        Juitil.TextBackground(g, "连续签到奖励  ", 16, 490, 117, UIUtils.COLOR_66BBAAFF, UIUtils.FZCY_HY16);
        for (int i = 0; i < 5; i++) {
            Juitil.TextBackground(g, num[i], 12, 15+i*75, 392, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY12);
            rightcanBe[i].setBounds(585, 170+i*61, 68, 17);

        }
        for (int i = 0; i < 7; i++) {
            Juitil.TextBackground(g, week[i], 14, 38+i*51, 146, UIUtils.COLOR_CCFFEE, UIUtils.FZCY_HY14);
        }
        Juitil.TextBackground(g, month.getText(), 13, 175, 121, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        int nowday = getNowDay();
        calendar.set(yearNumber, months-1, 1);
        int daysInMonth = calendar.getActualMaximum(Calendar.DATE);
        int firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int numIn = 1;
        for (int i = 1; i <= daysInMonth; i++) {
            // 确定日期的列和行
            int column = (firstDayOfWeek - Calendar.SUNDAY + i -1 ) % 7;
            int row = (firstDayOfWeek - Calendar.SUNDAY + i - 1 ) / 7;
            //可补签日期
            if (!sureqiandaoOrno(days, i) && nowday >= i) {
                color = UIUtils.COLOR_TREASURE;
                //设置可补签的日期
                labRepair.setText(numIn+"");
                numIn++;
                if (chooseqiandao!=null&&i == Integer.parseInt(chooseqiandao)){
                    g.drawImage(icons.getImage(),27+column * 51, 168 + row * 28,50,27,null);
                }
            }else if (nowday == i){//当日已签到
                color = UIUtils.COLOR_HURTR1;
            }else if (sureqiandaoOrno(days, i)){
                color = UIUtils.COLOR_NAME1;//已签到
            }else {
                color = UIUtils.COLOR_808182;//大于当日的日期
                labRepair.setText(0+"");
            }
            //单独判断当日可签到
            if (nowday == i&&!sureqiandaoOrno(days, i)){
                color = UIUtils.COLOR_NAME;
            }

            if (months!=LocalDateTime.now().getMonthValue()){color = UIUtils.COLOR_TREASURE;}

                Juitil.CenterTextdrawing(g, qiandaoday[i-1].getText(), (48+60) + column * (51+50), (174+190) + row * (57), color, UIUtils.FZCY_HY14);

        }

        int[] rewardDays = {1, 2, 3, 5, 7}; // 连续签到的天数数组
        for (int i = 0; i < rewardDays.length; i++) {
            Juitil.TextBackground(g, "连续签到"+(rewardDays[i])+"次", 13, 488, 170+i*61, UIUtils.COLOR_CCFFEE, UIUtils.FZCY_HY13);
        }

        Juitil.TextBackground(g, "可签到时间：24/08/27 0点 - 24/09/26 0点", 14,390 , 88, UIUtils.COLOR_TREASURE, UIUtils.FZCY_HY14);
    }

    /**
     * 获取当前月的最大天数
     * @return 当前月的最大天数
     */
    public int getDayOfMonth() {
        // 创建一个使用中国Locale的Calendar实例，用于获取日期相关的信息
        Calendar aCalendar = Calendar.getInstance(Locale.CHINA);
        // 返回当前月的最大天数
        return aCalendar.getActualMaximum(Calendar.DATE);
    }
    /**
     * 获取当前日期
     * @return 当前日期对应的整数值，表示一个月中的第几天
     */
    public static int getNowDay() {
        Calendar cal = Calendar.getInstance(); // 创建Calendar实例，用于操作日期和时间
        return cal.get(Calendar.DATE); // 从Calendar实例中获取当前日期，并返回
    }

    /**
     * 检查指定数组中是否存在与给定天数相等的元素
     * @param mes 字符串数组，包含一系列日期或数字的字符串表示
     * @param day1 需要检查的天数
     * @return 如果数组中存在与day1相等的元素，则返回true；否则返回false
     */
    public boolean sureqiandaoOrno(String[] mes, int day1) {
        // 检查输入数组是否为空，为空则直接返回false
        if (mes == null) {
            return false;
        } else {
            // 遍历数组中的每个元素
            for (String me : mes) {
                // 判断当前元素是否为空字符串，并将其转换为整数与day1比较
                if (!me.isEmpty() && Integer.parseInt(me) == day1) {
                    // 如果找到匹配的元素，返回true
                    return true;
                }
            }
            // 遍历结束，如果没有找到匹配的元素，返回false
            return false;
        }
    }

    public JLabel[] getQiandaoday() {
        return qiandaoday;
    }

    public void setQiandaoday(JLabel[] qiandaoday) {
        this.qiandaoday = qiandaoday;
    }

    public Color getColor() {
        return color;
    }

    public void setColor(Color color) {
        this.color = color;
    }

    public JLabel[] getQiandaoimg() {
        return qiandaoimg;
    }

    public void setQiandaoimg(JLabel[] qiandaoimg) {
        this.qiandaoimg = qiandaoimg;
    }

    public JLabel[] getOntinuous() {
        return ontinuous;
    }

    public void setOntinuous(JLabel[] ontinuous) {
        this.ontinuous = ontinuous;
    }

    public JLabel[] getPrize() {
        return prize;
    }

    public void setPrize(JLabel[] prize) {
        this.prize = prize;
    }

    public Map<Integer, List<Goodstable>> getGoodstables() {
        return goodstables;
    }

    public void setGoodstables(Map<Integer, List<Goodstable>> goodstables) {
        this.goodstables = goodstables;
    }

    public Map<Integer, List<Goodstable>> getGoodsontin() {
        return goodsontin;
    }

    public void setGoodsontin(Map<Integer, List<Goodstable>> goodsontin) {
        this.goodsontin = goodsontin;
    }

    public RichLabel getRichLabel() {
        if (richLabel==null){
            richLabel = new RichLabel("天天登录，天天有奖!每天登录游戏并成功签到，即可根据连续签到的次数及累计签到的次数领取相应的签到奖励!每一轮奖励领取结束之后，保持继续登录签到，可以继续领取新一轮的签到奖励畅玩神宠江南游，好礼享不停!", UIUtils.FZCY_HY14);
            Dimension d = richLabel.computeSize(660);
            richLabel.setSize(d);
            richLabel.setPreferredSize(d);
            richLabel.setBounds(18, 43, 660, 77);
            add(richLabel);
        }
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }
}
