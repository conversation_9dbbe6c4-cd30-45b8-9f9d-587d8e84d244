package come.tool.Scene;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import org.come.until.ScrenceUntil;
import org.come.until.Util;

import com.tool.tcpimg.UIUtils;

/**倒计时*/
public class CountDown {
     
	private int  type;//0是女武神倒计时  1是上古战场倒计时
	private long endTime;//倒计时结束时间
	private long time;
	private String msg;
	private Image image;
	public CountDown(int type, long endTime) {
		super();
		this.type = type;
		this.endTime = endTime;
		if (type==0) {
			image=new ImageIcon("inkImg/background/S16.png").getImage();
		}else if (type==1) {
			image=new ImageIcon("inkImg/background/S17.png").getImage();
		}
		this.msg="";
	}
	public void draw(Graphics g) {
		long cha=endTime-Util.getTime();
		if (cha>0) {
			g.drawImage(image,0,ScrenceUntil.Screen_y-130,null);
			cha/=1000;
			if (time!=cha) {
				time=cha;
				msg=cha/60+":"+cha%60;
			}
			g.setColor(Color.YELLOW);
			g.setFont(UIUtils.TEXT_FONT41);
			g.drawString(msg, 30, ScrenceUntil.Screen_y-65);
		}
	}
	public void toString(StringBuffer buffer){
    	buffer.append(type);
    	buffer.append("$");
    	buffer.append(endTime);
    }
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public long getEndTime() {
		return endTime;
	}
	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}
	
}
