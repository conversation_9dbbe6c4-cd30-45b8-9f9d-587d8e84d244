package jxy2.wsyl;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import come.tool.JDialog.TiShiUtil;
import jxy2.jutnil.Juitil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;

public class WsylBtn extends MoBanBtn {
    public int index;
    public WsylJPanel wsylJPanel;
    public WsylBtn(String iconpath, int type, int index, String prompt, String text,WsylJPanel wsylJPanel) {
        super(iconpath, type, 0, prompt, text);
        this.index=index;
        this.wsylJPanel=wsylJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        WsylJPanel wsylJPanels= WsylJFrame.getWsylJFrame().getWsylJPanel();
        switch (index){
            case 0:
                LXiulianMainFrame.getLXiulianMainFrame().getLXiulianMainPanel().panelGetData(UserMessUntil.getChosePetMes(),wsylJPanels);
                Juitil.OpenJFrame(124);
                break;
            case 1:
                // 洗点
                OptionsJframe.getOptionsJframe().getOptionsJpanel().showBox(TiShiUtil.XiLingXi,
                        UserMessUntil.getChosePetMes().getSid(),
                        "#Y洗点需消耗#R20000#Y仙玉，会清空所有灵犀技能返还灵犀点数。#R（已开启的灵犀技能格不需要重新开启）");

                break;
            case 2:
                break;
            case 3://返回所有
                wsylJPanel.changePanel(0);
                break;
            case 4://提升修炼
                // 发送给服务器
                String xl = Agreement.getAgreement().OpenLxLockAgreement("X&"+UserMessUntil.getChosePetMes().getSid());
                // 向服务器发送信息
                SendMessageUntil.toServer(xl);
                break;
            case 5://一键修炼
                LXiulianMainPanel lXiulianMainPanel = LXiulianMainFrame.getLXiulianMainFrame().getLXiulianMainPanel();
                RoleData roleData = RoleData.getRoleData();
                // 修炼等级
                int xlv = lXiulianMainPanel.getXl();
                // 灵犀点数
                int ds = lXiulianMainPanel.getDs();
                int count = (ds+9)-(xlv);

                long gold = roleData.getLoginResult().getGold().longValue();
                if (gold < lXiulianMainPanel.getNeedMoneyNum() * count){
                    count = (int)(gold / lXiulianMainPanel.getNeedMoneyNum());
                }

                OptionsJframe.getOptionsJframe().getOptionsJpanel().
                        showBox(TiShiUtil.Oneclick, UserMessUntil.getChosePetMes().getSid(),
                                "您本次可以提升#R"+((ds+9)-(xlv))+"#W个灵犀修炼进度点，将消耗#G"+count*lXiulianMainPanel.getNeedMoneyNum()+
                                        "#W金钱和#Y"+ (lXiulianMainPanel.getNeedExperienceNum()* count)+"召唤兽经验，您确定吗？");

                break;
            case 6://确定加点
                if (wsylJPanel!=null){
                        String lingxi = wsylJPanel.getLingxi();
                    if (lingxi.isEmpty()) {
                        // 发送给服务器
                        String save = Agreement.getAgreement().OpenLxLockAgreement("S&"+UserMessUntil.getChosePetMes().getSid()+"&"+wsylJPanel.getLingxi());
                        // 向服务器发送信息
                        SendMessageUntil.toServer(save);
                    }else {
                        ZhuFrame.getZhuJpanel().addPrompt2("你还没有点过该类型的任何灵犀技能呢");
                        return;
                    }

                }
                break;
        }
    }
}
