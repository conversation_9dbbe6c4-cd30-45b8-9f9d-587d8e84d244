package jxy2.roleRelated;

import com.tool.role.RoleData;
import org.come.action.FromServerAction;
import org.come.bean.LoginResult;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.until.GsonUtil;

public class RoleSwitchControl  implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        LoginResult result = GsonUtil.getGsonUtil().getgson().fromJson(mes,LoginResult.class);
        RoleSwitchJPanel roleSwitchJPanel = RoleSwitchFrame.getRoleSwitchFrame().getRoleSwitchJPanel();
        roleSwitchJPanel.initRoleInfo(result);
        RoleData.getRoleData().setLoginResult(result);
        PetAddPointMouslisten.getplayerValue();
    }
}
