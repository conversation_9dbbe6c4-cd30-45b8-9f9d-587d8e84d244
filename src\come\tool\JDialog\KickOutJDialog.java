package come.tool.JDialog;

import org.come.Frame.FactionMainJframe;
import org.come.Jpanel.FactionMemberJpanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

/**
 * 踢出帮派
 * 
 * <AUTHOR>
 */
public class KickOutJDialog implements TiShiChuLi {

    @Override
    public void tipBox(boolean tishi, Object object) {
        // TODO Auto-generated method stub
        int index = (int) object;
        if (tishi) {
            // TODO Auto-generated method stub
            try {
                // //复制写给客户端的流
                // String
                // sendMes=Agreement.GangShotAgreement(TestgrpactualJframe.getTestgrpactualJframe().getJgactual().getRoleTables().get(index).getRolename());
                // //返还给客户端
                // //向服务器发送信息
                // SendMessageUntil.toServer(sendMes);
                // TestgrpactualJframe.getTestgrpactualJframe().getJgactual().gangshop(index);

                FactionMemberJpanel factionMemberJpanel = FactionMainJframe.getFactionMainJframe()
                        .getFactionMainJpanel().getFactionCardJpanel().getFactionMemberJpanel();
                // 复制写给客户端的流
                String sendMes = Agreement.GangShotAgreement(factionMemberJpanel.getFactionCardJpanel()
                        .getGangResultBean().getRoleTables().get(index).getRole_id().toString());
                // 返还给客户端
                // 向服务器发送信息
                SendMessageUntil.toServer(sendMes);
                // TestgrpactualJframe.getTestgrpactualJframe().getJgactual().gangshop(index);
                factionMemberJpanel.getTableModel().removeRow(index);
                factionMemberJpanel.getFactionCardJpanel().getGangResultBean().getRoleTables().remove(index);

            } catch (Exception e2) {
                // TODO: handle exception
            }
        }
    }
}
