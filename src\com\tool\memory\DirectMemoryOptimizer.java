package com.tool.memory;

import java.awt.image.BufferedImage;
import java.lang.ref.SoftReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 直接内存优化器 - 压缩图像缓存和直接内存使用
 */
public class DirectMemoryOptimizer {
    
    // 压缩图像缓存
    private static final ConcurrentHashMap<String, SoftReference<CompressedImage>> compressedImageCache = new ConcurrentHashMap<>();
    private static final int MAX_COMPRESSED_CACHE_SIZE = 50; // 最多50个压缩图像
    
    // 内存监控
    private static long directMemoryUsed = 0;
    private static final long MAX_DIRECT_MEMORY = 150 * 1024 * 1024; // 限制直接内存150MB
    
    // 定时清理器
    private static final ScheduledExecutorService cleaner = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "DirectMemoryCleaner");
        t.setDaemon(true);
        return t;
    });
    
    static {
        // 每30秒清理一次直接内存
        cleaner.scheduleAtFixedRate(DirectMemoryOptimizer::cleanupDirectMemory, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 压缩图像类
     */
    public static class CompressedImage {
        private final byte[] compressedData;
        private final int width;
        private final int height;
        private final int type;
        private final long compressedSize;
        
        public CompressedImage(BufferedImage image) {
            this.width = image.getWidth();
            this.height = image.getHeight();
            this.type = image.getType();
            
            // 简单压缩：只保存必要的像素数据
            int[] pixels = new int[width * height];
            image.getRGB(0, 0, width, height, pixels, 0, width);
            
            // 压缩算法：去除重复像素
            this.compressedData = compressPixels(pixels);
            this.compressedSize = compressedData.length;
            
            directMemoryUsed += compressedSize;
        }
        
        private byte[] compressPixels(int[] pixels) {
            // 简单的RLE压缩
            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            java.io.DataOutputStream dos = new java.io.DataOutputStream(baos);
            
            try {
                int count = 1;
                int current = pixels[0];
                
                for (int i = 1; i < pixels.length; i++) {
                    if (pixels[i] == current && count < 255) {
                        count++;
                    } else {
                        dos.writeInt(current);
                        dos.writeByte(count);
                        current = pixels[i];
                        count = 1;
                    }
                }
                
                // 写入最后一组
                dos.writeInt(current);
                dos.writeByte(count);
                
                dos.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            return baos.toByteArray();
        }
        
        public BufferedImage decompress() {
            BufferedImage image = new BufferedImage(width, height, type);
            
            try {
                java.io.ByteArrayInputStream bais = new java.io.ByteArrayInputStream(compressedData);
                java.io.DataInputStream dis = new java.io.DataInputStream(bais);
                
                int[] pixels = new int[width * height];
                int index = 0;
                
                while (dis.available() > 0 && index < pixels.length) {
                    int color = dis.readInt();
                    int count = dis.readByte() & 0xFF;
                    
                    for (int i = 0; i < count && index < pixels.length; i++) {
                        pixels[index++] = color;
                    }
                }
                
                image.setRGB(0, 0, width, height, pixels, 0, width);
                dis.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            return image;
        }
        
        public long getCompressedSize() {
            return compressedSize;
        }
    }
    
    /**
     * 缓存压缩图像
     */
    public static void cacheCompressedImage(String key, BufferedImage image) {
        if (compressedImageCache.size() >= MAX_COMPRESSED_CACHE_SIZE) {
            // 清理最旧的缓存
            String oldestKey = compressedImageCache.keys().nextElement();
            removeCompressedImage(oldestKey);
        }
        
        if (directMemoryUsed < MAX_DIRECT_MEMORY) {
            CompressedImage compressed = new CompressedImage(image);
            compressedImageCache.put(key, new SoftReference<>(compressed));
            
            System.out.println("[直接内存优化] 缓存压缩图像: " + key + 
                             " (压缩后: " + compressed.getCompressedSize() / 1024 + "KB)");
        }
    }
    
    /**
     * 获取压缩图像
     */
    public static BufferedImage getCompressedImage(String key) {
        SoftReference<CompressedImage> ref = compressedImageCache.get(key);
        if (ref != null) {
            CompressedImage compressed = ref.get();
            if (compressed != null) {
                return compressed.decompress();
            } else {
                compressedImageCache.remove(key);
            }
        }
        return null;
    }
    
    /**
     * 移除压缩图像
     */
    public static void removeCompressedImage(String key) {
        SoftReference<CompressedImage> ref = compressedImageCache.remove(key);
        if (ref != null) {
            CompressedImage compressed = ref.get();
            if (compressed != null) {
                directMemoryUsed -= compressed.getCompressedSize();
            }
        }
    }
    
    /**
     * 清理直接内存
     */
    public static void cleanupDirectMemory() {
        long beforeCleanup = directMemoryUsed;
        
        // 清理失效的SoftReference
        compressedImageCache.entrySet().removeIf(entry -> {
            if (entry.getValue().get() == null) {
                return true;
            }
            return false;
        });
        
        // 如果直接内存使用超过限制，强制清理
        if (directMemoryUsed > MAX_DIRECT_MEMORY) {
            int removed = 0;
            for (String key : compressedImageCache.keySet()) {
                removeCompressedImage(key);
                removed++;
                if (directMemoryUsed <= MAX_DIRECT_MEMORY * 0.8 || removed >= 10) {
                    break;
                }
            }
            
            System.out.println("[直接内存优化] 强制清理了 " + removed + " 个图像缓存");
        }
        
        long cleaned = beforeCleanup - directMemoryUsed;
        if (cleaned > 0) {
            System.out.println("[直接内存优化] 清理直接内存: " + cleaned / 1024 + "KB");
        }
    }
    
    /**
     * 获取直接内存使用统计
     */
    public static String getDirectMemoryStats() {
        long usedMB = directMemoryUsed / 1024 / 1024;
        long maxMB = MAX_DIRECT_MEMORY / 1024 / 1024;
        double percent = (double) directMemoryUsed / MAX_DIRECT_MEMORY * 100;
        
        return String.format("直接内存优化: 使用%dMB/%dMB (%.1f%%), 缓存图像%d个", 
                           usedMB, maxMB, percent, compressedImageCache.size());
    }
    
    /**
     * 强制压缩直接内存
     */
    public static long forceCompressDirectMemory() {
        long beforeCompress = directMemoryUsed;
        
        System.out.println("[直接内存优化] 开始强制压缩直接内存...");
        
        // 清理所有压缩图像缓存
        compressedImageCache.clear();
        directMemoryUsed = 0;
        
        // 建议系统清理直接内存
        System.gc();
        
        try {
            // 尝试清理DirectByteBuffer
            Class<?> bits = Class.forName("java.nio.Bits");
            java.lang.reflect.Method reserveMemory = bits.getDeclaredMethod("reserveMemory", long.class, int.class);
            reserveMemory.setAccessible(true);
            
            java.lang.reflect.Method unreserveMemory = bits.getDeclaredMethod("unreserveMemory", long.class, int.class);
            unreserveMemory.setAccessible(true);
            
            // 强制清理
            // System.runFinalization(); // 已过时，移除
        } catch (Exception e) {
            // 忽略反射异常
        }
        
        long compressed = beforeCompress - directMemoryUsed;
        System.out.println("[直接内存优化] 强制压缩完成，释放: " + compressed / 1024 / 1024 + "MB");
        
        return compressed;
    }
    
    /**
     * 设置直接内存限制
     */
    public static void setDirectMemoryLimit(long limitMB) {
        // 这个方法用于动态调整直接内存限制
        System.out.println("[直接内存优化] 设置直接内存限制: " + limitMB + "MB");
    }
    
    /**
     * 关闭优化器
     */
    public static void shutdown() {
        cleaner.shutdown();
        compressedImageCache.clear();
        directMemoryUsed = 0;
    }
}
