package come.tool.FightingData;

import java.util.ArrayList;
import java.util.List;

import org.come.bean.LoginResult;
import org.come.entity.RoleSummoning;
import org.come.model.Lingbao;

/**
 * 战斗结束
 * <AUTHOR>
 *
 */
public class FightingEndD {
	//玩家数据
	private LoginResult loginResult;
	//召唤兽数据集合
	private RoleSummoning pet;	
	//战斗编号
	private int Fightingsumber;
	private List<Lingbao> lingbaos;
	private String mData;
	//送他去监狱 0不送 1走你 2送你回地府
	private int type;
	private Integer doorId;
	public String getmData() {
		return mData;
	}
	public void updatamData(String data) {
		if (this.mData==null) {
			this.mData=data;
		}else {
			this.mData+="%"+data;
		}
	}
	public void setmData(String mData) {
		this.mData = mData;
	}
	public void addLingbao(Lingbao lingbao) {
		getLingbaos().add(lingbao);
	}
	public List<Lingbao> getLingbaos() {
		if (lingbaos==null) {
			lingbaos=new ArrayList<>();
		}
		return lingbaos;
	}
	public void setLingbaos(List<Lingbao> lingbaos) {
		this.lingbaos = lingbaos;
	}
	public LoginResult getLoginResult() {
		return loginResult;
	}
	public void setLoginResult(LoginResult loginResult) {
		this.loginResult = loginResult;
	}
	public RoleSummoning getPet() {
		return pet;
	}
	public void setPet(RoleSummoning pet) {
		this.pet = pet;
	}
	public int getFightingsumber() {
		return Fightingsumber;
	}
	public void setFightingsumber(int fightingsumber) {
		Fightingsumber = fightingsumber;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public Integer getDoorId() {
		return doorId;
	}
	public void setDoorId(Integer doorId) {
		this.doorId = doorId;
	}
}
