package com.tool.role;
//经验获取
public class ExpUtil {
   //获取灵宝法宝的经验
   public static long LFExp(int lvl){
	     return lvl*lvl*15-(lvl-1)*(lvl-1)*15;
   }
   //获取灵宝法宝的等级总经验
   public static long LFExp2(int lvl){
	     return lvl*lvl*15;
   }
   //灵宝经验描述转换    12时辰一天  一年365天   1年1天1时辰 
   public static long YEAR=12*365;
   public static long DAY=12;
   public static String LFExptoString(long exp){
	     StringBuffer buffer=new StringBuffer();
	     buffer.append(exp/YEAR);
	     buffer.append("年");
	     exp%=YEAR;
	     buffer.append(exp/DAY);
	     buffer.append("天");
	     buffer.append(exp%=DAY);
	     buffer.append("时辰");
	     return buffer.toString();
   }
}
