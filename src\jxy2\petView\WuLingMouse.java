package jxy2.petView;

import jxy2.jutnil.Juitil;
import jxy2.supet.SipetUtil;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.WuLingJPanel;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.CutButtonImage;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.util.Arrays;

public class WuLingMouse extends TemplateMouseListener {
    public int index;
    public WuLingJPanel wuLingJPanel;
    private Skill skill;
    public WuLingMouse(int index, WuLingJPanel wuLingJPanel) {
        this.index = index;
        this.wuLingJPanel = wuLingJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (skill!=null) {
            RoleSummoning pet = UserMessUntil.getChosePetMes();
            //判断是否在可悟灵技能内
            boolean containsId = Arrays.stream(SipetUtil.lingfan).anyMatch(id -> id == Integer.parseInt(skill.getSkillid()));
            if (!containsId){
                ZhuFrame.getZhuJpanel().addPrompt("无法选择技能，无法悟灵");
                return;
            }
            //判断技能是否开启
            boolean skillLingjieEnabled = PetSkillQiLingBtn.isSkillLingjieEnabled(pet, skill.getSkillid());
            if (!skillLingjieEnabled){
                ZhuFrame.getZhuJpanel().addPrompt("#R当前技能还未开启悟灵，无法操作！");
                return;
            }

            setPosition(skill,144,79,43,43);
            setSkillBorder(index);
            //设置选中技能图标圆角以及展示
            ImageIcon image = CutButtonImage.getImage("0x6B6B" + skill.getSkillid(), 50, 50);
            Image imgs = Juitil.toRoundedCornerImage(image.getImage(), 15);
            wuLingJPanel.getLabskillimg().setIcon(new ImageIcon(imgs));
            wuLingJPanel.setPetskillid(skill.getSkillid());
            //设置选中技能当前等级
            int level = PetSkillQiLingBtn.getSkillLingjieLevel(UserMessUntil.getChosePetMes(),skill.getSkillid());
            wuLingJPanel.level = level+"/10";
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        if (skill!=null){
            setPosition(skill,140,82,50,50);
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }

    //技能边框显示
    public void setSkillBorder(int index){
        wuLingJPanel.getLabSkillBox()[index].setBorder(BorderFactory.createLineBorder(Color.RED,3));
        for (int i = 0; i < wuLingJPanel.getLabSkillBox().length; i++) {
            if (i != index){
                wuLingJPanel.getLabSkillBox()[i].setBorder(BorderFactory.createEmptyBorder());
            }
        }
    }
    //通用坐标修改
    public void setPosition(Skill skill,int zx,int zy,int w,int h){
        int nx = Util.SwitchUI==1?31:6;
        int ny = Util.SwitchUI==1?-19:-8;
        int x = wuLingJPanel.getPetSkillsJpanel().positions[index][0]+nx; // 获取X坐标
        int y = wuLingJPanel.getPetSkillsJpanel().positions[index][1]+ny; // 获取Y坐标
        ImageIcon img = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(),w,h,"skill.wdf");
        wuLingJPanel.getLabPetskills()[index].setIcon(img);
        wuLingJPanel.getLabPetskills()[index].setBounds(x+zx, y-zy, w, h); // 设置按钮位置和大小
    }

    public Skill getSkill() {
        return skill;
    }

    public void setSkill(Skill skill) {
        this.skill = skill;
    }
}
