package jxy2.supet;

import com.tool.btn.MoBanBtn;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChosePetSkillsMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.UserMessUntil;

import java.awt.event.MouseEvent;

public class JupoDanBtn extends MoBanBtn {
    public JupoDanJPanel jupoDanJPanel;
    public PetSkillsJpanel petSkillsJpanel;
    public int BtnId;
    public JupoDanBtn(String iconpath, int type, int BtnId, String labelName, JupoDanJPanel jupoDanJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.jupoDanJPanel = jupoDanJPanel;
    }

    public JupoDanBtn(String iconpath, int type, int BtnId,JupoDanJPanel jupoDanJPanel) {
        super(iconpath, type);
        this.BtnId = BtnId;
        this.jupoDanJPanel = jupoDanJPanel;
    }
    public JupoDanBtn(String iconpath, int type, int BtnId,PetSkillsJpanel petSkillsJpanel) {
        super(iconpath, type);
        this.BtnId = BtnId;
        this.petSkillsJpanel = petSkillsJpanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        switch (BtnId){
            case 1:
            case 2:
                if (jupoDanJPanel.getGoodstable()==null){
                    ZhuFrame.getZhuJpanel().addPrompt("#R您还没有选择聚魄丹");
                    return;
                }
                if (BtnId!=2&&PetSkillfull()) return;
                String sendmes = Agreement.getAgreement().userpetAgreement(jupoDanJPanel.getGoodstable().getRgid().toString() + "|" + UserMessUntil.getChosePetMes().getSid());
                SendMessageUntil.toServer(sendmes);
                for (int i = 0; i < jupoDanJPanel.getListModel().size(); i++) {
                    String zhi = jupoDanJPanel.getListModel().get(i).split("\\|")[1];
                    if (zhi.equals(jupoDanJPanel.getGoodstable().getRgid().toString())) {
                        jupoDanJPanel.getListModel().remove(i);
                    }
                }
                jupoDanJPanel.setGoodstable(null);
                break;
            case 3:  // 技能加锁
            case 4:  // 技能解锁
                // 统一处理逻辑
                int lockValue = (BtnId == 3) ? 1 : 0;
                // 获取当前召唤兽对象（避免重复调用）
                RoleSummoning chosenPet = UserMessUntil.getChosePetMes();
                if (chosenPet == null) {
                    throw new IllegalStateException("未选择召唤兽");
                }
                // 设置技能锁状态
                chosenPet.setSkilllock(lockValue);
                // 构建服务器协议消息
                String serverMes = Agreement.getAgreement().DemonsAgreement(
                        "N" + chosenPet.getSid()
                );

                // 发送消息到服务器
                SendMessageUntil.toServer(serverMes);
                JupoDanMouse.refreshPetSkills(UserMessUntil.getChosePetMes());
                ChosePetSkillsMouslisten.refreshPetSkills();
                break;

        }


    }

    public static boolean PetSkillfull() {
        String petskill = UserMessUntil.getChosePetMes().getPetSkills();
        int sum = UserMessUntil.getChosePetMes().getOpenSeal();
        if (sum > 0 && petskill != null && !petskill.isEmpty()) {
            String[] vs = petskill.split("\\|");
            if (vs.length >= 8 || sum <= vs.length) {
                ZhuFrame.getZhuJpanel().addPrompt2("召唤兽技能格子已经满了");
                return true;
            }
        }
        return false;
    }
}
