package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SeventyTwoChangesJpanel;
import org.come.model.aCard;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.UserMessUntil;

import com.tool.role.RoleData;

public class SevenTwoChangesBtn extends MoBanBtn {

    private int caozuo;
    private SeventyTwoChangesJpanel seventyTwoChangesJpanel;

    public SevenTwoChangesBtn(String iconpath, int type, String text, int caozuo,
            SeventyTwoChangesJpanel seventyTwoChangesJpanel) {
        super(iconpath, type);
        setText(text);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
        setForeground(Color.yellow);
        this.caozuo = caozuo;
        this.seventyTwoChangesJpanel = seventyTwoChangesJpanel;
    }

    public SevenTwoChangesBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            SeventyTwoChangesJpanel seventyTwoChangesJpanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.seventyTwoChangesJpanel = seventyTwoChangesJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        switch (caozuo) {
        case 0:
        case 1:
            seventyTwoChangesJpanel.changeMenuBtnSeventyTwoChanges(caozuo);
            break;
        case 10:
            btnChoose(0);
            break;
        case 11:
            btnChoose(1);
            break;
        case 20:
            seventyTwoChangesJpanel.getACardDatas(UserMessUntil.getaCardMap(),
                    seventyTwoChangesJpanel.getChooseMoneyType(), seventyTwoChangesJpanel.getSelectNameText().getText()
                            .trim());
            break;
        default:
            break;
        }
    }

    /**
     * 按钮选择变身
     * 
     * @param type
     *            判断是否变身
     */
    public void btnChoose(int type) {
        aCard chooseCard = seventyTwoChangesJpanel.getChooseCard();
        if (chooseCard == null) {
            return;
        }
        if (chooseCard.getType() == 0) {
            if (new BigDecimal(chooseCard.getMoney()).compareTo(RoleData.getRoleData().getLoginResult().getGold()) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足");
                return;
            }
        } else {
            if (new BigDecimal(chooseCard.getMoney()).compareTo(RoleData.getRoleData().getLoginResult().getCodecard()) > 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("仙玉不足");
                return;
            }
        }
        String senmes = Agreement.getAgreement().usercardAgreement(type + "|" + chooseCard.getId());
        SendMessageUntil.toServer(senmes);
    }
}
