package jxy2.wsyl;

import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;

public class ChosePetLxMouslisten {


    public static void refreshPetSkills() {
        WsylJPanel wsylJPanel = WsylJFrame.getWsylJFrame().getWsylJPanel();

        // 判断该召唤兽是否有灵犀技能
        if (UserMessUntil.getChosePetMes() != null && UserMessUntil.getChosePetMes().getTurnRount() >= 4) {
            // 飞升召唤兽展示灵犀按钮
            // 灵犀显示按钮
            // 使用 SwingUtilities.invokeLater 确保在事件调度线程中执行
//            SwingUtilities.invokeLater(() -> {
                wsylJPanel.init(); // 调用 init 方法
//            });
            return;
        }
        // 未选择召唤兽则关闭灵犀面板
        FormsManagement.HideForm(123);
    }
}
