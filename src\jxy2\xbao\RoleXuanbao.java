package jxy2.xbao;

import com.tool.role.RoleProperty;
import jxy2.jutnil.Juitil;
import jxy2.npk.NpkImageReader;
import org.come.Frame.ZhuFrame;
import org.come.until.CutButtonImage;
import org.come.until.UserData;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RoleXuanbao {
    private static RoleXuanbao roleXuanbao;
    private Xuanbao[] xuanbaos = new Xuanbao[38];
    // 选中的
    public Xuanbao choseBao = null;
    // 装备的玄宝位置
    public Xuanbao[] equipBao = new Xuanbao[3];
    // 玄宝页数
    private int xuanNum = 0;// 页数 0表示第一页
    // 存放当前页面的img
    private ImageIcon[] Xbimg = new ImageIcon[20];
    private List<String> listname = new ArrayList<>();
    // 记录玄宝原始位置
    private java.util.Map<String, Integer> originalPositions = new java.util.HashMap<>();
    public int currentIndex = 0;//计算出一共有多个玄宝
    public static RoleXuanbao getRoleXuanbao() {
        if (roleXuanbao == null) {
            roleXuanbao = new RoleXuanbao();
        }
        return roleXuanbao;
    }

    public static ImageIcon XB_G = CutButtonImage.getWdfPng("0x6FEB9086",20,20,"defaut.wdf");
    public static ImageIcon Xuanbaoimg(String xuanbao, int w, int h) {
        return NpkImageReader.getNpkPng(xuanbao, w, h, "item.npk");
    }

    public void lingChange(List<Xuanbao> list) {
        if (list == null) {return;}
        Arrays.fill(xuanbaos, null);
        originalPositions.clear();
        XbaoJpanel xbaoJpanel = XbaoFrame.getXbaoFrame().getXbaoJpanel();
        
          // 当前存放位置
        
        // 先处理已装备的玄宝
        for (Xuanbao xuanbao : list) {
            if (xuanbao.getEquipment() == 1) {
                xuanbaos[currentIndex] = xuanbao;
                equipBao[currentIndex] = xuanbao;
                xbaoJpanel.getWearableImg()[currentIndex].setIcon(Xuanbaoimg(UserMessUntil.getXuanabaoSikn(xuanbao.getBaoname()), 50, 50));
                if (currentIndex == 0) {
                    xbaoJpanel.drawEquip(0, true);
                    xbaoJpanel.LoadingOfSelected(equipBao[0]);
                }
                currentIndex++;
                originalPositions.put(xuanbao.getId()+"", currentIndex);

            }
            listname.add(xuanbao.getBaoname());
        }
        xbaoJpanel.getXuanbaoframe()[0].setBorder(BorderFactory.createLineBorder(Color.red,2));
        // 再处理未装备的玄宝，从位置0开始（如果没有已装备的玄宝）
        for (Xuanbao xuanbao : list) {
            if (xuanbao.getEquipment() == 0) {
                xuanbaos[currentIndex] = xuanbao;
                currentIndex++;
            }
        }

        lingNumChange(xuanNum);
    }

    public void lingNumChange(int number) {
        xuanNum = number;
        // 清空当前页面的图标
        Arrays.fill(Xbimg, null);

        // 根据页面类型决定每页显示数量
        int itemsPerPage = XuanbaoMouse.shijian ? 20 : 8; // 装备页20个(10x2)，属性页8个(4x2)

        // 计算当前页面的起始索引
        int startIndex = xuanNum * itemsPerPage;

        // 计算总玄宝数量
        int totalItems = 0;
        for (Xuanbao xuanbao : xuanbaos) {
            if (xuanbao != null) totalItems++;
        }

        // 计算最大页数 (向上取整)
        int maxPage = (totalItems - 1) / itemsPerPage;

        // 设置当前页面的图标
        int displayCount = XuanbaoMouse.shijian ? 20 : 8;
        for (int i = 0; i < displayCount; i++) {
            int currentIndex = startIndex + i;
            if (currentIndex < xuanbaos.length && xuanbaos[currentIndex] != null) {
                XuanImg(i, Xuanbaoimg(UserMessUntil.getXuanabaoSikn(xuanbaos[currentIndex].getBaoname()), -1, -1));
            }
        }

        // 更新翻页按钮状态
        XbaoFrame xbaoFrame = XbaoFrame.getXbaoFrame();
        if (xbaoFrame != null) {
            XbaoJpanel xbaoJpanel = xbaoFrame.getXbaoJpanel();
            if (xbaoJpanel != null) {
                // 更新上一页按钮状态
                if (xbaoJpanel.getPreviousPage() != null) {
                    if (number > 0) {
                        xbaoJpanel.getPreviousPage().setBtn(1);
                        ImageIcon[] path = Util.SwitchUI==1?Juitil.bt3:Juitil.bt5;
                        xbaoJpanel.getPreviousPage().setIconPath(path);
                    } else {
                        xbaoJpanel.getPreviousPage().setBtn(-1);
                        xbaoJpanel.getPreviousPage().setIcon(Juitil.tz370);
                    }
                }

                // 更新下一页按钮状态
                if (xbaoJpanel.getNextPage() != null) {
                    if (number < maxPage) {
                        xbaoJpanel.getNextPage().setBtn(1);
                        ImageIcon[] path = Util.SwitchUI==1?Juitil.bt4:Juitil.bt6;
                        xbaoJpanel.getNextPage().setIconPath(path);
                    } else {
                        xbaoJpanel.getNextPage().setBtn(-1);
                        xbaoJpanel.getNextPage().setIcon(Juitil.tz371);
                    }
                }
            }
        }
    }
    public void XuanImg(int Position, ImageIcon imageIcon) {
        Xbimg[Position] = imageIcon;
    }
    /**
     * 添加玄宝
     */
    public void addXuanbao(Xuanbao xuanbao) {

        if (xuanbao.getEquipment()==1){
            for (int i = 0; i < equipBao.length; i++) {
                if (equipBao[i].getBaoname().equals(xuanbao.getBaoname())){
                    equipBao[i] = xuanbao;
                    break;
                }
            }
        }


        int p = -1;
        for (int i = 0; i < xuanbaos.length; i++) {
            if (xuanbaos[i] == null) {
                if (p == -1) {
                    p = i;
                }
            }else if (xuanbaos[i].getId().compareTo(xuanbao.getId()) == 0) {
                xuanbaos[i] = xuanbao;
                return;
            }
        }
        if (p == -1) {
            return;
        }
        xuanbaos[p] = xuanbao;
        lingNumChange(xuanNum);
    }
    /** 获取玄宝 */
    public Xuanbao getxuanbao(int path) {
        // 根据页面类型决定每页显示数量
        int itemsPerPage = XuanbaoMouse.shijian ? 20 : 8; // 装备页20个(10x2)，属性页8个(4x2)
        
        // 计算实际索引
        int realIndex = xuanNum * itemsPerPage + path;
        
        if (realIndex >= 0 && realIndex < xuanbaos.length) {
            return xuanbaos[realIndex];
        }
        return null;
    }

    public void drawXb(Graphics g, int x, int y) {
        for (int i = 0; i < 20; i++) {
            int row = i % 10 * 51;
            int col = i / 10 * 51;
            Xuanbao xuanbao = getxuanbao(i);
            if (xuanbao != null){
                boolean is = XuanbaoMouse.list.contains(i+"");
                if (Xbimg[i]!=null){
                    if (is){
                        g.drawImage(Xbimg[i].getImage(), 1+x + row, 1+y + col, 50, 50, null);
                    }else {
                        g.drawImage(Xbimg[i].getImage(), x + row, y + col, 50, 50, null);
                    }

                }
                if (xuanbao.getEquipment() == 1) {
                    g.drawImage(XB_G.getImage(), x + row, y + col, null);
                }
            }
        }
    }
    public void drawXbac(Graphics g, int x, int y) {
        // 绘制2层4格布局
        for (int i = 0; i < 8; i++) {
            int row = i % 4 * 51;  // 每行4格
            int col = i / 4 * 51;  // 2层
            Xuanbao xuanbao = getxuanbao(i);
            if (xuanbao != null){
                boolean is = XuanbaoMouse.list.contains(i+"");
                if (Xbimg[i]!=null){
                    if (is){
                        g.drawImage(Xbimg[i].getImage(), 1+x + row, 1+y + col, 50, 50, null);
                    }else {
                        g.drawImage(Xbimg[i].getImage(), x + row, y + col, 50, 50, null);
                    }
                }
                if (xuanbao.getEquipment() == 1) {
                    g.drawImage(XB_G.getImage(), x + row, y + col, null);
                }
            }
        }
    }
    /** 穿戴玄宝 true 表示装备 false 脱下 */
    public void choseuse(Xuanbao xuanbao, boolean type) {
        XbaoJpanel xbaoJpanel = XbaoFrame.getXbaoFrame().getXbaoJpanel();
        if (type) {// 装备
            if (xuanbao.getEquipment() != 0) {
                return;
            }
            // 寻找空闲的装备位置
            int path = -1;
            for(int i = 0; i < equipBao.length; i++) {
                if(equipBao[i] == null) {
                    path = i;
                    break;
                }
            }
            // 如果没有空闲位置，返回
            if (path == -1) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有空闲位置");
                return;
            }

            // 记录当前位置
            int currentPos = -1;
            for(int i = 0; i < xuanbaos.length; i++) {
                if(xuanbaos[i] != null && xuanbaos[i].getId().equals(xuanbao.getId())) {
                    currentPos = i;
                    break;
                }
            }

            if(currentPos != -1) {
                // 记录原始位置用于脱下时还原
                originalPositions.put(xuanbao.getId()+"", currentPos);

                // 移动到装备位置
                Xuanbao temp = xuanbaos[currentPos];
                // 将中间的元素向后移动
                for(int i = currentPos; i > path; i--) {
                    xuanbaos[i] = xuanbaos[i-1];
                }
                xuanbaos[path] = temp;
            }

            // 装备到空闲位置
            equipBao[path] = xuanbao;
            choseBao = xuanbao;
            xuanbao.setEquipment(1);
            xbaoJpanel.drawEquip(path,true);
            xbaoJpanel.LoadingOfSelected(xuanbao);
            UserData.upxuan(xuanbao);
            xbaoJpanel.getWearableImg()[path].setIcon(Xuanbaoimg(UserMessUntil.getXuanabaoSikn(xuanbao.getBaoname()), 50, 50));

        } else {// 脱下
            // 查找装备位置
            int equipPos = -1;
            int arrayPos = -1;

            // 先找到在装备栏中的位置
            for(int i = 0; i < equipBao.length; i++) {
                if(equipBao[i] != null && equipBao[i].getId().equals(xuanbao.getId())) {
                    equipPos = i;
                    break;
                }
            }

            // 找到在数组中的实际位置
            for(int i = 0; i < xuanbaos.length; i++) {
                if(xuanbaos[i] != null && xuanbaos[i].getId().equals(xuanbao.getId())) {
                    arrayPos = i;
                    break;
                }
            }

            if(equipPos != -1) {
                // 保存要移动的玄宝
                Xuanbao tempXuanbao = xuanbaos[arrayPos];

                // 清除装备状态
                equipBao[equipPos] = null;
                choseBao = null;
                xuanbao.setEquipment(0);
                xbaoJpanel.getWearableImg()[equipPos].setIcon(null);

                // 找到最后一个非空位置
                int lastPos = -1;
                for(int i = 0; i < xuanbaos.length; i++) {
                    if(xuanbaos[i] != null && i != arrayPos) {
                        lastPos = i;
                    }
                }

                // 如果当前位置不是最后，需要移动其他玄宝
                if(arrayPos <= lastPos) {
                    // 将后面的元素向前移动
                    for(int i = arrayPos; i < lastPos; i++) {
                        xuanbaos[i] = xuanbaos[i + 1];
                    }
                }

                // 放到最后
                xuanbaos[lastPos == -1 ? 0 : lastPos] = tempXuanbao;
                
                // 清除原来位置的数据
                if(lastPos >= 0) {
                    xuanbaos[lastPos + 1] = null;
                }

                originalPositions.remove(xuanbao.getId()+"");
                // 刷新显示
                lingNumChange(xuanNum);
            }

            UserData.upxuan(xuanbao);
        }

        RoleProperty.ResetEw();
        lingNumChange(xuanNum); // 刷新显示
    }

    public int getXuanNum() {
        return xuanNum;
    }

    public void setXuanNum(int xuanNum) {
        this.xuanNum = xuanNum;
    }

    public Xuanbao[] getXuanbaos() {
        return xuanbaos;
    }

    public void setXuanbaos(Xuanbao[] xuanbaos) {
        this.xuanbaos = xuanbaos;
    }

    public ImageIcon[] getXbimg() {
        return Xbimg;
    }

    public void setXbimg(ImageIcon[] xbimg) {
        Xbimg = xbimg;
    }

    public List<String> getListname() {
        return listname;
    }

    public void setListname(List<String> listname) {
        this.listname = listname;
    }
}
