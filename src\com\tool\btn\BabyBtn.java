package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.TestChildJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.entity.Baby;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.UserMessUntil;

import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import com.tool.tcpimg.UIUtils;

public class BabyBtn extends MoBanBtn {

    private int caozuo;

    public BabyBtn(String iconpath, int type, String text) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(UIUtils.TEXT_FONT1);
        setForeground(Color.yellow);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public BabyBtn(String iconpath, int type) {
        super(iconpath, type);
        // TODO Auto-generated constructor stub
    }

    public BabyBtn(String iconpath, int type, Color[] colors, Font font, String text) {
        super(iconpath, type, colors);
        // TODO Auto-generated constructor stub
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        Baby baby = UserMessUntil.getbaby(TestChildJframe.getTestChildJframe().getTestChildJpanel().getBabyid());
        if (baby == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的孩子");
            return;
        }
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (getText().equals("跟随")) {
            setText("取消");
        } else if (getText().equals("取消")) {
            setText("跟随");
        } else if (getText().equals("待机")) {
            loginResult.setBabyId(null);
            RoleProperty.ResetBaby(null);
            setText("出战");
            String mes = Agreement.getAgreement().rolechangeAgreement("B");
            SendMessageUntil.toServer(mes);
        } else if (getText().equals("出战")) {
            RoleProperty.ResetBaby(baby);
            loginResult.setBabyId(baby.getBabyid());
            setText("待机");
            String mes = Agreement.getAgreement().rolechangeAgreement("B" + baby.getBabyid());
            SendMessageUntil.toServer(mes);
        }
    }
}