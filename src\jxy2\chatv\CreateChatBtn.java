package jxy2.chatv;

import com.tool.btn.MoBanBtn;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class CreateChatBtn extends MoBanBtn {
    public CreateChatJPanel createChatJPanel;
    public CreateChatBtn(String iconpath, int type, Color[] colors, Font font, String text, CreateChatJPanel createChatJPanel, int index, String prompt) {
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.createChatJPanel = createChatJPanel;
        this.index = index;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        //TODO 数据写入本地硬盘中~！ Lhd
        ChatFrame.getChatJPanel().updateButtons(createChatJPanel.stringList);
    }
}
