package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SuitBaptizeJpanel;
import org.come.Jpanel.WashJpanel;
import org.come.bean.QualityClBean;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;
import org.come.until.UserData;

import com.tool.role.RoleData;

public class BaptizeBtn extends MoBanBtn {

    private int caozuo;
    private static String newEx;
    private SuitBaptizeJpanel suitBaptizeJpanel;

    public BaptizeBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo,
            SuitBaptizeJpanel suitBaptizeJpanel) {
        super(iconpath, type, colors);
        this.caozuo = caozuo;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.suitBaptizeJpanel = suitBaptizeJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 1) {// 开始洗炼
            if (suitBaptizeJpanel.getBaptizeBtn1().getText().equals("开始洗炼")) {// 开始洗炼
                suitBaptizeJpanel.getBaptizeBtn1().setText("再次洗炼");
            }
            long money = 100000;
            int index = 2;
            // 判断是否要保存原有属性
            if (SuitBaptizeJpanel.saveOld) {// 要保存
                money = 100000;
                index = 2;
                suitBaptizeJpanel.getBaptizeBtn2().setBtn(1);
                suitBaptizeJpanel.getBaptizeBtn3().setBtn(1);
            } else {// 不保存
                money = 80000;
                index = 1;
                suitBaptizeJpanel.getBaptizeBtn2().setBtn(-1);
                suitBaptizeJpanel.getBaptizeBtn3().setBtn(-1);
            }
            if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money) {
                ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
                return;
            }
            if (RoleData.getRoleData().getLoginResult().getScoretype("灵修值").longValue() < 30) {
                ZhuFrame.getZhuJpanel().addPrompt("灵修值不足30点..");
                return;
            }
            SuitOperBean operBean = new SuitOperBean();
            List<BigDecimal> goods = new ArrayList<>();
            goods.add(WashJpanel.getGoodstableBean().getGoodstable().getRgid());
            operBean.setType(index);
            operBean.setGoods(goods);
            // 发送消息给服务器
            String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
            SendMessageUntil.toServer(senmes);
            // 消耗金钱
            RoleData.getRoleData().getLoginResult()
                    .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(new BigDecimal(money)));
            // 消耗灵修值
            RoleData.getRoleData().getLoginResult()
                    .setScore(UserData.Splice(RoleData.getRoleData().getLoginResult().getScore(), "灵修值=30", 3));
            ZhuFrame.getZhuJpanel().addPrompt("消耗了" + money + "金币..");
            ZhuFrame.getZhuJpanel().addPrompt("消耗了" + 30 + "点灵修值..");
        } else if (caozuo == 2) {// 保留属性
            FormsManagement.HideForm(74);
        } else if (caozuo == 3) {// 替换属性
            Goodstable goodstable = WashJpanel.getGoodstableBean().getGoodstable();
            if (goodstable == null)
                return;
            QualityClBean clBean = new QualityClBean();
            clBean.setRgid(goodstable.getRgid());
            clBean.setType(4);
            // 发送消息给服务器
            String senmes = Agreement.extrAttrOperAgreement(GsonUtil.getGsonUtil().getgson().toJson(clBean));
            SendMessageUntil.toServer(senmes);
            // 替换属性
            for (int i = 0; i < 4; i++) {
                suitBaptizeJpanel.getOldAttr()[i].setText(suitBaptizeJpanel.getNewAttr()[i].getText());
                suitBaptizeJpanel.getNewAttr()[i].setText("");
            }
            String[] ss = goodstable.getValue().split("\\|");
            if (newEx != null && newEx != "") {
                // 替换属性
                String value = newExtra(ss, 3, newEx);
                goodstable.setValue(value);
            }
        }
    }

    public static String[] Extras = new String[] { "炼化属性", "炼器属性", "神兵属性", "套装属性", "宝石属性", "觉醒技", "五行属性" };

    /** 刷新额外属性存在覆盖 没有生成 */
    public static String newExtra(String[] v, int type, String newEx) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < v.length; i++) {
            if (v[i].startsWith(Extras[type])) {
                if (newEx != null && !newEx.equals("")) {
                    if (i != 0) {
                        buffer.append("|");
                    }
                    buffer.append(newEx);
                    newEx = null;
                }
            } else {
                if (i != 0) {
                    buffer.append("|");
                }
                buffer.append(v[i]);
            }
        }
        if (newEx != null) {
            buffer.append("|");
            buffer.append(newEx);
        }
        return buffer.toString();
    }

    public static String getNewEx() {
        return newEx;
    }

    public static void setNewEx(String newEx) {
        BaptizeBtn.newEx = newEx;
    }
}
