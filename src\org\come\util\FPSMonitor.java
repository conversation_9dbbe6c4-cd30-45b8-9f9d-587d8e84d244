package org.come.util;

import com.tool.tcpimg.UIUtils;
import org.come.Jpanel.TeststateJpanel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 帧数监控器 - 检测游戏渲染帧数和性能问题
 */
public class FPSMonitor extends JPanel {
    
    private final AtomicInteger frameCount = new AtomicInteger(0);
    private final AtomicInteger currentFPS = new AtomicInteger(0);
    private final AtomicLong lastUpdateTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong lastFrameTime = new AtomicLong(System.currentTimeMillis());
    
    private Timer updateTimer;
    private JLabel fpsLabel;

    
    // 性能监控
    private int lowFPSCount = 0;
    private int zeroFPSCount = 0;
    private boolean isGameActive = true;
    
    public FPSMonitor() {
        initComponents();
        startMonitoring();
    }
    
    private void initComponents() {
        setLayout(new FlowLayout(FlowLayout.LEFT, 5, 2));
        setOpaque(false);
        setPreferredSize(new Dimension(200, 25));
        
        // FPS显示标签
        fpsLabel = TeststateJpanel.GJpanelText(Color.GREEN, UIUtils.TEXT_FONT15);
        fpsLabel.setBounds(5, 5, 200, 20);
        fpsLabel.setText("FPS: 0");
        add(fpsLabel);

    }

    @Override
    protected void printComponent(Graphics g) {
        super.printComponent(g);
        fpsLabel.setBounds(5, 5, 200, 20);
    }

    private void startMonitoring() {
        // 每秒更新一次FPS显示
        updateTimer = new Timer(1000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                updateFPSDisplay();
            }
        });
        updateTimer.start();
    }
    
    /**
     * 记录一帧渲染完成
     */
    public void recordFrame() {
        long currentTime = System.currentTimeMillis();
        lastFrameTime.set(currentTime);
        frameCount.incrementAndGet();
    }
    
    /**
     * 设置游戏活跃状态
     */
    public void setGameActive(boolean active) {
        this.isGameActive = active;

    }
    
    private void updateFPSDisplay() {
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastUpdateTime.get();
        
        if (timeDiff >= 1000) {
            int frames = frameCount.getAndSet(0);
            int fps = (int) (frames * 1000.0 / timeDiff);
            currentFPS.set(fps);
            lastUpdateTime.set(currentTime);
            
            // 检测性能问题
            checkPerformanceIssues(fps);
            
            // 更新显示
            SwingUtilities.invokeLater(() -> {
                updateDisplay(fps);
            });
        }
    }
    
    private void checkPerformanceIssues(int fps) {
        if (fps == 0) {
            zeroFPSCount++;

            // 只有在游戏活跃状态下才报告零帧数问题
            if (isGameActive) {
                if (zeroFPSCount >= 3) {
//                    System.err.println("[FPS警告] 游戏活跃时检测到连续" + zeroFPSCount + "秒帧数为0，可能存在渲染阻塞！");
                    if (zeroFPSCount >= 8) { // 增加阈值，避免误报
//                        System.err.println("[FPS严重] 游戏活跃时帧数长时间为0，建议检查渲染线程状态");
                        triggerRenderRecovery();
                    }
                }
            } else {
                // 游戏非活跃时，零帧数是正常的
                if (zeroFPSCount == 1) {
//                    System.out.println("[FPS信息] 游戏窗口失去焦点，渲染已暂停（正常行为）");
                }
            }
        } else {
            if (zeroFPSCount > 0) {
//                System.out.println("[FPS恢复] 帧数恢复正常: " + fps + " FPS");
            }
            zeroFPSCount = 0;
        }

        // 只在游戏活跃时检测低帧数
        if (isGameActive && fps < 10 && fps > 0) {
            lowFPSCount++;
            if (lowFPSCount >= 5) {
//                System.out.println("[FPS提示] 检测到低帧数：" + fps + " FPS");
            }
        } else {
            lowFPSCount = 0;
        }
    }
    
    private void updateDisplay(int fps) {
        // 获取目标FPS用于显示
        int targetFPS = getTargetFPS();
        // 根据游戏活跃状态显示不同信息
            fpsLabel.setText("FPS: " + fps+"                    ");

    }

    /**
     * 获取目标FPS（从DisplayInfo）
     */
    private int getTargetFPS() {
        try {
            return org.come.util.DisplayInfo.getInstance().getTargetFPS();
        } catch (Exception e) {
            return 60; // 默认值
        }
    }
    
    /**
     * 触发渲染恢复机制
     */
    private void triggerRenderRecovery() {
//        System.out.println("[FPS恢复] 尝试恢复渲染...");
        
        // 强制重绘
        SwingUtilities.invokeLater(() -> {
            Container parent = getParent();
            while (parent != null) {
                if (parent instanceof JFrame || parent instanceof JPanel) {
                    parent.repaint();
                    break;
                }
                parent = parent.getParent();
            }
        });
        
        // 强制垃圾回收
        System.gc();
        
        // 重置计数器
        zeroFPSCount = 0;
        frameCount.set(0);
    }
    
    /**
     * 获取当前FPS
     */
    public int getCurrentFPS() {
        return currentFPS.get();
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (updateTimer != null) {
            updateTimer.stop();
        }
    }
    
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        // 半透明背景
        Graphics2D g2d = (Graphics2D) g.create();
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.7f));
        g2d.setColor(Color.BLACK);
        g2d.fillRoundRect(0, 0, getWidth(), getHeight(), 5, 5);
        g2d.dispose();
    }
}
