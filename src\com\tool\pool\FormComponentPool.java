package com.tool.pool;

import javax.swing.*;
import java.awt.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 窗口组件对象池 - 用于复用窗口组件，减少FormsManagement中的内存堆积
 */
public class FormComponentPool {
    
    // 组件池 - 按类型分类
    private static final ConcurrentHashMap<Class<?>, ConcurrentLinkedQueue<Component>> componentPools = new ConcurrentHashMap<>();
    private static final int MAX_POOL_SIZE_PER_TYPE = 10; // 每种类型最多缓存10个
    
    // 窗口池
    private static final ConcurrentLinkedQueue<JFrame> framePool = new ConcurrentLinkedQueue<>();
    private static final ConcurrentLinkedQueue<JDialog> dialogPool = new ConcurrentLinkedQueue<>();
    private static final ConcurrentLinkedQueue<JPanel> panelPool = new ConcurrentLinkedQueue<>();
    
    // 统计信息
    private static long totalAcquired = 0;
    private static long totalReused = 0;
    private static long totalCreated = 0;
    
    /**
     * 获取组件
     */
    @SuppressWarnings("unchecked")
    public static <T extends Component> T acquireComponent(Class<T> componentType) {
        totalAcquired++;
        
        ConcurrentLinkedQueue<Component> pool = componentPools.get(componentType);
        if (pool != null) {
            Component component = pool.poll();
            if (component != null) {
                totalReused++;
                // 重置组件状态
                resetComponent(component);
                return (T) component;
            }
        }
        
        // 创建新组件
        totalCreated++;
        return createNewComponent(componentType);
    }
    
    /**
     * 归还组件
     */
    public static void releaseComponent(Component component) {
        if (component == null) return;
        
        Class<?> componentType = component.getClass();
        ConcurrentLinkedQueue<Component> pool = componentPools.computeIfAbsent(componentType, 
            k -> new ConcurrentLinkedQueue<>());
        
        if (pool.size() < MAX_POOL_SIZE_PER_TYPE) {
            // 清理组件状态
            cleanupComponent(component);
            pool.offer(component);
        } else {
            // 池已满，直接清理组件
            disposeComponent(component);
        }
    }
    
    /**
     * 获取JFrame
     */
    public static JFrame acquireFrame() {
        totalAcquired++;
        
        JFrame frame = framePool.poll();
        if (frame != null) {
            totalReused++;
            resetFrame(frame);
            return frame;
        } else {
            totalCreated++;
            return new JFrame();
        }
    }
    
    /**
     * 归还JFrame
     */
    public static void releaseFrame(JFrame frame) {
        if (frame == null) return;
        
        if (framePool.size() < MAX_POOL_SIZE_PER_TYPE) {
            cleanupFrame(frame);
            framePool.offer(frame);
        } else {
            frame.dispose();
        }
    }
    
    /**
     * 获取JDialog
     */
    public static JDialog acquireDialog() {
        totalAcquired++;
        
        JDialog dialog = dialogPool.poll();
        if (dialog != null) {
            totalReused++;
            resetDialog(dialog);
            return dialog;
        } else {
            totalCreated++;
            return new JDialog();
        }
    }
    
    /**
     * 归还JDialog
     */
    public static void releaseDialog(JDialog dialog) {
        if (dialog == null) return;
        
        if (dialogPool.size() < MAX_POOL_SIZE_PER_TYPE) {
            cleanupDialog(dialog);
            dialogPool.offer(dialog);
        } else {
            dialog.dispose();
        }
    }
    
    /**
     * 获取JPanel
     */
    public static JPanel acquirePanel() {
        totalAcquired++;
        
        JPanel panel = panelPool.poll();
        if (panel != null) {
            totalReused++;
            resetPanel(panel);
            return panel;
        } else {
            totalCreated++;
            return new JPanel();
        }
    }
    
    /**
     * 归还JPanel
     */
    public static void releasePanel(JPanel panel) {
        if (panel == null) return;
        
        if (panelPool.size() < MAX_POOL_SIZE_PER_TYPE) {
            cleanupPanel(panel);
            panelPool.offer(panel);
        }
    }
    
    /**
     * 创建新组件
     */
    @SuppressWarnings("unchecked")
    private static <T extends Component> T createNewComponent(Class<T> componentType) {
        try {
            return componentType.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("无法创建组件: " + componentType.getName(), e);
        }
    }
    
    /**
     * 重置组件状态
     */
    private static void resetComponent(Component component) {
        if (component instanceof Container) {
            ((Container) component).removeAll();
        }
        component.setVisible(false);
        component.setEnabled(true);
    }
    
    /**
     * 清理组件状态
     */
    private static void cleanupComponent(Component component) {
        if (component instanceof Container) {
            Container container = (Container) component;
            // 递归清理子组件
            for (Component child : container.getComponents()) {
                cleanupComponent(child);
            }
            container.removeAll();
        }
        
        // 移除监听器
        if (component instanceof JComponent) {
            JComponent jComponent = (JComponent) component;
            jComponent.removeAll();
            // 清理边框和工具提示
            jComponent.setBorder(null);
            jComponent.setToolTipText(null);
        }
        
        component.setVisible(false);
        component.setEnabled(true);
    }
    
    /**
     * 销毁组件
     */
    private static void disposeComponent(Component component) {
        cleanupComponent(component);
        if (component instanceof Window) {
            ((Window) component).dispose();
        }
    }
    
    /**
     * 重置JFrame
     */
    private static void resetFrame(JFrame frame) {
        frame.getContentPane().removeAll();
        frame.setTitle("");
        frame.setVisible(false);
        frame.setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        frame.setSize(400, 300);
    }
    
    /**
     * 清理JFrame
     */
    private static void cleanupFrame(JFrame frame) {
        frame.getContentPane().removeAll();
        frame.setVisible(false);
        frame.setTitle("");
        frame.setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        // 移除所有监听器
        frame.removeWindowListener(null);
    }
    
    /**
     * 重置JDialog
     */
    private static void resetDialog(JDialog dialog) {
        dialog.getContentPane().removeAll();
        dialog.setTitle("");
        dialog.setVisible(false);
        dialog.setModal(false);
        dialog.setSize(300, 200);
    }
    
    /**
     * 清理JDialog
     */
    private static void cleanupDialog(JDialog dialog) {
        dialog.getContentPane().removeAll();
        dialog.setVisible(false);
        dialog.setTitle("");
        dialog.setModal(false);
    }
    
    /**
     * 重置JPanel
     */
    private static void resetPanel(JPanel panel) {
        panel.removeAll();
        panel.setVisible(true);
        panel.setLayout(new FlowLayout());
    }
    
    /**
     * 清理JPanel
     */
    private static void cleanupPanel(JPanel panel) {
        panel.removeAll();
        panel.setVisible(false);
        panel.setBorder(null);
    }
    
    /**
     * 清空所有对象池
     */
    public static void clearAll() {
        // 清理组件池
        for (ConcurrentLinkedQueue<Component> pool : componentPools.values()) {
            Component component;
            while ((component = pool.poll()) != null) {
                disposeComponent(component);
            }
        }
        componentPools.clear();
        
        // 清理窗口池
        JFrame frame;
        while ((frame = framePool.poll()) != null) {
            frame.dispose();
        }
        
        JDialog dialog;
        while ((dialog = dialogPool.poll()) != null) {
            dialog.dispose();
        }
        
        panelPool.clear();
    }
    
    /**
     * 获取对象池统计信息
     */
    public static String getPoolStats() {
        double reuseRate = totalAcquired > 0 ? (double) totalReused / totalAcquired * 100 : 0;
        int totalPoolSize = framePool.size() + dialogPool.size() + panelPool.size();
        for (ConcurrentLinkedQueue<Component> pool : componentPools.values()) {
            totalPoolSize += pool.size();
        }
        
        return String.format("窗口组件池统计: 池大小=%d, 总获取=%d, 复用=%d, 新建=%d, 复用率=%.1f%%", 
                           totalPoolSize, totalAcquired, totalReused, totalCreated, reuseRate);
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStats() {
        totalAcquired = 0;
        totalReused = 0;
        totalCreated = 0;
    }
}
