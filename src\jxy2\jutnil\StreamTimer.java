package jxy2.jutnil;

import com.tool.tcp.Sprite;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;

// 定义一个名为StreamTimer的类
public class StreamTimer {
    // 声明一个名为time的长整型变量
    public long time;
    // 声明一个名为inputStream的输入流变量
    public InputStream inputStream;
    // 声明一个名为fileInputStream的文件输入流变量
    public FileInputStream fileInputStream;
    public BufferedImage bufferedImage;
    public Sprite Sprite;
    public ByteArrayOutputStream byteArrayOutputStream;
    public byte[] bytes;

    // 构造方法，接受时间和输入流作为参数
    public StreamTimer(long time, InputStream inputStream) {
        this.time = time;
        this.inputStream = inputStream;
    }
    public StreamTimer(long time, Sprite Sprite) {
        this.time = time;
        this.Sprite = Sprite;
    }
    public StreamTimer(long time, byte[] bytes) {
        this.time = time;
        this.bytes = bytes;
    }

    // 构造方法，接受时间和文件输入流作为参数
    public StreamTimer(long time, FileInputStream fileInputStream) {
        this.time = time;
        this.fileInputStream = fileInputStream;
    }
    // 构造方法，接受时间和文件输入流作为参数
    public StreamTimer(long time, BufferedImage bufferedImage) {
        this.time = time;
        this.bufferedImage = bufferedImage;
    }

    public com.tool.tcp.Sprite getSprite() {
        return Sprite;
    }

    public void setSprite(com.tool.tcp.Sprite sprite) {
        Sprite = sprite;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public FileInputStream getFileInputStream() {
        return fileInputStream;
    }

    public void setFileInputStream(FileInputStream fileInputStream) {
        this.fileInputStream = fileInputStream;
    }

    public BufferedImage getBufferedImage() {
        return bufferedImage;
    }

    public void setBufferedImage(BufferedImage bufferedImage) {
        this.bufferedImage = bufferedImage;
    }

    public ByteArrayOutputStream getByteArrayOutputStream() {
        return byteArrayOutputStream;
    }

    public void setByteArrayOutputStream(ByteArrayOutputStream byteArrayOutputStream) {
        this.byteArrayOutputStream = byteArrayOutputStream;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }
}
