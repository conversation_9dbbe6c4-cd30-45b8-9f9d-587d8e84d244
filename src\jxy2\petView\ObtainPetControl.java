package jxy2.petView;

import org.come.action.FromServerAction;
import org.come.entity.RoleSummoning;
import org.come.until.GsonUtil;

/**
* 获取返回的宠物信息
* <AUTHOR>
* @date 2024/6/10 上午7:57
*/
public class ObtainPetControl  implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        RoleSummoning roleSummoning = GsonUtil.getGsonUtil().getgson().fromJson(mes, RoleSummoning.class);
        if (roleSummoning!=null) {
//                TestPetJframe.getTestPetJframe().getPetMainJPanel().getCardJPanel().getGuideJPanel().getRoles().add(roleSummoning);
//                TestPetJframe.getTestPetJframe().getPetMainJPanel().getCardJPanel().getGuideJPanel().refreshPedia(0);
        }
    }
}
