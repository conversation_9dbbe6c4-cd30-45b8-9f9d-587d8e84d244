package come.tool.FightingEffect;

import java.util.ArrayList;
import java.util.List;

import org.come.bean.PathPoint;
import org.come.until.ScrenceUntil;

import come.tool.Fighting.FightingMixDeal;
import come.tool.Fighting.FightingState;
import come.tool.Fighting.StateProgress;

public class MoveEffect implements Effect{

	@Override
	public StateProgress analysisAction(FightingState State,int path) {
		// TODO Auto-generated method stub
		 StateProgress progress=EffectType.getProgress(State, path);
		 List<PathPoint> points=new ArrayList<>(); 
		 if (!State.getStartState().equals("瞬移")) {
		     points.add(new PathPoint(FightingMixDeal.CurrentData.get(path).getX(),FightingMixDeal.CurrentData.get(path).getY()));	 	
		 }
		 points.add(move(State.getEndState()));
		 progress.setPath(points);						   	
		 progress.setMan(path);
		 progress.setType(10);//移动
		 return progress;
	}
	/**
	 *  7 0 1
	 *  6   2
	 *  5 4 3
	 * */
	/**获取移动的位置*/
	public static PathPoint move(String v) {
		String[] vs = v.split("\\|");
		int camp = Integer.parseInt(vs[0]);
		int man = Integer.parseInt(vs[1]);
		PathPoint point=Position(camp, FightingMixDeal.camp, man);
		if (vs.length == 3) {
			int dir = Integer.parseInt(vs[2]);
			dir = EffectType.getdir(dir,camp);
			switch (dir) {
			case 1:
				point.setX(point.getX() + 70);
				point.setY(point.getY() - 40);
				break;
			case 3:
				point.setX(point.getX() + 70);
				point.setY(point.getY() + 30);
				break;
			case 5:
				point.setX(point.getX() - 70);
				point.setY(point.getY() + 40);
				break;
			default:
				point.setX(point.getX() - 70);
				point.setY(point.getY() - 30);
				break;
			}
		}
		return point;
	}
	/**输入敌我 和位置获取 对应坐标*/
	public static PathPoint Position(int Camp, int my_Camp, int man) {
		// 判断敌我
		if ((Camp == 1 && my_Camp == -1)|| Camp == my_Camp) {// 我方
			if (man < 5) {
				return new PathPoint((ScrenceUntil.Screen_x - 70) - 65 * man,(ScrenceUntil.Screen_y - 325) + 65 * man);
			} else {
				return new PathPoint((ScrenceUntil.Screen_x - 165) - 65* (man - 5),(ScrenceUntil.Screen_y - 390) + 65* (man - 5));
			}
		} else {// 敌方
			if (man < 5) {
				return new PathPoint(360 - 65 * man, 150 + 65 * man);
			} else {
				return new PathPoint(455 - 65 * (man - 5), 215 + 65 * (man - 5));
			}
		}
	}
//	public static PathPoint move(String v){
//		String[] vs=v.split("\\|");
//		int path=FightingMixDeal.CurrentData(Integer.parseInt(vs[0]),Integer.parseInt(vs[1]));
//		Fightingimage image=FightingMixDeal.CurrentData.get(path);
//		PathPoint point=image.Position(image.getFightingManData().getCamp(), FightingMixDeal.camp, image.getFightingManData().getMan());
//		if (vs.length==3) {
//			int dir=Integer.parseInt(vs[2]);
//			dir=EffectType.getdir(dir,image.getFightingManData().getCamp());	
//			switch (dir) {
//			case 1:
//				point.setX(point.getX()+70);
//				point.setY(point.getY()-40);
//				break;
//			case 3:
//				point.setX(point.getX()+70);
//				point.setY(point.getY()+30);
//				break;
//			case 5:
//				point.setX(point.getX()-70);
//				point.setY(point.getY()+40);
//				break;
//			default:
//				point.setX(point.getX()-70);
//				point.setY(point.getY()-30);
//				break;
//			}
//		}
//		return point;
//		
//	}
	
}
