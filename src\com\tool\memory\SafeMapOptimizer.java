package com.tool.memory;

import java.awt.Image;
import java.lang.ref.WeakReference;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 安全地图优化器 - 不影响地图显示的内存优化
 */
public class SafeMapOptimizer {
    
    // 地图相关的临时对象缓存
    private static final ConcurrentHashMap<String, WeakReference<Object>> tempObjectCache = new ConcurrentHashMap<>();
    
    // 地图加载统计
    private static long totalMapLoads = 0;
    private static long totalMemoryFreed = 0;
    
    /**
     * 地图加载前的安全内存优化
     */
    public static void optimizeBeforeMapLoad(int mapId) {
        totalMapLoads++;
        System.out.println("[安全地图优化] 地图加载前优化: " + mapId);
        
        // 1. 清理临时对象缓存
        cleanupTempObjects();
        
        // 2. 清理图像缓存中的失效引用
        cleanupImageReferences();
        
        // 3. 建议垃圾回收（如果内存使用较高）
        if (isMemoryPressureHigh()) {
            System.out.println("[安全地图优化] 内存压力较高，执行垃圾回收");
            System.gc();
        }
    }
    
    /**
     * 地图加载后的内存监控
     */
    public static void monitorAfterMapLoad(int mapId, long memoryBefore) {
        long memoryAfter = getCurrentMemory();
        long memoryIncrease = memoryAfter - memoryBefore;
        
        System.out.println(String.format("[安全地图优化] 地图 %d 加载完成，内存变化: %+dKB", 
                         mapId, memoryIncrease / 1024));
        
        // 如果内存增长过多，记录警告
        if (memoryIncrease > 10 * 1024 * 1024) { // 超过10MB
            System.out.println("[安全地图优化] ⚠️ 警告：地图加载内存增长过多: " + memoryIncrease / 1024 / 1024 + "MB");
            
            // 执行清理
            performPostLoadCleanup();
        }
    }
    
    /**
     * 清理临时对象
     */
    private static void cleanupTempObjects() {
        int beforeSize = tempObjectCache.size();
        
        // 清理失效的WeakReference
        tempObjectCache.entrySet().removeIf(entry -> entry.getValue().get() == null);
        
        int afterSize = tempObjectCache.size();
        if (beforeSize > afterSize) {
            System.out.println("[安全地图优化] 清理临时对象: " + (beforeSize - afterSize) + " 个");
        }
    }
    
    /**
     * 清理图像引用
     */
    private static void cleanupImageReferences() {
        // 这里可以清理一些不重要的图像缓存
        // 但要小心不要清理正在使用的图像
        System.out.println("[安全地图优化] 清理图像引用");
        
        // 建议系统清理未使用的图像
        // System.runFinalization(); // 已过时，移除
    }
    
    /**
     * 检查内存压力
     */
    private static boolean isMemoryPressureHigh() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double usagePercent = (double) usedMemory / maxMemory * 100;
        return usagePercent > 70.0; // 使用率超过70%认为压力较高
    }
    
    /**
     * 获取当前内存使用
     */
    private static long getCurrentMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * 地图加载后清理
     */
    private static void performPostLoadCleanup() {
        long beforeCleanup = getCurrentMemory();
        
        System.out.println("[安全地图优化] 执行地图加载后清理...");
        
        // 清理临时对象
        cleanupTempObjects();
        
        // 清理系统缓存
        System.gc();
        // System.runFinalization(); // 已过时，移除
        
        // 等待清理完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterCleanup = getCurrentMemory();
        long memoryFreed = beforeCleanup - afterCleanup;
        
        if (memoryFreed > 0) {
            totalMemoryFreed += memoryFreed;
            System.out.println("[安全地图优化] 清理释放内存: " + memoryFreed / 1024 + "KB");
        }
    }
    
    /**
     * 云朵缓存优化
     */
    public static int optimizeCloudsCache(java.util.Map<?, ?> cloudsMap) {
        if (cloudsMap == null) return 0;
        
        int cloudCount = cloudsMap.size();
        if (cloudCount > 0) {
            cloudsMap.clear();
            System.out.println("[安全地图优化] 清理云朵缓存: " + cloudCount + " 个");
        }
        return cloudCount;
    }
    
    /**
     * NPC加载优化
     */
    public static void optimizeNpcLoading() {
        System.out.println("[安全地图优化] NPC加载优化");
        
        // 清理NPC相关的临时对象
        cleanupTempObjects();
        
        // 如果内存压力高，执行清理
        if (isMemoryPressureHigh()) {
            System.out.println("[安全地图优化] NPC加载前内存清理");
            System.gc();
        }
    }
    
    /**
     * 小地图优化
     */
    public static void optimizeSmallMap() {
        System.out.println("[安全地图优化] 小地图优化");
        
        // 清理小地图相关的临时数据
        cleanupImageReferences();
    }
    
    /**
     * 获取优化统计
     */
    public static String getOptimizationStats() {
        long totalMemoryFreedMB = totalMemoryFreed / 1024 / 1024;
        
        return String.format("安全地图优化统计: 总加载%d次, 已释放%dMB内存, 临时对象缓存%d个", 
                           totalMapLoads, totalMemoryFreedMB, tempObjectCache.size());
    }
    
    /**
     * 内存使用建议
     */
    public static String getMemoryAdvice() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double usagePercent = (double) usedMemory / maxMemory * 100;
        
        StringBuilder advice = new StringBuilder();
        advice.append("内存使用建议:\n");
        
        if (usagePercent > 80) {
            advice.append("🚨 内存使用率过高(").append(String.format("%.1f", usagePercent)).append("%)\n");
            advice.append("建议: 立即执行垃圾回收或重启游戏\n");
        } else if (usagePercent > 60) {
            advice.append("⚠️ 内存使用率较高(").append(String.format("%.1f", usagePercent)).append("%)\n");
            advice.append("建议: 考虑清理缓存或减少同时打开的窗口\n");
        } else {
            advice.append("✅ 内存使用率正常(").append(String.format("%.1f", usagePercent)).append("%)\n");
        }
        
        return advice.toString();
    }
    
    /**
     * 强制内存清理
     */
    public static long forceMemoryCleanup() {
        long beforeCleanup = getCurrentMemory();
        
        System.out.println("[安全地图优化] 开始强制内存清理...");
        
        // 清理所有临时对象
        tempObjectCache.clear();
        
        // 清理图像引用
        cleanupImageReferences();
        
        // 多次垃圾回收
        for (int i = 0; i < 3; i++) {
            System.gc();
            // System.runFinalization(); // 已过时，移除
            
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        long afterCleanup = getCurrentMemory();
        long memoryFreed = beforeCleanup - afterCleanup;
        
        totalMemoryFreed += memoryFreed;
        System.out.println("[安全地图优化] 强制清理完成，释放: " + memoryFreed / 1024 / 1024 + "MB");
        
        return memoryFreed;
    }
    
    /**
     * 重置统计
     */
    public static void resetStats() {
        totalMapLoads = 0;
        totalMemoryFreed = 0;
        tempObjectCache.clear();
    }
}
