package jxy2.wdfDome.bean;

import jxy2.wdfDome.util.DataTool;
import jxy2.wdfDome.view.MainFrame;
import jxy2.wdfDome.util.WasTool;

import java.awt.image.BufferedImage;
import java.io.File;

public class MainItem implements Runnable, MainInterface {
    private MainFrame jf;
    private File file;
    private WasData wasData;

    public MainItem(MainFrame jf, File file, WasData wasData) {
        this.jf = jf;
        this.file = file;
        this.wasData = wasData;
    }
    @Override
    public void run() {
        this.execute();
    }

    @Override
    public void execute() {
        WasHead wasHead = WasTool.getWasHead(this.file, this.wasData.getFileOffset());
        if (wasHead != null) {
            BufferedImage[] bufimages = WasTool.was2Images(wasHead);
            this.jf.addItemPane(DataTool.ten2six(String.valueOf(this.wasData.getId())), bufimages);
        }
    }
}
