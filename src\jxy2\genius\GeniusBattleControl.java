package jxy2.genius;

import org.come.action.FromServerAction;
import org.come.until.GsonUtil;

public class GeniusBattleControl implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        GeniusBean result = GsonUtil.getGsonUtil().getgson().fromJson(mes,GeniusBean.class);
        GeniusBattleJPanel geniusBattleJPanel = GeniusBattleFrame.getGeniusBattleFrame().getGeniusBattleJPanel();
        geniusBattleJPanel.loadSignData(result);

    }
}
