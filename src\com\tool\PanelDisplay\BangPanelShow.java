package com.tool.PanelDisplay;

import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;

import com.tool.image.ImageMixDeal;

public class BangPanelShow {
	public static void Show() {
		// TODO Auto-generated method stub
		if (ImageMixDeal.userimg.getRoleShow().getGang_id() == null || ImageMixDeal.userimg.getRoleShow().getGang_id().intValue() == 0) {
			ZhuFrame.getZhuJpanel().addPrompt2("你没有帮派!");
			return;
		}
		if (FormsManagement.getframe(5).isVisible()) {
			FormsManagement.HideForm(5);
			return;
		}
		String senmes = Agreement.getAgreement().IntogangAgreement(ImageMixDeal.userimg.getRoleShow().getGang_id().toString());
		// 向服务器发送信息
		SendMessageUntil.toServer(senmes);
	}
}
