package com.tool.time;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import com.tool.role.RoleProperty;
import come.tool.Fighting.SkillTx;
import org.come.Frame.TaobaoCourtMainJframe;
import org.come.Frame.ZhuFrame;
import org.come.bean.PrivateData;
import org.come.bean.RoleShow;
import org.come.bean.RoleTxBean;
import org.come.model.Title;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.ScrenceUntil;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 时效
 * 
 * <AUTHOR>
 * 
 * 
 */
public class TimeLimit {
	public static TimeLimit timeLimit;

	public List<Limit> limits = new ArrayList<>();
	public static TimeLimit getLimits(){
		if (timeLimit == null)
			initial();
		return timeLimit;
	}

	/**
	 * 初始化时效类
	 */
	public static void initial() {
		timeLimit = new TimeLimit();
		PrivateData data=RoleData.getRoleData().getPrivateData();
		String v = data.getTimingGood();
		if (v == null || v.equals(""))
			return;
		String[] vs = v.split("\\^");
		for (int i = 0; i < vs.length; i++) {
			String[] vss = vs[i].split("#");
			Limit limit = new Limit();
			for (int j = 0; j < vss.length; j++) {
				if (j == 0){
					limit.setName(vss[j]);
				}else if (j == 1){
					limit.setType(vss[j]);
				}else if (j == 2){
					limit.setSkin(vss[j]);
				}else if (j == 3){
					long time=Long.parseLong(vss[j]);
					if (time<100000000) {
						time*=60000;
					}else if (time<2000000000) {
						time*=1000;
					}
					limit.setTime(time);
				}else if (j == 4){
					limit.setValue(vss[j]);
				}
			}
			JLabel jLabel = getTimeJlabel();
			ImageIcon icon = GoodsListFromServerUntil.imgpathAdaptive(limit.getSkin(),20,20);
			jLabel.addMouseListener(new TimeMouslisten(limit));
			jLabel.setIcon(icon);
			limit.setjLabel(jLabel);
			ZhuFrame.getZhuJpanel().add(jLabel);
			timeLimit.limits.add(limit);
		}
		timeLimit.Sorting();
	}


	/**添加*/
	public void addLimit(String name, String type, String skin, String value,long time) {
		Limit limit = getLimit(type);
		if (time==-1) {
			removeLimit(limit);
			return;
		}
		if (limit != null) {
			limit.setName(name);
			limit.setTime(time);
			limit.setValue(value);
			if (!limit.getSkin().equals(skin)) {
				limit.setSkin(skin);
				ImageIcon icon = GoodsListFromServerUntil.imgpathAdaptive(limit.getSkin(),20,20);
				limit.getjLabel().setIcon(icon);	
			}
		} else {
			limit = new Limit(name,type,skin,time,value);
			JLabel jLabel = getTimeJlabel();
			ImageIcon icon = GoodsListFromServerUntil.imgpathAdaptive(limit.getSkin(),20,20);
			jLabel.setIcon(icon);
			jLabel.addMouseListener(new TimeMouslisten(limit));
			limit.setjLabel(jLabel);
			ZhuFrame.getZhuJpanel().add(jLabel);
			timeLimit.limits.add(limit);
		}
		/**刷新月卡剩余时间*/
		if(type.equals("VIP")){
			TaobaoCourtMainJframe.getTaobaoCourtJframe().getTaobaoCourtMainJpanel().getTaobaoCourtCardJpanel().getMonthlyCardJpanel().changeTime();
		}
		timeLimit.Sorting();
		if (limit.getType().equals("变身卡")||limit.getType().equals("强法型")||limit.getType().equals("加抗型")||limit.getType().equals("增益型")
				||limit.getType().equals("VIP")||limit.getType().equals("帮派")||limit.getType().equals("单人竞技场")) {
			RoleProperty.ResetEw();
		}
		if (limit.getType().equals("变身卡")||limit.getType().equals("童卡")) {
			changeSkin();
		}
	}
	/**删除*/
	public void removeLimit(Limit limit) {
		if (limit == null){
			return;
		}
		//名称#类型#皮肤#过期时间(时间戳/60000)#作用描述^名称#类型#皮肤#过期时间(时间戳/60000)#作用描述
		ZhuFrame.getZhuJpanel().addPrompt2(limit.getName() + "已经失去效果");
		ZhuFrame.getZhuJpanel().remove(limit.getjLabel());
		limits.remove(limit);
		if (limit.getType().equals("变身卡")||limit.getType().equals("强法型")||limit.getType().equals("加抗型")||limit.getType().equals("增益型")||limit.getType().equals("VIP")) {
			RoleProperty.ResetEw();
		}
	    timeLimit.Sorting();
		if (limit.getType().equals("变身卡")||limit.getType().equals("童卡")) {
			changeSkin();
		}
	}
    public void changeSkin(){
    	RoleShow roleShow=ImageMixDeal.userimg.getRoleShow();
    	String skin=roleShow.getSkin();
		String skin2=getskin(getSkin(),RoleData.getRoleData().getPackRecord().getPutTX(),roleShow);
		if (skin==null) {skin="";}
		if (skin2==null) {skin2="";}

		if (!skin.equals(skin2)) {
			roleShow.setSkin(skin2.equals("")?null:skin2);
			RoleData.getRoleData().getLoginResult().setSkin(roleShow.getSkin());
		    ImageMixDeal.userimg.changeskin(null);		
		    GoodsListFromServerUntil.sendPackRecord(5,skin2);
		}
    }
	/**超时判断*/
	public static void Timeout() {
		if (timeLimit == null){
			return;
		}
		StringBuffer buffer=null;
		for (int i = timeLimit.limits.size() - 1; i >= 0; i--) {
			Limit limit=timeLimit.limits.get(i);
			if (limit.getTime()==0) {continue;}
			if (Util.getTime() > limit.getTime()){
				if (buffer==null) {
					buffer=new StringBuffer("T");
				}else {
					buffer.append("|");
				}
				buffer.append(limit.getType());
				timeLimit.removeLimit(limit);
			}
		}
		if (buffer!=null) {
			String senmes = Agreement.getAgreement().usercardAgreement(buffer.toString());
			SendMessageUntil.toServer(senmes);
		}
	}
	/**获取魔个类型*/
	public Limit getLimit(String type) {
		for (int i = 0; i < limits.size(); i++) {
			if (limits.get(i).getType().equals(type)) {
				return limits.get(i);
			}
		}
		return null;
	}
    /**获取皮肤*/
	public String getSkin(){
		Limit limit=getLimit("童卡");
		if (limit==null) {
			limit=getLimit("变身卡");
		}
		if (limit!=null&&limit.getValue()!=null&&!limit.getValue().equals("")) {
			String[] vs = limit.getValue().split("\\|");
			for (int i = 0; i < vs.length; i++) {
				String[] vss = vs[i].split("=");
				if (vss[0].equals("皮肤")) {
					return vss[1];
				}
			}	
		}
		return null;		
	}
	/**图标排序*/
	public void Sorting() {
		for (int i = 0; i < limits.size(); i++) {
			limits.get(i).getjLabel().setBounds(ScrenceUntil.Screen_x - 22 - i * 25, 52, 22, 22);
		}
	}
    /**获取皮肤字符串*/
	public static String getskin(String skin,List<String> txs,RoleShow roleShow){
//		S:皮肤 X:特效 P:装饰品 J:足迹
//		S1231|X1230_1|P123_1|J12312
		StringBuffer buffer=new StringBuffer();
		if (skin!=null&&!skin.equals("")) {
			buffer.append("S");
			buffer.append(skin);
		}
//		else if (roleShow.getMount_id()>0) {
//			buffer.append("S");
//			buffer.append((roleShow.getMount_id()<<40)|roleShow.getSpecies_id().longValue());
//		}
		else if (GoodsListFromServerUntil.getChoseGoodsList()[0]!=null) {
//
//			long se=roleShow.getSpecies_id().longValue();
////			String[] id = GoodsListFromServerUntil.getChoseGoodsList()[0].getSkin().split("0x6B6B");
//			long w=good(Integer.parseInt(GoodsListFromServerUntil.getChoseGoodsList()[0].getSkin()));
//			if (w!=0) {
//				if (RoleProperty.getRoleProperty().getQhv()>=12) {
//					if ((w==1&&se==21005)||(w==1&&se==22009)||(w==2&&se==20009)) {w+=18;}
//				}
//				buffer.append("S");
//				buffer.append((w<<32)|se);
//			}

			buffer.append("S");
			buffer.append("81604401625");
		}
		if (txs!=null) {
			for (int i = 0; i < txs.size(); i++) {
				int id=Integer.parseInt(txs.get(i));
				RoleTxBean bean=UserMessUntil.getTxBean(id);
				if (bean!=null) {
					if (buffer.length()!=0) {buffer.append("|");}
//					/**类型（1特效2装饰品3足迹）*/
					if (bean.getRdType()==1) {
						buffer.append("X");
					}else if (bean.getRdType()==2) {
						buffer.append("P");
					}else {
						buffer.append("J");
					}
					buffer.append(bean.getRdId());
					if (bean.getRdType()==1||bean.getRdType()==2) {
						buffer.append("_");
						buffer.append(bean.getRdStatues()-bean.getRdType());
					}
				}
			}
		}
		if (roleShow.getTitle()!=null) {
			Title te=UserMessUntil.getTitle(roleShow.getTitle());
			if (te!=null&&te.getSkin()!=null) {
				if (buffer.length()!=0) {buffer.append("|");}
				buffer.append("C");
				buffer.append(te.getSkin());
			}
		}
		if (GoodsListFromServerUntil.getChoseGoodsList()[12]!=null) {
			if (buffer.length()!=0) {buffer.append("|");}
			buffer.append("B");
			buffer.append(GoodsListFromServerUntil.getChoseGoodsList()[12].getSkin());
		}
		return buffer.toString();
	}
	public static int good(int id){
		if ((id>=1600&&id<=1615)||id==6100||id==7006) {return 1;}
		if ((id>=1400&&id<=1415)||id==6106||id==7012) {return 2;}
		if ((id>=1100&&id<=1115)||id==6124||id==7021) {return 3;}
		if ((id>=1200&&id<=1215)||id==6122||id==7022) {return 4;}
		if ((id>=2200&&id<=2215)||id==6109||id==7016) {return 5;}
		if ((id>=2400&&id<=2415)||id==6119||id==7020) {return 6;}
		if ((id>=1300&&id<=1315)||id==6103||id==7009) {return 7;}
		if ((id>=1700&&id<=1715)||id==6102||id==7008) {return 8;}
		if ((id>=2100&&id<=2115)||id==7013||id==6105) {return 9;}
		if ((id>=1000&&id<=1015)||id==6118||id==7007) {return 10;}
		if (id==7019||id==6120) {return 11;}
		if ((id>=1800&&id<=1815)||id==6104||id==7011) {return 12;}
		if ((id>=1900&&id<=1915)||id==6108||id==7017) {return 13;}
		if ((id>=2200&&id<=2215)||id==6109||id==7010) {return 14;}
		if ((id>=1500&&id<=1515)||id==7014||id==6117) {return 15;}
		if ((id>=2000&&id<=2015)||id==6107||id==7015) {return 16;}
		if (id==7018||id==6121) {return 17;}
		if ((id>=2617&&id<=2632)||id==6125||id==7023) {return 18;}
		return 0;  
	}
	public int getlimit(Limit limit) {
		return limits.indexOf(limit);
	}
	private static Image image;
	/**获取JLabel*/
	public static JLabel getTimeJlabel(){
		if (image==null) {
			image=SkillTx.getImage();	
		}	
		JLabel jLabel = new JLabel(){
			@Override
			protected void paintComponent(Graphics g) {
				// TODO Auto-generated method stub
				g.drawImage(image, 0, 0, 22, 22, null);
				g.translate(1, 1);
				super.paintComponent(g);
				g.translate(-1,-1);
			}
		};
		return jLabel;
	}
}
