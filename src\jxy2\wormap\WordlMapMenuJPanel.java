package jxy2.wormap;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;

public class WordlMapMenuJPanel extends JPanel {
    public JLabel[] labmaps;
    public WordlMapMenuJPanel() {
        this.setPreferredSize(new Dimension(640, 485));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
    }

   public Image[] image;
    /**初始化地图ID以及序列号*/
    public void initMapID(int MapID) {
        if (labmaps!=null) {
            for (JLabel labmap : labmaps) {
                remove(labmap);
            }
        }
    int[] mapid = MapIDShow(MapID);
    if (mapid.length>7){return;}
            image = new Image[mapid.length];
            labmaps = new JLabel[mapid.length];
            for (int i = 0; i < image.length; i++) {
            image[i] = CutButtonImage.getWdfPng("0xAE1F"+mapid[i],"maps.wdf").getImage();
                labmaps[i] = new JLabel();
                labmaps[i].addMouseListener(new WordlMapMenuMouse(this,i,mapid[i],image[i]));
                add(labmaps[i]);
          }
    }

    private int[] MapIDShow(int MapID) {
        switch (MapID){
            case 46:return new int[]{1221, 1222, 1223, 1224, 1225, 1226,3311};
            case 18:return new int[]{1289, 1290, 1291, 1292, 1293, 1294, 1295};
            case 17:return new int[]{1282, 1283, 1284, 1285, 1286, 1287, 1288};
            case 50:return new int[]{1275, 1273, 1276, 1274};
            case 22:return new int[]{1268, 1269, 1270, 1271};
        }
        return new int[0];
    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK_24, 25, 30, 604, 436, 1);
        g.drawImage(Juitil.tz252.getImage(), 0, 0, 640, 485, null);
        for (int i = 0; i < image.length; i++) {
            int shop_x = i % (image.length /2);
            int shop_y = i / (image.length /2);
            int w = image[i].getWidth(null);
            int h = image[i].getHeight(null);
            g.drawImage(image[i], 35+shop_x*(w+8), 35+shop_y*h, w, h, null);
            Juitil.ImngBack(g, Juitil.tz253, 35+shop_x*(w+8), 35+shop_y*h, w, h, 1);
            labmaps[i].setBounds(35+shop_x*(w+8), 35+shop_y*h, w, h);
        }
    }
}
