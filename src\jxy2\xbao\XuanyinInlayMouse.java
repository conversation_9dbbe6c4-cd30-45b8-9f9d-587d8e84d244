package jxy2.xbao;

import org.come.Frame.ZhuFrame;
import org.come.entity.Goodstable;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.GoodsListFromServerUntil;

import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class XuanyinInlayMouse extends TemplateMouseListener {
    public  XuanyinInlayJPanel xuanyinInlayJPanel;
    public static List<String> list =new ArrayList<>();
    public int i;
    public XuanyinInlayMouse(int i, XuanyinInlayJPanel xuanyinInlayJPanel) {
        this.i = i;
        this.xuanyinInlayJPanel = xuanyinInlayJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        list.add(i+"");
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        list.remove(i+"");
        // 检查界面位置i是否有效
        if (i >= xuanyinInlayJPanel.getList().size()) return;
        // 直接从list中获取物品
        Goodstable goodstable = xuanyinInlayJPanel.getList().get(i);
        if (goodstable==null)return;
        Xuanyin(goodstable, i);
    }

    private void Xuanyin(Goodstable goodstable, int i) {
        Xuanbao xuanbao= RoleXuanbao.getRoleXuanbao().choseBao;
        if (xuanbao==null){
            ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的玄宝");
            return;
        }

        // 这里需要选择要镶嵌到哪个槽位，而不是直接使用界面位置i
        // 应该弹出槽位选择界面或者找到第一个空闲的槽位
        int targetSlot = findAvailableSlot(xuanbao, goodstable);
        if (targetSlot == -1) {
            ZhuFrame.getZhuJpanel().addPrompt2("没有可用的槽位");
            return;
        }

        System.out.println("准备将玄印 " + goodstable.getGoodsname() + " 镶嵌到槽位 " + targetSlot);

        //开始装备到指定槽位
        RoleXuanbao.getRoleXuanbao().xuanyinchange(xuanbao, goodstable, targetSlot, true);

        // 使用原始包裹位置删除物品
        Integer originalIndex = xuanyinInlayJPanel.getSumMap().get(i);
        if (originalIndex != null) {
            GoodsListFromServerUntil.Deleted(originalIndex + GoodsListFromServerUntil.Pagenumber*24);
        }
    }

    /**
     * 找到可用的槽位
     * @param xuanbao 玄宝
     * @param goodstable 要镶嵌的玄印
     * @return 槽位索引，-1表示没有可用槽位
     */
    private int findAvailableSlot(Xuanbao xuanbao, Goodstable goodstable) {
        String[] activeSlots = xuanbao.getBaomarkactive().split("\\|");
        String[] currentIds = null;
        if (xuanbao.getBaomarkid() != null && !xuanbao.getBaomarkid().isEmpty()) {
            currentIds = xuanbao.getBaomarkid().split("\\|");
        }

        // 获取玄宝界面的颜色配置
        XbaoJpanel xbaoJpanel = XbaoFrame.getXbaoFrame().getXbaoJpanel();

        // 检查每个槽位
        for (int slot = 0; slot < activeSlots.length; slot++) {
            // 槽位必须是激活的
            if (!"1".equals(activeSlots[slot])) {
                continue;
            }

            // 检查颜色匹配
            if (xbaoJpanel != null && xbaoJpanel.getStorecolors() != null &&
                slot < xbaoJpanel.getStorecolors().length) {
                String slotColor = xbaoJpanel.getStorecolors()[slot].getText();
                if (slotColor != null && !slotColor.isEmpty()) {
                    // 检查玄印颜色是否与槽位颜色匹配
                    boolean colorMatches = slotColor.chars()
                            .anyMatch(c -> goodstable.getGoodsname().contains(String.valueOf((char)c)));
                    if (!colorMatches) {
                        System.out.println("槽位 " + slot + " 颜色不匹配，槽位颜色: " + slotColor +
                                         ", 玄印: " + goodstable.getGoodsname());
                        continue; // 颜色不匹配，跳过这个槽位
                    }
                }
            }

            // 检查该槽位是否已经有玄印
            boolean slotOccupied = false;
            if (currentIds != null && slot < currentIds.length && !currentIds[slot].isEmpty()) {
                slotOccupied = true;

                // 检查是否是同类型玄印
                try {
                    Goodstable existingXuanyin = GoodsListFromServerUntil.fushis.get(new BigDecimal(currentIds[slot]));
                    if (existingXuanyin != null && existingXuanyin.getGoodsname().equals(goodstable.getGoodsname())) {
                        ZhuFrame.getZhuJpanel().addPrompt2("你已经穿戴了同类型的玄印");
                        return -1;
                    }
                } catch (Exception e) {
                    System.out.println("检查现有玄印时出错: " + e.getMessage());
                }
            }

            // 如果槽位空闲且颜色匹配，返回这个槽位
            if (!slotOccupied) {
                System.out.println("找到可用槽位: " + slot + ", 颜色匹配");
                return slot;
            }
        }

        // 如果没有找到匹配的槽位，给出提示
        ZhuFrame.getZhuJpanel().addPrompt2("没有找到颜色匹配的空闲槽位");
        return -1; // 没有可用槽位
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        // 检查界面位置i是否有效
        if (i >= xuanyinInlayJPanel.getList().size()) return;
        // 直接从list中获取物品
        Goodstable goodstable = xuanyinInlayJPanel.getList().get(i);
        if (goodstable==null)return;
        ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }


}
