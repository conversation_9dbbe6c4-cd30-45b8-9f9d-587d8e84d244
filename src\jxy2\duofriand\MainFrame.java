package jxy2.duofriand;

import javax.swing.*;

public class <PERSON><PERSON>rame extends JFrame {

    private JDesktopPane desktopPane;

    public MainFrame() {
        setTitle("Chat Application");
        setSize(800, 600);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        desktopPane = new JDesktopPane();
        add(desktopPane);
    }



    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            MainFrame mainFrame = new MainFrame();
            mainFrame.setVisible(true);
            // 示例: 添加两个聊天窗口
//            mainFrame.addChatFrame("Friend 1");
//            mainFrame.addChatFrame("Friend 2");
        });
    }
}
