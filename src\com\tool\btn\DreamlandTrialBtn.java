package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Frame.ExchangeAwardJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.DreamlandTrialMainJpanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class DreamlandTrialBtn extends MoBanBtn{
    
    private int caozuo;
    private DreamlandTrialMainJpanel dreamlandTrialMainJpanel;

    public DreamlandTrialBtn(String iconpath, int type,int caozuo ,DreamlandTrialMainJpanel dreamlandTrialMainJpanel) {
        super(iconpath, type);
        this.caozuo = caozuo;
        this.dreamlandTrialMainJpanel = dreamlandTrialMainJpanel;
    }
    
    public DreamlandTrialBtn(String iconpath, int type, Color[] colors, Font font, String text, int caozuo ,DreamlandTrialMainJpanel dreamlandTrialMainJpanel) {
        super(iconpath, type,colors);
        setText(text);
        setFont(font);
        setHorizontalTextPosition(SwingConstants.CENTER);
        setVerticalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.dreamlandTrialMainJpanel = dreamlandTrialMainJpanel;
    }
    

    @Override
    public void chooseyes() {
        
    }

    @Override
    public void chooseno() {
        
    }

    @Override
    public void nochoose(MouseEvent e) {
        if(caozuo == 0){//首页
            if(dreamlandTrialMainJpanel.getPageNow() <= 1){
                ZhuFrame.getZhuJpanel().addPrompt2("已经是首页了");
                return;
            }
            dreamlandTrialMainJpanel.showLvlTier(1);
        }else if(caozuo == 1){//上一页
            if(dreamlandTrialMainJpanel.getPageNow() <= 1){
                ZhuFrame.getZhuJpanel().addPrompt2("已经是首页了");
                return;
            }
            dreamlandTrialMainJpanel.showLvlTier(dreamlandTrialMainJpanel.getPageNow()-1);
        }else if(caozuo == 2){//下一页
            if(dreamlandTrialMainJpanel.getPageNow() >= dreamlandTrialMainJpanel.getPageMax()){
                ZhuFrame.getZhuJpanel().addPrompt2("已经是末页了");
                return;
            }
            dreamlandTrialMainJpanel.showLvlTier(dreamlandTrialMainJpanel.getPageNow()+1);
        }else if(caozuo == 3){//末页
            if(dreamlandTrialMainJpanel.getPageNow() >= dreamlandTrialMainJpanel.getPageMax()){
                ZhuFrame.getZhuJpanel().addPrompt2("已经是末页了");
                return;
            }
            dreamlandTrialMainJpanel.showLvlTier(dreamlandTrialMainJpanel.getPageMax());
        }else if(caozuo == 4){//开始挑战
            if(dreamlandTrialMainJpanel.getChooseNum() == -1){
                ZhuFrame.getZhuJpanel().addPrompt2("请先选择关卡");
                return;
            }
            int num = (dreamlandTrialMainJpanel.getPageNow()-1)*6+dreamlandTrialMainJpanel.getChooseNum()+1;
            SendMessageUntil.toServer(Agreement.getAgreement().hjslAgreement("P"+ num));
        }else if(caozuo == 5){//招募高手
//            GetForm.getGetForm().getChangeName().use(4);
//            FormsManagement.showForm(49);
            ExchangeAwardJframe.getExchangeAwardJframe().getAwardJpanel()
            .use(5, null);
        }else if(caozuo == 6){//添加次数
            SendMessageUntil.toServer(Agreement.getAgreement().hjslAgreement("R"));
        }
    }

}
