package come.tool.Fighting;

import com.tool.ModerateTask.Hero;
import com.tool.PlayerKill.PKSys;
import com.tool.image.ImageMixDeal;
import com.tool.imagemonitor.FightingMonitor;
import com.tool.tcp.SpriteFactory;
import com.tool.tcpimg.UIUtils;
import com.tool.time.TimeLimit;
import come.tool.FightingData.BattleEnd;
import come.tool.FightingData.FightingEndD;
import come.tool.FightingEffect.EffectType;
import come.tool.handle.HandleState;
import come.tool.handle.HandleType;
import org.come.Frame.PartnerArenaJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.bean.FightOperation;
import org.come.bean.PetOperationDTO;
import org.come.control.AssetControl;
import org.come.entity.RoleSummoning;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FightingMixDeal {
	/**回合时间 30秒*/ 
	public static final int ROUND_TIME = 30000;
	/**玩家自动操作时间*/ 
	public static final int AUTOMATION_PLAYER = 2000;
	/**召唤兽自动操作时间*/ 
	public static final int AUTOMATION_PET = 750;
    /** 战斗类型 */
    public static int BattleType;
    /** 战斗编号 */
    public static int FightingNumber;
    /** 回合数 */
    public static int CurrentRound;
    
    // 延迟时间
    public static long CorrectTime = 0;
    // 当前时间
    public static long time;
    // 当前战斗状态
    public static int State = 0;
    // 战斗播放指令
    public static Map<Integer, Statelist> BattlefieldPlay = new HashMap<>();
    // 法术显示
    public static List<SkillSpell> skills = new ArrayList<>();
    // //当前战斗数据
    public static List<Fightingimage> CurrentData = new ArrayList<>();
    // 时间
    public static long systime = System.currentTimeMillis();
    public static long cha = 0;
    public static BuffUtil buffUtil = new BuffUtil();
    // 那个阵营属于左边 主视角的阵营
    public static int camp = 1;
    public static int man = -1;

    /** 屏幕调整刷新位置 */
    public static void changepath() {
        for (int i = 0; i < CurrentData.size(); i++) {
            CurrentData.get(i).tiaoz(camp);
        }
        buffUtil.ViewPath(camp);
    }

    // 剩余自动回合
    public static int zdhh = 0;
    // 当前播放的音乐
    public static String Music1 = "";

    /**
     * 进度处理
     * 
     * @return
     */
    public synchronized static void PalyProgress(long pass) {
        time += pass;
        if (pass == 0)
            return;
        try {
            for (int i = 0, size = CurrentData.size(); i < size; i++) {
                CurrentData.get(i).addTime(pass);
            }
            long DieTime = (long) (pass * 1.6);
            for (int i = FightingMixDeal.skills.size() - 1; i >= 0; i--) {
                if (FightingMixDeal.skills.get(i).getDietime(DieTime)) {
                    FightingMixDeal.skills.remove(i);
                }
            }
            HandleType.getHandleById(State).handle(pass);
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
    }

    // 切换背景图
    public static void CgImg(int id) {
        id %= MAX;
        id++;
        if (id != bjid) {
            bjid = id;
            bj = new ImageIcon("resource/map/" + bjid + ".jpg").getImage();
        }
        if (yq == null || nq == null) {
            yq = new ImageIcon("resource/map/yq.png").getImage();
            nq = new ImageIcon("resource/map/nq.png").getImage();
        }
    }

    private static int MAX = 5;
    private static int bjid;
    private static Image bj;
    private static Image yq;
    public static String yqz;
    private static Image nq;
    public static String nqz;

    /**
     * 字段名:di 第<br>
     * 字段名:bout 回合<br>
     * 字段名:se 第几个回合数<br>
     * 
     * @time 2019年12月28日 上午10:45:03<br>
     */
    private static String di = "第 ";
    private static String bout = " 回合";
    private static String se = Integer.toString(CurrentRound);
    private static int seInt = CurrentRound;

    /** 画图 */
    public static void Drawing(Graphics g, long dietime) {
        try {
            g.drawImage(bj, 0, 0, ScrenceUntil.Screen_x, ScrenceUntil.Screen_y, null);
            buffUtil.draw(g);
            for (int i = 0, size = CurrentData.size(); i < size; i++) {
                g.setColor(Color.yellow);
                CurrentData.get(i).Drawing(g, dietime);
            }
            for (int i = 0; i < skills.size(); i++) {
                skills.get(i).draw(g);
            }
            for (int i = 0, size = CurrentData.size(); i < size; i++) {
                CurrentData.get(i).Drawing2(g, dietime);
            }
            if (State <= HandleState.HANDLE_PET) {
                g.setFont(UIUtils.TEXT_FONT4);
                String times = (30 - time / 1000) + "";
                g.setColor(Color.RED);
                g.drawString(times, ScrenceUntil.Screen_x / 2, 110);
            }
            if (zdhh != 0) {
                g.setColor(Color.RED);
                g.setFont(UIUtils.TEXT_FONT1);
                g.drawString("剩余回合 " + zdhh, ScrenceUntil.Screen_x - 125, 120);
            }
            g.setFont(UIUtils.TEXT_HYK20);
            g.setColor(UIUtils.COLOR_FightingRound_Black);
            g.drawString(di, 91, 111);
            g.drawString(bout, 123 + se.length() * 11, 111);
            g.setColor(UIUtils.COLOR_FightingRound);
            g.drawString(di, 90, 110);
            g.drawString(bout, 122 + se.length() * 11, 110);
            if (seInt != CurrentRound) {
                seInt = CurrentRound;
                se = Integer.toString(CurrentRound);
            }
            g.setFont(UIUtils.TEXT_FONT3);
            g.setColor(UIUtils.COLOR_FightingRound_Black);
            g.drawString(se, 123, 111);
            g.setColor(Color.yellow);
            g.drawString(se, 122, 110);

            g.setFont(UIUtils.TEXT_FONT);
            if (yqz != null) {
                g.drawImage(yq, ScrenceUntil.Screen_x - 330, 15, null);
                g.drawString(yqz, ScrenceUntil.Screen_x - 305, 30);
            }
            if (nqz != null) {
                g.drawImage(nq, ScrenceUntil.Screen_x - 290, 15, null);
                g.drawString(nqz, ScrenceUntil.Screen_x - 265, 30);
            }
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    /**
     * 切换状态
     */
    public static void changeState(int state) {
        if (state == HandleState.HANDLE_END || state == HandleState.HANDLE_PLAYER || state == HandleState.HANDLE_PET
                || state == HandleState.HANDLE_WAIT) {
            CorrectTime = 0;
        }
        State = state;
    }
    /**回合结束后找出死亡切不为玩家的清除*/
    public static void qingchusiwang() {
        int bbman = myman() + 5;
        for (int i = CurrentData.size() - 1; i >= 0; i--) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (data.getHp_Current() <= 0) {
                if (data.getType() == 0)
                    continue;
                if (data.getType() == 2)
                    continue;
                if (isFightType()) {
                    if (data.getType() == 1 && FightingMixDeal.camp == data.getCamp() && bbman == data.getMan()
                            && TimeLimit.getLimits().getLimit("VIP") == null) {
                        RoleSummoning bb = Article.bb(data.getId());
                        if (bb != null) {
                            bb.setBasishp(1);
                            bb.addFaithful(-30);
                            PetOperationDTO dto = new PetOperationDTO();
                            dto.setPetId(bb.getSid());
                            dto.setOperationType("SUMMONING_BEAST_DEATH");
                            SendRoleAndRolesummingUntil.sendRoleSumming(dto);
                        }
                    }
                }
                CurrentData.remove(i);
            }
        }
    }

    /**
     * 根据接出流程战斗事件FightingEvents
     */
    public static List<StateProgress> AnalysisFightingEvents(FightingEvents fightingEvents, int JD) {
        List<StateProgress> States = new ArrayList<>();
        try {
            FightingState Originator = fightingEvents.getOriginator();
            if (Originator != null) {
                String type = Originator.getStartState();
                if (type.equals("召唤") || type.equals("召回") || type.equals("闪现")) {
                    StateProgress progress = EffectType.getEffectById(3).analysisAction(Originator, -1);
                    if (progress != null) {
                        States.add(progress);
                    }
                } else if (type.equals("逃跑成功") || type.equals("逃跑失败")) {
                    int path = FightingMixDeal.CurrentData(Originator.getCamp(), Originator.getMan());
                    if (path != -1) {
                        StateProgress progress = EffectType.getEffectById(4).analysisAction(Originator, path);
                        if (progress != null) {
                            States.add(progress);
                        }
                    }
                } else if (type.equals("捕捉失败") || type.equals("捕捉成功")) {
                    int path = FightingMixDeal.CurrentData(Originator.getCamp(), Originator.getMan());
                    if (path != -1) {
                        StateProgress progress = EffectType.getEffectById(5).analysisAction(Originator, path);
                        if (progress != null) {
                            States.add(progress);
                        }
                    }
                }
            } else {
                for (int i = 0; i < fightingEvents.getAccepterlist().size(); i++) {
                    FightingState fightingState = fightingEvents.getAccepterlist().get(i);
                    String type = fightingState.getStartState();
                    StateProgress progress = null;
                    if (type.equals("召唤") || type.equals("召回") || type.equals("闪现")) {
                        progress = EffectType.getEffectById(3).analysisAction(fightingState, -1);
                    } else {
                        int path = FightingMixDeal.CurrentData(fightingState.getCamp(), fightingState.getMan());
                        if (path != -1) {
                            if (type.equals("移动") || type.equals("瞬移") || type.equals("遁地")) {
                                progress = EffectType.getEffectById(1).analysisAction(fightingState, path);
                            } else if (type.equals("技能移动")) {
                                progress = EffectType.getEffectById(6).analysisAction(fightingState, path);
                            } else {
                                progress = EffectType.getEffectById(2).analysisAction(fightingState, path);
                            }
                        }
                    }
                    if (progress != null) {
                        if (progress.getSpell() != null) {
                            progress.getSpell().setRound(JD);
                        }
                        States.add(progress);
                    }
                }
            }
        } catch (Exception e) {
            // TODO: handle exception
            System.err.println(GsonUtil.getGsonUtil().getgson().toJson(fightingEvents));
            e.printStackTrace();
        }
        return States;
    }

    /** 找到删除指定位置 */
    public static void depath(int mycamp, int myman) {
        int bbman = FightingMixDeal.myman() + 5;
        for (int i = 0; i < CurrentData.size(); i++) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (data.getCamp() == mycamp && data.getMan() == myman) {
                if (FightingMixDeal.camp == data.getCamp() && bbman == data.getMan()) {
                    RoleSummoning bb = Article.bb(data.getId());
                    if (bb != null) {
                        if (FightingMixDeal.isFightType() && TimeLimit.getLimits().getLimit("VIP") == null) {
                            if (data.getHp_Current() <= 0) {
                                bb.setBasishp(1);
                                bb.addFaithful(-30);
                            }
                            PetOperationDTO dto = new PetOperationDTO();
                            dto.setPetId(bb.getSid());
                            dto.setOperationType("SUMMONING_BEAST_DEATH");
                            SendRoleAndRolesummingUntil.sendRoleSumming(dto);
                        }
                    }
                }
                CurrentData.remove(i);
                break;
            }
        }
    }

    /**
     * 进入回合战斗状态
     */
    public static void RoundFighting() {
        FormsManagement.HideForm(34);
        FormsManagement.HideForm(6);
        ZhuFrame.getZhuJpanel().HideBeastBtn();
    }

    /**
     * 进入回合决策状态
     */
    public static void RoundDecision() {
        time = 0;
        CurrentRound++;
        if (ZhuFrame.getZhuJpanel().getZidong().getText().equals("取消")&&zdhh-- <= 0) {
        	ZhuFrame.getZhuJpanel().getZidong().setText("自动");
        }
        ZhuFrame.getZhuJpanel().HideBeastBtn();
        if (camp != -1) {
            ZhuFrame.getZhuJpanel().ShowManBtn(isLL());
            changeState(State = HandleState.HANDLE_PLAYER);
        } else {
            changeState(State = HandleState.HANDLE_WAIT);
        }
        for (int i = CurrentData.size() - 1; i >= 0; i--) {
            CurrentData.get(i).getmsg();
        }
    }
    /** 观战退出战斗场景 */
    public static void FightingEnd() {
        // 结束战斗返回场景音乐
        changeState(State = HandleState.USUAL);
        BattlefieldPlay.clear();
        time = 0;
        CorrectTime = 0;
        CurrentRound = 0;
        Music.addbeijing(UserMessUntil.getAllmapbean().getAllMap().get(ImageMixDeal.userimg.getRoleShow().getMapid() + "").getMusic()+".mp3");
        OutFighting();
    }

    /** 战斗结束发送服务器 */
    public static void FightingEnd(BattleEnd battleEnd) {
        // 结束战斗返回场景音乐
        changeState(State = HandleState.USUAL);
        BattlefieldPlay.clear();
        time = 0;
        CorrectTime = 0;
        CurrentRound = 0;

        Music.addbeijing(UserMessUntil.getAllmapbean().getAllMap()
                .get(ImageMixDeal.userimg.getRoleShow().getMapid() + "").getMusic()
                + ".mp3");
        if (battleEnd == null) {
            FightingEndD fightingEndD = new FightingEndD();
            fightingEndD.setFightingsumber(FightingNumber);
            String sendMes = Agreement.FightingendAgreement(GsonUtil.getGsonUtil().getgson().toJson(fightingEndD));
            SendMessageUntil.toServer(sendMes);
        } else if (FightingMixDeal.camp != -1) {
            FrameMessageChangeJpanel.addtext(5, battleEnd.getCamp() == FightingMixDeal.camp ? "战斗胜利" : "战斗失败", null,
                    null);
            if (battleEnd.getTaskDaily() != null) {
                PKSys.getPkSys().upPK(battleEnd.getTaskDaily());
            }
            AssetControl.asset(battleEnd.getAssetUpdate());
            Hero.getHero().addTask(battleEnd.getTaskn());
            if (battleEnd.getMsg() != null && !battleEnd.getMsg().equals("")) {
                String[] vs = battleEnd.getMsg().split("\\|");
                for (int i = 0; i < vs.length; i++) {
                    FrameMessageChangeJpanel.addtext(5, vs[i], null, null);
                }
            }
            if (battleEnd.getArenaBean()!=null) {
            	PartnerArenaJframe.getPartnerArenaJframe().getPartnerArenaMainPanel().showView(battleEnd.getArenaBean());    
            }
        }

        OutFighting();
        
    }

	/**找到玩家的位置*/
	public static int myman() { return man; }
	/**找到玩家或者玩家召唤兽数据*/
	public static Fightingimage getdata(int type) { return CurrentDataImage(camp, myman() + type * 5); }
	/**判断是否还有召唤兽*/
	public static boolean MyBeastLifeAndDeath() { return CurrentDataImage(camp, myman() + 5)!=null; }
	/**判断是否是 人死 召唤兽死 true 是*/
	public static boolean isLL() {
		Fightingimage fightingimage=CurrentDataImage(camp, man+5);
		if (fightingimage==null||fightingimage.getFightingManData().getHp_Current()<=0) {
			fightingimage=CurrentDataImage(camp, man);
			if (fightingimage==null||fightingimage.getFightingManData().getHp_Current()<=0) {
				return true;
			}
		}
		return false;
	}
	/**根据阵营 和 位置找到在CurrentData的位置*/
	public static int CurrentData(int camp, int man) {
		for (int i = 0; i < CurrentData.size(); i++) {
			if (CurrentData.get(i).getFightingManData().getCamp() == camp
					&& CurrentData.get(i).getFightingManData().getMan() == man) {
				return i;
			}
		}
		return -1;
	}
	/**根据阵营 和 位置找到在CurrentData的位置*/
	public static Fightingimage CurrentDataImage(int camp, int man) {
		for (int i = 0; i < CurrentData.size(); i++) {
			Fightingimage fightingimage=CurrentData.get(i);
			if (fightingimage.getFightingManData().getCamp()==camp&&fightingimage.getFightingManData().getMan()==man) {
				return CurrentData.get(i);
			}
		}
		return null;
	}

    /** 返回玩家或者召唤兽的战斗FightingState */
    public static FightingState FightingState(String type, int type1) {
        if (type1 == 0) {
            return new FightingState(camp, myman(), type);//主人
        } else {
            return new FightingState(camp, myman() + 5, type);//召唤兽
        }
    }

    /** 初始化战斗数据的的方法 */
    public static void CurrentData(List<FightingManData> fightingManDatas, String buff, FightingManData mydata) {

        buffUtil.buffs.clear();
        int my_camp = -1;
        int my_man = -1;
        if (mydata != null) {
            my_camp = mydata.getCamp();
            my_man = mydata.getMan();
            nqz = mydata.getNqz() + "";
            yqz = mydata.getYqz() + "";
            buffUtil.SXSkill(mydata.cxxx("技能"));
        } else {
            buffUtil.skills.clear();
        }
        buffUtil.CS(buff, my_camp);
        CgImg(FightingNumber / 100);
        camp = my_camp;
        man = my_man;
        if (CurrentData == null)
            CurrentData = new ArrayList<>();
        else
            CurrentData.clear();
        int xz = 15;
        while (!fightingManDatas.isEmpty()) {
            for (int i = fightingManDatas.size() - 1; i >= 0; i--) {
//              int man=fightingManDatas.get(i).getMan();
//              if (man<10) {man=man/5*5+(5-man%5);}

            	if (fightingManDatas.get(i).getMan() >= xz) {
                    CurrentData.add(new Fightingimage(fightingManDatas.remove(i), my_camp));

                }
            }
            if (xz > 10) {
                xz -= 5;
            } else {
                xz--;
            }
        }
        if (camp == -1) {
            ZhuFrame.getZhuJpanel().getZidong().setText("离开");
        } else if (zdhh > 0) {
            ZhuFrame.getZhuJpanel().getZidong().setText("取消");
        } else {
            ZhuFrame.getZhuJpanel().getZidong().setText("自动");
        }
        CurrentRound = 1;
        ZhuFrame.getZhuJpanel().intoFighting();
        if(FightingMixDeal.BattleType == 101){
            if (FormsManagement.getInternalForm2(5) != null) {
                if (FormsManagement.getframe(5).isVisible()) {
                    FormsManagement.HideForm(5);
                }
            }
        }else if(FightingMixDeal.BattleType == 103){
            if (FormsManagement.getInternalForm2(111) != null) {
                if (FormsManagement.getframe(111).isVisible()) {
                    FormsManagement.HideForm(111);
                }
            }
        }
    }

    /** 退出战斗场景 */
    public static void OutFighting() {
        buffUtil.isMcqh = false;
        ZhuFrame.getZhuJpanel().outFighting();
        CurrentData.clear();
        BattlefieldPlay.clear();
        skills.clear();
        Music1 = "";
        FightingMixDeal.FightingNumber = -1;
        SpriteFactory.ResetAndRemove();
        nqz = null;
        yqz = null;
        buffUtil.buffs.clear();
    }

    /** 判断是否点击到人物上 监听触发 */
    public static void MonitorTrigger(int x, int y) {
        if (State == HandleState.HANDLE_PLAYER || State == HandleState.HANDLE_PET) {
            // 只会触发野怪召唤兽人物
            for (int i = 0; i < CurrentData.size(); i++) {// 不为灵宝和宝宝
                if (CurrentData.get(i).getFightingManData().getType() != 3
                        && CurrentData.get(i).getFightingManData().getType() != 4) {
                    if (CurrentData.get(i).isContains(x, y)) {// 触发监听
                        FightingMonitor.Fighting(CurrentData.get(i));
                        return;
                    }
                }
            }
            SkillTx skillTx = buffUtil.MonitorBuff(x, y);
            if (skillTx != null) {
                if (skillTx.isIs()) {
                    return;
                }
                if (State != HandleState.HANDLE_PLAYER) {
                    return;
                }
                Fightingimage fightingimage = getdata(0);
                if (fightingimage == null) {
                    return;
                }
                if (skillTx.getId() >= 9001 && skillTx.getId() <= 9500) {// 套装技能
                    if (fightingimage.getFightingManData().getNqz() < 100) {
                        ZhuFrame.getZhuJpanel().addPrompt2("你怒气值还未达到100,无法释放" + skillTx.getSkill().getSkillname());
                        return;
                    }
                }
                FightOperation operation = new FightOperation();
                operation.Record(fightingimage.getFightingManData().getCamp(), fightingimage.getFightingManData()
                        .getMan(), 1, skillTx.getSkill().getSkillname());
                FightingEvents events = FightingMonitor.AttackGenerate(operation);
                FightingMonitor.FightingOperation(events);
                skillTx.setIs(true);
                ZhuFrame.getZhuJpanel().addPrompt2("你释放了" + skillTx.getSkill().getSkillname());
                return;
            }
        }
    }

    /** 加载音乐 */
    public static void LoadMusic(String yinyue,FightingManData fightingManData) {
        if (yinyue != null && !yinyue.equals("")) {
            if (!Music1.equals(yinyue)) {
                String key = fightingManData.getModel().split("_")[0];
                String mp = "resource/Fightingmusic/"+key + "/" + yinyue  + ".mp3";//修改战斗音效路径位置
                if(new File(mp).exists()){
                    Music1 = yinyue;
                    Music.addyinxiaoNew(mp);
                }else{
                    Music1 = yinyue;
                    if (yinyue.equals("孩子喊话")) {
                        Music.addyinxiao(yinyue + ".mp3");
                    } else {
                        Music.addyinxiao(yinyue + ".mp3");
                    }
                }
            }
        }
    }

    /** 获取随机人物数据 camp true表示同阵营 false表示不同同阵营 my true表示能选自己 false表示不能 */
    public static Fightingimage Randomdata(boolean similar, boolean my, int man) {
        int cp = similar ? camp : EnemyCamp(camp);
        Fightingimage fightingimage = null;
        List<Integer> a = new ArrayList<>();
        for (int i = 0; i < CurrentData.size(); i++) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (cp == data.getCamp()) {
                if (similar && my && man == data.getMan())
                    continue;
                a.add(i);
            }
        }
        if (a.size() != 0) {
            fightingimage = CurrentData.get(a.get(Util.random.nextInt(a.size())));
        }
        return fightingimage;
    }

    /** 找到敌方阵营 */
    public static int EnemyCamp(int camp) {
        for (int i = 0; i < CurrentData.size(); i++) {
            if (CurrentData.get(i).getFightingManData().getCamp() != camp) {
                return CurrentData.get(i).getFightingManData().getCamp();
            }
        }
        return -1;
    }

    // 添加战斗对话
    public static void Dialogue(String name, String text) {
        for (int i = 0; i < CurrentData.size(); i++) {
            if (CurrentData.get(i).getFightingManData().getType() == 0) {
                if (CurrentData.get(i).getFightingManData().getManname().equals(name)) {
                    CurrentData.get(i).Dialogue(text);
                    return;
                }
            }
        }
    }

    /** 判断阵营的玩家死亡人数 */
    public static int zyd(int camp) {
        int size = 0;
        for (int i = CurrentData.size() - 1; i >= 0; i--) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (camp == data.getCamp() && data.getType() == 0 && data.getHp_Current() <= 0)
                size++;
        }
        return size;
    }

    /** 获取战斗中列表 */
    public static List<String> dwd(int camp) {
        List<String> buffer = new ArrayList<>();
        // 获取带头的
        for (int i = CurrentData.size() - 1; i >= 0; i--) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (camp == data.getCamp() && data.getType() == 0 && data.getMan() == 2) {
                buffer.add(data.getManname());
                break;
            }
        }
        for (int i = CurrentData.size() - 1; i >= 0; i--) {
            FightingManData data = CurrentData.get(i).getFightingManData();
            if (camp == data.getCamp() && data.getType() == 0 && data.getMan() != 2) {
                buffer.add(data.getManname());
            }
        }
        return buffer;
    }

    /** true 表示会掉 判断战斗类型是否会掉血法忠诚 */
    public static boolean isFightType() {
        if (BattleType == 5 || BattleType == 11 || BattleType == 12 || BattleType == 31 || BattleType == 33
                || BattleType == 34) {
            return false;
        }
        return true;
    }
}