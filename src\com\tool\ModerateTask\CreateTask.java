package com.tool.ModerateTask;

import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.bean.RoleShow;
import org.come.until.AnalysisString;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;

public class CreateTask {
   public static CreateTask createTask;
   public static CreateTask getCreateTask() {
	    if (createTask==null) createTask=new CreateTask();
		return createTask;
	}
	/**
	 *  点击杀怪触发判断
	 */
	public boolean isReceive2(Task task){
		TaskData taskData=task.getTaskData();
		//等级限制
		String lvl=taskData.getLvl();
		//组队限制
		int TeamSum=taskData.getTeamSum();
		//人数 等级判断
		if (islvl(TeamSum, lvl))
		return true;
		return false;
	}
	/**
	 * 等级限制 和组队人数限制
	 */
	public boolean islvl(int TeamSum,String lvl){
		int minlvl=0;
		int maxlvl=200;
		int turn=0;
		int maxturn=4;
		int maxLvl=200;	
//		int[4] lvls  最小转生次数 最小等级 最大转生次数 最大等级
		if (lvl!=null&&!lvl.equals("")) {
			String[] lvls=lvl.split("\\|");
			String[] vs=lvls[0].split("\\-");
			minlvl=Integer.parseInt(vs[0]);
			maxlvl=Integer.parseInt(vs[1]);
			if (lvls.length>=2) {
				turn=Integer.parseInt(lvls[1]);
			}
			if (lvls.length==3) {
				vs=lvls[2].split("\\-");
				maxturn=Integer.parseInt(vs[0]);
				maxLvl=Integer.parseInt(vs[1]);
			}
		}
		if (TeamSum==0) {
		if (ImageMixDeal.userimg.getTeams().length!=1) {
			ZhuFrame.getZhuJpanel().addPrompt2("该任务只能一个人完成");	
			return false;
		}
		int manlvl=AnalysisString.lvlint(ImageMixDeal.userimg.getRoleShow().getGrade());
		if (manlvl<minlvl||manlvl>maxlvl) {
			ZhuFrame.getZhuJpanel().addPrompt2("等级不满足在"+minlvl+"-"+maxlvl+"范围");
			return false;
		}
		int manturn=ImageMixDeal.userimg.getRoleShow().getTurnAround();
		if (manturn<turn) {
			ZhuFrame.getZhuJpanel().addPrompt2("转生次数最少"+turn+"次");	
			return false;
		}
		if (manturn>maxturn||(manturn==maxturn&&manlvl>maxLvl)) {
			ZhuFrame.getZhuJpanel().addPrompt2("最大到"+maxturn+"转"+maxLvl+"级");	
			return false;
		}
		}else if (TeamSum==1) {
		int manlvl=AnalysisString.lvlint(ImageMixDeal.userimg.getRoleShow().getGrade());
		if (manlvl<minlvl||manlvl>maxlvl) {
			ZhuFrame.getZhuJpanel().addPrompt2("等级不满足在"+minlvl+"-"+maxlvl+"范围");	
			return false;
		}
		int manturn=ImageMixDeal.userimg.getRoleShow().getTurnAround();
		if (manturn<turn) {
			ZhuFrame.getZhuJpanel().addPrompt2("转生次数最少"+turn+"次");	
			return false;
		}
		if (manturn>maxturn||(manturn==maxturn&&manlvl>maxLvl)) {
			ZhuFrame.getZhuJpanel().addPrompt2("最大到"+maxturn+"转"+maxLvl+"级");	
			return false;
		}
		}else {
			String[] v=ImageMixDeal.userimg.getTeams();
			if (v!=null) {
				int palSum=0;
				LoginResult login=RoleData.getRoleData().getLoginResult();
				if (login.getPals()!=null&&!login.getPals().equals("")) {
					palSum=login.getPals().split("\\|").length;
				}
				if (v.length+palSum<TeamSum) {
					ZhuFrame.getZhuJpanel().addPrompt2("队伍人数不够"+TeamSum+"个人,先凑齐人数在来吧");	
					return false;
				}
				for (int i = 0; i < v.length; i++) {
					RoleShow roleShow=ImageMixDeal.huoquLogin(v[i]).getRoleShow();
					int manlvl=AnalysisString.lvlint(roleShow.getGrade());
					if (manlvl<minlvl||manlvl>maxlvl) {
						ZhuFrame.getZhuJpanel().addPrompt2("队伍等级不满足在"+minlvl+"-"+maxlvl+"范围");	
						return false;
					}
					int manturn=roleShow.getTurnAround();
					if (manturn<turn) {
						ZhuFrame.getZhuJpanel().addPrompt2("转生次数最少"+turn+"次");	
						return false;
					}
					if (manturn>maxturn||(manturn==maxturn&&manlvl>maxLvl)) {
						ZhuFrame.getZhuJpanel().addPrompt2("最大到"+maxturn+"转"+maxLvl+"级");	
						return false;
					}
				}	
			}
			
		}
		return true;
	}
}
