package come.tool.JDialog;

import jxy2.flight.FlyFrame;
import jxy2.flight.FlyJPanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class DestroyFlyJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            FlyJPanel flyJPanel = FlyFrame.getFlightFrame().getFlightJPanel();
            if (flyJPanel.getAssemble()==null)return;
            String msg = Agreement.getAgreement().FlyAgreement("R"+ flyJPanel.getAssemble());
            SendMessageUntil.toServer(msg);
        }
    }
}
