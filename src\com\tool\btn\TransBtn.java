package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;

import javax.swing.SwingConstants;

import org.come.Jpanel.TransJpanel;
import org.come.until.Util;

public class TransBtn extends MoBanBtn {

    private TransJpanel transJpanel;
    // 0召唤兽 1灵宝 2确定 3上一页 4下一页
    private int p;

    public TransBtn(String iconpath, int type, int p, TransJpanel transJpanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type);
        this.transJpanel = transJpanel;
        this.p = p;
    }

    public TransBtn(String iconpath, int type, Color[] colors, Font font, String text, int p, TransJpanel transJpanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, colors);
        if (text != null) {
            this.setText(text);
            setFont(font);
            setVerticalTextPosition(SwingConstants.CENTER);
            setHorizontalTextPosition(SwingConstants.CENTER);
        }
        this.transJpanel = transJpanel;
        this.p = p;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        // TODO Auto-generated method stub
        // 0召唤兽 1灵宝 2确定
        if (p == 0 || p == 1) {
            transJpanel.cJpanelType(p);
        } else if (p == 2) {
            /**判断是否解锁*/
            if(Util.isCanBuyOrno()){
                return;
            }
            transJpanel.queDing();
        }
    }

}
