package jxy2.zodiac;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class InlaidStarBtn extends MoBanBtn {
        public InlaidStarJPanel innerPanel;
        private int index;
      public InlaidStarBtn(String iconpath, int type, Color[] colors, Font font, String text, int index, InlaidStarJPanel innerPanel, String prompt) {
          // TODO Auto-generated constructor stub
          super(iconpath, type, 0, colors, prompt);
          this.setText(text);
          setFont(font);
          this.index = index;
          setVerticalTextPosition(SwingConstants.CENTER);
          setHorizontalTextPosition(SwingConstants.CENTER);
          this.innerPanel = innerPanel;
      }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (getText().equals("镶嵌")) {//镶嵌
            if (innerPanel.getGoodstable()==null){
                ZhuFrame.getZhuJpanel().addPrompt2("#R您还没有收集到此星盘");
                return;
            }
            ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel().setDynamicBtn(innerPanel.getType());
            // 发送给服务器
            String msg = Agreement.getAgreement().ActivateSSAgreement("J" + innerPanel.getGoodstable().getRgid() + "|" + innerPanel.getType() + "|" + innerPanel.getIndex());
            // 向服务器发送信息
            SendMessageUntil.toServer(msg);
            FormsManagement.HideForm(134);
        }else if (getText().equals("激活")) {//激活技能
            ActivationSkill(innerPanel.getSkill().getSkillid(),innerPanel.getIndex());
        }else { // 关闭
            FormsManagement.HideForm(134);
        }
    }

    public static void ActivationSkill(String skillid ,int index) {
        if (RoleData.getRoleData().getLoginResult().getScoretype("魂值").intValue()==0) {
            ZhuFrame.getZhuJpanel().addPrompt2("#R您还没有足够的魂值来激活技能");
            return;
        }
        RoleData.getRoleData().getPrivateData().setSkills("X", skillid+"_"+index);
        // 发送给服务器\
        String msg = Agreement.getAgreement().ActivateSSAgreement("T"+skillid+"_"+index);
        // 向服务器发送信息
        SendMessageUntil.toServer(msg);
        //刷新
        ChineseZodiacFrame.getChineseZodiacFrame().getChineseZodiacPanel().ActivatedSkillQuery(true);
        FormsManagement.HideForm(134);
    }

}
