package jxy2.refine;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SynthesisJpanel;
import org.come.entity.Goodstable;
import org.come.mouslisten.StorageJadeMouslisten;
import org.come.until.GoodsListFromServerUntil;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class RefineMouse implements MouseListener {
    private RefiningNewEquiJpanel refineJPanel;
    private EqartificeJapanel eqartificeJapanel;
    private EqmMakeJpanel eqmMakeJpanel;
    private ReimpJpanel reimpJpanel;
    private EqsuitJpanel eqsuitJpanel;
    private SymbolJpanel symbolJpanel;
    private GodJpanel godJpanel;
    private GemsTJpanel gemsTJpanel;

    private int  index;
    public RefineMouse(RefiningNewEquiJpanel refineJPanel,int  index) {
        this.refineJPanel = refineJPanel;
        this.index = index;
    }
    public RefineMouse(EqartificeJapanel eqartificeJapanel,int  index) {
        this.eqartificeJapanel = eqartificeJapanel;
        this.index = index;
    }
    public RefineMouse(EqmMakeJpanel eqmMakeJpanel,int  index) {
        this.eqmMakeJpanel = eqmMakeJpanel;
        this.index = index;
    }
    public RefineMouse(ReimpJpanel reimpJpanel,int  index) {
        this.reimpJpanel = reimpJpanel;
        this.index = index;
    }
    public RefineMouse(EqsuitJpanel eqsuitJpanel,int  index) {
        this.eqsuitJpanel = eqsuitJpanel;
        this.index = index;
    }
    public RefineMouse(SymbolJpanel symbolJpanel, int  index) {
        this.symbolJpanel = symbolJpanel;
        this.index = index;
    }
    public RefineMouse(GodJpanel godJpanel, int  index) {
        this.godJpanel = godJpanel;
        this.index = index;
    }
    public RefineMouse(GemsTJpanel gemsTJpanel, int  index) {
        this.gemsTJpanel = gemsTJpanel;
        this.index = index;
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        Goodstable goodstable = null;
        if (index<24){
            goodstable = GoodsListFromServerUntil.Getgood(index);
        }else {
            if (eqartificeJapanel!=null){
                goodstable = eqartificeJapanel.goods[index - 24];
            }else if (refineJPanel!=null){
                goodstable = refineJPanel.goods[index - 24];
            }else if (eqmMakeJpanel!=null){
                goodstable = eqmMakeJpanel.goods[index - 24];
            }else if (reimpJpanel!=null){
                goodstable = reimpJpanel.goods[index - 24];
            } else if (eqsuitJpanel != null) {
                if (index==25&&eqsuitJpanel.getMinType()==0){
                    eqsuitJpanel.goods[1] = null;
                    eqsuitJpanel.twoEquipment[1].setIcon(null);
                }else {
                    goodstable = eqsuitJpanel.goods[index - 24];
                }
            }else if (symbolJpanel!=null&&symbolJpanel.getMinType()==0){
                goodstable = symbolJpanel.goods[index - 24];
            }else if (symbolJpanel!=null&&symbolJpanel.getMinType()==1){
                goodstable = symbolJpanel.goods1[index - 24];
            }else if (godJpanel!=null){
                goodstable = godJpanel.goods[index - 24];
            }else if (gemsTJpanel!=null){
                goodstable = gemsTJpanel.goods[index - 24];
            }
        }


        if (goodstable != null) {
            if (refineJPanel!=null){
                refineJPanel.ClickGood(goodstable, index);
            }else if (eqartificeJapanel!=null){
                eqartificeJapanel.ClickGood(goodstable, index);
            }else if (eqmMakeJpanel!=null){
               eqmMakeJpanel.ClickGood(goodstable,index);
            }else if (reimpJpanel!=null){
                reimpJpanel.ClickGood(goodstable,index);
            }else if (eqsuitJpanel!=null){
                eqsuitJpanel.ClickSuit(goodstable,index);
            }else if (symbolJpanel!=null){
                symbolJpanel.ClickSuit(goodstable,index);
            }else if (godJpanel!=null){
                godJpanel.ClickSuit(goodstable,index);
            }else if (gemsTJpanel!=null){
                gemsTJpanel.ClickSuit(goodstable,index);
            }

        }


    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        Goodstable goodstable = null;
        if (index==4){
            goodstable = refineJPanel.goods[0];
            if (goodstable!=null) {
                int i = goodstable.getRefinelv() == null ? 0 : 1;
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable, refineJPanel.getPer()+i);
            }
        }else {

            if (index<24){
                goodstable = GoodsListFromServerUntil.Getgood(index);
            }else {
                if (eqartificeJapanel!=null){
                    goodstable = eqartificeJapanel.goods[index - 24];
                }else if (refineJPanel!=null){
                    goodstable = refineJPanel.goods[index - 24];
                }else if (eqmMakeJpanel!=null){
                    goodstable = eqmMakeJpanel.goods[index - 24];
                }else if (reimpJpanel!=null){
                    goodstable = reimpJpanel.goods[index - 24];
                }else if (eqsuitJpanel!=null){
                    if (index==25&&eqsuitJpanel.getMinType()==0&&eqsuitJpanel.twoEquipment[1].getIcon()!=null){
                        int index = SynthesisJpanel.getGoodstableBean().getType() - 1;
                        if (SynthesisJpanel.getGoodstableBean().getPartJade() != null && index >= 0 && index < 5) {
                            StorageJadeMouslisten.showMsg(index);
                        }
                    }else {
                        goodstable = eqsuitJpanel.goods[index - 24];
                    }

                }else if (symbolJpanel!=null&&symbolJpanel.getMinType()==0){
                    goodstable = symbolJpanel.goods[index - 24];
                }else if (symbolJpanel!=null&&symbolJpanel.getMinType()==1){
                    goodstable = symbolJpanel.goods1[index - 24];
                }else if (godJpanel!=null){
                    goodstable = godJpanel.goods[index - 24];
                }else if (gemsTJpanel!=null){
                    goodstable = gemsTJpanel.goods[index - 24];
                }
            }
            if (goodstable != null) {
                ZhuFrame.getZhuJpanel().creatgoodtext(goodstable);
            }
        }

    }

    @Override
    public void mouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
    }
}
