package come.tool.BangBattle;

import java.awt.Graphics;
import java.awt.Image;

import org.come.bean.PathPoint;
import org.come.until.Util;

import com.tool.tcp.Frame;
import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;

/**
 * 建筑
 * <AUTHOR>
 *
 */
public class Build {
	public static int MAN=0;//满
	public static int CAN=1;//残血
	public static int PO=2;//破
	public static int LZ1=3;//龙门左转
	public static int LZ2=4;//龙门左开炮
	public static int LZ3=5;//龙门左回转
	public static int LY1=6;//龙门右转	
	public static int LY2=7;//龙门右开炮	
	public static int LY3=8;//龙门右回转	
	public static int TOWER_DOOR=0;//门
	public static int TOWER_FIRE=1;//火
	public static int TOWER_ICE=2;//冰
	public static int TOWER_LONG=3;//龙门    
	public static int TOWER_HUO=4;//火    
	private int bh;//编号
    private int type;//塔类型
	private int state;//状态
	private int hp;//hp
	//位置
	private int x;
	private int y;
	private Image image;
	private Sprite sprite;
	private Sprite s1;//顶部光环
	private Sprite s2;//底部光环
	private Sprite s3;//打击光环	
	private int v;
	private int px;
	private int py;
	//动画进度处理
	private int time;
	public void draw(Graphics g){
		if (type==TOWER_DOOR&&state==PO){
			image=null;
			return;
		}
		time+=9;
		if (state>=LZ1) {
			if (sprite!=null) {
				if (time>sprite.getTime()) {
	                if (state==LY3||state==LZ3) {
						state=MAN;
					}else {
						state++;		
					}
					setState(state);
				}
			}
		}else {
			time+=9;
		}
		PathPoint point = Util.mapmodel.path(x, y);
		if (sprite==null){
			StringBuffer buffer=new StringBuffer();
			buffer.append(GetTcpPath.MOUSE);
			buffer.append((type==TOWER_ICE?TOWER_FIRE:type));
			if (state==LZ3) {
				buffer.append(LZ1);
			}else if (state==LY3) {
				buffer.append(LY1);
			}else {
				buffer.append(state);
			}
			buffer.append(GetTcpPath.WEI);
			sprite=SpriteFactory.Prepare(buffer.toString());
		}
		if (sprite==null) {
			image=null;
			return;
		}
		if (point==null) {
			image=null;
			return;
		}
		if (type==TOWER_FIRE||type==TOWER_ICE) {
			if (s2==null) {
				if (type==TOWER_FIRE) {
					s2=SpriteFactory.Prepare("resource/bang/BgTDbR.tcp");
				}else {
					s2=SpriteFactory.Prepare("resource/bang/BgTDbB.tcp");		
				}	
			}else {
				s2.updateToTime(time,0);
				s2.draw(g,point.getX(),point.getY());
			}
		}
		if (state==LZ3||state==LY3) {
			sprite.updateToTime(sprite.getTime()-time,0);	
		}else {
			sprite.updateToTime(time,0);	
		}		
		sprite.draw(g,point.getX(),point.getY());
		Frame frame=sprite.getCurrFrame();
		px=frame.getRefPixelX();
		py=frame.getRefPixelY();
		image=frame.getImage();
		if (v!=0) {
			if (s3==null) {
				if (v==1) {
					if (type==TOWER_DOOR) {
						s3=SpriteFactory.Prepare("resource/bang/BgTHs.tcp");	
					}else {
						s3=SpriteFactory.Prepare("resource/bang/BgTDj.tcp");		
					}	
				}else {
					if (type==TOWER_FIRE) {
						s3=SpriteFactory.Prepare("resource/bang/BgTQR.tcp");
				    }else {
				    	s3=SpriteFactory.Prepare("resource/bang/BgTQB.tcp");		
				    }
				}
			}else {
				s3.updateToTime(time,0);
				s3.draw(g,point.getX(),point.getY());
				if (time>s3.getTime()) {
					v=0;
					s3=null;
				}
			}
		}
		
	}
	//初始化
	public Build(int bh,int type,int state,int hp) {
		// TODO Auto-generated constructor stub
		
		this.bh=bh;
		this.type=type;	
		this.state=state;
		this.hp=hp;
		//龙门     307x360         
		//左门     340x1593        
		//左上火 841x1113
		//左下火 1118x1287
		//左上冰 556x1228
		//左下冰 882x1414	
		//右门     2149x552
		//右上火 1465x781
		//右下火 1767x956
		//右上冰 1701x648
		//右下冰 2001x826
		switch (bh) {
		case 0:x=307;y=360;break;
        case 1:x=340;y=1593;break;
        case 2:x=841;y=1113;break;
        case 3:x=1118;y=1287;break;
        case 4:x=556;y=1228;break;
        case 5:x=882;y=1414;break;
        case 11:x=2149;y=552;break;
        case 12:x=1465;y=781;break;
        case 13:x=1767;y=956;break;
        case 14:x=1701;y=648;break;
        case 15:x=2001;y=826;break;
		}
	}
	
	public int getBh() {
		return bh;
	}
	public void setBh(int bh) {
		this.bh = bh;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getState() {
		return state;
	}
	public void setState(int state) {
		sprite=null;
		image=null;
		this.time=1;
		this.state = state;
	}
	public String getName() {
		switch (type) {
		case 0:
			return "城门";
		case 1:
			return "火塔";
		case 2:
			return "冰塔";
		case 3:
			return "龙神大炮";	
		default:
			break;
		}
		return "";
	}
	public Image getImage() {
		return image;
	}
	public void setImage(Image image) {
		this.image = image;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public int getPx() {
		return px;
	}
	public void setPx(int px) {
		this.px = px;
	}
	public int getPy() {
		return py;
	}
	public void setPy(int py) {
		this.py = py;
	}
	public int getHp() {
		return hp;
	}
	public void setHp(int hp) {
		if (this.hp==hp) {
			if (type==TOWER_FIRE||type==TOWER_ICE) {
				this.time=1;
				this.v=2;	
			}
			return;
		}
		if (type==TOWER_DOOR||type==TOWER_FIRE||type==TOWER_ICE) {
			this.time=1;
			this.v=1;
		}
		this.hp = hp;
	}
	
}
