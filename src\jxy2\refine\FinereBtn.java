package jxy2.refine;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.GoodDetailedJframe;
import org.come.Frame.NewRefiningJframe;
import org.come.Frame.TaobaoCourtMainJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.FrameMessageChangeJpanel;
import org.come.Jpanel.NewRefiningJpanel;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.test.Main;
import org.come.until.AccessSuitMsgUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Goodtype;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* 炼器子菜单按钮
* <AUTHOR>
* @date 2024/7/26 上午6:04
*/
public class FinereBtn extends MoBanBtn {
    public ReimpJpanel refineJPanel;
    public int typeBtn;
    public FinereBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, ReimpJpanel refineJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.refineJPanel = refineJPanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn==4){
            String v = refineJPanel.detection();
            if (v.equals("炼器")) {
                String value = AccessSuitMsgUntil.getExtra(refineJPanel.goods[0].getValue(), "炼器属性");
                NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel().show(value, 1, true);
            } else if (v.equals("?")) {
                ZhuFrame.getZhuJpanel().addPrompt2("公式不对");
            } else if (v.equals("清除")) {
                cao1(refineJPanel.goods, refineJPanel.money, 3);
            } else if (v.equals("开光")) {
                cao1(refineJPanel.goods, refineJPanel.money, 0);
            }
        }else {
            refineJPanel.setMinType(typeBtn);//接装装备炼化，0-4   5开始
            EquiButtonClick(typeBtn);
            refineJPanel.clear();
//        String text  = Juitil.TextInedx(refineJPanel.getMinType());
//        refineJPanel.getRichLabel().setTextSize(text,160);
        }
    }

    public void EquiButtonClick(int clickedIndex) {
        if (clickedIndex==-1)return;
        refineJPanel.getFinerBtn()[clickedIndex].btnchange(2);
        // 重置所有按钮状态为 2（假设btnchange(2)表示非激活状态）
        for (int i = 0; i <refineJPanel.getFinerBtn().length; i++) {
            if (i != clickedIndex) {
                refineJPanel.getFinerBtn()[i].btnchange(0);
            }
        }
    }

    private NewRefiningJpanel NrJpanel;
    /** 0开光 1炼器 3清除炼器 4炼化 */
    public boolean cao1(Goodstable[] goods, BigDecimal money, int type) {
        NewRefiningJpanel NrJpanel = null;
        if (this.NrJpanel == null) {
            NrJpanel = NewRefiningJframe.getNewRefiningJframe().getRefiningJpanel();
        } else {
            NrJpanel = this.NrJpanel;
        }
        int lock = 0;
        int num = 0;
        if (type == 4 || type == 1) {
            lock = NrJpanel.getlock();
            num = lock % 10;
            lock = lock / 10;
        }
        BigDecimal xy = null;
        if (RoleData.getRoleData().getLoginResult().getGold().longValue() < money.longValue()) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y金钱不足");
            return false;
        }
        if (num > 3) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("最多只能锁定3个");
            return false;
        } else if (num > 0) {
            if (type == 4
                    && (Goodtype.GodEquipment_xian(goods[0].getType()) || Goodtype.GodEquipment_God(goods[0].getType()))) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("仙器和神兵无法锁定连化");
                return false;
            }
            if (num == 1) {
                if (type == 4) {
                    xy = new BigDecimal(100);
                } else {
                    xy = new BigDecimal(1000);
                }
            } else if (num == 2) {
                if (type == 4) {
                    xy = new BigDecimal(500);
                } else {
                    xy = new BigDecimal(3000);
                }
            } else if (num == 3) {
                if (type == 4) {
                    xy = new BigDecimal(2500);
                } else {
                    xy = new BigDecimal(6000);
                }
            }
            if (RoleData.getRoleData().getLoginResult().getCodecard().longValue() < xy.longValue()) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("仙玉不足");
                return false;
            }
        }
        List<BigDecimal> rgids = new ArrayList<>();
        int size = 0;
        int p = -1;
        for (int i = 0; i < goods.length; i++) {
            if (goods[i] != null) {
                size++;
                p = i;
                if (goods[i].getGoodlock() == 1) {
                    ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                    return false;
                }
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return false;
                }
                int sum = 1;
                for (int j = 0; j < rgids.size(); j++) {
                    if (goods[i].getRgid().compareTo(rgids.get(j)) == 0) {
                        sum++;
                    }
                }
                if (sum > goods[i].getUsetime()) {
                    ZhuFrame.getZhuJpanel().addPrompt2("请凑齐物品再来");
                    return false;
                }
                rgids.add(goods[i].getRgid());
            }
        }
        if (p + 1 != size) {
            Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
            return false;
        }
        SuitOperBean operBean = new SuitOperBean();
        if (type == 0) {
            if (size != 2) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼器属性");
            if (extra != null) {
                String[] extras = extra.split("&");
                if (Integer.parseInt(extras[1]) >= 5) {
                    FrameMessageChangeJpanel.addtext(5, "最大开光次数5", null, null);
                    return false;
                }
            }
            operBean.setType(10);
        } else if (type == 1) {
            if (size != 4) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            String extra = AccessSuitMsgUntil.getExtra(goods[0].getValue(), "炼器属性");
            if (extra == null) {
                FrameMessageChangeJpanel.addtext(5, "先去开光", null, null);
                return false;
            }
            operBean.setType(11);
        } else if (type == 3) {
            if (size != 1) {
                Main.frame.getLoginJpanel().getGameView().addPrompt("#Y请在引导界面查看公式,如果引导界面没有对应公式 联系管理员补充");
                return false;
            }
            operBean.setType(13);
        } else if (type == 4) {
            if (AccessSuitMsgUntil.getExtra(goods[0].getValue(), "套装属性") != null) {
                FrameMessageChangeJpanel.addtext(5, "套装无法炼化", null, null);
                return false;
            }
            if (Goodtype.GodEquipment_xian(goods[0].getType())) {
                if (goods[1].getType() == 7005) {// 仙器阶数
                    String god = Goodtype.StringParsing(goods[1].getValue())[1];
                    if (!god.equals("阶数=1")) {
                        FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                        return false;
                    }
                } else if (Goodtype.GodEquipment_xian(goods[1].getType())) {
                    String god = Goodtype.StringParsing(goods[1].getValue())[0];
                    if (!god.equals("阶数=1")) {
                        FrameMessageChangeJpanel.addtext(5, "使用一阶作为炼化材料太掉价了吗?", null, null);
                        return false;
                    }
                }
            }
            operBean.setType(14);
        }
        for (int i = 1; i < goods.length; i++) {
            if (goods[i] != null) {
                goods[i].goodxh(1);
                if (goods[i].getUsetime() <= 0) {
                    GoodsListFromServerUntil.Deletebiaoid(goods[i].getRgid());
                }
            }
        }
        RoleData.getRoleData().getLoginResult()
                .setGold(RoleData.getRoleData().getLoginResult().getGold().subtract(money));
        if (xy != null) {
            RoleData.getRoleData().getLoginResult()
                    .setCodecard(RoleData.getRoleData().getLoginResult().getCodecard().subtract(xy));
            operBean.setJade(new PartJade(lock, 0));
            GoodDetailedJframe.getGoodDetailedJframe().getGoodDetailedJpanel().getYonghuXianyu()
                    .setText(RoleData.getRoleData().getLoginResult().getCodecard() + "");
            TaobaoCourtMainJframe.getTaobaoCourtJframe().getTaobaoCourtMainJpanel().getJadeNum()
                    .setText(RoleData.getRoleData().getLoginResult().getCodecard() + "");
        }

        operBean.setGoods(rgids);// 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        return true;
    }
}
