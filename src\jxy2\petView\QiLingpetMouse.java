package jxy2.petView;

import com.tool.btn.MoBanBtn;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class QiLingpetMouse extends TemplateMouseListener {
    private JList<PetSkillQiLingJPanel> listpet;//
    private PetSkillsJpanel petSkillsJpanel;

    public QiLingpetMouse(JList<PetSkillQiLingJPanel> listskillQiLing,PetSkillsJpanel petSkillsJpanel) {
        this.petSkillsJpanel = petSkillsJpanel;
        listpet = listskillQiLing;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        // 获取被点击的列表项索引
        int index = listpet.locationToIndex(e.getPoint());
        if (index != -1) {
            PetSkillQiLingJPanel panel = petSkillsJpanel.getMapPetModelPanel().get(index);
            // 遍历面板中的按钮
            for (Component comp : panel.getComponents()) {
                if (comp instanceof MoBanBtn) {
                    MoBanBtn button = (MoBanBtn) comp;
                    // 检查鼠标点击位置是否在按钮范围内
                    if (button.getBounds().contains(e.getPoint())&&button.isVisible()) {
                        button.dianji();
                        break;  // 找到并处理后退出循环
                    }
                }
            }
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        // 不执行任何操作
        int index = listpet.locationToIndex(e.getPoint());
        if (index != -1) {
            PetSkillQiLingJPanel panel = petSkillsJpanel.getMapPetModelPanel().get(index);
            // 遍历面板中的按钮
            for (Component comp : panel.getComponents()) {
                if (comp instanceof MoBanBtn) {
                    MoBanBtn button = (MoBanBtn) comp;
                    button.shifang(e);
                }
            }
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        // 不执行任何操作
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {
        // 不执行任何操作
    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
