package jxy2.wormap;

import com.tool.btn.MoBanBtn;
import org.come.Jpanel.TestsmallmapJpanel;
import org.come.until.FormsManagement;
import org.come.until.Music;
import org.come.until.Util;

import java.awt.event.MouseEvent;

public class TestsmallmapBtn extends MoBanBtn {
    private TestsmallmapJpanel testsmallmapJpanel;
    private NpcQueryJPanel npcQueryJPanel;
    private int index;
    public TestsmallmapBtn(String iconpath,int type, int index,TestsmallmapJpanel testsmallmapJpanel) {
        super(iconpath,0, type);
        this.index =index ;
        this.testsmallmapJpanel =testsmallmapJpanel ;
    }
    public TestsmallmapBtn(String iconpath,int type, int index,NpcQueryJPanel npcQueryJPanel) {
        super(iconpath,0, type);
        this.index =index ;
        this.npcQueryJPanel =npcQueryJPanel ;
    }
    public TestsmallmapBtn(String iconpath, int type, String text, String prowpt, int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        this.index=index;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        NpcQueryJPanel npcQueryJPanel = NpcQueryFrame.getNpcQueryFrame().getNpcQueryJPanel();
        switch (index) {
            case 0:
                if (testsmallmapJpanel!=null){
                    testsmallmapJpanel.showArenaDownLab();
                    testsmallmapJpanel.LoadAllNPC(false,Util.mapmodel.getGamemap().getMapid());
                }
                break;
            case 1:
                break;
            case 2:
                npcQueryJPanel.showArenaDownLab();
                break;
            case 3:
                if (!FormsManagement.getframe(131).isVisible()) {
                    npcQueryJPanel.LoadNPC(Util.mapmodel.getGamemap().getMapname());
                    FormsManagement.showForm(131);
                    Music.addyinxiao("开关窗口.mp3");
                } else {
                    FormsManagement.HideForm(131);
                    Music.addyinxiao("关闭窗口.mp3");
                }
                break;
        }
    }
}
