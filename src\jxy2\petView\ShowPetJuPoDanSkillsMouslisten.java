package jxy2.petView;

import jxy2.supet.JupoDanJPanel;
import org.come.Frame.MsgJframe;
import org.come.bean.Skill;
import org.come.mouslisten.TemplateMouseListener;
import org.come.until.CutButtonImage;
import org.come.until.Util;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class ShowPetJuPoDanSkillsMouslisten extends TemplateMouseListener {
    private JupoDanJPanel jupoDanJPanel;
    private int index;
    private Skill skill;
    public ShowPetJuPoDanSkillsMouslisten(JupoDanJPanel jupoDanJPanel, int index) {
        this.jupoDanJPanel = jupoDanJPanel;
        this.index = index;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        int nx = Util.SwitchUI==1?25:0;
        int ny = Util.SwitchUI==1?-14:0;
        int x = jupoDanJPanel.getPetSkillsJpanel().positions[index][0]+nx;
        int y = jupoDanJPanel.getPetSkillsJpanel().positions[index][1]+ny;
        if (skill != null) {// 有召唤兽技能
            ImageIcon img = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(),43,43,"skill.wdf");
            jupoDanJPanel.getLabPetskills()[index].setIcon(img);
            jupoDanJPanel.getLabPetskills()[index].setBounds(x+148, y-88, 43, 43); // 设置按钮位置和大小
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        int nx = Util.SwitchUI==1?25:0;
        int ny = Util.SwitchUI==1?-14:0;
        int x = jupoDanJPanel.getPetSkillsJpanel().positions[index][0]+nx;
        int y = jupoDanJPanel.getPetSkillsJpanel().positions[index][1]+ny;
        if (skill != null) {
            ImageIcon img = CutButtonImage.getWdfPng("0x6B6B" + skill.getSkillid(), 50, 50, "skill.wdf");
            jupoDanJPanel.getLabPetskills()[index].setIcon(img);
            jupoDanJPanel.getLabPetskills()[index].setBounds(x + 145, y - 92, 50, 50); // 设置按钮位置和大小
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        if (skill!=null){
            MsgJframe.getJframe().getJapnel().SkillPrompt(skill,true);
        }
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        System.out.println("?");
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {
        System.out.println("?");
    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        System.out.println("?");
    }

    public Skill getSkill() {
        return skill;
    }

    public void setSkill(Skill skill) {
        this.skill = skill;
    }
}
