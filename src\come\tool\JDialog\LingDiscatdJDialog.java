package come.tool.JDialog;

import org.come.model.Lingbao;
import org.come.until.UserData;

import com.tool.role.RoleLingFa;

/**
 * 灵宝丢弃
 * 
 * <AUTHOR>
 */
public class LingDiscatdJDialog implements TiShiChuLi {

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		Lingbao lingbao = (Lingbao) object;
		if (tishi) {
			lingbao.setOperation("删除");
			UserData.upling(lingbao);
			RoleLingFa.getRoleLingFa().deleteling(lingbao);
		}
	}

}
