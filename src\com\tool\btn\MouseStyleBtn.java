package com.tool.btn;

import org.come.mouslisten.ChangeMouseSymbolMouslisten;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class MouseStyleBtn extends MoBanBtn{

      private String FlagMes;
    
	public MouseStyleBtn(ImageIcon[] imageIcons, int type, String FlagMes) {
		super(imageIcons,0, type);
		// TODO Auto-generated constructor stub
		this.FlagMes=FlagMes;
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void nochoose(MouseEvent e) {
		ChangeMouseSymbolMouslisten.dianji(FlagMes);
	}
}
