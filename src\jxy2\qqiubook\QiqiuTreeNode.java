package jxy2.qqiubook;

import org.come.until.UserMessUntil;

import java.util.List;

public class QiqiuTreeNode {
    private int id;                 // 唯一标识符
    private int parentId;           // 父节点ID
    private String name;            // 配置项名称
    private String type;            // 节点类型（overview/category/config）
    private String description;     // 描述说明
    private String conclusion;      // 结束语
    private String value;           // 配置值
    private String meritvalue;      // 功绩值
    private String reward;            // 奖励
    private int complete;           //已完成
    private String time;            //完成时间
    private int total_achievement;           //总功绩
    private int subclass_achievement;      //子类功绩

    private List<QiqiuTreeNode> children;

    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getParentId() { return parentId; }
    public void setParentId(int parentId) { this.parentId = parentId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }


    public List<QiqiuTreeNode> getChildren() { return children; }
    public void setChildren(List<QiqiuTreeNode> children) { this.children = children; }

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public String getMeritvalue() {
        return meritvalue;
    }

    public void setMeritvalue(String meritvalue) {
        this.meritvalue = meritvalue;
    }

    public int getComplete() {
        return complete;
    }

    public void setComplete(int complete) {
        this.complete = complete;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }




    public int getTotal_achievement() {
        // 如果是总览节点（ID=1），计算所有子节点的meritvalue总和
        if (id == 1) {
            int total = 0;
            // 获取所有节点
            for (QiqiuTreeNode node : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
                    if (node.getId()>=100&&node.getId()<=9999)
                total++;
//                if (node.getMeritvalue() != null && !node.getMeritvalue().isEmpty()) {
//                    try {
//                        total += Integer.parseInt(node.getMeritvalue().trim());
//                    } catch (NumberFormatException e) {
//                        // 记录错误但继续处理
//                        System.err.println("无效的功绩值: " + node.getMeritvalue() + " 在节点: " + node.getId());
//                    }
//                }
            }
            return total;
        }
        // 如果是分类节点（ID在2-10之间），计算该分类下所有子节点的meritvalue总和
        if (id >= 2 && id <= 10) {
            int total = 0;
            for (QiqiuTreeNode node : UserMessUntil.getAllQianqiuTree().getAllQianqiuTree().values()) {
                if (node.getParentId() == this.id && node.getMeritvalue() != null && !node.getMeritvalue().isEmpty()) {
                    try {
                        total += Integer.parseInt(node.getMeritvalue().trim());
                    } catch (NumberFormatException e) {
                        System.err.println("无效的功绩值: " + node.getMeritvalue() + " 在节点: " + node.getId());
                    }
                }
            }
            return total;
        }

        // 如果是普通节点，直接返回自己的meritvalue
        if (meritvalue != null && !meritvalue.isEmpty()) {
            try {
                return Integer.parseInt(meritvalue.trim());
            } catch (NumberFormatException e) {
                System.err.println("无效的功绩值: " + meritvalue + " 在节点: " + id);
                return 0;
            }
        }
        return 0;
    }

    public void setTotal_achievement(int total_achievement) {
        this.total_achievement = total_achievement;
    }

    public int getSubclass_achievement() {
        return subclass_achievement;
    }

    public void setSubclass_achievement(int subclass_achievement) {
        this.subclass_achievement = subclass_achievement;
    }
    
    /**
     * 从另一个节点更新当前节点的属性
     * @param other 源节点
     */
    public void updateFrom(QiqiuTreeNode other) {
        if (other == null) return;
        this.complete = other.complete;
        this.time = other.time;
        this.meritvalue = other.meritvalue;
        this.reward = other.reward;
        this.value = other.value;
        this.description = other.description;
        this.conclusion = other.conclusion;
        this.total_achievement = other.total_achievement;
        this.subclass_achievement = other.subclass_achievement;
    }
    @Override
    public String toString() {
        return name;
    }
}
