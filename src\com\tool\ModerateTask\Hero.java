package com.tool.ModerateTask;

import java.util.ArrayList;
import java.util.List;

import org.come.Frame.NPCJfram;
import org.come.Frame.TesttaskJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TesttaskJapnel;
import org.come.bean.PrivateData;
import org.come.model.InternalForm;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.thread.TimeControlRunnable;
import org.come.until.FormsManagement;

import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;

public class Hero {
	public static Hero hero;
	private List<Task> doingTasks;
	private Task BYTask;//备用对象
	public Hero() {
		doingTasks = new ArrayList<Task>();
	}
	public static List<Task> getList() {
		if (hero!=null) {
			return hero.doingTasks;
		}
		return null;
	}	
	public static Hero getHero() {
		if (hero == null){initial();}
		return hero;
	}
	public static void initial() {
		hero = new Hero();
		PrivateData data = RoleData.getRoleData().getPrivateData();
		String msg = data.getTaskData();
		if (msg == null || msg.equals("")){return;}
		String tasks[] = msg.split("\\|");
		for (int i = 0; i < tasks.length; i++) {
			String[] values = tasks[i].split("=");
			int  taskid = Integer.parseInt(values[0]);
			int  state  = Integer.parseInt(values[1]);
	        Task task=new Task(taskid, state);
			task.addProgress(values);
	        for (int j = 0; j < task.getProgress().size(); j++)
				ImageMixDeal.addMonster(task.getProgress().get(j));
			hero.doingTasks.add(task);
		}
		ZhuFrame.getZhuJpanel().getTaskGuideView().guideSX(hero.doingTasks);
	}
    /**addTask (String)*/
	public Task addTask(int taskid,int state,String[] values){
		if (state==TaskState.quxiao||state==TaskState.quxiaoTime||state==TaskState.finishTask) {
			return removeTask(taskid, state);
		}
		Task task=getTaskId(taskid);
		if (task!=null) {
			removeYG(task);
			task.addProgress(values);
		}else {
			task=createTask(taskid, state);
			task.addProgress(values);
			hero.doingTasks.add(task);
			ZhuFrame.getZhuJpanel().addPrompt2("领取了" + task.getTaskData().getTaskName());
		}
		for (int j = 0; j < task.getProgress().size(); j++)
			ImageMixDeal.addMonster(task.getProgress().get(j));
		return task;
	}
	/**addTask (String)*/
	public void addTask(String msg){
		if (msg==null||msg.equals("")) {return;}
		TesttaskJapnel taskJapnel=null;
		boolean is=false;
		InternalForm form=FormsManagement.getInternalForm2(3);
		if (form!=null) {
			taskJapnel=((TesttaskJframe)form.getFrame()).getJtask();
		}
		//C修改任务记录=R=L=N1000   R 添加领取次数 L添加完成次数 N修改最新进度
		//任务id=任务状态=T任务过期时间=D任务完成数据刷新
		String[] mes=msg.split("\\|");
		for (int z = 0; z < mes.length; z++) {
			String[] values=mes[z].split("=");
			if (values[0].startsWith("C")) {
				values[0]=values[0].substring(1);
				TaskRoleData.upTaskComplete(values);
			}else {
				int  taskid = Integer.parseInt(values[0]);
				int  state  = Integer.parseInt(values[1]);
				Task task=addTask(taskid,state,values);
                if (taskJapnel!=null&&taskJapnel.getTaskId()==taskid) {is=true;}	
				if (task!=null) {
					TimeControlRunnable.upTask(task, state);	
				}
			}	
		}	
		if (taskJapnel!=null) {
			if (form.getFrame().isVisible()) {
				taskJapnel.showTaskMethod();	
			}
			if (is) {
				taskJapnel.taskShow(null,null);
			}
		}
		ZhuFrame.getZhuJpanel().getTaskGuideView().guideSX(doingTasks);
	}
	/***/
	public synchronized Task createTask(int id,int state){
		Task task=null;
		if (BYTask!=null) {
			task=BYTask;
			BYTask=null;
		}else {
			task=new Task(id,state);
		}
		task.setTaskId(id);
		task.setTaskState(state);
		task.reset();
		return task;
	}
    public Task removeTask(int id,int type){
    	for (int i = 0; i < doingTasks.size(); i++) {
    		Task task=doingTasks.get(i);
    		if (task.getTaskId()==id) {
    			removeYG(task);
    			BYTask=doingTasks.remove(i);
    			if (type==TaskState.quxiaoTime) {
    				ZhuFrame.getZhuJpanel().addPrompt2("你的"+task.getTaskData().getTaskName()+"任务因为超时导致失败");
				}else if (type==TaskState.quxiao) {
					ZhuFrame.getZhuJpanel().addPrompt2("取消了"+task.getTaskData().getTaskName());
				}else if (type==TaskState.finishTask) {
					ZhuFrame.getZhuJpanel().addPrompt2("完成了"+task.getTaskData().getTaskName());
					if (task.getTaskData().getTaskEnd()!=null&&!task.getTaskData().getTaskEnd().equals("")) {
						NPCJfram.getNpcJfram().getNpcjpanel().taskend(task.getTaskData().getTaskEnd());
					}
				}
    			return task;
			}
    	}
    	return null;
    }

	public void removeYG(Task task) {
		for (int j = 0; j < task.getProgress().size(); j++) {
			TaskProgress progress = task.getProgress().get(j);
			ImageMixDeal.removeMonster(progress);
		}
	}

	public void OverTime() {
		StringBuffer buffer=null;
		for (int i = doingTasks.size() - 1; i >= 0; i--) {
			Task task = doingTasks.get(i);
			if (task.isOverTime()) {
				if (buffer==null) {buffer=new StringBuffer("T");}
				else {buffer.append("|");}
				buffer.append(task.getTaskId());
			}
		}
		if (buffer!=null) {
			String mes = Agreement.getAgreement().TaskNAgreement(buffer.toString());
			SendMessageUntil.toServer(mes);
		}
	}
	/** 获取当前正在进行的任务id */
	public Task getTaskId(int id) {
		for (int i = doingTasks.size() - 1; i >= 0; i--) {
			if (doingTasks.get(i).getTaskId() == id) {
				return doingTasks.get(i);
			}
		}
		return null;
	}
	/**根据setID 获取任务*/
	public Task getSetTask(int setID) {
		for (int i = 0; i < doingTasks.size(); i++) {
			int setId=doingTasks.get(i).getTaskData().getTaskSetID();
			if (setId==setID) {
				return doingTasks.get(i);
			}
		}
		return null;
	}
	/**判断问候给与是否有影响*/
	public Task PartFinish(String type,String name){
		for (int i = doingTasks.size() - 1; i >= 0; i--) {
			Task task = doingTasks.get(i);
			int v=task.PartFinish(type,name);
			if (v!=0) {return task;}
		}
		return null;
	}
	public List<Task> getDoingTasks() {
		return doingTasks;
	}
	/**获取所有的setid*/
    public void getSetId(List<Integer> list){
    	S:for (int i = 0; i < doingTasks.size(); i++) {
			int setId=doingTasks.get(i).getTaskData().getTaskSetID();
			if (list.contains(setId)) {continue;}
			for (int j = 0; j < list.size(); j++) {
				if (list.get(j)<setId) {
					list.add(j,setId);
					continue S;
				}
			}
			list.add(setId);
		}
    }
    public Task getTaskSet(int setID){
    	for (int i = doingTasks.size() - 1; i >= 0; i--) {
			if (doingTasks.get(i).getTaskData().getTaskSetID() == setID) {
				return doingTasks.get(i);
			}
		}
		return null;
    }
}