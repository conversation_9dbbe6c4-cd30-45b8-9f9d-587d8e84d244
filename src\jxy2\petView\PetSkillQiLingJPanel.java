package jxy2.petView;

import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.supet.SipetUtil;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.until.CustomFunction;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.Arrays;

public class PetSkillQiLingJPanel extends JPanel {
    private RichLabel box;
    public PetSkillsJpanel petSkillsJpanel;
    public PetSkillQiLingBtn btndomesticated,btnwuling;
    private int boxheight,boxheightone;
    public PetSkillQiLingJPanel() {
        this.setPreferredSize(new Dimension(222-11, 500));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        getBtndomesticated();
        getBtnwuling();
        box = new RichLabel();
        add(box);

    }

    public void skillmsg(Skill skill, RoleSummoning pet,PetSkillsJpanel petSkillsJpanel) {
        box.setText("");
        this.petSkillsJpanel = petSkillsJpanel;
        if (skill!=null){

            String msg = skill.getRemark();
            double sv = Double.parseDouble(skill.getGrow());// 技能成长
            long qm = UserMessUntil.getChosePetMes().getFriendliness();
            double value = Double.parseDouble(skill.getValue());// 介值
            //天降流火
            if (skill.getSkillid().equals("1237")) {
                msg = msg.replace("{0}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{1}", String.format("%.2f", 25 + CustomFunction.XS(qm, sv)));
                //归去来兮
                //一御当千
            } else if (skill.getSkillid().equals("1238") || skill.getSkillid().equals("1240")) {
                msg = msg.replace("{0}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{1}", String.format("%.2f", Double.parseDouble(skill.getValue1()) + CustomFunction.XS(qm, Double.parseDouble(skill.getGrow1()))));
            } else if (skill.getSkillid().equals("1241")) {
                msg = msg.replace("{0}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
            } else if (skill.getSkillid().equals("1216")) {
                msg = msg.replace("{0}", "2");
                msg = msg.replace("{1}", String.format("%.0f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{2}", String.format("10", value + CustomFunction.XS(qm, sv)));
            } else if (skill.getSkillid().equals("1256")) {
                msg = msg.replace("{0}", String.format("%.1f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{1}", String.format("%.1f", value + CustomFunction.XS(qm, sv)));
            } else if (skill.getSkillid().equals("1257")) {
                msg = msg.replace("{0}", value + CustomFunction.XS(qm, sv)+"");
            } else if (skill.getSkillid().equals("1827")) {
                msg = msg.replace("{0}", "#R 170");
            } else if (skill.getSkillid().equals("1885")) {
                msg = msg.replace("{0}", "#R {200}");
            }else if (skill.getSkillid().equals("1255")) {
                msg = msg.replace("{0}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{1}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
                msg = msg.replace("{2}", String.format("%.2f", value + CustomFunction.XS(qm, sv)));
            }else if (skill.getSkillid().equals("1820")||skill.getSkillid().equals("1821")||
                    skill.getSkillid().equals("1822")||skill.getSkillid().equals("1823")||
                    skill.getSkillid().equals("1824")) {
                msg = msg.replace("{0}", String.format(" #R%.1f", value + CustomFunction.XS(qm, sv)));
            }else if (skill.getSkillid().equals("1802")) {//神功鬼力
                //增加召唤兽AP上限(受召唤兽等级 转生次数 亲密等影响)。
                double ap = PetSkillsJpanel.calculateAttackBonus(pet.getGrade(), pet.getTurnRount(), pet.getFriendliness());
                msg = msg.replace("{0}", String.format("%.0f", ap));
            }else if (skill.getSkillid().equals("1800")) {
                double hpLimit = PetSkillsJpanel.calculateHPLimit(pet.getTurnRount(), pet.getGrade(), pet.getFriendliness(),0);
                msg = msg.replace("{0}", String.format("%.0f", hpLimit));
            }
            String[] v = PetSkillsJpanel.StringReplace(msg).split("\\|");

            int i = 0;
            while (i < v.length) {
                String vg;
                String[] v1 = v[i].split("=");
                if ((vg = this.gongshi(v, i)) != null) {
                    box.addText("#c" + v1[0] + " " + v1[1] + "#c" + v[++i].split("=")[0] + vg + "#r");
                }
                else {
                    box.addText("#c" + v1[0] + " " + v1[1] + "#r",UIUtils.TEXT_FONT1);
                }
                ++i;
            }

            box.addText("#r ");
            box.addText("#r ");
            box.addText("#r ");

            box.setBounds(0,0,190,500);


            Dimension d = box.computeSize(190);
            box.setSize(d);
            box.setPreferredSize(d);
            setBoxheight(box.getHeight());
            boolean containsId = Arrays.stream(SipetUtil.lingfan).anyMatch(id -> id == Integer.parseInt(skill.getSkillid()));
            if (containsId) {
                boolean enabled = PetSkillQiLingBtn.isSkillLingjieEnabled(pet, skill.getSkillid());
                msg =enabled?"#W技能处于#G悟灵状态#r ":"技能#G可以开启悟灵#r ";
                box.addText("#W   "+msg);
                //获取技能灵阶效果
                msg = btndomesticated.getText().equals("关闭")?"#r #r #r":"";
                if (enabled){
                    box.addText(msg+SipetUtil.SpiritLevelEffect(skill.getSkillid(),pet,0));
                }else {
                    box.addText(SipetUtil.SpiritLevelEffect(skill.getSkillid(),pet,1));
                }
                btndomesticated.setVisible(true);
                btnwuling.setVisible(pet.getQiling()>0);
            }else {
                box.addText("#c939893      此技能无法悟灵#r");
                btndomesticated.setVisible(false);
                btnwuling.setVisible(false);
            }


            d = box.computeSize(185);
            box.setSize(d);
            box.setPreferredSize(d);
            boxheightone = box.getHeight();
            int sum = Util.SwitchUI==1?8:5;
            box.setBounds(sum,0,185,boxheightone);
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (box.getText()==null)return;
        if (petSkillsJpanel!=null&&!petSkillsJpanel.getPetskillID().isEmpty()) {
            int sum = Util.SwitchUI==1?6:2;
            boolean containsId = Arrays.stream(SipetUtil.lingfan).anyMatch(id -> id == Integer.parseInt(petSkillsJpanel.getPetskillID()));
            int openqid = UserMessUntil.getChosePetMes().getQiling();
            if (containsId) {
                Juitil.ImngBack(g, Util.SwitchUI==1?Juitil.tz128:Juitil.red_0018, sum, boxheight-29, 189+sum, openqid>0?(boxheightone-100):45, 1);
                g.drawImage(Juitil.tz100.getImage(),sum+5, boxheight-18, 17, 18, null);
                g.drawImage(Juitil.tz19.getImage(),sum+2, boxheight+5, 190, 5, null);
                btndomesticated.setBounds(sum+150,boxheight-18,34,18);
                if (btndomesticated.getText().equals("关闭")) {
                    int level = PetSkillQiLingBtn.getSkillLingjieLevel(UserMessUntil.getChosePetMes(), petSkillsJpanel.getPetskillID());
                    btnwuling.setBounds(sum+150, boxheight + 15, 34, 18);
                    g.drawImage(Juitil.tz336.getImage(), sum+45, boxheight + 16, 100, 16, null);
                    g.drawImage(Juitil.tz337.getImage(), sum+48, boxheight + 19, Math.max(9, (level*10)-6), 10, null);
                    Juitil.TextBackground(g, "等级", 13, sum+12, boxheight + 16, UIUtils.COLOR_White, UIUtils.TEXT_FONT2, UIUtils.COLOR_NAME8);
                    Juitil.TextBackground(g, level+"/10",
                            13, sum+80, boxheight + 16, UIUtils.COLOR_White, UIUtils.FZCY_HY14, UIUtils.COLOR_NAME8);
                }
                btnwuling.setVisible(!box.getText().contains("已达最高级")&&openqid>0&&btndomesticated.getText().equals("关闭"));
            }else {

                Juitil.ImngBack(g,Util.SwitchUI==1?Juitil.tz128:Juitil.red_0018, sum, box.getHeight()-30, 189, 45, 1);
            }
        }


    }

    public String gongshi(String[] v, int i) {
        // 判断是否有下一个
        if (v.length > i + 1) {
            String[] vs = v[i + 1].split("=");
            if (vs.length > 1) {
                if (vs[1].equals("{公式一}") || vs[1].equals("{公式二}") || vs[1].equals("{公式三}") || vs[1].equals("{公式四}")
                        || vs[1].equals("{公式五}")) {
                    return 1 + "";
                }
            }
        }
        return null;
    }

    public RichLabel getBox() {
        return box;
    }

    public void setBox(RichLabel box) {
        this.box = box;
    }

    public PetSkillQiLingBtn getBtndomesticated() {
        if (btndomesticated == null){
            btndomesticated = new PetSkillQiLingBtn(ImgConstants.tz114, 1, "开启",1,"",this);
            btndomesticated.setVisible(false);
            btndomesticated.setBounds(0,2,34,18);
            add(btndomesticated);
        }
        return btndomesticated;
    }

    public void setBtndomesticated(PetSkillQiLingBtn btndomesticated) {
        this.btndomesticated = btndomesticated;
    }

    public PetSkillQiLingBtn getBtnwuling() {
        if (btnwuling == null){
            btnwuling = new PetSkillQiLingBtn(ImgConstants.tz114, 1, "悟灵",2,"",this);
            btnwuling.setVisible(false);
            btnwuling.setBounds(0,2,34,18);
            add(btnwuling);
        }
        return btnwuling;
    }

    public void setBtnwuling(PetSkillQiLingBtn btnwuling) {
        this.btnwuling = btnwuling;
    }

    public PetSkillsJpanel getPetSkillsJpanel() {
        return petSkillsJpanel;
    }

    public void setPetSkillsJpanel(PetSkillsJpanel petSkillsJpanel) {
        this.petSkillsJpanel = petSkillsJpanel;
    }

    public int getBoxheight() {
        return boxheight;
    }

    public void setBoxheight(int boxheight) {
        this.boxheight = boxheight;
    }

    public int getBoxheightone() {
        return boxheightone;
    }

    public void setBoxheightone(int boxheightone) {
        this.boxheightone = boxheightone;
    }
}
