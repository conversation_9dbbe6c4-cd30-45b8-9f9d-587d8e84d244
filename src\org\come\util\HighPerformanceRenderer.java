package org.come.util;

import org.come.login.GameView;
import javax.swing.SwingUtilities;

/**
 * 高性能渲染器 - 专用线程实现真正的144FPS
 */
public class HighPerformanceRenderer {
    
    private static HighPerformanceRenderer instance;
    private Thread renderThread;
    private volatile boolean isRunning = false;
    private volatile boolean isPaused = false;
    private GameView gameView;
    private DirectRenderCanvas directCanvas;
    private volatile boolean useDirectRender = false;
    
    // 精确的FPS控制
    private volatile int targetFPS = 144;
    private volatile long targetFrameTimeNanos = 6944444L; // 1秒/144帧 的纳秒数
    
    // 性能统计
    private long frameCount = 0;
    private long lastStatsTime = System.nanoTime();
    private double actualFPS = 0;
    
    private HighPerformanceRenderer() {}
    
    public static HighPerformanceRenderer getInstance() {
        if (instance == null) {
            instance = new HighPerformanceRenderer();
        }
        return instance;
    }
    
    /**
     * 设置GameView
     */
    public void setGameView(GameView gameView) {
        this.gameView = gameView;
    }

    /**
     * 设置直接渲染Canvas
     */
    public void setDirectCanvas(DirectRenderCanvas canvas) {
        this.directCanvas = canvas;
    }

    /**
     * 启用直接渲染模式
     */
    public void enableDirectRender(boolean enable) {
        this.useDirectRender = enable;
        System.out.println("[高性能渲染] " + (enable ? "启用" : "禁用") + "直接渲染模式");
    }
    
    /**
     * 启动高性能渲染
     */
    public void start() {
        if (isRunning) {
            return;
        }
        
        isRunning = true;
        isPaused = false;
        
        System.out.println("[高性能渲染] 启动144FPS渲染线程");
        System.out.println("[高性能渲染] 目标帧时间: " + (targetFrameTimeNanos/1000000.0) + "ms");
        
        renderThread = Thread.ofVirtual().name("HighPerformanceRenderer").start(() -> {
            renderLoop();
        });
    }
    
    /**
     * 停止渲染
     */
    public void stop() {
        isRunning = false;
        if (renderThread != null) {
            try {
                renderThread.join(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        System.out.println("[高性能渲染] 渲染线程已停止");
    }
    
    /**
     * 暂停/恢复渲染
     */
    public void setPaused(boolean paused) {
        this.isPaused = paused;
        System.out.println("[高性能渲染] " + (paused ? "暂停" : "恢复") + "渲染");
    }
    
    /**
     * 设置目标FPS
     */
    public void setTargetFPS(int fps) {
        if (fps > 0 && fps <= 300) {
            this.targetFPS = fps;
            this.targetFrameTimeNanos = 1000000000L / fps;
            System.out.println("[高性能渲染] 设置目标FPS: " + fps + 
                " (帧时间: " + (targetFrameTimeNanos/1000000.0) + "ms)");
        }
    }
    
    /**
     * 核心渲染循环
     */
    private void renderLoop() {
        long lastFrameTime = System.nanoTime();
        
        while (isRunning) {
            try {
                long currentTime = System.nanoTime();
                
                // 如果没有暂停，触发渲染
                if (!isPaused) {
                    if (useDirectRender && directCanvas != null) {
                        // 直接渲染模式 - 绕过Swing
                        directCanvas.directRender();
                    } else if (gameView != null) {
                        // 传统Swing渲染模式
                        SwingUtilities.invokeLater(() -> {
                            if (gameView != null) {
                                gameView.repaint();
                            }
                        });
                    }

                    frameCount++;
                    
                    // 每秒统计一次实际FPS
                    if (currentTime - lastStatsTime >= 1000000000L) {
                        actualFPS = frameCount * 1000000000.0 / (currentTime - lastStatsTime);
                        System.out.println("[高性能渲染] 实际FPS: " + String.format("%.1f", actualFPS) + 
                            " / " + targetFPS);
                        frameCount = 0;
                        lastStatsTime = currentTime;
                    }
                }
                
                // 精确的帧时间控制
                long frameTime = currentTime - lastFrameTime;
                long sleepTime = targetFrameTimeNanos - frameTime;
                
                if (sleepTime > 0) {
                    // 使用纳秒级精确睡眠
                    long sleepMs = sleepTime / 1000000;
                    int sleepNs = (int) (sleepTime % 1000000);
                    
                    if (sleepMs > 0) {
                        Thread.sleep(sleepMs, sleepNs);
                    } else if (sleepNs > 0) {
                        // 短时间用自旋等待，更精确
                        long spinStart = System.nanoTime();
                        while (System.nanoTime() - spinStart < sleepNs) {
                            Thread.onSpinWait(); // Java 9+ 的自旋等待优化
                        }
                    }
                } else {
                    // 如果渲染太慢，让出CPU
                    Thread.yield();
                }
                
                lastFrameTime = System.nanoTime();
                
            } catch (Exception e) {
                System.err.println("[高性能渲染] 渲染循环异常: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 获取实际FPS
     */
    public double getActualFPS() {
        return actualFPS;
    }
    
    /**
     * 获取目标FPS
     */
    public int getTargetFPS() {
        return targetFPS;
    }
    
    /**
     * 是否正在运行
     */
    public boolean isRunning() {
        return isRunning;
    }
    
    /**
     * 是否暂停
     */
    public boolean isPaused() {
        return isPaused;
    }
}
