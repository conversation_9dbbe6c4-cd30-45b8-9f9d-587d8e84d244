package jxy2.guaed.fl;

import org.come.Frame.ZhuFrame;
import org.come.mouslisten.TemplateMouseListener;

import java.awt.event.MouseEvent;

public class AsoulMousetr extends TemplateMouseListener{
    public AsoulJPanel asoulJPanel;
    public int i;
    public AsoulMousetr(AsoulJPanel asoulJPanel, int i) {
        this.asoulJPanel = asoulJPanel;
        this.i = i;
    }
    @Override
    protected void specificMousePressed(MouseEvent e) {
        if (i==0) {
            asoulJPanel.getAddGoodsImg().setBounds(71, 170, 48, 48);
        }
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {
        if (i==0){
            asoulJPanel.setComponentVisible(true,null);
            asoulJPanel.getAddGoodsImg().setBounds(70, 169, 48, 48);
        }
    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {
        if (i==0) {
            if (asoulJPanel.getGoodstable()!=null&&asoulJPanel.getAddGoodsImg().getIcon()!=null){
                ZhuFrame.getZhuJpanel().creatgoodtext(asoulJPanel.getGoodstable());
            }
        }else {
            if (asoulJPanel.getLyjgoods()!=null){
                ZhuFrame.getZhuJpanel().creatgoodtext(asoulJPanel.getLyjgoods());
            }
        }
    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        ZhuFrame.getZhuJpanel().cleargoodtext();
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
