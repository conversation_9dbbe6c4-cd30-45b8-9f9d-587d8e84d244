package com.tool.Stall;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
import org.come.bean.ImgZoom;
import org.come.bean.PathPoint;
import org.come.until.Util;

import java.awt.*;
import java.math.BigDecimal;

public class StallBean {
    // 试营业
    public static int PREPARE = 0;
    // 营业
    public static int OFF = 1;
    // 托管
    public static int MANAGE = 2;
    // 关门
    public static int NO = 3;
    // 记录摆摊id
    private int id;
    // 摊位所在的地图id
    private int mapid;
    // 记录摊位人
    private String role;
    private BigDecimal roleid;
    // 记录摊位名
    private String stall = "太1的摊位";
    // 记录摊位状态
    private int state;
    // 记录位置
    private int x;
    private int y;
    private ImgZoom imgZoom;

    public StallBean() {
        // TODO Auto-generated constructor stub
    }
    public StallBean(Stall stall) {
        // TODO Auto-generated constructor stub
        this.id = stall.getId();
        this.mapid = stall.getMapid();
        this.role = stall.getRole();
        this.roleid = stall.getRoleid();
        this.stall = stall.getStall();
        this.state = stall.getState();
    }
    public static String path = "skin/300040/stand.tcp";
    // 绘制
    int size = 0;
    public void draw(Graphics g) {
        PathPoint point = Util.mapmodel.path(x, y);
        if (point != null) {
            g = g.create();
            g.translate(point.getX(), point.getY());
            if (imgZoom == null) {
//                imgZoom = CutButtonImage.cuts("inkImg/background/S148.png", 7, 7, true);
                size = org.come.until.SafeFontMetrics.getFontMetrics(g).stringWidth(stall);
                if (size < 96) {
                    imgZoom.setMiddlew(96);
                    size = 48 - size / 2 + 8;
                } else {
                    imgZoom.setMiddlew(size);
                    size = 8;
                }
                imgZoom.setMiddleh(org.come.until.SafeFontMetrics.getFontMetrics(g).getHeight()-4);
            }
            imgZoom.draw(g);
            g.drawString(stall, size, 20);
            if (state == MANAGE) {
                Sprite role = SpriteFactory.Prepare(path);
                if (role != null) {
                    role.updateToTime(ImageMixDeal.userimg.getTime(), 4);
                    role.draw(g, 50, 135);
                }
            }
            g.dispose();
        }
    }

    // 判断是否点中
    public boolean isDJ(int dx, int dy) {
        dx -= x;
        dy -= y;
        if (imgZoom != null) {
            if ((dx >= 0 && dx < imgZoom.getEdgew() * 2 + imgZoom.getMiddlew())
                    && (dy >= 0 && dy < imgZoom.getEdgeh() * 2 + imgZoom.getMiddleh())) {
                return true;
            }
        }
        return false;

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getMapid() {
        return mapid;
    }

    public void setMapid(int mapid) {
        this.mapid = mapid;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public BigDecimal getRoleid() {
        return roleid;
    }

    public void setRoleid(BigDecimal roleid) {
        this.roleid = roleid;
    }

    public String getStall() {
        return stall;
    }

    public void setStall(String stall) {
        this.stall = stall;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }
}
