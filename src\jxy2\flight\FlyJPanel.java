package jxy2.flight;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.NewPart;
import com.tool.tcp.SpriteFactory;
import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.jutnil.Juitil;
import org.come.action.FlyControl;
import org.come.bean.ImgZoom;
import org.come.until.CutButtonImage;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

//山河
public class FlyJPanel extends JPanel {
    public FlyBtn sortGoods, assemb,service,supplement,advanced,destroy;// 服务;
    private static List<Fly> flyList = new ArrayList<Fly>(); // 用来存放自己的飞行器集合
    private JLabel[] flyicon = new JLabel[6];
    private JLabel[] flyiconback = new JLabel[6];
    private JLabel[] equipicon = new JLabel[6];
    private BigDecimal assemble = new BigDecimal(0);
    public FlyJPanel() {
        this.setPreferredSize(new Dimension(489, 452));
        this.setLayout(null);  // 使用绝对布局
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this, 148, 489);
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        sortGoods = new FlyBtn(iconResource, 1, "飞行", 0, this);
        sortGoods.setBounds(235, 400, 59, 24);
        this.add(sortGoods);

        assemb = new FlyBtn(iconResource, 1, "装备", 1, this);
        assemb.setBounds(335, 400, 59, 24);
        this.add(assemb);

        String leng34_18 = Util.SwitchUI==1  ? "0x6FEB8614": "0x6FAB1014";
        service = new FlyBtn(leng34_18, 1, "修炼",2);
        service.setBounds(206+46 * 2, 220, 42, 18);
        this.add(service);

        supplement = new FlyBtn(leng34_18, 1, "补充",3);
        supplement.setBounds(206+46 * 2, 220, 42, 18);
        this.add(supplement);

        destroy = new FlyBtn(leng34_18, 1, "销毁",5);
        destroy.setBounds(206+46 * 2, 220, 42, 18);
        this.add(destroy);

        advanced = new FlyBtn(iconResource, -1, "升阶", 4, this);
        advanced.setBounds(335, 400, 59, 24);
        this.add(advanced);



        for (int i = 0; i < flyicon.length; i++) {
            flyicon[i] = new JLabel();
            flyicon[i].setBounds(79+i*66, 304, 51, 51);
            flyicon[i].addMouseListener(new FlightJMouse(this,i));
            this.add(flyicon[i]);

            flyiconback[i] = new JLabel();
            flyiconback[i].setBounds(75+i*66, 301, 62, 62);
            flyiconback[i].addMouseListener(new FlightJMouse(this,i));
            this.add(flyiconback[i]);

            equipicon[i] = new JLabel();
            equipicon[i].setBounds(113+i*66, 304, 20, 20);
            equipicon[i].setIcon(CutButtonImage.getWdfPng("0x6FEB9086",20,20,"defaut.wdf"));
            equipicon[i].setVisible(false);
            add(equipicon[i]);

        }
    }


    public void InitializeAircraftData(){
        if (flyList!=null){
            for (int i = 0; i < flyList.size(); i++) {
                Fly fly = flyList.get(i);

                equipicon[i].setVisible(fly.getFlyEquip() == 1);
                if (fly.getFlyEquip() == 1){
                    setAssemble(fly.getFlyid());
                    flyiconback[i].setBorder(BorderFactory.createLineBorder(Color.yellow,2));
                    setfkypart(FlyControl.GetFlyName(fly.getFlyname())+fly.getFlysteps());
                    updateCharacter(fly);
                    FlyPracticeFrame.getFlyPracticeFrame().getFlyPracticeJPanel().initialization(fly);
                    if (fly.getFlylv()>=getTSExp(fly.getFlysteps())&&fly.getFlysteps()<5){
                        advanced.setBtn(1);
                    }
                }else {
                    flyiconback[i].setBorder(BorderFactory.createEmptyBorder());
                }
                flyicon[i].setIcon(CutButtonImage.getWdfPng(FindTheCorrespondingIconBasedOnTheName(fly.getFlyname())+fly.getFlysteps(),51, 51,"defaut.wdf"));
            }
        }
    }
    public String flyname,flysteps,flylv,flyexe,flysp,flyconsume,flyBsv;
    /**更新最新数据字符*/
    public void updateCharacter(Fly fly) {
        if (fly==null)return;
        flyname = fly.getFlyname();
        flysteps = fly.getFlysteps()+"";
        flylv = fly.getFlylv()+"";
        flyexe = fly.getFlyexe()+"";
        flysp = fly.getFlysp()+"";
        flyconsume = fly.getFlyconsume()+"";
        flyBsv = fly.getFlyBsv()+"";
    }
    /**根据ID回去飞行器*/
    public static Fly FindAircraftById(BigDecimal flyid){
        for (Fly fly : flyList) {
            if (fly.getFlyid().compareTo(flyid) == 0) {
                return fly;
            }
        }
        return null;
    }


    public String FindTheCorrespondingIconBasedOnTheName(String flyname){
            switch (flyname){
                case "富贵锦":
                    return "0x6FEB903";
                case "净心荷":
                    return "0x6FEB904";
                case "奔云燕":
                    return "0x6FEB905";
                case "香叶扇":
                    return "0x6FEB906";
                case "轻鸿羽":
                    return "0x6FEB907";
                default://筋斗云
                    return "0x6FEB908";
            }
    }
    /**
     * 获取从当前等级升级到下一级所需的经验值
     * 1级升2级：16点
     * 2级升3级：32点
     * 3级升4级：64点
     * 以此类推，每升一级需要的经验值是上一级的2倍
     * @param lvl 当前等级
     * @return 升级到下一级所需的经验值
     */
    public static int getTSExp(int lvl) {
        if (lvl ==5)return 128;
        // 1级升2级需要16点经验，2级升3级需要32点，3级升4级需要64点...
        // 公式：16 * 2^(currentLevel-1)
        return 16 * (1 << (lvl - 1));
    }

    /**
     * 获取从当前等级升级到下一级所需的灵兔绒数量
     * 一阶升二阶：1个
     * 二阶升三阶：3个
     * 三阶升四阶：9个
     * 四阶升五阶：27个
     * 以此类推，每升一阶需要的灵兔绒数量是上一阶的3倍
     * @param currentTier 当前阶数（1-5）
     * @return 升级到下一阶所需的灵兔绒数量，如果当前阶数小于1或大于等于5则返回0
     */
    public static int getTSLingRu(int currentTier) {
        // 一阶升二阶：1个
        // 二阶升三阶：3个
        // 三阶升四阶：9个
        // 四阶升五阶：27个
        if (currentTier < 1 || currentTier >= 5) {
            return 0;  // 无效阶数或已达最高阶
        }
        return (int) Math.pow(3, currentTier - 1);  // 3^(currentTier-1)

    }




    public void setfkypart(int assemble) {
            String flyPath = "res/fly/fly" + assemble + "/stand.tcp";
            this.flyPart = SpriteFactory.createPart(flyPath, -3, 1, null);
    }

    public NewPart part,flyPart;
    public String[] datatext = {"技能等级","灵 动 值","飞行速度","当前燃灵值","每分消耗燃灵值"};  // 属性文本顺序与界面布局一致
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        // 绘制一条蓝色的水平线

        if (Util.SwitchUI == 2) {
            Juitil.RedMuNewShow(g, getWidth(), getHeight(), "飞 行");
        } else {
            Juitil.SheNewShow(g, 515, getHeight(), "飞 行", 337, 86);
        }
        // 使用简化的绘制方法  燃灵值
        UiBack.UIResource.IMG_ZOOM.draw(g, this);
        UiBack.UIResource.BACK.draw(g, this);
        int[] xArray = {48, 74, 48};  // 所有样式都使用相同的X起始坐标
        int[] yArray = {313, 300, 313};  // 默认样式=313, She样式=300, Red样式=313
        int[] spacingArray = {7, 7, 7};  // 所有样式都使用相同的间距
        // 调用新方法
        UiBack.UIResource.BK_59X58.drawRow(g, xArray, yArray, 59, 58, 6, spacingArray);
        if (ImageMixDeal.userimg != null) {
            int  fnxs = Util.SwitchUI==1?0:-20;
            int  fnys = Util.SwitchUI==1?0:+5;
           if (flyPart != null) {
               flyPart.drawFly(g, 170+fnxs, 158+fnys, 8, ImageMixDeal.userimg.getTime(), 1F);
           }
            int  nxs = Util.SwitchUI==1?28:0;
            int  nys = Util.SwitchUI==1?-14:-10;
            long se = ImageMixDeal.userimg.getRoleShow().getSpecies_id().longValue();
            if (part == null) {
                part = SpriteFactory.createPart(se, 2, 1, null);
            }
            part.draw(g, 138 + nxs, 170 + nys, 8, ImageMixDeal.userimg.getTime());
        }

        // 定义UI样式相关常量
        boolean isSheStyle = Util.SwitchUI == 1;  // 是否是山河样式
        ImgZoom backImage = isSheStyle ? Juitil.tz128 : Juitil.red_0043;  // 背景图片
        int backHeight = isSheStyle ? 19 : 22;  // 背景高度
        
        // 定义UI元素位置和大小的配置
        int[][] backConfigs = {
            // xShe, xRed, yShe, yRed, width, description
            {159, 132, 240, 253, 114},  // 当前燃烧值
            {366, 339,  78,  91, 104},  // 技能等级
            {366, 339, 102, 115, 104},  // 灵动值
            {355, 332, 196, 209,  57},  // 绒毛消耗
            {199, 172, 264, 277,  74}   // 每分钟消耗
        };
        
        // 批量绘制背景
        for (int[] config : backConfigs) {
            int x = isSheStyle ? config[0] : config[1];
            int y = isSheStyle ? config[2] : config[3];
            int width = config[4];
            Juitil.ImngBack(g, backImage, x, y, width, backHeight, 1);
        }
        //名字阶数绘制
        if (flyname!=null&&!flyname.isEmpty()) {
            int  nxs = Util.SwitchUI==1?28:0;
            int  nys = Util.SwitchUI==1?-14:0;

            Juitil.TextBackground(g, flyname + "(" + Util.number2Chinese(Integer.parseInt(flysteps)) + "阶)", 16,
                    306+nxs, 55+nys,
                    Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1, UIUtils.MSYH_HY16B, UIUtils.Color_BACK);
            Juitil.TextBackground(g,flylv+"/"+ getTSExp(Integer.parseInt(flysteps)),15,
                    340+nxs,91+nys,
                    UIUtils.COLOR_NAME3,UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            Juitil.TextBackground(g,"0/300",15,
                    340+nxs,115+nys,
                    UIUtils.COLOR_NAME3,UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            Juitil.TextBackground(g,"0/"+getTSLingRu(Integer.parseInt(flysteps)),15,
                    344+nxs,209+nys,
                    UIUtils.COLOR_NAME3,UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            Juitil.TextBackground(g,flysteps,15,
                    175+nxs,277+nys,
                    UIUtils.COLOR_White,UIUtils.LiSu_LS15, UIUtils.Color_BACK);
            Juitil.TextBackground(g,flyBsv,15,
                    135+nxs,253+nys,
                    UIUtils.COLOR_White,UIUtils.LiSu_LS15, UIUtils.Color_BACK);
                    int steps = Integer.parseInt(flysteps);
                    // 根据阶数计算速度百分比
                    int speedPercentage;
                    switch (steps) {
                        case 1: speedPercentage = 80; break;   // 一阶80%
                        case 2: speedPercentage = 100; break;  // 二阶100%
                        case 3: speedPercentage = 110; break;  // 三阶110%
                        case 4: speedPercentage = 120; break;  // 四阶120%
                        case 5: speedPercentage = 130; break;  // 五阶130%
                        default: speedPercentage = 100;        // 默认100%
                    }

                    // 计算下一阶速度
                    int nextSpeedPercentage = Math.min(5, steps + 1) * 10 + 80; // 下一阶速度百分比
                    if (nextSpeedPercentage > 130) nextSpeedPercentage = 130;   // 不超过130%
                    Juitil.TextBackground(g,
                    speedPercentage + "% → " + nextSpeedPercentage + "%", 15,
                    340 + nxs, 246 + nys,
                    Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1,
                    UIUtils.LiSu_LS15,
                    UIUtils.Color_BACK
            );

            // 绘制属性文本和数值
            int[][] textPositions = {
                {263, 92},  // 技能等级
                {263, 116}, // 灵动值
                {263, 245}, // 飞行速度
                {42, 252}, // 当前燃灵值
                {42, 276}  // 每分消耗燃灵值
            };
            
            // 根据UI样式调整X坐标
            int xOffset = Util.SwitchUI == 1 ? 28 : 0;
            int yOffset = Util.SwitchUI == 1 ? -13 :0;
            
            // 绘制属性名称
            for (int i = 0; i < datatext.length; i++) {
                Juitil.TextBackground(g, datatext[i], 15,
                        textPositions[i][0] + xOffset,
                        textPositions[i][1] + yOffset,
                        Util.SwitchUI == 1 ? Color.BLACK : UIUtils.COLOR_Wing1, UIUtils.TEXT_HYJ16B, UIUtils.Color_BACK);
            }

        }

        //将100改为更大的值（如150），线条会向右移动
        //将190改为更小的值（如140），线条会变短
        //将170改为更大的值（如200），线条会向下移动
        //将170改为更小的值（如140），线条会向上移动
        if (Util.SwitchUI==2){
            g.setColor(new Color(106,40,0));
            g.drawLine(440, 80, 271, 80);
            g.drawLine(440, 237, 271, 237);
        }else {
            g.setColor(new Color(98,129,123));
            g.drawLine(467, 67, 298, 67);
            g.drawLine(467, 224, 298, 224);
        }

    }




    /***
     * 更新按钮图片
     * @param uiType 0:青山 1:山河 2:反璞
     */
    public void updateButtonImages(int uiType) {
        String panelName = this.getClass().getSimpleName();
        if (uiType!=1){
            Juitil.resetFrameAdjustment(148);
        }else {
            Juitil.adjustFrameSize(515, 452, 148);
        }
        Juitil.addClosingButtonToPanel(this,148,uiType==1?515:500);
        String iconResource = Util.SwitchUI==1 ? "0x6FAC1008" : "0x6FAB1002";
        String leng34_18 = uiType==0||uiType==1  ? "0x6FEB8614": "0x6FAB1014";
        sortGoods.setIcons(Juitil.getImgs(iconResource));
        assemb.setIcons(Juitil.getImgs(iconResource));
        advanced.setIcons(Juitil.getImgs(iconResource));
        service.setIcons(Juitil.getImgs(leng34_18));
        supplement.setIcons(Juitil.getImgs(leng34_18));
        destroy.setIcons(Juitil.getImgs(leng34_18));

        UiBack.getComponentStyle(panelName, "sortGoods").applyToButton(sortGoods);
        UiBack.getComponentStyle(panelName, "advanced").applyToButton(advanced);
        UiBack.getComponentStyle(panelName, "assemb").applyToButton(assemb);
        UiBack.getComponentStyle(panelName, "service").applyToButton(service);
        UiBack.getComponentStyle(panelName, "supplement").applyToButton(supplement);
        UiBack.getComponentStyle(panelName, "destroy").applyToButton(destroy);
        int fnx = uiType==1?0:-28;
        int fny = uiType==1?0:+12;
        for (int i = 0; i < 6; i++) {
            flyicon[i].setBounds(fnx+79+i*66, fny+304, 51, 51);
            flyiconback[i].setBounds(fnx+75+i*66, fny+301, 62, 62);
            equipicon[i].setBounds(fnx+113+i*66, fny+304, 20, 20);
        }

        Juitil.changeButtonColor(this,uiType);
    }

    public static List<Fly> getFlyList() {
        return flyList;
    }

    public static void setFlyList(List<Fly> flyList) {
        FlyJPanel.flyList = flyList;
    }

    public JLabel[] getFlyicon() {
        return flyicon;
    }

    public void setFlyicon(JLabel[] flyicon) {
        this.flyicon = flyicon;
    }

    public JLabel[] getFlyiconback() {
        return flyiconback;
    }

    public void setFlyiconback(JLabel[] flyiconback) {
        this.flyiconback = flyiconback;
    }

    public BigDecimal getAssemble() {
        return assemble;
    }

    public void setAssemble(BigDecimal assemble) {
        this.assemble = assemble;
    }

    public JLabel[] getEquipicon() {
        return equipicon;
    }

    public void setEquipicon(JLabel[] equipicon) {
        this.equipicon = equipicon;
    }
}
