package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.imagemonitor.PlayerMonitor;
import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import org.come.Frame.TeamJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.TeamApplyJpanel;
import org.come.Jpanel.TeamJpanel;
import org.come.Jpanel.TeamPostMessageJpanel;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.RoleShow;
import org.come.bean.TeamBean;
import org.come.entity.TeamRole;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GsonUtil;
import org.come.until.MessagrFlagUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class TeamPanelBtn extends MoBanBtn {

    private TeamApplyJpanel teamApplyJpanel;
    private TeamJpanel teamJpanel;
    private ZhuJpanel zhuJpanel;
    private TeamPostMessageJpanel messageJpanel;
    private int id;
    private int index;
    public TeamPanelBtn(String iconpath, int type, String text, TeamJpanel teamJpanel, TeamApplyJpanel teamApplyJpanel) {
        super(iconpath, type);
        this.setText(text);
        setFont(UIUtils.TEXT_FONT1);
        setForeground(Color.orange);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.teamJpanel = teamJpanel;
        this.teamApplyJpanel = teamApplyJpanel;		
    }

    public TeamPanelBtn(String iconpath, int type, String text, Color[] colors, Font font, TeamJpanel teamJpanel,
            TeamApplyJpanel teamApplyJpanel) {
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.teamJpanel = teamJpanel;
        this.teamApplyJpanel = teamApplyJpanel;
    }

    public TeamPanelBtn(String iconpath, int type, String text, Font font, ZhuJpanel zhuJpanel) {
        super(iconpath, type, 0,null);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.zhuJpanel = zhuJpanel;
    }

    public TeamPanelBtn(String iconpath, int type, String text, Color[] colors, Font font) {
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public TeamPanelBtn(String iconpath, int type, String text, Color[] colors, Font font,TeamPostMessageJpanel messageJpanel) {
        super(iconpath, type, colors);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.messageJpanel = messageJpanel;
    }


    public TeamPanelBtn(ImageIcon[] imageIcons, int type, int index,String tecx) {
        super(imageIcons, index,type);
        setNtext(tecx);
        this.index = index;
    }


    public TeamPanelBtn(String iconpath, int type, int index, Color[] colors, Font font) {
        super(iconpath, type, colors);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.index=index;
        
    }
    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
    	if (FightingMixDeal.State != HandleState.USUAL) {
			return;
		}
        RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
        if (this.getText().equals("解散队伍") ||this.getText().equals("离开队伍") ) {
            String sendmes = Agreement.getAgreement().team5Agreement("D");
            SendMessageUntil.toServer(sendmes);
        	FormsManagement.HideForm(13);
        } else if (this.getText().equals("移交队长") && roleShow.getCaptian()==1) {
        	TeamRole teamRole=teamJpanel.getXZ();
        	teamJpanel.upXz(-1);
        	if (teamRole==null) {
        		ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的玩家");
        		return;  
			}
        	if (teamRole.getRoleId().compareTo(roleShow.getRole_id())==0) {
        		ZhuFrame.getZhuJpanel().addPrompt2("不能选中你自己");
        		return;  
			}
        	String sendmes = Agreement.getAgreement().team5Agreement("S"+teamRole.getRoleId());
            SendMessageUntil.toServer(sendmes);
        } else if (this.getText().equals("请离队伍") && roleShow.getCaptian()==1) {
        	TeamRole teamRole=teamJpanel.getXZ();
        	teamJpanel.upXz(-1);
        	if (teamRole==null) {
        		ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的玩家");
        		return;  
			}
        	if (teamRole.getRoleId().compareTo(roleShow.getRole_id())==0) {
        		ZhuFrame.getZhuJpanel().addPrompt2("不能选中你自己");
        		return;  
			}
        	String sendmes = Agreement.getAgreement().team5Agreement("K"+teamRole.getRoleId());
            SendMessageUntil.toServer(sendmes);
        } else if (this.getText().equals("申请列表") && roleShow.getCaptian()==1) {
        	String sendmes = Agreement.getAgreement().team6Agreement("");
            SendMessageUntil.toServer(sendmes);
        } else if (this.getText().equals("加为好友")) {
        	TeamRole teamRole=teamJpanel.getXZ();
        	if (teamRole==null) {
        		ZhuFrame.getZhuJpanel().addPrompt2("你没有选中的玩家");
        		return;  
			}
        	if (teamRole.getRoleId().compareTo(roleShow.getRole_id())==0) {
        		ZhuFrame.getZhuJpanel().addPrompt2("不能选中你自己");
        		return;  
			}
        	PlayerMonitor.addFriend(teamRole.getRoleId(), teamRole.getName());
        } else if (this.getText().equals("允许") ) {
        	teamApplyJpanel.teamAgree();
        } else if (this.getText().equals("拒绝") ) {
        	teamApplyJpanel.teamRefruse();
        } else if (this.getText().equals("清空") ) {
        	teamApplyJpanel.teamClear();
        } else if ("组队操作".equals(getText())) {        	
            zhuJpanel.showIsTeamBtn(false, 0);
            if (ImageMixDeal.userimg.getRoleShow().getTroop_id()!=null) {
            	TeamJframe.getTeamJframe().getTeamjpanel().show(ImageMixDeal.userimg.getRoleShow(), RoleData.getRoleData().getTeamBean());
            }else {
				MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE2);
			}
        } else if ("组队平台".equals(getText())) {
        	zhuJpanel.showIsTeamBtn(false, 0);
            String sendmes = Agreement.getAgreement().enlistAgreement("");
            SendMessageUntil.toServer(sendmes);
        } else if ("立即发布".equals(getText())) {
            TeamBean teamBean = new TeamBean();
            teamBean.seteTask(messageJpanel.getChooseRestrainStr(1));
            teamBean.seteTeam(messageJpanel.getChooseRestrainStr(2));
            teamBean.seteMsg(messageJpanel.getSendBelTextArea().getText());
            String sendmes = Agreement.getAgreement().enlistAgreement(GsonUtil.getGsonUtil().getgson().toJson(teamBean));
            SendMessageUntil.toServer(sendmes);
            FormsManagement.HideForm(19);
        } else if (getText().equals("暂离队伍")||getText().equals("离")) {
    		String sendmes = Agreement.getAgreement().team4Agreement("L");
            SendMessageUntil.toServer(sendmes);
        } else if (getText().equals("回归队伍")||getText().equals("归")) {
        	String sendmes = Agreement.getAgreement().team4Agreement("R");
            SendMessageUntil.toServer(sendmes);
        } else if (getText().equals("召回")||getText().equals("召")) {
        	String sendmes = Agreement.getAgreement().team4Agreement("C"+RoleData.getRoleData().getTeamBean().getTeams().get(index).getRoleId());
            SendMessageUntil.toServer(sendmes);
        }
    }
}
