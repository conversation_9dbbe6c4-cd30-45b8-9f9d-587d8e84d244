package jxy2.synt;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;

import javax.swing.*;
import java.awt.*;
import java.util.HashMap;
import java.util.Map;

public class ExchangSoulJPanel extends JPanel {
    private JScrollPane resetSkill;
    private JList<ExchangModel> exchangModelJList;
    private Map<Integer, ExchangModel> exchangModelMap = new HashMap<>();
    public ExchangSoulBtn rese;
    public ExchangSoulJPanel() {
        this.setPreferredSize(new Dimension(249, 256));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        rese = new ExchangSoulBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "确 定", 1,this,"");
        rese.setBounds(100,220,59,24);
        add(rese);
        getResetSkill();
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz103, 0, 0, 249, 256, 1);
        Juitil.Subtitledrawing(g, 90, 24, "选择魔晶", Color.WHITE, UIUtils.MSYH_HY16,1);
    }

    /**
     * 初始化内容
     * @param name 物品名称
     */
    public void InitializeSkillList(String name) {
        initialization();
        int index = 0;
        Goodstable[] goodstable = GoodsListFromServerUntil.getGoodslist();
        for (int i = 0; i < goodstable.length; i++) {
            if (goodstable[i] == null) {continue;}
            if (goodstable[i].getGoodsname().equals(name)){
                Goodstable goods = goodstable[i];
                if (goods!=null){
                    ExchangModel exchangModel = new ExchangModel();
                    exchangModel.setBounds(0, index * 55, 249, 55);
                    exchangModel.initSkillInfo(goods);
                    exchangModel.addMouseListener(new ExchangSoulMouse(index,exchangModel));
                    exchangModelJList.add(exchangModel);
                    exchangModelMap.put(index,exchangModel);
                    index++;
                }
            }
        }
        exchangModelJList.setPreferredSize(new Dimension(249, index * 55));
    }


    /**
     * 清除组件
     */
    private void initialization() {
        exchangModelJList.removeAll();
        exchangModelMap.clear();
    }

    public JScrollPane getResetSkill() {
        if (resetSkill == null) {
            resetSkill = new JScrollPane(getExchangModelJList());
            resetSkill.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
            resetSkill.getVerticalScrollBar().setUI(null);
            resetSkill.getVerticalScrollBar().setUnitIncrement(20);
            resetSkill.getViewport().setOpaque(false);
            resetSkill.setOpaque(false);
            resetSkill.setBounds(0, 30, 249, 170);
            resetSkill.setBorder(BorderFactory.createEmptyBorder());
            resetSkill.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
            this.add(resetSkill);
        }
        return resetSkill;
    }

    public void setResetSkill(JScrollPane resetSkill) {
        this.resetSkill = resetSkill;
    }

    public JList<ExchangModel> getExchangModelJList() {
        if (exchangModelJList==null){
            exchangModelJList = new JList<>();
            exchangModelJList.setSelectionBackground(new Color(122, 117, 112));
            exchangModelJList.setSelectionForeground(Color.white);
            exchangModelJList.setForeground(Color.white);
            exchangModelJList.setFont(UIUtils.TEXT_HY16);
            exchangModelJList.removeAll();
            exchangModelJList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
            DefaultListCellRenderer cellRenderer = new DefaultListCellRenderer() {
                @Override
                public Component getListCellRendererComponent(javax.swing.JList<?> list, Object value, int index,
                                                              boolean isSelected, boolean cellHasFocus) {
                    super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                    return this;
                }
            };
            exchangModelJList.setCellRenderer(cellRenderer);
            exchangModelJList.setOpaque(false);
        }
        return exchangModelJList;
    }

    public void setExchangModelJList(JList<ExchangModel> exchangModelJList) {
        this.exchangModelJList = exchangModelJList;
    }

    public Map<Integer, ExchangModel> getExchangModelMap() {
        return exchangModelMap;
    }

    public void setExchangModelMap(Map<Integer, ExchangModel> exchangModelMap) {
        this.exchangModelMap = exchangModelMap;
    }

    public ExchangSoulBtn getRese() {
        return rese;
    }

    public void setRese(ExchangSoulBtn rese) {
        this.rese = rese;
    }
}
