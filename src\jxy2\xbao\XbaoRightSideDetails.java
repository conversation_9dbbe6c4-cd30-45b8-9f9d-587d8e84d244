package jxy2.xbao;

import com.tool.tcpimg.ChatBox;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class XbaoRightSideDetails extends JPanel {
    public ChatBox box = new ChatBox();
    public ChatBox box2 = new ChatBox();
    public ChatBox box3 = new ChatBox();
    private JLabel[] labimg = new JLabel[5];
    private JLabel[] labimgadd = new JLabel[5];
    private JLabel[] skillimg = new JLabel[6];//放置三个激活技能图标
    private JLabel[] chatBoxBackImg = new JLabel[3];
    private int xOffset = 15; // 默认x偏移量
    public XbaoRightSideDetails() {
        setPreferredSize(new Dimension(300, box.getHeight()));
        this.setBackground(UIUtils.Color_BACK);
        this.setLayout(null);
        this.setOpaque(false);
        for (int i = 0; i < labimg.length; i++) {
            labimg[i] = new JLabel();
            labimg[i].setIcon(Juitil.tz377);
            add(labimg[i]);
        }

        for (int i = 0; i < labimgadd.length; i++) {
            labimgadd[i] = new JLabel();
            labimgadd[i].setIcon(Juitil.tz378);
            add(labimgadd[i]);
        }

        // 初始化技能图标
        for (int i = 0; i < 6; i++) {
            skillimg[i] = new JLabel();
            this.add(skillimg[i]);
        }
        for (int i = 0; i < chatBoxBackImg.length; i++) {
            chatBoxBackImg[i] = new JLabel();
            this.add(chatBoxBackImg[i]);
        }

    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        // 创建并绘制图形上下文
        Graphics g3 = g.create(15, 0, 300, box.getHeight());
        box.paint(g3);
        g3.dispose();

        Graphics g32 = g.create(75, box.getHeight()-7, 300, box2.getHeight());
        box2.paint(g32);
        g32.dispose();

        Graphics g33 = g.create(15, box2.getHeight()+box.getHeight()-7, 300, box3.getHeight());
        box3.paint(g33);
        g33.dispose();

    }

    public ChatBox getBox() {
        return box;
    }
    public void setBox(ChatBox box) {
        this.box = box;
    }

    public JLabel[] getLabimg() {
        return labimg;
    }

    public void setLabimg(JLabel[] labimg) {
        this.labimg = labimg;
    }

    public JLabel[] getLabimgadd() {
        return labimgadd;
    }

    public void setLabimgadd(JLabel[] labimgadd) {
        this.labimgadd = labimgadd;
    }

    public JLabel[] getSkillimg() {
        return skillimg;
    }

    public void setSkillimg(JLabel[] skillimg) {
        this.skillimg = skillimg;
    }

    public JLabel[] getChatBoxBackImg() {
        return chatBoxBackImg;
    }

    public void setChatBoxBackImg(JLabel[] chatBoxBackImg) {
        this.chatBoxBackImg = chatBoxBackImg;
    }
    // 添加设置x偏移量的方法
    public void setXOffset(int offset) {
        this.xOffset = offset;
    }

    public ChatBox getBox2() {
        return box2;
    }

    public void setBox2(ChatBox box2) {
        this.box2 = box2;
    }

    public ChatBox getBox3() {
        return box3;
    }

    public void setBox3(ChatBox box3) {
        this.box3 = box3;
    }
}
