package jxy2.guaed.fl;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.ZhuFrame;
import org.come.bean.Alchemy;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.Timer;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.*;

public class AsoulBtn extends MoBanBtn {
    public AsoulJPanel asoulJPanel;
    public int BtnId,size;
    public Timer timers;
    public AsoulBtn(String iconpath, int type, int BtnId, String labelName,
                    AsoulJPanel asoulJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.asoulJPanel = asoulJPanel;
    }
    public AsoulBtn(String iconpath, int type, String text, AsoulJPanel asoulJPanel, String prowpt, int BtnId) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        setColor(UIUtils.COLOR_CL_NAME);
        setFont(UIUtils.NEWTX_HY17);
        this.BtnId=BtnId;
        this.asoulJPanel=asoulJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (BtnId==1){
            if (!asoulJPanel.getAddGoodsImg().isVisible()){
                return;
            }
            if (asoulJPanel.getXzgoods()==null){
                asoulJPanel.setXzgoods(new Goodstable());
            }
            CycleTimer(asoulJPanel.getXzgoods());
            handleEnchantingState(BtnId,asoulJPanel);
        }else if (BtnId==4){
            if (asoulJPanel.getGoodstable().getValue().compareTo(asoulJPanel.getNewitemattributes().getValue())!=0){
                asoulJPanel.setGoodstable(asoulJPanel.getNewitemattributes());
            }
          CycleTimer(asoulJPanel.getXzgoods());
        }else if (BtnId==3||BtnId==2){
            handleEnchantingState(BtnId,asoulJPanel);
        }
    }

    public void CycleTimer(Goodstable goods){
        if (timers != null && timers.isRunning()) {
            return; // 如果计时器已经在运行，则直接返回，不创建新的计时器
        }
        Random random = new Random();
        timers = new Timer(50, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int sum =random.nextInt(5)+1;
                Set<Map.Entry<String, List<Alchemy>>> entrySet = UserMessUntil.getAllAlchemy().getAlchemymap().entrySet();
                for (Map.Entry<String, List<Alchemy>> entry : entrySet) {
                    if (entry.getKey().equals("守护石")){
                        List<Alchemy> alchemies = entry.getValue();
                            // 创建副本列表，用于选择随机元素
                            List<Alchemy> alchemiesCopy = new ArrayList<>(alchemies);
                            // 生成随机索引，从列表中选择6个元素
                            List<Alchemy> randomAlchemies = new ArrayList<>();
                            while (randomAlchemies.size() < sum && !alchemiesCopy.isEmpty()) {
                                int index = random.nextInt(alchemiesCopy.size());
                                Alchemy alchemy = alchemiesCopy.get(index);
                                randomAlchemies.add(alchemy);
                                alchemiesCopy.remove(index);
                            }
                        // 构建一个唯一的字符串
                        StringBuilder valueBuilder = new StringBuilder();
                        for (Alchemy alchemy : randomAlchemies) {
                            if (valueBuilder.length() > 0) {
                                valueBuilder.append("|");
                            }
                            valueBuilder.append(alchemy.getAlchemykey()).append("=").append(random.nextInt(Integer.parseInt(alchemy.getAlchemymv()))+0.5);
                        }

                        // 设置构建好的字符串
                        goods.setValue(valueBuilder.toString());
                        asoulJPanel.xz = false;
                        size++;
                    }
                }

                if (size>=20){
                    asoulJPanel.xz = true;
                    AsouGuard(BtnId==1?asoulJPanel.getGoodstable():asoulJPanel.getNewitemattributes(),asoulJPanel.getLyjgoods());
                    timers.stop();
                    size=0;
                }
            }
        });timers.start();

    }

    public static void AsouGuard(Goodstable... goods) {
        if (goods.length != 2) {return;}
        List<BigDecimal> rgids = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i].getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (goods[i].getStatus() == 0) {
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return;
                }
            }
            rgids.add(goods[i].getRgid());
        }

        goods[1].goodxh(1);
        if (goods[1].getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goods[1].getRgid());
        }
        goods[1].setStatus(1);


        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(105);
        operBean.setGoods(rgids);
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);

    }


    /**
     * 处理附魔界面状态切换的方法
     * @param BtnId 按钮ID，1为开始附魔，其他值为取消附魔
     * @param asoulJPanel 面板实例
     */
    public void handleEnchantingState(int BtnId, AsoulJPanel asoulJPanel) {
        // 判断是否为开始附魔操作
        if (BtnId == 1) {
            // 检查是否同时存在物品和灵石
            if (canStartEnchanting(asoulJPanel)) {
                startEnchanting(asoulJPanel);
            }
        } else {
            cancelEnchanting(asoulJPanel);
        }
    }

    /**
     * 检查是否可以开始附魔
     * @param panel 面板实例
     * @return 是否可以开始附魔
     */
    private boolean canStartEnchanting(AsoulJPanel panel) {
        return panel.getAddGoodsImg().getIcon() != null &&
                panel.getAddStoneImg().getIcon() != null;
    }

    /**
     * 开始附魔状态设置
     * @param panel 面板实例
     */
    private void startEnchanting(AsoulJPanel panel) {
        // 检查灵源
        panel.checkLingyuan();

        // 设置附魔状态
        panel.enchanting = true;

        // 清理物品图标和数据
        clearGoodsDisplay(panel);

        // 更新界面显示状态
        updateUIVisibility(panel, true);
    }

    /**
     * 取消附魔状态设置
     * @param panel 面板实例
     */
    private void cancelEnchanting(AsoulJPanel panel) {
        // 设置附魔状态
        panel.enchanting = false;
        // 更新界面显示状态
        updateUIVisibility(panel, false);
    }

    /**
     * 清理物品显示
     * @param panel 面板实例
     */
    private void clearGoodsDisplay(AsoulJPanel panel) {
        panel.getAddGoodsImg().setIcon(null);
        panel.getAddGoodsImg().setVisible(false);
//
    }

    /**
     * 更新界面元素可见性
     * @param panel 面板实例
     * @param isEnchanting 是否处于附魔状态
     */
    private void updateUIVisibility(AsoulJPanel panel, boolean isEnchanting) {
        panel.getSpage().setVisible(isEnchanting);
        panel.getOneclickupbtn().setVisible(!isEnchanting);
        panel.getPlus().setVisible(!isEnchanting);
        panel.getAddStoneImg().setVisible(!isEnchanting);
        panel.getAddGoodsImg().setVisible(!isEnchanting);
        panel.getDetermine().setVisible(isEnchanting);
        panel.getUpgradeBtn().setVisible(isEnchanting);

    }
}
