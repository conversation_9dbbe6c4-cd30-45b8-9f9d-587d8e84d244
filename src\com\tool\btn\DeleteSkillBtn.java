package com.tool.btn;

import com.tool.role.RoleData;
import com.tool.tcpimg.UIUtils;
import come.tool.JDialog.TiShiUtil;
import org.come.Frame.OptionsJframe;
import org.come.Frame.PetSkillsJframe;
import org.come.Frame.SupportListJframe;
import org.come.Frame.ZhuFrame;
import org.come.until.FormsManagement;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class DeleteSkillBtn extends MoBanBtn {

	public DeleteSkillBtn(String iconpath, int type, String text,int index) {
		// TODO Auto-generated constructor stub
		super(iconpath, type, UIUtils.COLOR_BTNTEXT);
		this.setText(text);
		setFont(UIUtils.TEXT_FONT);
		this.index=index;
		setVerticalTextPosition(SwingConstants.CENTER);
		setHorizontalTextPosition(SwingConstants.CENTER);
	}

	@Override
	public void chooseyes() {
		// TODO Auto-generated method stub

	}

	@Override
	public void chooseno() {
		// TODO Auto-generated method stub

	}

	@Override
	public void nochoose(MouseEvent e) {
		if (getText().equals("支援列表")) {
			if (!FormsManagement.getframe(62).isVisible()) {
				// 刷新面板
				RoleData roleData = RoleData.getRoleData();
				roleData.addHelpBb(roleData.getHelpBbId());
				SupportListJframe.getSupportListJframe().getSupportListJpanel().init(roleData.getHelpBbName(roleData.getHelpBb()));
				FormsManagement.showForm(62);
			} else {
				FormsManagement.HideForm(62);
			}

		} else if (getText().equals("删除技能")) {
		    //判断是否解锁
            if(Util.isCanBuyOrno()){
                return;
            }
			if (UserMessUntil.getChosePetMes().getSkilllock()==1){
				ZhuFrame.getZhuJpanel().addPrompt2("当前召唤兽技能属于锁定状态无法删除技能操作！！！");
				return;
			}

			if (PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillNO() == 6) {
				if (UserMessUntil.getChosePetMes().getBeastSkills() != null && !UserMessUntil.getChosePetMes().getBeastSkills().isEmpty()) {
					if (!PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillID().isEmpty()) {
						OptionsJframe.getOptionsJframe().getOptionsJpanel().
						showBox(TiShiUtil.DeleteSkill, 1,"#Y你确定要删除 #G"
								+ UserMessUntil.getSkillId(PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillID())
								.getSkillname() + "#Y这个技能吗?");
					
					} else {
						ZhuFrame.getZhuJpanel().addPrompt2("请选择你要删除的召唤兽技能！！！");
					}
				} else {
					ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽没有技能！！！");
				}
			} else {
				if (UserMessUntil.getChosePetMes().getPetSkills() != null && !UserMessUntil.getChosePetMes().getPetSkills().isEmpty()) {
					if (!PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillID().isEmpty()) {
						OptionsJframe.getOptionsJframe().getOptionsJpanel().
						showBox(TiShiUtil.DeleteSkill, 2,"#Y你确定要删除 #G"
								+ UserMessUntil.getSkillId(PetSkillsJframe.getPetSkillsJframe().getPetSkillsJpanel().getPetskillID())
								.getSkillname() + "#Y这个技能吗?");
					} else {
						ZhuFrame.getZhuJpanel().addPrompt2("请选择你要删除的召唤兽技能！！！");
					}
				} else {
					ZhuFrame.getZhuJpanel().addPrompt2("这只召唤兽没有技能！！！");
				}
			}
		}

	}

}
