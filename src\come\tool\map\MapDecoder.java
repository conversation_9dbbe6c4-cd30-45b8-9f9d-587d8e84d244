package come.tool.map;

import java.io.EOFException;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;

public class MapDecoder {

	private int width;

	private int height;

	private int[][] segmentsOffset;

	private Object[][] jpegDatas;
    
	private String filename;

	private MyRandomAccessFile mapFile;

	private int horSegmentCount;

	private int verSegmentCount;

	public MapDecoder(File file) throws Exception {
		this.filename = file.getName();
		mapFile = new MyRandomAccessFile(file, "r");
	}
	/**
	 * 从流加载MAP
	 */
	public byte[][] loadHeader() {
		byte[][] bs=null;
		if (!isValidMapFile()) { throw new IllegalArgumentException("非梦幻地图格式文件!"); }
		try {
			// start decoding
			width = mapFile.readInt2();
			height = mapFile.readInt2();
			horSegmentCount = (int) Math.ceil(width / 320.0);
			verSegmentCount = (int) Math.ceil(height / 240.0);
            segmentsOffset = new int[horSegmentCount][verSegmentCount];
			jpegDatas = new Object[horSegmentCount][verSegmentCount];
			for (int v = 0;v<verSegmentCount;v++) {
				for (int h=0;h<horSegmentCount;h++) {
					segmentsOffset[h][v] = mapFile.readInt2();
				}
			}
			bs=new byte[verSegmentCount*20][horSegmentCount*20];
			for (int v = 0;v<verSegmentCount;v++) {
				for (int h=0;h<horSegmentCount;h++) {
					getJpegData(h, v, bs);
				}
			}
		} catch (Exception e) {
			throw new IllegalArgumentException("地图解码失败:" + e.getMessage());
		}
		return bs;
	}
	
  /**
	* 读取地图规则
	* LECC
	* h表示第h个width  v表示第v个height
	*/
	public void cell(int h, int v,byte[][] bs,byte[] ccles){
		//表示换行次数
		int gao=0;
			for (int i = 0; i < ccles.length; i++) {
				bs[v*12+gao][h*16+i%16]=ccles[i];
				if (i%16==15) gao++;
			}	
	}
	/**
	 * 获取指定的JPEG数据块和地图规则
	 * 
	 * @param h
	 *            行
	 * @param v
	 *            列
	 * @return
	 */
	public void getJpegData(int h, int v,byte[][] bs) {
		try {
			int len = 0;
			byte jpegBuf[] = null;
			byte jpegBuf1[] = null;
			mapFile.seek(segmentsOffset[h][v]);// XXX offset
			if (isJPEGData()) {
				len = mapFile.readInt2();
				jpegBuf = new byte[len];				
				mapFile.readFully(jpegBuf);
				jpegDatas[h][v] = jpegBuf;
			}
			if (isLLECData()) {			
				jpegBuf1 = data();
				cell(h, v, bs, jpegBuf1);
			}
		} catch (Exception e) {
			System.err.println("获取JPEG 数据块失败：" + e.getMessage());
		}
	}
	public byte[] getJpegData(int h, int v){
		byte[] bs=(byte[]) jpegDatas[h][v];
		return bs;
	}
	/**
	 * 清楚数据
	 */
	public void removedata(){
		try {
			mapFile.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	/**获取数据*/
	public byte[] data(){
		try {
			 int len=mapFile.readInt2();
			 byte[] jpegBuf = null;
			 jpegBuf = new byte[len];
			 mapFile.readFully(jpegBuf);
			 return jpegBuf;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;		
	}
	private boolean isJPEGData() {
		byte[] buf = new byte[4];
		try {
			int len = mapFile.read();
			mapFile.read(buf);// 47 45 50 4A; GEPJ		
			String str = new String(buf);
			return str.equals("GEPJ");
		} catch (IOException ex) {
			ex.printStackTrace();
		}
		return false;
	}
	private boolean isLLECData() {
		byte[] buf = new byte[4];
		try {
			mapFile.read(buf);// 47 45 50 4A; GEPJ		
			String str = new String(buf);
			return str.equals("LLEC");
		} catch (IOException ex) {
			ex.printStackTrace();
		}
		return false;
	}

	private boolean isValidMapFile() {
		byte[] buf = new byte[4];
		try {
			mapFile.read(buf);
			String str = new String(buf);
			return str.equals("HHYZ");
		} catch (IOException ex) {
			ex.printStackTrace();
		}
		return false;
	}

	public String getFilename() {
		return filename;
	}

	public int getHeight() {
		return height;
	}

	public int getWidth() {
		return width;
	}

	class MyRandomAccessFile extends RandomAccessFile {
		public MyRandomAccessFile(File file, String mode) throws FileNotFoundException {
			super(file, mode);
		}
		public int readInt2() throws IOException {
			int ch1 = this.read();
			int ch2 = this.read();
			int ch3 = this.read();
			int ch4 = this.read();
			if ((ch1 | ch2 | ch3 | ch4) < 0)
				throw new EOFException();
			return ((ch1 << 0) + (ch2 << 8) + (ch3 << 16) + (ch4 << 24));
		}
	}

	public int getHorSegmentCount() {
		return horSegmentCount;
	}

	public int getVerSegmentCount() {
		return verSegmentCount;
	}

}

