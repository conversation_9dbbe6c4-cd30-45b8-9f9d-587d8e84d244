package jxy2.petView;

import jxy2.UniversalModel;
import org.come.Jpanel.WuLingJPanel;
import org.come.mouslisten.TemplateMouseListener;

import javax.swing.*;
import java.awt.event.MouseEvent;

public class Wu<PERSON>ing<PERSON>etMouse extends TemplateMouseListener {
    private JList<UniversalModel> petList;//
    private WuLingJPanel wuLingJPanel;
    public WuLingPetMouse(JList<UniversalModel> petList,WuLingJPanel wuLingJPanel) {
        this.petList = petList;
        this.wuLingJPanel = wuLingJPanel;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {

    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {
        WuLingJPanel.idxs = -1;
    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {
        if (e.getY() / 20 < petList.getModel().getSize()) {
            WuLingJPanel.idxs = petList.locationToIndex(e.getPoint());
        }
    }
}
