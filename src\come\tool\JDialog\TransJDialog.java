package come.tool.JDialog;

import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class TransJDialog implements TiShiChuLi{

	@Override
	public void tipBox(boolean tishi, Object object) {
		// TODO Auto-generated method stub
		String roleName=(String) object;
		if (tishi) {
			String send=Agreement.getAgreement().TransStateAgreement("1|"+roleName);
			SendMessageUntil.toServer(send);
		}
	}

}
