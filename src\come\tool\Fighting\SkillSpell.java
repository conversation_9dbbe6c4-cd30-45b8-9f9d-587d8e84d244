package come.tool.Fighting;

import com.tool.tcp.PartOne;
import com.tool.tcp.Sprite;

import java.awt.*;

public class SkillSpell {
	private String skillid;
	private Sprite skill;
	// 记录流逝的时间
	private long dietime;
	// 法术显示位置类型 0玩家身上 1局中 2 居左 3局右 4移动
	private int skinpath;
	private int dir;
	// 需要显示的位置
	private int x;
	private int y;
	//技能ID
	private int id;
	//技能透明度
	private float alpha;
	private ShadowMode shadowMode;
	private int Round;
	public void draw(Graphics g) {	
		if (shadowMode!=null) {
			shadowMode.draw(g);
		}else if (skill != null) {
			skill.updateToTime(dietime,dir);
			float v = id == 1065 ? 0.4f : 1.0f;
			skill.draw(g, x, y,v);
		}
	}
	public SkillSpell() {
		// TODO Auto-generated constructor stub
	}
	public SkillSpell(String skinid, int x, int y) {
		super();
		this.skillid = skinid;
		this.dietime = 0;
		this.x = x;
		this.y = y;
	}

	public String getSkillid() {
		return skillid;
	}

	public void setSkillid(String skillid) {
		this.skillid = skillid;
	}
	public boolean getDietime(long dietime) {
		if (shadowMode!=null) {
			return shadowMode.addTime(dietime);
		}else {
			this.dietime += dietime;
			if (skill == null){
				if (PartOne.getSkinValue(-1, skillid) != null) {
					skill = PartOne.sprite(-1,skillid);
				}
				//原始硬盘调用
//				skill = SpriteFactory.Prepare(skillid);
			}
			if (skill != null) {
				return this.dietime>skill.getTime();
			} else {
				return this.dietime>3000;
			}	
		}
	}
	public void setDietime(long dietime) {
		this.dietime = dietime;
	}
	public int getSkinpath() {
		return skinpath;
	}
	public void setSkinpath(int skinpath) {
		this.skinpath = skinpath;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public ShadowMode getShadowMode() {
		return shadowMode;
	}
	public void setShadowMode(ShadowMode shadowMode) {
		this.shadowMode = shadowMode;
	}
	public Sprite getSkill() {
		return skill;
	}
	public void setSkill(Sprite skill) {
		this.skill = skill;
	}
	public int getDir() {
		return dir;
	}
	public void setDir(int dir) {
		this.dir = dir;
	}
	public long getDietime() {
		return dietime;
	}
	public int getRound() {
		return Round;
	}
	public void setRound(int round) {
		Round = round;
	}

	public float getAlpha() {
		return alpha;
	}

	public void setAlpha(float alpha) {
		this.alpha = alpha;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}
}
