package jxy2.guaed.fl;

import org.come.mouslisten.TemplateMouseListener;

import java.awt.event.MouseEvent;

public class <PERSON>glianMouse extends TemplateMouseListener {
    public RonglianJPanel ronglianJPanel;
    public int index,type;

    public RonglianMouse(RonglianJPanel ronglianJPanel, int index,int type) {
        this.ronglianJPanel = ronglianJPanel;
        this.index = index;
        this.type = type;
    }

    @Override
    protected void specificMousePressed(MouseEvent e) {
        ronglianJPanel.getAddGoodsImg()[index].setIcon(null);
        ronglianJPanel.getPlus()[index].setVisible(true);
        RonglianBtn.activeButtonId = 0;
        RonglianJPanel.isvstop();
    }

    @Override
    protected void specificMouseReleased(MouseEvent e) {

    }

    @Override
    protected void specificMouseEntered(MouseEvent e) {

    }

    @Override
    protected void specificMouseExited(MouseEvent e) {

    }

    @Override
    protected void specificMouseDragged(MouseEvent e) {

    }

    @Override
    protected void specificMouseMoved(MouseEvent e) {

    }
}
