package com.tool.Stall;

import jxy2.xbao.Xuanbao;
import org.come.bean.UseCardBean;
import org.come.entity.*;
import org.come.model.Lingbao;
import org.come.until.JmSum;

import java.util.List;

/**财产更新*/
public class AssetUpdate {
    //类型 0npc购买 1商城购买 2积分兑换 3给予获取 4交易  5摆摊购买 6摆摊获得 7收摊 8物品使用获得
	public static int NPC=0;
	public static int MALL=1;
	public static int INTEGRATION=2;
	public static int GIVE=3;
	public static int DEAL=4;
	public static int STALLBUY=5;
	public static int STALLGET=6;
	public static int STALLRET=7;
	public static int USERGOOD=8;
	//物品
	private List<Goodstable> goods;
	//添加的召唤兽
	private List<RoleSummoning> pets;
	//添加的灵宝
	private List<Lingbao> lingbaos;
	//添加的玄宝
	private List<Xuanbao> xuanbaos;
	//修改的坐骑
	private List<Mount> mounts;
	//修改的孩子
	private List<Baby> babys;
	//记录玉符的修改
	private List<PartJade> jades;
	private List<Pal> pals;//伙伴
	private UseCardBean useCard;
	//类型                     
	private int type;
	/**提示*/
	private String msg;
	/**更改的数据 D游戏币X仙玉E经验C充值积分*/
	/**
	 * 人物状态      R等级=经验=HP=MP
	 * 召唤兽状态  P召唤兽ID=等级=经验=亲密=HP=MP
	 * 坐骑状态      M坐骑id=等级=经验=熟练度
	 * 灵宝状态      L灵宝id=等级=经验=契合
	 * 内丹状态      N内丹id=转生=等级=经验
	 */
	private String data;
	//副本数据
	private String sceneMsg;
	private long I;
	//修改的帮派抗性
	private String resistance;
	private String vip;
	private String task;
	public AssetUpdate() {
		// TODO Auto-generated constructor stub
	}
	public List<Goodstable> getGoods() {
		return goods;
	}
	public void setGoods(List<Goodstable> goods) {
		this.goods = goods;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public List<RoleSummoning> getPets() {
		return pets;
	}
	public void setPets(List<RoleSummoning> pets) {
		this.pets = pets;
	}
	public List<Lingbao> getLingbaos() {
		return lingbaos;
	}
	public void setLingbaos(List<Lingbao> lingbaos) {
		this.lingbaos = lingbaos;
	}

	public List<Xuanbao> getXuanbaos() {
		return xuanbaos;
	}

	public void setXuanbaos(List<Xuanbao> xuanbaos) {
		this.xuanbaos = xuanbaos;
	}

	public List<PartJade> getJades() {
		return jades;
	}
	public void setJades(List<PartJade> jades) {
		this.jades = jades;
	}
	
	public List<Mount> getMounts() {
		return mounts;
	}
	public void setMounts(List<Mount> mounts) {
		this.mounts = mounts;
	}
	public List<Baby> getBabys() {
		return babys;
	}
	public void setBabys(List<Baby> babys) {
		this.babys = babys;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public String getData() {
		return data;
	}
	public void setData(String data) {
		this.data = data;
	}
	public long getI() {
		return JmSum.MZ(I);
	}
	public void setI(long i) {
		this.I =JmSum.ZM(i);
	}
	
	public UseCardBean getUseCard() {
		return useCard;
	}
	public void setUseCard(UseCardBean useCard) {
		this.useCard = useCard;
	}
	public String getSceneMsg() {
		return sceneMsg;
	}
	public void setSceneMsg(String sceneMsg) {
		this.sceneMsg = sceneMsg;
	}
	
	//提示生成
	public String msg(){
	    //类型 0npc购买 1商城购买 2积分兑换 3给予获取 4交易  5摆摊购买 6摆摊获得 7收摊
		StringBuffer buffer=null;
		switch (type) {
		case 0:
		case 1:
		case 2:
			if (msg==null||msg.length()==0) {return null;}
			buffer=new StringBuffer();
			buffer.append("获得了");
			buffer.append(msg);
			return buffer.toString();
		case 3:
			buffer=new StringBuffer();
			buffer.append("你收到别人送给你的");
			if (msg!=null) {
				buffer.append(msg);
			}
			if (data!=null) {
				if (buffer.length()!=0) {buffer.append(",");}
				String[] VS=data.split("\\|");
				for (int i = 0; i < VS.length; i++) {
					if (VS[i].startsWith("D")) {
						buffer.append("金钱");
						buffer.append(VS[i].split("=")[1]);		
					}
				}
			}
			return buffer.toString();
		case 5:
			if (data==null) {return null;}
			
			buffer=new StringBuffer();
			buffer.append("花费了");
			String[] VS=data.split("\\|");
			for (int i = 0; i < VS.length; i++) {
				if (VS[i].startsWith("D")) {
					buffer.append(VS[i].split("=")[1]);		
					buffer.append("金钱");
				}
			}
			buffer.append("购买了");
			buffer.append(msg);
			return buffer.toString();
		case 6:
			if (data==null) {return null;}
			buffer=new StringBuffer();
			buffer.append("你售出");
			buffer.append(msg);
			buffer.append("获得了");
			String[] VS6=data.split("\\|");
			for (int i = 0; i < VS6.length; i++) {
				if (VS6[i].startsWith("D")) {
					buffer.append(VS6[i].split("=")[1]);		
					buffer.append("金钱");
				}
			}
			return buffer.toString();
		case 8:
			if (data==null) {return null;}
			buffer=new StringBuffer();
			buffer.append("获得了");
			String[] VS8=data.split("\\|");
			for (int i = 0; i < VS8.length; i++) {
				if (VS8[i].startsWith("D")) {
					buffer.append(VS8[i].split("=")[1]);		
					buffer.append("金钱");
				}else if (VS8[i].startsWith("P")){
					buffer.append(getPets().get(i).getSummoningname());
				}
			}

			return buffer.toString();

		case 11:
			buffer=new StringBuffer();
			buffer.append("你取回了");
			buffer.append(msg);
			return buffer.toString();
		case 31:
			if (data==null) {return null;}
			buffer=new StringBuffer();
			buffer.append("获得了");
			String[] vs=data.split("\\|");
			for (int i = 0; i < vs.length; i++) {
					if (vs[i].startsWith("P")){
					buffer.append(getPets().get(i).getSummoningname());
				}
			}
			return buffer.toString();
		case 22:
		case 23:
		case 24:
		case 25:
			return msg;
		}
		return null;	
	}
	public String getVip() {
		return vip;
	}
	public void setVip(String vip) {
		this.vip = vip;
	}
	public List<Pal> getPals() {
		return pals;
	}
	public void setPals(List<Pal> pals) {
		this.pals = pals;
	}
	public String getResistance() {
		return resistance;
	}
	public void setResistance(String resistance) {
		this.resistance = resistance;
	}
	public String getTask() {
		return task;
	}
	public void setTask(String task) {
		this.task = task;
	}
}
