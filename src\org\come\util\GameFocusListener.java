package org.come.util;

import org.come.login.GameView;

import javax.swing.*;
import java.awt.event.*;

/**
 * 游戏窗口焦点监听器
 * 监控窗口焦点变化，帮助诊断帧数问题
 */
public class GameFocusListener implements WindowFocusListener, ComponentListener, WindowStateListener {

    private GameView gameView;
    private long lastFocusLostTime = 0;
    private long lastFocusGainedTime = 0;
    private long lastMinimizeTime = 0;
    private boolean wasMinimized = false;
    private Timer recoveryTimer;

    public GameFocusListener(GameView gameView) {
        this.gameView = gameView;
    }

    /**
     * 设置GameView引用（用于延迟设置）
     */
    public void setGameView(GameView gameView) {
        this.gameView = gameView;
    }
    
    @Override
    public void windowGainedFocus(WindowEvent e) {
        lastFocusGainedTime = System.currentTimeMillis();
        long focusLostDuration = lastFocusGainedTime - lastFocusLostTime;

        if (gameView != null) {
            gameView.setGameViewActive(true);

            // 检查是否从最小化恢复
            if (wasMinimized) {
                triggerMinimizeRecovery();
                wasMinimized = false;
            } else if (focusLostDuration > 10000) {
                gameView.forceRefresh();
            }
        }
    }

    @Override
    public void windowLostFocus(WindowEvent e) {
        lastFocusLostTime = System.currentTimeMillis();
        if (gameView != null) {
            gameView.setGameViewActive(false);
        }
    }
    
    @Override
    public void componentResized(ComponentEvent e) {
        if (gameView != null) {
            // 窗口大小改变时强制刷新
            gameView.forceRefresh();
        }
    }
    
    @Override
    public void componentMoved(ComponentEvent e) {
        // 窗口移动通常不影响渲染
    }
    
    @Override
    public void componentShown(ComponentEvent e) {
        if (gameView != null) {
            gameView.setGameViewActive(true);
            gameView.forceRefresh();
        }
    }
    
    @Override
    public void componentHidden(ComponentEvent e) {
        if (gameView != null) {
            gameView.setGameViewActive(false);
        }
    }

    // 新增：窗口状态监听
    @Override
    public void windowStateChanged(WindowEvent e) {
        int newState = e.getNewState();
        int oldState = e.getOldState();

        if ((newState & java.awt.Frame.ICONIFIED) != 0) {
            // 窗口被最小化
            lastMinimizeTime = System.currentTimeMillis();
            wasMinimized = true;
            if (gameView != null) {
                gameView.setGameViewActive(false);
            }
        }
    }

    /**
     * 最小化恢复的特殊处理
     */
    private void triggerMinimizeRecovery() {
        if (gameView != null) {
            // 立即强制刷新
            gameView.forceRefresh();

            // 启动恢复定时器，连续刷新几次
            if (recoveryTimer != null) {
                recoveryTimer.stop();
            }

            recoveryTimer = new Timer(500, new ActionListener() {
                private int refreshCount = 0;

                @Override
                public void actionPerformed(ActionEvent e) {
                    if (gameView != null && refreshCount < 5) {
                        gameView.forceRefresh();

                        // 强制垃圾回收
                        if (refreshCount == 2) {
                            System.gc();
                        }

                        refreshCount++;
                    } else {
                        ((Timer) e.getSource()).stop();
                    }
                }
            });
            recoveryTimer.start();
        }
    }
}
