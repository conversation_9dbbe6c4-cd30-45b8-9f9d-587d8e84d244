package jxy2.chatv;

import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import org.come.Frame.NoTitleInternalFrameUI;
import org.come.Frame.ZhuFrame;
import org.come.mouslisten.Mouselistener;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.image.MemoryImageSource;

public class ChatFrame  extends JInternalFrame implements MouseListener {
    private static ChatJPanel chatJPanel;
    private static ChatFrame chatFrame;
    public static ChatFrame getChatFrame(){
        if (chatFrame==null) {
            try {
                chatFrame=new ChatFrame();
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return chatFrame;
    }



    public ChatFrame() {
        // TODO Auto-generated constructor stub
        //设置鼠标样式
        this.setBackground(UIUtils.Color_BACK);
        Image image = Toolkit.getDefaultToolkit().createImage(new MemoryImageSource(0, 0, new int[0], 0, 0));
        setCursor(Toolkit.getDefaultToolkit().createCustomCursor(image,new Point(0, 0), null));
        this.setBorder(BorderFactory.createEmptyBorder());//去除内部窗体的边框
        setUI(new NoTitleInternalFrameUI(this));
        chatJPanel=new ChatJPanel();
        this.add(chatJPanel);
        this.pack();
        this.setBounds(0, ScrenceUntil.Screen_y - 28 - getHeight(), this.getWidth(), this.getHeight());//设置窗口出现的位置
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        this.setVisible(true);
        //设置不可以缩放
        this.setResizable(false);
        this.addMouseListener(this);
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }
    int xd = 0;
    int yd = 0;
    @Override
    public void mousePressed(MouseEvent e) {
        if (FightingMixDeal.State == HandleState.USUAL&&e.getButton() == MouseEvent.BUTTON3) {
            int x = e.getX();
            int y = e.getY()+ ScrenceUntil.Screen_y-227;
            int xdd = (Util.mapmodel.getShot_x() + x) / 20;
            int ydd = (Util.mapmodel.getShot_y() + y) / 20;
            if (xd != xdd || yd != ydd) {
                xd = xdd;
                yd = ydd;
                Mouselistener.Pathfinding(xd, yd);
            }
        }else {
            ZhuFrame.getZhuJpanel().getSendMes().requestFocus();
        }

    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public static ChatJPanel getChatJPanel() {
        return chatJPanel;
    }

    public static void setChatJPanel(ChatJPanel chatJPanel) {
        ChatFrame.chatJPanel = chatJPanel;
    }
}
