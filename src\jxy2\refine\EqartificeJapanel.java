package jxy2.refine;

import com.tool.btn.RefineOperBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.setup.AudioSteupMouslisten;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.RefiningUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
* 装备炼化面板
* <AUTHOR>
* @date 2024/7/28 下午8:10
*/

public class EqartificeJapanel extends JPanel {
    public String[] minMenu;
    public RefineMinMenuBtn[] minMenuBtn =new RefineMinMenuBtn[6];
    public JLabel[] sixEquipment = new JLabel[6];
    public Goodstable[] goods = new Goodstable[6];
    private RefineOperBtn workshopBtn;// 炼化装备按钮
    private int minType = 0;
    public BigDecimal money;
    public EqartificeJapanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        money = new BigDecimal(100000);
        minMenu = new String[]{"普通装备","仙器","配饰护符","秘石合成","彩晶石","伙伴装备"};
        for (int i = 0; i < minMenuBtn.length; i++) {
            minMenuBtn[i] = new RefineMinMenuBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, minMenu[i], i, this,"");
            minMenuBtn[i].btnchange(i==0?2:0);
            minMenuBtn[i].setBounds(138+i*66, 48, 66, 24);
            add(minMenuBtn[i]);
        }

        for (int i = 0; i < sixEquipment.length; i++) {
            int centerElementX = 282;
            int row, col;
            int x, y;
            // 确定行和列
            row = i < 2 ? 0 : (i == 2 ? 1 : 2);
            col = i < 2 ? i : (i == 2 ? 1 : i - 3);
            // 计算x和y坐标
            x = row == 1 ? centerElementX : row==2?  225 + col * 60 :250 + col * 60;
            y = 142 + row * 60;
            sixEquipment[i] = new JLabel();
            sixEquipment[i].addMouseListener(new RefineMouse(this, 24 + i));
            sixEquipment[i].setBounds(x, y, 50, 50);
            this.add(sixEquipment[i]);
        }

        // 炼化装备按钮
        workshopBtn = new RefineOperBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "?", 0, this,"");
        workshopBtn.setBounds(270,374, 82,29);
        this.add(workshopBtn);

        for (int i = 0; i < labnum.length; i++) {
            labnum[i] = new JLabel();
            labnum[i].setBounds(454,300+i*22, 17, 16);
            labnum[i].addMouseListener(new Mymouse(i));
            this.add(labnum[i]);
        }
    }

    class Mymouse implements MouseListener{
        int index;
        public Mymouse(int index) {
            this.index = index;
        }

        @Override
        public void mouseClicked(MouseEvent e) {

        }

        @Override
        public void mousePressed(MouseEvent e) {
            MaxIndex(index);
        }

        @Override
        public void mouseReleased(MouseEvent e) {

        }

        @Override
        public void mouseEntered(MouseEvent e) {

        }

        @Override
        public void mouseExited(MouseEvent e) {

        }
    }

    public void MaxIndex(int index){

        labnum[index].setIcon(AudioSteupMouslisten.icon);
        for (int i = 0; i < labnum.length; i++) {
            if (i!=index){
                labnum[i].setIcon(null);
            }
        }
    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        switch (minType) {
            case 0:
                // 预先计算好中间行单个元素的x坐标
                int centerElementX = 282;
                for (int i = 0; i < 6; i++) {
                    int row, col;
                    int x, y;
                    // 确定行和列
                    row = i < 2 ? 0 : (i == 2 ? 1 : 2);
                    col = i < 2 ? i : (i == 2 ? 1 : i - 3);
                    // 计算x和y坐标
                    x = row == 1 ? centerElementX : row == 2 ? 225 + col * 60 : 250 + col * 60;
                    y = 142 + row * 60;
                    // 绘制元素
                    Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1);
                    if (sixEquipment[i].getIcon() == null) {
                        Juitil.TextBackground(g, getPrompt(minType)[i], 13, x + 9, y + 17, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    } else {
                        if (goods[i] != null) {
                            Goodstable goodstable = GoodsListFromServerUntil.getGoods(goods[i].getGoodsid());
                            if (goodstable != null && goodstable.getUsetime() > 1) {
                                Juitil.TextBackground(g, goodstable.getUsetime() + "", 13, x, y, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                            }
                        }
                    }
                }
                break;
            case 3:
                int centerX = 280; // 圆心的X坐标
                int centerY = 200; // 圆心的Y坐标
                int radius = 70;  // 圆的半径
                int numElements = 4; // 元素数量
                double angleIncrement = Math.PI / numElements * 2; // 角度增量
                for (int i = 0; i < numElements; i++) {
                    double angle = i * angleIncrement; // 当前元素的角度
                    int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                    int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                    Juitil.ImngBack(g, Juitil.good_2, x, y, 50, 50, 1);
                    if (sixEquipment[i].getIcon() == null) {
                        Juitil.TextBackground(g, getPrompt(minType)[i], 13, x + 9, y + 20, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    }
                }
                break;

            default:

                for (int i = 0; i < 2; i++) {
                    Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
                    if (sixEquipment[i].getIcon() == null) {
                        String text = getPrompt(minType)[i];
                        Font font = UIUtils.FZCY_HY13;
                        FontMetrics metrics = org.come.until.SafeFontMetrics.getFontMetrics(font);
                        int textWidth = metrics.stringWidth(text);
                        int x = 238 + i * 134 - textWidth / 2; // 调整X坐标，使其居中
                        Juitil.TextBackground(g, text, 13, x, 220, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                    }else {
                        if (goods[1] != null) {
                            Goodstable goodstable = GoodsListFromServerUntil.getGoods(goods[1].getGoodsid());
                            if (goodstable != null && goodstable.getUsetime() > 1) {
                                Juitil.TextBackground(g, goodstable.getUsetime() + "", 13, 349, 200, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
                            }
                        }
                    }
                }
                break;
        }
        if ("装备培养".equals(workshopBtn.getText())){
            for (int i = 0; i < 6; i++) {
                g.drawImage(Juitil.tz83.getImage(), 454,300+i*22,14,14,null);
                Juitil.TextBackground(g, num[i], 14, 469, 298+i*22, UIUtils.COLOR_NAME, UIUtils.FZCY_HY14);

            }

            Juitil.TextBackground(g, "批量培养：", 14, 454, 276, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        }

    }
    private JLabel[] labnum = new JLabel[6];
    public String[] num = new String[]{"5个","10个","15个","20个","30个","99个"};
    public void RefiningDisplay() {
        for (int i = 0; i < goods.length; i++) {
          goods[i] = null;
          sixEquipment[i].setIcon(null);
        }
        workshopBtn.setText("?");
        int xhsum =  minType==0?6:minType==3?4:2;
        for (int i = 0; i < xhsum; i++) { // 控制循环次数，minType==0时为6，minType==1时为2
            int centerElementX = 282;
            int row, col;
            int x, y;
            // 确定行和列
            row = i < 2 ? 0 : (i == 2 ? 1 : 2);
            col = i < 2 ? i : (i == 2 ? 1 : i - 3);
            // 计算x和y坐标
            x = row == 1 ? centerElementX : row == 2 ? 225 + col * 60 : 250 + col * 60;
            y = 142 + row * 60;
            // 根据minType调整坐标
            x = minType == 0 ? x : 215 + i * 134;
            y = minType == 0 ? y : 202;
            sixEquipment[i].setBounds(x, y, 50, 50);
            sixEquipment[i].setVisible(true); // 设置元素为可见
        }

        // 隐藏多余的元素
        for (int i = xhsum; i < 6; i++) {
            sixEquipment[i].setVisible(false);
        }
        if (minType==3) {
            int centerX = 280; // 圆心的X坐标
            int centerY = 200; // 圆心的Y坐标
            int radius = 70;  // 圆的半径
            int numElements = 4; // 元素数量
            double angleIncrement = Math.PI / numElements * 2; // 角度增量
            for (int i = 0; i < numElements; i++) {
                double angle = i * angleIncrement; // 当前元素的角度
                int x = (int) (centerX + radius * Math.cos(angle)); // 计算X坐标
                int y = (int) (centerY + radius * Math.sin(angle)); // 计算Y坐标
                sixEquipment[i].setBounds(x, y, 50, 50);
            }
        }
    }

    public static Map<Integer, String[]> PROMPT_MAP = new ConcurrentHashMap<>();
    static {
        PROMPT_MAP.put(0, new String[]{"装备", "内丹", "玲珑", "九彩", "九彩", "九彩"});
        PROMPT_MAP.put(1, new String[]{"装备", "精华"});
        PROMPT_MAP.put(2, new String[]{"装备", "矿石"});
        PROMPT_MAP.put(3, new String[]{"秘石","秘石","秘石","秘石"});
        PROMPT_MAP.put(4, new String[]{"彩晶石", "彩晶石"});
        PROMPT_MAP.put(5, new String[]{"装备", "精华"});
        PROMPT_MAP.put(7, new String[]{"装备", "九天"});
        PROMPT_MAP.put(8, new String[]{"装备", "落魄", "落魄", "落魄"});
        PROMPT_MAP.put(10, new String[]{"装备", "玉符"});
        PROMPT_MAP.put(12, new String[]{"已升级", "套装","玉符"});
        PROMPT_MAP.put(13, new String[]{"消耗金钱", "拥有金钱","消耗灵修值","拥有灵修值"});
        PROMPT_MAP.put(14, new String[]{"装备", "装备"});
        PROMPT_MAP.put(15, new String[]{"生成个数", "消耗金钱","消耗灵修值","拥有灵修值"});
        PROMPT_MAP.put(16, new String[]{"装备", "矿石"});
        PROMPT_MAP.put(17, new String[]{"装备", "矿石"});
        PROMPT_MAP.put(18, new String[]{"仙器", "悔梦石"});
        PROMPT_MAP.put(19, new String[]{"八荒遗风", "矿石"});
        PROMPT_MAP.put(20, new String[]{"八荒遗风", "一阶"});
    }
    /**根究类型返回数组文本*/
    public String[] getPrompt(int type) {
        String[] result = PROMPT_MAP.get(type);
        return result != null ? result.clone() : null;
    }

    /** 点击good */
    public void ClickGood(Goodstable good, int path) {
        if (path < 24) {
            boolean a = true;
            if (good != null && good.getType() >= 497 && good.getType() <= 499) {
                int i = 1;
                if (good.getType() == 497) {
                    i = 1;
                } else if (good.getType() == 499) {
                    i = 2;
                } else if (good.getType() == 498) {
                    i = 3;
                }

                if (goods[i] == null) {
                    change(good, i);
                    a = false;
                } else if (!goods[i].getType().equals(good.getType())) {
                    change(good, i);
                    a = false;
                }
            }
            if (a) {
                int xhsum =  minType==0?6:minType==3?4:2;
                for (int i = 0; i < xhsum; i++) {
                    if (goods[i] == null) {
                        change(good, i);
                        break;
                    }
                }
            }
        } else {
            change(null, path - 24);
        }

        int ty=minType>=2?2:1;
        if (minType==5||minType==6){
            ty = 1;
        }
        String v = RefiningUtil.detection(goods, ty);
        if (!workshopBtn.getText().equals(v)) {
            workshopBtn.setText(v);
        }
    }
    /** 切换指定位置 */
    public void change(Goodstable good, int path) {
        goods[path] = good;
        if (good != null) {
            sixEquipment[path].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        } else {
            sixEquipment[path].setIcon(null);
        }
    }

    public RefineMinMenuBtn[] getMinMenuBtn() {
        return minMenuBtn;
    }

    public void setMinMenuBtn(RefineMinMenuBtn[] minMenuBtn) {
        this.minMenuBtn = minMenuBtn;
    }

    public RefineOperBtn getWorkshopBtn() {
        return workshopBtn;
    }

    public void setWorkshopBtn(RefineOperBtn workshopBtn) {
        this.workshopBtn = workshopBtn;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public JLabel[] getLabnum() {
        return labnum;
    }

    public void setLabnum(JLabel[] labnum) {
        this.labnum = labnum;
    }
}
