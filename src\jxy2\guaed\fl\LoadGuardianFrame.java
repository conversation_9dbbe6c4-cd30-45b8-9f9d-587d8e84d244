package jxy2.guaed.fl;

import com.tool.tcpimg.UIUtils;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import javax.swing.plaf.basic.BasicInternalFrameUI;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

public class LoadGuardianFrame extends J<PERSON><PERSON>nal<PERSON>rame implements MouseListener {
    public LoadGuardianJPanel loadGuardianJPanel;
    public static LoadGuardianFrame getLoadGuardianFrame() {
        return (LoadGuardianFrame) FormsManagement.getInternalForm(142).getFrame();
    }

    public LoadGuardianFrame() {
        this.loadGuardianJPanel = new LoadGuardianJPanel();
        this.setContentPane(loadGuardianJPanel);
        this.setBorder(BorderFactory.createEmptyBorder());// 去除内部窗体的边框
        ((BasicInternalFrameUI) this.getUI()).setNorthPane(null);// 去除顶部的边框
        this.setBackground(UIUtils.Color_BACK);
        this.setBounds(180, 319, 370, 170);
        this.pack();
        this.setVisible(false);
        this.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }

    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //开启窗口音效
        Music.addyinxiao("关闭窗口.mp3");
        //打开了窗体
          boolean isRightClick = javax.swing.SwingUtilities.isRightMouseButton(e) ||
                e.getButton() == MouseEvent.BUTTON3 ||
                e.isMetaDown(); // 保留原有逻辑作为备选

        if (isRightClick) {// 检测鼠标右键单击//检测鼠标右键单击
            FormsManagement.HideForm(142);
        }else {
            FormsManagement.Switchinglevel(142);
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {

    }

    public LoadGuardianJPanel getLoadGuardianJPanel() {
        return loadGuardianJPanel;
    }

    public void setLoadGuardianJPanel(LoadGuardianJPanel loadGuardianJPanel) {
        this.loadGuardianJPanel = loadGuardianJPanel;
    }
}
