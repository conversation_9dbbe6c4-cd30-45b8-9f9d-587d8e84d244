package jxy2.wormap;

import org.come.Frame.TestsmallmapJframe;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;

public class MapJlistChoseMouslisten  implements MouseListener, MouseMotionListener {
    private JList<String> listgamemap;//
    private NpcQueryJPanel npcQueryJPanel;
    public MapJlistChoseMouslisten(JList<String> listgamemap, NpcQueryJPanel npcQueryJPanel) {
        this.listgamemap = listgamemap;
        this.npcQueryJPanel = npcQueryJPanel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        if (listgamemap.getSelectedIndex() != -1) {
            String mapid = npcQueryJPanel.listModel.get(listgamemap.getSelectedIndex()).split(",")[4];
            WorldMapBtn.NewWorldMapImgShow(Integer.parseInt(mapid),2,true);
            TestsmallmapJframe.getTestsmallmapJframe(2).getTestsmallmapJpanel().setNpcnames( npcQueryJPanel.listModel.get(listgamemap.getSelectedIndex()));
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {

    }

    @Override
    public void mouseEntered(MouseEvent e) {

    }

    @Override
    public void mouseExited(MouseEvent e) {
        NpcQueryJPanel.idx = -1;
    }

    @Override
    public void mouseDragged(MouseEvent e) {

    }

    @Override
    public void mouseMoved(MouseEvent e) {
        if (listgamemap!=null){
            if (e.getY() / 36 < npcQueryJPanel.listModel.getSize()){
                NpcQueryJPanel.idx = npcQueryJPanel.listgamemap.locationToIndex(e.getPoint());
            }
        }
    }
}
