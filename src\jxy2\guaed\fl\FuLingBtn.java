package jxy2.guaed.fl;

import com.tool.btn.MoBanBtn;
import org.come.until.FormsManagement;
import org.come.until.Music;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class FuLingBtn extends MoBanBtn {
    private int typeBtn;
    private FuLingJPanel fuLingJPanel;
    private AsoulJPanel asoulJPanel;
    public FuLingBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, FuLingJPanel fuLingJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.fuLingJPanel = fuLingJPanel;
    }

    public FuLingBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, AsoulJPanel asoulJPanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.asoulJPanel = asoulJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn==3){
            if (!FormsManagement.getframe(142).isVisible()) {
                FormsManagement.showForm(142);
                LoadGuardianFrame.getLoadGuardianFrame().getLoadGuardianJPanel().Vis(true);
                int x = FuLingFrame.getFuLingFrame().getX();
                int y = FuLingFrame.getFuLingFrame().getY();
                LoadGuardianFrame.getLoadGuardianFrame().setBounds( x- 50, y  + 220, 370, 170);
            } else {
                FormsManagement.HideForm(142);
                Music.addyinxiao("关闭窗口.mp3");
            }
        }else {
            switch (typeBtn){
                case 0:
                    fuLingJPanel.getCardJPanel().getCardLayout().show(fuLingJPanel.getCardJPanel(), "asoul");
                    fuLingJPanel.titie = "附 灵";
                    fuLingJPanel.setType(0);
                    break;
                case 1:
                    fuLingJPanel.getCardJPanel().getCardLayout().show(fuLingJPanel.getCardJPanel(), "ronglian");
                    fuLingJPanel.titie = "融 炼";
                    fuLingJPanel.setType(1);
                    break;
            }
            fuLingJPanel.getVnavi()[typeBtn].btnchange(2);
            for (int i = 0; i < fuLingJPanel.getVnavi().length; i++) {
                if (i!=typeBtn){
                    fuLingJPanel.getVnavi()[i].btnchange(0);
                }
            }
            for (int i = 0; i < 2; i++) {
                fuLingJPanel.getCardJPanel().getRonglianJPanel().getAddGoodsImg()[i].setIcon(null);
                fuLingJPanel.getCardJPanel().getRonglianJPanel().getPlus()[i].setVisible(true);
            }
            RonglianJPanel.isvstop();
            RonglianBtn.activeButtonId = 0;
            FormsManagement.HideForm(142);
        }

    }
}
