package jxy2.npk;

/**
 * NPK文件头结构
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkHeader {
    private String signature;          // 文件签名
    private int entryCount;            // 条目数量
    private int unknownVar;            // 未知变量
    private int encryptionMode;        // 加密模式
    private int hashMode;              // 哈希模式
    private long indexOffset;          // 索引偏移
    private String configName;         // 配置名称
    private int infoSize;              // 信息大小
    private int decryptionKey;         // 解密密钥

    public NpkHeader() {
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public int getEntryCount() {
        return entryCount;
    }

    public void setEntryCount(int entryCount) {
        this.entryCount = entryCount;
    }

    public int getUnknownVar() {
        return unknownVar;
    }

    public void setUnknownVar(int unknownVar) {
        this.unknownVar = unknownVar;
    }

    public int getEncryptionMode() {
        return encryptionMode;
    }

    public void setEncryptionMode(int encryptionMode) {
        this.encryptionMode = encryptionMode;
    }

    public int getHashMode() {
        return hashMode;
    }

    public void setHashMode(int hashMode) {
        this.hashMode = hashMode;
    }

    public long getIndexOffset() {
        return indexOffset;
    }

    public void setIndexOffset(long indexOffset) {
        this.indexOffset = indexOffset;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public int getInfoSize() {
        return infoSize;
    }

    public void setInfoSize(int infoSize) {
        this.infoSize = infoSize;
    }

    public int getDecryptionKey() {
        return decryptionKey;
    }

    public void setDecryptionKey(int decryptionKey) {
        this.decryptionKey = decryptionKey;
    }

    @Override
    public String toString() {
        return String.format(
                "NPK Header:\n" +
                        "  Signature: %s\n" +
                        "  Entry Count: %d\n" +
                        "  Unknown Var: %d\n" +
                        "  Encryption Mode: %d\n" +
                        "  Hash Mode: %d\n" +
                        "  Index Offset: 0x%X\n" +
                        "  Config Name: %s\n" +
                        "  Info Size: %d\n" +
                        "  Decryption Key: %d",
                signature, entryCount, unknownVar, encryptionMode,
                hashMode, indexOffset, configName, infoSize, decryptionKey
        );
    }
}
