package com.tool.role;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.NewPart;
import com.tool.tcp.SpriteFactory;
import com.tool.time.TimeLimit;
import org.come.Frame.ZhuFrame;
import org.come.bean.RoleShow;
import org.come.bean.RoleTxBean;
import org.come.entity.Goodstable;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.UserMessUntil;

import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RoleTX {

    private static RoleTX roleTX;

    public static RoleTX getRoleTX() {
        if (roleTX == null) {
            roleTX = new RoleTX();
        }
        return roleTX;
    }

    public RoleTX() {
        // TODO Auto-generated constructor stub
        txImgs = new TxImg[25];
        eTxImg = new TxImg[4];
    }

    // 特效面板预览形象
    private NewPart part1;
    private int dir1 = 0;
    private TxImg[] txImgs;// 背包有的特效
    private TxImg[] eTxImg;// 显示的特效
    private int txType = 0;// 记录当前页数
    private int txYs = 0;// 记录当前页数
    // 多宝阁预览形象
    private NewPart part2;
    private int dir2 = 0;

	public void draw(Graphics g, int type) {
		if (type == 0) {
			part1.draw(g, 208, 310, dir1, ImageMixDeal.userimg.getTime());
			for (int i = 0; i < txImgs.length; i++) {
				if (txImgs[i] != null) {
					g.drawImage(txImgs[i].getIcon().getImage(),330 + i % 5 * 51, 97 + i / 5 * 51, 49, 49, null);
				}
			}
			for (int i = 0; i < eTxImg.length; i++) {
				if (eTxImg[i] != null) {
					g.drawImage(eTxImg[i].getIcon().getImage(), 59, 108 + i * 45, 38, 38, null);
				}
			}
		} else {
			part2.draw(g, 160, 230, dir2,ImageMixDeal.userimg.getTime());
		}
	}

    /** 修改方向 true:← false:→ */
    public void upDir(int type, boolean is) {
        if (type == 0) {
            switch (dir1) {
            case 4:
                if (is) {
                    dir1 = 1;
                } else {
                    dir1 = 0;
                }
                break;
            case 0:
                if (is) {
                    dir1 = 4;
                } else {
                    dir1 = 7;
                }
                break;
            case 7:
                if (is) {
                    dir1 = 0;
                } else {
                    dir1 = 3;
                }
                break;
            case 3:
                if (is) {
                    dir1 = 7;
                } else {
                    dir1 = 6;
                }
                break;
            case 6:
                if (is) {
                    dir1 = 3;
                } else {
                    dir1 = 2;
                }
                break;
            case 2:
                if (is) {
                    dir1 = 6;
                } else {
                    dir1 = 5;
                }
                break;
            case 5:
                if (is) {
                    dir1 = 2;
                } else {
                    dir1 = 1;
                }
                break;
            case 1:
                if (is) {
                    dir1 = 5;
                } else {
                    dir1 = 4;
                }
                break;
            }
        } else {
            switch (dir2) {
            case 4:
                if (is) {
                    dir2 = 1;
                } else {
                    dir2 = 0;
                }
                break;
            case 0:
                if (is) {
                    dir2 = 4;
                } else {
                    dir2 = 7;
                }
                break;
            case 7:
                if (is) {
                    dir2 = 0;
                } else {
                    dir2 = 3;
                }
                break;
            case 3:
                if (is) {
                    dir2 = 7;
                } else {
                    dir2 = 6;
                }
                break;
            case 6:
                if (is) {
                    dir2 = 3;
                } else {
                    dir2 = 2;
                }
                break;
            case 2:
                if (is) {
                    dir2 = 6;
                } else {
                    dir2 = 5;
                }
                break;
            case 5:
                if (is) {
                    dir2 = 2;
                } else {
                    dir2 = 1;
                }
                break;
            case 1:
                if (is) {
                    dir2 = 5;
                } else {
                    dir2 = 4;
                }
                break;
            }
        }
    }

    // 添加 0:特效面板 1:多宝阁预览
    public void addPart(int type, NewPart part) {
        if (type == 0) {
            part1 = part1.addPart(part);
        } else {
            part2 = part2.addPart(part);
        }
    }

    // 修改预览形象
    public void updatePart(int type, int lvl, BigDecimal species_id) {
        if (type == 0) {
        	part1=SpriteFactory.setPart(part1, lvl, species_id.toString());
        } else {
        	part2=SpriteFactory.setPart(part2, lvl, species_id.toString());
        }
    }

    // 移除形象
    public void removePart(int type, String skin) {
        if (type == 0) {
            part1.removePart(skin);
        } else {
            part2.removePart(skin);
        }
    }
    /** 人物角色形象 */
    public void initRole(BigDecimal species_id) {
        if (part1 == null) {
        	part1 = SpriteFactory.createPart(species_id.toString(), 2, 1, null);
        } else {
            updatePart(0, 1, species_id);
        }
        if (part2 == null) {
        	part2 = SpriteFactory.createPart(species_id.toString(), 2, 1, null);
        } else {
            updatePart(1, 1, species_id);
        }
    }

    /** 添加特效 0:特效面板 1:多宝阁预览 */
    public void addTX(int type, int id) {
        RoleTxBean txBean = UserMessUntil.getTxBean(id);
        if (txBean == null) {
            return;
        }
        NewPart part = SpriteFactory.createPart("tx/tx" + txBean.getRdId(), -2, txBean.getRdStatues() - txBean.getRdType(), null);
        if (type == 0) {
            part1 = part1.addPart(part);
        } else {
            part2 = part2.addPart(part);
        }
    }

    /**
     * 翅膀专用添加特效 0:特效面板 1:多宝阁预览
     * 
     * @param type
     * @param skin
     */
    public void addTX(int type, String skin) {
    	NewPart tx1=SpriteFactory.createPart("tx/" + skin + "0", -2, -5, null);
    	NewPart tx2=SpriteFactory.createPart("tx/" + skin + "1", -2, 5 , null);
        if (type == 0) {
            part1 = part1.addPart(tx1);
            part1 = part1.addPart(tx2);
        } else {
            part2 = part2.addPart(tx1);
            part2 = part2.addPart(tx2);
        }
    }

    /** 移除特效 0:特效面板 1:多宝阁预览 */
    public void removeTX(int type, int id) {
        RoleTxBean txBean = UserMessUntil.getTxBean(id);
        if (txBean == null) {
            return;
        }
        if (type == 0) {
            part1 = part1.removePart("tx/tx" + txBean.getRdId());
        } else {
            part2 = part2.removePart("tx/tx" + txBean.getRdId());
        }
    }

    /**
     * 翅膀专用移除特效 0:特效面板 1:多宝阁预览
     * 
     * @param type
     * @param skin
     */
    public void removeTX(int type, String skin) {
        if (type == 0) {
            part1 = part1.removePart("tx/" + skin + "0");
            part1 = part1.removePart("tx/" + skin + "1");
        } else {
            part2 = part2.removePart("tx/" + skin + "0");
            part2 = part2.removePart("tx/" + skin + "1");
        }
    }

    /** 界面切换处理 0:特效 1:装饰品 2:足迹 3翅膀 -1上一页 下一页 0上一页 1下一页 */
    public void Toggle(int type, int is) {
        if (type != -1) {
            txType = type;
            Toggle2(0);
        } else {
            Toggle2(txYs + (is == 0 ? -1 : 1));
        }
    }

    /** 切换 */
    public void Toggle2(int ys) {
        if (ys < 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("已经是首页");
            return;
        }
        if (txType == 3) {
            List<Goodstable> wingGoodsList = GoodsListFromServerUntil.getWingGoodsList();
            int size = wingGoodsList.size();
            if (size < ys * 25) {
                ZhuFrame.getZhuJpanel().addPrompt2("已经是尾页");
                return;
            }
            for (int i = 0; i < txImgs.length; i++) {
                if (i + ys * 25 < size) {
                    if (wingGoodsList.get(i + ys * 25) == null) {
                        continue;
                    }
                    if (txImgs[i] == null) {
                        txImgs[i] = new TxImg();
                    }
                    txImgs[i].setId(wingGoodsList.get(i + ys * 25).getRgid().intValue());
                    txImgs[i].setIcon(GoodsListFromServerUntil.imgpathAdaptive(wingGoodsList.get(i + ys * 25).getSkin(),49,49));
                } else {
                    txImgs[i] = null;
                }
            }
            txYs = ys;
        } else {
            List<Integer> txLis = getTX(txType + 1);
            int size = txLis.size();
            if (size < ys * 25) {
                ZhuFrame.getZhuJpanel().addPrompt2("已经是尾页");
                return;
            }
            for (int i = 0; i < txImgs.length; i++) {
                if (i + ys * 25 < size) {
                    RoleTxBean txBean = UserMessUntil.getTxBean(txLis.get(i + ys * 25));
                    if (txBean == null) {
                        continue;
                    }
                    if (txImgs[i] == null) {
                        txImgs[i] = new TxImg();
                    }
                    txImgs[i].setId(txBean.getGid());
                    txImgs[i].setIcon(GoodsListFromServerUntil.imgpathAdaptive("tx" + txBean.getRdId(),49,49));
                } else {
                    txImgs[i] = null;
                }
            }
            txYs = ys;
        }
    }

    /** 获取该类型的特效集合 */
    public List<Integer> getTX(int txType) {
        List<Integer> txList = new ArrayList<>();
        String tx = RoleData.getRoleData().getPackRecord().getTx();
        if (tx == null || tx.equals("")) {
            return txList;
        }
        String[] txs = tx.split("\\|");
        for (int i = 0; i < txs.length; i++) {
            if (txs[i].endsWith("#")) {
                txs[i] = txs[i].substring(0, txs[i].length() - 1);
            }
            RoleTxBean txBean = UserMessUntil.getTxBean(Integer.parseInt(txs[i]));
            if (txBean == null || txType != txBean.getRdType()) {
                continue;
            }
            txList.add(txBean.getGid());
        }
        return txList;
    }

    /** 初始化已穿戴的特效 负数脱对应位置装备 特效 装饰 足迹 翅膀 */
    public void EToggle(int id) {
        if (id < 0) {
            if (id == -4) {
                if (eTxImg[Math.abs(id) - 1] != null) {
                    removeTX(0, eTxImg[Math.abs(id) - 1].getWingSkin());
                }
                eTxImg[Math.abs(id) - 1] = null;
                return;
            }
            if (eTxImg[Math.abs(id) - 1] != null) {
                removeTX(0, eTxImg[Math.abs(id) - 1].getId());
            }
            eTxImg[Math.abs(id) - 1] = null;
            return;
        } else if (txType != 3) {
            RoleTxBean txBean = UserMessUntil.getTxBean(id);
            if (txBean == null) {
                return;
            }
            if (eTxImg[txBean.getRdType() - 1] == null) {
                eTxImg[txBean.getRdType() - 1] = new TxImg();
            }
            removeTX(0, eTxImg[txBean.getRdType() - 1].getId());
            addTX(0, txBean.getGid());
            eTxImg[txBean.getRdType() - 1].setId(txBean.getGid());
            eTxImg[txBean.getRdType() - 1].setIcon(GoodsListFromServerUntil.imgpathAdaptive("tx" + txBean.getRdId(),49,49));
        } else {
            Goodstable goodstable = getWing(id);
            if (goodstable != null) {
                if (eTxImg[3] == null) {
                    eTxImg[3] = new TxImg();
                }
                removeTX(0, eTxImg[3].getWingSkin());
                addTX(0, goodstable.getSkin());
                eTxImg[3].setWingSkin(goodstable.getSkin());
                eTxImg[3].setId(goodstable.getRgid().intValue());
                eTxImg[3].setIcon(GoodsListFromServerUntil.imgpathAdaptive(goodstable.getSkin(),49,49));
            }
        }
    }

    public void chushihuaWing() {
        Goodstable goodstable = GoodsListFromServerUntil.getChoseGoodsList()[12];
        if (goodstable != null) {
            if (eTxImg[3] == null) {
                eTxImg[3] = new TxImg();
            }
            removeTX(0, eTxImg[3].getWingSkin());
            addTX(0, goodstable.getSkin());
            eTxImg[3].setWingSkin(goodstable.getSkin());
            eTxImg[3].setId(goodstable.getRgid().intValue());
            eTxImg[3].setIcon(GoodsListFromServerUntil.imgpathAdaptive(goodstable.getSkin(),49,49));
        }
    }

    public int getTxYs() {
        return txYs;
    }

    // 获取当前位置的对象
    public RoleTxBean getTx(int p) {
        if (p < 0) {
            if (eTxImg[Math.abs(p) - 1] != null) {
                return UserMessUntil.getTxBean(eTxImg[Math.abs(p) - 1].getId());
            }
        } else {
            if (txImgs[p] != null) {
                return UserMessUntil.getTxBean(txImgs[p].getId());
            }
        }
        return null;
    }

    // 获取当前位置翅膀的对象
    public Goodstable getWing(int p) {
        if (p < 0) {
            if (eTxImg[3] == null || p != -4) {
                return null;
            }
            for (int i = 0; i < GoodsListFromServerUntil.getWingGoodsList().size(); i++) {
                if (eTxImg[3].getId() == GoodsListFromServerUntil.getWingGoodsList().get(i).getRgid().intValue()) {
                    return GoodsListFromServerUntil.getWingGoodsList().get(i);
                }
            }
        }
        if (txType != 3) {
            return null;
        }
        if (p + txYs * 25 >= GoodsListFromServerUntil.getWingGoodsList().size()) {
            return null;
        }
        return GoodsListFromServerUntil.getWingGoodsList().get(p + txYs * 25);
    }

    /** 获取当前穿戴的翅膀 */
    public Goodstable getWingGoods(int rgid) {
        for (int i = 0; i < GoodsListFromServerUntil.getWingGoodsList().size(); i++) {
            if (rgid == GoodsListFromServerUntil.getWingGoodsList().get(i).getRgid().intValue()) {
                return GoodsListFromServerUntil.getWingGoodsList().get(i);
            }
        }
        return null;
    }

    /**保存形象*/
    public void BCXX() {
        RoleData.getRoleData()
                .getPackRecord()
                .putTX(eTxImg[0] != null ? eTxImg[0].getId() + "" : null,
                        eTxImg[1] != null ? eTxImg[1].getId() + "" : null,
                        eTxImg[2] != null ? eTxImg[2].getId() + "" : null);
        Goodstable goodstable = GoodsListFromServerUntil.getChoseGoodsList()[12];
        if (goodstable != null) {
            goodstable.setStatus(0);
            GoodsMouslisten.gooduse(goodstable, 0);
        }
        if (eTxImg[3] != null) {
            Goodstable wingGoods = getWingGoods(eTxImg[3].getId());
            if (wingGoods != null) {
                GoodsListFromServerUntil.getChoseGoodsList()[12] = wingGoods;
                wingGoods.setStatus(1);
                GoodsMouslisten.gooduse(wingGoods, 0);
            }
        } else {
            GoodsListFromServerUntil.getChoseGoodsList()[12] = null;

        }
        ZhuFrame.getZhuJpanel().addPrompt2("保存成功");
        RoleProperty.getRoleProperty().equipWearOff();
        skin();
    }

    /** 皮肤数据更改 */
    public void skin() {
        RoleShow roleShow = ImageMixDeal.userimg.getRoleShow();
        String skin = roleShow.getSkin();
        if (skin == null) {
            skin = "";
        }
        List<String> list = RoleData.getRoleData().getPackRecord().getPutTX();
        String skin2 = TimeLimit.getskin(TimeLimit.getLimits().getSkin(),RoleData.getRoleData().getPackRecord().getPutTX(),roleShow);
        if (!skin2.equals(skin)) {
            if (skin2.equals("")) {
                skin2 = null;
            }
            roleShow.setSkin(skin2);
            RoleData.getRoleData().getLoginResult().setSkin(skin2);
            ImageMixDeal.userimg.changeskin(null);
            if (list != null && list.size() != 0) {
                StringBuffer buffer = new StringBuffer();
                buffer.append("E");
                buffer.append(list.get(0));
                for (int i = 1; i < list.size(); i++) {
                    buffer.append("_");
                    buffer.append(list.get(i));
                }
                if (skin2 == null) {
                    skin2 = buffer.toString();
                } else {
                    skin2 += "|" + buffer.toString();
                }
            }
            GoodsListFromServerUntil.sendPackRecord(5, skin2);
        }
    }
}
