package com.tool.time;

import javax.swing.JLabel;

public class Limit {
	// 时效名#时效类型#时效皮肤#剩余时效#时效描述
	// 时效名#时效类型#时效皮肤#剩余时效#时效描述
	// 时效名
	private String name;
	// 时效类型
	private String type;
	// 时效皮肤
	private String skin;
	// 剩余时效
	private long time;
	// 时效描述
	private String value;
	// 显示图像
	private JLabel jLabel;

	
	public Limit(String name, String type, String skin, long time, String value) {
		super();
		this.name = name;
		this.type = type;
		this.skin = skin;
		this.time = time;
		this.value = value;
	}

	public Limit() {
		// TODO Auto-generated constructor stub
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSkin() {
		return skin;
	}

	public void setSkin(String skin) {
		this.skin = skin;
	}

	public long getTime() {
		return time;
	}

	public void setTime(long time) {
		this.time = time;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public JLabel getjLabel() {
		return jLabel;
	}

	public void setjLabel(JLabel jLabel) {
		this.jLabel = jLabel;
	}

}
