package come.tool.map;

import come.tool.map.XLPath.XLP;
import org.come.bean.NpcInfoBean;
import org.come.model.Door;
import org.come.model.Gamemap;
import org.come.until.SplitStringTool;
import org.come.until.UserMessUntil;

import java.util.ArrayList;
import java.util.List;

public class XLMap {
	private int mapid;
	private List<XLP> xlpOne;//其他地图的传送
	private List<XLP> xlpTwo;//本地图内的传送
	public XLMap(int mapID) {
		// TODO Auto-generated constructor stub
		this.mapid=mapID;
		Gamemap gamemap=UserMessUntil.getAllmapbean().getAllMap().get(mapID+"");
		if (gamemap!=null) {
			if (gamemap.getMapway()!=null&&!gamemap.getMapway().equals("")) {
				List<String> strings = SplitStringTool.splitString(gamemap.getMapway());
				for (int i = 0; i < strings.size(); i++) {
					Door door=UserMessUntil.getDoor(strings.get(i));
					if (door!=null&&door.getRects()!=null) {
						addXLP(new XLP(door,mapID));
					}
				}
			}

			if (gamemap.getMapnpc() != null&&!gamemap.getMapnpc().equals("")) {
				List<String> strings = SplitStringTool.splitString(gamemap.getMapnpc());
				for (int i = 0; i < strings.size(); i++) {
					NpcInfoBean infoBean = UserMessUntil.getnpc(strings.get(i));
					if (infoBean!=null&&infoBean.getNpctable().getNpctype().equals("2")) {
						if (infoBean.getNpctable().getNpcway()!=null&&!infoBean.getNpctable().getNpcway().equals("")) {
							List<String> strings1 = SplitStringTool.splitString(infoBean.getNpctable().getNpcway());
							for (int j = 0; j < strings1.size(); j++) {
								Door door=UserMessUntil.getDoor(strings1.get(j));
								if (door!=null) {
									addXLP(new XLP(door,infoBean.getNpctable(),mapID));
								}
							}
						}
					}
				}
			}
		}
	}
	public void addXLP(XLP xlp){
		if (xlp.getMap()!=mapid) {
			if (xlpOne==null) {
				xlpOne=new ArrayList<>();
			}
			xlpOne.add(xlp);
		}else {
			if (xlpTwo==null) {
				xlpTwo=new ArrayList<>();
			}
			xlpTwo.add(xlp);
		}
	}
	public int getMapid() {
		return mapid;
	}
	public void setMapid(int mapid) {
		this.mapid = mapid;
	}
	public List<XLP> getXlpOne() {
		return xlpOne;
	}
	public void setXlpOne(List<XLP> xlpOne) {
		this.xlpOne = xlpOne;
	}
	public List<XLP> getXlpTwo() {
		return xlpTwo;
	}
	public void setXlpTwo(List<XLP> xlpTwo) {
		this.xlpTwo = xlpTwo;
	}
	
}
