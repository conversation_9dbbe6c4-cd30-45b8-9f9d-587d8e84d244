package jxy2.jutnil;


import com.tool.tcp.GetTcpPath;
import com.tool.tcp.SpriteFactory;
import jxy2.Config;
import jxy2.MemoryProfiler;
import jxy2.npk.NpkImageReader;

public class JPanelUiloading{
    /**预加载*/
    public JPanelUiloading() {
//        new Thread(new CheckTimer()).start();
        long totalStart = System.currentTimeMillis();
        Config.reload();

        // 预加载NPK文件 - 在其他资源加载之前
        long npkStart = System.currentTimeMillis();
        NpkImageReader.preloadNpkFiles();
        long npkEnd = System.currentTimeMillis();
        System.out.println("[LOADING] NPK预加载总耗时: " + (npkEnd - npkStart) + "ms");
        MemoryProfiler.checkpoint("NPK预加载完成");

        long juiStart = System.currentTimeMillis();
        Thread.ofVirtual().start(() -> {
        try {
            Juitil.loadImagesInBackground();
        } catch (Exception e) {
            System.err.println("图像加载出错: " + e.getMessage());
            // 继续执行，不中断程序
            }
        });
        long juiEnd = System.currentTimeMillis();
        System.out.println("[LOADING] Juitil.loadImagesInBackground() 耗时: " + (juiEnd - juiStart) + "ms");
        MemoryProfiler.checkpoint("Juitil图像加载完成");

        //预加载特技
        SpriteFactory.Prepare(GetTcpPath.getMouseTcp("悟技"));

        long totalEnd = System.currentTimeMillis();
        System.out.println("[LOADING] 游戏资源预加载完成，总耗时: " + (totalEnd - totalStart) + "ms");
        MemoryProfiler.checkpoint("游戏启动完成");
        MemoryProfiler.showDetailedMemory("启动完成状态");
        MemoryProfiler.showAllCheckpoints();
    }
}
