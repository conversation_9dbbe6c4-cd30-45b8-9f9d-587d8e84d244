package come.tool.JDialog;

import com.tool.role.RoleData;
import jxy2.LawGate.LawGateMainJPanel;
import org.come.Frame.ZhuFrame;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class ResetLawGateJDialog implements TiShiChuLi {
    @Override
    public void tipBox(boolean tishi, Object object) {
        if (tishi){
            if (RoleData.getRoleData().getLoginResult().getGold().longValue() < 200000) {
                ZhuFrame.getZhuJpanel().addPrompt2("金钱不足");
                return;
            }
            RoleData.getRoleData().getPrivateData().setSkills("F",null);
            String mes = Agreement.getAgreement().rolechangeAgreement("R");
            SendMessageUntil.toServer(mes);
            for (int i = 0; i < LawGateMainJPanel.lawGateJPanels.length; i++) {
                LawGateMainJPanel.lawGateJPanels[i].testMes.setVisible(true);
                LawGateMainJPanel.lawGateJPanels[i].K = 0;
                LawGateMainJPanel.lawGateJPanels[i].isv = false;
                for (int k = 0; k < 2; k++) {
                    LawGateMainJPanel.lawGateJPanels[i].practice[k].setVisible(false);
                    LawGateMainJPanel.lawGateJPanels[i].skillValue[k].setText("0/10000");
                }
                LawGateMainJPanel.lawGateJPanels[i].study.setVisible(true);
            }
        }
    }
}
