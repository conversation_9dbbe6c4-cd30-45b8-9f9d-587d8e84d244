package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.RewardHallJpanel;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

import com.tool.role.RoleData;

public class LotteryBtn extends MoBanBtn {

    private RewardHallJpanel rewardHallJpanel;

    public LotteryBtn(String iconpath, int type, Color[] colors, Font font, String text,
            RewardHallJpanel rewardHallJpanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.rewardHallJpanel = rewardHallJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {

        // 先判断自己的积分是否足以抽奖
        if (RoleData.getRoleData().getLoginResult().getScoretype("帮派积分") == null
                || RoleData.getRoleData().getLoginResult().getScoretype("帮派积分").compareTo(new BigDecimal(50)) <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt("您的战功不足以进行抽奖活动！！");
            return;
        }
        // 修改抽奖界面的值
        rewardHallJpanel.setTotalsum(rewardHallJpanel.getTotalsum().subtract(new BigDecimal(50)));
        rewardHallJpanel.setLotteryNum(rewardHallJpanel.getLotteryNum().subtract(new BigDecimal(1))
                .compareTo(new BigDecimal(0)) >= 0 ? rewardHallJpanel.getLotteryNum().subtract(new BigDecimal(1))
                : new BigDecimal(0));

        // 发送服务器
        String senmes = null;
        try {
            senmes = Agreement.getAgreement().drawnitemsAgreement();
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        SendMessageUntil.toServer(senmes);
    }

}
