package com.tool.btn;

import com.tool.image.ImageMixDeal;
import com.tool.image.ManimgAttribute;
import com.tool.imagemonitor.PlayerMonitor;
import com.tool.role.RoleData;
import com.tool.role.SkillUtil;
import com.tool.tcpimg.UIUtils;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import org.come.Frame.*;
import org.come.Jpanel.*;
import org.come.bean.ChatBean;
import org.come.bean.PetOperationDTO;
import org.come.bean.Role_bean;
import org.come.entity.Friendtable;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.ChangeMouseSymbolMouslisten;
import org.come.mouslisten.GoodsMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.test.Main;
import org.come.until.*;
import org.skill.frame.SkillMainFrame;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class RoleCaoZuoBtn extends MoBanBtn {

    private int index;
    private AddFriendJpanel addFriendJpanel;

    public RoleCaoZuoBtn(String iconpath, int type, String text, int index) {
        super(iconpath, type);
        this.setText(text);
        if (text.equals("加为好友") || text.equals("申请入队") || text.equals("搜 索") || text.equals("发 送")) {
            setFont(UIUtils.TEXT_TIP);
        } else {
            setFont(UIUtils.TEXT_FONT1);
        }
        setForeground(Color.orange);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.index = index;
    }

    public RoleCaoZuoBtn(String iconpath, int type, Color[] colors, Font font, String text, int index) {
        super(iconpath, type);
        this.setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.index = index;
    }

    public RoleCaoZuoBtn(String iconpath, int type, int index, String labelName,String string) {
        super(iconpath, type,0,string,labelName);
        this.index = index;
    }


    public RoleCaoZuoBtn(String iconpath, int type, String text, int index, Color[] colors) {
        super(iconpath, type, colors);
        this.index = index;
    }

    public RoleCaoZuoBtn(String iconpath, int type, String text,String prowpt,int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, prowpt,text);
        this.index=index;
    }



    public RoleCaoZuoBtn(String iconpath, int type, int index, AddFriendJpanel addFriendJpanel) {
        super(iconpath, type);
        this.index = index;
        this.addFriendJpanel = addFriendJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }
    @Override
    public void nochoose(MouseEvent e) {
        if (this.getText().equals("确认") && FightingMixDeal.State == HandleState.USUAL && index == 0) {
            sureRenMing();
        } else if (this.getText().equals("取消") && FightingMixDeal.State == HandleState.USUAL && index == 1) {
            FormsManagement.HideForm(37);
        } else if (FightingMixDeal.State == HandleState.USUAL && index == 3) {
            sureZhuanSheng();
        } else if (this.getText().equals("取 消") && FightingMixDeal.State == HandleState.USUAL && index == 3) {
            FormsManagement.HideForm(41);
        } else if (this.getNtext().equals("吐出内丹") && FightingMixDeal.State == HandleState.USUAL && index == 0
                && UserMessUntil.getChosePetMes() != null) {
            spitOutNeDan();
        } else if (this.getNtext().equals("转换经验") && FightingMixDeal.State == HandleState.USUAL && index == 0) {
            conversionExp();
        } else if (this.getNtext().equals("发送") && index == 5) {
            sendMessageUntil();
            FriendChatMessageJframe chatjframe =  FormsManagement.getOrCreateFriendChatFrame(FriendChatMessageJframe.getRolename());
            if (!FriendChatMessageJpanel.closeCk) {
                Main.frame.getLoginJpanel().getGameView().remove(chatjframe);
                chatjframe.setVisible(false);
            }
        } else if (this.getText().equals("取消") && index == 5) {
//            FriendChatMessageJframe.getFriendChatMessageJframe().getJpanel().getSetwords().setText("");

        } else if (index == 6) {
            Role_bean bean = AddFriendJframe.getAddFriendJframe().getAddFriendJpanel().getRole();
            if (bean == null) {
                return;
            }
            if (this.getText().equals("加为好友")) {
                PlayerMonitor.addFriend(bean.getRole_id(), bean.getRolename());
            } else if (this.getText().equals("申请入队")) {
                PlayerMonitor.teamApply(bean.getRole_id());
            }
        } else if (this.getText().equals("搜 索") && index == 7) {
            String text = AddFriendJframe.getAddFriendJframe().getAddFriendJpanel().getFieldText();
            String type = AddFriendJframe.getAddFriendJframe().getAddFriendJpanel().getTj();
            String msg = null;
            if (type.equals("数字ID")) {
                try {
                    Integer.parseInt(text);
                    msg = Agreement.getAgreement().searcahChatRoleIdAgreement(text);
                } catch (Exception e2) {
                    // TODO: handle exception
                    return;
                }
            } else if (type.equals("昵称")) {
                msg = Agreement.getAgreement().searcahChatRoleNameAgreement(text);
            }
            if (msg != null) {
                AddFriendJframe.getAddFriendJframe().getAddFriendJpanel().CF(null);
                SendMessageUntil.toServer(msg);
            }
        } else if (index == 8) {// 断交
            Friendtable friend = FriendMsgJframe.getFriendMsgJframe().getMsgJpanel().getFriend();
            PlayerMonitor.deleteFriden(friend.getRolename());
        } else if (index == 9) {// 历史消息
            Friendtable friend = FriendMsgJframe.getFriendMsgJframe().getMsgJpanel().getFriend();
//            FriendChatMessageJframe.getFriendChatMessageJframe().getJpanel()
//                    .showFriend(friend, MessagrFlagUntil.getRichLabel(friend.getRolename()));
            // 打开聊天面板
            FormsManagement.showForm(56);
        } else if (index == 10) {
            Friendtable friend = FriendMsgJframe.getFriendMsgJframe().getMsgJpanel().getFriend();
            if (this.getText().equals("发送消息")) {
                // 打开聊天面板
//                FriendChatMessageJframe.getFriendChatMessageJframe().getJpanel()
//                        .showFriend(friend, MessagrFlagUntil.getRichLabel(friend.getRolename()));
                FormsManagement.showForm(56);
                return;
            } else if (this.getText().equals("申请入队")) {
                PlayerMonitor.teamApply(friend.getRole_id());
                return;
            }
            ManimgAttribute attribute = ImageMixDeal.huoquLogin(friend.getRolename());
            if (attribute == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("玩家离你太远了");
                return;
            }
            if (friend.getRolename().equals(ImageMixDeal.userimg.getRoleShow().getRolename())) {
                ZhuFrame.getZhuJpanel().addPrompt2("不能对自己操作");
                return;
            }
            if (this.getText().equals("交易")) {
                PlayerMonitor.transApply(friend.getRolename());
            }
        } else if (index == 11 || index == 12) {
            addFriendJpanel.labMenuChange(index);
        }else if (index==13){

        }
    }

    /**
     * 任命
     */
    public void sureRenMing() {

        try {
            FactionCardJpanel factionCardJpanel = FactionMainJframe.getFactionMainJframe().getFactionMainJpanel()
                    .getFactionCardJpanel();
            factionCardJpanel
                    .getGangResultBean()
                    .getRoleTables()
                    .get(ApointJpanel.index)
                    .setGangpost(
                            ApointJframe
                                    .getApointJframe()
                                    .getapointJpanel()
                                    .getListModel()
                                    .get(ApointJframe.getApointJframe().getapointJpanel().getListposition()
                                            .getSelectedIndex()));
            factionCardJpanel.getFactionMemberJpanel().showMenuMessage(factionCardJpanel.getGangResultBean());
            String sendMes = Agreement.GangAppointAgreement(factionCardJpanel.getGangResultBean().getRoleTables()
                    .get(ApointJpanel.index).getRole_id()
                    + "|" + ApointJframe.getApointJframe().getapointJpanel().getListposition().getSelectedValue());
            // 向服务器发送信息
            SendMessageUntil.toServer(sendMes);
        } catch (Exception e2) {
            // TODO: handle exception
        }
        FormsManagement.HideForm(37);

    }

    /** 转换种族/转生 */
    public void sureZhuanSheng() {
        RaceChangeMainJpanel raceChangeMainJpanel = RaceChangeMainJframe.getRaceChangeMainJframe()
                .getRaceChangeMainJpanel();
        int zhuantype = raceChangeMainJpanel.getLeixing();
        if (raceChangeMainJpanel.getSpecies_id() == null) {
            ZhuFrame.getZhuJpanel().addPrompt2("选中要转换的角色");
            return;
        }
        for (int i = 0; i < GoodsListFromServerUntil.getChoseGoodsList().length; i++) {
            if (GoodsListFromServerUntil.getChoseGoodsList()[i] != null) {// 先判断是否有穿装备

                if (zhuantype == 0) {
                    ZhuFrame.getZhuJpanel().addPrompt2("转换种族前必须要卸下所有装备！");
                } else {
                    ZhuFrame.getZhuJpanel().addPrompt2("转生前必须要卸下所有装备！");
                }
                return;
            }
        }

        String seName = SkillUtil.getSepciesN(raceChangeMainJpanel.getSpecies_id());
        String[] vs = SkillUtil.getSepcieswas(seName);
        SkillMainFrame.getSkillMainFrame().getSkillMainPanel().getCopyOfSkillTYCPanel().Roelder(seName, vs);//重新加载

        RoleData.getRoleData().getLoginResult().setLawlick(null);
        SendRoleAndRolesummingUntil.sendRoleResult(RoleData.getRoleData().getLoginResult());
        String sendmes = Agreement.getAgreement().RacialTransformationAgreement(
                zhuantype + "|" + raceChangeMainJpanel.getSpecies_id());
        SendMessageUntil.toServer(sendmes);
    }

    /**
     * 吐出内丹
     */
    public void spitOutNeDan() {
        // 判断召唤兽是否被加锁
        if (UserMessUntil.getChosePetMes().getPetlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt(
                    "召唤兽" + UserMessUntil.getChosePetMes().getSummoningname() + "已被加锁，不可吐出内丹！！");
            return;
        }
        // 先判断背包是否还有空位
        int packNumber = GoodsListFromServerUntil.Surplussum("-1", "-1", 999);
        if (packNumber <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt("背包已满！！！");
            return;
        }
        String[] strings = UserMessUntil.getChosePetMes().getInnerGoods().split("\\|");
        if (strings.length > 0) {
            StringBuilder values = new StringBuilder();
            for (String string : strings)
                if (ZhuJpanel.getNedangoods() != null
                        && ZhuJpanel.getNedangoods().getRgid().compareTo(new BigDecimal(string)) == 0) {
                    ZhuJpanel.getNedangoods().setStatus(0);
                    GoodsListFromServerUntil.fushis.remove(ZhuJpanel.getNedangoods().getRgid());
                    GoodsListFromServerUntil.newgood(ZhuJpanel.getNedangoods());
                    GoodsMouslisten.gooduse(ZhuJpanel.getNedangoods(), 0);
                    FormsManagement.HideForm(47);
                    ZhuJpanel.setNedangoods(null);
                } else {
                    if (values.length() > 0)
                        values.append("|");
                    values.append(string);
                }

            UserMessUntil.getChosePetMes().setInnerGoods(values.toString());
            PetOperationDTO dto = new PetOperationDTO();
            dto.setPetId(UserMessUntil.getChosePetMes().getSid());
            dto.setOperationType("SPIT_OUT_THE_INNER_ALCHEMY");
            dto.setEventType(values.toString());
            ChangeMouseSymbolMouslisten.refreshNedan(UserMessUntil.getChosePetMes());
            SendRoleAndRolesummingUntil.sendRoleSumming(dto);
        }

    }

    /** 转换经验 */
    public void conversionExp() {
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        if (pet != null) {
            if (ZhuJpanel.getNedangoods().getRgid() == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选中的内丹");
            }
            String sendmes = Agreement.getAgreement().userpetAgreement(
                    "ND|" + pet.getSid() + "|" + ZhuJpanel.getNedangoods().getRgid());
            SendMessageUntil.toServer(sendmes);
        }
    }

    /**
     * 发送消息
     */
    public static void sendMessageUntil() {
        try {
        FriendChatMessageJframe chatjframe =  FormsManagement.getOrCreateFriendChatFrame(FriendChatMessageJframe.getRolename());
            String text =chatjframe.getJpanel().getSetwords().getText();
            if (text.isEmpty()||text.equals("\n")) {
                ZhuFrame.getZhuJpanel().addPrompt2("发送消息不能为空");
                return;
            }
            chatjframe.getJpanel().getSetwords().setText("");

            // 发送信息给服务器
            ChatBean chatBean = new ChatBean();
            chatBean.setRolename(ImageMixDeal.userimg.getRoleShow().getRolename());
            if (chatjframe.getJpanel().getHyName().getText() != null && !chatjframe.getJpanel().getHyName().getText().isEmpty())
                chatBean.setFriendName(chatjframe.getJpanel().getHyName().getText());
            else
                chatBean.setFriendName(UserMessUntil.getChatFriendName());

            chatBean.setMessage(text);
            chatBean.setTime(Util.getTime());
            String mes = Agreement.getAgreement()
                    .friendchatAgreement(GsonUtil.getGsonUtil().getgson().toJson(chatBean));
            // 向服务器发送信息
            SendMessageUntil.toServer(mes);
            // 发送成功将发送的信息添加到聊天记录框
            MessagrFlagUntil.ReceiveMessage(chatBean, chatBean.getFriendName());
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }
}
