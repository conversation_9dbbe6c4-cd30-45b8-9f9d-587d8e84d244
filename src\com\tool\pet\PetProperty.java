package com.tool.pet;

import com.tool.btn.GuardMountBtn;
import com.tool.btn.PetPanelBtn;
import com.tool.role.GetExp;
import com.tool.role.Ql;
import com.tool.role.RoleData;
import jxy2.petView.PetSkillQiLingBtn;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.Guardian;
import org.come.bean.LoginResult;
import org.come.bean.Skill;
import org.come.entity.*;
import org.come.model.PalData;
import org.come.until.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class PetProperty {
	static String[] evs=new String[]{"根骨=","灵性=","力量=","敏捷=","炼化属性","增加气血","增加法力","增加攻击"};
	private static Ql ql;
	public static Ql getQl(RoleSummoning pet) {
		if (ql==null)ql=new Ql();
		return AccessNedanMsgUntil.getPetQl(ql,pet);
	}
	public static Ql getQl(Pal pal) {
		if (ql==null)ql=new Ql();
		return AccessNedanMsgUntil.getPalQl(ql,pal);
	}
	public static void ShowQl(RoleSummoning pet){
		PetPanelBtn.showRolesumming(getQl(pet));
	}
	public static void ShowQl(Pal pal){
		PetPanelBtn.showRolesumming(getQl(pal));
	}
	/**获取伙伴的hp,mp,ap,sp*/
	public static int[] getPalHMASp(Pal pal,PalData palData){
		int[] pals=new int[4];
		double grow=pal!=null?pal.getGrow():1D;
        Integer grade = RoleData.getRoleData().getLoginResult().getGrade();
        int lvl = AnalysisString.lvlint(grade);
        int zs = AnalysisString.lvltrue(grade);
		if (pal!=null) {
			Map<String, Double> map=AccessNedanMsgUntil.getPalMap(pal, true);
			pals[0]=getBase(lvl, (int)AccessNedanMsgUntil.getMapValue(map, "根骨"), grow, palData.getHp(), 0, map);
			pals[1]=getBase(lvl, (int)AccessNedanMsgUntil.getMapValue(map, "灵性"), grow, palData.getMp(), 1, map);
			pals[2]=getBase(lvl, (int)AccessNedanMsgUntil.getMapValue(map, "力量"), grow, palData.getAp(), 2, map);
			pals[3]=getBase(lvl, (int)AccessNedanMsgUntil.getMapValue(map, "敏捷"), grow, palData.getSp(), 3, map);
			map.clear();
		}else {
			int[] jds=palData.getJds();
			int size=jds[0]+jds[1]+jds[2]+jds[3];
			int point=lvl+zs*40;//分配点数
			point=lvl%size;
			for (int i = 0; i < pals.length; i++) {
				pals[i]+=lvl+point/size*jds[i];
				if (point>0&&jds[i]>0) {
					if (point>palData.getJds()[i]) {
						pals[i]+=jds[i];
						point-=jds[i];
					}else {
						pals[i]+=point;
						point=0;
					}
				}
			}
			pals[0]=getRoleValue(lvl, pals[0], grow, palData.getHp(), 0);
			pals[1]=getRoleValue(lvl, pals[1], grow, palData.getMp(), 1);
			pals[2]=getRoleValue(lvl, pals[2], grow, palData.getAp(), 2);
			pals[3]=getRoleValue(lvl, pals[3], grow, palData.getSp(), 3);

		}
		return pals;
	}
	public static int getBase(int lvl,int P,double G, int base, int type,Map<String,Double> map){
		int value=getRoleValue(lvl,P,G,base,type);
		if (type==0) {
			value+= (int) AccessNedanMsgUntil.getMapValue(map, "hp");
			value+= (int) AccessNedanMsgUntil.getMapValue(map, "HP");
			value+= (int) AccessNedanMsgUntil.getMapValue(map, "加气血");
			value+= (int) AccessNedanMsgUntil.getMapValue(map, "气血");
			value+= (int) AccessNedanMsgUntil.getMapValue(map, "附加气血");
			value*= (int) (AccessNedanMsgUntil.getMapValue(map, "HP成长")+1);
			value*= (int) (AccessNedanMsgUntil.getMapValue(map, "加强气血")/100+1);
		}else if (type==1) {
			value+= (int) AccessNedanMsgUntil.getMapValue(map,"mp");
			value+= (int) AccessNedanMsgUntil.getMapValue(map,"MP");
			value+= (int) AccessNedanMsgUntil.getMapValue(map,"法力");
			value+= (int) AccessNedanMsgUntil.getMapValue(map,"加魔法");
			value+= (int) AccessNedanMsgUntil.getMapValue(map,"附加魔法");
			value*= (int) (AccessNedanMsgUntil.getMapValue(map,"MP成长")+(AccessNedanMsgUntil.getMapValue(map,"加强魔法")/100)+1);
		}else if (type==2) {
			value += (int) AccessNedanMsgUntil.getMapValue(map,"ap");
			value += (int) AccessNedanMsgUntil.getMapValue(map,"AP");
			value += (int) AccessNedanMsgUntil.getMapValue(map,"攻击");
			value += (int) AccessNedanMsgUntil.getMapValue(map,"加攻击");
			value *= (int) (AccessNedanMsgUntil.getMapValue(map,"AP成长")+(AccessNedanMsgUntil.getMapValue(map,"加强攻击")/100)+1);
		}else if (type==3) {
			value +=(int) AccessNedanMsgUntil.getMapValue(map,"sp");
			value +=(int) AccessNedanMsgUntil.getMapValue(map,"SP");
			value +=(int) AccessNedanMsgUntil.getMapValue(map,"速度");
			value +=(int) AccessNedanMsgUntil.getMapValue(map,"加速度");
			value +=(int) AccessNedanMsgUntil.getMapValue(map,"附加速度");
			value *= (int) (AccessNedanMsgUntil.getMapValue(map,"SP成长")+(AccessNedanMsgUntil.getMapValue(map,"加强速度")/100)+1);
		}
		return value;
	} 
	/**获取召唤兽hp mp ap sp*/
	public static int[] getPetHMASp(RoleSummoning pet){
		if (pet==null)return new int[]{0};
		integerSumMap.clear();
		decimalSumMap.clear();
		int[] pets=new int[5];
		int lvl=AnalysisString.petLvlint(pet.getGrade());
		double grow=Double.parseDouble(pet.getGrowlevel());
		pets[0]=pet.getHp();
		pets[1]=pet.getMp();
		pets[2]=pet.getAp();
		pets[3]=pet.getSp();
		pets[4]=0;
		if (grow>=2.5||pets[0]>3500||pets[1]>3500||pets[2]>3500||pets[3]>2500) {
			JmSum.xiugaiqi();
		}
		int zBone=pet.getBone();
		int zSpir=pet.getSpir();
		int zPower=pet.getPower();
		int zSpeed=pet.getSpeed();
		int zCalm=pet.getCalm();
		int addhp=0,addmp=0,addap=0;
		
		if (pet.getStye()!=null&&pet.getStye().length()>1) {
			String[] v=pet.getStye().split("\\|");
			for (int i = 1; i < v.length; i++) {
				String[] vs=v[i].split("-");
				if (vs.length>=2) {
					Goodstable goodstable=GoodsListFromServerUntil.fushis.get(new BigDecimal(vs[1]));
					if (goodstable!=null) {
						String[] t=goodstable.getValue().split("\\|");
						for (int j = 0; j < t.length; j++) {
							if (t[j].startsWith(evs[0])) {
								String[] ts = t[j].split("=");
								zBone += Integer.parseInt(ts[1]);
							}else if (t[j].startsWith(evs[1])) {
								String[] ts = t[j].split("=");
								zSpir += Integer.parseInt(ts[1]);
							}else if (t[j].startsWith(evs[2])) {
								String[] ts = t[j].split("=");
								zPower += Integer.parseInt(ts[1]);
							}else if (t[j].startsWith(evs[3])) {
								String[] ts = t[j].split("=");
								zSpeed += Integer.parseInt(ts[1]);
							}else if (t[j].startsWith(evs[4])) {
								String[] vStrings =t[j].split("\\&");
								for (int k = 1; k < vStrings.length; k++) {
									if (vStrings[k].startsWith(evs[5])) {
										String[] mes = vStrings[k].split("=");
										addhp+= (int) Double.parseDouble(mes[1]);
									}else if (vStrings[k].startsWith(evs[6])) {
										String[] mes = vStrings[k].split("=");
										addmp+= (int) Double.parseDouble(mes[1]);
									}else if (vStrings[k].startsWith(evs[7])) {
										String[] mes = vStrings[k].split("=");
										addap+= (int) Double.parseDouble(mes[1]);
									}
								}
							}
						}
					}
				}
			}
		}
		pets[0]=getRoleValue(lvl,zBone ,grow,pets[0],0)+addhp;
		pets[1]=getRoleValue(lvl,zSpir ,grow,pets[1],1)+addmp;
		pets[2]=getRoleValue(lvl,zPower,grow,pets[2],2)+addap;
		pets[3]=getRoleValue(lvl,zSpeed,grow,pets[3],3);
		pets[4]=getRoleValue(lvl,zCalm ,grow,pets[4],4);
		//获取四维的数值加成
		try {
			pet.getSI(pets);
		} catch (Exception e) {
			System.err.println("[PetProperty] getSI 异常: " + e.getMessage());
		}
		//获取灵犀加成
		try {
			pet.getLX(pets);
		} catch (Exception e) {
			System.err.println("[PetProperty] getLX 异常: " + e.getMessage());
		}
		//获取召唤兽技能悟灵阶属性加成
		if (pet.getQiling()>0&&pet.getPetSkills()!=null&&pet.getPetSkills().contains("1831")&& PetSkillQiLingBtn.isSkillLingjieEnabled(pet,"1831")){
			int level = PetSkillQiLingBtn.getSkillLingjieLevel(pet, "1831");
			pets[2]+=level*2000;
		}

		for (int i = 0; i < ZhuJpanel.getListMount().size(); i++) {//坐骑加成属性
			Mount mount=ZhuJpanel.getListMount().get(i);

			if(mount.isID(pet.getSid())){
				List<MountSkill> mountSkills=mount.getMountskill();
				if (mountSkills!=null) {
					double xs1=1,xs2=1,xs3=1,xs4=1;
					for (int j = 0; j < mountSkills.size(); j++) {
						String ms=Util.calculateAdditionBase(mount,mountSkills.get(j).getSkillname());
						if (ms!=null) {
							String[] v1 =ms.split("=");
                            switch (v1[0]) {
                                case "HP":
                                    xs1 += Double.parseDouble(v1[1]);
                                    break;
                                case "MP":
                                    xs2 += Double.parseDouble(v1[1]);
                                    break;
                                case "AP":
                                    xs3 += Double.parseDouble(v1[1]);
                                    break;
                                case "SP":
                                    xs4 += Double.parseDouble(v1[1]);
                                    break;
                            }
						} 	
					}
					pets[0]*=xs1;
					pets[1]*=xs2;
					pets[2]*=xs3;
					pets[3]*=xs4;
				}
					if (mount.getMountstone()!=null){
						Goodstable goodstable = GoodsListFromServerUntil.getRgid(mount.getMountstone());
						if (goodstable!=null) {
							String[] v = goodstable.getValue().split("&");
							for (String property : v) {
								if (property.startsWith("特技")) {
									String[] zhi = property.split("=");
									if (zhi[0].equals("特技")) {
										for (int k = 1; k < zhi.length; k++) {
											Skill skill = UserMessUntil.getSkillId(zhi[k]);
											if (skill != null) {
//												System.out.println(skill.getSkillname());
											}
										}
									}
								} else {
									String[] entries = property.split("\\|");
									for (String entry : entries) {
										String[] keyValue = entry.split("=");
										if (keyValue.length != 2) {
											continue; // 跳过格式错误的条目
										}
										String key = keyValue[0];
										if (key.startsWith("耐久度") || key.startsWith("锻造等级")) {
											continue; // 忽略特定的属性
										}
										double value = Double.parseDouble(keyValue[1]);
                                        switch (key) {
                                            case "附加攻击":
                                                pets[0] += value;
                                                break;
                                            case "附加气血":
                                                pets[1] += value;
                                                break;
                                            case "附加魔法":
                                                pets[2] += value;
                                                break;
                                            case "附加速度":
                                                pets[3] += value;
                                                break;
                                        }
									}
								}
							}
						}
					}
				break;
			}
		}

		//TODO 坐骑守护石缺少四项属性加成显示

		//获得坐骑守护四象属性，四位属性加成
		getMount(getMount(getMount(pet)));
		String fourGod = getMount(getMount(pet));
		int ztsh = 0;
		for (Map.Entry<String, Double> entry : decimalSumMap.entrySet()) {
			String key = entry.getKey();
			double value = entry.getValue();
            if (fourGod != null && fourGod.equals(key.substring(0, 2))) {
                ztsh = (int) value;
				break;
            }
        }
		double xs1 = 0, xs2 = 0, xs3 = 0, xs4 = 0;
		double cz1 = 0, cz2 = 0, cz3 = 0, cz4 = 0;
		for (Map.Entry<String, Double> entry : integerSumMap.entrySet()) {
			String key = entry.getKey();
			double value = entry.getValue();
			// 首先处理四维成长
			switch (key){
				case "AP成长":
					cz3 = (value * (100 + ztsh)) / 100; // 计算AP成长的实际影响
					break;
				case "HP成长":
					cz1 = (value * (100 + ztsh)) / 100; // 计算AP成长的实际影响
					break;
				case "MP成长":
					cz2 = (value * (100 + ztsh)) / 100; // 计算AP成长的实际影响
					break;
				case "SP成长":
					cz4 = (value * (100 + ztsh)) / 100; // 计算AP成长的实际影响
					break;
			}
			// 根据cz2是否已计算，决定是否应用到其他属性
				switch (key) {
					case "气血":
						xs1 += value;
						xs1 += value * cz1 / 100; // 增加由HP成长决定的百分比
						break;
					case "法力":
						xs2 += value;
						xs2 += value * cz2 / 100; // 增加由MP成长决定的百分比
						break;
					case "攻击":
						xs3 += value; // 基础值增加
						xs3 += value * cz3 / 100; // 增加由AP成长决定的百分比
						break;
					case "速度":
						xs4 += value;
						xs4 += value * cz4 / 100; // 增加由SP成长决定的百分比
						break;
				}
		}
		pets[0] += (int) xs1;
		pets[1] += (int) xs2;
		pets[2] += (int) xs3;
		pets[3] += (int) xs4;

		return pets;
	}

	public static Map<String, Double> integerSumMap = new HashMap<>();
	public static Map<String, Double> decimalSumMap = new HashMap<>();
	/**根据四象名称还获得属性*/
	public static void getMount(String guardianid) {
		LoginResult loginResult = RoleData.getRoleData().getLoginResult();
		if (guardianid!=null) {
			int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData(guardianid));
			int tsp = GetExp.getTSP(parseInt);
			Set<Map.Entry<String, List<Guardian>>> entrySet = UserMessUntil.getAllGuardian().getAllGuardian().entrySet();
			for (Map.Entry<String, List<Guardian>> entry : entrySet) {
				if (entry.getKey().equals(guardianid)) {
					for (int i = 0; i < entry.getValue().size(); i++) {
						Guardian guardian = entry.getValue().get(i);
						if (tsp >= Integer.parseInt(guardian.getGuardianlevel())) {
							String valueStr = guardian.getGuardianvalue();
							String key = guardian.getGuardiankey();
							double value = Double.parseDouble(valueStr);
							integerSumMap.put(key, integerSumMap.getOrDefault(key, 0.0) + value);
						}
					}
					break;
				}
			}
		}

		int parseInt = loginResult.getExtraPointInt(GuardMountBtn.TypeDianNumData("中天"));
		int tsp = GetExp.getTSP(parseInt);
		Set<Map.Entry<String, List<Guardian>>> entrySet = UserMessUntil.getAllGuardian().getAllGuardian().entrySet();
		for (Map.Entry<String, List<Guardian>> entry : entrySet) {
			if (entry.getKey().equals("中天")) {
				for (int i = 0; i < entry.getValue().size(); i++) {
					Guardian guardian = entry.getValue().get(i);
					if (tsp >= Integer.parseInt(guardian.getGuardianlevel())) {
						String valueStr = guardian.getGuardianvalue();
						String key = guardian.getGuardiankey();
						double value = Double.parseDouble(valueStr);
						decimalSumMap.put(key, decimalSumMap.getOrDefault(key, 0.0) + value);
					}
				}
				break;
			}
		}
	}



	/**根据召唤兽获取到它管制的坐骑*/
	public static Mount getMount(RoleSummoning pet) {
		for (int i = 0; i < ZhuJpanel.getListMount().size(); i++) {//坐骑加成属性
			Mount mount = ZhuJpanel.getListMount().get(i);
			if (mount.isID(pet.getSid())) {
				return mount;
			}
		}
		return null;
	}


	/**根据坐骑返回对应的四象之力*/
	//取下守护管制后，清除
	public static String getMount(Mount mount) {
		if (mount==null)return null;
		LoginResult loginResult = RoleData.getRoleData().getLoginResult();
		String[] guardMount = loginResult.getMountid().split("\\|");
        for (String string : guardMount) {
            String name = string.split("=")[0].split("-")[1];
				String[] mountid = string.split("=")[1].split("_");
				for (String s : mountid) {
					//没有守护的坐骑那么这只召唤兽无法获得守护属性
					if (!s.equals("0")&&s.contains(mount.getMid()+"")){
						return name;
					}
				}
        }
			return null;
	}

	public static int getRoleValue(int lvl, int P, double G, int base, int type) {
		if (type == 0 || type == 1) {
			return (int) (lvl * P * G) + (int) ((0.7 * lvl * G + 1) * base);
		} else if (type == 2) {
			return (int) (lvl * P * G / 5)
					+ (int) ((0.14 * lvl * G + 1) * base);
		}else if (type == 3) {
			return (int) ((base + P) * G);
		}else {
			return P;
		}
	}
}
