package come.tool.BangBattle;

import java.awt.Graphics;
import java.awt.Image;

import javax.swing.ImageIcon;

import org.come.bean.PathPoint;
import org.come.control.BangBattleControl;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.ScrenceUntil;
import org.come.until.Util;

import com.tool.image.ImageMixDeal;
import com.tool.tcp.GetTcpPath;
import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteFactory;
public class BangFight {
	//最小人数
	public static int MINSUM=3;
	private static BangFight bangFight;
	public static BangFight getBangFight() {
		if (bangFight==null)bangFight=new BangFight();
		return bangFight;
	}
	public BangFight() {
		// TODO Auto-generated constructor stub
		
	}
	//绘制帮战血条 548 56   226 13 //1 11
	private Image t_z;
	private Image t_xg;
	private Image t_xr;
	private Sprite Bg_Xt_Z;
	public BangTZ tz;
	public void HZBZJD(Graphics g){
		if (t_xg==null) {
			return;
		}
		g.drawImage(t_z,ScrenceUntil.Screen_x/2-274,40,null);
		Bg_Xt_Z.updateToTime(ImageMixDeal.userimg.getTime(), 0);
		Bg_Xt_Z.draw(g, ScrenceUntil.Screen_x/2,70);
		Build build=builds[1];
		double xc=build.getHp()/6000.0;
		int cd=(int) (xc*226);
		if (xc<0.3) {
			g.drawImage(t_xr,ScrenceUntil.Screen_x/2-43-cd,61,cd,13,null);
		}else {
			g.drawImage(t_xg,ScrenceUntil.Screen_x/2-43-cd,61,cd,13,null);		
		}
		build=builds[6];
		xc=build.getHp()/6000.0;
		cd=(int) (xc*226);
		if (xc<0.3) {
			g.drawImage(t_xr,ScrenceUntil.Screen_x/2+43,61,cd,13,null);
		}else {
			g.drawImage(t_xg,ScrenceUntil.Screen_x/2+43,61,cd,13,null);	
		}
	}
	//0关闭 1开启
	public int state;
	//人物状态 -1 离开  0正常  1休息 2冰冻 3挑战状态  4充能或者攻击
	public int manstate;
	private Build[] builds=new Build[11];
	//场景特效
	Sprite Huo;
	public void draw(Graphics g){
		if (state==1&&Util.ditubianma==3315) {
			for (int i = 0; i < builds.length; i++) {
				if (builds[i]==null)continue;
				builds[i].draw(g);
			}	
			//绘制4个火
			if (Huo==null) {
				Huo=SpriteFactory.Prepare(GetTcpPath.getMouseTcp("40"));
			}else {
				Huo.updateToTime(ImageMixDeal.userimg.getTime(), 0);
				PathPoint point = Util.mapmodel.path(336,466);
				if (point!=null) {
					Huo.draw(g, point.getX(),point.getY());	
				}
				point = Util.mapmodel.path( 11,306);
				if (point!=null) {
					Huo.draw(g, point.getX(),point.getY());	
				}
				point = Util.mapmodel.path(350,135);
				if (point!=null) {
					Huo.draw(g, point.getX(),point.getY());	
				}
				point = Util.mapmodel.path(684,292);
				if (point!=null) {
					Huo.draw(g, point.getX(),point.getY());	
				}
			}
			HZBZJD(g);
			if (tz!=null) {
				if (tz.draw(g)) {
					tz=null;
				}
			}
		}
	}
	//初始化
	public void init(String msg){
		if (t_xg==null) {
			t_xg=new ImageIcon("img/xy2uiimg/bang_t_xg.png").getImage();
			t_xr=new ImageIcon("img/xy2uiimg/bang_t_xr.png").getImage();
			t_z=new ImageIcon("img/xy2uiimg/bang_t_z.png").getImage();	
			Bg_Xt_Z=SpriteFactory.VloadSprite("resource/bang/BgXtZ.tcp", null);
		}
		if (msg!=null) {
			String[] v=msg.split("\\|");
			int value= Integer.parseInt(v[0]);
			if (value==0) {
				state    = Integer.parseInt(v[1]);
				manstate = Integer.parseInt(v[2]);	
			}					
			for (int i = (value==1?1:3); i < v.length; i++) {
				String[] vs=v[i].split("=");
				int bh=Integer.parseInt(vs[0]);
				int wei=bh>5?bh-5:bh;
				if (builds[wei]==null) {
					builds[wei]=new Build(bh,Integer.parseInt(vs[1]),Integer.parseInt(vs[2]),Integer.parseInt(vs[3]));
				}else {
					builds[wei].setState(Integer.parseInt(vs[2]));	
					builds[wei].setHp(Integer.parseInt(vs[3]));
				}		
			}
		}else {
			state=0;
		}		
	}
	//点击监听
	public boolean Monitor(int x, int y){
		if (state==1) {
			for (int i = 0; i < builds.length; i++) {
				if (builds[i]==null)continue;
				if (builds[i].getImage()==null)continue;
				if (ImageMixDeal.toBufferedImage(builds[i].getImage(),
						x - builds[i].getX()+ builds[i].getPx(),
						y - builds[i].getY()+ builds[i].getPy())) {
					BuildMonitor(builds[i]);
					return true;
				}
			}
		}
		return false;	
	}
	//监听触发
	public void BuildMonitor(Build build){
		BangBattleControl.build=build;
		String sendmes = null;
		try {
			sendmes = Agreement.getAgreement().gangmonitor(build.getBh()+"");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// 向服务器发送信息
		SendMessageUntil.toServer(sendmes);
	}
	//判断是否能够操作
	public boolean isChao(){
		if (state==1&&Util.ditubianma==3315) {
			if (manstate==0||manstate==1||manstate==-1){
				return true;
			}	
			return false;
		}
		return true;
	}
	
}
