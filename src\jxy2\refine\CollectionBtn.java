package jxy2.refine;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.bean.LoginResult;
import org.come.bean.SuitOperBean;
import org.come.entity.PartJade;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;
import org.come.until.UserData;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

public class CollectionBtn extends MoBanBtn {
    public CollectionJpanel collectionJanel;
    public int typeBtn;
    public CollectionBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, CollectionJpanel collectionJanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type,0,colors,prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.collectionJanel = collectionJanel;
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn==99){
            getJade();
        }else {
            collectionJanel.setMinType(typeBtn);//接装装备炼化，0-4   5开始
            collectionJanel.clear();
        }

    }

    /**
     * 生成玉符
     */
    public void getJade() {
        PartJade jade = collectionJanel.getGoodstableBean().getPartJade();
        if (jade == null) {
            ZhuFrame.getZhuJpanel().addPrompt("请选择你要生成的玉符..");
            return;
        }
        if (jade.getJade1() != 1) {
            ZhuFrame.getZhuJpanel().addPrompt("你还没收录过此玉符..");
            return;
        }
        if (collectionJanel.getTextField().getText() == null
                || collectionJanel.getTextField().getText().equals("")) {
            ZhuFrame.getZhuJpanel().addPrompt("请输入你要生成的玉符数量..");
            return;
        }
        long val = Long.parseLong(collectionJanel.getTextField().getText());
        if (val <= 0) {
            return;
        }
        BigDecimal money = new BigDecimal(1000000 * val);
        BigDecimal sxlxz = new BigDecimal(10 * val);
        LoginResult loginResult = RoleData.getRoleData().getLoginResult();
        if (money.compareTo(loginResult.getGold()) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("金币不足..");
            return;
        }
        if (sxlxz.compareTo(loginResult.getScoretype("灵修值")) > 0) {
            ZhuFrame.getZhuJpanel().addPrompt("灵修值不足，快去获取吧..");
            return;
        }
        PartJade jade2 = new PartJade(jade.getSuitid(), jade.getPartId());
        jade2.setJade(1, (int) val);
        SuitOperBean operBean = new SuitOperBean();
        operBean.setJade(jade2);
        operBean.setType(9);
        // 发送消息给服务器
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
        // 扣金币
        loginResult.setGold(loginResult.getGold().subtract(money));
        // 扣灵修值
        loginResult.setScore(UserData.Splice(loginResult.getScore(), "灵修值=" + sxlxz, 3));
        ZhuFrame.getZhuJpanel().addPrompt("消耗了" + sxlxz + "点灵修值    扣除了" + money + "金币..");
        // 清空界面
        collectionJanel.clear();
    }
}
