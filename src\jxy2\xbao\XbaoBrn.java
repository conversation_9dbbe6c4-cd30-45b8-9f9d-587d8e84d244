package jxy2.xbao;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GsonUtil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class XbaoBrn extends MoBanBtn {
    public XbaoJpanel xbaoJpanel;
    public int index;
    public String text;
    // 按钮状态常量
    private static final int ACTIVE_STATE = 2;   // 激活状态
    private static final int INACTIVE_STATE = 0; // 非激活状态
    
    private ImageIcon[] iconPath; // 添加iconPath字段
    
    public XbaoBrn(ImageIcon[] iconpath, int type, String text, XbaoJpanel xbaoJpanel, int index) {
        super(iconpath, type,type);
        this.text = text;
        this.index = index;
        this.xbaoJpanel = xbaoJpanel;
        this.iconPath = iconpath;
    }
    public XbaoBrn(String iconpath, int type, String text, int index, XbaoJpanel xbaoJpanel) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, type,null);
        this.index = index;
        this.xbaoJpanel = xbaoJpanel;
        setFont(UIUtils.TEXT_FONT);
        setColors(UIUtils.COLOR_BTNTEXT);
        this.setText(text);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    public XbaoBrn(String iconpath, int type, String text, int index) {
        // TODO Auto-generated constructor stub
        super(iconpath, index, type, null);  // 修改为正确的参数顺序
        setNtext(text);
        this.index = index;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (text!=null) {
            if (type == 2) {
                Juitil.Txtpet(g, 7, 19, text, Color.WHITE, UIUtils.TEXT_HYJ18B);
            } else {
                Juitil.Txtpet(g, zhen == 2 ? 5 : 8, 18, text, Color.WHITE, UIUtils.TEXT_HYJ18B);
            }
        }
    }

    @Override
    public void chooseyes() {
    }

    @Override
    public void chooseno() {
    }

    @Override
    public void nochoose(MouseEvent e) {
            if (index==5){
                Util.StopFrame(153);
                Thread.ofVirtual().start(() -> {
                    XbaoLibraryFrame.getXbaoLibraryFrame().getXbaoLibraryJPanel().refreshList();
                });
                return;
            }else if (index==6){
                Util.StopFrame(154);
                XbPracticeFrame.getXbPracticeFrame().getXbPracticeJPanel().initialization(RoleXuanbao.getRoleXuanbao().choseBao);
                return;
            }else if (index==7){//修炼
                String msg = Agreement.getAgreement().XbPracticeAgreement(GsonUtil.getGsonUtil().getgson().toJson(RoleXuanbao.getRoleXuanbao().choseBao));
                SendMessageUntil.toServer(msg);
                return;
            }

        if (index < 2) { // 装备页和属性页切换
            xbaoJpanel.setType(index);
            // 更新按钮状态
            xbaoJpanel.setComponentVisible(index==0);
            xbaoJpanel.setAttributeVisible(index==1);
            xbaoJpanel.getXzimg().setBounds(index==0?245:95, index==0?96:71, 168, 168);
            XuanbaoMouse.shijian = index==0;
            // 使用通用方法切换按钮状态
            toggleButtonGroup(index == 1 ? 0 : 1,
                    xbaoJpanel.getXbaoAttrBtn(),
                    xbaoJpanel.getXbaoEquiBtn()
            );

            // 计算新页码
            RoleXuanbao roleXuanbao = RoleXuanbao.getRoleXuanbao();
            int totalItems = roleXuanbao.currentIndex;
            int newItemsPerPage = index == 0 ? 20 : 8; // 新的每页显示数量
            
            // 获取当前选中的玄宝
            Xuanbao selectedXuanbao = roleXuanbao.choseBao;
            if (selectedXuanbao != null) {
                // 查找选中玄宝在数组中的位置
                int selectedIndex = -1;
                Xuanbao[] xuanbaos = roleXuanbao.getXuanbaos();
                for (int i = 0; i < xuanbaos.length; i++) {
                    if (xuanbaos[i] != null && xuanbaos[i].equals(selectedXuanbao)) {
                        selectedIndex = i;
                        break;
                    }
                }
                
                // 根据选中玄宝的位置计算新页码
                if (selectedIndex != -1) {
                    int newPage = selectedIndex / newItemsPerPage;
                    // 确保新页码不超过最大页数
                    int maxPage = (totalItems + newItemsPerPage - 1) / newItemsPerPage - 1;
                    newPage = Math.min(newPage, maxPage);
                    roleXuanbao.lingNumChange(newPage);
                    
                    // 计算选中玄宝在新页面中的位置
                    int positionInNewPage = selectedIndex % newItemsPerPage;
                    
                    // 清除所有边框
                    for (int i = 0; i < xbaoJpanel.getXuanbaoframe().length; i++) {
                        xbaoJpanel.getXuanbaoframe()[i].setBorder(null);
                    }
                    
                    // 设置新的选中边框
                    if (positionInNewPage < xbaoJpanel.getXuanbaoframe().length) {
                        xbaoJpanel.getXuanbaoframe()[positionInNewPage].setBorder(BorderFactory.createLineBorder(Color.red, 2));
                    }
                } else {
                    roleXuanbao.lingNumChange(0);
                }
                xbaoJpanel.LoadingOfSelected(selectedXuanbao);
            } else {
                roleXuanbao.lingNumChange(0);
            }
            
        } else { // 翻页逻辑
            RoleXuanbao roleXuanbao = RoleXuanbao.getRoleXuanbao();
            int currentPage = roleXuanbao.getXuanNum();
            int newPage = currentPage; // 记录新的页码
            
            // 使用RoleXuanbao中记录的总数
            int totalItems = roleXuanbao.currentIndex;
            
            // 根据当前页面类型计算每页显示数量和最大页数
            int itemsPerPage = XuanbaoMouse.shijian ? 20 : 8;
            int maxPage = (totalItems + itemsPerPage - 1) / itemsPerPage - 1;
            
            // 执行翻页操作
            if(index == 2) {  // 上一页按钮
                if(currentPage > 0) {
                    newPage = currentPage - 1;
                    roleXuanbao.lingNumChange(newPage);
                }
            } else if(index == 3) {  // 下一页按钮
                if(currentPage < maxPage) {
                    newPage = currentPage + 1;
                    roleXuanbao.lingNumChange(newPage);
                }
            }

            // 更新边框显示
            Xuanbao selectedXuanbao = roleXuanbao.choseBao;
            if (selectedXuanbao != null) {
                // 查找选中玄宝在数组中的位置
                int selectedIndex = -1;
                Xuanbao[] xuanbaos = roleXuanbao.getXuanbaos();
                for (int i = 0; i < xuanbaos.length; i++) {
                    if (xuanbaos[i] != null && xuanbaos[i].equals(selectedXuanbao)) {
                        selectedIndex = i;
                        break;
                    }
                }

                // 清除所有边框
                for (int i = 0; i < xbaoJpanel.getXuanbaoframe().length; i++) {
                    xbaoJpanel.getXuanbaoframe()[i].setBorder(null);
                }

                // 只有当选中的玄宝在当前页面时才显示边框
                if (selectedIndex != -1) {
                    int positionInCurrentPage = selectedIndex % itemsPerPage;
                    int selectedPage = selectedIndex / itemsPerPage;
                    
                    if (selectedPage == newPage && positionInCurrentPage < xbaoJpanel.getXuanbaoframe().length) {
                        xbaoJpanel.getXuanbaoframe()[positionInCurrentPage].setBorder(BorderFactory.createLineBorder(Color.red, 2));
                    }
                }
            }
            
            // 更新当前按钮状态
            if(index == 2) {  // 上一页按钮
                ImageIcon[] path = Util.SwitchUI==1?Juitil.bt3:Juitil.bt5;
                if(newPage <= 0) {
                    setIcon(Juitil.tz370);
                    setBtn(-1);
                } else {
                    setBtn(1);
                    setIconPath(path);
                }
            } else {  // 下一页按钮
                ImageIcon[] path = Util.SwitchUI==1?Juitil.bt4:Juitil.bt6;
                if(newPage >= maxPage) {
                    setIcon(Juitil.tz371);
                    setBtn(-1);
                } else {
                    setBtn(1);
                    setIconPath(path);
                }
            }
            
            // 更新另一个按钮的状态
            XbaoBrn otherBtn = (index == 2) ? xbaoJpanel.getNextPage() : xbaoJpanel.getPreviousPage();
            if(otherBtn != null) {
                if(index == 2) {  // 当前是上一页按钮，更新下一页按钮
                    ImageIcon[] path = Util.SwitchUI==1?Juitil.bt4:Juitil.bt6;
                    if(newPage >= maxPage) {
                        otherBtn.setIcon(Juitil.tz371);
                        otherBtn.setBtn(-1);
                    } else {
                        otherBtn.setBtn(1);
                        otherBtn.setIconPath(path);
                    }
                } else {  // 当前是下一页按钮，更新上一页按钮
                    ImageIcon[] path = Util.SwitchUI==1?Juitil.bt3:Juitil.bt5;
                    if(newPage <= 0) {
                        otherBtn.setIcon(Juitil.tz370);
                        otherBtn.setBtn(-1);
                    } else {
                        otherBtn.setBtn(1);
                        otherBtn.setIconPath(path);
                    }
                }
            }
        }
    }

    // 添加setIconPath方法
    public void setIconPath(ImageIcon[] iconPath) {
        this.iconPath = iconPath;
        if(iconPath != null) {
            setIcon(iconPath[0]);
        } else {
            setIcon(null);
        }
    }

    /**
     * 通用的按钮组切换方法
     * @param targetIndex 目标按钮的索引（需要激活的按钮索引）
     * @param buttons 按钮数组，按索引顺序传入
     */
    public static void toggleButtonGroup(int targetIndex, MoBanBtn... buttons) {
        if (buttons == null || buttons.length == 0) {
            return;
        }
        for (int i = 0; i < buttons.length; i++) {
            if (buttons[i] != null) {
                buttons[i].btnchange(i == targetIndex ? ACTIVE_STATE : INACTIVE_STATE);
            }
        }
    }
}
