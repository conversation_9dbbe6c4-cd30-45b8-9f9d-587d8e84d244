package jxy2.qqiubook;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.Juitil;
import org.come.Jpanel.TeststateJpanel;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;

public class ShowThousandJPanl extends JPanel {
    public JLabel labTime,labMeritvalue,labmerbackimg,labwcimg,labtx,labname,labValue ;
    private ShowThousandMouse mouseListener;  // 添加鼠标监听器引用

    public ShowThousandJPanl() {
        // 为面板添加键盘监听
        this.setPreferredSize(new Dimension(320, 70));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        this.setOpaque(true);  // 确保面板是不透明的

        // 初始化并添加鼠标事件监听器
        mouseListener = new ShowThousandMouse(this);
        this.addMouseListener(mouseListener);
        this.addMouseMotionListener(mouseListener);

        // 确保面板可以接收鼠标事件
        this.setFocusable(true);
        this.setEnabled(true);

        getLabMeritvalue();
        getLabTime();
        getLabmerbackimg();
        getLabwcimg();
        getLabtx();
        getLabname();
        getLabValue();


    }

    public void initData(QiqiuTreeNode node){
        if (node==null)return;
        labTime.setText("完成时间："+node.getTime());
        labMeritvalue.setText(node.getMeritvalue());
        labtx.setIcon(QianqiuBookModel.getIcon(node.getParentId()));
        if (node.getId()==100){
            labname.setText(node.getName());
        }else {
            System.out.println(node.getValue()+"??");
            labValue.setText(!node.getValue().isEmpty()?"-"+node.getValue():"");
            String kk = node.getValue().length()==3?"    级":"   级";
            labname.setText(!node.getValue().isEmpty()?node.getName()+kk:node.getName());
        }
        // 确保所有子组件都能接收鼠标事件
        Component[] components = getComponents();
        for (Component component : components) {
            component.addMouseListener(mouseListener);
            component.addMouseMotionListener(mouseListener);
        }
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Util.SwitchUI ==1?Juitil.she_0003:Juitil.red_0007,5,0,getWidth()-9,70,1);
        g.setColor(new Color(98,129,123));
        g.drawLine(56, 29, 240, 29);

    }

    public void getLabMeritvalue() {
        if (labMeritvalue==null){
            labMeritvalue = TeststateJpanel.GJpanelText(Color.yellow,UIUtils.TEXT_FONT1);
            labMeritvalue.setBounds(278,7,20,20);
            add(labMeritvalue);
        }
    }

    public void getLabTime() {
        if (labTime==null){
            labTime = TeststateJpanel.GJpanelText(Util.SwitchUI==1?Color.BLACK:Color.WHITE,UIUtils.TEXT_FONT);
            labTime.setBounds(57,35,390,15);
            add(labTime);
        }
    }

    public void getLabmerbackimg() {
        if (labmerbackimg==null){
            labmerbackimg = new JLabel();
            labmerbackimg.setIcon(Juitil.tz342);
            labmerbackimg.setBounds(255,9,55,17);
            add(labmerbackimg);
        }
    }

    public void getLabwcimg() {
        if (labwcimg==null){
            labwcimg = new JLabel();
            labwcimg.setIcon(Juitil.tz346);
            labwcimg.setBounds(201,8,65,55);
            add(labwcimg);
        }
    }

    public JLabel getLabname() {
        if (labname==null){
            labname = TeststateJpanel.GJpanelText(Util.SwitchUI==1?new Color(16,24,24):
                            UIUtils.COLOR_Wing1,
                    Util.SwitchUI==1?UIUtils.NEWTX_HY17B:UIUtils.TEXT_HYJ17B);
            labname.setBounds(59,10,204,15);
            add(labname);
        }
        return labname;
    }

    public void getLabtx() {
        if (labtx==null){
            labtx = new JLabel();
            labtx.setBounds(7,9,44,43);
            add(labtx);
        }
    }

    public void getLabValue() {
        if (labValue==null){
            labValue = TeststateJpanel.PJpanelText(Util.SwitchUI==1?new Color(16,24,24):
                    UIUtils.COLOR_Wing1,UIUtils.TEXT_FONT_17);
            labValue.setBounds(132,11,390,15);
            add(labValue);
        }
    }
}

