package come.tool.Fighting;

import java.awt.Graphics;
import java.util.ArrayList;
import java.util.List;

import org.come.until.ScrenceUntil;

public class BuffUtil {
	
	 public boolean isMcqh=false;
	 //buff特效显示
	 public List<Buff> buffs=new ArrayList<>();
	 //右下排数据技能显示
	 public List<SkillTx> skills=new ArrayList<>();
     //字符串处理           camp是主视角的阵营
     public void CS(String text,int camp){
    	 if (text==null||text.equals("")) {return;}
    	 String[] vs=text.split("\\|");
    	 for (int i = 0; i < vs.length; i++) {
			String[] vss=vs[i].split("=");
			if (vss[0].equals("0")) {//加vss[1] id vss[2] camp vss[3] type 
				if (vss[3].equals("化无")||vss[3].equals("bbxr")||vss[3].equals("bbyh")||
				vss[3].equals("bbtm")||vss[3].equals("bbsgqx")||vss[3].equals("1881")||vss[3].equals("1234")) {
					Buff buff=new Buff(vss);
					buffs.add(buff);	
				}else if (vss[3].equals("bbmcqh")) {
					Buff buff=new Buff(vss);
					buffs.add(buff);
					if (buff.getCamp()==camp) {
						isMcqh=true;
					}
				}
			}else {//删
				int id=Integer.parseInt(vss[1]);
				for (int j = buffs.size()-1; j >=0; j--) {
					Buff buff=buffs.get(j);
					if (buff.getId()==id) {
						if (buff.getType().equals("bbmcqh")) {
							if (buff.getCamp()==camp) {isMcqh=false;}
						}
						buffs.remove(j);
						break;
					}
				}
			}
		 }
    	 ViewPath(camp);
     }
     /**调整显示位置 
      * camp是主视角的阵营*/
     public void ViewPath(int camp){
    	 //化无 bb_xr bb_yh  // 阵营排序最多显示2个 
    	 //bb_mcqh 紧贴阵营
    	 //bb_sgqx bb_tm 屏幕中间
    	 Buff tm=null;
    	 Buff bb_hw1=null;
    	 Buff bb_hw2=null;
    	 Buff bb_xr1=null;
    	 Buff bb_xr2=null;
    	 Buff bb_yh1=null;
    	 Buff bb_yh2=null;
    	 Buff bb_xc1=null;//1881
    	 Buff bb_xc2=null;
    	 Buff bb_ff1=null;//1234
    	 Buff bb_ff2=null;
    	for (int i = buffs.size()-1; i >=0; i--) {
    		Buff buff=buffs.get(i);
    		buff.setIsv(false);
    		if (buff.getType().equals("bbsgqx")||buff.getType().equals("bbtm")) {
    			buff.setX(ScrenceUntil.Screen_x/2);
    			buff.setY(ScrenceUntil.Screen_y/2);
    			buff.setIsv(true);
    			if (buff.getType().equals("bbsgqx")) {
					if (tm!=null) {
						tm.setIsv(false);
					}
				}else {
					tm=buff;
				}
			}else if (buff.getType().equals("bbmcqh")) {
				if (buff.getCamp()==camp) {//主视角
					//x230 y80
					buff.setX(ScrenceUntil.Screen_x-230);
	    			buff.setY(ScrenceUntil.Screen_y-80);
	    			buff.setIsv(true);
				}else {//x100 y360
					buff.setX(100);
	    			buff.setY(360);
	    			buff.setIsv(true);
				}
			}else if (buff.getType().equals("化无")) {//化无展示对调
				if (buff.getCamp()==camp) {bb_hw2=buff;}
				else {bb_hw1=buff;}
			}else if (buff.getType().equals("1881")) {//虚寒问蝉展示对调
				if (buff.getCamp()==camp) {bb_xc2=buff;}
				else {bb_xc1=buff;}
			}else if (buff.getType().equals("1234")) {
				if (buff.getCamp()==camp) {bb_ff1=buff;}
				else {bb_ff2=buff;}
			}else if (buff.getType().equals("bbxr")) {
				if (buff.getCamp()==camp) {
					 bb_xr1=buff;
				}else {
					 bb_xr2=buff;
				}
			}else if (buff.getType().equals("bbyh")) {
				if (buff.getCamp()==camp) {
					 bb_yh1=buff;
				}else {
					 bb_yh2=buff;
				}
			}
		}
    	//主
    	if (bb_hw1==null) {
			if (bb_xr1!=null) {
				bb_hw1=bb_xr1;
				bb_xr1=null;
			}
		}
		if (bb_xr1==null) {
			bb_xr1=bb_yh1;
		}
		if (bb_hw1!=null) {
			bb_hw1.setX(ScrenceUntil.Screen_x-70);
			bb_hw1.setY(ScrenceUntil.Screen_y*4/5);
			bb_hw1.setIsv(true);
		}
		if (bb_xr1!=null) {
        	bb_xr1.setX(ScrenceUntil.Screen_x-30);
			bb_xr1.setY(ScrenceUntil.Screen_y*3/5);
        	bb_xr1.setIsv(true);
		}
		if (bb_ff1!=null) {
			bb_ff1.setX(ScrenceUntil.Screen_x-50);
			bb_ff1.setY(ScrenceUntil.Screen_y*4/5);
			bb_ff1.setIsv(true);
		}
        if (bb_xc1!=null) {
        	bb_xc1.setX(ScrenceUntil.Screen_x-90);
        	bb_xc1.setY(ScrenceUntil.Screen_y*4/5);
        	bb_xc1.setIsv(true);
		}        
        //副
        if (bb_hw2==null) {
			if (bb_xr2!=null) {
				bb_hw2=bb_xr2;
				bb_xr2=null;
			}
		}
		if (bb_xr2==null) {
			bb_xr2=bb_yh2;
		}
		if (bb_hw2!=null) {
			bb_hw2.setX(50);
			bb_hw2.setY(ScrenceUntil.Screen_y*2/5);
			bb_hw2.setIsv(true);
		}
        if (bb_xr2!=null) {
			bb_xr2.setX(160);
			bb_xr2.setY((int)(ScrenceUntil.Screen_y*1.5/5));
        	bb_xr2.setIsv(true);
		}
		if (bb_ff2!=null) {
			bb_ff2.setX(120);
			bb_ff2.setY(ScrenceUntil.Screen_y*2/5);
			bb_ff2.setIsv(true);
		}
        if (bb_xc2!=null) {
        	bb_xc2.setX(90);
        	bb_xc2.setY(ScrenceUntil.Screen_y*2/5);
        	bb_xc2.setIsv(true);
		} 
     }
     /**刷新技能*/
     public void SXSkill(List<TypeState> states){
    	int size=0;
        int max=skills.size();
        for (int i = 0; i < states.size(); i++) {
			 if (states.get(i).getState()!=1) {continue;}
        	 if (size<max) {					
				 skills.get(size++).SReset(states.get(i).getType(),ScrenceUntil.Screen_x-50-(size-1)*40, ScrenceUntil.Screen_y-85);
			 }else {
				 size++;
				 SkillTx skillTx=new SkillTx();
				 skillTx.SReset(states.get(i).getType(),ScrenceUntil.Screen_x-50-(size-1)*40, ScrenceUntil.Screen_y-85);
				 skills.add(skillTx);
			}
		}   
        for (int i = skills.size()-1; i >=size; i--) {
        	skills.remove(i);
		}
     }
     /**判断是否点击*/
     public SkillTx MonitorBuff(int x,int y){
    	 for (int i = skills.size()-1; i >=0; i--) {
   		      if (skills.get(i).isMonitor(x, y)) {
				  return skills.get(i);
			  }
         }
		 return null;
     }
     /***/
     public void draw(Graphics g){
    	 for (int i = buffs.size()-1; i >=0; i--) {
    		  buffs.get(i).draw(g);
         }
    	 for (int i = skills.size()-1; i >=0; i--) {
    		  skills.get(i).draw(g);
         }
     }
}
