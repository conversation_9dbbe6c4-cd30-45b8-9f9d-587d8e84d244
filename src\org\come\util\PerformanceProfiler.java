package org.come.util;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 性能分析器 - 专门分析渲染性能瓶颈
 */
public class PerformanceProfiler {
    
    private static PerformanceProfiler instance;
    private Timer profileTimer;
    private boolean isProfiling = false;
    
    // 性能统计
    private long totalRenderTime = 0;
    private long totalSleepTime = 0;
    private int renderCount = 0;
    private long maxRenderTime = 0;
    private long minRenderTime = Long.MAX_VALUE;
    
    private PerformanceProfiler() {}
    
    public static PerformanceProfiler getInstance() {
        if (instance == null) {
            instance = new PerformanceProfiler();
        }
        return instance;
    }
    
    /**
     * 开始性能分析
     */
    public void startProfiling() {
        if (isProfiling) return;
        
        isProfiling = true;
        resetStats();

        
        profileTimer = new Timer(5000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                reportPerformanceStats();
            }
        });
        profileTimer.start();
    }
    
    /**
     * 停止性能分析
     */
    public void stopProfiling() {
        if (!isProfiling) return;
        
        isProfiling = false;
        if (profileTimer != null) {
            profileTimer.stop();
        }


    }
    
    /**
     * 记录一次渲染
     */
    public void recordRender(long renderTimeNs, long sleepTimeMs) {
        if (!isProfiling) return;
        
        long renderTimeMs = renderTimeNs / 1000000;
        
        totalRenderTime += renderTimeMs;
        totalSleepTime += sleepTimeMs;
        renderCount++;
        
        maxRenderTime = Math.max(maxRenderTime, renderTimeMs);
        minRenderTime = Math.min(minRenderTime, renderTimeMs);
    }
    
    /**
     * 重置统计数据
     */
    private void resetStats() {
        totalRenderTime = 0;
        totalSleepTime = 0;
        renderCount = 0;
        maxRenderTime = 0;
        minRenderTime = Long.MAX_VALUE;
    }
    
    /**
     * 报告性能统计
     */
    private void reportPerformanceStats() {
        if (renderCount == 0) {
//            System.out.println("[性能分析] 警告：没有检测到渲染调用！");
            return;
        }
        // 重置统计，准备下一轮
        resetStats();
    }


}
