package jxy2.setup;

import com.tool.btn.MoBanBtn;
import jxy2.chatv.ChatFrame;
import jxy2.npk.NpkImageReader;
import jxy2.xbao.XbaoFrame;
import jxy2.xbao.XbaoLibraryFrame;
import org.come.Frame.*;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class AudioSteupBtn extends MoBanBtn {
    public int typeBtn;//提示
    public AudioSteupJpanel audioSteupJpanel;

    public AudioSteupBtn(String iconpath, int type, Color[] colors, Font font, String text, Integer typeBtn, AudioSteupJpanel audioSteupJpanel, String prompt) {
        // TODO Auto-generated constructor stub
        super(iconpath, type, 0, colors, prompt);
        this.setText(text);
        setFont(font);
        this.typeBtn = typeBtn;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.audioSteupJpanel = audioSteupJpanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (typeBtn == 0) {
            if (audioSteupJpanel.leftFlag == 1) {
                audioSteupJpanel.getOptionJpanel().setVisible(true);
                audioSteupJpanel.leftFlag = 0;
            } else {
                audioSteupJpanel.getOptionJpanel().setVisible(false);
                audioSteupJpanel.leftFlag = 1;
            }
        } else if (typeBtn == 1) {
            //清理缓存
//            MemoryCompressor.performLightCompression();
//            MemoryCompressor.forceCompressToTarget();
            NpkImageReader.forceGarbageCollection();
//            System.gc();
        } else if (typeBtn == 2) {

            if (ZhuFrame.getzhuframe().getUI() != null) {
                ChatFrame.getChatFrame().setUI(new NoTitleInternalFrameUI(ChatFrame.getChatFrame()));
                TestpackJframe.getTestpackJframe().setUI(new NoTitleInternalFrameUI(TestpackJframe.getTestpackJframe()));
                ZhuFrame.getzhuframe().setUI(new NoTitleInternalFrameUI(ZhuFrame.getzhuframe()));
                MsgJframe.getJframe().setUI(new NoTitleInternalFrameUI(MsgJframe.getJframe()));
                Teststatejframe.getTeststatejframe().setUI(new NoTitleInternalFrameUI(Teststatejframe.getTeststatejframe()));
                XbaoFrame.getXbaoFrame().setUI(new NoTitleInternalFrameUI(XbaoFrame.getXbaoFrame()));
                XbaoLibraryFrame.getXbaoLibraryFrame().setUI(new NoTitleInternalFrameUI(XbaoLibraryFrame.getXbaoLibraryFrame()));
                XbaoLibraryFrame.getXbaoLibraryFrame().getXbaoLibraryJPanel().getjScrollPane().getVerticalScrollBar().setUI(null);
                XbaoLibraryFrame.getXbaoLibraryFrame().getXbaoLibraryJPanel().getXbaoRightSideJpanel().getjScrollPane().getVerticalScrollBar().setUI(null);
                ChatFrame.getChatJPanel().getjScrollPane().getVerticalScrollBar().setUI(null);
            }
            ChatFrame.getChatJPanel().updateButtonImages(Util.SwitchUI);

        }
    }
}
