package jxy2.setup;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;

import javax.swing.*;
import java.awt.*;

public class SetupMainJPanel extends JPanel {

    private SetupCardJPanel setupCardJPanel;
    private SetupBtn[] setupBtns = new SetupBtn[4];
    private String[] names = {"影音设置","游戏设置","个人设置","战斗设置"};
    private int index;
    public static Font allFont;//所有的名称字体
    public static int allUi;
    public SetupMainJPanel() {
        setPreferredSize(new Dimension(300, 354));
        setOpaque(false);
        setLayout(null);
        Juitil.addClosingButtonToPanel(this,50,300);
        setupCardJPanel =new SetupCardJPanel();
        add(setupCardJPanel);

        for (int i = 0; i < setupBtns.length; i++) {
            setupBtns[i] = new SetupBtn(ImgConstants.tz45, 1, UIUtils.COLOR_ZHUJPANEL, UIUtils.MSYH_HY14, names[i], i, this,"");
            setupBtns[0].btnchange(2);
            setupBtns[i].setBounds(18+i*66, 48, 66, 24);
            this.add(setupBtns[i]);
        }
    }
    public String titie = "游戏设置";
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, getWidth(), 32, 1);
        Juitil.ImngBack(g, Juitil.JK_1, 0, 32, getWidth()+4, getHeight()-32, 1);
        g.drawImage(Juitil.icon.getImage(), 18, 70, getWidth()-40, 2, null);
        Juitil.Subtitledrawing(g, getWidth()/2-40, 24, titie, UIUtils.COLOR_White, UIUtils.HYXKJ_HY20,1);

    }

    public SetupCardJPanel getSetupCardJPanel() {
        return setupCardJPanel;
    }

    public void setSetupCardJPanel(SetupCardJPanel setupCardJPanel) {
        this.setupCardJPanel = setupCardJPanel;
    }

    public SetupBtn[] getSetupBtns() {
        return setupBtns;
    }

    public void setSetupBtns(SetupBtn[] setupBtns) {
        this.setupBtns = setupBtns;
    }

    public String[] getNames() {
        return names;
    }

    public void setNames(String[] names) {
        this.names = names;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public static Font getAllFont() {
        return allFont;
    }

    public static void setAllFont(Font allFont) {
        SetupMainJPanel.allFont = allFont;
    }

    public static int getAllUi() {
        return allUi;
    }

    public static void setAllUi(int allUi) {
        SetupMainJPanel.allUi = allUi;
    }
}
