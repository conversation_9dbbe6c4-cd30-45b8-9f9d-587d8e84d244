package jxy2.refine;

import com.tool.btn.DazaoBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.entity.Goodstable;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GodJpanel extends JPanel {
    public String[] textSymbol;
    private DazaoBtn btndazao;// 打造按钮
    private GodBtn[] btnGod = new GodBtn[3];
    private int minType = 0;
    private String type;
    private BigDecimal money = new BigDecimal(100000);// 消耗金钱
    public Goodstable[] goods = new Goodstable[2];
    public JLabel[] towEquipment = new JLabel[2];//五
    public GodJpanel() {
        this.setPreferredSize(new Dimension(640, 460));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        textSymbol = new String[]{"神兵升级","附神兵石","精炼神兵"};
        for (int i = 0; i < btnGod.length; i++) {
            btnGod[i] = new GodBtn(ImgConstants.tz45, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, textSymbol[i], i, this,"");
            btnGod[i].btnchange(i==0?2:0);
            btnGod[i].setBounds(138+i*66, 48, 66, 24);
            add(btnGod[i]);
        }
        btndazao = new DazaoBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "洗练", this);
        btndazao.setBounds(270,374, 82,29);
        this.add(btndazao);

        for (int i = 0; i < towEquipment.length; i++) {
            towEquipment[i] = new JLabel();
            towEquipment[i].setBounds(215 + i * 134,202,50,50);
            towEquipment[i].addMouseListener(new RefineMouse(this, 24 + i));
            this.add(towEquipment[i]);
        }
    }
    public void ClickSuit(Goodstable good, int path) {
        if (path<24){
            for (int i = 0; i < 2; i++) {
                if (goods[i] == null) {
                    change(good, i);
                    break;
                }
            }
        }else {
            change(null, path - 24);
        }

        type = minType==2?"精炼神兵":minType==0?"我要升级神兵":"我要上神兵石";
        String v = minType==2?"升级":minType==0?"精炼":"附石";
        if (!btndazao.getText().equals(v)) {
            btndazao.setText(v);
        }
    }

    private void change(Goodstable good, int i) {
            goods[i] = good;
        if (good != null) {
            towEquipment[i].setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(),49,49));
        }else {
            towEquipment[i].setIcon(null);
        }
    }

    public void clear() {
        for (int i = 0; i < 2; i++) {
            goods[i] = null;
            towEquipment[i].setIcon(null);
        }
    }
    public static Map<Integer,String[]> integerMap =new ConcurrentHashMap<>();
    static {
        integerMap.put(0,new String[]{"神兵","矿石"});
        integerMap.put(1,new String[]{"神兵","神兵石"});
        integerMap.put(2,new String[]{"神兵","神兵"});
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        for (int i = 0; i < 2; i++) {
            Juitil.ImngBack(g, Juitil.good_2, 215 + i * 134, 202, 50, 50, 1);
            if (towEquipment[i].getIcon()==null){
                Juitil.TextBackground(g, integerMap.get(minType)[i], 13, 226 + i * 134, 202 + 18, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
            }
            Juitil.ImngBack(g, Juitil.tz26, 500, 334+i*25, 121, 23, 1);
        }

        Juitil.TextBackground(g, "消耗金钱", 13, 440, 310+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        Juitil.TextBackground(g, "拥有金钱", 13, 440, 335+25, UIUtils.COLOR_NAME, UIUtils.FZCY_HY13);
        // 画现金
        Util.drawMoney(g, 504, 374);
        // 画消耗金钱
        if (money != null)
            Util.drawPrice(g, money, 504, 374-25);
    }

    public GodBtn[] getBtnGod() {
        return btnGod;
    }

    public void setBtnGod(GodBtn[] btnGod) {
        this.btnGod = btnGod;
    }

    public String[] getTextSymbol() {
        return textSymbol;
    }

    public void setTextSymbol(String[] textSymbol) {
        this.textSymbol = textSymbol;
    }

    public int getMinType() {
        return minType;
    }

    public void setMinType(int minType) {
        this.minType = minType;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public DazaoBtn getBtndazao() {
        return btndazao;
    }

    public void setBtndazao(DazaoBtn btndazao) {
        this.btndazao = btndazao;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
