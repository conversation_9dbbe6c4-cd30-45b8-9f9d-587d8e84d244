package jxy2.petAi;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.summonequip.JframeSummonEquipMain;
import org.come.summonequip.JpanelSummonEquipMain;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PetSkillAiBtn extends MoBanBtn {
    public PetSkillAiJPanel petSkillAiJPanel;
    public int index;
    public static Timer timer;
    public PetSkillAiBtn(String iconpath, int type, Color[] colors,Font font , String text , int index,PetSkillAiJPanel petSkillAiJPanel, String prompt) {
        super(iconpath, type, 0, colors, prompt);
        this.setText(text);
        setFont(font);
        this.index = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.petSkillAiJPanel = petSkillAiJPanel;
    }

    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (RoleData.getRoleData().getLoginResult().getPaysum().intValue()<=10){
            ZhuFrame.getZhuJpanel().addPrompt2("#R错误操作！累计积分小于10无法开启自动");
            return;
        }
        if (petSkillAiJPanel.getBtnAr().getText().equals("停止")){
            timer.stop();
            petSkillAiJPanel.getBtnAr().setText("自动");
            return;
        }
        if (timer != null && timer.isRunning()) {
            return; // 如果计时器已经在运行，则直接返回，不创建新的计时器
        }
        petSkillAiJPanel.getBtnAr().setText("停止");
        timer = new Timer(500, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JpanelSummonEquipMain jpanelSummonEquipMain = JframeSummonEquipMain.getJframeSummonEquipMain().getJpanelSummonEquipMain();
                if (jpanelSummonEquipMain == null) return;
                Goodstable chooseEquip = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseEquip());
                if (chooseEquip == null) {
                    // 没有选中召唤兽装备
                    ZhuFrame.getZhuJpanel().addPrompt2("没有选中召唤兽装备");
                    return;
                }

                String[] values = chooseEquip.getValue().split("\\|");
                SuitOperBean suitOperBean = new SuitOperBean();
                if (jpanelSummonEquipMain.getSummonEquipMenuType() == 2 && jpanelSummonEquipMain.getRadioBtnType() == 2) {
                    /** 重悟技能 */
                    if (RoleData.getRoleData().getLoginResult().getGold().compareTo(jpanelSummonEquipMain.getMoney()) < 0) {
                        // 金钱不足
                        timer.stop();
                        ZhuFrame.getZhuJpanel().addPrompt2("游戏币不足500万");
                        return;
                    }
                    if (jpanelSummonEquipMain.getChooseGoods() == null) {
                        timer.stop();
                        ZhuFrame.getZhuJpanel().addPrompt2("没有选择千年魂石");

                        return;
                    }
                    Goodstable chooseGoods = GoodsListFromServerUntil.getRgid(jpanelSummonEquipMain.getChooseGoods());
                    if (chooseGoods == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("没有选中隐月神石");
                        return;
                    }
                    if (jpanelSummonEquipMain.getValuesMessage(values, "觉醒技") == null) {
                        ZhuFrame.getZhuJpanel().addPrompt2("该装备没有觉醒技");
                        return;
                    }

                    suitOperBean.setType(44);
                    suitOperBean.setJudgingVip(1);
                    suitOperBean.setStopmsg(petSkillAiJPanel.stopmsg);
                    List<BigDecimal> goods = new ArrayList<>();
                    // 添加物品rgid
                    goods.add(chooseEquip.getRgid());
                    goods.add(chooseGoods.getRgid());
                    suitOperBean.setGoods(goods);
                    deductGoods(chooseGoods, 1);
                    if (chooseGoods.getUsetime() <= 0) {
                        jpanelSummonEquipMain.recoverEquipImgTwo();
                        FormsManagement.showForm(94);
                    }
                }

                String sendmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(suitOperBean));
                SendMessageUntil.toServer(sendmes);
            }
        });
        timer.start();
    }
    /** 物品消耗扣除 */
    public void deductGoods(Goodstable goodstable, int num) {
        goodstable.goodxh(num);
        if (goodstable.getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goodstable.getRgid());
        }
    }
}
