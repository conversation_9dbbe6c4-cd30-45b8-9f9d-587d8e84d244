package jxy2.roleRelated;

import org.come.action.FromServerAction;
import org.come.entity.RoleSummoning;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

public class PetSwitchControl  implements FromServerAction {
    @Override
    public void controlMessFromServer(String mes, String type) {
        RoleSummoning pet = GsonUtil.getGsonUtil().getgson().fromJson(mes,RoleSummoning.class);
        PetSwitchJPanel petSwitchJPanel = PetSwitchFrame.getPetSwitchFrame().getPetSwitchJPanel();
        petSwitchJPanel.initPetInfo(pet);
        UserMessUntil.setChosePetMes(pet);

    }
}
