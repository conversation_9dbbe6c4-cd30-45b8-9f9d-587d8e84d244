package come.tool.Fighting;

import java.awt.Graphics;
import java.util.List;

import org.come.bean.PathPoint;

import com.tool.tcp.NewPart;
/**影子模式*/
public class ShadowMode{
	//皮肤
	private NewPart part;
	//坐标
	private int x,y;	
	//路径
	private List<PathPoint> paths;
	private int time;
	private int dir;
	public ShadowMode(NewPart part, int x, int y, List<PathPoint> paths,int dir) {
		super();
		this.part = part;
		this.x = x;
		this.y = y;
		this.paths = paths;
		this.dir = dir;
	}
	public void draw(Graphics g){
		part.draw(g,x,y,dir,time);
	}
	/***/
	public boolean addTime(long time){
		this.time+=time;
		if (paths!=null) {
			return FightingMove2.getmovetime(this, paths);
		}
		if (this.time>=part.getTime()) {
			this.time=part.getTime();
			return true;
		}
		return false;
	}
	public NewPart getPart() {
		return part;
	}
	public void setPart(NewPart part) {
		this.part = part;
	}
	public int getX() {
		return x;
	}
	public void setX(int x) {
		this.x = x;
	}
	public int getY() {
		return y;
	}
	public void setY(int y) {
		this.y = y;
	}
	public List<PathPoint> getPaths() {
		return paths;
	}
	public void setPaths(List<PathPoint> paths) {
		this.paths = paths;
	}
	public int getTime() {
		return time;
	}
	public void setTime(int time) {
		this.time = time;
	}
	public int getDir() {
		return dir;
	}
	public void setDir(int dir) {
		this.dir = dir;
	}
	
}
