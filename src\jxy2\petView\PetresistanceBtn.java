package jxy2.petView;

import com.tool.btn.MoBanBtn;
import com.tool.tcpimg.UIUtils;
import org.come.Frame.RolePetResistanceJframe;
import org.come.Jpanel.FaShuKangXingJpanel;
import org.come.until.FormsManagement;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;

public class PetresistanceBtn extends MoBanBtn {
    public int locktype,index;
    private String text;
    public PetresistanceModelJPanel panel;
    public FaShuKangXingJpanel faShuKangXingJpanel;

    public PetresistanceBtn(String iconpath, int type, int locktype, String text, Color[] colors, PetresistanceModelJPanel panel, int index) {
        super(iconpath, type,0,colors,text);
        this.locktype = locktype;
        setText(text);
        setFont(UIUtils.MSYH_HY14);
        this.panel = panel;
        this.index = index;
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        // TODO Auto-generated constructor stub
    }
    public PetresistanceBtn(String iconpath, int type, int locktype, String text, FaShuKangXingJpanel faShuKangXingJpanel, int index) {
        super(iconpath, type,0,text,text);
        this.locktype = locktype;
        this.faShuKangXingJpanel = faShuKangXingJpanel;
        this.index = index;
        // TODO Auto-generated constructor stub
    }
    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }

    @Override
    public void nochoose(MouseEvent e) {
        RolePetResistanceJframe.getResistancejframe().getResistancejpanel().PetResistanceRefresh(index,!faShuKangXingJpanel.isExpanded());
        if (faShuKangXingJpanel!=null&&e.getButton() == MouseEvent.BUTTON3){
            FormsManagement.HideForm(8);
            FormsManagement.HideForm(58);
        }
    }

}
