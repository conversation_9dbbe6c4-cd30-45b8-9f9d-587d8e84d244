package com.tool.btn;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

import javax.swing.SwingConstants;

import org.come.Frame.ZhuFrame;
import org.come.Jpanel.QuackGameJpanel;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;

public class QuackGameBtn extends MoBanBtn {
    private QuackGameJpanel gameJpanel;

    // public QuackGameBtn(String iconpath, int type, String text,
    // QuackGameJpanel gameJpanel) {
    // super(iconpath, type);
    // this.gameJpanel = gameJpanel;
    // setText(text);
    // setFont(UIUtils.TEXT_NAME_FONT);
    // setForeground(UIUtils.getColor("orange"));
    // setVerticalTextPosition(SwingConstants.CENTER);
    // setHorizontalTextPosition(SwingConstants.CENTER);
    // }
    public QuackGameBtn(String iconpath, int type, Color[] colors, Font font, String text, QuackGameJpanel gameJpanel) {
        super(iconpath, type, colors);
        this.gameJpanel = gameJpanel;
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if ("一键召唤".equals(getText())) {
            BigDecimal rgid = null;
            for (int i = 0; i < GoodsListFromServerUntil.getGoodslist().length; i++) {
                Goodstable goodstable = GoodsListFromServerUntil.getGoodslist()[i];
                if (goodstable != null && goodstable.getType() == 889) {
                    rgid = goodstable.getRgid();
                    goodstable.goodxh(1);
                    gameJpanel.kyNum = goodstable.getUsetime();
                    if (gameJpanel.kyNum <= 0) {
                        GoodsListFromServerUntil.Deletebiaoid(rgid);
                    }
                    break;
                }
            }
            if (rgid == null) {
                ZhuFrame.getZhuJpanel().addPrompt("你没有通灵宝券，快去购买吧！！");
                return;
            }
            String sendMes = Agreement.getFiveMsgAgreement("G" + rgid);
            SendMessageUntil.toServer(sendMes);
            // 设置按钮不可点击
            gameJpanel.getGameBtn().setBtn(-1);
        } else if ("再来一次".equals(getText())) {
            gameJpanel.getGameBtn().setText("一键召唤");
            gameJpanel.reset();
            // //向服务器获取上面一排五个的图片信息
            // String sendMes = Agreement.getFiveMsgAgreement("");
            // SendMessageUntil.toServer(sendMes);
        } else if ("?".equals(getText())) {
            if (!FormsManagement.getframe(72).isVisible()) {
                FormsManagement.showForm(72);
            } else {
                FormsManagement.HideForm(72);
            }

        }
    }
}
