package com.tool.tcp;

import java.awt.*;

public class PartTwo implements NewPart{
	/**新人物动作加载*/
	private long skin;//路径
	private HHOne[] ones;//全部动作
	private long[] ls;//当前图片拼接
	private Sprite[] tcps;//画布
	private int act;//动作
	private int lvl;//层级
	private long colorId;//颜色Id
	private NewPart flyPart;
	private NewPart part;//下一层 层数低先画  默认1层  召唤兽饰品2层
	private float shadowOffsetY = 0; // 影子Y轴偏移量
	private boolean isFlying = false; // 是否在飞行中
	private static final float MAX_SHADOW_OFFSET = 100f; // 最大影子偏移量
	private static final float SHADOW_STEP = 5f; // 每次更新增加/减少的偏移量
	private double bodyAlpha = 1.0f; // 主体透明度
	
	public PartTwo(long skin, HHOne[] ones, int act, int lvl,String color) {
		super();
		this.skin = skin;
		this.ones = ones;
		this.ls = ones[0].getLs();
		this.act = act;
		this.lvl = lvl;
		if (color!=null&&!color.equals("")) {
			try { this.colorId=Long.parseLong(color); } catch (Exception e) { }
		}
		
	}
	@Override
	public void draw(Graphics g, int x, int y, int dir, long time) {
		synchronized(this){
			if (tcps==null) {resetHH();}
			
			// 始终更新影子位置
			updateShadowOffset(time);
			// 绘制主体
			if (flyPart!=null) {
				flyPart.drawFly(g, x, y, dir, time, 1F);
			}
			
			for (int i = 0; i < tcps.length; i++) {
				if (tcps[i]==null) { loadTcp(i); }
				if (tcps[i]!=null) {
					tcps[i].updateToTime(time,dir);
					tcps[i].draw(g, x, y);
				}
			}
		}
	 if (part!=null) {
			part.draw(g, x, y, dir, time);
		}
	}
	@Override
	public void draw(Graphics g, int x, int y, int dir, long time, float alpha) {
		// TODO Auto-generated method stub
		synchronized(this){
			if (flyPart!=null) {
				flyPart.drawFly(g, x, y, dir, time,alpha);
			}
			else { SpriteFactory.shadow.draw(g, x, y);	}
			if (tcps==null) {resetHH();}
			for (int i = 0; i < tcps.length; i++) {
				if (tcps[i]==null) { loadTcp(i); }
				if (tcps[i]!=null) {
					tcps[i].updateToTime(time,dir);
					tcps[i].draw(g, x, y, alpha);
				}
			}
		}
		if (part!=null) {
			part.draw(g,x,y,dir,time);
		}
	}

	@Override
	public void draw(Graphics g, int x, int y, int dir, long time, int type) {

	}

	@Override
	public void drawEnd(Graphics g, int x, int y, int dir, float alpha) {
		// TODO Auto-generated method stub
		if (lvl!=1) {
			if (part!=null) {
				part.drawEnd(g,x,y,dir,alpha);
			}
			return;
		}
		synchronized(this){
			if (tcps==null) {resetHH();}
			SpriteFactory.shadow.draw(g, x, y);
			for (int i = 0; i < tcps.length; i++) {
				if (tcps[i]==null) { loadTcp(i); }
				if (tcps[i]!=null) {
					tcps[i].updateToTime(tcps[i].getTime()-1,dir);
					tcps[i].draw(g, x, y, alpha);
				}
			}	
		}	
	}
	@Override
	public void drawBattle(Graphics g, int x, int y, int dir, long time,float alpha) {
		// TODO Auto-generated method stub
		synchronized(this){
			if (tcps==null) {resetHH();}
			SpriteFactory.shadow.draw(g, x, y);
			for (int i = 0; i < tcps.length; i++) {
				if (tcps[i]==null) { loadTcp(i); }
				if (tcps[i]!=null) {
					tcps[i].updateToTime(time,SpriteFactory.changdir(dir,tcps[i].getAnimationCount()));
					tcps[i].draw(g, x, y, alpha);
				}
			}
		}
		if (part!=null) {
			part.drawBattle(g, x, y, dir, time, alpha);
		}
	}
	@Override
	public void drawFly(Graphics g, int x, int y, int dir, long time, float alpha) {
		synchronized(this){
			if (tcps==null) {resetHH();}
			// 更新影子位置
			updateShadowOffset(time);
			// 如果影子偏移量大于0，绘制影子
			if (shadowOffsetY > 0) {
				for (int i = 0; i < tcps.length; i++) {
					if (tcps[i]==null) { loadTcp(i); }
					if (tcps[i]!=null) {
						tcps[i].updateToTime(time,dir);
						tcps[i].draw(g, x, y + (int)shadowOffsetY);    
					}
				}
			}
			
			// 绘制主体
			for (int i = 0; i < tcps.length; i++) {
				if (tcps[i]!=null) {
					tcps[i].updateToTime(time,dir);
					tcps[i].draw(g, x, y);    
				}
			}
		}
	}
	@Override
	public NewPart addPart(NewPart newPart) {
		// TODO Auto-generated method stub
		if (newPart==null) {
			return this;
		}
		if (newPart.getLvl()<lvl) {
			newPart.setPart(this);
			return newPart;
		}
		if (part==null) {
			part=newPart;
			return this;
		}else {
			part=part.addPart(newPart);			
		}
		return this;
	}
	@Override
	public NewPart removePart(String rSkin) {
		// TODO Auto-generated method stub
		if (isD(rSkin)) {
			return part;
		}
		if (part!=null) {
			part=part.removePart(rSkin);	
		}
		return this;
	}
	/**对比*/
	private boolean isD(String rSkin) {
		return rSkin.equals(skin+"");
	}
	@Override
	public boolean contains(int x, int y) {
		// TODO Auto-generated method stub
		if (lvl == 1) {
			synchronized (this) {
				if (tcps!=null) {
					for (int i = 0; i < tcps.length; i++) {
						if (tcps[i]!=null) {
							boolean is=tcps[i].contains(x, y);
							if (is) {return is;}
						}
					}
				}
			}
		} else if (part != null) {
			return part.contains(x, y);
		}
		return false;
	}
	@Override
	public void recycle() {
		// TODO Auto-generated method stub
		synchronized(this){
			if (tcps!=null) {
				for (int i = 0; i < tcps.length; i++) {
					tcps[i]=null;
				}
			}
		}
		if (part!=null) {
			part.recycle();
		}
	}
	@Override
	public int getTime() {
		// TODO Auto-generated method stub
		if (lvl!=1&&part!=null) {
			return part.getTime();
		}
		synchronized(this){
			if (tcps==null) {
				resetHH();
			}
			if (tcps.length>1) {
				if (tcps[1]!=null) {
		        	return tcps[1].getTime();
				}
				loadTcp(1);
			}else {
				if (tcps[0]!=null) {
		        	return tcps[0].getTime();
				}
				loadTcp(0);
			}
			
		}
		return 1200;
	}
	@Override
	public void loadTcp() {
		// TODO Auto-generated method stub
		
	}
	public void loadTcp(int i) {
//		System.out.println(act+"=="+skin);
//		tcps[i]= WdfLoaDing.dynamic("0x91939"+(100+act),"defaut.wdf");
		tcps[i]=SpriteFactory.Prepare(colorId<<40|ls[i],act);
	}
	@Override
	public int getAct() {
		// TODO Auto-generated method stub
		if (lvl!=1&&part!=null) {
			return part.getAct();
		}
		return act;
	}
	@Override
	public void setAct(int Act) {
		// TODO Auto-generated method stub
		if (act!=Act) {
			act=Act;
			synchronized(this){
				resetHH();
			}
		}
		if (part!=null) {
			part.setAct(Act);
		}
	}
	@Override
	public int getLvl() {
		// TODO Auto-generated method stub
		return lvl;
	}
	@Override
	public NewPart getPart() {
		// TODO Auto-generated method stub
		return part;
	}
	@Override
	public void setPart(NewPart part) {
		// TODO Auto-generated method stub
		this.part=part;
	}
	/**重置当前动作*/
	public void resetHH(){
		ls=ones[0].getLs();
		if (flyPart!=null) {
			for (int i = 0; i < ones.length; i++) {
				if (ones[i].getAct()==2)   { ls=ones[i].getLs();break; }
			}
		}else {
			for (int i = 0; i < ones.length; i++) {
				if (ones[i].getAct()==act) { ls=ones[i].getLs();break; }
			}
		}
		
		if (tcps==null||tcps.length!=ls.length) {
			tcps=new Sprite[ls.length];
		}else {
			for (int i = 0; i < tcps.length; i++) { tcps[i]=null; }
		}
	}
	@Override
	public NewPart setPart(int lvl, String skin) {
		// TODO Auto-generated method stub
		if (this.lvl==lvl) {
			NewPart newPart=new PartOne(skin, act, lvl, colorId+"");
			newPart.setPart(part);
        	return newPart;
		}else if (part!=null) {
			return part.setPart(lvl, skin);
		}
		return this;
	}
	@Override
	public NewPart setPart(int lvl, long skin, HHOne[] ones) {
		// TODO Auto-generated method stub
        if (this.lvl==lvl) {
        	if (this.skin==skin) {
				return this;
			}
			synchronized (this) {
				this.skin = skin;
				this.ones = ones;
				resetHH();
			}
			return this;
		}else if (part!=null) {
			return part.setPart(lvl, skin, ones);
		}
		return this;
	}
	@Override
	public int getPy() {
		// TODO Auto-generated method stub
		if (lvl==1) {
			synchronized(this){
				if (tcps!=null) {
					int py=0;
					for (int i = 0; i < tcps.length; i++) {//暂时未整理
						if (tcps[i]!=null&&tcps[i].getHightMax()>py) {
							py=tcps[i].getHightMax();
						}
					}
					return py;
				}
			}
		}else if (part!=null) {
			return part.getPy();
		}
		return -1;
	}
	@Override
	public NewPart clonePart() {
		// TODO Auto-generated method stub
		NewPart newPart=new PartTwo(skin, ones, act, lvl, colorId+"");
		if (part!=null) {newPart.setPart(part.clonePart());}
		return newPart;
	}
	@Override
	public int getAnimationCount() {
		// TODO Auto-generated method stub
		if (lvl==1) {
			synchronized(this){
				if (tcps!=null) {
					for (int i = 0; i < tcps.length; i++) {
						if (tcps[i]!=null) {
							return tcps[i].getAnimationCount();
						}
					}	
				}
			}
		}else if (part!=null) {
			return part.getAnimationCount();
		}
		return 2;
	}
	@Override
	public void setFly(String skin) {
		if (lvl==1) {
			if (skin==null) {
				isFlying = false; // 开始降落动画
			} else {
				isFlying = true; // 开始起飞动画
				flyPart=SpriteFactory.createPart(skin, -3, 1, null);
			}
			return;
		}
		if (part!=null) {
			part.setFly(skin);
		}
	}
	// 更新影子位置
	private void updateShadowOffset(long time) {
        if (isFlying && shadowOffsetY < MAX_SHADOW_OFFSET) {
            shadowOffsetY = Math.min(shadowOffsetY + SHADOW_STEP, MAX_SHADOW_OFFSET);
        } else if (!isFlying && shadowOffsetY > 0) {
            shadowOffsetY = Math.max(shadowOffsetY - SHADOW_STEP, 0);
        }
    }

	public double getBodyAlpha() {
		return bodyAlpha;
	}

	@Override
	public void setBodyAlpha(double alpha) {
		this.bodyAlpha = alpha;
	}

	@Override
	public float getCurrentShadowOffset() {
		return 0; // PartTwo不处理影子偏移，返回0
	}
}
