package jxy2.supet;

import com.tool.btn.FormsOnOffBtn;
import com.tool.tcpimg.RichLabel;
import com.tool.tcpimg.UIUtils;
import jxy2.UiBack;
import jxy2.UniversalModel;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.petView.JuPodanMouse;
import jxy2.petView.ShowPetJuPoDanSkillsMouslisten;
import org.come.Jpanel.PetSkillsJpanel;
import org.come.entity.Goodstable;
import org.come.entity.RoleSummoning;
import org.come.llandudno.AtlasListCell;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.SrcollPanelUI;
import org.come.until.UserMessUntil;
import org.come.until.Util;

import javax.swing.*;
import java.awt.*;
import java.util.List;

/**
 * ClassName:聚魄丹
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/4/8 12:00
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class JupoDanJPanel extends JPanel {

    private JupoDanBtn acquiringBtn,opengridBtn;
    private JScrollPane jScrollPane,jScrollPane1;
    private JLabel[] labPetskills = new JLabel[9];// 召唤兽的七个技能框
    private PetSkillsBtn[] petSkillsBtns = new PetSkillsBtn[9];
    private JLabel[] labSkilllock = new JLabel[9];
    private PetSkillsJpanel petSkillsJpanel;// 引用PetSkillsJpanel
    private ShowPetJuPoDanSkillsMouslisten[] showPetJuPoDanSkillsMouslistens = new ShowPetJuPoDanSkillsMouslisten[9];
    public static int idx = -1;
    public static int idxs = -1;
    private JupoDanBtn btngoodlock;// 单个物品加锁
    // 列表
    public JList<String> listpet;
    // 列表中的对象
    public DefaultListModel<String> listModel;// 对象
    public JList<UniversalModel> petList;
    private DefaultListModel<UniversalModel> defaultListModel = new DefaultListModel<>();

    private JLabel goodimg;
    private RichLabel richLabel;
    private Goodstable goodstable;
    private FormsOnOffBtn offBtn;
    private SipetJPanel sipetJPanel; // 引用SipetJPanel以共享召唤兽列表
    public JupoDanJPanel() {
        this.setPreferredSize(new Dimension(480, 470));
        this.setLayout(null);  // 使用绝对布局
        this.setBackground(UIUtils.Color_BACK);
        offBtn = new FormsOnOffBtn(ImgConstants.stop, 1, 145, 0);
        offBtn.setBounds(480-35, 5, 23, 22);
        add(offBtn);
        acquiringBtn = new JupoDanBtn(ImgConstants.tz85, 1,  1, "获取技能", this,"");
        acquiringBtn.setBounds(214, 412, 100, 26);
        this.add(acquiringBtn);
        opengridBtn = new JupoDanBtn(ImgConstants.tz85, 1, 2, "开技能格", this,"");
        opengridBtn.setBounds(344, 412, 100, 26);
        this.add(opengridBtn);

        // 获取SipetJPanel实例
        sipetJPanel = SipetJPanel.getInstance();
        petSkillsJpanel = PetSkillsJpanel.getInstance();

        for (int i = 0; i < labPetskills.length; i++) {
            int x = petSkillsJpanel.positions[i][0]; // 获取X坐标
            int y = petSkillsJpanel.positions[i][1]; // 获取Y坐标
            labSkilllock[i] = new JLabel();
            labSkilllock[i].setBounds(x+180, y-60, 15, 17);
            this.add(labSkilllock[i]);
            petSkillsBtns[i] = new PetSkillsBtn(ImgConstants.tz330,1,i+20,this);
            petSkillsBtns[i].setBounds(x+145, y-91, 50, 50);
            this.add(petSkillsBtns[i]);
            labPetskills[i] = petSkillsJpanel.getLabPetskills()[i];
            showPetJuPoDanSkillsMouslistens[i] = new ShowPetJuPoDanSkillsMouslisten(this, i);
            labPetskills[i].addMouseListener(showPetJuPoDanSkillsMouslistens[i]);
            labPetskills[i].setBounds(x+145, y-91, 50, 50);
            this.add(labPetskills[i]);
        }


        petList = new JList<>(defaultListModel);
        petList.setSelectionForeground(Color.white);
        petList.setForeground(Color.white);
        petList.setFont(UIUtils.TEXT_HY16);
        petList.setModel(defaultListModel);
        petList.setCellRenderer(new AtlasListCell(idxs, UIUtils.COLOR_XZ, 143, 20, 15));
        petList.addMouseListener(new JuPodanMouse(petList));
        petList.addMouseMotionListener(new JuPodanMouse(petList));
        petList.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        petList.setOpaque(false);

        // 使用SipetJPanel的滚动面板
        jScrollPane = new JScrollPane(petList);
        jScrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane.getVerticalScrollBar().setUnitIncrement(50);
        jScrollPane.setOpaque(false);
        jScrollPane.getViewport().setOpaque(false);
        jScrollPane.setBounds(23, 80, 167, 158);
        jScrollPane.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane);

        listModel = new DefaultListModel<String>();
        // 聚魄丹列表
        listpet = new JList<String>();
        listpet.setOpaque(false);
        listpet.setSelectionBackground(new Color(33, 42, 52));
        listpet.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        listpet.setBackground(UIUtils.Color_BACK); // 设置列表框为透明背景
        listpet.setModel(listModel);
        listpet.setCellRenderer(new AtlasListCell(idx,UIUtils.COLOR_XZ,160,20,7));
        listpet.addMouseListener(new MapJlistJupoDanMouslisten(listpet, this));
        listpet.addMouseMotionListener(new MapJlistJupoDanMouslisten(listpet, this));

        //聚魄丹列表
        jScrollPane1 = new JScrollPane(listpet);
        jScrollPane1.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
        jScrollPane1.getVerticalScrollBar().setUI(new SrcollPanelUI());
        jScrollPane1.getVerticalScrollBar().setUnitIncrement(50);
        jScrollPane1.setOpaque(false);
        jScrollPane1.getViewport().setOpaque(false);
        jScrollPane1.setBounds(23, 80+195, 167, 158);
        jScrollPane1.setBorder(BorderFactory.createEmptyBorder());
        jScrollPane1.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        this.add(jScrollPane1);

        //文本信息
        richLabel = new RichLabel("",UIUtils.MSYH_HY14,165);
        richLabel.setBounds(198+72,350,194,57);
        add(richLabel);
        //物品图片
        goodimg = new JLabel();
        goodimg.setBounds(202, 346, 50, 50);
        this.add(goodimg);
        //技能锁按钮
        btngoodlock = new JupoDanBtn(ImgConstants.LOCK, 1, 3,this);
        btngoodlock.setBounds(420, 66, 18, 18);
        this.add(btngoodlock);
    }


    /**服务器获取召唤兽数据进行对比*/
    public void ServerObtainsData() {
        defaultListModel.clear();
        petList.removeAll();
        List<RoleSummoning> pets = UserMessUntil.getPetListTable();
        for (int i = 0; i < pets.size(); i++) {
            RoleSummoning pet = pets.get(i);
            if (pet != null) {
                UniversalModel universalModel = new UniversalModel(143, 140);
                universalModel.PetData(i, pet,
                        0, 2,
                        20,
                        false,
                        true,
                        20, 1);
                universalModel.setBounds(0,i*20,143,140);
                petList.add(universalModel);
                defaultListModel.add(i, universalModel);

                //判断是否开启了启灵
                if (pet.getSid().compareTo(UserMessUntil.getChosePetMes().getSid()) == 0) {
                    petList.setSelectedIndex(i);
//                PetAddPointMouslisten.ShowPet(pet);
                }
            }
        }

        int inde = Math.max(20 * UserMessUntil.getPetListTable().size(), 199);
        petList.setPreferredSize(new Dimension(168, inde));
    }

    /**刷新数据*/
    public void RemoveCon(Goodstable good) {
        //背包中找到聚魄丹
        //判断召唤兽技能是否锁定
        ServerObtainsData();
        setGoodstable(good);
        RewriteState(UserMessUntil.getChosePetMes());
        listModel.clear();
        int index = 0;
        for (int i = 0; i < GoodsListFromServerUntil.getGoodslist().length ; i++) {
            Goodstable goods = GoodsListFromServerUntil.getGoodslist()[i];
            if (goods != null && goods.getType()==2326) {
                String[] vs = goods.getValue().split("\\|");
                for (int j = 0; j < vs.length; j++) {
                    String[] vss = vs[j].split("=");
                        if (vss[0].equals("技能")){
                            listModel.add(j,vss[1]+"|"+goods.getRgid());
                            index++;
                            break;
                        }
                }
            }else {
                getGoodimg().setIcon(null);
                richLabel.setText("");
            }
        }

        XzSeletedindex(good);
        int indie = Math.max(20 * index, 165);
        listpet.setPreferredSize(new Dimension(167, indie));
    }

    public void RewriteState(RoleSummoning pet) {
        boolean isLocked = (pet.getSkilllock() == 1);
        acquiringBtn.setBtn(isLocked ? -1 : 1);   // 直接根据状态取相反值
        opengridBtn.setBtn(-acquiringBtn.getBtn());// 通过取反避免重复逻辑
    }



    private void XzSeletedindex(Goodstable good) {
        StringBuilder buffer = new StringBuilder();
            String[] vs = good.getValue().split("\\|");
            buffer.append("#Y").append(vs[0].replace("=", "：#G"));
            buffer.append("#r");
            buffer.append("#Y").append(vs[1].replace("=", "：#G"));
            richLabel.setText(buffer.toString());
            goodimg.setIcon(GoodsListFromServerUntil.imgpathWdfile(good.getSkin(), 50 ,50));
        for (int i = 0; i < listModel.size(); i++) {
            if (listModel.get(i).split("\\|")[1].equals(good.getRgid()+"")){
                listpet.setSelectedIndex(i);
                listpet.ensureIndexIsVisible(i);
                break;
            }
        }
    }
    public void updateButtonImages(int uiType) {
        String panelName = this.getClass().getSimpleName();
        offBtn.setIcons(Juitil.getImgs(Juitil.offbtn(uiType)));
        Juitil.changeButtonColor(this,uiType);
        //调整窗体大小
        if (uiType!=1){
            Juitil.adjustFrameSize(480, 450, 145);
        }else {
            Juitil.adjustFrameSize(500, 440, 145);
        }
        jScrollPane.getVerticalScrollBar().setUI(Juitil.createUI(uiType));
        jScrollPane1.getVerticalScrollBar().setUI(Juitil.createUI(uiType));
        acquiringBtn.setIcons(Juitil.getImgs(Juitil.btn100_26(uiType)));
        opengridBtn.setIcons(Juitil.getImgs(Juitil.btn100_26(uiType)));
        UiBack.getComponentStyle(panelName, "jScrollPane").applyToScrollBar(jScrollPane);
        UiBack.getComponentStyle(panelName, "jScrollPane1").applyToScrollBar(jScrollPane1);
        UiBack.getComponentStyle(panelName, "btngoodlock").applyToButton(btngoodlock);
        UiBack.getComponentStyle(panelName, "offBtn").applyToButton(offBtn);
        UiBack.getComponentStyle(panelName, "acquiringBtn").applyToButton(acquiringBtn);
        UiBack.getComponentStyle(panelName, "opengridBtn").applyToButton(opengridBtn);
        int nx = uiType==1?24:0;
        int ny = uiType==1?-15:0;
        for (int i = 0; i < labPetskills.length; i++) {
            int x = petSkillsJpanel.positions[i][0]+nx; // 获取X坐标
            int y = petSkillsJpanel.positions[i][1]+ny; // 获取Y坐标
            labSkilllock[i].setBounds(x+180, y-60, 15, 17);
            petSkillsBtns[i].setBounds(x+145, y-91, 50, 50);
            labPetskills[i].setBounds(x+145, y-91, 50, 50);
        }
        richLabel.setBounds(nx+270,ny+336,194,57);
        goodimg.setBounds((uiType==1?nx+5:nx)+202, ny+336, 50, 50);
    }
    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (Util.SwitchUI==0){
            Juitil.JPanelNewShow(g, getWidth(), getHeight(), "聚魄丹");
        }else if (Util.SwitchUI==1){
            Juitil.SheNewShow(g,500,getHeight(),"聚魄丹",236,86);
        }else {
            Juitil.RedMuNewShow(g,getWidth(),getHeight(),"聚魄丹");
        }
        int nx = Util.SwitchUI == 1?25:0;
        int ny = Util.SwitchUI == 1?-14:0;
        UiBack.UIResource.LIST.draw(g,this,0,0,2,188,0,0);
        Juitil.Textdrawing(g, "召唤兽列表", nx+60,57+ny,Juitil.changGrapColor(), Juitil.changGrapFont());
         if (petList.getSelectedIndex()!=-1) {
            RoleSummoning pet = UserMessUntil.getPetListTable().get(petList.getSelectedIndex());
             if (pet.getSsn().equals("2") || pet.getSsn().equals("3") || pet.getSsn().equals("4")) {
                 g.drawImage(Juitil.tz329.getImage(), nx+198, ny+42, 262, 289, null);
             }else {
                 g.drawImage(Juitil.tz328.getImage(), nx+198, ny+42, 262, 289, null);
             }
        }
        Juitil.Textdrawing(g, "拥有聚魄丹", nx+60,ny+245,Juitil.changGrapColor(), Juitil.changGrapFont());
        UiBack.UIResource.BK_59X58.draw(g,this);
        UiBack.UIResource.BACK_1.draw(g,this);
    }




    public JupoDanBtn getAcquiringBtn() {
        return acquiringBtn;
    }

    public void setAcquiringBtn(JupoDanBtn acquiringBtn) {
        this.acquiringBtn = acquiringBtn;
    }

    public JupoDanBtn getOpengridBtn() {
        return opengridBtn;
    }

    public void setOpengridBtn(JupoDanBtn opengridBtn) {
        this.opengridBtn = opengridBtn;
    }

    public SipetJPanel getSipetJPanel() {
        return sipetJPanel;
    }

    public void setSipetJPanel(SipetJPanel sipetJPanel) {
        this.sipetJPanel = sipetJPanel;
    }


    public JLabel[] getLabPetskills() {
        return labPetskills;
    }

    public void setLabPetskills(JLabel[] labPetskills) {
        this.labPetskills = labPetskills;
    }

    public PetSkillsJpanel getPetSkillsJpanel() {
        return petSkillsJpanel;
    }

    public void setPetSkillsJpanel(PetSkillsJpanel petSkillsJpanel) {
        this.petSkillsJpanel = petSkillsJpanel;
    }

    public PetSkillsBtn[] getPetSkillsBtns() {
        return petSkillsBtns;
    }

    public void setPetSkillsBtns(PetSkillsBtn[] petSkillsBtns) {
        this.petSkillsBtns = petSkillsBtns;
    }

    public RichLabel getRichLabel() {
        return richLabel;
    }

    public void setRichLabel(RichLabel richLabel) {
        this.richLabel = richLabel;
    }

    public JLabel getGoodimg() {
        return goodimg;
    }

    public void setGoodimg(JLabel goodimg) {
        this.goodimg = goodimg;
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    public JScrollPane getjScrollPane() {
        return jScrollPane;
    }

    public void setjScrollPane(JScrollPane jScrollPane) {
        this.jScrollPane = jScrollPane;
    }

    public JScrollPane getjScrollPane1() {
        return jScrollPane1;
    }

    public void setjScrollPane1(JScrollPane jScrollPane1) {
        this.jScrollPane1 = jScrollPane1;
    }

    public static int getIdx() {
        return idx;
    }

    public static void setIdx(int idx) {
        JupoDanJPanel.idx = idx;
    }

    public JList<String> getListpet() {
        return listpet;
    }

    public void setListpet(JList<String> listpet) {
        this.listpet = listpet;
    }

    public DefaultListModel<String> getListModel() {
        return listModel;
    }

    public void setListModel(DefaultListModel<String> listModel) {
        this.listModel = listModel;
    }

    public JupoDanBtn getBtngoodlock() {
        return btngoodlock;
    }

    public void setBtngoodlock(JupoDanBtn btngoodlock) {
        this.btngoodlock = btngoodlock;
    }

    public JLabel[] getLabSkilllock() {
        return labSkilllock;
    }

    public void setLabSkilllock(JLabel[] labSkilllock) {
        this.labSkilllock = labSkilllock;
    }

    public ShowPetJuPoDanSkillsMouslisten[] getShowPetJuPoDanSkillsMouslistens() {
        return showPetJuPoDanSkillsMouslistens;
    }

    public void setShowPetJuPoDanSkillsMouslistens(ShowPetJuPoDanSkillsMouslisten[] showPetJuPoDanSkillsMouslistens) {
        this.showPetJuPoDanSkillsMouslistens = showPetJuPoDanSkillsMouslistens;
    }


}

