package jxy2.refine;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/***
 * 1 升级 2: 90%
 * 2 升级 3: 80%
 * 3 升级 4: 70%
 * 4 升级 5: 60%
 * 5 升级 6: 50%
 * 6 升级 7: 40%
 * 7 升级 8: 30%
 * 8 升级 9: 25%
 * 9 升级 10: 20%
 * 10 升级 11: 15%
 * 11 升级 12: 10%
 * 12 升级 13: 8%
 * 13 升级 14: 5%
 * 14 升级 15: 3%
 */
public class EquipmentRefinement {
    private static final Map<Integer, Double> baseSuccessRates = new HashMap<>();
    static {
        // 定义每个等级的基础升级几率 (假设值)
        baseSuccessRates.put(1, 0.90);
        baseSuccessRates.put(2, 0.80);
        baseSuccessRates.put(3, 0.70);
        baseSuccessRates.put(4, 0.60);
        baseSuccessRates.put(5, 0.50);
        baseSuccessRates.put(6, 0.40);
        baseSuccessRates.put(7, 0.30);
        baseSuccessRates.put(8, 0.25);
        baseSuccessRates.put(9, 0.20);
        baseSuccessRates.put(10, 0.15);
        baseSuccessRates.put(11, 0.10);
        baseSuccessRates.put(12, 0.08);
        baseSuccessRates.put(13, 0.05);
        baseSuccessRates.put(14, 0.03);
        baseSuccessRates.put(15, 0.02);
    }

    private static final Map<String, Double> runeEffects = new HashMap<>();
    static {
        // 定义每种符文增加的成功几率
        runeEffects.put("保底符", 0.0);
        runeEffects.put("梦境地灵符", 0.15);
        runeEffects.put("梦境天运符", 0.20);
        runeEffects.put("梦境乾坤符", 0.25);
    }

    public static boolean refine(int level, String runeType) {
        if (level < 1 || level > 15 || !baseSuccessRates.containsKey(level) || !runeEffects.containsKey(runeType)) {
            throw new IllegalArgumentException("Invalid level or rune type");
        }

        double baseRate = baseSuccessRates.get(level);
        double runeEffect = runeEffects.get(runeType);
        double finalSuccessRate = baseRate + runeEffect;
        // 确保成功率不超过100%
        finalSuccessRate = Math.min(finalSuccessRate, 1.0);

        // 进行精炼判定
        Random rand = new Random();
        return rand.nextDouble() <= finalSuccessRate;
    }

    public static void main(String[] args) {
        int level = 14;
        String runeType = "梦境乾坤符"; // 例子：使用地灵符
            boolean isSuccess = refine(level, runeType);
            System.out.println("Level " + level + " to " + (level+1) + " Refinement： " + (isSuccess ? "精炼成功" : "精炼失败，装备消失"));

    }
}
