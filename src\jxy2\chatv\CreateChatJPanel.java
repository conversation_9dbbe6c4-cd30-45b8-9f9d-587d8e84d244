package jxy2.chatv;

import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import org.come.until.CutButtonImage;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.*;

public class CreateChatJPanel extends JPanel {
    public static boolean[] closeCk  = new boolean[7];
    private CreateChatBtn chatBtn;
    private JLabel[] selection = new JLabel[7];
    public List<String> stringList = new ArrayList<>();
    private String[] basis = {"综合","当前","系统","队伍","世界","战斗","信息"};
    private int index;
    public CreateChatJPanel() {
        this.setPreferredSize(new Dimension(300, 216));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,114,300);
        chatBtn = new CreateChatBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "增 加", this,0,"");
        chatBtn.setBounds(126,178,59,24);
        add(chatBtn);

        for (int i = 0; i < selection.length; i++) {
            int shop_x = i % 2;
            int shop_y = i / 2;
            selection[i] = new JLabel();
            selection[i].setIcon(CutButtonImage.getWdfPng(ImgConstants.tz83,"defaut.wdf"));
            selection[i].setForeground(UIUtils.COLOR_value);
            selection[i].setText(basis[i]);
            selection[i].addMouseListener(new PaneListMouse(i));
            selection[i].setHorizontalTextPosition(SwingConstants.RIGHT);
            selection[i].setFont(UIUtils.MSYH_HY13);
            selection[i].setBounds(60+shop_x*88,65+shop_y*26,50,16);
            add(selection[i]);
        }

    }
    class PaneListMouse extends MouseAdapter {
        public int type;
        public PaneListMouse(int type) {
            this.type = type;
        }
        @Override
        public void mousePressed(MouseEvent e) {
                if (type==5||type==6||type==4) {
                    closeCk[type] = !closeCk[type];
                    selection[type].setIcon(CutButtonImage.getWdfPng(closeCk[type] ? ImgConstants.tz84 : ImgConstants.tz83, "defaut.wdf"));
                    if (closeCk[type]) {
                        stringList.add(selection[type].getText());
                    } else {
                        stringList.remove(selection[type].getText());
                    }
                    index= type;
                }
        }
        @Override
        public void mouseReleased(MouseEvent e) {

        }
    }



    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.JK, 0, 0, getWidth(), 32, 1);
        Juitil.ImngBack(g, Juitil.JK_1, 0, 32, getWidth()+4, getHeight()-32, 1);
        Juitil.ImngBack(g, Juitil.tz22, 7, 55, 285, 136, 1);
        Juitil.Subtitledrawing(g, getWidth()/2 -55, 24, "聊天窗口创建", Color.WHITE, UIUtils.HYXKJ_HY20,1);
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
