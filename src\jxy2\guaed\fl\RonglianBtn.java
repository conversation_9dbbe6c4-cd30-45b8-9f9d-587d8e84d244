package jxy2.guaed.fl;

import com.tool.btn.MoBanBtn;
import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.bean.Alchemy;
import org.come.bean.SuitOperBean;
import org.come.entity.Goodstable;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import javax.swing.Timer;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;
import java.util.*;

public class RonglianBtn  extends MoBanBtn {
    public RonglianJPanel ronglianJPanel;
    public int BtnId;
    public static int size;
    public static Timer timers;
    public RonglianBtn(String iconpath, int type, int BtnId, String labelName, RonglianJPanel ronglianJPanel, String string) {
        super(iconpath, type,0,string,labelName);
        this.BtnId = BtnId;
        this.ronglianJPanel = ronglianJPanel;
    }


    @Override
    public void chooseyes() {

    }

    @Override
    public void chooseno() {

    }
    // 增加一个状态追踪变量
    public static int activeButtonId = 0;
    @Override
    public void nochoose(MouseEvent e) {
        switch (BtnId){
            case 1:
                //可吸收灵窍个数
                resetImgbackVisibility();
//                CycleTimer();
                if (GoodsListFromServerUntil.isExist(ronglianJPanel.getGoodstables()[1])) {
                    return;
                }
                AsouGuard(ronglianJPanel.getGoodstables()[0],ronglianJPanel.getGoodstables()[1]);

                break;
            case 2:
            case 3:

                if (BtnId==3&&ronglianJPanel.getAddGoodsImg()[0].getIcon()==null){
                    ZhuFrame.getZhuJpanel().addPrompt("#G请先加入主守护石");
                    return;
                }
                // 如果点击的是当前活跃按钮，则关闭
                if (activeButtonId == BtnId) {
                    FormsManagement.HideForm(142);
                    activeButtonId = 0;
                    return;
                }
                // 如果有其他按钮已经打开，先关闭
                if (activeButtonId != 0 && activeButtonId != BtnId) {
                    FormsManagement.HideForm(142);
                }
                // 显示新的表单
                FormsManagement.showForm(142);
                LoadGuardianFrame.getLoadGuardianFrame().getLoadGuardianJPanel().Vis(true);
                activeButtonId = BtnId;
                // 获取父窗体位置
                FuLingFrame fuLingFrame = FuLingFrame.getFuLingFrame();
                int x = fuLingFrame.getX();
                int y = fuLingFrame.getY();
                // 根据按钮ID动态调整位置
                int offsetX = (BtnId == 2) ? -50 : 240;
                // 设置LoadGuardianFrame位置
                LoadGuardianFrame.getLoadGuardianFrame().setBounds(
                        x + offsetX,
                        y + 130,
                        370,
                        170
                );
                ronglianJPanel.setType(BtnId);
                break;
        }
    }

    public static void CycleTimer(RonglianJPanel ronglianJPanel, boolean is,String mes,int match,String keys) {
        // 检查计时器是否已运行
        if (timers != null && timers.isRunning()) {
            return;
        }
        RonglianJPanel.isvstop();
        // 获取已经亮起的位置
        List<Integer> litPositions = new ArrayList<>();
        for (int i = 0; i < ronglianJPanel.getImgback().length; i++) {
            if (ronglianJPanel.getImgback()[i].isVisible()) {
                litPositions.add(i);
            }
        }

        // 找出主副物品相同属性的位置
        List<Integer> matchPositions = new ArrayList<>();
        Map<String, String> mainAttributes = GoodsListFromServerUntil.parseAttributes(ronglianJPanel.getGoodstables()[0].getValue());
        String[] props = ronglianJPanel.getGoodstables()[1].getValue().split("\\|");
        
        for (int i = 0; i < props.length; i++) {
            if (props[i].contains("=")) {
                String key = props[i].split("=")[0];
                if (mainAttributes.containsKey(key)) {
                    matchPositions.add(i);
                }
            }
        }

        // 计算可选择的位置数量
        int[] forgeGrades = AsoulJPanel.getForgeGrades();
        int maxNum = Math.min(Math.max(0, forgeGrades[1] - forgeGrades[0] + 2), 
                            ronglianJPanel.getImgback().length);

        Random random = new Random();
        
        timers = new Timer(88, new ActionListener() {
            private int currentCycle = 0;
            private final int TOTAL_CYCLES = 20;
            private List<Integer> finalPositions = new ArrayList<>();
            private boolean isVisible = true;

            @Override
            public void actionPerformed(ActionEvent e) {
                if (currentCycle < TOTAL_CYCLES - 1) {
                    // 闪烁阶段
                    isVisible = !isVisible;
                    
                    if (isVisible) {
                        finalPositions.clear();
                        // 随机选择位置
                        List<Integer> availablePositions = new ArrayList<>();
                        for (int i = 0; i < ronglianJPanel.getImgback().length; i++) {
                            if (!litPositions.contains(i)) {
                                availablePositions.add(i);
                            }
                        }
                        
                        while (availablePositions.size() > 0 && finalPositions.size() < maxNum) {
                            int index = random.nextInt(availablePositions.size());
                            finalPositions.add(availablePositions.get(index));
                            availablePositions.remove(index);
                        }
                    }
                    
                    // 显示/隐藏选中的位置
                    for (JLabel imgback : ronglianJPanel.getImgback()) {
                        imgback.setVisible(false);
                    }
                    if (isVisible) {
                        for (Integer pos : finalPositions) {
                            ronglianJPanel.getImgback()[pos].setVisible(true);
                        }
                    }
                } else {
                    // 最终选择阶段
                    finalPositions.clear();
                    
                    if (is) {
                        // is=true: 必须选择一个匹配属性的位置，然后随机填充剩余位置
                        if (!matchPositions.isEmpty()) {
                            // 先随机选择一个匹配位置
                            int matchPos = matchPositions.get(random.nextInt(matchPositions.size()));
                            if (!litPositions.contains(matchPos)) {
                                finalPositions.add(matchPos);
                            }
                            
                            // 继续选择剩余位置直到达到maxNum
                            List<Integer> availablePositions = new ArrayList<>();
                            for (int i = 0; i < ronglianJPanel.getImgback().length; i++) {
                                if (!litPositions.contains(i) && !finalPositions.contains(i)) {
                                    availablePositions.add(i);
                                }
                            }
                            
                            while (availablePositions.size() > 0 && finalPositions.size() < maxNum) {
                                int index = random.nextInt(availablePositions.size());
                                finalPositions.add(availablePositions.get(index));
                                availablePositions.remove(index);
                            }
                        }
                    } else {
                        // is=false: 随机选择非匹配属性的位置
                        List<Integer> nonMatchPositions = new ArrayList<>();
                        for (int i = 0; i < ronglianJPanel.getImgback().length; i++) {
                            if (!litPositions.contains(i) && !matchPositions.contains(i)) {
                                nonMatchPositions.add(i);
                            }
                        }
                        
                        while (nonMatchPositions.size() > 0 && finalPositions.size() < maxNum) {
                            int index = random.nextInt(nonMatchPositions.size());
                            finalPositions.add(nonMatchPositions.get(index));
                            nonMatchPositions.remove(index);
                        }
                        ZhuFrame.getZhuJpanel().addPrompt("#Y副守护石提取出的属性/特技与主守护石不匹配，主守护石未发生变化");
                    }
                    
                    // 显示最终选择的位置
                    for (JLabel imgback : ronglianJPanel.getImgback()) {
                        imgback.setVisible(false);
                    }
                    for (Integer pos : finalPositions) {
                        ronglianJPanel.getImgback()[pos].setVisible(true);
                    }
                }

                currentCycle++;
                if (currentCycle >= TOTAL_CYCLES) {
                    timers.stop();
                    if (match!=-1&&!mes.isEmpty()&&!keys.isEmpty()) {
                        ronglianJPanel.getNewtext()[match].setText("+"+mes);
                        ronglianJPanel.getNewtext()[match].setVisible(true);
                        //mes是新是属性值，这里需要更新主物品的属性
                        updateMainItemAttributes(keys,mes); // 调用更新主物品属性的方法
                        ZhuFrame.getZhuJpanel().addPrompt("#Y成功从副守护石中提取出匹配属性/特技，主守护石获得增强");
                    }
                }
            }
        });

        timers.start();
    }

// 添加更新主物品属性的方法  分解 拆开
    public static void updateMainItemAttributes(String key,String newAttributes) {
        // 获取主物品
        Goodstable goodstable = FuLingFrame.getFuLingFrame().getFuLingJPanel().getCardJPanel().getRonglianJPanel().getGoodstables()[0];
        if (goodstable == null) return;
        
        // 获取物品的所有属性
        String value = goodstable.getValue();
        String[] properties = value.split("&");
        StringBuilder newValue = new StringBuilder();
        
        // 遍历所有属性块
        for (int i = 0; i < properties.length; i++) {
            if (i > 0) newValue.append("&");
            
            // 如果是特技属性块，直接保留
            if (properties[i].startsWith("特技")) {
                newValue.append(properties[i]);
                continue;
            }
            
            // 处理普通属性块
            String[] entries = properties[i].split("\\|");
            for (int j = 0; j < entries.length; j++) {
                if (j > 0) newValue.append("|");
                
                String[] keyValue = entries[j].split("=");
                if (keyValue.length == 2) {
                    // 如果是要更新的属性，计算新值
                    if (keyValue[0].equals(key)) {
                        try {
                            // 将字符串转换为数值进行计算
                            double oldValue = Double.parseDouble(keyValue[1]);
                            double addValue = Double.parseDouble(newAttributes);
                            double finalValue = oldValue + addValue;
                            
                            // 获取属性最大值并判断
                            Alchemy alchemy = getAlchemy("守护石", key);
                            if (alchemy != null) {
                                int maxValue = Integer.parseInt(alchemy.getAlchemymv());
                                if (finalValue > maxValue) {
                                    finalValue = maxValue;
                                }
                            }
                            
                            // 如果结果是整数，去掉小数点
                            String result = (finalValue == (int)finalValue) ? 
                                String.valueOf((int)finalValue) : 
                                String.valueOf(finalValue);
                            newValue.append(key).append("=").append(result);
                        } catch (NumberFormatException e) {
                            // 如果转换失败，保持原值
                            newValue.append(entries[j]);
                        }
                    } else {
                        // 否则保持原值
                        newValue.append(entries[j]);
                    }
                } else {
                    // 格式不正确的属性直接保留
                    newValue.append(entries[j]);
                }
            }
        }
        
        // 更新物品属性

        goodstable.setValue(newValue.toString());
    }


    /**
     * 重置所有图像为不可见
     */
    private void resetImgbackVisibility() {
        for (JLabel imgback : ronglianJPanel.getImgback()) {
            imgback.setVisible(false);
        }
    }

    public static void AsouGuard(Goodstable... goods) {
        if (goods.length != 2) {return;}
        if (RoleData.getRoleData().getLoginResult().getScoretype("守护之尘").intValue()<100){
            ZhuFrame.getZhuJpanel().addPrompt("#Y守护之尘数目不足100，无法融炼");
            return;
        }
        List<BigDecimal> rgids = new ArrayList<>();
        for (int i = 0; i < goods.length; i++) {
            if (goods[i].getGoodlock() == 1) {
                ZhuFrame.getZhuJpanel().addPrompt("该物品已被加锁");
                return;
            }
            if (goods[i].getStatus() == 0) {
                if (GoodsListFromServerUntil.isExist(goods[i])) {
                    return;
                }
            }
            rgids.add(goods[i].getRgid());
        }
        goods[1].goodxh(1);
        if (goods[1].getUsetime() <= 0) {
            GoodsListFromServerUntil.Deletebiaoid(goods[1].getRgid());
        }
        goods[1].setStatus(1);

        SuitOperBean operBean = new SuitOperBean();
        operBean.setType(106);
        operBean.setGoods(rgids);
        String senmes = Agreement.suitOperateAgreement(GsonUtil.getGsonUtil().getgson().toJson(operBean));
        SendMessageUntil.toServer(senmes);
    }

    public static Alchemy getAlchemy(String type, String lei){
        List<Alchemy> alchemies= UserMessUntil.getAllAlchemy().getAlchemymap().get(type);
        if (alchemies!=null) {
            for (int i = alchemies.size()-1; i >=0; i--) {
                if (alchemies.get(i).getAlchemykey().equals(lei)) {
                    return alchemies.get(i);
                }
            }
        }
        return null;
    }
}

