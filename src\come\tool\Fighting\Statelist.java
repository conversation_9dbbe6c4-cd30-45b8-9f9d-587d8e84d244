package come.tool.Fighting;

import java.util.List;

/**
 * 执行的状态集合
 * <AUTHOR>
 *
 */
public class Statelist {

	private List<FightingEvents> Round;
	//需要执行的状态集合
	private List<StateProgress> State;
	//进度
	private int Progress=0;
	public List<StateProgress> getState() {
		return State;
	}
	public void setState(List<StateProgress> state) {
		State = state;
	}
	public int getProgress() {
		return Progress;
	}
	public void setProgress(int progress) {
		Progress = progress;
	}
	public List<FightingEvents> getRound() {
		return Round;
	}
	public void setRound(List<FightingEvents> round) {
		Round = round;
	}
	
	
	
}
