package jxy2.zodiac;

import jxy2.jutnil.Juitil;
import org.come.Frame.MsgJframe;
import org.come.entity.Goodstable;
import org.come.until.FormsManagement;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.MessagrFlagUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class RStarSoulsMouse implements MouseListener {
    public RStarSoulsJPanel rStarSoulsJPanel;
    public int caozuo ,type ;
    public static List<String> list = new ArrayList<>();
    public RStarSoulsMouse(int caozuo, RStarSoulsJPanel rStarSoulsJPanel,int type) {
        this.caozuo = caozuo;
        this.type = type;
        this.rStarSoulsJPanel = rStarSoulsJPanel;
    }
    @Override
    public void mouseClicked(MouseEvent e) {

    }

    @Override
    public void mousePressed(MouseEvent e) {
        //TODO 放置选中的物品告诉服务器
        //服务器扣除列表中的2个物品，然后随机给一个物品
        if (type==0) {
            Goodstable goods = GoodsListFromServerUntil.getXpGoods(Juitil.XpName()[caozuo]);
            if (goods==null)return;
            for (BigDecimal key :rStarSoulsJPanel.getGoodsrgid()){
                Goodstable goodstable = GoodsListFromServerUntil.getRgid(key);
                if (goodstable!=null) {
                    int lv = Integer.parseInt(goodstable.getValue().split("=")[1]);
                    int lvl = Integer.parseInt(goods.getValue().split("=")[1]);
                    if (lvl!=lv)return;
                }
            }

            if ((rStarSoulsJPanel.getGoodsrgid().size()) <2 &&!rStarSoulsJPanel.getGoodsrgid().contains(goods.getRgid())) {
                rStarSoulsJPanel.getGoodsrgid().add(goods.getRgid());
            }
            rStarSoulsJPanel.getGoodLabels()[caozuo].setBorder(BorderFactory.createLineBorder(Color. red, 2));
            for (int i = 0; i < rStarSoulsJPanel.getGoodLabels().length; i++) {
                if (i != caozuo) {
                    rStarSoulsJPanel.getGoodLabels()[i].setBorder(BorderFactory.createEmptyBorder());
                }
            }
            list.add(caozuo + "");
        }else {
            int i = 0;
            for (BigDecimal key :rStarSoulsJPanel.getGoodsrgid()){
                if (caozuo==i) {
                    rStarSoulsJPanel.getGoodsrgid().remove(key);
                    rStarSoulsJPanel.firstLv =  null;
                    break;
                }
                i++;
            }
            if (rStarSoulsJPanel.getXhGoods()!=null&&caozuo==2){
                rStarSoulsJPanel.setXhGoods(null);
            }
        }


    }

    @Override
    public void mouseReleased(MouseEvent e) {
        if (type==0){
            list.remove(caozuo + "");
        }else {

        }

    }

    @Override
    public void mouseEntered(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE1)||MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE13)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE12);
        }
        if (type==0){
            MsgJframe.getJframe().getJapnel().StarChartGoods(caozuo,0);
        }
    }

    @Override
    public void mouseExited(MouseEvent e) {
        if (MessagrFlagUntil.ImgFlagImg.equals(MessagrFlagUntil.MOUSE12)) {
            MessagrFlagUntil.setMouse(MessagrFlagUntil.MOUSE1);
        }
        FormsManagement.HideForm(46);
    }
}
