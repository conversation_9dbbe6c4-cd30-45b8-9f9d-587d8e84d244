package come.tool.JDialog;

import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;

public class OpenLxLockDialog  implements TiShiChuLi  {
    @Override
    public void tipBox(boolean tishi, Object object) {
        //设置开启灵犀技能格条件
        //发送信息到服务端
        if (tishi) {

            String sendmes = Agreement.getAgreement().OpenLxLockAgreement("K&" + object+"&");
            SendMessageUntil.toServer(sendmes);
        }
    }
}
