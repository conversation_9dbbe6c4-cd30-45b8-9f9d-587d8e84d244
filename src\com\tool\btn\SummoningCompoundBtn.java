package com.tool.btn;

import com.tool.role.RoleData;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.SummoningCompoundJpanel;
import org.come.bean.SummonPetBean;
import org.come.entity.Goodstable;
import org.come.model.petExchange;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.GoodsListFromServerUntil;
import org.come.until.GsonUtil;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @time 2020年1月10日 下午7:02:42<br>
 * @class 类名:SummoningCompoundBtn<br>
 */
public class SummoningCompoundBtn extends MoBanBtn {

    private int caozuo;
    private SummoningCompoundJpanel compoundJpanel;

    public SummoningCompoundBtn(String iconpath, int type, Color[] colors, String text, Font font, int caozuo,
            SummoningCompoundJpanel compoundJpanel) {
        super(iconpath, type, colors);
        setText(text);
        setFont(font);
        setVerticalTextPosition(SwingConstants.CENTER);
        setHorizontalTextPosition(SwingConstants.CENTER);
        this.caozuo = caozuo;
        this.compoundJpanel = compoundJpanel;
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub

    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub

    }

    @Override
    public void nochoose(MouseEvent e) {
        if (caozuo == 1) {
//            int x =  TestPetJframe.getTestPetJframe().getPetMainJPanel().getPetsum();
//            if (UserMessUntil.getPetListTable() != null && UserMessUntil.getPetListTable().size() >= x) {
//                ZhuFrame.getZhuJpanel().addPrompt2("您的召唤兽可携带的数量已满！！！");
//                return;
//            }
            petExchange petExchange = compoundJpanel.getPetExchange();
            if (petExchange == null) {
                ZhuFrame.getZhuJpanel().addPrompt2("没有选中召唤兽");
                return;
            }
            if (RoleData.getRoleData().getLoginResult().getGold().compareTo(compoundJpanel.getMoney()) < 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("金额不足");
                return;
            }
            String consume = petExchange.getConsume();
            if (consume != null && !"".equals(consume)) {
                String[] split = consume.split("\\|");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].startsWith("G")) {
                        String[] arrMoney = split[i].split("=");
                        Goodstable goodstable = UserMessUntil.getgoodstable(new BigDecimal(arrMoney[1]));
                        int needMum = Integer.parseInt(arrMoney[2]);
                        int goodNum = GoodsListFromServerUntil.getGoodNum(goodstable.getGoodsid());
                        if (needMum > goodNum) {
                            ZhuFrame.getZhuJpanel().addPrompt2("合成材料不足");
                            return;
                        }
                    }
                }
            }

            // 获取召唤兽
            SummonPetBean summonPetBean = new SummonPetBean();
            summonPetBean.setOpertype(2);
            summonPetBean.setPetid(new BigDecimal(petExchange.geteId()));
            String mes = Agreement.getAgreement().summonpetAgreement(GsonUtil.getGsonUtil().getgson().toJson(summonPetBean));
            // 向服务器发送信息
            SendMessageUntil.toServer(mes);
        }
    }
}
