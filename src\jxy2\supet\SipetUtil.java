package jxy2.supet;

import com.tool.PanelDisplay.PetPanelShow;
import com.tool.image.ImageMixDeal;
import com.tool.role.RoleData;
import come.tool.Fighting.FightingMixDeal;
import jxy2.petView.PetSkillQiLingBtn;
import org.come.Frame.AlchemyJframe;
import org.come.Frame.RolePetResistanceJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.*;
import org.come.action.WuLingBean;
import org.come.bean.PetOperationDTO;
import org.come.entity.Pal;
import org.come.entity.RoleSummoning;
import org.come.mouslisten.GoodsMouslisten;
import org.come.mouslisten.PetAddPointMouslisten;
import org.come.socket.Agreement;
import org.come.socket.SendMessageUntil;
import org.come.until.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ClassName:召唤兽工具类
 * @Author: 四木
 * @Contact:289557289
 * @DateTime: 2025/3/22 20:12
 * @prompt 仅供学习交流，严禁用于商业用途，请于24小时内删除
 */
public class SipetUtil {
    //高级技能
   public static int[] highSkill = new int[] {1600,1601,1602,1603,1604,1605,1611,1612,1811,
            1815,1816,1817,1818,1819,1815,1816,1817,1818,1819,
            1820,1821,1822,1823,1824,1825,1826,1827,1831,1833,
            1834,1835,1836,1837,1838,1839,1848,1850,1852,1854,1858,1859,1860,1862,1864,1865,
            1871,1872,1873,1874,1875,1876,1877,1878,1879,1880};
   //灵返技能
    public static int[] lingfan = new int[] { 1880, 1895, 1891, 1600, 1601, 1602, 1603, 1604, 1605,
            1820, 1822, 1831, 1833, 1834, 1835, 1836, 1865, 1896, 1611,
            1897, 1872, 1612, 1838, 1873,
           1892,1893,1887,1889,1894,1898
    };
    /**宠物名称修改*/
    public static void changname() {
        // 将更改的名字进行传送到服务器进行修改数据
        try {
            if (UserMessUntil.getChosePetMes() != null) {
                SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
                String lastname = UserMessUntil.getChosePetMes().getSummoningname();
                // 若要修改的名字为空，则提示其输入新的召唤兽名字
                if (sipetJPanel.getLabname().getText() == null || sipetJPanel.getLabname().getText().isEmpty()) {
                    ZhuFrame.getZhuJpanel().addPrompt("请输入召唤兽" + lastname + "的新名字！");
                    return;
                }
                if (sipetJPanel.getLabname().getText().length() > 6) {
                    ZhuFrame.getZhuJpanel().addPrompt("长?那是一种专属");
                    return;
                }
                if (sipetJPanel.getLabname().getText().contains("#")){
                    ZhuFrame.getZhuJpanel().addPrompt("特殊符号无法更改");
                    return;
                }
                // 改变名字
                UserMessUntil.getChosePetMes().setSummoningname(sipetJPanel.getLabname().getText().trim());
                // 发送给服务器
                PetOperationDTO dto = new PetOperationDTO();
                dto.setPetId(UserMessUntil.getChosePetMes().getSid());
                dto.setOperationType("PET_NAME_XG");
                dto.setEventType(sipetJPanel.getLabname().getText().trim());
                SendRoleAndRolesummingUntil.sendRoleSumming(dto);
                ZhuFrame.getZhuJpanel().addPrompt2(
                        "您的召唤兽 " + lastname + "成功改名为:" + UserMessUntil.getChosePetMes().getSummoningname());
                // 刷新面板
                sipetJPanel.RefreshPetViewport();
            } else {
                ZhuFrame.getZhuJpanel().addPrompt("请选择你要改名的召唤兽！");
            }
        } catch (Exception e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
    }

    /**
     * 驯养
     */
    public static void xunyang() {
        // 选中了召唤兽，然后把宠物口粮进行解析
        int a = 0;
        for (int i = 0; i < GoodsListFromServerUntil.getGoodslist().length; i++) {
            if (UserMessUntil.getChosePetMes().getFaithful() >= 100) {
                ZhuFrame.getZhuJpanel().addPrompt2(
                        "召唤兽 " + UserMessUntil.getChosePetMes().getSummoningname() + "的忠诚度已满！！！");
                return;
            }
            if (GoodsListFromServerUntil.getGoodslist()[i] != null && GoodsListFromServerUntil.getGoodslist()[i].getType() == 49) {
                ZhuFrame.getZhuJpanel().addPrompt2("召唤兽 " + UserMessUntil.getChosePetMes().getSummoningname() + "增加了"
                                + GoodsListFromServerUntil.getGoodslist()[i].getValue() + "点忠诚度啦！！！");
                GoodsListFromServerUntil.getGoodslist()[i].goodxh(1);
                GoodsMouslisten.gooduse(GoodsListFromServerUntil.getGoodslist()[i], 1);
                if (GoodsListFromServerUntil.getGoodslist()[i].getUsetime() <= 0)
                    GoodsListFromServerUntil.Deleted(i);
                // 将这个召唤兽发给服务器
                PetOperationDTO dto = new PetOperationDTO();
                dto.setPetId(UserMessUntil.getChosePetMes().getSid());
                dto.setItmeId(GoodsListFromServerUntil.getGoodslist()[i].getRgid());
                dto.setOperationType("RAISE_AND_TRAIN");
                dto.setEventType(GoodsListFromServerUntil.getGoodslist()[i].getValue());
                SendRoleAndRolesummingUntil.sendRoleSumming(dto);
                // 刷新界面忠诚度
                SipetFrame.getSipetFrame().getSipetJPanel().getjLabels()[2].setText(UserMessUntil.getChosePetMes().getFaithful().toString());
                a = 1;
            }
        }
        if (a == 0) {
            ZhuFrame.getZhuJpanel().addPrompt2("您没有足够宠物口粮了！！！");
        }
    }

    /**参战*/
    public static void canzhan() {
        List<Pal> pals = RoleData.getRoleData().getPals();
        for (Pal pal : pals) {
            if (pal.getSummoning_id() != null && pal.getSummoning_id().compareTo(UserMessUntil.getChosePetMes().getSid()) == 0) {
                ZhuFrame.getZhuJpanel().addPrompt2("#R此召唤兽伙伴管制中！");
                return;
            }
        }
        // 将宠物头像框设置为有头像
        String path = "0x6B" + UserMessUntil.getChosePetMes().getSummoningskin();
        ZhuJpanel.setLabpetimg(path);
        // 选中的召唤兽id
        String mes = Agreement.getAgreement().rolechangeAgreement("P" + UserMessUntil.getChosePetMes().getSid().toString());
        SendMessageUntil.toServer(mes);
        // 设置自身携带的宠物id
        RoleData.getRoleData().getLoginResult().setSummoning_id(UserMessUntil.getChosePetMes().getSid());
        // 绘制召唤兽 3条
        Article.souxie(UserMessUntil.getChosePetMes());
        PetPanelShow.ShowMesForJpanel();
        SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
        sipetJPanel.getBtnwar().setNtext("休息");
        sipetJPanel.getBtnwar().setIndex(5);
        sipetJPanel.RefreshPetViewport();
        AlchemyJpanel.newPart = null;
        sipetJPanel.BtnPoints(UserMessUntil.getChosePetMes().getCanpoint());
    }

    /**休息*/
    public static void xiuxi() {
        ZhuJpanel.setLabpetimg(null);
        String mes = Agreement.getAgreement().rolechangeAgreement("P");
        SendMessageUntil.toServer(mes);
        RoleData.getRoleData().getLoginResult().setSummoning_id(null);
        SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
        sipetJPanel.getBtnwar().setNtext("出战");
        sipetJPanel.getBtnwar().setIndex(4);
        sipetJPanel.RefreshPetViewport();
        AlchemyJpanel.newPart = null;
        sipetJPanel.BtnPoints(UserMessUntil.getChosePetMes().getCanpoint());
    }

    /**加点*/
    //TODO可以刷属性
    public static void jiadian() {
        SipetJPanel sipetJPanel = SipetFrame.getSipetFrame().getSipetJPanel();
        RoleSummoning pet = UserMessUntil.getChosePetMes();
        // 根骨输入框转变
        pet.setBone(Integer.parseInt(sipetJPanel.getjLabels()[11].getText())-pet.getExtra("根骨"));
        // 灵性输入框
        pet.setSpir(Integer.parseInt(sipetJPanel.getjLabels()[12].getText())-pet.getExtra("灵性"));
        // 力量输入框
        pet.setPower(Integer.parseInt(sipetJPanel.getjLabels()[13].getText())-pet.getExtra("力量"));
        // 敏捷输入框
        pet.setSpeed(Integer.parseInt(sipetJPanel.getjLabels()[14].getText())-pet.getExtra("敏捷"));
        if (pet.getTurnRount() >= 4) {
            // 定力输入框
            pet.setCalm(Integer.parseInt(sipetJPanel.getjLabels()[15].getText())-pet.getExtra("定力"));
        }
        // 增加对应的属性
        PetAddPointMouslisten.showPetValue();
        String mes = Agreement.getAgreement().DemonsAgreement(
                "D_M_"+pet.getSid()+"_" + pet.getBone() + "=" + pet.getSpir() + "=" + pet.getPower() + "="
                        + pet.getSpeed() + "=" + pet.getCalm());
        SendMessageUntil.toServer(mes);
        ZhuFrame.getZhuJpanel().addPrompt2("#G保存成功");
        // 刷新血条
        Article.souxie(pet);
    }

    /**
     * 炼妖
     */
    public static void lianyao() {
        // 清空左上角放置召唤兽的表格
        AlchemyMainJpanel alchemyMainJpanel = AlchemyJframe.getAlchemyjframe().getAlchemyMainJpanel();
        alchemyMainJpanel.ServerObtainsData();
        AlchemyJpanel.newPart = SipetJPanel.getBtnRich()==null?null:SipetJPanel.getBtnRich().getPart().clonePart();
        AlchemyExtractJpanel.newPart = null;
        AlchemyReturnJpanel.newPart = null;
        alchemyMainJpanel.getCardJPanel().getAlchemyExtractJpanel().getLabpetimg().setIcon(null);
        alchemyMainJpanel.getCardJPanel().getAlchemyReturnJpanel().getLabpetimg().setIcon(null);
        if (alchemyMainJpanel.getInterface()==4) {
            alchemyMainJpanel.getCardJPanel().getAlchemyExtractJpanel().initList(alchemyMainJpanel);
        }
        if (alchemyMainJpanel.getInterface()==5) {
            alchemyMainJpanel.getCardJPanel().getAlchemyReturnJpanel().initList(alchemyMainJpanel);
        }
         //设置参战的召唤兽模型
        // 将界面展示出来
        Util.StopFrame(17);
    }

    /**
     * 召唤兽抗性面板展示
     */
    public static void petZhanshi() {
        if (UserMessUntil.getChosePetMes() == null) {
            // 显示在聊天面板
            FrameMessageChangeJpanel.addtext(6, "请选择你要查看抗性的召唤兽！", null, null);
        } else {
            // 展示召唤兽抗性面板
            if (!FormsManagement.getframe(58).isVisible()) {
//                Xkshuxin();
               RolePetResistanceJframe.getResistancejframe().getResistancejpanel().Petresistancedisplay();
                FormsManagement.showForm(58);
                FormsManagement.HideForm(8);
            } else {
                FormsManagement.HideForm(58);
                FormsManagement.HideForm(8);
            }

        }
    }
    public static void Xkshuxin(){
        if (ImageMixDeal.userimg.getRoleShow().getFighting() == 0 || FightingMixDeal.camp == -1) {
//            PetProperty.ShowQl(UserMessUntil.getChosePetMes());
            //TODO ???
        } else {// 获取战斗内的抗性
            SendMessageUntil.toServer(Agreement.getAgreement().fightQlAgreement("P"+UserMessUntil.getChosePetMes().getSid()));
        }
    }

    public static Map<String, WuLingBean> wuLingBeanMap = new ConcurrentHashMap<>();

    public static String SpiritLevelEffect(String id, RoleSummoning pet,int type) {
        int level = PetSkillQiLingBtn.getSkillLingjieLevel(pet, id);
        // 从 WuLingLvControl 获取配置
        WuLingBean wuLingBean = getWuLingBean(id);
        if (wuLingBean == null) {
            return "#r#R 下一灵阶效果：（尚未激活）";
        }
        // 解析基础值和增长值
        String[] baseValues = wuLingBean.getInitValue().split("_");
        double baseValue1 = baseValues.length > 0 ? Double.parseDouble(baseValues[0]) : 0;
        double baseValue2 = baseValues.length > 1 ? Double.parseDouble(baseValues[1]) : 0;
        // 根据配置计算效果
        if (level > 0&&type==0) {
            // 解析基础值
            String msg = wuLingBean.getEffect();
            msg = msg.replace("{0}", String.format("%.1f",baseValue1 * level));
            msg = msg.replace("{1}", String.format("%.1f",baseValue2 * level));
            // 当前效果
            String currentEffect = msg;
            // 下一级效果
            if (level < 10) {
                msg = wuLingBean.getEffect();
                msg = msg.replace("{0}", String.format("%.1f",baseValue1 * (level + 1) ));
                msg = msg.replace("{1}", String.format("%.1f",baseValue2 * (level + 1) ));
                String nextEffect = msg;
                return String.format("#G当前灵阶效果：%s#r\n#R下阶灵阶效果：（尚未激活）#r%s", currentEffect, nextEffect);
            } else {
                return String.format("#G当前悟灵效果：%s#r\n已达最高级", currentEffect);
            }
        }
        String msg = wuLingBean.getEffect();
        msg = msg.replace("{0}", String.format("%.1f",baseValue1 * (level + 1) ));
        msg = msg.replace("{1}", String.format("%.1f",baseValue2 * (level + 1) ));
        String nextEffect = msg;
        return String.format("%s#r\n#R下阶灵阶效果：（尚未激活）#r%s", "", nextEffect);
    }
    public static WuLingBean getWuLingBean(String id) {
        return wuLingBeanMap.get(id);
    }

    public static Map<String, WuLingBean> getWuLingBeanMap() {
        return wuLingBeanMap;
    }

    public static void setWuLingBeanMap(Map<String, WuLingBean> wuLingBeanMap) {
        SipetUtil.wuLingBeanMap = wuLingBeanMap;
    }
}
