package com.tool.btn;

import com.tool.role.GetExp;
import come.tool.Fighting.FightingMixDeal;
import come.tool.handle.HandleState;
import org.come.Frame.MsgJframe;
import org.come.Frame.NedanJframe;
import org.come.Frame.ZhuFrame;
import org.come.Jpanel.ZhuJpanel;
import org.come.bean.PetOperationDTO;
import org.come.entity.Goodstable;
import org.come.mouslisten.ChangeMouseSymbolMouslisten;
import org.come.mouslisten.GoodsMouslisten;
import org.come.until.*;

import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.math.BigDecimal;

public class NeidanBtn extends MoBanBtn implements MouseListener {

    private int flag;
    private Goodstable goodstable;

    public NeidanBtn(String iconpath, int type, int flag) {
        super(iconpath,0, type);
        this.flag = flag;
        // TODO Auto-generated constructor stub
    }

    @Override
    public void chooseyes() {
        // TODO Auto-generated method stub
    }

    @Override
    public void chooseno() {
        // TODO Auto-generated method stub
    }

    @Override
    public void nochoose(MouseEvent e) {
        // 判断是否有这个内丹物品
        if (e.getButton() == MouseEvent.BUTTON3 && FightingMixDeal.State == HandleState.USUAL && UserMessUntil.getChosePetMes() != null) {
            ZhuJpanel.setNedangoods(goodstable);
            ZhuFrame.getZhuJpanel().drawEquip(goodstable,0);
            spitOutNeDan();//吐出内丹
        }else {
            if (goodstable != null) {
                ZhuJpanel.setNedangoods(goodstable);
                ShowNedanMsg(goodstable);
                Util.StopFrame(47);
            }
        }
    }


    public void spitOutNeDan() {
        // 判断召唤兽是否被加锁
        if (UserMessUntil.getChosePetMes().getPetlock() == 1) {
            ZhuFrame.getZhuJpanel().addPrompt(
                    "召唤兽" + UserMessUntil.getChosePetMes().getSummoningname() + "已被加锁，不可吐出内丹！！");
            return;
        }
        // 先判断背包是否还有空位
        int packNumber = GoodsListFromServerUntil.Surplussum("-1", "-1", 999);
        if (packNumber <= 0) {
            ZhuFrame.getZhuJpanel().addPrompt("背包已满！！！");
            return;
        }
        String[] strings = UserMessUntil.getChosePetMes().getInnerGoods().split("\\|");
        if (strings.length > 0) {
            StringBuilder values = new StringBuilder();
            for (int i = 0; i < strings.length; i++)
                if (ZhuJpanel.getNedangoods() != null && ZhuJpanel.getNedangoods().getRgid().compareTo(new BigDecimal(strings[i])) == 0) {
                    ZhuJpanel.getNedangoods().setStatus(0);
                    GoodsListFromServerUntil.fushis.remove(ZhuJpanel.getNedangoods().getRgid());
                    GoodsListFromServerUntil.newgood(ZhuJpanel.getNedangoods());
                    GoodsMouslisten.gooduse(ZhuJpanel.getNedangoods(), 0);
                    FormsManagement.HideForm(47);
                    ZhuJpanel.setNedangoods(null);
                } else {
                    if (values.length() > 0) values.append("|");values.append(strings[i]);
                }
            PetOperationDTO dto = new PetOperationDTO();
            dto.setPetId(UserMessUntil.getChosePetMes().getSid());
            dto.setItmeId(goodstable.getRgid());
            dto.setOperationType("SPIT_OUT_THE_INNER_ALCHEMY");
            dto.setEventType(values.toString());
            ChangeMouseSymbolMouslisten.refreshNedan(UserMessUntil.getChosePetMes());
            SendRoleAndRolesummingUntil.sendRoleSumming(dto);
        }

    }

    public static void ShowNedanMsg(Goodstable goodstable) {
        // 将这个内丹的信息展示出来
        // 名称
        NedanJframe.getNedanJframe().getNedanJpanel().getLabNedanName().setText(goodstable.getGoodsname());
        String[] strings = goodstable.getValue().split("\\|");
        String[] stringLevel = strings[2].split("\\=");
        String[] stringLevel2 = stringLevel[1].split("\\转");
        String[] stringExp = strings[3].split("\\=");
        // 28200
        // 等级
        NedanJframe.getNedanJframe().getNedanJpanel().getLabLevel().setText(stringLevel[1] + "级");
        // 转生次数
        NedanJframe.getNedanJframe().getNedanJpanel().getLabnumber().setText(stringLevel2[0]);
        // 当前经验
        NedanJframe.getNedanJframe().getNedanJpanel().getLabExp().setText(stringExp[1]);
        NedanJframe
                .getNedanJframe()
                .getNedanJpanel()
                .getLabTotalExp()
                .setText(
                        GetExp.getBBNeiExp(Integer.parseInt(stringLevel2[0]), Integer.parseInt(stringLevel2[1]) + 1)
                                + "");
        String[] string = strings[0].split("\\=");
        String[] string2 = string[1].split("\\:");
        // 内丹描述
        NedanJframe.getNedanJframe().getNedanJpanel().getAreaMsg().setText(string2[1]);
    }

    public Goodstable getGoodstable() {
        return goodstable;
    }

    public void setGoodstable(Goodstable goodstable) {
        this.goodstable = goodstable;
    }

    /**
     * Invoked when the mouse enters a component.
     */
    public void mouseEntered(MouseEvent e) {
        if (btn != 0 && zhen != 2) {
            btnchange(1);
        }
        MsgJframe.getJframe().getJapnel().NeiDan(getGoodstable());
    }

    /**
     * Invoked when the mouse exits a component.
     */
    public void mouseExited(MouseEvent e) {
        if (zhen != 2) {btnchange(0);}
        FormsManagement.HideForm(46);
    }

}
