package com.tool.ModerateTask;

public class TaskData {
	
	private int taskID;//任务ID
	private int taskSetID;//任务系列ID
	private String taskName;//任务名称
	private String taskOpen;//任务领取选项   
//	private String consume;//任务消耗     
//	private String finishTerm;//任务目的 
//	private String taskAward;//任务奖励
	private String taskText;//任务说明
	private String taskEnd;//任务结束语 
//	private int postTaskTerm;//下一任务条件
//	private String postTaskId;//后置任务id
	private String lvl;//等级限制
	private int TeamSum;//组队人数 0只能自己完成  其他数字表示队伍最少人数	
//	private int DoorID;//传送 0为不传送
	private int isTP;//任务进行时传送限制 1不可传送
	private int time;//时间 0为无限制 单位分钟
    private String openTime;//任务开放时间	
// 	private String talk;//喊话
    private int nd;//难度设置 
    //困难 地狱 炼狱
	public int getTaskID() {
		return taskID;
	}
	public void setTaskID(int taskID) {
		this.taskID = taskID;
	}
	public int getTaskSetID() {
		return taskSetID;
	}
	public void setTaskSetID(int taskSetID) {
		this.taskSetID = taskSetID;
	}
	public String getTaskName() {
		return taskName;
	}
	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}
	public String getTaskOpen() {
		return taskOpen;
	}
	public void setTaskOpen(String taskOpen) {
		this.taskOpen = taskOpen;
	}
//	public String getConsume() {
//		return consume;
//	}
//	public void setConsume(String consume) {
//		this.consume = consume;
//	}
//	public String getFinishTerm() {
//		return finishTerm;
//	}
//	public void setFinishTerm(String finishTerm) {
//		this.finishTerm = finishTerm;
//	}
	public String getTaskText() {
		return taskText;
	}
	public void setTaskText(String taskText) {
		this.taskText = taskText;
	}
	public String getTaskEnd() {
		return taskEnd;
	}
	public void setTaskEnd(String taskEnd) {
		this.taskEnd = taskEnd;
	}
	public String getLvl() {
		return lvl;
	}
	public void setLvl(String lvl) {
		this.lvl = lvl;
	}
	public int getTeamSum() {
		return TeamSum;
	}
	public void setTeamSum(int teamSum) {
		TeamSum = teamSum;
	}
	public int getIsTP() {
		return isTP;
	}
	public void setIsTP(int isTP) {
		this.isTP = isTP;
	}
	public int getTime() {
		return time;
	}
	public void setTime(int time) {
		this.time = time;
	}
	public String getOpenTime() {
		return openTime;
	}
	public void setOpenTime(String openTime) {
		this.openTime = openTime;
	}
	public int getNd() {
		return nd;
	}
	public void setNd(int nd) {
		this.nd = nd;
	}
	
}
