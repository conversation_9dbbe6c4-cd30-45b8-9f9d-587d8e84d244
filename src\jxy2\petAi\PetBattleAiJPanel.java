package jxy2.petAi;

import com.tool.btn.PartnerBtn;
import com.tool.tcpimg.UIUtils;
import jxy2.jutnil.ImgConstants;
import jxy2.jutnil.Juitil;
import jxy2.setup.AudioSteupMouslisten;
import org.come.Jpanel.TeststateJpanel;
import org.come.bean.Skill;
import org.come.entity.RoleSummoning;
import org.come.until.UserMessUntil;

import javax.swing.*;
import java.awt.*;

public class PetBattleAiJPanel extends JPanel {
    private PartnerBtn btnserve,btnai;
    private JLabel labSkill,labCondOne,labCondTwo,labCondThree,labCondFour,labCondFive,labCondSix,labDefense;
    private JLabel[] xz;
    private Skill skill;
    private RoleSummoning pet;
    public boolean isStop = false;
    public PetBattleAiJPanel() {
        this.setPreferredSize(new Dimension(284, 320));
        this.setLayout(null);
        this.setBackground(UIUtils.Color_BACK);
        Juitil.addClosingButtonToPanel(this,121,284);
        getBtnserve();
        getBtnai();
        getLabSkill();
        getLabCondOne();
        getLabCondTwo();
        getLabCondThree();
        getLabDefense();
        getLabCondFour();
        getLabCondFive();
        getLabCondSix();
        getXz();
    }
    /**初始化召唤兽信息*/
    //条件=回合-1=法术-1602=25000|条件=回合-5=法术-1602=25000|条件=回合-10=法术-1602=25000
    public void initPetInfo(RoleSummoning pet)
    {
        for (JLabel jLabel : xz) {
        jLabel.setIcon(null);
     }
        this.pet = pet;
        if (pet.getAi()!=null){
            String[] vs = pet.getAi().split("\\|");
            for (int i = 0; i < vs.length; i++) {
                if (vs[i].contains("防御")){
                    xz[6].setIcon(AudioSteupMouslisten.icon);
                }else if(vs[i].contains("法术")||vs[i].contains("普通攻击")){
                    xz[i].setIcon(AudioSteupMouslisten.icon);
                }else {
                    xz[i].setIcon(null);
                }

            }
        }
        //这只召唤兽有技能，但是没有对应的主动技能该如何判定
        Visible(pet.getPetSkills()!=null);

        boolean hasActiveSkill = false;
        if (pet.getPetSkills()==null){return;}
        String[] vs = pet.getPetSkills().split("\\|");
        for (int i = 0; i < vs.length; i++) {
            if (Juitil.isActiveSkill(vs[i])) {
                Skill skill = UserMessUntil.getSkillId(vs[i]);
                if (skill != null) {
                    this.skill = skill;
                    labSkill.setText("技能·" + skill.getSkillname() + "(主动)");
                    labCondOne.setText("条件一·第一回合释放:" + skill.getSkillname());
                    labCondTwo.setText("条件二·间隔普通攻击");
                    labCondThree.setText("条件三·第五回合释放:" + skill.getSkillname());
                    labCondFour.setText("条件四·间隔普通攻击");
                    labCondFive.setText("条件五·第十回合释放:" + skill.getSkillname());
                    labCondSix.setText("条件四·间隔普通攻击");
                    hasActiveSkill = true;
                    break; // 找到第一个主动技能后退出循环
                }
            }
        }
        if (!hasActiveSkill) {
            labSkill.setText("技能·普通攻击");
            Visible(false);
        }
    }

    /**组件可见*/
    public void Visible(boolean visible) {

        labSkill.setVisible(visible);
        labCondOne.setVisible(visible);
        labCondTwo.setVisible(visible);
        labCondThree.setVisible(visible);
        labDefense.setVisible(visible);
        labCondFour.setVisible(visible);
        labCondFive.setVisible(visible);
        labCondSix.setVisible(visible);
        isStop = !visible;
    }


    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        Juitil.ImngBack(g, Juitil.tz67, 0, 0, 284, 320, 1);
        if (!isStop) {
            for (int i = 0; i < 7; i++) {
                g.drawImage(Juitil.tz83.getImage(), 18, 62 + i * 30, 14, 14, null);
            }
        }
        Juitil.TextBackground(g, "全部取消勾选默认“普通攻击”", 13, 18, 265, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY13);
        if (pet!=null) {
            Juitil.TextBackground(g, "当前召唤兽：" + pet.getSummoningname(), 13, 66, 10, UIUtils.COLOR_goods_quantity, UIUtils.FZCY_HY14);
        }
    }

    public PartnerBtn getBtnserve() {
        if (btnserve == null) {
            btnserve = new PartnerBtn(ImgConstants.tz34, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "保存",
                    18);

            btnserve.setBounds(50, 285, 59, 26);
            this.add(btnserve);
        }
        return btnserve;
    }

    public void setBtnserve(PartnerBtn btnserve) {
        this.btnserve = btnserve;
    }

    public PartnerBtn getBtnai() {
        if (btnai == null) {
            btnai = new PartnerBtn(ImgConstants.tz85, 1, UIUtils.COLOR_BTNTEXT, UIUtils.MSYH_HY14, "未知条件",
                    19);
            btnai.setBounds(163, 285, 82, 26);
            this.add(btnai);
        }
        return btnai;
    }

    public void setBtnai(PartnerBtn btnai) {
        this.btnai = btnai;
    }

    public JLabel getLabSkill() {
        if (labSkill==null){
            labSkill = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labSkill.setText("技能：暂无技能");
            labSkill.setBounds(18, 25, 150, 26);
            this.add(labSkill);
        }
        return labSkill;
    }

    public void setLabSkill(JLabel labSkill) {
        this.labSkill = labSkill;
    }

    public JLabel getLabCondOne() {
        if (labCondOne==null){
            labCondOne = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondOne.setText("条件一：");
            labCondOne.setBounds(35, 55, 267, 26);
            this.add(labCondOne);
        }
        return labCondOne;
    }

    public void setLabCondOne(JLabel labCondOne) {
        this.labCondOne = labCondOne;
    }

    public JLabel getLabCondTwo() {
        if (labCondTwo==null){
            labCondTwo = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondTwo.setText("条件二：");
            labCondTwo.setBounds(35, 85, 267, 26);
            this.add(labCondTwo);
        }
        return labCondTwo;
    }

    public void setLabCondTwo(JLabel labCondTwo) {
        this.labCondTwo = labCondTwo;
    }

    public JLabel getLabCondThree() {
        if (labCondThree==null){
            labCondThree = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondThree.setText("条件三：");
            labCondThree.setBounds(35, 115, 267, 26);
            this.add(labCondThree);
        }
        return labCondThree;
    }

    public void setLabCondThree(JLabel labCondThree) {
        this.labCondThree = labCondThree;
    }

    public JLabel getLabDefense() {
        if (labDefense==null){
            labDefense = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labDefense.setText("防御");
            labDefense.setBounds(35, 235, 267, 26);
            this.add(labDefense);
        }
        return labDefense;
    }

    public void setLabDefense(JLabel labDefense) {
        this.labDefense = labDefense;
    }


    public JLabel getLabCondFour() {
        if (labCondFour==null){
            labCondFour = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondFour.setText("条件四：");
            labCondFour.setBounds(35, 145, 267, 26);
            this.add(labCondFour);
        }
        return labCondFour;
    }

    public void setLabCondFour(JLabel labCondFour) {
        this.labCondFour = labCondFour;
    }

    public JLabel getLabCondFive() {
        if (labCondFive==null){
            labCondFive = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondFive.setText("条件五：");
            labCondFive.setBounds(35, 175, 267, 26);
            this.add(labCondFive);
        }

        return labCondFive;
    }

    public void setLabCondFive(JLabel labCondFive) {
        this.labCondFive = labCondFive;
    }

    public JLabel getLabCondSix() {
        if (labCondSix==null){
            labCondSix = TeststateJpanel.GJpanelText(UIUtils.COLOR_NAME,null);
            labCondSix.setText("条件六：");
            labCondSix.setBounds(35, 205, 267, 26);
            this.add(labCondSix);
        }
        return labCondSix;
    }

    public void setLabCondSix(JLabel labCondSix) {
        this.labCondSix = labCondSix;
    }

    public JLabel[] getXz() {
        if (xz==null){
            xz = new JLabel[7];
            for (int i = 0; i < 7; i++) {
                xz[i] = new JLabel();
                xz[i].addMouseListener(new PetBattleAiMouse(this,i));
                xz[i].setBounds(18, 62+i*30, 17, 16);
                this.add(xz[i]);
            }
        }
        return xz;
    }

    public void setXz(JLabel[] xz) {
        this.xz = xz;
    }

    public Skill getSkill() {
        return skill;
    }

    public void setSkill(Skill skill) {
        this.skill = skill;
    }

    public RoleSummoning getPet() {
        return pet;
    }

    public void setPet(RoleSummoning pet) {
        this.pet = pet;
    }
}
