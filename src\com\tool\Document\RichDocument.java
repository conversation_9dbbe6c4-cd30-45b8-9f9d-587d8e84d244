package com.tool.Document;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.swing.text.AttributeSet;
import javax.swing.text.BadLocationException;
import javax.swing.text.PlainDocument;

import org.come.until.GsonUtil;

import com.tool.tcpimg.InputBean;

public class RichDocument extends PlainDocument {
 
    private static final long serialVersionUID = 1L;
    private List<InputBean> inputs;
    public RichDocument() {
        super();
        inputs=new ArrayList<>();
    }
    @Override
	public void insertString(int offset, String str, AttributeSet attrSet) throws BadLocationException {
        if (str == null) {
            return;
        }
        for (int i = inputs.size()-1; i >=0; i--) {if (inputs.get(i).isIndex(offset)) {return;}}
        Offset(offset, str.length());
        super.insertString(offset,str,attrSet);
    }
    /**插入物品等奇葩展示 插入位置 类型 id 别名 颜色*/
    public void insertRich(int offset,int type,BigDecimal id,String str,String color,AttributeSet attrSet) throws BadLocationException {
        if (str == null||inputs.size()>=3) {
            return;
        }
        str=str.replace("#"," ");
        for (int i = inputs.size()-1; i >=0; i--) {if (inputs.get(i).isIndex(offset)) {return;}}
        Offset(offset, str.length());
        InputBean bean=new InputBean(offset, type, id, str, color);
        insertInput(bean);
        super.insertString(offset,str,attrSet);
    }
    @Override
	public void remove(int offs, int len) throws BadLocationException {
    	Offset(offs, -len);
    	super.remove(offs,len);
    }
    /**添加一个插入*/
    public void insertInput(InputBean bean){
    	for (int i = 0; i < inputs.size(); i++) {
    		if (inputs.get(i).getIndex()>bean.getIndex()) {
    			inputs.add(i,bean);
    			return;
    		}
		}
    	inputs.add(bean);
    }
    /**指标偏移 偏移 偏移的大小*/
    public void Offset(int offs,int offset){
    	for (int i = inputs.size()-1; i >=0; i--) {
    		InputBean bean=inputs.get(i);
			if (bean.getIndex()>=offs) {
				bean.setIndex(bean.getIndex()+offset);
				if (bean.getIndex()<offs) {
					inputs.remove(i);
				}
			}	
		}
    }
    /**获得网络传输字符串*/
    public String sendText(){
    	 try {
			String text=super.getText(0, getLength());
			if (inputs.size()==0) {
				return text;
			}
			int cha=0;
			StringBuffer buffer=new StringBuffer();
			for (int i = 0; i < inputs.size(); i++) {
				InputBean bean=inputs.get(i);
				String a=text.substring(0,bean.getIndex()-cha);
				buffer.append(a);
				buffer.append("#V");
				buffer.append(GsonUtil.getGsonUtil().getgson().toJson(bean));
				buffer.append("#L");
				text=text.substring(bean.getIndex()+bean.getName().length()-cha);
				cha=bean.getIndex()+bean.getName().length();
			}
			buffer.append(text);
			return buffer.toString();
		} catch (BadLocationException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
    }
}
